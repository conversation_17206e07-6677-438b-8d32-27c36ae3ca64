import { RelationType } from 'src/engine/metadata-modules/field-metadata/interfaces/relation-type.interface';
import { converRelationTypeToTypeORMRelationType } from '../convert-relation-type-to-typeorm-relation-type.util';

describe('converRelationTypeToTypeORMRelationType', () => {
  it('should convert ONE_TO_MANY to one-to-many', () => {
    expect(converRelationTypeToTypeORMRelationType(RelationType.ONE_TO_MANY)).toBe('one-to-many');
  });

  it('should convert MANY_TO_ONE to many-to-one', () => {
    expect(converRelationTypeToTypeORMRelationType(RelationType.MANY_TO_ONE)).toBe('many-to-one');
  });

  it('should convert ONE_TO_ONE to one-to-one', () => {
    expect(converRelationTypeToTypeORMRelationType(RelationType.ONE_TO_ONE)).toBe('one-to-one');
  });

  it('should throw error for invalid relation type', () => {
    expect(() => converRelationTypeToTypeORMRelationType('INVALID' as RelationType)).toThrow('Invalid relation type: INVALID');
  });
});

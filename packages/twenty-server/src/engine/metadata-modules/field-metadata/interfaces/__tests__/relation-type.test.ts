import { RelationType } from '../relation-type.interface';

describe('RelationType', () => {
  it('should include ONE_TO_ONE relationship type', () => {
    expect(RelationType.ONE_TO_ONE).toBe('ONE_TO_ONE');
  });

  it('should include all expected relationship types', () => {
    const expectedTypes = ['ONE_TO_MANY', 'MANY_TO_ONE', 'ONE_TO_ONE'];
    const actualTypes = Object.values(RelationType);
    
    expect(actualTypes).toEqual(expect.arrayContaining(expectedTypes));
    expect(actualTypes).toHaveLength(3);
  });
});

{"version": 3, "sources": ["../../../../ts-key-enum/dist/js/Key.enum.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * An enum that includes all non-printable string values one can expect from $event.key.\n * For example, this enum includes values like \"CapsLock\", \"Backspace\", and \"AudioVolumeMute\",\n * but does not include values like \"a\", \"A\", \"#\", \"é\", or \"¿\".\n * Auto generated from MDN: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values#Speech_recognition_keys\n */\nvar Key;\n(function (Key) {\n    /**\n     * The user agent wasn't able to map the event's virtual keycode to a\n     * specific key value.\n     * This can happen due to hardware or software constraints, or because of\n     * constraints around the platform on which the user agent is running.\n     */\n    Key[\"Unidentified\"] = \"Unidentified\";\n    /** The Alt (Alternative) key. */\n    Key[\"Alt\"] = \"Alt\";\n    /**\n     * The AltGr or AltGraph (Alternate Graphics) key.\n     * Enables the ISO Level 3 shift modifier (where Shift is the\n     * level 2 modifier).\n     */\n    Key[\"AltGraph\"] = \"AltGraph\";\n    /**\n     * The Caps Lock key. Toggles the capital character lock on and\n     * off for subsequent input.\n     */\n    Key[\"CapsLock\"] = \"CapsLock\";\n    /**\n     * The Control, Ctrl, or Ctl key. Allows\n     * typing control characters.\n     */\n    Key[\"Control\"] = \"Control\";\n    /**\n     * The Fn (Function modifier) key. Used to allow generating\n     * function key (F1–F15, for instance) characters on\n     * keyboards without a dedicated function key area. Often handled in\n     * hardware so that events aren't generated for this key.\n     */\n    Key[\"Fn\"] = \"Fn\";\n    /**\n     * The FnLock or F-Lock (Function Lock) key.Toggles\n     * the function key mode described by \"Fn\" on and off. Often\n     * handled in hardware so that events aren't generated for this key.\n     */\n    Key[\"FnLock\"] = \"FnLock\";\n    /** The Hyper key. */\n    Key[\"Hyper\"] = \"Hyper\";\n    /**\n     * The Meta key. Allows issuing special command inputs. This is\n     * the Windows logo key, or the Command or\n     * ⌘ key on Mac keyboards.\n     */\n    Key[\"Meta\"] = \"Meta\";\n    /**\n     * The NumLock (Number Lock) key. Toggles the numeric keypad\n     * between number entry some other mode (often directional arrows).\n     */\n    Key[\"NumLock\"] = \"NumLock\";\n    /**\n     * The Scroll Lock key. Toggles between scrolling and cursor\n     * movement modes.\n     */\n    Key[\"ScrollLock\"] = \"ScrollLock\";\n    /**\n     * The Shift key. Modifies keystrokes to allow typing upper (or\n     * other) case letters, and to support typing punctuation and other special\n     * characters.\n     */\n    Key[\"Shift\"] = \"Shift\";\n    /** The Super key. */\n    Key[\"Super\"] = \"Super\";\n    /** The Symbol modifier key (found on certain virtual keyboards). */\n    Key[\"Symbol\"] = \"Symbol\";\n    /** The Symbol Lock key. */\n    Key[\"SymbolLock\"] = \"SymbolLock\";\n    /**\n     * The Enter or ↵ key (sometimes labeled\n     * Return).\n     */\n    Key[\"Enter\"] = \"Enter\";\n    /** The Horizontal Tab key, Tab. */\n    Key[\"Tab\"] = \"Tab\";\n    /** The down arrow key. */\n    Key[\"ArrowDown\"] = \"ArrowDown\";\n    /** The left arrow key. */\n    Key[\"ArrowLeft\"] = \"ArrowLeft\";\n    /** The right arrow key. */\n    Key[\"ArrowRight\"] = \"ArrowRight\";\n    /** The up arrow key. */\n    Key[\"ArrowUp\"] = \"ArrowUp\";\n    /** The End key. Moves to the end of content. */\n    Key[\"End\"] = \"End\";\n    /** The Home key. Moves to the start of content. */\n    Key[\"Home\"] = \"Home\";\n    /**\n     * The Page Down (or PgDn) key. Scrolls down or\n     * displays the next page of content.\n     */\n    Key[\"PageDown\"] = \"PageDown\";\n    /**\n     * The Page Up (or PgUp) key. Scrolls up or displays\n     * the previous page of content.\n     */\n    Key[\"PageUp\"] = \"PageUp\";\n    /**\n     * The Backspace key. This key is labeled Delete on\n     * Mac keyboards.\n     */\n    Key[\"Backspace\"] = \"Backspace\";\n    /** The Clear key. Removes the currently selected input. */\n    Key[\"Clear\"] = \"Clear\";\n    /** The Copy key (on certain extended keyboards). */\n    Key[\"Copy\"] = \"Copy\";\n    /** The Cursor Select key, CrSel. */\n    Key[\"CrSel\"] = \"CrSel\";\n    /** The Cut key (on certain extended keyboards). */\n    Key[\"Cut\"] = \"Cut\";\n    /** The Delete key, Del. */\n    Key[\"Delete\"] = \"Delete\";\n    /**\n     * Erase to End of Field. Deletes all characters from the current cursor\n     * position to the end of the current field.\n     */\n    Key[\"EraseEof\"] = \"EraseEof\";\n    /** The ExSel (Extend Selection) key. */\n    Key[\"ExSel\"] = \"ExSel\";\n    /**\n     * The Insert key, Ins. Toggles between inserting and\n     * overwriting text.\n     */\n    Key[\"Insert\"] = \"Insert\";\n    /** Paste from the clipboard. */\n    Key[\"Paste\"] = \"Paste\";\n    /** Redo the last action. */\n    Key[\"Redo\"] = \"Redo\";\n    /** Undo the last action. */\n    Key[\"Undo\"] = \"Undo\";\n    /**\n     * The Accept, Commit, or OK key or\n     * button. Accepts the currently selected option or input method sequence\n     * conversion.\n     */\n    Key[\"Accept\"] = \"Accept\";\n    /** The Again key. Redoes or repeats a previous action. */\n    Key[\"Again\"] = \"Again\";\n    /** The Attn (Attention) key. */\n    Key[\"Attn\"] = \"Attn\";\n    /** The Cancel key. */\n    Key[\"Cancel\"] = \"Cancel\";\n    /**\n     * Shows the context menu. Typically found between the\n     * Windows (or OS) key and the Control key\n     * on the right side of the keyboard.\n     */\n    Key[\"ContextMenu\"] = \"ContextMenu\";\n    /**\n     * The Esc (Escape) key. Typically used as an exit, cancel, or\n     * \"escape this operation\" button. Historically, the Escape character was\n     * used to signal the start of a special control sequence of characters\n     * called an \"escape sequence.\"\n     */\n    Key[\"Escape\"] = \"Escape\";\n    /** The Execute key. */\n    Key[\"Execute\"] = \"Execute\";\n    /**\n     * The Find key. Opens an interface (typically a dialog box) for\n     * performing a find/search operation.\n     */\n    Key[\"Find\"] = \"Find\";\n    /** The Finish key. */\n    Key[\"Finish\"] = \"Finish\";\n    /**\n     * The Help key. Opens or toggles the display of help\n     * information.\n     */\n    Key[\"Help\"] = \"Help\";\n    /**\n     * The Pause key. Pauses the current application or state, if\n     * applicable.\n     * Note: This shouldn't be confused with the\n     * \"MediaPause\" key value, which is used for media\n     * controllers, rather than to control applications and processes.\n     */\n    Key[\"Pause\"] = \"Pause\";\n    /**\n     * The Play key. Resumes a previously paused application, if\n     * applicable.\n     * Note: This shouldn't be confused with the\n     * \"MediaPlay\" key value, which is used for media\n     * controllers, rather than to control applications and processes.\n     */\n    Key[\"Play\"] = \"Play\";\n    /** The Props (Properties) key. */\n    Key[\"Props\"] = \"Props\";\n    /** The Select key. */\n    Key[\"Select\"] = \"Select\";\n    /** The ZoomIn key. */\n    Key[\"ZoomIn\"] = \"ZoomIn\";\n    /** The ZoomOut key. */\n    Key[\"ZoomOut\"] = \"ZoomOut\";\n    /**\n     * The Brightness Down key. Typically used to reduce the brightness of the\n     * display.\n     */\n    Key[\"BrightnessDown\"] = \"BrightnessDown\";\n    /**\n     * The Brightness Up key. Typically increases the brightness of the\n     * display.\n     */\n    Key[\"BrightnessUp\"] = \"BrightnessUp\";\n    /**\n     * The Eject key. Ejects removable media (or toggles an optical\n     * storage device tray open and closed).\n     */\n    Key[\"Eject\"] = \"Eject\";\n    /** The LogOff key. */\n    Key[\"LogOff\"] = \"LogOff\";\n    /**\n     * The Power button or key, to toggle power on and off.\n     * Note: Not all systems pass this key through to the\n     * user agent.\n     */\n    Key[\"Power\"] = \"Power\";\n    /**\n     * The PowerOff or PowerDown key. Shuts off the\n     * system.\n     */\n    Key[\"PowerOff\"] = \"PowerOff\";\n    /**\n     * The PrintScreen or PrtScr key. Sometimes\n     * SnapShot. Captures the screen and prints it or saves it to\n     * disk.\n     */\n    Key[\"PrintScreen\"] = \"PrintScreen\";\n    /**\n     * The Hibernate key. This saves the state of the computer to\n     * disk and then shuts down; the computer can be returned to its previous\n     * state by restoring the saved state information.\n     */\n    Key[\"Hibernate\"] = \"Hibernate\";\n    /**\n     * The Standby key. (Also known as Suspend or\n     * Sleep.) This turns off the display and puts the computer in a\n     * low power consumption mode, without completely powering off.\n     */\n    Key[\"Standby\"] = \"Standby\";\n    /**\n     * The WakeUp key. Used to wake the computer from the\n     * hibernation or standby modes.\n     */\n    Key[\"WakeUp\"] = \"WakeUp\";\n    /**\n     * The All Candidates key, which starts multi-candidate mode, in\n     * which multiple candidates are displayed for the ongoing input.\n     */\n    Key[\"AllCandidates\"] = \"AllCandidates\";\n    /** The Alphanumeric key. */\n    Key[\"Alphanumeric\"] = \"Alphanumeric\";\n    /**\n     * The Code Input key, which enables code input mode, which lets\n     * the user enter characters by typing their code points (their Unicode\n     * character numbers, typically).\n     */\n    Key[\"CodeInput\"] = \"CodeInput\";\n    /** The Compose key. */\n    Key[\"Compose\"] = \"Compose\";\n    /**\n     * The Convert key, which instructs the IME to convert the\n     * current input method sequence into the resulting character.\n     */\n    Key[\"Convert\"] = \"Convert\";\n    /**\n     * A dead \"combining\" key; that is, a key which is used in tandem with\n     * other keys to generate accented and other modified characters. If\n     * pressed by itself, it doesn't generate a character.\n     * If you wish to identify which specific dead key was pressed (in cases\n     * where more than one exists), you can do so by examining the\n     * KeyboardEvent's associated\n     * compositionupdate event's\n     * data property.\n     */\n    Key[\"Dead\"] = \"Dead\";\n    /**\n     * The Final (Final Mode) key is used on some Asian keyboards to\n     * enter final mode when using IMEs.\n     */\n    Key[\"FinalMode\"] = \"FinalMode\";\n    /**\n     * Switches to the first character group on an\n     * ISO/IEC 9995 keyboard. Each key may have multiple groups of characters, each in its own\n     * column. Pressing this key instructs the device to interpret keypresses\n     * as coming from the first column on subsequent keystrokes.\n     */\n    Key[\"GroupFirst\"] = \"GroupFirst\";\n    /**\n     * Switches to the last character group on an\n     * ISO/IEC 9995 keyboard.\n     */\n    Key[\"GroupLast\"] = \"GroupLast\";\n    /**\n     * Switches to the next character group on an\n     * ISO/IEC 9995 keyboard.\n     */\n    Key[\"GroupNext\"] = \"GroupNext\";\n    /**\n     * Switches to the previous character group on an\n     * ISO/IEC 9995 keyboard.\n     */\n    Key[\"GroupPrevious\"] = \"GroupPrevious\";\n    /** The Mode Change key. Toggles or cycles among input modes of IMEs. */\n    Key[\"ModeChange\"] = \"ModeChange\";\n    /**\n     * The Next Candidate function key. Selects the next possible match for the\n     * ongoing input.\n     */\n    Key[\"NextCandidate\"] = \"NextCandidate\";\n    /**\n     * The NonConvert (\"Don't convert\") key. This accepts the\n     * current input method sequence without running conversion when using an\n     * IME.\n     */\n    Key[\"NonConvert\"] = \"NonConvert\";\n    /**\n     * The Previous Candidate key. Selects the previous possible match for the\n     * ongoing input.\n     */\n    Key[\"PreviousCandidate\"] = \"PreviousCandidate\";\n    /** The Process key. Instructs the IME to process the conversion. */\n    Key[\"Process\"] = \"Process\";\n    /**\n     * The Single Candidate key. Enables single candidate mode (as opposed to\n     * multi-candidate mode); in this mode, only one candidate is displayed at\n     * a time.\n     */\n    Key[\"SingleCandidate\"] = \"SingleCandidate\";\n    /**\n     * The Hangul (Korean character set) mode key, which toggles\n     * between Hangul and English entry modes.\n     */\n    Key[\"HangulMode\"] = \"HangulMode\";\n    /**\n     * Selects the Hanja mode, for converting Hangul characters to the more\n     * specific Hanja characters.\n     */\n    Key[\"HanjaMode\"] = \"HanjaMode\";\n    /**\n     * Selects the Junja mode, in which Korean is represented using single-byte\n     * Latin characters.\n     */\n    Key[\"JunjaMode\"] = \"JunjaMode\";\n    /**\n     * The Eisu key. This key's purpose is defined by the IME, but\n     * may be used to close the IME.\n     */\n    Key[\"Eisu\"] = \"Eisu\";\n    /** The Hankaku (half-width characters) key. */\n    Key[\"Hankaku\"] = \"Hankaku\";\n    /** The Hiragana key; selects Kana characters mode. */\n    Key[\"Hiragana\"] = \"Hiragana\";\n    /** Toggles between the Hiragana and Katakana writing systems. */\n    Key[\"HiraganaKatakana\"] = \"HiraganaKatakana\";\n    /** The Kana Mode (Kana Lock) key. */\n    Key[\"KanaMode\"] = \"KanaMode\";\n    /**\n     * The Kanji Mode key. Enables entering Japanese text using the\n     * ideographic characters of Chinese origin.\n     */\n    Key[\"KanjiMode\"] = \"KanjiMode\";\n    /** The Katakana key. */\n    Key[\"Katakana\"] = \"Katakana\";\n    /** The Romaji key; selects the Roman character set. */\n    Key[\"Romaji\"] = \"Romaji\";\n    /** The Zenkaku (full width) characters key. */\n    Key[\"Zenkaku\"] = \"Zenkaku\";\n    /** The Zenkaku/Hankaku (full width/half width) toggle key. */\n    Key[\"ZenkakuHanaku\"] = \"ZenkakuHanaku\";\n    /** The first general-purpose function key, F1. */\n    Key[\"F1\"] = \"F1\";\n    /** The F2 key. */\n    Key[\"F2\"] = \"F2\";\n    /** The F3 key. */\n    Key[\"F3\"] = \"F3\";\n    /** The F4 key. */\n    Key[\"F4\"] = \"F4\";\n    /** The F5 key. */\n    Key[\"F5\"] = \"F5\";\n    /** The F6 key. */\n    Key[\"F6\"] = \"F6\";\n    /** The F7 key. */\n    Key[\"F7\"] = \"F7\";\n    /** The F8 key. */\n    Key[\"F8\"] = \"F8\";\n    /** The F9 key. */\n    Key[\"F9\"] = \"F9\";\n    /** The F10 key. */\n    Key[\"F10\"] = \"F10\";\n    /** The F11 key. */\n    Key[\"F11\"] = \"F11\";\n    /** The F12 key. */\n    Key[\"F12\"] = \"F12\";\n    /** The F13 key. */\n    Key[\"F13\"] = \"F13\";\n    /** The F14 key. */\n    Key[\"F14\"] = \"F14\";\n    /** The F15 key. */\n    Key[\"F15\"] = \"F15\";\n    /** The F16 key. */\n    Key[\"F16\"] = \"F16\";\n    /** The F17 key. */\n    Key[\"F17\"] = \"F17\";\n    /** The F18 key. */\n    Key[\"F18\"] = \"F18\";\n    /** The F19 key. */\n    Key[\"F19\"] = \"F19\";\n    /** The F20 key. */\n    Key[\"F20\"] = \"F20\";\n    /** The first general-purpose virtual function key. */\n    Key[\"Soft1\"] = \"Soft1\";\n    /** The second general-purpose virtual function key. */\n    Key[\"Soft2\"] = \"Soft2\";\n    /** The third general-purpose virtual function key. */\n    Key[\"Soft3\"] = \"Soft3\";\n    /** The fourth general-purpose virtual function key. */\n    Key[\"Soft4\"] = \"Soft4\";\n    /**\n     * Presents a list of recently-used applications which lets the user change\n     * apps quickly.\n     */\n    Key[\"AppSwitch\"] = \"AppSwitch\";\n    /** The Call key. Dials the number which has been entered. */\n    Key[\"Call\"] = \"Call\";\n    /** The Camera key. Activates the camera. */\n    Key[\"Camera\"] = \"Camera\";\n    /** The Focus key. Focuses the camera. */\n    Key[\"CameraFocus\"] = \"CameraFocus\";\n    /** The End Call or Hang Up button. */\n    Key[\"EndCall\"] = \"EndCall\";\n    /** The Back button. */\n    Key[\"GoBack\"] = \"GoBack\";\n    /**\n     * The Home button. Returns the user to the phone's main screen\n     * (usually an application launcher).\n     */\n    Key[\"GoHome\"] = \"GoHome\";\n    /**\n     * The Headset Hook key. This is typically actually a button on\n     * the headset which is used to hang up calls and play or pause media.\n     */\n    Key[\"HeadsetHook\"] = \"HeadsetHook\";\n    /** The Redial button. Redials the last-called number. */\n    Key[\"LastNumberRedial\"] = \"LastNumberRedial\";\n    /** The Notification key. */\n    Key[\"Notification\"] = \"Notification\";\n    /**\n     * A button which cycles among the notification modes: silent, vibrate,\n     * ring, and so forth.\n     */\n    Key[\"MannerMode\"] = \"MannerMode\";\n    /** The Voice Dial key. Initiates voice dialing. */\n    Key[\"VoiceDial\"] = \"VoiceDial\";\n    /** Switches to the previous channel. */\n    Key[\"ChannelDown\"] = \"ChannelDown\";\n    /** Switches to the next channel. */\n    Key[\"ChannelUp\"] = \"ChannelUp\";\n    /** Starts, continues, or increases the speed of fast forwarding the media. */\n    Key[\"MediaFastForward\"] = \"MediaFastForward\";\n    /**\n     * Pauses the currently playing media.\n     * Note: Some older applications use\n     * \"Pause\", but this is not correct.\n     */\n    Key[\"MediaPause\"] = \"MediaPause\";\n    /**\n     * Starts or continues playing media at normal speed, if not already doing\n     * so. Has no effect otherwise.\n     */\n    Key[\"MediaPlay\"] = \"MediaPlay\";\n    /** Toggles between playing and pausing the current media. */\n    Key[\"MediaPlayPause\"] = \"MediaPlayPause\";\n    /** Starts or resumes recording media. */\n    Key[\"MediaRecord\"] = \"MediaRecord\";\n    /** Starts, continues, or increases the speed of rewinding the media. */\n    Key[\"MediaRewind\"] = \"MediaRewind\";\n    /**\n     * Stops the current media activity (such as playing, recording, pausing,\n     * forwarding, or rewinding). Has no effect if the media is currently\n     * stopped already.\n     */\n    Key[\"MediaStop\"] = \"MediaStop\";\n    /** Seeks to the next media or program track. */\n    Key[\"MediaTrackNext\"] = \"MediaTrackNext\";\n    /** Seeks to the previous media or program track. */\n    Key[\"MediaTrackPrevious\"] = \"MediaTrackPrevious\";\n    /** Adjusts audio balance toward the left. */\n    Key[\"AudioBalanceLeft\"] = \"AudioBalanceLeft\";\n    /** Adjusts audio balance toward the right. */\n    Key[\"AudioBalanceRight\"] = \"AudioBalanceRight\";\n    /** Decreases the amount of bass. */\n    Key[\"AudioBassDown\"] = \"AudioBassDown\";\n    /**\n     * Reduces bass boosting or cycles downward through bass boost modes or\n     * states.\n     */\n    Key[\"AudioBassBoostDown\"] = \"AudioBassBoostDown\";\n    /** Toggles bass boosting on and off. */\n    Key[\"AudioBassBoostToggle\"] = \"AudioBassBoostToggle\";\n    /**\n     * Increases the amount of bass boosting, or cycles upward through a set of\n     * bass boost modes or states.\n     */\n    Key[\"AudioBassBoostUp\"] = \"AudioBassBoostUp\";\n    /** Increases the amount of bass. */\n    Key[\"AudioBassUp\"] = \"AudioBassUp\";\n    /** Adjusts the audio fader toward the front. */\n    Key[\"AudioFaderFront\"] = \"AudioFaderFront\";\n    /** Adjusts the audio fader toward the rear. */\n    Key[\"AudioFaderRear\"] = \"AudioFaderRear\";\n    /** Selects the next available surround sound mode. */\n    Key[\"AudioSurroundModeNext\"] = \"AudioSurroundModeNext\";\n    /** Decreases the amount of treble. */\n    Key[\"AudioTrebleDown\"] = \"AudioTrebleDown\";\n    /** Increases the amount of treble. */\n    Key[\"AudioTrebleUp\"] = \"AudioTrebleUp\";\n    /** Decreases the audio volume. */\n    Key[\"AudioVolumeDown\"] = \"AudioVolumeDown\";\n    /** Mutes the audio. */\n    Key[\"AudioVolumeMute\"] = \"AudioVolumeMute\";\n    /** Increases the audio volume. */\n    Key[\"AudioVolumeUp\"] = \"AudioVolumeUp\";\n    /** Toggles the microphone on and off. */\n    Key[\"MicrophoneToggle\"] = \"MicrophoneToggle\";\n    /** Decreases the microphone's input volume. */\n    Key[\"MicrophoneVolumeDown\"] = \"MicrophoneVolumeDown\";\n    /** Mutes the microphone input. */\n    Key[\"MicrophoneVolumeMute\"] = \"MicrophoneVolumeMute\";\n    /** Increases the microphone's input volume. */\n    Key[\"MicrophoneVolumeUp\"] = \"MicrophoneVolumeUp\";\n    /** Switches into TV viewing mode. */\n    Key[\"TV\"] = \"TV\";\n    /** Toggles 3D TV mode on and off. */\n    Key[\"TV3DMode\"] = \"TV3DMode\";\n    /** Toggles between antenna and cable inputs. */\n    Key[\"TVAntennaCable\"] = \"TVAntennaCable\";\n    /** Toggles audio description mode on and off. */\n    Key[\"TVAudioDescription\"] = \"TVAudioDescription\";\n    /**\n     * Decreases the audio description's mixing volume; reduces the volume of\n     * the audio descriptions relative to the program sound.\n     */\n    Key[\"TVAudioDescriptionMixDown\"] = \"TVAudioDescriptionMixDown\";\n    /**\n     * Increases the audio description's mixing volume; increases the volume of\n     * the audio descriptions relative to the program sound.\n     */\n    Key[\"TVAudioDescriptionMixUp\"] = \"TVAudioDescriptionMixUp\";\n    /**\n     * Displays or hides the media contents available for playback (this may be\n     * a channel guide showing the currently airing programs, or a list of\n     * media files to play).\n     */\n    Key[\"TVContentsMenu\"] = \"TVContentsMenu\";\n    /** Displays or hides the TV's data service menu. */\n    Key[\"TVDataService\"] = \"TVDataService\";\n    /** Cycles the input mode on an external TV. */\n    Key[\"TVInput\"] = \"TVInput\";\n    /** Switches to the input \"Component 1.\" */\n    Key[\"TVInputComponent1\"] = \"TVInputComponent1\";\n    /** Switches to the input \"Component 2.\" */\n    Key[\"TVInputComponent2\"] = \"TVInputComponent2\";\n    /** Switches to the input \"Composite 1.\" */\n    Key[\"TVInputComposite1\"] = \"TVInputComposite1\";\n    /** Switches to the input \"Composite 2.\" */\n    Key[\"TVInputComposite2\"] = \"TVInputComposite2\";\n    /** Switches to the input \"HDMI 1.\" */\n    Key[\"TVInputHDMI1\"] = \"TVInputHDMI1\";\n    /** Switches to the input \"HDMI 2.\" */\n    Key[\"TVInputHDMI2\"] = \"TVInputHDMI2\";\n    /** Switches to the input \"HDMI 3.\" */\n    Key[\"TVInputHDMI3\"] = \"TVInputHDMI3\";\n    /** Switches to the input \"HDMI 4.\" */\n    Key[\"TVInputHDMI4\"] = \"TVInputHDMI4\";\n    /** Switches to the input \"VGA 1.\" */\n    Key[\"TVInputVGA1\"] = \"TVInputVGA1\";\n    /** The Media Context menu key. */\n    Key[\"TVMediaContext\"] = \"TVMediaContext\";\n    /** Toggle the TV's network connection on and off. */\n    Key[\"TVNetwork\"] = \"TVNetwork\";\n    /** Put the TV into number entry mode. */\n    Key[\"TVNumberEntry\"] = \"TVNumberEntry\";\n    /** The device's power button. */\n    Key[\"TVPower\"] = \"TVPower\";\n    /** Radio button. */\n    Key[\"TVRadioService\"] = \"TVRadioService\";\n    /** Satellite button. */\n    Key[\"TVSatellite\"] = \"TVSatellite\";\n    /** Broadcast Satellite button. */\n    Key[\"TVSatelliteBS\"] = \"TVSatelliteBS\";\n    /** Communication Satellite button. */\n    Key[\"TVSatelliteCS\"] = \"TVSatelliteCS\";\n    /** Toggles among available satellites. */\n    Key[\"TVSatelliteToggle\"] = \"TVSatelliteToggle\";\n    /**\n     * Selects analog terrestrial television service (analog cable or antenna\n     * reception).\n     */\n    Key[\"TVTerrestrialAnalog\"] = \"TVTerrestrialAnalog\";\n    /**\n     * Selects digital terrestrial television service (digital cable or antenna\n     * reception).\n     */\n    Key[\"TVTerrestrialDigital\"] = \"TVTerrestrialDigital\";\n    /** Timer programming button. */\n    Key[\"TVTimer\"] = \"TVTimer\";\n    /** Changes the input mode on an external audio/video receiver (AVR) unit. */\n    Key[\"AVRInput\"] = \"AVRInput\";\n    /** Toggles the power on an external AVR unit. */\n    Key[\"AVRPower\"] = \"AVRPower\";\n    /**\n     * General-purpose media function key, color-coded red. This has index\n     * 0 among the colored keys.\n     */\n    Key[\"ColorF0Red\"] = \"ColorF0Red\";\n    /**\n     * General-purpose media function key, color-coded green. This has index\n     * 1 among the colored keys.\n     */\n    Key[\"ColorF1Green\"] = \"ColorF1Green\";\n    /**\n     * General-purpose media function key, color-coded yellow. This has index\n     * 2 among the colored keys.\n     */\n    Key[\"ColorF2Yellow\"] = \"ColorF2Yellow\";\n    /**\n     * General-purpose media function key, color-coded blue. This has index\n     * 3 among the colored keys.\n     */\n    Key[\"ColorF3Blue\"] = \"ColorF3Blue\";\n    /**\n     * General-purpose media function key, color-coded grey. This has index\n     * 4 among the colored keys.\n     */\n    Key[\"ColorF4Grey\"] = \"ColorF4Grey\";\n    /**\n     * General-purpose media function key, color-coded brown. This has index\n     * 5 among the colored keys.\n     */\n    Key[\"ColorF5Brown\"] = \"ColorF5Brown\";\n    /** Toggles closed captioning on and off. */\n    Key[\"ClosedCaptionToggle\"] = \"ClosedCaptionToggle\";\n    /**\n     * Adjusts the brightness of the device by toggling between two brightness\n     * levels or by cycling among multiple brightness levels.\n     */\n    Key[\"Dimmer\"] = \"Dimmer\";\n    /** Cycles among video sources. */\n    Key[\"DisplaySwap\"] = \"DisplaySwap\";\n    /** Switches the input source to the Digital Video Recorder (DVR). */\n    Key[\"DVR\"] = \"DVR\";\n    /** The Exit button, which exits the current application or menu. */\n    Key[\"Exit\"] = \"Exit\";\n    /** Clears the program or content stored in the first favorites list slot. */\n    Key[\"FavoriteClear0\"] = \"FavoriteClear0\";\n    /** Clears the program or content stored in the second favorites list slot. */\n    Key[\"FavoriteClear1\"] = \"FavoriteClear1\";\n    /** Clears the program or content stored in the third favorites list slot. */\n    Key[\"FavoriteClear2\"] = \"FavoriteClear2\";\n    /** Clears the program or content stored in the fourth favorites list slot. */\n    Key[\"FavoriteClear3\"] = \"FavoriteClear3\";\n    /**\n     * Selects (recalls) the program or content stored in the first favorites\n     * list slot.\n     */\n    Key[\"FavoriteRecall0\"] = \"FavoriteRecall0\";\n    /**\n     * Selects (recalls) the program or content stored in the second favorites\n     * list slot.\n     */\n    Key[\"FavoriteRecall1\"] = \"FavoriteRecall1\";\n    /**\n     * Selects (recalls) the program or content stored in the third favorites\n     * list slot.\n     */\n    Key[\"FavoriteRecall2\"] = \"FavoriteRecall2\";\n    /**\n     * Selects (recalls) the program or content stored in the fourth favorites\n     * list slot.\n     */\n    Key[\"FavoriteRecall3\"] = \"FavoriteRecall3\";\n    /**\n     * Stores the current program or content into the first favorites list\n     * slot.\n     */\n    Key[\"FavoriteStore0\"] = \"FavoriteStore0\";\n    /**\n     * Stores the current program or content into the second favorites list\n     * slot.\n     */\n    Key[\"FavoriteStore1\"] = \"FavoriteStore1\";\n    /**\n     * Stores the current program or content into the third favorites list\n     * slot.\n     */\n    Key[\"FavoriteStore2\"] = \"FavoriteStore2\";\n    /**\n     * Stores the current program or content into the fourth favorites list\n     * slot.\n     */\n    Key[\"FavoriteStore3\"] = \"FavoriteStore3\";\n    /** Toggles the display of the program or content guide. */\n    Key[\"Guide\"] = \"Guide\";\n    /**\n     * If the guide is currently displayed, this button tells the guide to\n     * display the next day's content.\n     */\n    Key[\"GuideNextDay\"] = \"GuideNextDay\";\n    /**\n     * If the guide is currently displayed, this button tells the guide to\n     * display the previous day's content.\n     */\n    Key[\"GuidePreviousDay\"] = \"GuidePreviousDay\";\n    /**\n     * Toggles the display of information about the currently selected content,\n     * program, or media.\n     */\n    Key[\"Info\"] = \"Info\";\n    /**\n     * Tells the device to perform an instant replay (typically some form of\n     * jumping back a short amount of time then playing it again, possibly but\n     * not usually in slow motion).\n     */\n    Key[\"InstantReplay\"] = \"InstantReplay\";\n    /** Opens content linked to the current program, if available and possible. */\n    Key[\"Link\"] = \"Link\";\n    /** Lists the current program. */\n    Key[\"ListProgram\"] = \"ListProgram\";\n    /** Toggles a display listing currently available live content or programs. */\n    Key[\"LiveContent\"] = \"LiveContent\";\n    /** Locks or unlocks the currently selected content or pgoram. */\n    Key[\"Lock\"] = \"Lock\";\n    /**\n     * Presents a list of media applications, such as photo viewers, audio and\n     * video players, and games. [1]\n     */\n    Key[\"MediaApps\"] = \"MediaApps\";\n    /** The Audio Track key. */\n    Key[\"MediaAudioTrack\"] = \"MediaAudioTrack\";\n    /** Jumps back to the last-viewed content, program, or other media. */\n    Key[\"MediaLast\"] = \"MediaLast\";\n    /** Skips backward to the previous content or program. */\n    Key[\"MediaSkipBackward\"] = \"MediaSkipBackward\";\n    /** Skips forward to the next content or program. */\n    Key[\"MediaSkipForward\"] = \"MediaSkipForward\";\n    /** Steps backward to the previous content or program. */\n    Key[\"MediaStepBackward\"] = \"MediaStepBackward\";\n    /** Steps forward to the next content or program. */\n    Key[\"MediaStepForward\"] = \"MediaStepForward\";\n    /**\n     * Top Menu button. Opens the media's main menu (e.g., for a DVD or Blu-Ray\n     * disc).\n     */\n    Key[\"MediaTopMenu\"] = \"MediaTopMenu\";\n    /** Navigates into a submenu or option. */\n    Key[\"NavigateIn\"] = \"NavigateIn\";\n    /** Navigates to the next item. */\n    Key[\"NavigateNext\"] = \"NavigateNext\";\n    /** Navigates out of the current screen or menu. */\n    Key[\"NavigateOut\"] = \"NavigateOut\";\n    /** Navigates to the previous item. */\n    Key[\"NavigatePrevious\"] = \"NavigatePrevious\";\n    /** Cycles to the next channel in the favorites list. */\n    Key[\"NextFavoriteChannel\"] = \"NextFavoriteChannel\";\n    /**\n     * Cycles to the next saved user profile, if this feature is supported and\n     * multiple profiles exist.\n     */\n    Key[\"NextUserProfile\"] = \"NextUserProfile\";\n    /**\n     * Opens the user interface for selecting on demand content or programs to\n     * watch.\n     */\n    Key[\"OnDemand\"] = \"OnDemand\";\n    /** Starts the process of pairing the remote with a device to be controlled. */\n    Key[\"Pairing\"] = \"Pairing\";\n    /** A button to move the picture-in-picture view downward. */\n    Key[\"PinPDown\"] = \"PinPDown\";\n    /** A button to control moving the picture-in-picture view. */\n    Key[\"PinPMove\"] = \"PinPMove\";\n    /** Toggles display of the picture-in-picture view on and off. */\n    Key[\"PinPToggle\"] = \"PinPToggle\";\n    /** A button to move the picture-in-picture view upward. */\n    Key[\"PinPUp\"] = \"PinPUp\";\n    /** Decreases the media playback rate. */\n    Key[\"PlaySpeedDown\"] = \"PlaySpeedDown\";\n    /** Returns the media playback rate to normal. */\n    Key[\"PlaySpeedReset\"] = \"PlaySpeedReset\";\n    /** Increases the media playback rate. */\n    Key[\"PlaySpeedUp\"] = \"PlaySpeedUp\";\n    /** Toggles random media (also known as \"shuffle mode\") on and off. */\n    Key[\"RandomToggle\"] = \"RandomToggle\";\n    /**\n     * A code sent when the remote control's battery is low. This doesn't\n     * actually correspond to a physical key at all.\n     */\n    Key[\"RcLowBattery\"] = \"RcLowBattery\";\n    /** Cycles among the available media recording speeds. */\n    Key[\"RecordSpeedNext\"] = \"RecordSpeedNext\";\n    /**\n     * Toggles radio frequency (RF) input bypass mode on and off. RF bypass\n     * mode passes RF input directly to the RF output without any processing or\n     * filtering.\n     */\n    Key[\"RfBypass\"] = \"RfBypass\";\n    /**\n     * Toggles the channel scan mode on and off. This is a mode which flips\n     * through channels automatically until the user stops the scan.\n     */\n    Key[\"ScanChannelsToggle\"] = \"ScanChannelsToggle\";\n    /** Cycles through the available screen display modes. */\n    Key[\"ScreenModeNext\"] = \"ScreenModeNext\";\n    /** Toggles display of the device's settings screen on and off. */\n    Key[\"Settings\"] = \"Settings\";\n    /** Toggles split screen display mode on and off. */\n    Key[\"SplitScreenToggle\"] = \"SplitScreenToggle\";\n    /** Cycles among input modes on an external set-top box (STB). */\n    Key[\"STBInput\"] = \"STBInput\";\n    /** Toggles on and off an external STB. */\n    Key[\"STBPower\"] = \"STBPower\";\n    /** Toggles the display of subtitles on and off if they're available. */\n    Key[\"Subtitle\"] = \"Subtitle\";\n    /**\n     * Toggles display of [teletext](https://en.wikipedia.org/wiki/Teletext),\n     * if available.\n     */\n    Key[\"Teletext\"] = \"Teletext\";\n    /** Cycles through the available video modes. */\n    Key[\"VideoModeNext\"] = \"VideoModeNext\";\n    /**\n     * Causes the device to identify itself in some fashion, such as by\n     * flashing a light, briefly changing the brightness of indicator lights,\n     * or emitting a tone.\n     */\n    Key[\"Wink\"] = \"Wink\";\n    /**\n     * Toggles between fullscreen and scaled content display, or otherwise\n     * change the magnification level.\n     */\n    Key[\"ZoomToggle\"] = \"ZoomToggle\";\n    /**\n     * Presents a list of possible corrections for a word which was incorrectly\n     * identified.\n     */\n    Key[\"SpeechCorrectionList\"] = \"SpeechCorrectionList\";\n    /**\n     * Toggles between dictation mode and command/control mode. This lets the\n     * speech engine know whether to interpret spoken words as input text or as\n     * commands.\n     */\n    Key[\"SpeechInputToggle\"] = \"SpeechInputToggle\";\n    /** Closes the current document or message. Must not exit the application. */\n    Key[\"Close\"] = \"Close\";\n    /** Creates a new document or message. */\n    Key[\"New\"] = \"New\";\n    /** Opens an existing document or message. */\n    Key[\"Open\"] = \"Open\";\n    /** Prints the current document or message. */\n    Key[\"Print\"] = \"Print\";\n    /** Saves the current document or message. */\n    Key[\"Save\"] = \"Save\";\n    /** Starts spell checking the current document. */\n    Key[\"SpellCheck\"] = \"SpellCheck\";\n    /** Opens the user interface to forward a message. */\n    Key[\"MailForward\"] = \"MailForward\";\n    /** Opens the user interface to reply to a message. */\n    Key[\"MailReply\"] = \"MailReply\";\n    /** Sends the current message. */\n    Key[\"MailSend\"] = \"MailSend\";\n    /**\n     * The Calculator key, often labeled with an icon. This is often\n     * used as a generic application launcher key\n     * (APPCOMMAND_LAUNCH_APP2).\n     */\n    Key[\"LaunchCalculator\"] = \"LaunchCalculator\";\n    /** The Calendar key. Often labeled with an icon. */\n    Key[\"LaunchCalendar\"] = \"LaunchCalendar\";\n    /** The Contacts key. */\n    Key[\"LaunchContacts\"] = \"LaunchContacts\";\n    /** The Mail key. Often labeled with an icon. */\n    Key[\"LaunchMail\"] = \"LaunchMail\";\n    /** The Media Player key. */\n    Key[\"LaunchMediaPlayer\"] = \"LaunchMediaPlayer\";\n    /** The Music Player key. Often labeled with an icon. */\n    Key[\"LaunchMusicPlayer\"] = \"LaunchMusicPlayer\";\n    /**\n     * The My Computer key on Windows keyboards. This is often used\n     * as a generic application launcher key\n     * (APPCOMMAND_LAUNCH_APP1).\n     */\n    Key[\"LaunchMyComputer\"] = \"LaunchMyComputer\";\n    /**\n     * The Phone key. Opens the phone dialer application (if one is\n     * present).\n     */\n    Key[\"LaunchPhone\"] = \"LaunchPhone\";\n    /** The Screen Saver key. */\n    Key[\"LaunchScreenSaver\"] = \"LaunchScreenSaver\";\n    /** The Spreadsheet key. This key may be labeled with an icon. */\n    Key[\"LaunchSpreadsheet\"] = \"LaunchSpreadsheet\";\n    /**\n     * The Web Browser key. This key is frequently labeled with an\n     * icon.\n     */\n    Key[\"LaunchWebBrowser\"] = \"LaunchWebBrowser\";\n    /** The WebCam key. Opens the webcam application. */\n    Key[\"LaunchWebCam\"] = \"LaunchWebCam\";\n    /**\n     * The Word Processor key. This may be an icon of a specific\n     * word processor application, or a generic document icon.\n     */\n    Key[\"LaunchWordProcessor\"] = \"LaunchWordProcessor\";\n    /** The first generic application launcher button. */\n    Key[\"LaunchApplication1\"] = \"LaunchApplication1\";\n    /** The second generic application launcher button. */\n    Key[\"LaunchApplication2\"] = \"LaunchApplication2\";\n    /** The third generic application launcher button. */\n    Key[\"LaunchApplication3\"] = \"LaunchApplication3\";\n    /** The fourth generic application launcher button. */\n    Key[\"LaunchApplication4\"] = \"LaunchApplication4\";\n    /** The fifth generic application launcher button. */\n    Key[\"LaunchApplication5\"] = \"LaunchApplication5\";\n    /** The sixth generic application launcher button. */\n    Key[\"LaunchApplication6\"] = \"LaunchApplication6\";\n    /** The seventh generic application launcher button. */\n    Key[\"LaunchApplication7\"] = \"LaunchApplication7\";\n    /** The eighth generic application launcher button. */\n    Key[\"LaunchApplication8\"] = \"LaunchApplication8\";\n    /** The ninth generic application launcher button. */\n    Key[\"LaunchApplication9\"] = \"LaunchApplication9\";\n    /** The 10th generic application launcher button. */\n    Key[\"LaunchApplication10\"] = \"LaunchApplication10\";\n    /** The 11th generic application launcher button. */\n    Key[\"LaunchApplication11\"] = \"LaunchApplication11\";\n    /** The 12th generic application launcher button. */\n    Key[\"LaunchApplication12\"] = \"LaunchApplication12\";\n    /** The 13th generic application launcher button. */\n    Key[\"LaunchApplication13\"] = \"LaunchApplication13\";\n    /** The 14th generic application launcher button. */\n    Key[\"LaunchApplication14\"] = \"LaunchApplication14\";\n    /** The 15th generic application launcher button. */\n    Key[\"LaunchApplication15\"] = \"LaunchApplication15\";\n    /** The 16th generic application launcher button. */\n    Key[\"LaunchApplication16\"] = \"LaunchApplication16\";\n    /**\n     * Navigates to the previous content or page in the current Web view's\n     * history.\n     */\n    Key[\"BrowserBack\"] = \"BrowserBack\";\n    /** Opens the user's list of bookmarks/favorites. */\n    Key[\"BrowserFavorites\"] = \"BrowserFavorites\";\n    /** Navigates to the next content or page in the current Web view's history. */\n    Key[\"BrowserForward\"] = \"BrowserForward\";\n    /** Navigates to the user's preferred home page. */\n    Key[\"BrowserHome\"] = \"BrowserHome\";\n    /** Refreshes the current page or content. */\n    Key[\"BrowserRefresh\"] = \"BrowserRefresh\";\n    /**\n     * Activates the user's preferred search engine or the search interface\n     * within their browser.\n     */\n    Key[\"BrowserSearch\"] = \"BrowserSearch\";\n    /** Stops loading the currently displayed Web view or content. */\n    Key[\"BrowserStop\"] = \"BrowserStop\";\n    /**\n     * The decimal point key (typically . or\n     * , depending on the region).\n     * In newer browsers, this value to be the character generated by the\n     * decimal key (one of those two characters). [1]\n     */\n    Key[\"Decimal\"] = \"Decimal\";\n    /** The 11 key found on certain media numeric keypads. */\n    Key[\"Key11\"] = \"Key11\";\n    /** The 12 key found on certain media numeric keypads. */\n    Key[\"Key12\"] = \"Key12\";\n    /** The numeric keypad's multiplication key, *. */\n    Key[\"Multiply\"] = \"Multiply\";\n    /** The numeric keypad's addition key, +. */\n    Key[\"Add\"] = \"Add\";\n    /** The numeric keypad's division key, /. */\n    Key[\"Divide\"] = \"Divide\";\n    /** The numeric keypad's subtraction key, -. */\n    Key[\"Subtract\"] = \"Subtract\";\n    /**\n     * The numeric keypad's places separator character.\n     * (In the United States this is a comma, but elsewhere it is frequently\n     * a period.)\n     */\n    Key[\"Separator\"] = \"Separator\";\n})(Key = exports.Key || (exports.Key = {}));\n"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAO5D,QAAI;AACJ,KAAC,SAAUA,MAAK;AAOZ,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,KAAK,IAAI;AAMb,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,SAAS,IAAI;AAOjB,MAAAA,KAAI,IAAI,IAAI;AAMZ,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,OAAO,IAAI;AAMf,MAAAA,KAAI,MAAM,IAAI;AAKd,MAAAA,KAAI,SAAS,IAAI;AAKjB,MAAAA,KAAI,YAAY,IAAI;AAMpB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,MAAM,IAAI;AAKd,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,OAAO,IAAI;AAKf,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,MAAM,IAAI;AAMd,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,QAAQ,IAAI;AAMhB,MAAAA,KAAI,aAAa,IAAI;AAOrB,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,SAAS,IAAI;AAKjB,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,MAAM,IAAI;AAQd,MAAAA,KAAI,OAAO,IAAI;AAQf,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,SAAS,IAAI;AAKjB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,QAAQ,IAAI;AAMhB,MAAAA,KAAI,OAAO,IAAI;AAKf,MAAAA,KAAI,UAAU,IAAI;AAMlB,MAAAA,KAAI,aAAa,IAAI;AAMrB,MAAAA,KAAI,WAAW,IAAI;AAMnB,MAAAA,KAAI,SAAS,IAAI;AAKjB,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,cAAc,IAAI;AAMtB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,SAAS,IAAI;AAKjB,MAAAA,KAAI,SAAS,IAAI;AAWjB,MAAAA,KAAI,MAAM,IAAI;AAKd,MAAAA,KAAI,WAAW,IAAI;AAOnB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,WAAW,IAAI;AAKnB,MAAAA,KAAI,WAAW,IAAI;AAKnB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,eAAe,IAAI;AAMvB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,SAAS,IAAI;AAMjB,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,WAAW,IAAI;AAKnB,MAAAA,KAAI,WAAW,IAAI;AAKnB,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,OAAO,IAAI;AAKf,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,QAAQ,IAAI;AAKhB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,kBAAkB,IAAI;AAM1B,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,aAAa,IAAI;AAMrB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,eAAe,IAAI;AAKvB,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,sBAAsB,IAAI;AAK9B,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,iBAAiB,IAAI;AAEzB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,uBAAuB,IAAI;AAE/B,MAAAA,KAAI,iBAAiB,IAAI;AAEzB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,iBAAiB,IAAI;AAEzB,MAAAA,KAAI,iBAAiB,IAAI;AAEzB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,sBAAsB,IAAI;AAE9B,MAAAA,KAAI,sBAAsB,IAAI;AAE9B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,IAAI,IAAI;AAEZ,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,oBAAoB,IAAI;AAK5B,MAAAA,KAAI,2BAA2B,IAAI;AAKnC,MAAAA,KAAI,yBAAyB,IAAI;AAMjC,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,mBAAmB,IAAI;AAK3B,MAAAA,KAAI,qBAAqB,IAAI;AAK7B,MAAAA,KAAI,sBAAsB,IAAI;AAE9B,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,eAAe,IAAI;AAKvB,MAAAA,KAAI,aAAa,IAAI;AAKrB,MAAAA,KAAI,aAAa,IAAI;AAKrB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,qBAAqB,IAAI;AAK7B,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,OAAO,IAAI;AAKf,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,kBAAkB,IAAI;AAK1B,MAAAA,KAAI,MAAM,IAAI;AAMd,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,MAAM,IAAI;AAKd,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,iBAAiB,IAAI;AAEzB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,kBAAkB,IAAI;AAK1B,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,qBAAqB,IAAI;AAK7B,MAAAA,KAAI,iBAAiB,IAAI;AAKzB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,cAAc,IAAI;AAEtB,MAAAA,KAAI,iBAAiB,IAAI;AAMzB,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,UAAU,IAAI;AAKlB,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,eAAe,IAAI;AAMvB,MAAAA,KAAI,MAAM,IAAI;AAKd,MAAAA,KAAI,YAAY,IAAI;AAKpB,MAAAA,KAAI,sBAAsB,IAAI;AAM9B,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,MAAM,IAAI;AAEd,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,WAAW,IAAI;AAEnB,MAAAA,KAAI,UAAU,IAAI;AAMlB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,YAAY,IAAI;AAEpB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,mBAAmB,IAAI;AAM3B,MAAAA,KAAI,kBAAkB,IAAI;AAK1B,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,mBAAmB,IAAI;AAE3B,MAAAA,KAAI,mBAAmB,IAAI;AAK3B,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,cAAc,IAAI;AAKtB,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,oBAAoB,IAAI;AAE5B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAE7B,MAAAA,KAAI,qBAAqB,IAAI;AAK7B,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,kBAAkB,IAAI;AAE1B,MAAAA,KAAI,gBAAgB,IAAI;AAExB,MAAAA,KAAI,aAAa,IAAI;AAErB,MAAAA,KAAI,gBAAgB,IAAI;AAKxB,MAAAA,KAAI,eAAe,IAAI;AAEvB,MAAAA,KAAI,aAAa,IAAI;AAOrB,MAAAA,KAAI,SAAS,IAAI;AAEjB,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,OAAO,IAAI;AAEf,MAAAA,KAAI,UAAU,IAAI;AAElB,MAAAA,KAAI,KAAK,IAAI;AAEb,MAAAA,KAAI,QAAQ,IAAI;AAEhB,MAAAA,KAAI,UAAU,IAAI;AAMlB,MAAAA,KAAI,WAAW,IAAI;AAAA,IACvB,GAAG,MAAM,QAAQ,QAAQ,QAAQ,MAAM,CAAC,EAAE;AAAA;AAAA;", "names": ["Key"]}
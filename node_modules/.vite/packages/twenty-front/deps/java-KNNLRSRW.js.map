{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/java/java.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/java/java.ts\nvar conf = {\n  // the default separators except `@$`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".java\",\n  keywords: [\n    \"abstract\",\n    \"continue\",\n    \"for\",\n    \"new\",\n    \"switch\",\n    \"assert\",\n    \"default\",\n    \"goto\",\n    \"package\",\n    \"synchronized\",\n    \"boolean\",\n    \"do\",\n    \"if\",\n    \"private\",\n    \"this\",\n    \"break\",\n    \"double\",\n    \"implements\",\n    \"protected\",\n    \"throw\",\n    \"byte\",\n    \"else\",\n    \"import\",\n    \"public\",\n    \"throws\",\n    \"case\",\n    \"enum\",\n    \"instanceof\",\n    \"return\",\n    \"transient\",\n    \"catch\",\n    \"extends\",\n    \"int\",\n    \"short\",\n    \"try\",\n    \"char\",\n    \"final\",\n    \"interface\",\n    \"static\",\n    \"void\",\n    \"class\",\n    \"finally\",\n    \"long\",\n    \"strictfp\",\n    \"volatile\",\n    \"const\",\n    \"float\",\n    \"native\",\n    \"super\",\n    \"while\",\n    \"true\",\n    \"false\",\n    \"yield\",\n    \"record\",\n    \"sealed\",\n    \"non-sealed\",\n    \"permits\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \">>>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // Special keyword with a dash\n      [\"non-sealed\", \"keyword.non-sealed\"],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // @ annotations.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, \"annotation\"],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\"],\n      [/0(@octaldigits)[Ll]?/, \"number.octal\"],\n      [/0[bB](@binarydigits)[Ll]?/, \"number.binary\"],\n      [/(@digits)[fFdD]/, \"number.float\"],\n      [/(@digits)[lL]?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"\"\"/, \"string\", \"@multistring\"],\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@javadoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested comment not allowed :-(\n      // [/\\/\\*/,    'comment.invalid' ],    // this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    javadoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      // [/\\/\\*/, 'comment.doc', '@push' ],    // nested comment not allowed :-(\n      [/\\/\\*/, \"comment.doc.invalid\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    multistring: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"\"\"/, \"string\", \"@pop\"],\n      [/./, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAkCA;AA3CJ;AAAA;AASA,IAAI,OAAO;AAAA;AAAA,MAET,aAAa;AAAA,MACb,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,oDAAoD;AAAA,UACtE,KAAK,IAAI,OAAO,sDAAsD;AAAA,QACxE;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,cAAc;AAAA,MACd,WAAW;AAAA;AAAA,MAEX,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,cAAc,oBAAoB;AAAA;AAAA,UAEnC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa,EAAE,OAAO,aAAa;AAAA,gBACnC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,oBAAoB,WAAW;AAAA,UAChC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,0BAA0B,YAAY;AAAA;AAAA,UAEvC,CAAC,0CAA0C,cAAc;AAAA,UACzD,CAAC,qDAAqD,cAAc;AAAA,UACpE,CAAC,0BAA0B,YAAY;AAAA,UACvC,CAAC,wBAAwB,cAAc;AAAA,UACvC,CAAC,6BAA6B,eAAe;AAAA,UAC7C,CAAC,mBAAmB,cAAc;AAAA,UAClC,CAAC,kBAAkB,QAAQ;AAAA;AAAA,UAE3B,CAAC,SAAS,WAAW;AAAA;AAAA,UAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,OAAO,UAAU,cAAc;AAAA,UAChC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,UAEzB,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,UAC1D,CAAC,KAAK,gBAAgB;AAAA,QACxB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,gBAAgB,eAAe,UAAU;AAAA,UAC1C,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA;AAAA;AAAA,UAGrB,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA;AAAA,QAEA,SAAS;AAAA,UACP,CAAC,WAAW,aAAa;AAAA;AAAA,UAEzB,CAAC,QAAQ,qBAAqB;AAAA,UAC9B,CAAC,QAAQ,eAAe,MAAM;AAAA,UAC9B,CAAC,SAAS,aAAa;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,QACxB;AAAA,QACA,aAAa;AAAA,UACX,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,OAAO,UAAU,MAAM;AAAA,UACxB,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
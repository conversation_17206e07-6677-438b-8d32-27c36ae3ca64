{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/ViewLayout/ViewLayoutContent.vue.js"], "sourcesContent": ["import { openBlock as e, createElementBlock as o, renderSlot as t } from \"vue\";\nimport l from \"../../_virtual/_plugin-vue_export-helper.js\";\nconst c = {}, s = { class: \"*:border-t-1/2 xl:*:border-l-1/2 custom-scroll flex flex-col pr-0 first:*:border-t-0 xl:flex-row xl:*:border-t-0 xl:first:*:border-l-0\" };\nfunction n(r, f) {\n  return e(), o(\"div\", s, [\n    t(r.$slots, \"default\")\n  ]);\n}\nconst x = /* @__PURE__ */ l(c, [[\"render\", n]]);\nexport {\n  x as default\n};\n"], "mappings": ";;;;;;;;;;AAEA,IAAM,IAAI,CAAC;AAAX,IAAcA,KAAI,EAAE,OAAO,yIAAyI;AACpK,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,UAAE,GAAG,mBAAE,OAAOA,IAAG;AAAA,IACtB,WAAE,EAAE,QAAQ,SAAS;AAAA,EACvB,CAAC;AACH;AACA,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;", "names": ["s"]}
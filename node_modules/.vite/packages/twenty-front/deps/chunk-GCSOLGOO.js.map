{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/dialog/dialog.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// Open simple dialogs on top of an editor. Relies on dialog.css.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  function dialogDiv(cm, template, bottom) {\n    var wrap = cm.getWrapperElement();\n    var dialog;\n    dialog = wrap.appendChild(document.createElement(\"div\"));\n    if (bottom)\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-bottom\";\n    else\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-top\";\n\n    if (typeof template == \"string\") {\n      dialog.innerHTML = template;\n    } else { // Assuming it's a detached DOM element.\n      dialog.appendChild(template);\n    }\n    CodeMirror.addClass(wrap, 'dialog-opened');\n    return dialog;\n  }\n\n  function closeNotification(cm, newVal) {\n    if (cm.state.currentNotificationClose)\n      cm.state.currentNotificationClose();\n    cm.state.currentNotificationClose = newVal;\n  }\n\n  CodeMirror.defineExtension(\"openDialog\", function(template, callback, options) {\n    if (!options) options = {};\n\n    closeNotification(this, null);\n\n    var dialog = dialogDiv(this, template, options.bottom);\n    var closed = false, me = this;\n    function close(newVal) {\n      if (typeof newVal == 'string') {\n        inp.value = newVal;\n      } else {\n        if (closed) return;\n        closed = true;\n        CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n        dialog.parentNode.removeChild(dialog);\n        me.focus();\n\n        if (options.onClose) options.onClose(dialog);\n      }\n    }\n\n    var inp = dialog.getElementsByTagName(\"input\")[0], button;\n    if (inp) {\n      inp.focus();\n\n      if (options.value) {\n        inp.value = options.value;\n        if (options.selectValueOnOpen !== false) {\n          inp.select();\n        }\n      }\n\n      if (options.onInput)\n        CodeMirror.on(inp, \"input\", function(e) { options.onInput(e, inp.value, close);});\n      if (options.onKeyUp)\n        CodeMirror.on(inp, \"keyup\", function(e) {options.onKeyUp(e, inp.value, close);});\n\n      CodeMirror.on(inp, \"keydown\", function(e) {\n        if (options && options.onKeyDown && options.onKeyDown(e, inp.value, close)) { return; }\n        if (e.keyCode == 27 || (options.closeOnEnter !== false && e.keyCode == 13)) {\n          inp.blur();\n          CodeMirror.e_stop(e);\n          close();\n        }\n        if (e.keyCode == 13) callback(inp.value, e);\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(dialog, \"focusout\", function (evt) {\n        if (evt.relatedTarget !== null) close();\n      });\n    } else if (button = dialog.getElementsByTagName(\"button\")[0]) {\n      CodeMirror.on(button, \"click\", function() {\n        close();\n        me.focus();\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(button, \"blur\", close);\n\n      button.focus();\n    }\n    return close;\n  });\n\n  CodeMirror.defineExtension(\"openConfirm\", function(template, callbacks, options) {\n    closeNotification(this, null);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var buttons = dialog.getElementsByTagName(\"button\");\n    var closed = false, me = this, blurring = 1;\n    function close() {\n      if (closed) return;\n      closed = true;\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n      me.focus();\n    }\n    buttons[0].focus();\n    for (var i = 0; i < buttons.length; ++i) {\n      var b = buttons[i];\n      (function(callback) {\n        CodeMirror.on(b, \"click\", function(e) {\n          CodeMirror.e_preventDefault(e);\n          close();\n          if (callback) callback(me);\n        });\n      })(callbacks[i]);\n      CodeMirror.on(b, \"blur\", function() {\n        --blurring;\n        setTimeout(function() { if (blurring <= 0) close(); }, 200);\n      });\n      CodeMirror.on(b, \"focus\", function() { ++blurring; });\n    }\n  });\n\n  /*\n   * openNotification\n   * Opens a notification, that can be closed with an optional timer\n   * (default 5000ms timer) and always closes on click.\n   *\n   * If a notification is opened while another is opened, it will close the\n   * currently opened one and open the new one immediately.\n   */\n  CodeMirror.defineExtension(\"openNotification\", function(template, options) {\n    closeNotification(this, close);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var closed = false, doneTimer;\n    var duration = options && typeof options.duration !== \"undefined\" ? options.duration : 5000;\n\n    function close() {\n      if (closed) return;\n      closed = true;\n      clearTimeout(doneTimer);\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n    }\n\n    CodeMirror.on(dialog, 'click', function(e) {\n      CodeMirror.e_preventDefault(e);\n      close();\n    });\n\n    if (duration)\n      doneTimer = setTimeout(close, duration);\n\n    return close;\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA+B,CAAA;EAKtC,GAAE,SAAS,YAAY;AACtB,aAAS,UAAU,IAAI,UAAU,QAAQ;AACvC,UAAI,OAAO,GAAG,kBAAA;AACd,UAAIC;AACJA,gBAAS,KAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AACvD,UAAI;AACFA,gBAAO,YAAY;;AAEnBA,gBAAO,YAAY;AAErB,UAAI,OAAO,YAAY,UAAU;AAC/BA,gBAAO,YAAY;MACzB,OAAW;AACLA,gBAAO,YAAY,QAAQ;MAC5B;AACD,iBAAW,SAAS,MAAM,eAAe;AACzC,aAAOA;IACR;AAED,aAAS,kBAAkB,IAAI,QAAQ;AACrC,UAAI,GAAG,MAAM;AACX,WAAG,MAAM,yBAAA;AACX,SAAG,MAAM,2BAA2B;IACrC;AAED,eAAW,gBAAgB,cAAc,SAAS,UAAU,UAAU,SAAS;AAC7E,UAAI,CAAC,QAAS,WAAU,CAAA;AAExB,wBAAkB,MAAM,IAAI;AAE5B,UAAIA,UAAS,UAAU,MAAM,UAAU,QAAQ,MAAM;AACrD,UAAI,SAAS,OAAO,KAAK;AACzB,eAAS,MAAM,QAAQ;AACrB,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,QAAQ;QACpB,OAAa;AACL,cAAI,OAAQ;AACZ,mBAAS;AACT,qBAAW,QAAQA,QAAO,YAAY,eAAe;AACrDA,kBAAO,WAAW,YAAYA,OAAM;AACpC,aAAG,MAAK;AAER,cAAI,QAAQ,QAAS,SAAQ,QAAQA,OAAM;QAC5C;MACF;AAED,UAAI,MAAMA,QAAO,qBAAqB,OAAO,EAAE,CAAC,GAAG;AACnD,UAAI,KAAK;AACP,YAAI,MAAK;AAET,YAAI,QAAQ,OAAO;AACjB,cAAI,QAAQ,QAAQ;AACpB,cAAI,QAAQ,sBAAsB,OAAO;AACvC,gBAAI,OAAM;UACX;QACF;AAED,YAAI,QAAQ;AACV,qBAAW,GAAG,KAAK,SAAS,SAAS,GAAG;AAAE,oBAAQ,QAAQ,GAAG,IAAI,OAAO,KAAK;UAAE,CAAC;AAClF,YAAI,QAAQ;AACV,qBAAW,GAAG,KAAK,SAAS,SAAS,GAAG;AAAC,oBAAQ,QAAQ,GAAG,IAAI,OAAO,KAAK;UAAE,CAAC;AAEjF,mBAAW,GAAG,KAAK,WAAW,SAAS,GAAG;AACxC,cAAI,WAAW,QAAQ,aAAa,QAAQ,UAAU,GAAG,IAAI,OAAO,KAAK,GAAG;AAAE;UAAS;AACvF,cAAI,EAAE,WAAW,MAAO,QAAQ,iBAAiB,SAAS,EAAE,WAAW,IAAK;AAC1E,gBAAI,KAAI;AACR,uBAAW,OAAO,CAAC;AACnB,kBAAA;UACD;AACD,cAAI,EAAE,WAAW,GAAI,UAAS,IAAI,OAAO,CAAC;QAClD,CAAO;AAED,YAAI,QAAQ,gBAAgB,MAAO,YAAW,GAAGA,SAAQ,YAAY,SAAU,KAAK;AAClF,cAAI,IAAI,kBAAkB,KAAM,OAAK;QAC7C,CAAO;MACP,WAAe,SAASA,QAAO,qBAAqB,QAAQ,EAAE,CAAC,GAAG;AAC5D,mBAAW,GAAG,QAAQ,SAAS,WAAW;AACxC,gBAAA;AACA,aAAG,MAAK;QAChB,CAAO;AAED,YAAI,QAAQ,gBAAgB,MAAO,YAAW,GAAG,QAAQ,QAAQ,KAAK;AAEtE,eAAO,MAAK;MACb;AACD,aAAO;IACX,CAAG;AAED,eAAW,gBAAgB,eAAe,SAAS,UAAU,WAAW,SAAS;AAC/E,wBAAkB,MAAM,IAAI;AAC5B,UAAIA,UAAS,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM;AAChE,UAAI,UAAUA,QAAO,qBAAqB,QAAQ;AAClD,UAAI,SAAS,OAAO,KAAK,MAAM,WAAW;AAC1C,eAAS,QAAQ;AACf,YAAI,OAAQ;AACZ,iBAAS;AACT,mBAAW,QAAQA,QAAO,YAAY,eAAe;AACrDA,gBAAO,WAAW,YAAYA,OAAM;AACpC,WAAG,MAAK;MACT;AACD,cAAQ,CAAC,EAAE,MAAA;AACX,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,YAAI,IAAI,QAAQ,CAAC;AACjB,SAAC,SAAS,UAAU;AAClB,qBAAW,GAAG,GAAG,SAAS,SAAS,GAAG;AACpC,uBAAW,iBAAiB,CAAC;AAC7B,kBAAA;AACA,gBAAI,SAAU,UAAS,EAAE;UACnC,CAAS;QACT,GAAS,UAAU,CAAC,CAAC;AACf,mBAAW,GAAG,GAAG,QAAQ,WAAW;AAClC,YAAE;AACF,qBAAW,WAAW;AAAE,gBAAI,YAAY,EAAG,OAAA;UAAQ,GAAI,GAAG;QAClE,CAAO;AACD,mBAAW,GAAG,GAAG,SAAS,WAAW;AAAE,YAAE;QAAS,CAAE;MACrD;IACL,CAAG;AAUD,eAAW,gBAAgB,oBAAoB,SAAS,UAAU,SAAS;AACzE,wBAAkB,MAAM,KAAK;AAC7B,UAAIA,UAAS,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM;AAChE,UAAI,SAAS,OAAO;AACpB,UAAI,WAAW,WAAW,OAAO,QAAQ,aAAa,cAAc,QAAQ,WAAW;AAEvF,eAAS,QAAQ;AACf,YAAI,OAAQ;AACZ,iBAAS;AACT,qBAAa,SAAS;AACtB,mBAAW,QAAQA,QAAO,YAAY,eAAe;AACrDA,gBAAO,WAAW,YAAYA,OAAM;MACrC;AAED,iBAAW,GAAGA,SAAQ,SAAS,SAAS,GAAG;AACzC,mBAAW,iBAAiB,CAAC;AAC7B,cAAA;MACN,CAAK;AAED,UAAI;AACF,oBAAY,WAAW,OAAO,QAAQ;AAExC,aAAO;IACX,CAAG;EACH,CAAC;;;;;;;;", "names": ["require$$0", "dialog"]}
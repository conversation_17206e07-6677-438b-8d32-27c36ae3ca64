{"version": 3, "sources": ["../../../../date-fns/esm/locale/de/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/de/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/de/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/de/_lib/localize/index.js", "../../../../date-fns/esm/locale/de/_lib/match/index.js", "../../../../date-fns/esm/locale/de/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    },\n    withPreposition: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    }\n  },\n  halfAMinute: {\n    standalone: 'halbe Minute',\n    withPreposition: 'halben Minute'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    },\n    withPreposition: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    },\n    withPreposition: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    },\n    withPreposition: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: '1 Tag',\n      other: '{{count}} Tage'\n    },\n    withPreposition: {\n      one: '1 Tag',\n      other: '{{count}} Tagen'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monate'\n    },\n    withPreposition: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monaten'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: '1 Monat',\n      other: '{{count}} Monate'\n    },\n    withPreposition: {\n      one: '1 Monat',\n      other: '{{count}} Monaten'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahren'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: '1 Jahr',\n      other: '{{count}} Jahre'\n    },\n    withPreposition: {\n      one: '1 Jahr',\n      other: '{{count}} Jahren'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahren'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahren'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return 'vor ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\nvar dateFormats = {\n  full: 'EEEE, do MMMM y',\n  // Montag, 7. Januar 2018\n  long: 'do MMMM y',\n  // 7. Januar 2018\n  medium: 'do MMM y',\n  // 7. Jan. 2018\n  short: 'dd.MM.y' // 07.01.2018\n};\n\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'um' {{time}}\",\n  long: \"{{date}} 'um' {{time}}\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "var formatRelativeLocale = {\n  lastWeek: \"'letzten' eeee 'um' p\",\n  yesterday: \"'gestern um' p\",\n  today: \"'heute um' p\",\n  tomorrow: \"'morgen um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['v.Chr.', 'n.Chr.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['vor <PERSON><PERSON>', 'nach <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal']\n};\n\n// Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['<PERSON>', 'Feb', '<PERSON>är', 'Apr', '<PERSON>', '<PERSON>', '<PERSON>', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],\n  wide: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember']\n};\n\n// https://st.unicode.org/cldr-apps/v#/de/Gregorian/\nvar formattingMonthValues = {\n  narrow: monthValues.narrow,\n  abbreviated: ['Jan.', 'Feb.', 'März', 'Apr.', 'Mai', 'Juni', 'Juli', 'Aug.', 'Sep.', 'Okt.', 'Nov.', 'Dez.'],\n  wide: monthValues.wide\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'M', 'D', 'F', 'S'],\n  short: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],\n  abbreviated: ['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],\n  wide: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/de.html#1881\nvar dayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachm.',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachm.',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    formattingValues: formattingMonthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  wide: /^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i\n};\nvar parseEraPatterns = {\n  any: [/^v/i, /^n/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](\\.)? Quartal/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\\.?/i,\n  wide: /^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^j[aä]/i, /^f/i, /^mär/i, /^ap/i, /^mai/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smdmf]/i,\n  short: /^(so|mo|di|mi|do|fr|sa)/i,\n  abbreviated: /^(son?|mon?|die?|mit?|don?|fre?|sam?)\\.?/i,\n  wide: /^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i\n};\nvar parseDayPatterns = {\n  any: [/^so/i, /^mo/i, /^di/i, /^mi/i, /^do/i, /^f/i, /^sa/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(vm\\.?|nm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n  abbreviated: /^(vorm\\.?|nachm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n  wide: /^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^v/i,\n    pm: /^n/i,\n    midnight: /^Mitte/i,\n    noon: /^Mitta/i,\n    morning: /morgens/i,\n    afternoon: /nachmittags/i,\n    // will never be matched. Afternoon is matched by `pm`\n    evening: /abends/i,\n    night: /nachts/i // will never be matched. Night is matched by `pm`\n  }\n};\n\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@DeMuu]{@link https://github.com/DeMuu}\n * <AUTHOR> [@asia-t]{@link https://github.com/asia-t}\n * <AUTHOR> [@vanvuongngo]{@link https://github.com/vanvuongngo}\n * <AUTHOR> [@pex]{@link https://github.com/pex}\n * <AUTHOR> [@Philipp91]{@link https://github.com/Philipp91}\n */\nvar locale = {\n  code: 'de',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;AAAA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,IAChB,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,IAChB,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAClE,MAAI;AACJ,MAAI,aAAa,YAAY,QAAQ,YAAY,UAAU,QAAQ,YAAY,qBAAqB,KAAK,EAAE,kBAAkB,qBAAqB,KAAK,EAAE;AACzJ,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAC9D;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;AC7Kf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA;AAAA,EAEN,QAAQ;AAAA;AAAA,EAER,OAAO;AAAA;AACT;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;ACtCf,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,OAAO,WAAW,UAAU;AAC9E,SAAO,qBAAqB,KAAK;AACnC;AACA,IAAO,yBAAQ;;;ACVf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,UAAU,QAAQ;AAAA,EAC3B,aAAa,CAAC,UAAU,QAAQ;AAAA,EAChC,MAAM,CAAC,gBAAgB,eAAe;AACxC;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,cAAc,cAAc,cAAc,YAAY;AAC/D;AAMA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;AAC9H;AAGA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,YAAY;AAAA,EACpB,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC3G,MAAM,YAAY;AACpB;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,WAAW,UAAU,YAAY,YAAY,cAAc,WAAW,SAAS;AACxF;AAGA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa;AACtD,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAO,mBAAQ;;;ACnIf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,WAAW,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACxG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AAC7D;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA;AAAA,IAEX,SAAS;AAAA,IACT,OAAO;AAAA;AAAA,EACT;AACF;AAEA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;AClFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["formatDistance", "formatRelative", "ordinalNumber", "valueCallback"]}
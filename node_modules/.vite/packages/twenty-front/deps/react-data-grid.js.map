{"version": 3, "sources": ["../../../../react-data-grid/node_modules/clsx/dist/clsx.m.js", "../../../../react-data-grid/node_modules/style-inject/dist/style-inject.es.js", "../../../../react-data-grid/src/style/cell.ts", "../../../../react-data-grid/src/style/core.ts", "../../../../react-data-grid/src/style/row.ts", "../../../../react-data-grid/src/formatters/CheckboxFormatter.tsx", "../../../../react-data-grid/src/hooks/useLayoutEffect.ts", "../../../../react-data-grid/src/hooks/useFocusRef.ts", "../../../../react-data-grid/src/DataGridDefaultComponentsProvider.ts", "../../../../react-data-grid/src/formatters/SelectCellFormatter.tsx", "../../../../react-data-grid/src/formatters/ValueFormatter.tsx", "../../../../react-data-grid/src/formatters/ToggleGroupFormatter.tsx", "../../../../react-data-grid/src/hooks/useRowSelection.ts", "../../../../react-data-grid/src/Columns.tsx", "../../../../react-data-grid/src/utils/colSpanUtils.ts", "../../../../react-data-grid/src/utils/domUtils.ts", "../../../../react-data-grid/src/utils/keyboardUtils.ts", "../../../../react-data-grid/src/utils/selectedCellUtils.ts", "../../../../react-data-grid/src/utils/styleUtils.ts", "../../../../react-data-grid/src/utils/index.ts", "../../../../react-data-grid/src/hooks/useCalculatedColumns.ts", "../../../../react-data-grid/src/hooks/useGridDimensions.ts", "../../../../react-data-grid/src/hooks/useLatestFunc.ts", "../../../../react-data-grid/src/hooks/useRovingCellRef.ts", "../../../../react-data-grid/src/hooks/useViewportColumns.ts", "../../../../react-data-grid/src/hooks/useViewportRows.ts", "../../../../react-data-grid/src/HeaderRenderer.tsx", "../../../../react-data-grid/src/HeaderCell.tsx", "../../../../react-data-grid/src/HeaderRow.tsx", "../../../../react-data-grid/src/Cell.tsx", "../../../../react-data-grid/src/Row.tsx", "../../../../react-data-grid/src/GroupCell.tsx", "../../../../react-data-grid/src/GroupRow.tsx", "../../../../react-data-grid/src/SummaryCell.tsx", "../../../../react-data-grid/src/SummaryRow.tsx", "../../../../react-data-grid/src/EditCell.tsx", "../../../../react-data-grid/src/DragHandle.tsx", "../../../../react-data-grid/src/SortIcon.tsx", "../../../../react-data-grid/src/DataGrid.tsx", "../../../../react-data-grid/src/editors/TextEditor.tsx"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", "import { css } from '@linaria/core';\n\nexport const cell = css`\n  /*\n  Cannot use these because of a Chromium bug:\n  https://bugs.chromium.org/p/chromium/issues/detail?id=1326946\n  once this is fixed we can also remove \"position: relative:\"\n  contain: strict;\n  contain: size layout style paint;\n  */\n  position: relative; /* needed for absolute positioning to work */\n  contain: size style;\n  padding-block: 0;\n  padding-inline: 8px;\n  border-inline-end: 1px solid var(--rdg-border-color);\n  border-block-end: 1px solid var(--rdg-border-color);\n  grid-row-start: var(--rdg-grid-row-start);\n  background-color: inherit;\n\n  white-space: nowrap;\n  overflow: hidden;\n  overflow: clip;\n  text-overflow: ellipsis;\n  outline: none;\n\n  &[aria-selected='true'] {\n    outline: 2px solid var(--rdg-selection-color);\n    outline-offset: -2px;\n  }\n`;\n\nexport const cellClassname = `rdg-cell ${cell}`;\n\n// max-content does not calculate width when contain is set to style or size\nexport const cellAutoResizeClassname = css`\n  .${cell} {\n    contain: content;\n  }\n`;\n\nexport const cellFrozen = css`\n  position: sticky;\n  /* Should have a higher value than 0 to show up above unfrozen cells */\n  z-index: 1;\n`;\n\nexport const cellFrozenClassname = `rdg-cell-frozen ${cellFrozen}`;\n\nexport const cellFrozenLast = css`\n  box-shadow: calc(2px * var(--rdg-sign)) 0 5px -2px rgba(136, 136, 136, 0.3);\n`;\n\nexport const cellFrozenLastClassname = `rdg-cell-frozen-last ${cellFrozenLast}`;\n", "import { css } from '@linaria/core';\nimport { row } from './row';\n\nconst lightTheme = `\n  --rdg-color: #000;\n  --rdg-border-color: #ddd;\n  --rdg-summary-border-color: #aaa;\n  --rdg-background-color: hsl(0deg 0% 100%);\n  --rdg-header-background-color: hsl(0deg 0% 97.5%);\n  --rdg-row-hover-background-color: hsl(0deg 0% 96%);\n  --rdg-row-selected-background-color: hsl(207deg 76% 92%);\n  --row-selected-hover-background-color: hsl(207deg 76% 88%);\n\n  --rdg-checkbox-color: hsl(207deg 100% 29%);\n  --rdg-checkbox-focus-color: hsl(207deg 100% 69%);\n  --rdg-checkbox-disabled-border-color: #ccc;\n  --rdg-checkbox-disabled-background-color: #ddd;\n`;\n\nconst darkTheme = `\n  --rdg-color: #ddd;\n  --rdg-border-color: #444;\n  --rdg-summary-border-color: #555;\n  --rdg-background-color: hsl(0deg 0% 13%);\n  --rdg-header-background-color: hsl(0deg 0% 10.5%);\n  --rdg-row-hover-background-color: hsl(0deg 0% 9%);\n  --rdg-row-selected-background-color: hsl(207deg 76% 42%);\n  --row-selected-hover-background-color: hsl(207deg 76% 38%);\n\n  --rdg-checkbox-color: hsl(207deg 100% 79%);\n  --rdg-checkbox-focus-color: hsl(207deg 100% 89%);\n  --rdg-checkbox-disabled-border-color: #000;\n  --rdg-checkbox-disabled-background-color: #333;\n`;\n\nconst root = css`\n  ${lightTheme}\n  --rdg-selection-color: #66afe9;\n  --rdg-font-size: 14px;\n\n  display: grid;\n\n  color-scheme: var(--rdg-color-scheme, light dark);\n\n  /* https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context */\n  /* We set a stacking context so internal elements don't render on top of external components. */\n  contain: strict;\n  contain: size layout style paint;\n  content-visibility: auto;\n  block-size: 350px;\n  border: 1px solid var(--rdg-border-color);\n  box-sizing: border-box;\n  overflow: auto;\n  user-select: none;\n  background-color: var(--rdg-background-color);\n  color: var(--rdg-color);\n  font-size: var(--rdg-font-size);\n\n  /* set stacking context in safari */\n  @supports not (contain: strict) {\n    position: relative;\n    z-index: 0;\n  }\n\n  *,\n  *::before,\n  *::after {\n    box-sizing: inherit;\n  }\n\n  /* needed on Firefox */\n  &::before {\n    content: '';\n    grid-column: 1/-1;\n    grid-row: 1/-1;\n  }\n\n  &.rdg-dark {\n    --rdg-color-scheme: dark;\n    ${darkTheme}\n  }\n\n  &.rdg-light {\n    --rdg-color-scheme: light;\n  }\n\n  @media (prefers-color-scheme: dark) {\n    &:not(.rdg-light) {\n      ${darkTheme}\n    }\n  }\n`;\n\nexport const rootClassname = `rdg ${root}`;\n\nconst viewportDragging = css`\n  &.${row} {\n    cursor: move;\n  }\n`;\n\nexport const viewportDraggingClassname = `rdg-viewport-dragging ${viewportDragging}`;\n\nexport const focusSinkClassname = css`\n  grid-column: 1/-1;\n  pointer-events: none;\n  /* Should have a higher value than 3 to show up above header row */\n  z-index: 4;\n`;\n", "import { css } from '@linaria/core';\n\nexport const row = css`\n  display: contents;\n  line-height: var(--rdg-row-height);\n  background-color: var(--rdg-background-color);\n\n  &:hover {\n    background-color: var(--rdg-row-hover-background-color);\n  }\n\n  &[aria-selected='true'] {\n    background-color: var(--rdg-row-selected-background-color);\n\n    &:hover {\n      background-color: var(--row-selected-hover-background-color);\n    }\n  }\n`;\n\nexport const rowClassname = `rdg-row ${row}`;\n\nexport const rowSelected = css`\n  outline: 2px solid var(--rdg-selection-color);\n  outline-offset: -2px;\n`;\n\nexport const rowSelectedClassname = `rdg-row-selected`;\n\nexport const rowSelectedWithFrozenCell = css`\n  &::before {\n    content: '';\n    display: inline-block;\n    height: 100%;\n    position: sticky;\n    inset-inline-start: 0;\n    border-inline-start: 2px solid var(--rdg-selection-color);\n  }\n`;\n", "import { forwardRef } from 'react';\nimport clsx from 'clsx';\nimport { css } from '@linaria/core';\n\nimport type { CheckboxFormatterProps } from '../types';\n\nconst checkboxLabel = css`\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: absolute;\n  inset: 0;\n  margin-inline-end: 1px; /* align checkbox in row group cell */\n`;\n\nconst checkboxLabelClassname = `rdg-checkbox-label ${checkboxLabel}`;\n\nconst checkboxInput = css`\n  all: unset;\n`;\n\nconst checkboxInputClassname = `rdg-checkbox-input ${checkboxInput}`;\n\nconst checkbox = css`\n  content: '';\n  inline-size: 20px;\n  block-size: 20px;\n  border: 2px solid var(--rdg-border-color);\n  background-color: var(--rdg-background-color);\n  .${checkboxInput}:checked + & {\n    background-color: var(--rdg-checkbox-color);\n    outline: 4px solid var(--rdg-background-color);\n    outline-offset: -6px;\n  }\n  .${checkboxInput}:focus + & {\n    border-color: var(--rdg-checkbox-focus-color);\n  }\n`;\n\nconst checkboxClassname = `rdg-checkbox ${checkbox}`;\n\nconst checkboxLabelDisabled = css`\n  cursor: default;\n  .${checkbox} {\n    border-color: var(--rdg-checkbox-disabled-border-color);\n    background-color: var(--rdg-checkbox-disabled-background-color);\n  }\n`;\n\nconst checkboxLabelDisabledClassname = `rdg-checkbox-label-disabled ${checkboxLabelDisabled}`;\n\nexport const CheckboxFormatter = forwardRef<HTMLInputElement, CheckboxFormatterProps>(\n  function CheckboxFormatter({ onChange, ...props }: CheckboxFormatterProps, ref) {\n    function handleChange(e: React.ChangeEvent<HTMLInputElement>) {\n      onChange(e.target.checked, (e.nativeEvent as MouseEvent).shiftKey);\n    }\n\n    return (\n      <label\n        className={clsx(checkboxLabelClassname, {\n          [checkboxLabelDisabledClassname]: props.disabled\n        })}\n      >\n        <input\n          type=\"checkbox\"\n          ref={ref}\n          {...props}\n          className={checkboxInputClassname}\n          onChange={handleChange}\n        />\n        <div className={checkboxClassname} />\n      </label>\n    );\n  }\n);\n", "// eslint-disable-next-line @typescript-eslint/no-restricted-imports\nimport { useEffect, useLayoutEffect as useOriginalLayoutEffect } from 'react';\n\n// Silence silly warning\n// https://reactjs.org/link/uselayouteffect-ssr\nexport const useLayoutEffect = typeof window === 'undefined' ? useEffect : useOriginalLayoutEffect;\n", "import { useRef } from 'react';\nimport { useLayoutEffect } from './useLayoutEffect';\n\nexport function useFocusRef<T extends HTMLOrSVGElement>(isSelected: boolean) {\n  const ref = useRef<T>(null);\n\n  useLayoutEffect(() => {\n    if (!isSelected) return;\n    ref.current?.focus({ preventScroll: true });\n  }, [isSelected]);\n\n  return {\n    ref,\n    tabIndex: isSelected ? 0 : -1\n  };\n}\n", "import { createContext, useContext } from 'react';\n\nimport type { Components, Maybe } from './types';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst DataGridDefaultComponentsContext = createContext<Maybe<Components<any, any>>>(undefined);\n\nexport const DataGridDefaultComponentsProvider = DataGridDefaultComponentsContext.Provider;\n\nexport function useDefaultComponents<R, SR>(): Maybe<Components<R, SR>> {\n  return useContext(DataGridDefaultComponentsContext);\n}\n", "import { useFocusRef } from '../hooks/useFocusRef';\nimport { useDefaultComponents } from '../DataGridDefaultComponentsProvider';\nimport type { CheckboxFormatterProps } from '../types';\n\ntype SharedInputProps = Pick<CheckboxFormatterProps, 'disabled' | 'aria-label' | 'aria-labelledby'>;\n\ninterface SelectCellFormatterProps extends SharedInputProps {\n  isCellSelected: boolean;\n  value: boolean;\n  onChange: (value: boolean, isShiftClick: boolean) => void;\n}\n\nexport function SelectCellFormatter({\n  value,\n  isCellSelected,\n  disabled,\n  onChange,\n  'aria-label': ariaLabel,\n  'aria-labelledby': ariaLabelledBy\n}: SelectCellFormatterProps) {\n  const { ref, tabIndex } = useFocusRef<HTMLInputElement>(isCellSelected);\n  const Formatter = useDefaultComponents()!.checkboxFormatter!;\n\n  return (\n    <Formatter\n      aria-label={ariaLabel}\n      aria-labelledby={ariaLabelledBy}\n      ref={ref}\n      tabIndex={tabIndex}\n      disabled={disabled}\n      checked={value}\n      onChange={onChange}\n    />\n  );\n}\n", "import type { FormatterProps } from '../types';\n\nexport function ValueFormatter<R, SR>(props: FormatterProps<R, SR>) {\n  try {\n    return <>{props.row[props.column.key as keyof R]}</>;\n  } catch {\n    return null;\n  }\n}\n", "import { css } from '@linaria/core';\nimport type { GroupFormatterProps } from '../types';\nimport { useFocusRef } from '../hooks/useFocusRef';\n\nconst groupCellContent = css`\n  outline: none;\n`;\n\nconst groupCellContentClassname = `rdg-group-cell-content ${groupCellContent}`;\n\nconst caret = css`\n  margin-inline-start: 4px;\n  stroke: currentColor;\n  stroke-width: 1.5px;\n  fill: transparent;\n  vertical-align: middle;\n\n  > path {\n    transition: d 0.1s;\n  }\n`;\n\nconst caretClassname = `rdg-caret ${caret}`;\n\nexport function ToggleGroupFormatter<R, SR>({\n  groupKey,\n  isExpanded,\n  isCellSelected,\n  toggleGroup\n}: GroupFormatterProps<R, SR>) {\n  const { ref, tabIndex } = useFocusRef<HTMLSpanElement>(isCellSelected);\n\n  function handleKeyDown({ key }: React.KeyboardEvent<HTMLSpanElement>) {\n    if (key === 'Enter') {\n      toggleGroup();\n    }\n  }\n\n  const d = isExpanded ? 'M1 1 L 7 7 L 13 1' : 'M1 7 L 7 1 L 13 7';\n\n  return (\n    <span\n      ref={ref}\n      className={groupCellContentClassname}\n      tabIndex={tabIndex}\n      onKeyDown={handleKeyDown}\n    >\n      {groupKey as string}\n      <svg viewBox=\"0 0 14 8\" width=\"14\" height=\"8\" className={caretClassname} aria-hidden>\n        <path d={d} />\n      </svg>\n    </span>\n  );\n}\n", "import { createContext, useContext } from 'react';\nimport type { SelectRowEvent } from '../types';\n\nconst RowSelectionContext = createContext<boolean | undefined>(undefined);\n\nexport const RowSelectionProvider = RowSelectionContext.Provider;\n\nconst RowSelectionChangeContext = createContext<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ((selectRowEvent: SelectRowEvent<any>) => void) | undefined\n>(undefined);\n\nexport const RowSelectionChangeProvider = RowSelectionChangeContext.Provider;\n\nexport function useRowSelection<R>(): [boolean, (selectRowEvent: SelectRowEvent<R>) => void] {\n  const rowSelectionContext = useContext(RowSelectionContext);\n  const rowSelectionChangeContext = useContext(RowSelectionChangeContext);\n\n  if (rowSelectionContext === undefined || rowSelectionChangeContext === undefined) {\n    throw new Error('useRowSelection must be used within DataGrid cells');\n  }\n\n  return [rowSelectionContext, rowSelectionChangeContext];\n}\n", "import { SelectCellFormatter } from './formatters';\nimport { useRowSelection } from './hooks/useRowSelection';\nimport type { Column, FormatterProps, GroupFormatterProps } from './types';\n\nexport const SELECT_COLUMN_KEY = 'select-row';\n\nfunction SelectFormatter(props: FormatterProps<unknown>) {\n  const [isRowSelected, onRowSelectionChange] = useRowSelection();\n\n  return (\n    <SelectCellFormatter\n      aria-label=\"Select\"\n      isCellSelected={props.isCellSelected}\n      value={isRowSelected}\n      onChange={(checked, isShiftClick) => {\n        onRowSelectionChange({ row: props.row, checked, isShiftClick });\n      }}\n    />\n  );\n}\n\nfunction SelectGroupFormatter(props: GroupFormatterProps<unknown>) {\n  const [isRowSelected, onRowSelectionChange] = useRowSelection();\n\n  return (\n    <SelectCellFormatter\n      aria-label=\"Select Group\"\n      isCellSelected={props.isCellSelected}\n      value={isRowSelected}\n      onChange={(checked) => {\n        onRowSelectionChange({ row: props.row, checked, isShiftClick: false });\n      }}\n    />\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const SelectColumn: Column<any, any> = {\n  key: SELECT_COLUMN_KEY,\n  name: '',\n  width: 35,\n  minWidth: 35,\n  maxWidth: 35,\n  resizable: false,\n  sortable: false,\n  frozen: true,\n  headerRenderer(props) {\n    return (\n      <SelectCellFormatter\n        aria-label=\"Select All\"\n        isCellSelected={props.isCellSelected}\n        value={props.allRowsSelected}\n        onChange={props.onAllRowsSelectionChange}\n      />\n    );\n  },\n  formatter: SelectFormatter,\n  groupFormatter: SelectGroupFormatter\n};\n", "import type { CalculatedColumn, ColSpanArgs } from '../types';\n\nexport function getColSpan<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  lastFrozenColumnIndex: number,\n  args: ColSpanArgs<R, SR>\n): number | undefined {\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(args) : 1;\n  if (\n    Number.isInteger(colSpan) &&\n    colSpan! > 1 &&\n    // ignore colSpan if it spans over both frozen and regular columns\n    (!column.frozen || column.idx + colSpan! - 1 <= lastFrozenColumnIndex)\n  ) {\n    return colSpan!;\n  }\n  return undefined;\n}\n", "import type { Maybe } from '../types';\n\nexport function stopPropagation(event: React.SyntheticEvent) {\n  event.stopPropagation();\n}\n\nexport function scrollIntoView(element: Maybe<HTMLDivElement>) {\n  element?.scrollIntoView({ inline: 'nearest', block: 'nearest' });\n}\n", "// https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key/Key_Values\nconst nonInputKeys = new Set([\n  // Special keys\n  'Unidentified',\n  // Modifier keys\n  'Alt',\n  'AltGraph',\n  'CapsLock',\n  'Control',\n  'Fn',\n  'FnLock',\n  'Meta',\n  'NumLock',\n  '<PERSON><PERSON>Lock',\n  'Shift',\n  // Whitespace keys\n  'Tab',\n  // Navigation keys\n  'ArrowDown',\n  'ArrowLeft',\n  'ArrowRight',\n  'ArrowUp',\n  'End',\n  'Home',\n  'PageDown',\n  'PageUp',\n  // Editing\n  'Insert',\n  // UI keys\n  'ContextMenu',\n  'Escape',\n  'Pause',\n  'Play',\n  // Device keys\n  'PrintScreen',\n  // Function keys\n  'F1',\n  // 'F2', /!\\ specifically allowed, do not edit\n  'F3',\n  'F4',\n  'F5',\n  'F6',\n  'F7',\n  'F8',\n  'F9',\n  'F10',\n  'F11',\n  'F12'\n]);\n\nexport function isCtrlKeyHeldDown(e: React.KeyboardEvent): boolean {\n  return (e.ctrlKey || e.metaKey) && e.key !== 'Control';\n}\n\nexport function isDefaultCellInput(event: React.KeyboardEvent<HTMLDivElement>): boolean {\n  return !nonInputKeys.has(event.key);\n}\n\n/**\n * By default, the following navigation keys are enabled while an editor is open, under specific conditions:\n * - Tab:\n *   - The editor must be an <input>, a <textarea>, or a <select> element.\n *   - The editor element must be the only immediate child of the editor container/a label.\n */\nexport function onEditorNavigation({ key, target }: React.KeyboardEvent<HTMLDivElement>): boolean {\n  if (\n    key === 'Tab' &&\n    (target instanceof HTMLInputElement ||\n      target instanceof HTMLTextAreaElement ||\n      target instanceof HTMLSelectElement)\n  ) {\n    return target.matches(\n      '.rdg-editor-container > :only-child, .rdg-editor-container > label:only-child > :only-child'\n    );\n  }\n  return false;\n}\n", "import type { CalculatedColumn, Position, GroupRow, CellNavigationMode, Maybe } from '../types';\nimport { getColSpan } from './colSpanUtils';\n\ninterface IsSelectedCellEditableOpts<R, SR> {\n  selectedPosition: Position;\n  columns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly (R | GroupRow<R>)[];\n  isGroupRow: (row: R | GroupRow<R>) => row is GroupRow<R>;\n}\n\nexport function isSelectedCellEditable<R, SR>({\n  selectedPosition,\n  columns,\n  rows,\n  isGroupRow\n}: IsSelectedCellEditableOpts<R, SR>): boolean {\n  const column = columns[selectedPosition.idx];\n  const row = rows[selectedPosition.rowIdx];\n  return !isGroupRow(row) && isCellEditable(column, row);\n}\n\nexport function isCellEditable<R, SR>(column: CalculatedColumn<R, SR>, row: R): boolean {\n  return (\n    column.editor != null &&\n    !column.rowGroup &&\n    (typeof column.editable === 'function' ? column.editable(row) : column.editable) !== false\n  );\n}\n\ninterface GetNextSelectedCellPositionOpts<R, SR> {\n  cellNavigationMode: CellNavigationMode;\n  columns: readonly CalculatedColumn<R, SR>[];\n  colSpanColumns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly (R | GroupRow<R>)[];\n  summaryRows: Maybe<readonly SR[]>;\n  minRowIdx: number;\n  maxRowIdx: number;\n  currentPosition: Position;\n  nextPosition: Position;\n  lastFrozenColumnIndex: number;\n  isCellWithinBounds: (position: Position) => boolean;\n  isGroupRow: (row: R | GroupRow<R>) => row is GroupRow<R>;\n}\n\nexport function getSelectedCellColSpan<R, SR>({\n  rows,\n  summaryRows,\n  rowIdx,\n  lastFrozenColumnIndex,\n  column,\n  isGroupRow\n}: Pick<\n  GetNextSelectedCellPositionOpts<R, SR>,\n  'rows' | 'summaryRows' | 'isGroupRow' | 'lastFrozenColumnIndex'\n> & {\n  rowIdx: number;\n  column: CalculatedColumn<R, SR>;\n}) {\n  if (rowIdx === -1) {\n    return getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' });\n  }\n\n  if (rowIdx >= 0 && rowIdx < rows.length) {\n    const row = rows[rowIdx];\n    if (!isGroupRow(row)) {\n      return getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n    }\n    return undefined;\n  }\n\n  if (summaryRows) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row: summaryRows[rowIdx - rows.length]\n    });\n  }\n\n  return undefined;\n}\n\nexport function getNextSelectedCellPosition<R, SR>({\n  cellNavigationMode,\n  columns,\n  colSpanColumns,\n  rows,\n  summaryRows,\n  minRowIdx,\n  maxRowIdx,\n  currentPosition: { idx: currentIdx },\n  nextPosition,\n  lastFrozenColumnIndex,\n  isCellWithinBounds,\n  isGroupRow\n}: GetNextSelectedCellPositionOpts<R, SR>): Position {\n  let { idx: nextIdx, rowIdx: nextRowIdx } = nextPosition;\n\n  const setColSpan = (moveRight: boolean) => {\n    if (nextRowIdx >= 0 && nextRowIdx < rows.length) {\n      const row = rows[nextRowIdx];\n      if (isGroupRow(row)) return;\n    }\n    // If a cell within the colspan range is selected then move to the\n    // previous or the next cell depending on the navigation direction\n    for (const column of colSpanColumns) {\n      const colIdx = column.idx;\n      if (colIdx > nextIdx) break;\n      const colSpan = getSelectedCellColSpan({\n        rows,\n        summaryRows,\n        rowIdx: nextRowIdx,\n        lastFrozenColumnIndex,\n        column,\n        isGroupRow\n      });\n\n      if (colSpan && nextIdx > colIdx && nextIdx < colSpan + colIdx) {\n        nextIdx = colIdx + (moveRight ? colSpan : 0);\n        break;\n      }\n    }\n  };\n\n  if (isCellWithinBounds(nextPosition)) {\n    setColSpan(nextIdx - currentIdx > 0);\n  }\n\n  if (cellNavigationMode !== 'NONE') {\n    const columnsCount = columns.length;\n    const isAfterLastColumn = nextIdx === columnsCount;\n    const isBeforeFirstColumn = nextIdx === -1;\n\n    if (isAfterLastColumn) {\n      if (cellNavigationMode === 'CHANGE_ROW') {\n        const isLastRow = nextRowIdx === maxRowIdx;\n        if (!isLastRow) {\n          nextIdx = 0;\n          nextRowIdx += 1;\n        }\n      } else {\n        nextIdx = 0;\n      }\n    } else if (isBeforeFirstColumn) {\n      if (cellNavigationMode === 'CHANGE_ROW') {\n        const isFirstRow = nextRowIdx === minRowIdx;\n        if (!isFirstRow) {\n          nextRowIdx -= 1;\n          nextIdx = columnsCount - 1;\n        }\n      } else {\n        nextIdx = columnsCount - 1;\n      }\n      setColSpan(false);\n    }\n  }\n\n  return { idx: nextIdx, rowIdx: nextRowIdx };\n}\n\ninterface CanExitGridOpts {\n  cellNavigationMode: CellNavigationMode;\n  maxColIdx: number;\n  minRowIdx: number;\n  maxRowIdx: number;\n  selectedPosition: Position;\n  shiftKey: boolean;\n}\n\nexport function canExitGrid({\n  cellNavigationMode,\n  maxColIdx,\n  minRowIdx,\n  maxRowIdx,\n  selectedPosition: { rowIdx, idx },\n  shiftKey\n}: CanExitGridOpts): boolean {\n  // When the cellNavigationMode is 'none' or 'changeRow', you can exit the grid if you're at the first or last cell of the grid\n  // When the cellNavigationMode is 'loopOverRow', there is no logical exit point so you can't exit the grid\n  if (cellNavigationMode === 'NONE' || cellNavigationMode === 'CHANGE_ROW') {\n    const atLastCellInRow = idx === maxColIdx;\n    const atFirstCellInRow = idx === 0;\n    const atLastRow = rowIdx === maxRowIdx;\n    const atFirstRow = rowIdx === minRowIdx;\n\n    return shiftKey ? atFirstCellInRow && atFirstRow : atLastCellInRow && atLastRow;\n  }\n\n  return false;\n}\n", "import type { CSSProperties } from 'react';\nimport clsx from 'clsx';\n\nimport type { CalculatedColumn } from '../types';\nimport { cellClassname, cellFrozenClassname, cellFrozenLastClassname } from '../style';\n\nexport function getRowStyle(rowIdx: number, height?: number): CSSProperties {\n  if (height !== undefined) {\n    return {\n      '--rdg-grid-row-start': rowIdx,\n      '--rdg-row-height': `${height}px`\n    } as unknown as CSSProperties;\n  }\n  return { '--rdg-grid-row-start': rowIdx } as unknown as CSSProperties;\n}\n\nexport function getCellStyle<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  colSpan?: number\n): React.CSSProperties {\n  return {\n    gridColumnStart: column.idx + 1,\n    gridColumnEnd: colSpan !== undefined ? `span ${colSpan}` : undefined,\n    insetInlineStart: column.frozen ? `var(--rdg-frozen-left-${column.idx})` : undefined\n  };\n}\n\nexport function getCellClassname<R, SR>(\n  column: CalculatedColumn<R, SR>,\n  ...extraClasses: Parameters<typeof clsx>\n): string {\n  return clsx(\n    cellClassname,\n    {\n      [cellFrozenClassname]: column.frozen,\n      [cellFrozenLastClassname]: column.isLastFrozenColumn\n    },\n    ...extraClasses\n  );\n}\n", "import type { CalculatedColumn } from '../types';\n\nexport * from './colSpanUtils';\nexport * from './domUtils';\nexport * from './keyboardUtils';\nexport * from './selectedCellUtils';\nexport * from './styleUtils';\n\nexport const { min, max, round, floor, sign, abs, ceil } = Math;\n\nexport function assertIsValidKeyGetter<R, K extends React.Key>(\n  keyGetter: unknown\n): asserts keyGetter is (row: R) => K {\n  if (typeof keyGetter !== 'function') {\n    throw new Error('Please specify the rowKeyGetter prop to use selection');\n  }\n}\n\nexport function clampColumnWidth<R, SR>(\n  width: number,\n  { minWidth, maxWidth }: CalculatedColumn<R, SR>\n): number {\n  width = max(width, minWidth);\n\n  // ignore maxWidth if it less than minWidth\n  if (typeof maxWidth === 'number' && maxWidth >= minWidth) {\n    return min(width, maxWidth);\n  }\n\n  return width;\n}\n", "import { useMemo } from 'react';\n\nimport type { CalculatedColumn, Column, Maybe } from '../types';\nimport type { DataGridProps } from '../DataGrid';\nimport { ValueFormatter, ToggleGroupFormatter } from '../formatters';\nimport { SELECT_COLUMN_KEY } from '../Columns';\nimport { clampColumnWidth, floor, max, min, round } from '../utils';\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\ninterface ColumnMetric {\n  width: number;\n  left: number;\n}\n\ninterface CalculatedColumnsArgs<R, SR> extends Pick<DataGridProps<R, SR>, 'defaultColumnOptions'> {\n  rawColumns: readonly Column<R, SR>[];\n  rawGroupBy: Maybe<readonly string[]>;\n  viewportWidth: number;\n  scrollLeft: number;\n  columnWidths: ReadonlyMap<string, number>;\n  enableVirtualization: boolean;\n}\n\nexport function useCalculatedColumns<R, SR>({\n  rawColumns,\n  columnWidths,\n  viewportWidth,\n  scrollLeft,\n  defaultColumnOptions,\n  rawGroupBy,\n  enableVirtualization\n}: CalculatedColumnsArgs<R, SR>) {\n  const defaultWidth = defaultColumnOptions?.width;\n  const defaultMinWidth = defaultColumnOptions?.minWidth ?? 80;\n  const defaultMaxWidth = defaultColumnOptions?.maxWidth;\n  const defaultFormatter = defaultColumnOptions?.formatter ?? ValueFormatter;\n  const defaultSortable = defaultColumnOptions?.sortable ?? false;\n  const defaultResizable = defaultColumnOptions?.resizable ?? false;\n\n  const { columns, colSpanColumns, lastFrozenColumnIndex, groupBy } = useMemo((): {\n    columns: readonly CalculatedColumn<R, SR>[];\n    colSpanColumns: readonly CalculatedColumn<R, SR>[];\n    lastFrozenColumnIndex: number;\n    groupBy: readonly string[];\n  } => {\n    // Filter rawGroupBy and ignore keys that do not match the columns prop\n    const groupBy: string[] = [];\n    let lastFrozenColumnIndex = -1;\n\n    const columns = rawColumns.map((rawColumn) => {\n      const rowGroup = rawGroupBy?.includes(rawColumn.key) ?? false;\n      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n      const frozen = rowGroup || rawColumn.frozen || false;\n\n      const column: Mutable<CalculatedColumn<R, SR>> = {\n        ...rawColumn,\n        idx: 0,\n        frozen,\n        isLastFrozenColumn: false,\n        rowGroup,\n        width: rawColumn.width ?? defaultWidth,\n        minWidth: rawColumn.minWidth ?? defaultMinWidth,\n        maxWidth: rawColumn.maxWidth ?? defaultMaxWidth,\n        sortable: rawColumn.sortable ?? defaultSortable,\n        resizable: rawColumn.resizable ?? defaultResizable,\n        formatter: rawColumn.formatter ?? defaultFormatter\n      };\n\n      if (rowGroup) {\n        column.groupFormatter ??= ToggleGroupFormatter;\n      }\n\n      if (frozen) {\n        lastFrozenColumnIndex++;\n      }\n\n      return column;\n    });\n\n    columns.sort(({ key: aKey, frozen: frozenA }, { key: bKey, frozen: frozenB }) => {\n      // Sort select column first:\n      if (aKey === SELECT_COLUMN_KEY) return -1;\n      if (bKey === SELECT_COLUMN_KEY) return 1;\n\n      // Sort grouped columns second, following the groupBy order:\n      if (rawGroupBy?.includes(aKey)) {\n        if (rawGroupBy.includes(bKey)) {\n          return rawGroupBy.indexOf(aKey) - rawGroupBy.indexOf(bKey);\n        }\n        return -1;\n      }\n      if (rawGroupBy?.includes(bKey)) return 1;\n\n      // Sort frozen columns third:\n      if (frozenA) {\n        if (frozenB) return 0;\n        return -1;\n      }\n      if (frozenB) return 1;\n\n      // Sort other columns last:\n      return 0;\n    });\n\n    const colSpanColumns: CalculatedColumn<R, SR>[] = [];\n    columns.forEach((column, idx) => {\n      column.idx = idx;\n\n      if (column.rowGroup) {\n        groupBy.push(column.key);\n      }\n\n      if (column.colSpan != null) {\n        colSpanColumns.push(column);\n      }\n    });\n\n    if (lastFrozenColumnIndex !== -1) {\n      columns[lastFrozenColumnIndex].isLastFrozenColumn = true;\n    }\n\n    return {\n      columns,\n      colSpanColumns,\n      lastFrozenColumnIndex,\n      groupBy\n    };\n  }, [\n    rawColumns,\n    defaultWidth,\n    defaultMinWidth,\n    defaultMaxWidth,\n    defaultFormatter,\n    defaultResizable,\n    defaultSortable,\n    rawGroupBy\n  ]);\n\n  const { layoutCssVars, totalFrozenColumnWidth, columnMetrics } = useMemo((): {\n    layoutCssVars: Readonly<Record<string, string>>;\n    totalFrozenColumnWidth: number;\n    columnMetrics: ReadonlyMap<CalculatedColumn<R, SR>, ColumnMetric>;\n  } => {\n    const columnMetrics = new Map<CalculatedColumn<R, SR>, ColumnMetric>();\n    let left = 0;\n    let totalFrozenColumnWidth = 0;\n    let templateColumns = '';\n    let allocatedWidth = 0;\n    let unassignedColumnsCount = 0;\n\n    for (const column of columns) {\n      let width = getSpecifiedWidth(column, columnWidths, viewportWidth);\n\n      if (width === undefined) {\n        unassignedColumnsCount++;\n      } else {\n        width = clampColumnWidth(width, column);\n        allocatedWidth += width;\n        columnMetrics.set(column, { width, left: 0 });\n      }\n    }\n\n    for (const column of columns) {\n      let width: number;\n      if (columnMetrics.has(column)) {\n        const columnMetric = columnMetrics.get(column)!;\n        columnMetric.left = left;\n        ({ width } = columnMetric);\n      } else {\n        // avoid decimals as subpixel positioning can lead to cell borders not being displayed\n        const unallocatedWidth = viewportWidth - allocatedWidth;\n        const unallocatedColumnWidth = round(unallocatedWidth / unassignedColumnsCount);\n        width = clampColumnWidth(unallocatedColumnWidth, column);\n        allocatedWidth += width;\n        unassignedColumnsCount--;\n        columnMetrics.set(column, { width, left });\n      }\n      left += width;\n      templateColumns += `${width}px `;\n    }\n\n    if (lastFrozenColumnIndex !== -1) {\n      const columnMetric = columnMetrics.get(columns[lastFrozenColumnIndex])!;\n      totalFrozenColumnWidth = columnMetric.left + columnMetric.width;\n    }\n\n    const layoutCssVars: Record<string, string> = {\n      gridTemplateColumns: templateColumns\n    };\n\n    for (let i = 0; i <= lastFrozenColumnIndex; i++) {\n      const column = columns[i];\n      layoutCssVars[`--rdg-frozen-left-${column.idx}`] = `${columnMetrics.get(column)!.left}px`;\n    }\n\n    return { layoutCssVars, totalFrozenColumnWidth, columnMetrics };\n  }, [columnWidths, columns, viewportWidth, lastFrozenColumnIndex]);\n\n  const [colOverscanStartIdx, colOverscanEndIdx] = useMemo((): [number, number] => {\n    if (!enableVirtualization) {\n      return [0, columns.length - 1];\n    }\n    // get the viewport's left side and right side positions for non-frozen columns\n    const viewportLeft = scrollLeft + totalFrozenColumnWidth;\n    const viewportRight = scrollLeft + viewportWidth;\n    // get first and last non-frozen column indexes\n    const lastColIdx = columns.length - 1;\n    const firstUnfrozenColumnIdx = min(lastFrozenColumnIndex + 1, lastColIdx);\n\n    // skip rendering non-frozen columns if the frozen columns cover the entire viewport\n    if (viewportLeft >= viewportRight) {\n      return [firstUnfrozenColumnIdx, firstUnfrozenColumnIdx];\n    }\n\n    // get the first visible non-frozen column index\n    let colVisibleStartIdx = firstUnfrozenColumnIdx;\n    while (colVisibleStartIdx < lastColIdx) {\n      const { left, width } = columnMetrics.get(columns[colVisibleStartIdx])!;\n      // if the right side of the columnn is beyond the left side of the available viewport,\n      // then it is the first column that's at least partially visible\n      if (left + width > viewportLeft) {\n        break;\n      }\n      colVisibleStartIdx++;\n    }\n\n    // get the last visible non-frozen column index\n    let colVisibleEndIdx = colVisibleStartIdx;\n    while (colVisibleEndIdx < lastColIdx) {\n      const { left, width } = columnMetrics.get(columns[colVisibleEndIdx])!;\n      // if the right side of the column is beyond or equal to the right side of the available viewport,\n      // then it the last column that's at least partially visible, as the previous column's right side is not beyond the viewport.\n      if (left + width >= viewportRight) {\n        break;\n      }\n      colVisibleEndIdx++;\n    }\n\n    const colOverscanStartIdx = max(firstUnfrozenColumnIdx, colVisibleStartIdx - 1);\n    const colOverscanEndIdx = min(lastColIdx, colVisibleEndIdx + 1);\n\n    return [colOverscanStartIdx, colOverscanEndIdx];\n  }, [\n    columnMetrics,\n    columns,\n    lastFrozenColumnIndex,\n    scrollLeft,\n    totalFrozenColumnWidth,\n    viewportWidth,\n    enableVirtualization\n  ]);\n\n  return {\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    layoutCssVars,\n    columnMetrics,\n    lastFrozenColumnIndex,\n    totalFrozenColumnWidth,\n    groupBy\n  };\n}\n\nfunction getSpecifiedWidth<R, SR>(\n  { key, width }: Column<R, SR>,\n  columnWidths: ReadonlyMap<string, number>,\n  viewportWidth: number\n): number | undefined {\n  if (columnWidths.has(key)) {\n    // Use the resized width if available\n    return columnWidths.get(key);\n  }\n\n  if (typeof width === 'number') {\n    return width;\n  }\n  if (typeof width === 'string' && /^\\d+%$/.test(width)) {\n    return floor((viewportWidth * parseInt(width, 10)) / 100);\n  }\n  return undefined;\n}\n", "import { useRef, useState } from 'react';\nimport { useLayoutEffect } from './useLayoutEffect';\n\nimport { ceil } from '../utils';\n\nexport function useGridDimensions(): [\n  ref: React.RefObject<HTMLDivElement>,\n  width: number,\n  height: number\n] {\n  const gridRef = useRef<HTMLDivElement>(null);\n  const [inlineSize, setInlineSize] = useState(1);\n  const [blockSize, setBlockSize] = useState(1);\n\n  useLayoutEffect(() => {\n    const { ResizeObserver } = window;\n\n    // don't break in Node.js (SSR), jest/jsdom, and browsers that don't support ResizeObserver\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (ResizeObserver == null) return;\n\n    const { clientWidth, clientHeight, offsetWidth, offsetHeight } = gridRef.current!;\n    const { width, height } = gridRef.current!.getBoundingClientRect();\n    const initialWidth = width - offsetWidth + clientWidth;\n    const initialHeight = height - offsetHeight + clientHeight;\n\n    setInlineSize(handleDevicePixelRatio(initialWidth));\n    setBlockSize(initialHeight);\n\n    const resizeObserver = new ResizeObserver((entries) => {\n      const size = entries[0].contentBoxSize[0];\n      setInlineSize(handleDevicePixelRatio(size.inlineSize));\n      setBlockSize(size.blockSize);\n    });\n    resizeObserver.observe(gridRef.current!);\n\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, []);\n\n  return [gridRef, inlineSize, blockSize];\n}\n\n// TODO: remove once fixed upstream\n// we reduce width by 1px here to avoid layout issues in Chrome\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1206298\nfunction handleDevicePixelRatio(size: number) {\n  return size - (devicePixelRatio === 1 ? 0 : ceil(devicePixelRatio));\n}\n", "import { useRef, useEffect, useCallback } from 'react';\n\n// https://reactjs.org/docs/hooks-faq.html#what-can-i-do-if-my-effect-dependencies-change-too-often\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function useLatestFunc<T extends (...args: any[]) => any>(fn: T) {\n  const ref = useRef(fn);\n\n  useEffect(() => {\n    ref.current = fn;\n  });\n\n  return useCallback((...args: Parameters<T>) => {\n    ref.current(...args);\n  }, []);\n}\n", "import { useCallback, useState } from 'react';\nimport { scrollIntoView } from '../utils';\n\n// https://www.w3.org/TR/wai-aria-practices-1.1/#kbd_roving_tabindex\nexport function useRovingCellRef(isSelected: boolean) {\n  // https://www.w3.org/TR/wai-aria-practices-1.1/#gridNav_focus\n  const [isChildFocused, setIsChildFocused] = useState(false);\n\n  if (isChildFocused && !isSelected) {\n    setIsChildFocused(false);\n  }\n\n  const ref = useCallback((cell: HTMLDivElement | null) => {\n    if (cell === null) return;\n    scrollIntoView(cell);\n    if (cell.contains(document.activeElement)) return;\n    cell.focus({ preventScroll: true });\n  }, []);\n\n  function onFocus(event: React.FocusEvent<HTMLDivElement>) {\n    if (event.target !== event.currentTarget) {\n      setIsChildFocused(true);\n    }\n  }\n\n  const isFocused = isSelected && !isChildFocused;\n\n  return {\n    ref: isSelected ? ref : undefined,\n    tabIndex: isFocused ? 0 : -1,\n    onFocus: isSelected ? onFocus : undefined\n  };\n}\n", "import { useMemo } from 'react';\n\nimport { getColSpan } from '../utils';\nimport type { CalculatedColumn, GroupRow, Maybe } from '../types';\n\ninterface ViewportColumnsArgs<R, SR> {\n  columns: readonly CalculatedColumn<R, SR>[];\n  colSpanColumns: readonly CalculatedColumn<R, SR>[];\n  rows: readonly (R | GroupRow<R>)[];\n  summaryRows: Maybe<readonly SR[]>;\n  colOverscanStartIdx: number;\n  colOverscanEndIdx: number;\n  lastFrozenColumnIndex: number;\n  rowOverscanStartIdx: number;\n  rowOverscanEndIdx: number;\n  isGroupRow: (row: R | GroupRow<R>) => row is GroupRow<R>;\n}\n\nexport function useViewportColumns<R, SR>({\n  columns,\n  colSpanColumns,\n  rows,\n  summaryRows,\n  colOverscanStartIdx,\n  colOverscanEndIdx,\n  lastFrozenColumnIndex,\n  rowOverscanStartIdx,\n  rowOverscanEndIdx,\n  isGroupRow\n}: ViewportColumnsArgs<R, SR>) {\n  // find the column that spans over a column within the visible columns range and adjust colOverscanStartIdx\n  const startIdx = useMemo(() => {\n    if (colOverscanStartIdx === 0) return 0;\n\n    let startIdx = colOverscanStartIdx;\n\n    const updateStartIdx = (colIdx: number, colSpan: number | undefined) => {\n      if (colSpan !== undefined && colIdx + colSpan > colOverscanStartIdx) {\n        startIdx = colIdx;\n        return true;\n      }\n      return false;\n    };\n\n    for (const column of colSpanColumns) {\n      // check header row\n      const colIdx = column.idx;\n      if (colIdx >= startIdx) break;\n      if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' }))) {\n        break;\n      }\n\n      // check viewport rows\n      for (let rowIdx = rowOverscanStartIdx; rowIdx <= rowOverscanEndIdx; rowIdx++) {\n        const row = rows[rowIdx];\n        if (isGroupRow(row)) continue;\n        if (\n          updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row }))\n        ) {\n          break;\n        }\n      }\n\n      // check summary rows\n      if (summaryRows != null) {\n        for (const row of summaryRows) {\n          if (\n            updateStartIdx(\n              colIdx,\n              getColSpan(column, lastFrozenColumnIndex, { type: 'SUMMARY', row })\n            )\n          ) {\n            break;\n          }\n        }\n      }\n    }\n\n    return startIdx;\n  }, [\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    summaryRows,\n    colOverscanStartIdx,\n    lastFrozenColumnIndex,\n    colSpanColumns,\n    isGroupRow\n  ]);\n\n  return useMemo((): readonly CalculatedColumn<R, SR>[] => {\n    const viewportColumns: CalculatedColumn<R, SR>[] = [];\n    for (let colIdx = 0; colIdx <= colOverscanEndIdx; colIdx++) {\n      const column = columns[colIdx];\n\n      if (colIdx < startIdx && !column.frozen) continue;\n      viewportColumns.push(column);\n    }\n\n    return viewportColumns;\n  }, [startIdx, colOverscanEndIdx, columns]);\n}\n", "import { useMemo } from 'react';\nimport { floor, max, min } from '../utils';\nimport type { GroupRow, Maybe, RowHeightArgs } from '../types';\n\ntype GroupByDictionary<TRow> = Record<\n  string,\n  {\n    readonly childRows: readonly TRow[];\n    readonly childGroups: readonly TRow[] | Readonly<GroupByDictionary<TRow>>;\n    readonly startRowIndex: number;\n  }\n>;\n\ninterface ViewportRowsArgs<R> {\n  rawRows: readonly R[];\n  rowHeight: number | ((args: RowHeightArgs<R>) => number);\n  clientHeight: number;\n  scrollTop: number;\n  groupBy: readonly string[];\n  rowGrouper: Maybe<(rows: readonly R[], columnKey: string) => Record<string, readonly R[]>>;\n  expandedGroupIds: Maybe<ReadonlySet<unknown>>;\n  enableVirtualization: boolean;\n}\n\n// TODO: https://github.com/microsoft/TypeScript/issues/41808\nfunction isReadonlyArray(arr: unknown): arr is readonly unknown[] {\n  return Array.isArray(arr);\n}\n\nexport function useViewportRows<R>({\n  rawRows,\n  rowHeight,\n  clientHeight,\n  scrollTop,\n  groupBy,\n  rowGrouper,\n  expandedGroupIds,\n  enableVirtualization\n}: ViewportRowsArgs<R>) {\n  const [groupedRows, rowsCount] = useMemo(() => {\n    if (groupBy.length === 0 || rowGrouper == null) return [undefined, rawRows.length];\n\n    const groupRows = (\n      rows: readonly R[],\n      [groupByKey, ...remainingGroupByKeys]: readonly string[],\n      startRowIndex: number\n    ): [Readonly<GroupByDictionary<R>>, number] => {\n      let groupRowsCount = 0;\n      const groups: GroupByDictionary<R> = {};\n      for (const [key, childRows] of Object.entries(rowGrouper(rows, groupByKey))) {\n        // Recursively group each parent group\n        const [childGroups, childRowsCount] =\n          remainingGroupByKeys.length === 0\n            ? [childRows, childRows.length]\n            : groupRows(childRows, remainingGroupByKeys, startRowIndex + groupRowsCount + 1); // 1 for parent row\n        groups[key] = { childRows, childGroups, startRowIndex: startRowIndex + groupRowsCount };\n        groupRowsCount += childRowsCount + 1; // 1 for parent row\n      }\n\n      return [groups, groupRowsCount];\n    };\n\n    return groupRows(rawRows, groupBy, 0);\n  }, [groupBy, rowGrouper, rawRows]);\n\n  const [rows, isGroupRow] = useMemo((): [\n    ReadonlyArray<R | GroupRow<R>>,\n    (row: R | GroupRow<R>) => row is GroupRow<R>\n  ] => {\n    const allGroupRows = new Set<unknown>();\n    if (!groupedRows) return [rawRows, isGroupRow];\n\n    const flattenedRows: Array<R | GroupRow<R>> = [];\n    const expandGroup = (\n      rows: GroupByDictionary<R> | readonly R[],\n      parentId: string | undefined,\n      level: number\n    ): void => {\n      if (isReadonlyArray(rows)) {\n        flattenedRows.push(...rows);\n        return;\n      }\n      Object.keys(rows).forEach((groupKey, posInSet, keys) => {\n        // TODO: should users have control over the generated key?\n        const id = parentId !== undefined ? `${parentId}__${groupKey}` : groupKey;\n        const isExpanded = expandedGroupIds?.has(id) ?? false;\n        const { childRows, childGroups, startRowIndex } = rows[groupKey];\n\n        const groupRow: GroupRow<R> = {\n          id,\n          parentId,\n          groupKey,\n          isExpanded,\n          childRows,\n          level,\n          posInSet,\n          startRowIndex,\n          setSize: keys.length\n        };\n        flattenedRows.push(groupRow);\n        allGroupRows.add(groupRow);\n\n        if (isExpanded) {\n          expandGroup(childGroups, id, level + 1);\n        }\n      });\n    };\n\n    expandGroup(groupedRows, undefined, 0);\n    return [flattenedRows, isGroupRow];\n\n    function isGroupRow(row: R | GroupRow<R>): row is GroupRow<R> {\n      return allGroupRows.has(row);\n    }\n  }, [expandedGroupIds, groupedRows, rawRows]);\n\n  const { totalRowHeight, gridTemplateRows, getRowTop, getRowHeight, findRowIdx } = useMemo(() => {\n    if (typeof rowHeight === 'number') {\n      return {\n        totalRowHeight: rowHeight * rows.length,\n        gridTemplateRows: ` repeat(${rows.length}, ${rowHeight}px)`,\n        getRowTop: (rowIdx: number) => rowIdx * rowHeight,\n        getRowHeight: () => rowHeight,\n        findRowIdx: (offset: number) => floor(offset / rowHeight)\n      };\n    }\n\n    let totalRowHeight = 0;\n    let gridTemplateRows = ' ';\n    // Calcule the height of all the rows upfront. This can cause performance issues\n    // and we can consider using a similar approach as react-window\n    // https://github.com/bvaughn/react-window/blob/b0a470cc264e9100afcaa1b78ed59d88f7914ad4/src/VariableSizeList.js#L68\n    const rowPositions = rows.map((row: R | GroupRow<R>) => {\n      const currentRowHeight = isGroupRow(row)\n        ? rowHeight({ type: 'GROUP', row })\n        : rowHeight({ type: 'ROW', row });\n      const position = { top: totalRowHeight, height: currentRowHeight };\n      gridTemplateRows += `${currentRowHeight}px `;\n      totalRowHeight += currentRowHeight;\n      return position;\n    });\n\n    const validateRowIdx = (rowIdx: number) => {\n      return max(0, min(rows.length - 1, rowIdx));\n    };\n\n    return {\n      totalRowHeight,\n      gridTemplateRows,\n      getRowTop: (rowIdx: number) => rowPositions[validateRowIdx(rowIdx)].top,\n      getRowHeight: (rowIdx: number) => rowPositions[validateRowIdx(rowIdx)].height,\n      findRowIdx(offset: number) {\n        let start = 0;\n        let end = rowPositions.length - 1;\n        while (start <= end) {\n          const middle = start + floor((end - start) / 2);\n          const currentOffset = rowPositions[middle].top;\n\n          if (currentOffset === offset) return middle;\n\n          if (currentOffset < offset) {\n            start = middle + 1;\n          } else if (currentOffset > offset) {\n            end = middle - 1;\n          }\n\n          if (start > end) return end;\n        }\n        return 0;\n      }\n    };\n  }, [isGroupRow, rowHeight, rows]);\n\n  let rowOverscanStartIdx = 0;\n  let rowOverscanEndIdx = rows.length - 1;\n\n  if (enableVirtualization) {\n    const overscanThreshold = 4;\n    const rowVisibleStartIdx = findRowIdx(scrollTop);\n    const rowVisibleEndIdx = findRowIdx(scrollTop + clientHeight);\n    rowOverscanStartIdx = max(0, rowVisibleStartIdx - overscanThreshold);\n    rowOverscanEndIdx = min(rows.length - 1, rowVisibleEndIdx + overscanThreshold);\n  }\n\n  return {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    rowsCount,\n    totalRowHeight,\n    gridTemplateRows,\n    isGroupRow,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  };\n}\n", "import { css } from '@linaria/core';\n\nimport { useFocusRef } from './hooks';\nimport type { HeaderRendererProps } from './types';\nimport { useDefaultComponents } from './DataGridDefaultComponentsProvider';\n\nconst headerSortCell = css`\n  cursor: pointer;\n  display: flex;\n\n  &:focus {\n    outline: none;\n  }\n`;\n\nconst headerSortCellClassname = `rdg-header-sort-cell ${headerSortCell}`;\n\nconst headerSortName = css`\n  flex-grow: 1;\n  overflow: hidden;\n  overflow: clip;\n  text-overflow: ellipsis;\n`;\n\nconst headerSortNameClassname = `rdg-header-sort-name ${headerSortName}`;\n\nexport default function HeaderRenderer<R, SR>({\n  column,\n  sortDirection,\n  priority,\n  onSort,\n  isCellSelected\n}: HeaderRendererProps<R, SR>) {\n  if (!column.sortable) return <>{column.name}</>;\n\n  return (\n    <SortableHeaderCell\n      onSort={onSort}\n      sortDirection={sortDirection}\n      priority={priority}\n      isCellSelected={isCellSelected}\n    >\n      {column.name}\n    </SortableHeaderCell>\n  );\n}\n\ntype SharedHeaderCellProps<R, SR> = Pick<\n  HeaderRendererProps<R, SR>,\n  'sortDirection' | 'onSort' | 'priority' | 'isCellSelected'\n>;\n\ninterface SortableHeaderCellProps<R, SR> extends SharedHeaderCellProps<R, SR> {\n  children: React.ReactNode;\n}\n\nfunction SortableHeaderCell<R, SR>({\n  onSort,\n  sortDirection,\n  priority,\n  children,\n  isCellSelected\n}: SortableHeaderCellProps<R, SR>) {\n  const SortIcon = useDefaultComponents<R, SR>()!.sortIcon!;\n  const { ref, tabIndex } = useFocusRef<HTMLSpanElement>(isCellSelected);\n\n  function handleKeyDown(event: React.KeyboardEvent<HTMLSpanElement>) {\n    if (event.key === ' ' || event.key === 'Enter') {\n      // stop propagation to prevent scrolling\n      event.preventDefault();\n      onSort(event.ctrlKey || event.metaKey);\n    }\n  }\n\n  function handleClick(event: React.MouseEvent<HTMLSpanElement>) {\n    onSort(event.ctrlKey || event.metaKey);\n  }\n\n  return (\n    <span\n      ref={ref}\n      tabIndex={tabIndex}\n      className={headerSortCellClassname}\n      onClick={handleClick}\n      onKeyDown={handleKeyDown}\n    >\n      <span className={headerSortNameClassname}>{children}</span>\n      <span>\n        <SortIcon sortDirection={sortDirection} />\n        {priority}\n      </span>\n    </span>\n  );\n}\n", "import { css } from '@linaria/core';\n\nimport type { CalculatedColumn, SortColumn } from './types';\nimport type { HeaderRowProps } from './HeaderRow';\nimport DefaultHeaderRenderer from './HeaderRenderer';\nimport { getCellStyle, getCellClassname, clampColumnWidth } from './utils';\nimport { useRovingCellRef } from './hooks';\n\nconst cellResizable = css`\n  touch-action: none;\n\n  &::after {\n    content: '';\n    cursor: col-resize;\n    position: absolute;\n    inset-block-start: 0;\n    inset-inline-end: 0;\n    inset-block-end: 0;\n    inline-size: 10px;\n  }\n`;\n\nconst cellResizableClassname = `rdg-cell-resizable ${cellResizable}`;\n\ntype SharedHeaderRowProps<R, SR> = Pick<\n  HeaderRowProps<R, SR, React.Key>,\n  | 'sortColumns'\n  | 'onSortColumnsChange'\n  | 'allRowsSelected'\n  | 'onAllRowsSelectionChange'\n  | 'selectCell'\n  | 'onColumnResize'\n  | 'shouldFocusGrid'\n  | 'direction'\n>;\n\nexport interface HeaderCellProps<R, SR> extends SharedHeaderRowProps<R, SR> {\n  column: CalculatedColumn<R, SR>;\n  colSpan: number | undefined;\n  isCellSelected: boolean;\n}\n\nexport default function HeaderCell<R, SR>({\n  column,\n  colSpan,\n  isCellSelected,\n  onColumnResize,\n  allRowsSelected,\n  onAllRowsSelectionChange,\n  sortColumns,\n  onSortColumnsChange,\n  selectCell,\n  shouldFocusGrid,\n  direction\n}: HeaderCellProps<R, SR>) {\n  const isRtl = direction === 'rtl';\n  const { ref, tabIndex, onFocus } = useRovingCellRef(isCellSelected);\n  const sortIndex = sortColumns?.findIndex((sort) => sort.columnKey === column.key);\n  const sortColumn =\n    sortIndex !== undefined && sortIndex > -1 ? sortColumns![sortIndex] : undefined;\n  const sortDirection = sortColumn?.direction;\n  const priority = sortColumn !== undefined && sortColumns!.length > 1 ? sortIndex! + 1 : undefined;\n  const ariaSort =\n    sortDirection && !priority ? (sortDirection === 'ASC' ? 'ascending' : 'descending') : undefined;\n\n  const className = getCellClassname(column, column.headerCellClass, {\n    [cellResizableClassname]: column.resizable\n  });\n\n  const HeaderRenderer = column.headerRenderer ?? DefaultHeaderRenderer;\n\n  function onPointerDown(event: React.PointerEvent<HTMLDivElement>) {\n    if (event.pointerType === 'mouse' && event.buttons !== 1) {\n      return;\n    }\n\n    const { currentTarget, pointerId } = event;\n    const { right, left } = currentTarget.getBoundingClientRect();\n    const offset = isRtl ? event.clientX - left : right - event.clientX;\n\n    if (offset > 11) {\n      // +1px to account for the border size\n      return;\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      const { right, left } = currentTarget.getBoundingClientRect();\n      const width = isRtl ? right + offset - event.clientX : event.clientX + offset - left;\n      if (width > 0) {\n        onColumnResize(column, clampColumnWidth(width, column));\n      }\n    }\n\n    function onLostPointerCapture() {\n      currentTarget.removeEventListener('pointermove', onPointerMove);\n      currentTarget.removeEventListener('lostpointercapture', onLostPointerCapture);\n    }\n\n    currentTarget.setPointerCapture(pointerId);\n    currentTarget.addEventListener('pointermove', onPointerMove);\n    currentTarget.addEventListener('lostpointercapture', onLostPointerCapture);\n  }\n\n  function onSort(ctrlClick: boolean) {\n    if (onSortColumnsChange == null) return;\n    const { sortDescendingFirst } = column;\n    if (sortColumn === undefined) {\n      // not currently sorted\n      const nextSort: SortColumn = {\n        columnKey: column.key,\n        direction: sortDescendingFirst ? 'DESC' : 'ASC'\n      };\n      onSortColumnsChange(sortColumns && ctrlClick ? [...sortColumns, nextSort] : [nextSort]);\n    } else {\n      let nextSortColumn: SortColumn | undefined;\n      if (\n        (sortDescendingFirst && sortDirection === 'DESC') ||\n        (!sortDescendingFirst && sortDirection === 'ASC')\n      ) {\n        nextSortColumn = {\n          columnKey: column.key,\n          direction: sortDirection === 'ASC' ? 'DESC' : 'ASC'\n        };\n      }\n      if (ctrlClick) {\n        const nextSortColumns = [...sortColumns!];\n        if (nextSortColumn) {\n          // swap direction\n          nextSortColumns[sortIndex!] = nextSortColumn;\n        } else {\n          // remove sort\n          nextSortColumns.splice(sortIndex!, 1);\n        }\n        onSortColumnsChange(nextSortColumns);\n      } else {\n        onSortColumnsChange(nextSortColumn ? [nextSortColumn] : []);\n      }\n    }\n  }\n\n  function onClick() {\n    selectCell(column.idx);\n  }\n\n  function onDoubleClick(event: React.MouseEvent<HTMLDivElement>) {\n    const { right, left } = event.currentTarget.getBoundingClientRect();\n    const offset = isRtl ? event.clientX - left : right - event.clientX;\n\n    if (offset > 11) {\n      // +1px to account for the border size\n      return;\n    }\n\n    onColumnResize(column, 'auto');\n  }\n\n  function handleFocus(event: React.FocusEvent<HTMLDivElement>) {\n    onFocus?.(event);\n    if (shouldFocusGrid) {\n      // Select the first header cell if there is no selected cell\n      selectCell(0);\n    }\n  }\n\n  return (\n    <div\n      role=\"columnheader\"\n      aria-colindex={column.idx + 1}\n      aria-selected={isCellSelected}\n      aria-sort={ariaSort}\n      aria-colspan={colSpan}\n      ref={ref}\n      // set the tabIndex to 0 when there is no selected cell so grid can receive focus\n      tabIndex={shouldFocusGrid ? 0 : tabIndex}\n      className={className}\n      style={{\n        ...getCellStyle(column, colSpan),\n        minWidth: column.minWidth,\n        maxWidth: column.maxWidth ?? undefined\n      }}\n      onFocus={handleFocus}\n      onClick={onClick}\n      onDoubleClick={column.resizable ? onDoubleClick : undefined}\n      onPointerDown={column.resizable ? onPointerDown : undefined}\n    >\n      <HeaderRenderer\n        column={column}\n        sortDirection={sortDirection}\n        priority={priority}\n        onSort={onSort}\n        allRowsSelected={allRowsSelected}\n        onAllRowsSelectionChange={onAllRowsSelectionChange}\n        isCellSelected={isCellSelected}\n      />\n    </div>\n  );\n}\n", "import { memo } from 'react';\nimport clsx from 'clsx';\nimport { css } from '@linaria/core';\n\nimport HeaderCell from './HeaderCell';\nimport type { CalculatedColumn, Direction } from './types';\nimport { getColSpan, getRowStyle } from './utils';\nimport type { DataGridProps } from './DataGrid';\nimport { cell, cellFrozen, rowSelectedClassname } from './style';\n\ntype SharedDataGridProps<R, SR, K extends React.Key> = Pick<\n  DataGridProps<R, SR, K>,\n  'sortColumns' | 'onSortColumnsChange'\n>;\n\nexport interface HeaderRowProps<R, SR, K extends React.Key> extends SharedDataGridProps<R, SR, K> {\n  columns: readonly CalculatedColumn<R, SR>[];\n  allRowsSelected: boolean;\n  onAllRowsSelectionChange: (checked: boolean) => void;\n  onColumnResize: (column: CalculatedColumn<R, SR>, width: number | 'auto') => void;\n  selectCell: (columnIdx: number) => void;\n  lastFrozenColumnIndex: number;\n  selectedCellIdx: number | undefined;\n  shouldFocusGrid: boolean;\n  direction: Direction;\n}\n\nconst headerRow = css`\n  display: contents;\n  line-height: var(--rdg-header-row-height);\n  background-color: var(--rdg-header-background-color);\n  font-weight: bold;\n\n  > .${cell} {\n    /* Should have a higher value than 1 to show up above frozen cells */\n    z-index: 2;\n    position: sticky;\n    inset-block-start: 0;\n  }\n\n  > .${cellFrozen} {\n    z-index: 3;\n  }\n`;\n\nconst headerRowClassname = `rdg-header-row ${headerRow}`;\n\nfunction HeaderRow<R, SR, K extends React.Key>({\n  columns,\n  allRowsSelected,\n  onAllRowsSelectionChange,\n  onColumnResize,\n  sortColumns,\n  onSortColumnsChange,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  selectCell,\n  shouldFocusGrid,\n  direction\n}: HeaderRowProps<R, SR, K>) {\n  const cells = [];\n  for (let index = 0; index < columns.length; index++) {\n    const column = columns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'HEADER' });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    cells.push(\n      <HeaderCell<R, SR>\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        isCellSelected={selectedCellIdx === column.idx}\n        onColumnResize={onColumnResize}\n        allRowsSelected={allRowsSelected}\n        onAllRowsSelectionChange={onAllRowsSelectionChange}\n        onSortColumnsChange={onSortColumnsChange}\n        sortColumns={sortColumns}\n        selectCell={selectCell}\n        shouldFocusGrid={shouldFocusGrid && index === 0}\n        direction={direction}\n      />\n    );\n  }\n\n  return (\n    <div\n      role=\"row\"\n      aria-rowindex={1} // aria-rowindex is 1 based\n      className={clsx(headerRowClassname, {\n        [rowSelectedClassname]: selectedCellIdx === -1\n      })}\n      style={getRowStyle(1)}\n    >\n      {cells}\n    </div>\n  );\n}\n\nexport default memo(HeaderRow) as <R, SR, K extends React.Key>(\n  props: HeaderRowProps<R, SR, K>\n) => JSX.Element;\n", "import { memo } from 'react';\nimport { css } from '@linaria/core';\n\nimport { getCellStyle, getCellClassname, isCellEditable } from './utils';\nimport type { CellRendererProps } from './types';\nimport { useRovingCellRef } from './hooks';\n\nconst cellCopied = css`\n  background-color: #ccccff;\n`;\n\nconst cellCopiedClassname = `rdg-cell-copied ${cellCopied}`;\n\nconst cellDraggedOver = css`\n  background-color: #ccccff;\n\n  &.${cellCopied} {\n    background-color: #9999ff;\n  }\n`;\n\nconst cellDraggedOverClassname = `rdg-cell-dragged-over ${cellDraggedOver}`;\n\nfunction Cell<R, SR>({\n  column,\n  colSpan,\n  isCellSelected,\n  isCopied,\n  isDraggedOver,\n  row,\n  dragHandle,\n  onRowClick,\n  onRowDoubleClick,\n  onRowChange,\n  selectCell,\n  ...props\n}: CellRendererProps<R, SR>) {\n  const { ref, tabIndex, onFocus } = useRovingCellRef(isCellSelected);\n\n  const { cellClass } = column;\n  const className = getCellClassname(\n    column,\n    {\n      [cellCopiedClassname]: isCopied,\n      [cellDraggedOverClassname]: isDraggedOver\n    },\n    typeof cellClass === 'function' ? cellClass(row) : cellClass\n  );\n\n  function selectCellWrapper(openEditor?: boolean | null) {\n    selectCell(row, column, openEditor);\n  }\n\n  function handleClick() {\n    selectCellWrapper(column.editorOptions?.editOnClick);\n    onRowClick?.(row, column);\n  }\n\n  function handleContextMenu() {\n    selectCellWrapper();\n  }\n\n  function handleDoubleClick() {\n    selectCellWrapper(true);\n    onRowDoubleClick?.(row, column);\n  }\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1} // aria-colindex is 1-based\n      aria-selected={isCellSelected}\n      aria-colspan={colSpan}\n      aria-readonly={!isCellEditable(column, row) || undefined}\n      ref={ref}\n      tabIndex={tabIndex}\n      className={className}\n      style={getCellStyle(column, colSpan)}\n      onClick={handleClick}\n      onDoubleClick={handleDoubleClick}\n      onContextMenu={handleContextMenu}\n      onFocus={onFocus}\n      {...props}\n    >\n      {!column.rowGroup && (\n        <>\n          <column.formatter\n            column={column}\n            row={row}\n            isCellSelected={isCellSelected}\n            onRowChange={onRowChange}\n          />\n          {dragHandle}\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default memo(Cell) as <R, SR>(props: CellRendererProps<R, SR>) => JSX.Element;\n", "import { memo, forwardRef } from 'react';\nimport type { RefAttributes } from 'react';\nimport clsx from 'clsx';\n\nimport Cell from './Cell';\nimport { RowSelectionProvider, useLatestFunc } from './hooks';\nimport { getColSpan, getRowStyle } from './utils';\nimport { rowClassname, rowSelectedClassname } from './style';\nimport type { RowRendererProps } from './types';\n\nfunction Row<R, SR>(\n  {\n    className,\n    rowIdx,\n    gridRowStart,\n    height,\n    selectedCellIdx,\n    isRowSelected,\n    copiedCellIdx,\n    draggedOverCellIdx,\n    lastFrozenColumnIndex,\n    row,\n    viewportColumns,\n    selectedCellEditor,\n    selectedCellDragHandle,\n    onRowClick,\n    onRowDoubleClick,\n    rowClass,\n    setDraggedOverRowIdx,\n    onMouseEnter,\n    onRowChange,\n    selectCell,\n    ...props\n  }: RowRendererProps<R, SR>,\n  ref: React.Ref<HTMLDivElement>\n) {\n  const handleRowChange = useLatestFunc((newRow: R) => {\n    onRowChange(rowIdx, newRow);\n  });\n\n  function handleDragEnter(event: React.MouseEvent<HTMLDivElement>) {\n    setDraggedOverRowIdx?.(rowIdx);\n    onMouseEnter?.(event);\n  }\n\n  className = clsx(\n    rowClassname,\n    `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n    {\n      [rowSelectedClassname]: selectedCellIdx === -1\n    },\n    rowClass?.(row),\n    className\n  );\n\n  const cells = [];\n\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const { idx } = column;\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    const isCellSelected = selectedCellIdx === idx;\n\n    if (isCellSelected && selectedCellEditor) {\n      cells.push(selectedCellEditor);\n    } else {\n      cells.push(\n        <Cell\n          key={column.key}\n          column={column}\n          colSpan={colSpan}\n          row={row}\n          isCopied={copiedCellIdx === idx}\n          isDraggedOver={draggedOverCellIdx === idx}\n          isCellSelected={isCellSelected}\n          dragHandle={isCellSelected ? selectedCellDragHandle : undefined}\n          onRowClick={onRowClick}\n          onRowDoubleClick={onRowDoubleClick}\n          onRowChange={handleRowChange}\n          selectCell={selectCell}\n        />\n      );\n    }\n  }\n\n  return (\n    <RowSelectionProvider value={isRowSelected}>\n      <div\n        role=\"row\"\n        ref={ref}\n        className={className}\n        onMouseEnter={handleDragEnter}\n        style={getRowStyle(gridRowStart, height)}\n        {...props}\n      >\n        {cells}\n      </div>\n    </RowSelectionProvider>\n  );\n}\n\nexport default memo(forwardRef(Row)) as <R, SR>(\n  props: RowRendererProps<R, SR> & RefAttributes<HTMLDivElement>\n) => JSX.Element;\n", "import { memo } from 'react';\n\nimport { getCellStyle, getCellClassname } from './utils';\nimport type { CalculatedColumn, GroupRow } from './types';\nimport type { GroupRowRendererProps } from './GroupRow';\nimport { useRovingCellRef } from './hooks';\n\ntype SharedGroupRowRendererProps<R, SR> = Pick<\n  GroupRowRendererProps<R, SR>,\n  'id' | 'groupKey' | 'childRows' | 'isExpanded' | 'toggleGroup'\n>;\n\ninterface GroupCellProps<R, SR> extends SharedGroupRowRendererProps<R, SR> {\n  column: CalculatedColumn<R, SR>;\n  row: GroupRow<R>;\n  isCellSelected: boolean;\n  groupColumnIndex: number;\n}\n\nfunction GroupCell<R, SR>({\n  id,\n  groupKey,\n  childRows,\n  isExpanded,\n  isCellSelected,\n  column,\n  row,\n  groupColumnIndex,\n  toggleGroup: toggleGroupWrapper\n}: GroupCellProps<R, SR>) {\n  const { ref, tabIndex, onFocus } = useRovingCellRef(isCellSelected);\n\n  function toggleGroup() {\n    toggleGroupWrapper(id);\n  }\n\n  // Only make the cell clickable if the group level matches\n  const isLevelMatching = column.rowGroup && groupColumnIndex === column.idx;\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1}\n      aria-selected={isCellSelected}\n      ref={ref}\n      tabIndex={tabIndex}\n      key={column.key}\n      className={getCellClassname(column)}\n      style={{\n        ...getCellStyle(column),\n        cursor: isLevelMatching ? 'pointer' : 'default'\n      }}\n      onClick={isLevelMatching ? toggleGroup : undefined}\n      onFocus={onFocus}\n    >\n      {(!column.rowGroup || groupColumnIndex === column.idx) && column.groupFormatter && (\n        <column.groupFormatter\n          groupKey={groupKey}\n          childRows={childRows}\n          column={column}\n          row={row}\n          isExpanded={isExpanded}\n          isCellSelected={isCellSelected}\n          toggleGroup={toggleGroup}\n        />\n      )}\n    </div>\n  );\n}\n\nexport default memo(GroupCell) as <R, SR>(props: GroupCellProps<R, SR>) => JSX.Element;\n", "import { memo } from 'react';\nimport clsx from 'clsx';\nimport { css } from '@linaria/core';\n\nimport { cell, cellFrozenLast, rowClassname, rowSelectedClassname } from './style';\nimport { SELECT_COLUMN_KEY } from './Columns';\nimport GroupCell from './GroupCell';\nimport type { CalculatedColumn, GroupRow, Omit } from './types';\nimport { RowSelectionProvider } from './hooks';\nimport { getRowStyle } from './utils';\n\nexport interface GroupRowRendererProps<R, SR>\n  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'style' | 'children'> {\n  id: string;\n  groupKey: unknown;\n  viewportColumns: readonly CalculatedColumn<R, SR>[];\n  childRows: readonly R[];\n  rowIdx: number;\n  row: GroupRow<R>;\n  gridRowStart: number;\n  height: number;\n  level: number;\n  selectedCellIdx: number | undefined;\n  isExpanded: boolean;\n  isRowSelected: boolean;\n  selectGroup: (rowIdx: number) => void;\n  toggleGroup: (expandedGroupId: unknown) => void;\n}\n\nconst groupRow = css`\n  &:not([aria-selected='true']) {\n    background-color: var(--rdg-header-background-color);\n  }\n\n  > .${cell}:not(:last-child):not(.${cellFrozenLast}) {\n    border-inline-end: none;\n  }\n`;\n\nconst groupRowClassname = `rdg-group-row ${groupRow}`;\n\nfunction GroupedRow<R, SR>({\n  id,\n  groupKey,\n  viewportColumns,\n  childRows,\n  rowIdx,\n  row,\n  gridRowStart,\n  height,\n  level,\n  isExpanded,\n  selectedCellIdx,\n  isRowSelected,\n  selectGroup,\n  toggleGroup,\n  ...props\n}: GroupRowRendererProps<R, SR>) {\n  // Select is always the first column\n  const idx = viewportColumns[0].key === SELECT_COLUMN_KEY ? level + 1 : level;\n\n  function handleSelectGroup() {\n    selectGroup(rowIdx);\n  }\n\n  return (\n    <RowSelectionProvider value={isRowSelected}>\n      <div\n        role=\"row\"\n        aria-level={level}\n        aria-expanded={isExpanded}\n        className={clsx(\n          rowClassname,\n          groupRowClassname,\n          `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n          {\n            [rowSelectedClassname]: selectedCellIdx === -1\n          }\n        )}\n        onClick={handleSelectGroup}\n        style={getRowStyle(gridRowStart, height)}\n        {...props}\n      >\n        {viewportColumns.map((column) => (\n          <GroupCell\n            key={column.key}\n            id={id}\n            groupKey={groupKey}\n            childRows={childRows}\n            isExpanded={isExpanded}\n            isCellSelected={selectedCellIdx === column.idx}\n            column={column}\n            row={row}\n            groupColumnIndex={idx}\n            toggleGroup={toggleGroup}\n          />\n        ))}\n      </div>\n    </RowSelectionProvider>\n  );\n}\n\nexport default memo(GroupedRow) as <R, SR>(props: GroupRowRendererProps<R, SR>) => JSX.Element;\n", "import { memo } from 'react';\nimport { css } from '@linaria/core';\n\nimport { getCellStyle, getCellClassname } from './utils';\nimport type { CalculatedColumn, CellRendererProps } from './types';\nimport { useRovingCellRef } from './hooks';\n\nexport const summaryCellClassname = css`\n  inset-block-start: var(--rdg-summary-row-top);\n  inset-block-end: var(--rdg-summary-row-bottom);\n`;\n\ninterface SharedCellRendererProps<R, SR>\n  extends Pick<CellRendererProps<R, SR>, 'column' | 'colSpan' | 'isCellSelected'> {\n  selectCell: (row: SR, column: CalculatedColumn<R, SR>) => void;\n}\n\ninterface SummaryCellProps<R, SR> extends SharedCellRendererProps<R, SR> {\n  row: SR;\n}\n\nfunction SummaryCell<R, SR>({\n  column,\n  colSpan,\n  row,\n  isCellSelected,\n  selectCell\n}: SummaryCellProps<R, SR>) {\n  const { ref, tabIndex, onFocus } = useRovingCellRef(isCellSelected);\n  const { summaryFormatter: SummaryFormatter, summaryCellClass } = column;\n  const className = getCellClassname(\n    column,\n    summaryCellClassname,\n    typeof summaryCellClass === 'function' ? summaryCellClass(row) : summaryCellClass\n  );\n\n  function onClick() {\n    selectCell(row, column);\n  }\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1}\n      aria-colspan={colSpan}\n      aria-selected={isCellSelected}\n      ref={ref}\n      tabIndex={tabIndex}\n      className={className}\n      style={getCellStyle(column, colSpan)}\n      onClick={onClick}\n      onFocus={onFocus}\n    >\n      {SummaryFormatter && (\n        <SummaryFormatter column={column} row={row} isCellSelected={isCellSelected} />\n      )}\n    </div>\n  );\n}\n\nexport default memo(SummaryCell) as <R, SR>(props: SummaryCellProps<R, SR>) => JSX.Element;\n", "import { memo } from 'react';\nimport clsx from 'clsx';\nimport { css } from '@linaria/core';\n\nimport { cell, row, rowClassname, rowSelectedClassname } from './style';\nimport { getColSpan, getRowStyle } from './utils';\nimport SummaryCell from './SummaryCell';\nimport type { CalculatedColumn, RowRendererProps } from './types';\n\ntype SharedRowRendererProps<R, SR> = Pick<\n  RowRendererProps<R, SR>,\n  'viewportColumns' | 'rowIdx' | 'gridRowStart'\n>;\n\ninterface SummaryRowProps<R, SR> extends SharedRowRendererProps<R, SR> {\n  'aria-rowindex': number;\n  row: SR;\n  top: number | undefined;\n  bottom: number | undefined;\n  lastFrozenColumnIndex: number;\n  selectedCellIdx: number | undefined;\n  selectCell: (row: SR, column: CalculatedColumn<R, SR>) => void;\n}\n\nconst summaryRow = css`\n  &.${row} {\n    line-height: var(--rdg-summary-row-height);\n    > .${cell} {\n      position: sticky;\n    }\n  }\n`;\n\nconst summaryRowBorderClassname = css`\n  & > .${cell} {\n    border-block-start: 2px solid var(--rdg-summary-border-color);\n  }\n`;\n\nconst summaryRowClassname = `rdg-summary-row ${summaryRow}`;\n\nfunction SummaryRow<R, SR>({\n  rowIdx,\n  gridRowStart,\n  row,\n  viewportColumns,\n  top,\n  bottom,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  selectCell,\n  'aria-rowindex': ariaRowIndex\n}: SummaryRowProps<R, SR>) {\n  const cells = [];\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'SUMMARY', row });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n\n    const isCellSelected = selectedCellIdx === column.idx;\n\n    cells.push(\n      <SummaryCell<R, SR>\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        row={row}\n        isCellSelected={isCellSelected}\n        selectCell={selectCell}\n      />\n    );\n  }\n\n  return (\n    <div\n      role=\"row\"\n      aria-rowindex={ariaRowIndex}\n      className={clsx(\n        rowClassname,\n        `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`,\n        summaryRowClassname,\n        {\n          [summaryRowBorderClassname]: rowIdx === 0,\n          [rowSelectedClassname]: selectedCellIdx === -1\n        }\n      )}\n      style={\n        {\n          ...getRowStyle(gridRowStart),\n          '--rdg-summary-row-top': top !== undefined ? `${top}px` : undefined,\n          '--rdg-summary-row-bottom': bottom !== undefined ? `${bottom}px` : undefined\n        } as unknown as React.CSSProperties\n      }\n    >\n      {cells}\n    </div>\n  );\n}\n\nexport default memo(SummaryRow) as <R, SR>(props: SummaryRowProps<R, SR>) => JSX.Element;\n", "import { useEffect, useRef } from 'react';\nimport { css } from '@linaria/core';\n\nimport { useLatestFunc } from './hooks';\nimport { getCellStyle, getCellClassname, onEditorNavigation } from './utils';\nimport type { CellRendererProps, EditorProps, Omit } from './types';\n\n/*\n * To check for outside `mousedown` events, we listen to all `mousedown` events at their birth,\n * i.e. on the window during the capture phase, and at their death, i.e. on the window during the bubble phase.\n *\n * We schedule a check at the birth of the event, cancel the check when the event reaches the \"inside\" container,\n * and trigger the \"outside\" callback when the event bubbles back up to the window.\n *\n * The event can be `stopPropagation()`ed halfway through, so they may not always bubble back up to the window,\n * so an alternative check must be used. The check must happen after the event can reach the \"inside\" container,\n * and not before it run to completion. `requestAnimationFrame` is the best way we know how to achieve this.\n * Usually we want click event handlers from parent components to access the latest commited values,\n * so `mousedown` is used instead of `click`.\n *\n * We must also rely on React's event capturing/bubbling to handle elements rendered in a portal.\n */\n\nconst cellEditing = css`\n  &.rdg-cell {\n    padding: 0;\n  }\n`;\n\ntype SharedCellRendererProps<R, SR> = Pick<CellRendererProps<R, SR>, 'colSpan'>;\n\ninterface EditCellProps<R, SR>\n  extends Omit<EditorProps<R, SR>, 'onClose'>,\n    SharedCellRendererProps<R, SR> {\n  closeEditor: () => void;\n}\n\nexport default function EditCell<R, SR>({\n  column,\n  colSpan,\n  row,\n  onRowChange,\n  closeEditor\n}: EditCellProps<R, SR>) {\n  const frameRequestRef = useRef<number | undefined>();\n  const commitOnOutsideClick = column.editorOptions?.commitOnOutsideClick !== false;\n\n  // We need to prevent the `useEffect` from cleaning up between re-renders,\n  // as `onWindowCaptureMouseDown` might otherwise miss valid mousedown events.\n  // To that end we instead access the latest props via useLatestFunc.\n  const commitOnOutsideMouseDown = useLatestFunc(() => {\n    onClose(true);\n  });\n\n  useEffect(() => {\n    if (!commitOnOutsideClick) return;\n\n    function onWindowCaptureMouseDown() {\n      frameRequestRef.current = requestAnimationFrame(commitOnOutsideMouseDown);\n    }\n\n    addEventListener('mousedown', onWindowCaptureMouseDown, { capture: true });\n\n    return () => {\n      removeEventListener('mousedown', onWindowCaptureMouseDown, { capture: true });\n      cancelFrameRequest();\n    };\n  }, [commitOnOutsideClick, commitOnOutsideMouseDown]);\n\n  function cancelFrameRequest() {\n    cancelAnimationFrame(frameRequestRef.current!);\n  }\n\n  function onKeyDown(event: React.KeyboardEvent<HTMLDivElement>) {\n    if (event.key === 'Escape') {\n      event.stopPropagation();\n      // Discard changes\n      onClose();\n    } else if (event.key === 'Enter') {\n      event.stopPropagation();\n      onClose(true);\n    } else {\n      const onNavigation = column.editorOptions?.onNavigation ?? onEditorNavigation;\n      if (!onNavigation(event)) {\n        event.stopPropagation();\n      }\n    }\n  }\n\n  function onClose(commitChanges?: boolean) {\n    if (commitChanges) {\n      onRowChange(row, true);\n    } else {\n      closeEditor();\n    }\n  }\n\n  const { cellClass } = column;\n  const className = getCellClassname(\n    column,\n    'rdg-editor-container',\n    !column.editorOptions?.renderFormatter && cellEditing,\n    typeof cellClass === 'function' ? cellClass(row) : cellClass\n  );\n\n  return (\n    <div\n      role=\"gridcell\"\n      aria-colindex={column.idx + 1} // aria-colindex is 1-based\n      aria-colspan={colSpan}\n      aria-selected\n      className={className}\n      style={getCellStyle(column, colSpan)}\n      onKeyDown={onKeyDown}\n      onMouseDownCapture={commitOnOutsideClick ? cancelFrameRequest : undefined}\n    >\n      {column.editor != null && (\n        <>\n          <column.editor column={column} row={row} onRowChange={onRowChange} onClose={onClose} />\n          {column.editorOptions?.renderFormatter && (\n            <column.formatter column={column} row={row} isCellSelected onRowChange={onRowChange} />\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n", "import { css } from '@linaria/core';\n\nimport type { CalculatedColumn, FillEvent, Position } from './types';\nimport type { DataGridProps, SelectCellState } from './DataGrid';\n\nconst cellDragHandle = css`\n  cursor: move;\n  position: absolute;\n  inset-inline-end: 0;\n  inset-block-end: 0;\n  inline-size: 8px;\n  block-size: 8px;\n  background-color: var(--rdg-selection-color);\n\n  &:hover {\n    inline-size: 16px;\n    block-size: 16px;\n    border: 2px solid var(--rdg-selection-color);\n    background-color: var(--rdg-background-color);\n  }\n`;\n\nconst cellDragHandleClassname = `rdg-cell-drag-handle ${cellDragHandle}`;\n\ninterface Props<R, SR> extends Pick<DataGridProps<R, SR>, 'rows' | 'onRowsChange'> {\n  columns: readonly CalculatedColumn<R, SR>[];\n  selectedPosition: SelectCellState;\n  latestDraggedOverRowIdx: React.MutableRefObject<number | undefined>;\n  isCellEditable: (position: Position) => boolean;\n  onFill: (event: FillEvent<R>) => R;\n  setDragging: (isDragging: boolean) => void;\n  setDraggedOverRowIdx: (overRowIdx: number | undefined) => void;\n}\n\nexport default function DragHandle<R, SR>({\n  rows,\n  columns,\n  selectedPosition,\n  latestDraggedOverRowIdx,\n  isCellEditable,\n  onRowsChange,\n  onFill,\n  setDragging,\n  setDraggedOverRowIdx\n}: Props<R, SR>) {\n  function handleMouseDown(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {\n    if (event.buttons !== 1) return;\n    setDragging(true);\n    window.addEventListener('mouseover', onMouseOver);\n    window.addEventListener('mouseup', onMouseUp);\n\n    function onMouseOver(event: MouseEvent) {\n      // Trigger onMouseup in edge cases where we release the mouse button but `mouseup` isn't triggered,\n      // for example when releasing the mouse button outside the iframe the grid is rendered in.\n      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n      if (event.buttons !== 1) onMouseUp();\n    }\n\n    function onMouseUp() {\n      window.removeEventListener('mouseover', onMouseOver);\n      window.removeEventListener('mouseup', onMouseUp);\n      setDragging(false);\n      handleDragEnd();\n    }\n  }\n\n  function handleDragEnd() {\n    const overRowIdx = latestDraggedOverRowIdx.current;\n    if (overRowIdx === undefined) return;\n\n    const { rowIdx } = selectedPosition;\n    const startRowIndex = rowIdx < overRowIdx ? rowIdx + 1 : overRowIdx;\n    const endRowIndex = rowIdx < overRowIdx ? overRowIdx + 1 : rowIdx;\n    updateRows(startRowIndex, endRowIndex);\n    setDraggedOverRowIdx(undefined);\n  }\n\n  function handleDoubleClick(event: React.MouseEvent<HTMLDivElement>) {\n    event.stopPropagation();\n    updateRows(selectedPosition.rowIdx + 1, rows.length);\n  }\n\n  function updateRows(startRowIdx: number, endRowIdx: number) {\n    const { idx, rowIdx } = selectedPosition;\n    const column = columns[idx];\n    const sourceRow = rows[rowIdx];\n    const updatedRows = [...rows];\n    const indexes: number[] = [];\n    for (let i = startRowIdx; i < endRowIdx; i++) {\n      if (isCellEditable({ rowIdx: i, idx })) {\n        const updatedRow = onFill({ columnKey: column.key, sourceRow, targetRow: rows[i] });\n        if (updatedRow !== rows[i]) {\n          updatedRows[i] = updatedRow;\n          indexes.push(i);\n        }\n      }\n    }\n\n    if (indexes.length > 0) {\n      onRowsChange?.(updatedRows, { indexes, column });\n    }\n  }\n\n  return (\n    <div\n      className={cellDragHandleClassname}\n      onMouseDown={handleMouseDown}\n      onDoubleClick={handleDoubleClick}\n    />\n  );\n}\n", "import { css } from '@linaria/core';\nimport type { SortIconProps } from './types';\n\nconst arrow = css`\n  fill: currentColor;\n\n  > path {\n    transition: d 0.1s;\n  }\n`;\n\nconst arrowClassname = `rdg-sort-arrow ${arrow}`;\n\nexport default function SortIcon({ sortDirection }: SortIconProps) {\n  return sortDirection !== undefined ? (\n    <svg viewBox=\"0 0 12 8\" width=\"12\" height=\"8\" className={arrowClassname} aria-hidden>\n      <path d={sortDirection === 'ASC' ? 'M0 8 6 0 12 8' : 'M0 0 6 8 12 0'} />\n    </svg>\n  ) : null;\n}\n", "import { forwardRef, useState, useRef, useImperativeHandle, useCallback, useMemo } from 'react';\nimport type { Key, RefAttributes } from 'react';\nimport clsx from 'clsx';\n\nimport {\n  rootClassname,\n  viewportDraggingClassname,\n  focusSinkClassname,\n  cellAutoResizeClassname,\n  rowSelected,\n  rowSelectedWithFrozenCell\n} from './style';\nimport {\n  useLayoutEffect,\n  useGridDimensions,\n  useCalculatedColumns,\n  useViewportColumns,\n  useViewportRows,\n  useLatestFunc,\n  RowSelectionChangeProvider\n} from './hooks';\nimport HeaderRow from './HeaderRow';\nimport Row from './Row';\nimport GroupRowRenderer from './GroupRow';\nimport SummaryRow from './SummaryRow';\nimport EditCell from './EditCell';\nimport DragHandle from './DragHandle';\nimport SortIcon from './SortIcon';\nimport { CheckboxFormatter } from './formatters';\nimport {\n  DataGridDefaultComponentsProvider,\n  useDefaultComponents\n} from './DataGridDefaultComponentsProvider';\nimport {\n  assertIsValidKeyGetter,\n  getNextSelectedCellPosition,\n  isSelectedCellEditable,\n  canExitGrid,\n  isCtrlKeyHeldDown,\n  isDefaultCellInput,\n  getColSpan,\n  sign,\n  abs,\n  getSelectedCellColSpan,\n  scrollIntoView\n} from './utils';\n\nimport type {\n  CalculatedColumn,\n  Column,\n  Position,\n  RowsChangeData,\n  SelectRowEvent,\n  FillEvent,\n  CopyEvent,\n  PasteEvent,\n  CellNavigationMode,\n  SortColumn,\n  RowHeightArgs,\n  Maybe,\n  Components,\n  Direction\n} from './types';\n\nexport interface SelectCellState extends Position {\n  readonly mode: 'SELECT';\n}\n\ninterface EditCellState<R> extends Position {\n  readonly mode: 'EDIT';\n  readonly row: R;\n  readonly originalRow: R;\n}\n\ntype DefaultColumnOptions<R, SR> = Pick<\n  Column<R, SR>,\n  'formatter' | 'width' | 'minWidth' | 'maxWidth' | 'resizable' | 'sortable'\n>;\n\nconst initialPosition: SelectCellState = {\n  idx: -1,\n  rowIdx: -2,\n  mode: 'SELECT'\n};\n\nexport interface DataGridHandle {\n  element: HTMLDivElement | null;\n  scrollToColumn: (colIdx: number) => void;\n  scrollToRow: (rowIdx: number) => void;\n  selectCell: (position: Position, enableEditor?: Maybe<boolean>) => void;\n}\n\ntype SharedDivProps = Pick<\n  React.HTMLAttributes<HTMLDivElement>,\n  'aria-label' | 'aria-labelledby' | 'aria-describedby' | 'className' | 'style'\n>;\n\nexport interface DataGridProps<R, SR = unknown, K extends Key = Key> extends SharedDivProps {\n  /**\n   * Grid and data Props\n   */\n  /** An array of objects representing each column on the grid */\n  columns: readonly Column<R, SR>[];\n  /** A function called for each rendered row that should return a plain key/value pair object */\n  rows: readonly R[];\n  /**\n   * Rows to be pinned at the bottom of the rows view for summary, the vertical scroll bar will not scroll these rows.\n   * Bottom horizontal scroll bar can move the row left / right. Or a customized row renderer can be used to disabled the scrolling support.\n   */\n  summaryRows?: Maybe<readonly SR[]>;\n  /** The getter should return a unique key for each row */\n  rowKeyGetter?: Maybe<(row: R) => K>;\n  onRowsChange?: Maybe<(rows: R[], data: RowsChangeData<R, SR>) => void>;\n\n  /**\n   * Dimensions props\n   */\n  /**\n   * The height of each row in pixels\n   * @default 35\n   */\n  rowHeight?: Maybe<number | ((args: RowHeightArgs<R>) => number)>;\n  /**\n   * The height of the header row in pixels\n   * @default 35\n   */\n  headerRowHeight?: Maybe<number>;\n  /**\n   * The height of each summary row in pixels\n   * @default 35\n   */\n  summaryRowHeight?: Maybe<number>;\n\n  /**\n   * Feature props\n   */\n  /** Set of selected row keys */\n  selectedRows?: Maybe<ReadonlySet<K>>;\n  /** Function called whenever row selection is changed */\n  onSelectedRowsChange?: Maybe<(selectedRows: Set<K>) => void>;\n  /** Used for multi column sorting */\n  sortColumns?: Maybe<readonly SortColumn[]>;\n  onSortColumnsChange?: Maybe<(sortColumns: SortColumn[]) => void>;\n  defaultColumnOptions?: Maybe<DefaultColumnOptions<R, SR>>;\n  groupBy?: Maybe<readonly string[]>;\n  rowGrouper?: Maybe<(rows: readonly R[], columnKey: string) => Record<string, readonly R[]>>;\n  expandedGroupIds?: Maybe<ReadonlySet<unknown>>;\n  onExpandedGroupIdsChange?: Maybe<(expandedGroupIds: Set<unknown>) => void>;\n  onFill?: Maybe<(event: FillEvent<R>) => R>;\n  onCopy?: Maybe<(event: CopyEvent<R>) => void>;\n  onPaste?: Maybe<(event: PasteEvent<R>) => R>;\n\n  /**\n   * Event props\n   */\n  /** Function called whenever a row is clicked */\n  onRowClick?: Maybe<(row: R, column: CalculatedColumn<R, SR>) => void>;\n  /** Function called whenever a row is double clicked */\n  onRowDoubleClick?: Maybe<(row: R, column: CalculatedColumn<R, SR>) => void>;\n  /** Called when the grid is scrolled */\n  onScroll?: Maybe<(event: React.UIEvent<HTMLDivElement>) => void>;\n  /** Called when a column is resized */\n  onColumnResize?: Maybe<(idx: number, width: number) => void>;\n\n  /**\n   * Toggles and modes\n   */\n  /** @default 'NONE' */\n  cellNavigationMode?: Maybe<CellNavigationMode>;\n  /** @default true */\n  enableVirtualization?: Maybe<boolean>;\n\n  /**\n   * Miscellaneous\n   */\n  components?: Maybe<Components<R, SR>>;\n  rowClass?: Maybe<(row: R) => Maybe<string>>;\n  direction?: Maybe<Direction>;\n  'data-testid'?: Maybe<string>;\n}\n\n/**\n * Main API Component to render a data grid of rows and columns\n *\n * @example\n *\n * <DataGrid columns={columns} rows={rows} />\n */\nfunction DataGrid<R, SR, K extends Key>(\n  {\n    // Grid and data Props\n    columns: rawColumns,\n    rows: rawRows,\n    summaryRows,\n    rowKeyGetter,\n    onRowsChange,\n    // Dimensions props\n    rowHeight,\n    headerRowHeight: rawHeaderRowHeight,\n    summaryRowHeight: rawSummaryRowHeight,\n    // Feature props\n    selectedRows,\n    onSelectedRowsChange,\n    sortColumns,\n    onSortColumnsChange,\n    defaultColumnOptions,\n    groupBy: rawGroupBy,\n    rowGrouper,\n    expandedGroupIds,\n    onExpandedGroupIdsChange,\n    // Event props\n    onRowClick,\n    onRowDoubleClick,\n    onScroll,\n    onColumnResize,\n    onFill,\n    onCopy,\n    onPaste,\n    // Toggles and modes\n    cellNavigationMode: rawCellNavigationMode,\n    enableVirtualization,\n    // Miscellaneous\n    components,\n    className,\n    style,\n    rowClass,\n    direction,\n    // ARIA\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    'aria-describedby': ariaDescribedBy,\n    'data-testid': testId\n  }: DataGridProps<R, SR, K>,\n  ref: React.Ref<DataGridHandle>\n) {\n  /**\n   * defaults\n   */\n  const defaultComponents = useDefaultComponents<R, SR>();\n  rowHeight ??= 35;\n  const headerRowHeight = rawHeaderRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const summaryRowHeight = rawSummaryRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const RowRenderer = components?.rowRenderer ?? defaultComponents?.rowRenderer ?? Row;\n  const sortIcon = components?.sortIcon ?? defaultComponents?.sortIcon ?? SortIcon;\n  const checkboxFormatter =\n    components?.checkboxFormatter ?? defaultComponents?.checkboxFormatter ?? CheckboxFormatter;\n  const noRowsFallback = components?.noRowsFallback ?? defaultComponents?.noRowsFallback;\n  const cellNavigationMode = rawCellNavigationMode ?? 'NONE';\n  enableVirtualization ??= true;\n  direction ??= 'ltr';\n\n  /**\n   * states\n   */\n  const [scrollTop, setScrollTop] = useState(0);\n  const [scrollLeft, setScrollLeft] = useState(0);\n  const [columnWidths, setColumnWidths] = useState<ReadonlyMap<string, number>>(() => new Map());\n  const [selectedPosition, setSelectedPosition] = useState<SelectCellState | EditCellState<R>>(\n    initialPosition\n  );\n  const [copiedCell, setCopiedCell] = useState<{ row: R; columnKey: string } | null>(null);\n  const [isDragging, setDragging] = useState(false);\n  const [draggedOverRowIdx, setOverRowIdx] = useState<number | undefined>(undefined);\n  const [autoResizeColumn, setAutoResizeColumn] = useState<CalculatedColumn<R, SR> | null>(null);\n\n  /**\n   * refs\n   */\n  const prevSelectedPosition = useRef(selectedPosition);\n  const latestDraggedOverRowIdx = useRef(draggedOverRowIdx);\n  const lastSelectedRowIdx = useRef(-1);\n  const rowRef = useRef<HTMLDivElement>(null);\n\n  /**\n   * computed values\n   */\n  const [gridRef, gridWidth, gridHeight] = useGridDimensions();\n  const headerRowsCount = 1;\n  const summaryRowsCount = summaryRows?.length ?? 0;\n  const clientHeight = gridHeight - headerRowHeight - summaryRowsCount * summaryRowHeight;\n  const isSelectable = selectedRows != null && onSelectedRowsChange != null;\n  const isHeaderRowSelected = selectedPosition.rowIdx === -1;\n  const isRtl = direction === 'rtl';\n  const leftKey = isRtl ? 'ArrowRight' : 'ArrowLeft';\n  const rightKey = isRtl ? 'ArrowLeft' : 'ArrowRight';\n\n  const defaultGridComponents = useMemo(\n    () => ({\n      sortIcon,\n      checkboxFormatter\n    }),\n    [sortIcon, checkboxFormatter]\n  );\n\n  const allRowsSelected = useMemo((): boolean => {\n    // no rows to select = explicitely unchecked\n    const { length } = rawRows;\n    return (\n      length !== 0 &&\n      selectedRows != null &&\n      rowKeyGetter != null &&\n      selectedRows.size >= length &&\n      rawRows.every((row) => selectedRows.has(rowKeyGetter(row)))\n    );\n  }, [rawRows, selectedRows, rowKeyGetter]);\n\n  const {\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    layoutCssVars,\n    columnMetrics,\n    lastFrozenColumnIndex,\n    totalFrozenColumnWidth,\n    groupBy\n  } = useCalculatedColumns({\n    rawColumns,\n    columnWidths,\n    scrollLeft,\n    viewportWidth: gridWidth,\n    defaultColumnOptions,\n    rawGroupBy: rowGrouper ? rawGroupBy : undefined,\n    enableVirtualization\n  });\n\n  const {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    rowsCount,\n    totalRowHeight,\n    gridTemplateRows,\n    isGroupRow,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  } = useViewportRows({\n    rawRows,\n    groupBy,\n    rowGrouper,\n    rowHeight,\n    clientHeight,\n    scrollTop,\n    expandedGroupIds,\n    enableVirtualization\n  });\n\n  const viewportColumns = useViewportColumns({\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    lastFrozenColumnIndex,\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    summaryRows,\n    isGroupRow\n  });\n\n  const hasGroups = groupBy.length > 0 && typeof rowGrouper === 'function';\n  const minColIdx = hasGroups ? -1 : 0;\n  const maxColIdx = columns.length - 1;\n  const minRowIdx = -1; // change it to 0?\n  const maxRowIdx = headerRowsCount + rows.length + summaryRowsCount - 2;\n  const selectedCellIsWithinSelectionBounds = isCellWithinSelectionBounds(selectedPosition);\n  const selectedCellIsWithinViewportBounds = isCellWithinViewportBounds(selectedPosition);\n\n  /**\n   * The identity of the wrapper function is stable so it won't break memoization\n   */\n  const selectRowLatest = useLatestFunc(selectRow);\n  const selectAllRowsLatest = useLatestFunc(selectAllRows);\n  const handleFormatterRowChangeLatest = useLatestFunc(updateRow);\n  const selectViewportCellLatest = useLatestFunc(\n    (row: R, column: CalculatedColumn<R, SR>, enableEditor: Maybe<boolean>) => {\n      const rowIdx = rows.indexOf(row);\n      selectCell({ rowIdx, idx: column.idx }, enableEditor);\n    }\n  );\n  const selectGroupLatest = useLatestFunc((rowIdx: number) => {\n    selectCell({ rowIdx, idx: -1 });\n  });\n  const selectHeaderCellLatest = useLatestFunc((idx: number) => {\n    selectCell({ rowIdx: -1, idx });\n  });\n  const selectSummaryCellLatest = useLatestFunc(\n    (summaryRow: SR, column: CalculatedColumn<R, SR>) => {\n      const rowIdx = summaryRows!.indexOf(summaryRow) + headerRowsCount + rows.length - 1;\n      selectCell({ rowIdx, idx: column.idx });\n    }\n  );\n  const toggleGroupLatest = useLatestFunc(toggleGroup);\n\n  /**\n   * effects\n   */\n  useLayoutEffect(() => {\n    if (\n      !selectedCellIsWithinSelectionBounds ||\n      isSamePosition(selectedPosition, prevSelectedPosition.current)\n    ) {\n      prevSelectedPosition.current = selectedPosition;\n      return;\n    }\n\n    prevSelectedPosition.current = selectedPosition;\n\n    if (selectedPosition.idx === -1) {\n      rowRef.current!.focus({ preventScroll: true });\n      scrollIntoView(rowRef.current);\n    }\n  });\n\n  useLayoutEffect(() => {\n    if (autoResizeColumn === null) return;\n    const columnElement = gridRef.current!.querySelector(\n      `[aria-colindex=\"${autoResizeColumn.idx + 1}\"]`\n    )!;\n    const { width } = columnElement.getBoundingClientRect();\n    setColumnWidths((columnWidths) => {\n      const newColumnWidths = new Map(columnWidths);\n      newColumnWidths.set(autoResizeColumn.key, width);\n      return newColumnWidths;\n    });\n    setAutoResizeColumn(null);\n    onColumnResize?.(autoResizeColumn.idx, width);\n  }, [autoResizeColumn, gridRef, onColumnResize]);\n\n  useImperativeHandle(ref, () => ({\n    element: gridRef.current,\n    scrollToColumn,\n    scrollToRow(rowIdx: number) {\n      const { current } = gridRef;\n      if (!current) return;\n      current.scrollTo({\n        top: getRowTop(rowIdx),\n        behavior: 'smooth'\n      });\n    },\n    selectCell\n  }));\n\n  /**\n   * callbacks\n   */\n  const handleColumnResize = useCallback(\n    (column: CalculatedColumn<R, SR>, width: number | 'auto') => {\n      if (width === 'auto') {\n        setAutoResizeColumn(column);\n        return;\n      }\n      setColumnWidths((columnWidths) => {\n        const newColumnWidths = new Map(columnWidths);\n        newColumnWidths.set(column.key, width);\n        return newColumnWidths;\n      });\n\n      onColumnResize?.(column.idx, width);\n    },\n    [onColumnResize]\n  );\n\n  const setDraggedOverRowIdx = useCallback((rowIdx?: number) => {\n    setOverRowIdx(rowIdx);\n    latestDraggedOverRowIdx.current = rowIdx;\n  }, []);\n\n  /**\n   * event handlers\n   */\n  function selectRow({ row, checked, isShiftClick }: SelectRowEvent<R>) {\n    if (!onSelectedRowsChange) return;\n\n    assertIsValidKeyGetter<R, K>(rowKeyGetter);\n    const newSelectedRows = new Set(selectedRows);\n    if (isGroupRow(row)) {\n      for (const childRow of row.childRows) {\n        const rowKey = rowKeyGetter(childRow);\n        if (checked) {\n          newSelectedRows.add(rowKey);\n        } else {\n          newSelectedRows.delete(rowKey);\n        }\n      }\n      onSelectedRowsChange(newSelectedRows);\n      return;\n    }\n\n    const rowKey = rowKeyGetter(row);\n    if (checked) {\n      newSelectedRows.add(rowKey);\n      const previousRowIdx = lastSelectedRowIdx.current;\n      const rowIdx = rows.indexOf(row);\n      lastSelectedRowIdx.current = rowIdx;\n      if (isShiftClick && previousRowIdx !== -1 && previousRowIdx !== rowIdx) {\n        const step = sign(rowIdx - previousRowIdx);\n        for (let i = previousRowIdx + step; i !== rowIdx; i += step) {\n          const row = rows[i];\n          if (isGroupRow(row)) continue;\n          newSelectedRows.add(rowKeyGetter(row));\n        }\n      }\n    } else {\n      newSelectedRows.delete(rowKey);\n      lastSelectedRowIdx.current = -1;\n    }\n\n    onSelectedRowsChange(newSelectedRows);\n  }\n\n  function selectAllRows(checked: boolean) {\n    if (!onSelectedRowsChange) return;\n\n    assertIsValidKeyGetter<R, K>(rowKeyGetter);\n    const newSelectedRows = new Set(selectedRows);\n\n    for (const row of rawRows) {\n      const rowKey = rowKeyGetter(row);\n      if (checked) {\n        newSelectedRows.add(rowKey);\n      } else {\n        newSelectedRows.delete(rowKey);\n      }\n    }\n\n    onSelectedRowsChange(newSelectedRows);\n  }\n\n  function toggleGroup(expandedGroupId: unknown) {\n    if (!onExpandedGroupIdsChange) return;\n    const newExpandedGroupIds = new Set(expandedGroupIds);\n    if (newExpandedGroupIds.has(expandedGroupId)) {\n      newExpandedGroupIds.delete(expandedGroupId);\n    } else {\n      newExpandedGroupIds.add(expandedGroupId);\n    }\n    onExpandedGroupIdsChange(newExpandedGroupIds);\n  }\n\n  function handleKeyDown(event: React.KeyboardEvent<HTMLDivElement>) {\n    if (!(event.target instanceof Element)) return;\n    const isCellEvent = event.target.closest('.rdg-cell') !== null;\n    const isRowEvent = hasGroups && event.target === rowRef.current;\n    if (!isCellEvent && !isRowEvent) return;\n\n    const { key, keyCode } = event;\n    const { rowIdx } = selectedPosition;\n\n    if (\n      selectedCellIsWithinViewportBounds &&\n      (onPaste != null || onCopy != null) &&\n      isCtrlKeyHeldDown(event) &&\n      !isGroupRow(rows[rowIdx]) &&\n      selectedPosition.mode === 'SELECT'\n    ) {\n      // event.key may differ by keyboard input language, so we use event.keyCode instead\n      // event.nativeEvent.code cannot be used either as it would break copy/paste for the DVORAK layout\n      const cKey = 67;\n      const vKey = 86;\n      if (keyCode === cKey) {\n        handleCopy();\n        return;\n      }\n      if (keyCode === vKey) {\n        handlePaste();\n        return;\n      }\n    }\n\n    if (isRowIdxWithinViewportBounds(rowIdx)) {\n      const row = rows[rowIdx];\n\n      if (\n        isGroupRow(row) &&\n        selectedPosition.idx === -1 &&\n        // Collapse the current group row if it is focused and is in expanded state\n        ((key === leftKey && row.isExpanded) ||\n          // Expand the current group row if it is focused and is in collapsed state\n          (key === rightKey && !row.isExpanded))\n      ) {\n        event.preventDefault(); // Prevents scrolling\n        toggleGroup(row.id);\n        return;\n      }\n    }\n\n    switch (event.key) {\n      case 'Escape':\n        setCopiedCell(null);\n        return;\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n      case 'Tab':\n      case 'Home':\n      case 'End':\n      case 'PageUp':\n      case 'PageDown':\n        navigate(event);\n        break;\n      default:\n        handleCellInput(event);\n        break;\n    }\n  }\n\n  function handleScroll(event: React.UIEvent<HTMLDivElement>) {\n    const { scrollTop, scrollLeft } = event.currentTarget;\n    setScrollTop(scrollTop);\n    // scrollLeft is nagative when direction is rtl\n    setScrollLeft(abs(scrollLeft));\n    onScroll?.(event);\n  }\n\n  function getRawRowIdx(rowIdx: number) {\n    return hasGroups ? rawRows.indexOf(rows[rowIdx] as R) : rowIdx;\n  }\n\n  function updateRow(rowIdx: number, row: R) {\n    if (typeof onRowsChange !== 'function') return;\n    const rawRowIdx = getRawRowIdx(rowIdx);\n    if (row === rawRows[rawRowIdx]) return;\n    const updatedRows = [...rawRows];\n    updatedRows[rawRowIdx] = row;\n    onRowsChange(updatedRows, {\n      indexes: [rawRowIdx],\n      column: columns[selectedPosition.idx]\n    });\n  }\n\n  function commitEditorChanges() {\n    if (selectedPosition.mode !== 'EDIT') return;\n    updateRow(selectedPosition.rowIdx, selectedPosition.row);\n  }\n\n  function handleCopy() {\n    const { idx, rowIdx } = selectedPosition;\n    const sourceRow = rawRows[getRawRowIdx(rowIdx)];\n    const sourceColumnKey = columns[idx].key;\n    setCopiedCell({ row: sourceRow, columnKey: sourceColumnKey });\n    onCopy?.({ sourceRow, sourceColumnKey });\n  }\n\n  function handlePaste() {\n    if (!onPaste || !onRowsChange || copiedCell === null || !isCellEditable(selectedPosition)) {\n      return;\n    }\n\n    const { idx, rowIdx } = selectedPosition;\n    const targetRow = rawRows[getRawRowIdx(rowIdx)];\n\n    const updatedTargetRow = onPaste({\n      sourceRow: copiedCell.row,\n      sourceColumnKey: copiedCell.columnKey,\n      targetRow,\n      targetColumnKey: columns[idx].key\n    });\n\n    updateRow(rowIdx, updatedTargetRow);\n  }\n\n  function handleCellInput(event: React.KeyboardEvent<HTMLDivElement>) {\n    if (!selectedCellIsWithinViewportBounds) return;\n    const row = rows[selectedPosition.rowIdx];\n    if (isGroupRow(row)) return;\n    const { key, shiftKey } = event;\n\n    // Select the row on Shift + Space\n    if (isSelectable && shiftKey && key === ' ') {\n      assertIsValidKeyGetter<R, K>(rowKeyGetter);\n      const rowKey = rowKeyGetter(row);\n      selectRow({ row, checked: !selectedRows.has(rowKey), isShiftClick: false });\n      // do not scroll\n      event.preventDefault();\n      return;\n    }\n\n    const column = columns[selectedPosition.idx];\n    column.editorOptions?.onCellKeyDown?.(event);\n    if (event.isDefaultPrevented()) return;\n\n    if (isCellEditable(selectedPosition) && isDefaultCellInput(event)) {\n      setSelectedPosition(({ idx, rowIdx }) => ({\n        idx,\n        rowIdx,\n        mode: 'EDIT',\n        row,\n        originalRow: row\n      }));\n    }\n  }\n\n  /**\n   * utils\n   */\n  function isColIdxWithinSelectionBounds(idx: number) {\n    return idx >= minColIdx && idx <= maxColIdx;\n  }\n\n  function isRowIdxWithinViewportBounds(rowIdx: number) {\n    return rowIdx >= 0 && rowIdx < rows.length;\n  }\n\n  function isCellWithinSelectionBounds({ idx, rowIdx }: Position): boolean {\n    return rowIdx >= minRowIdx && rowIdx <= maxRowIdx && isColIdxWithinSelectionBounds(idx);\n  }\n\n  function isCellWithinViewportBounds({ idx, rowIdx }: Position): boolean {\n    return isRowIdxWithinViewportBounds(rowIdx) && isColIdxWithinSelectionBounds(idx);\n  }\n\n  function isCellEditable(position: Position): boolean {\n    return (\n      isCellWithinViewportBounds(position) &&\n      isSelectedCellEditable({ columns, rows, selectedPosition: position, isGroupRow })\n    );\n  }\n\n  function selectCell(position: Position, enableEditor?: Maybe<boolean>): void {\n    if (!isCellWithinSelectionBounds(position)) return;\n    commitEditorChanges();\n\n    if (enableEditor && isCellEditable(position)) {\n      const row = rows[position.rowIdx] as R;\n      setSelectedPosition({ ...position, mode: 'EDIT', row, originalRow: row });\n    } else if (isSamePosition(selectedPosition, position)) {\n      // Avoid re-renders if the selected cell state is the same\n      scrollIntoView(gridRef.current?.querySelector('[tabindex=\"0\"]'));\n    } else {\n      setSelectedPosition({ ...position, mode: 'SELECT' });\n    }\n  }\n\n  function scrollToColumn(idx: number): void {\n    const { current } = gridRef;\n    if (!current) return;\n\n    if (idx > lastFrozenColumnIndex) {\n      const { rowIdx } = selectedPosition;\n      if (!isCellWithinSelectionBounds({ rowIdx, idx })) return;\n      const { clientWidth } = current;\n      const column = columns[idx];\n      const { left, width } = columnMetrics.get(column)!;\n      let right = left + width;\n\n      const colSpan = getSelectedCellColSpan({\n        rows,\n        summaryRows,\n        rowIdx,\n        lastFrozenColumnIndex,\n        column,\n        isGroupRow\n      });\n\n      if (colSpan !== undefined) {\n        const { left, width } = columnMetrics.get(columns[column.idx + colSpan - 1])!;\n        right = left + width;\n      }\n\n      const isCellAtLeftBoundary = left < scrollLeft + totalFrozenColumnWidth;\n      const isCellAtRightBoundary = right > clientWidth + scrollLeft;\n      const sign = isRtl ? -1 : 1;\n      if (isCellAtLeftBoundary) {\n        current.scrollLeft = (left - totalFrozenColumnWidth) * sign;\n      } else if (isCellAtRightBoundary) {\n        current.scrollLeft = (right - clientWidth) * sign;\n      }\n    }\n  }\n\n  function getNextPosition(key: string, ctrlKey: boolean, shiftKey: boolean): Position {\n    const { idx, rowIdx } = selectedPosition;\n    const row = rows[rowIdx];\n    const isRowSelected = selectedCellIsWithinSelectionBounds && idx === -1;\n\n    // If a group row is focused, and it is collapsed, move to the parent group row (if there is one).\n    if (key === leftKey && isRowSelected && isGroupRow(row) && !row.isExpanded && row.level !== 0) {\n      let parentRowIdx = -1;\n      for (let i = selectedPosition.rowIdx - 1; i >= 0; i--) {\n        const parentRow = rows[i];\n        if (isGroupRow(parentRow) && parentRow.id === row.parentId) {\n          parentRowIdx = i;\n          break;\n        }\n      }\n      if (parentRowIdx !== -1) {\n        return { idx, rowIdx: parentRowIdx };\n      }\n    }\n\n    switch (key) {\n      case 'ArrowUp':\n        return { idx, rowIdx: rowIdx - 1 };\n      case 'ArrowDown':\n        return { idx, rowIdx: rowIdx + 1 };\n      case leftKey:\n        return { idx: idx - 1, rowIdx };\n      case rightKey:\n        return { idx: idx + 1, rowIdx };\n      case 'Tab':\n        return { idx: idx + (shiftKey ? -1 : 1), rowIdx };\n      case 'Home':\n        // If row is selected then move focus to the first row\n        if (isRowSelected) return { idx, rowIdx: 0 };\n        return { idx: 0, rowIdx: ctrlKey ? minRowIdx : rowIdx };\n      case 'End':\n        // If row is selected then move focus to the last row.\n        if (isRowSelected) return { idx, rowIdx: rows.length - 1 };\n        return { idx: maxColIdx, rowIdx: ctrlKey ? maxRowIdx : rowIdx };\n      case 'PageUp': {\n        if (selectedPosition.rowIdx === minRowIdx) return selectedPosition;\n        const nextRowY = getRowTop(rowIdx) + getRowHeight(rowIdx) - clientHeight;\n        return { idx, rowIdx: nextRowY > 0 ? findRowIdx(nextRowY) : 0 };\n      }\n      case 'PageDown': {\n        if (selectedPosition.rowIdx >= rows.length) return selectedPosition;\n        const nextRowY = getRowTop(rowIdx) + clientHeight;\n        return { idx, rowIdx: nextRowY < totalRowHeight ? findRowIdx(nextRowY) : rows.length - 1 };\n      }\n      default:\n        return selectedPosition;\n    }\n  }\n\n  function navigate(event: React.KeyboardEvent<HTMLDivElement>) {\n    const { key, shiftKey } = event;\n    let mode = cellNavigationMode;\n    if (key === 'Tab') {\n      if (\n        canExitGrid({\n          shiftKey,\n          cellNavigationMode,\n          maxColIdx,\n          minRowIdx,\n          maxRowIdx,\n          selectedPosition\n        })\n      ) {\n        commitEditorChanges();\n        // Allow focus to leave the grid so the next control in the tab order can be focused\n        return;\n      }\n\n      mode = cellNavigationMode === 'NONE' ? 'CHANGE_ROW' : cellNavigationMode;\n    }\n\n    // Do not allow focus to leave\n    event.preventDefault();\n\n    const ctrlKey = isCtrlKeyHeldDown(event);\n    const nextPosition = getNextPosition(key, ctrlKey, shiftKey);\n    if (isSamePosition(selectedPosition, nextPosition)) return;\n\n    const nextSelectedCellPosition = getNextSelectedCellPosition({\n      columns,\n      colSpanColumns,\n      rows,\n      summaryRows,\n      minRowIdx,\n      maxRowIdx,\n      lastFrozenColumnIndex,\n      cellNavigationMode: mode,\n      currentPosition: selectedPosition,\n      nextPosition,\n      isCellWithinBounds: isCellWithinSelectionBounds,\n      isGroupRow\n    });\n\n    selectCell(nextSelectedCellPosition);\n  }\n\n  function getDraggedOverCellIdx(currentRowIdx: number): number | undefined {\n    if (draggedOverRowIdx === undefined) return;\n    const { rowIdx } = selectedPosition;\n\n    const isDraggedOver =\n      rowIdx < draggedOverRowIdx\n        ? rowIdx < currentRowIdx && currentRowIdx <= draggedOverRowIdx\n        : rowIdx > currentRowIdx && currentRowIdx >= draggedOverRowIdx;\n\n    return isDraggedOver ? selectedPosition.idx : undefined;\n  }\n\n  function getLayoutCssVars() {\n    if (autoResizeColumn === null) return layoutCssVars;\n    const { gridTemplateColumns } = layoutCssVars;\n    const newSizes = gridTemplateColumns.split(' ');\n    newSizes[autoResizeColumn.idx] = 'max-content';\n    return {\n      ...layoutCssVars,\n      gridTemplateColumns: newSizes.join(' ')\n    };\n  }\n\n  function getDragHandle(rowIdx: number) {\n    if (\n      selectedPosition.rowIdx !== rowIdx ||\n      selectedPosition.mode === 'EDIT' ||\n      hasGroups || // drag fill is not supported when grouping is enabled\n      onFill == null\n    ) {\n      return;\n    }\n\n    return (\n      <DragHandle\n        rows={rawRows}\n        columns={columns}\n        selectedPosition={selectedPosition}\n        isCellEditable={isCellEditable}\n        latestDraggedOverRowIdx={latestDraggedOverRowIdx}\n        onRowsChange={onRowsChange}\n        onFill={onFill}\n        setDragging={setDragging}\n        setDraggedOverRowIdx={setDraggedOverRowIdx}\n      />\n    );\n  }\n\n  function getCellEditor(rowIdx: number) {\n    if (selectedPosition.rowIdx !== rowIdx || selectedPosition.mode === 'SELECT') return;\n\n    const { idx, row } = selectedPosition;\n    const column = columns[idx];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, { type: 'ROW', row });\n\n    const closeEditor = () => {\n      setSelectedPosition(({ idx, rowIdx }) => ({ idx, rowIdx, mode: 'SELECT' }));\n    };\n\n    const onRowChange = (row: R, commitChanges?: boolean) => {\n      if (commitChanges) {\n        updateRow(selectedPosition.rowIdx, row);\n        closeEditor();\n      } else {\n        setSelectedPosition((position) => ({ ...position, row }));\n      }\n    };\n\n    if (rows[selectedPosition.rowIdx] !== selectedPosition.originalRow) {\n      // Discard changes if rows are updated from outside\n      closeEditor();\n    }\n\n    return (\n      <EditCell\n        key={column.key}\n        column={column}\n        colSpan={colSpan}\n        row={row}\n        onRowChange={onRowChange}\n        closeEditor={closeEditor}\n      />\n    );\n  }\n\n  function getRowViewportColumns(rowIdx: number) {\n    const selectedColumn = columns[selectedPosition.idx];\n    if (\n      // idx can be -1 if grouping is enabled\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      selectedColumn !== undefined &&\n      selectedPosition.rowIdx === rowIdx &&\n      !viewportColumns.includes(selectedColumn)\n    ) {\n      // Add the selected column to viewport columns if the cell is not within the viewport\n      return selectedPosition.idx > colOverscanEndIdx\n        ? [...viewportColumns, selectedColumn]\n        : [\n            ...viewportColumns.slice(0, lastFrozenColumnIndex + 1),\n            selectedColumn,\n            ...viewportColumns.slice(lastFrozenColumnIndex + 1)\n          ];\n    }\n    return viewportColumns;\n  }\n\n  function getViewportRows() {\n    const rowElements = [];\n    let startRowIndex = 0;\n\n    const { idx: selectedIdx, rowIdx: selectedRowIdx } = selectedPosition;\n    const startRowIdx =\n      selectedCellIsWithinViewportBounds && selectedRowIdx < rowOverscanStartIdx\n        ? rowOverscanStartIdx - 1\n        : rowOverscanStartIdx;\n    const endRowIdx =\n      selectedCellIsWithinViewportBounds && selectedRowIdx > rowOverscanEndIdx\n        ? rowOverscanEndIdx + 1\n        : rowOverscanEndIdx;\n\n    for (let viewportRowIdx = startRowIdx; viewportRowIdx <= endRowIdx; viewportRowIdx++) {\n      const isRowOutsideViewport =\n        viewportRowIdx === rowOverscanStartIdx - 1 || viewportRowIdx === rowOverscanEndIdx + 1;\n      const rowIdx = isRowOutsideViewport ? selectedRowIdx : viewportRowIdx;\n\n      let rowColumns = viewportColumns;\n      const selectedColumn = columns[selectedIdx];\n      // selectedIdx can be -1 if grouping is enabled\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (selectedColumn !== undefined) {\n        if (isRowOutsideViewport) {\n          // if the row is outside the viewport then only render the selected cell\n          rowColumns = [selectedColumn];\n        } else {\n          // if the row is within the viewport and cell is not, add the selected column to viewport columns\n          rowColumns = getRowViewportColumns(rowIdx);\n        }\n      }\n\n      const row = rows[rowIdx];\n      const gridRowStart = headerRowsCount + rowIdx + 1;\n      if (isGroupRow(row)) {\n        ({ startRowIndex } = row);\n        const isGroupRowSelected =\n          isSelectable && row.childRows.every((cr) => selectedRows.has(rowKeyGetter!(cr)));\n        rowElements.push(\n          <GroupRowRenderer\n            aria-level={row.level + 1} // aria-level is 1-based\n            aria-setsize={row.setSize}\n            aria-posinset={row.posInSet + 1} // aria-posinset is 1-based\n            aria-rowindex={headerRowsCount + startRowIndex + 1} // aria-rowindex is 1 based\n            aria-selected={isSelectable ? isGroupRowSelected : undefined}\n            key={row.id}\n            id={row.id}\n            groupKey={row.groupKey}\n            viewportColumns={rowColumns}\n            childRows={row.childRows}\n            rowIdx={rowIdx}\n            row={row}\n            gridRowStart={gridRowStart}\n            height={getRowHeight(rowIdx)}\n            level={row.level}\n            isExpanded={row.isExpanded}\n            selectedCellIdx={selectedRowIdx === rowIdx ? selectedIdx : undefined}\n            isRowSelected={isGroupRowSelected}\n            selectGroup={selectGroupLatest}\n            toggleGroup={toggleGroupLatest}\n          />\n        );\n        continue;\n      }\n\n      startRowIndex++;\n      let key;\n      let isRowSelected = false;\n      if (typeof rowKeyGetter === 'function') {\n        key = rowKeyGetter(row);\n        isRowSelected = selectedRows?.has(key) ?? false;\n      } else {\n        key = hasGroups ? startRowIndex : rowIdx;\n      }\n\n      rowElements.push(\n        <RowRenderer\n          aria-rowindex={headerRowsCount + (hasGroups ? startRowIndex : rowIdx) + 1} // aria-rowindex is 1 based\n          aria-selected={isSelectable ? isRowSelected : undefined}\n          key={key}\n          rowIdx={rowIdx}\n          row={row}\n          viewportColumns={rowColumns}\n          isRowSelected={isRowSelected}\n          onRowClick={onRowClick}\n          onRowDoubleClick={onRowDoubleClick}\n          rowClass={rowClass}\n          gridRowStart={gridRowStart}\n          height={getRowHeight(rowIdx)}\n          copiedCellIdx={\n            copiedCell !== null && copiedCell.row === row\n              ? columns.findIndex((c) => c.key === copiedCell.columnKey)\n              : undefined\n          }\n          selectedCellIdx={selectedRowIdx === rowIdx ? selectedIdx : undefined}\n          draggedOverCellIdx={getDraggedOverCellIdx(rowIdx)}\n          setDraggedOverRowIdx={isDragging ? setDraggedOverRowIdx : undefined}\n          lastFrozenColumnIndex={lastFrozenColumnIndex}\n          onRowChange={handleFormatterRowChangeLatest}\n          selectCell={selectViewportCellLatest}\n          selectedCellDragHandle={getDragHandle(rowIdx)}\n          selectedCellEditor={getCellEditor(rowIdx)}\n        />\n      );\n    }\n\n    return rowElements;\n  }\n\n  // Reset the positions if the current values are no longer valid. This can happen if a column or row is removed\n  if (selectedPosition.idx > maxColIdx || selectedPosition.rowIdx > maxRowIdx) {\n    setSelectedPosition(initialPosition);\n    setDraggedOverRowIdx(undefined);\n  }\n\n  let templateRows = `${headerRowHeight}px`;\n  if (rows.length > 0) {\n    templateRows += gridTemplateRows;\n  }\n  if (summaryRowsCount > 0) {\n    templateRows += ` repeat(${summaryRowsCount}, ${summaryRowHeight}px)`;\n  }\n\n  const isGroupRowFocused = selectedPosition.idx === -1 && selectedPosition.rowIdx !== -2;\n\n  return (\n    <div\n      role={hasGroups ? 'treegrid' : 'grid'}\n      aria-label={ariaLabel}\n      aria-labelledby={ariaLabelledBy}\n      aria-describedby={ariaDescribedBy}\n      aria-multiselectable={isSelectable ? true : undefined}\n      aria-colcount={columns.length}\n      aria-rowcount={headerRowsCount + rowsCount + summaryRowsCount}\n      className={clsx(\n        rootClassname,\n        {\n          [viewportDraggingClassname]: isDragging,\n          [cellAutoResizeClassname]: autoResizeColumn !== null\n        },\n        className\n      )}\n      style={\n        {\n          ...style,\n          // set scrollPadding to correctly position non-sticky cells after scrolling\n          scrollPaddingInlineStart:\n            selectedPosition.idx > lastFrozenColumnIndex\n              ? `${totalFrozenColumnWidth}px`\n              : undefined,\n          scrollPaddingBlock:\n            selectedPosition.rowIdx >= 0 && selectedPosition.rowIdx < rows.length\n              ? `${headerRowHeight}px ${summaryRowsCount * summaryRowHeight}px`\n              : undefined,\n          gridTemplateRows: templateRows,\n          '--rdg-header-row-height': `${headerRowHeight}px`,\n          '--rdg-summary-row-height': `${summaryRowHeight}px`,\n          '--rdg-sign': isRtl ? -1 : 1,\n          ...getLayoutCssVars()\n        } as unknown as React.CSSProperties\n      }\n      dir={direction}\n      ref={gridRef}\n      onScroll={handleScroll}\n      onKeyDown={handleKeyDown}\n      data-testid={testId}\n    >\n      {/* extra div is needed for row navigation in a treegrid */}\n      {hasGroups && (\n        <div\n          ref={rowRef}\n          tabIndex={isGroupRowFocused ? 0 : -1}\n          className={clsx(focusSinkClassname, {\n            [rowSelected]: isGroupRowFocused,\n            [rowSelectedWithFrozenCell]: isGroupRowFocused && lastFrozenColumnIndex !== -1\n          })}\n          style={{\n            gridRowStart: selectedPosition.rowIdx + 2\n          }}\n          onKeyDown={handleKeyDown}\n        />\n      )}\n      <DataGridDefaultComponentsProvider value={defaultGridComponents}>\n        <HeaderRow\n          columns={getRowViewportColumns(-1)}\n          onColumnResize={handleColumnResize}\n          allRowsSelected={allRowsSelected}\n          onAllRowsSelectionChange={selectAllRowsLatest}\n          sortColumns={sortColumns}\n          onSortColumnsChange={onSortColumnsChange}\n          lastFrozenColumnIndex={lastFrozenColumnIndex}\n          selectedCellIdx={isHeaderRowSelected ? selectedPosition.idx : undefined}\n          selectCell={selectHeaderCellLatest}\n          shouldFocusGrid={!selectedCellIsWithinSelectionBounds}\n          direction={direction}\n        />\n        {rows.length === 0 && noRowsFallback ? (\n          noRowsFallback\n        ) : (\n          <>\n            <RowSelectionChangeProvider value={selectRowLatest}>\n              {getViewportRows()}\n            </RowSelectionChangeProvider>\n            {summaryRows?.map((row, rowIdx) => {\n              const gridRowStart = headerRowsCount + rows.length + rowIdx + 1;\n              const summaryRowIdx = headerRowsCount + rows.length + rowIdx - 1;\n              const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;\n              const top =\n                clientHeight > totalRowHeight\n                  ? gridHeight - summaryRowHeight * (summaryRows.length - rowIdx)\n                  : undefined;\n              const bottom =\n                top === undefined\n                  ? summaryRowHeight * (summaryRows.length - 1 - rowIdx)\n                  : undefined;\n\n              return (\n                <SummaryRow\n                  aria-rowindex={headerRowsCount + rowsCount + rowIdx + 1}\n                  key={rowIdx}\n                  rowIdx={rowIdx}\n                  gridRowStart={gridRowStart}\n                  row={row}\n                  top={top}\n                  bottom={bottom}\n                  viewportColumns={getRowViewportColumns(summaryRowIdx)}\n                  lastFrozenColumnIndex={lastFrozenColumnIndex}\n                  selectedCellIdx={isSummaryRowSelected ? selectedPosition.idx : undefined}\n                  selectCell={selectSummaryCellLatest}\n                />\n              );\n            })}\n          </>\n        )}\n      </DataGridDefaultComponentsProvider>\n    </div>\n  );\n}\n\nfunction isSamePosition(p1: Position, p2: Position) {\n  return p1.idx === p2.idx && p1.rowIdx === p2.rowIdx;\n}\n\nexport default forwardRef(DataGrid) as <R, SR = unknown, K extends Key = Key>(\n  props: DataGridProps<R, SR, K> & RefAttributes<DataGridHandle>\n) => JSX.Element;\n", "import { css } from '@linaria/core';\nimport type { EditorProps } from '../types';\n\nconst textEditor = css`\n  appearance: none;\n\n  box-sizing: border-box;\n  inline-size: 100%;\n  block-size: 100%;\n  padding-block: 0;\n  padding-inline: 6px;\n  border: 2px solid #ccc;\n  vertical-align: top;\n  color: var(--rdg-color);\n  background-color: var(--rdg-background-color);\n\n  font-family: inherit;\n  font-size: var(--rdg-font-size);\n\n  &:focus {\n    border-color: var(--rdg-selection-color);\n    outline: none;\n  }\n\n  &::placeholder {\n    color: #999;\n    opacity: 1;\n  }\n`;\n\nexport const textEditorClassname = `rdg-text-editor ${textEditor}`;\n\nfunction autoFocusAndSelect(input: HTMLInputElement | null) {\n  input?.focus();\n  input?.select();\n}\n\nexport default function TextEditor<TRow, TSummaryRow>({\n  row,\n  column,\n  onRowChange,\n  onClose\n}: EditorProps<TRow, TSummaryRow>) {\n  return (\n    <input\n      className={textEditorClassname}\n      ref={autoFocusAndSelect}\n      value={row[column.key as keyof TRow] as unknown as string}\n      onChange={(event) => onRowChange({ ...row, [column.key]: event.target.value })}\n      onBlur={() => onClose(true)}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,EAAE,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE;AAAG,MAAG,YAAU,OAAO,KAAG,YAAU,OAAO,EAAE,MAAG;AAAA,WAAU,YAAU,OAAO,EAAE,KAAG,MAAM,QAAQ,CAAC,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,MAAI,IAAE,EAAE,EAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAQ,MAAI,KAAK,EAAE,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAQ,SAAS,OAAM;AAAC,WAAQ,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,SAAQ,EAAC,IAAE,UAAU,GAAG,OAAK,IAAE,EAAE,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAC,IAAO,iBAAQ;;;;ACAjX,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAK,QAAQ,OAAS,OAAM,CAAA;AAC5B,MAAI,WAAW,IAAI;AAEnB,MAAI,CAAC,OAAO,OAAO,aAAa,aAAa;AAAE;EAAO;AAEtD,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AAEb,MAAI,aAAa,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,OAAO,KAAK,UAAU;IAC9C,OAAW;AACL,WAAK,YAAY,KAAK;IAC5B;EACA,OAAS;AACL,SAAK,YAAY,KAAK;EAC1B;AAEE,MAAI,MAAM,YAAY;AACpB,UAAM,WAAW,UAAU;EAC/B,OAAS;AACL,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;EAClD;AACA;;;ACvBO,IAAMA,OAAN;AA6BA,IAAMC,gBAAa,YAAeD,IAAK;AAGvC,IAAME,0BAAN;AAMA,IAAMC,aAAN;AAMA,IAAMC,sBAAuB,mBAAkBD,UAA/C;AAEA,IAAME,iBAAN;AAIA,IAAMC,0BAA2B,wBAAuBD,cAAxD;;;ACjBP,IAAME,OAAN;AA0DO,IAAMC,gBAAiB,OAAMD,IAA7B;AAEP,IAAME,mBAAN;AAMO,IAAMC,4BAA6B,yBAAwBD,gBAA3D;AAEA,IAAME,qBAAN;;;ACrGA,IAAMC,MAAN;AAkBA,IAAMC,eAAgB,WAAUD,GAAhC;AAEA,IAAME,cAAN;AAKA,IAAMC,uBAAN;AAEA,IAAMC,4BAAN;;;ACvBP,IAAMC,gBAAN;AAUA,IAAMC,yBAA+CD,sBAAAA,aAArD;AAEA,IAAME,gBAAN;AAIA,IAAMC,yBAA+CD,sBAAAA,aAArD;AAEA,IAAME,WAAN;AAgBA,IAAMC,oBAAoCD,gBAAAA,QAA1C;AAEA,IAAME,wBAAN;AAQA,IAAMC,iCAAgED,+BAAAA,qBAAtE;IAEaE,wBAAoBC,yBAC/B,SAASD,mBAAkB;EAAEE;EAAU,GAAGC;AAAf,GAAgDC,KAAK;AAC9E,WAASC,aAAaC,GAAwC;AAC5DJ,aAASI,EAAEC,OAAOC,SAAUF,EAAEG,YAA2BC,QAAjD;EACT;AAED,aACEC,yBAAA,SAAA;IACE,WAAWC,eAAKnB,wBACoBU,MAAMU,YAAvCd,8BADY;IADjB,UAKE,KAAAe,wBAAA,SAAA;MACE,MAAK;MACL;MAFF,GAGMX;MACJ,WAAWR;MACX,UAAUU;IALZ,CAAA,OAOAS,wBAAA,OAAA;MAAK,WAAWjB;KAZlB,CAAA;GADF;AANuC,CAAA;AC/CpC,IAAMkB,kBAAkB,OAAOC,WAAW,cAAcC,yBAAYC,aAAAA;ACFpE,SAASC,YAAwCC,YAAqB;AAC3E,QAAMhB,UAAMiB,qBAAU,IAAJ;AAElBN,kBAAgB,MAAM;;AACpB,QAAI,CAACK,WAAY;AACjBhB,cAAIkB,YAAJlB,mBAAamB,MAAM;MAAEC,eAAe;;EACrC,GAAE,CAACJ,UAAD,CAHY;AAKf,SAAO;IACLhB;IACAqB,UAAUL,aAAa,IAAI;;AAE9B;ACVD,IAAMM,uCAAmCC,4BAA2CC,MAA9B;AAEzCC,IAAAA,oCAAoCH,iCAAiCI;AAE3E,SAASC,uBAAwD;AACtE,aAAOC,yBAAWN,gCAAD;AAClB;ACCM,SAASO,oBAAoB;EAClCC;EACAC;EACAtB;EACAX;EACA,cAAckC;EACd,mBAAmBC;AANe,GAOP;AAC3B,QAAM;IAAEjC;IAAKqB;MAAaN,YAA8BgB,cAAnB;AACrC,QAAMG,YAAYP,qBAAoB,EAAIQ;AAE1C,aACEzB,wBAAC,WAAD;IACE,cAAYsB;IACZ,mBAAiBC;IACjB;IACA;IACA;IACA,SAASH;IACT;GARJ;AAWD;AChCM,SAASM,eAAsBrC,OAA8B;AAClE,MAAI;AACF,eAAOW,wBAAA2B,6BAAA;MAAA,UAAGtC,MAAMhB,IAAIgB,MAAMuC,OAAOC,GAAvB;KAAV;EACD,QAAO;AACN,WAAO;EACR;AACF;;;ACJD,IAAMC,mBAAN;AAIA,IAAMC,4BAAsDD,0BAAAA,gBAA5D;AAEA,IAAME,QAAN;AAYA,IAAMC,iBAA8BD,aAAAA,KAApC;AAEO,SAASE,qBAA4B;EAC1CC;EACAC;EACAf;EACAgB;AAJ0C,GAKb;AAC7B,QAAM;IAAE/C;IAAKqB;MAAaN,YAA6BgB,cAAlB;AAErC,WAASiB,cAAc;IAAET;EAAF,GAA+C;AACpE,QAAIA,QAAQ,SAAS;AACnBQ,kBAAW;IACZ;EACF;AAED,QAAME,IAAIH,aAAa,sBAAsB;AAE7C,aACEvC,yBAAA,QAAA;IACE;IACA,WAAWkC;IACX;IACA,WAAWO;IAJb,UAAA,CAMGH,cACDnC,wBAAA,OAAA;MAAK,SAAQ;MAAW,OAAM;MAAK,QAAO;MAAI,WAAWiC;MAAgB,eAAzE;MAAA,cACEjC,wBAAA,QAAA;QAAM;MAAN,CAAA;KARJ,CAAA;GADF;AAaD;AClDD,IAAMwC,0BAAsB3B,4BAAmCC,MAAtB;AAElC,IAAM2B,uBAAuBD,oBAAoBxB;AAExD,IAAM0B,gCAA4B7B,4BAGhCC,MAH6C;AAKxC,IAAM6B,6BAA6BD,0BAA0B1B;AAE7D,SAAS4B,kBAA6E;AAC3F,QAAMC,0BAAsB3B,yBAAWsB,mBAAD;AACtC,QAAMM,gCAA4B5B,yBAAWwB,yBAAD;AAE5C,MAAIG,wBAAwB/B,UAAagC,8BAA8BhC,QAAW;AAChF,UAAM,IAAIiC,MAAM,oDAAV;EACP;AAED,SAAO,CAACF,qBAAqBC,yBAAtB;AACR;ACnBM,IAAME,oBAAoB;AAEjC,SAASC,gBAAgB5D,OAAgC;AACvD,QAAM,CAAC6D,eAAeC,oBAAhB,IAAwCP,gBAAe;AAE7D,aACE5C,wBAAC,qBAAD;IACE,cAAW;IACX,gBAAgBX,MAAMgC;IACtB,OAAO6B;IACP,UAAU,CAACxD,SAAS0D,iBAAiB;AACnCD,2BAAqB;QAAE9E,KAAKgB,MAAMhB;QAAKqB;QAAS0D;MAA3B,CAAD;IACrB;GAPL;AAUD;AAED,SAASC,qBAAqBhE,OAAqC;AACjE,QAAM,CAAC6D,eAAeC,oBAAhB,IAAwCP,gBAAe;AAE7D,aACE5C,wBAAC,qBAAD;IACE,cAAW;IACX,gBAAgBX,MAAMgC;IACtB,OAAO6B;IACP,UAAWxD,aAAY;AACrByD,2BAAqB;QAAE9E,KAAKgB,MAAMhB;QAAKqB;QAAS0D,cAAc;MAAzC,CAAD;IACrB;GAPL;AAUD;AAGM,IAAME,eAAiC;EAC5CzB,KAAKmB;EACLO,MAAM;EACNC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,WAAW;EACXC,UAAU;EACVC,QAAQ;EACRC,eAAezE,OAAO;AACpB,eACEW,wBAAC,qBAAD;MACE,cAAW;MACX,gBAAgBX,MAAMgC;MACtB,OAAOhC,MAAM0E;MACb,UAAU1E,MAAM2E;KALpB;;EASFC,WAAWhB;EACXiB,gBAAgBb;AApB4B;ACnCvC,SAASc,WACdvC,QACAwC,uBACAC,MACoB;AACpB,QAAMC,UAAU,OAAO1C,OAAO0C,YAAY,aAAa1C,OAAO0C,QAAQD,IAAf,IAAuB;AAC9E,MACEE,OAAOC,UAAUF,OAAjB,KACAA,UAAW,MAEV,CAAC1C,OAAOiC,UAAUjC,OAAO6C,MAAMH,UAAW,KAAKF,wBAChD;AACA,WAAOE;EACR;AACD,SAAOxD;AACR;ACXM,SAAS4D,eAAeC,SAAgC;AAC7DA,qCAASD,eAAe;IAAEE,QAAQ;IAAWC,OAAO;;AACrD;ACPD,IAAMC,eAAe,oBAAIC,IAAI,CAE3B,gBAEA,OACA,YACA,YACA,WACA,MACA,UACA,QACA,WACA,cACA,SAEA,OAEA,aACA,aACA,cACA,WACA,OACA,QACA,YACA,UAEA,UAEA,eACA,UACA,SACA,QAEA,eAEA,MAEA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,KA9C2B,CAAR;AAiDd,SAASC,kBAAkBxF,GAAiC;AACjE,UAAQA,EAAEyF,WAAWzF,EAAE0F,YAAY1F,EAAEqC,QAAQ;AAC9C;AAEM,SAASsD,mBAAmBC,OAAqD;AACtF,SAAO,CAACN,aAAaO,IAAID,MAAMvD,GAAvB;AACT;AAQM,SAASyD,mBAAmB;EAAEzD;EAAKpC;AAAP,GAA+D;AAChG,MACEoC,QAAQ,UACPpC,kBAAkB8F,oBACjB9F,kBAAkB+F,uBAClB/F,kBAAkBgG,oBACpB;AACA,WAAOhG,OAAOiG,QACZ,6FADK;EAGR;AACD,SAAO;AACR;AClEM,SAASC,uBAA8B;EAC5CC;EACAC;EACAC;EACAC;AAJ4C,GAKC;AAC7C,QAAMnE,SAASiE,QAAQD,iBAAiBnB,GAAlB;AACtB,QAAMpG,OAAMyH,KAAKF,iBAAiBI,MAAlB;AAChB,SAAO,CAACD,WAAW1H,IAAD,KAAS4H,eAAerE,QAAQvD,IAAT;AAC1C;AAEM,SAAS4H,eAAsBrE,QAAiCvD,MAAiB;AACtF,SACEuD,OAAOsE,UAAU,QACjB,CAACtE,OAAOuE,aACP,OAAOvE,OAAOwE,aAAa,aAAaxE,OAAOwE,SAAS/H,IAAhB,IAAuBuD,OAAOwE,cAAc;AAExF;AAiBM,SAASC,uBAA8B;EAC5CP;EACAQ;EACAN;EACA5B;EACAxC;EACAmE;AAN4C,GAa3C;AACD,MAAIC,WAAW,IAAI;AACjB,WAAO7B,WAAWvC,QAAQwC,uBAAuB;MAAEmC,MAAM;IAAR,CAAhC;EAClB;AAED,MAAIP,UAAU,KAAKA,SAASF,KAAKU,QAAQ;AACvC,UAAMnI,OAAMyH,KAAKE,MAAD;AAChB,QAAI,CAACD,WAAW1H,IAAD,GAAO;AACpB,aAAO8F,WAAWvC,QAAQwC,uBAAuB;QAAEmC,MAAM;QAAOlI,KAAAA;MAAf,CAAhC;IAClB;AACD,WAAOyC;EACR;AAED,MAAIwF,aAAa;AACf,WAAOnC,WAAWvC,QAAQwC,uBAAuB;MAC/CmC,MAAM;MACNlI,KAAKiI,YAAYN,SAASF,KAAKU,MAAf;IAF+B,CAAhC;EAIlB;AAED,SAAO1F;AACR;AAEM,SAAS2F,4BAAmC;EACjDC;EACAb;EACAc;EACAb;EACAQ;EACAM;EACAC;EACAC,iBAAiB;IAAErC,KAAKsC;;EACxBC;EACA5C;EACA6C;EACAlB;AAZiD,GAaE;AACnD,MAAI;IAAEtB,KAAKyC;IAASlB,QAAQmB;EAAxB,IAAuCH;AAE3C,QAAMI,aAAcC,eAAuB;AACzC,QAAIF,cAAc,KAAKA,aAAarB,KAAKU,QAAQ;AAC/C,YAAMnI,OAAMyH,KAAKqB,UAAD;AAChB,UAAIpB,WAAW1H,IAAD,EAAO;IACtB;AAGD,eAAWuD,UAAU+E,gBAAgB;AACnC,YAAMW,SAAS1F,OAAO6C;AACtB,UAAI6C,SAASJ,QAAS;AACtB,YAAM5C,UAAU+B,uBAAuB;QACrCP;QACAQ;QACAN,QAAQmB;QACR/C;QACAxC;QACAmE;MANqC,CAAD;AAStC,UAAIzB,WAAW4C,UAAUI,UAAUJ,UAAU5C,UAAUgD,QAAQ;AAC7DJ,kBAAUI,UAAUD,YAAY/C,UAAU;AAC1C;MACD;IACF;;AAGH,MAAI2C,mBAAmBD,YAAD,GAAgB;AACpCI,eAAWF,UAAUH,aAAa,CAAxB;EACX;AAED,MAAIL,uBAAuB,QAAQ;AACjC,UAAMa,eAAe1B,QAAQW;AAC7B,UAAMgB,oBAAoBN,YAAYK;AACtC,UAAME,sBAAsBP,YAAY;AAExC,QAAIM,mBAAmB;AACrB,UAAId,uBAAuB,cAAc;AACvC,cAAMgB,YAAYP,eAAeN;AACjC,YAAI,CAACa,WAAW;AACdR,oBAAU;AACVC,wBAAc;QACf;MACF,OAAM;AACLD,kBAAU;MACX;eACQO,qBAAqB;AAC9B,UAAIf,uBAAuB,cAAc;AACvC,cAAMiB,aAAaR,eAAeP;AAClC,YAAI,CAACe,YAAY;AACfR,wBAAc;AACdD,oBAAUK,eAAe;QAC1B;MACF,OAAM;AACLL,kBAAUK,eAAe;MAC1B;AACDH,iBAAW,KAAD;IACX;EACF;AAED,SAAO;IAAE3C,KAAKyC;IAASlB,QAAQmB;;AAChC;AAWM,SAASS,YAAY;EAC1BlB;EACAmB;EACAjB;EACAC;EACAjB,kBAAkB;IAAEI;IAAQvB;;EAC5B7E;AAN0B,GAOC;AAG3B,MAAI8G,uBAAuB,UAAUA,uBAAuB,cAAc;AACxE,UAAMoB,kBAAkBrD,QAAQoD;AAChC,UAAME,mBAAmBtD,QAAQ;AACjC,UAAMuD,YAAYhC,WAAWa;AAC7B,UAAMoB,aAAajC,WAAWY;AAE9B,WAAOhH,WAAWmI,oBAAoBE,aAAaH,mBAAmBE;EACvE;AAED,SAAO;AACR;ACrLM,SAASE,YAAYlC,QAAgBmC,QAAgC;AAC1E,MAAIA,WAAWrH,QAAW;AACxB,WAAO;MACL,wBAAwBkF;MACxB,oBAAqB,GAAEmC,MAAO;;EAEjC;AACD,SAAO;IAAE,wBAAwBnC;;AAClC;AAEM,SAASoC,aACdxG,QACA0C,SACqB;AACrB,SAAO;IACL+D,iBAAiBzG,OAAO6C,MAAM;IAC9B6D,eAAehE,YAAYxD,SAAa,QAAOwD,OAAQ,KAAIxD;IAC3DyH,kBAAkB3G,OAAOiC,SAAU,yBAAwBjC,OAAO6C,GAAI,MAAK3D;;AAE9E;AAEM,SAAS0H,iBACd5G,WACG6G,cACK;AACR,SAAO3I,eACLpC,eAKA,GAAG+K,cAHsB7G,OAAOiC,UAA7BhG,qBAC0B+D,OAAO8G,sBAAjC3K,uBAJM;AAQZ;AC/BM,IAAM;EAAE4K;EAAKC;EAAKC;EAAOC;EAAOC;EAAMC;EAAKC;AAArC,IAA8CC;AAEpD,SAASC,uBACdC,WACoC;AACpC,MAAI,OAAOA,cAAc,YAAY;AACnC,UAAM,IAAIrG,MAAM,uDAAV;EACP;AACF;AAEM,SAASsG,iBACd7F,OACA;EAAEC;EAAUC;AAAZ,GACQ;AACRF,UAAQoF,IAAIpF,OAAOC,QAAR;AAGX,MAAI,OAAOC,aAAa,YAAYA,YAAYD,UAAU;AACxD,WAAOkF,IAAInF,OAAOE,QAAR;EACX;AAED,SAAOF;AACR;ACJM,SAAS8F,qBAA4B;EAC1CC;EACAC;EACAC;EACAC;EACAC;EACAC;EACAC;AAP0C,GAQX;AAC/B,QAAMC,eAAeH,6DAAsBnG;AAC3C,QAAMuG,mBAAkBJ,6DAAsBlG,aAAY;AAC1D,QAAMuG,kBAAkBL,6DAAsBjG;AAC9C,QAAMuG,oBAAmBN,6DAAsB1F,cAAavC;AAC5D,QAAMwI,mBAAkBP,6DAAsB/F,aAAY;AAC1D,QAAMuG,oBAAmBR,6DAAsBhG,cAAa;AAE5D,QAAM;IAAEkC;IAASc;IAAgBvC;IAAuBgG;UAAYC,sBAAQ,MAKvE;AAEH,UAAMD,WAAoB,CAAA;AAC1B,QAAIhG,yBAAwB;AAE5B,UAAMyB,WAAU0D,WAAWe,IAAKC,eAAc;AAC5C,YAAMpE,YAAWyD,yCAAYY,SAASD,UAAU1I,SAAQ;AAExD,YAAMgC,SAASsC,YAAYoE,UAAU1G,UAAU;AAE/C,YAAMjC,SAA2C;QAC/C,GAAG2I;QACH9F,KAAK;QACLZ;QACA6E,oBAAoB;QACpBvC;QACA3C,OAAO+G,UAAU/G,SAASsG;QAC1BrG,UAAU8G,UAAU9G,YAAYsG;QAChCrG,UAAU6G,UAAU7G,YAAYsG;QAChCpG,UAAU2G,UAAU3G,YAAYsG;QAChCvG,WAAW4G,UAAU5G,aAAawG;QAClClG,WAAWsG,UAAUtG,aAAagG;;AAGpC,UAAI9D,UAAU;AACZvE,eAAOsC,mBAAPtC,OAAOsC,iBAAmBhC;MAC3B;AAED,UAAI2B,QAAQ;AACVO,QAAAA;MACD;AAED,aAAOxC;IACR,CA5Be;AA8BhBiE,IAAAA,SAAQ4E,KAAK,CAAC;MAAE5I,KAAK6I;MAAM7G,QAAQ8G;IAArB,GAAgC;MAAE9I,KAAK+I;MAAM/G,QAAQgH;IAArB,MAAmC;AAE/E,UAAIH,SAAS1H,kBAAmB,QAAO;AACvC,UAAI4H,SAAS5H,kBAAmB,QAAO;AAGvC,UAAI4G,yCAAYY,SAASE,OAAO;AAC9B,YAAId,WAAWY,SAASI,IAApB,GAA2B;AAC7B,iBAAOhB,WAAWkB,QAAQJ,IAAnB,IAA2Bd,WAAWkB,QAAQF,IAAnB;QACnC;AACD,eAAO;MACR;AACD,UAAIhB,yCAAYY,SAASI,MAAO,QAAO;AAGvC,UAAID,SAAS;AACX,YAAIE,QAAS,QAAO;AACpB,eAAO;MACR;AACD,UAAIA,QAAS,QAAO;AAGpB,aAAO;KAtBT;AAyBA,UAAMlE,kBAA4C,CAAA;AAClDd,IAAAA,SAAQkF,QAAQ,CAACnJ,QAAQ6C,QAAQ;AAC/B7C,aAAO6C,MAAMA;AAEb,UAAI7C,OAAOuE,UAAU;AACnBiE,QAAAA,SAAQY,KAAKpJ,OAAOC,GAApB;MACD;AAED,UAAID,OAAO0C,WAAW,MAAM;AAC1BqC,QAAAA,gBAAeqE,KAAKpJ,MAApB;MACD;KATH;AAYA,QAAIwC,2BAA0B,IAAI;AAChCyB,MAAAA,SAAQzB,sBAAD,EAAwBsE,qBAAqB;IACrD;AAED,WAAO;MACL7C,SAAAA;MACAc,gBAAAA;MACAvC,uBAAAA;MACAgG,SAAAA;;EAEH,GAAE,CACDb,YACAO,cACAC,iBACAC,iBACAC,kBACAE,kBACAD,iBACAN,UARC,CAxFwE;AAmG3E,QAAM;IAAEqB;IAAeC;IAAwBC;UAAkBd,sBAAQ,MAIpE;AACH,UAAMc,iBAAgB,oBAAIC,IAAJ;AACtB,QAAIC,OAAO;AACX,QAAIH,0BAAyB;AAC7B,QAAII,kBAAkB;AACtB,QAAIC,iBAAiB;AACrB,QAAIC,yBAAyB;AAE7B,eAAW5J,UAAUiE,SAAS;AAC5B,UAAIrC,QAAQiI,kBAAkB7J,QAAQ4H,cAAcC,aAAvB;AAE7B,UAAIjG,UAAU1C,QAAW;AACvB0K;MACD,OAAM;AACLhI,gBAAQ6F,iBAAiB7F,OAAO5B,MAAR;AACxB2J,0BAAkB/H;AAClB2H,QAAAA,eAAcO,IAAI9J,QAAQ;UAAE4B;UAAO6H,MAAM;SAAzC;MACD;IACF;AAED,eAAWzJ,UAAUiE,SAAS;AAC5B,UAAIrC;AACJ,UAAI2H,eAAc9F,IAAIzD,MAAlB,GAA2B;AAC7B,cAAM+J,eAAeR,eAAcS,IAAIhK,MAAlB;AACrB+J,qBAAaN,OAAOA;AACpB,SAAC;UAAE7H;QAAF,IAAYmI;MACd,OAAM;AAEL,cAAME,mBAAmBpC,gBAAgB8B;AACzC,cAAMO,yBAAyBjD,MAAMgD,mBAAmBL,sBAApB;AACpChI,gBAAQ6F,iBAAiByC,wBAAwBlK,MAAzB;AACxB2J,0BAAkB/H;AAClBgI;AACAL,QAAAA,eAAcO,IAAI9J,QAAQ;UAAE4B;UAAO6H;SAAnC;MACD;AACDA,cAAQ7H;AACR8H,yBAAoB,GAAE9H,KAAM;IAC7B;AAED,QAAIY,0BAA0B,IAAI;AAChC,YAAMuH,eAAeR,eAAcS,IAAI/F,QAAQzB,qBAAD,CAAzB;AACrB8G,MAAAA,0BAAyBS,aAAaN,OAAOM,aAAanI;IAC3D;AAED,UAAMyH,iBAAwC;MAC5Cc,qBAAqBT;;AAGvB,aAASU,IAAI,GAAGA,KAAK5H,uBAAuB4H,KAAK;AAC/C,YAAMpK,SAASiE,QAAQmG,CAAD;AACtBf,MAAAA,eAAe,qBAAoBrJ,OAAO6C,GAAI,EAAjC,IAAuC,GAAE0G,eAAcS,IAAIhK,MAAlB,EAA2ByJ,IAAK;IACvF;AAED,WAAO;MAAEJ,eAAAA;MAAeC,wBAAAA;MAAwBC,eAAAA;;KAC/C,CAAC3B,cAAc3D,SAAS4D,eAAerF,qBAAvC,CA1DqE;AA4DxE,QAAM,CAAC6H,qBAAqBC,iBAAtB,QAA2C7B,sBAAQ,MAAwB;AAC/E,QAAI,CAACR,sBAAsB;AACzB,aAAO,CAAC,GAAGhE,QAAQW,SAAS,CAArB;IACR;AAED,UAAM2F,eAAezC,aAAawB;AAClC,UAAMkB,gBAAgB1C,aAAaD;AAEnC,UAAM4C,aAAaxG,QAAQW,SAAS;AACpC,UAAM8F,yBAAyB3D,IAAIvE,wBAAwB,GAAGiI,UAA5B;AAGlC,QAAIF,gBAAgBC,eAAe;AACjC,aAAO,CAACE,wBAAwBA,sBAAzB;IACR;AAGD,QAAIC,qBAAqBD;AACzB,WAAOC,qBAAqBF,YAAY;AACtC,YAAM;QAAEhB;QAAM7H;UAAU2H,cAAcS,IAAI/F,QAAQ0G,kBAAD,CAAzB;AAGxB,UAAIlB,OAAO7H,QAAQ2I,cAAc;AAC/B;MACD;AACDI;IACD;AAGD,QAAIC,mBAAmBD;AACvB,WAAOC,mBAAmBH,YAAY;AACpC,YAAM;QAAEhB;QAAM7H;UAAU2H,cAAcS,IAAI/F,QAAQ2G,gBAAD,CAAzB;AAGxB,UAAInB,OAAO7H,SAAS4I,eAAe;AACjC;MACD;AACDI;IACD;AAED,UAAMP,uBAAsBrD,IAAI0D,wBAAwBC,qBAAqB,CAA9C;AAC/B,UAAML,qBAAoBvD,IAAI0D,YAAYG,mBAAmB,CAAhC;AAE7B,WAAO,CAACP,sBAAqBC,kBAAtB;EACR,GAAE,CACDf,eACAtF,SACAzB,uBACAsF,YACAwB,wBACAzB,eACAI,oBAPC,CA5CqD;AAsDxD,SAAO;IACLhE;IACAc;IACAsF;IACAC;IACAjB;IACAE;IACA/G;IACA8G;IACAd;;AAEH;AAED,SAASqB,kBACP;EAAE5J;EAAK2B;AAAP,GACAgG,cACAC,eACoB;AACpB,MAAID,aAAanE,IAAIxD,GAAjB,GAAuB;AAEzB,WAAO2H,aAAaoC,IAAI/J,GAAjB;EACR;AAED,MAAI,OAAO2B,UAAU,UAAU;AAC7B,WAAOA;EACR;AACD,MAAI,OAAOA,UAAU,YAAY,SAASiJ,KAAKjJ,KAAd,GAAsB;AACrD,WAAOsF,MAAOW,gBAAgBiD,SAASlJ,OAAO,EAAR,IAAe,GAAzC;EACb;AACD,SAAO1C;AACR;ACxRM,SAAS6L,oBAId;AACA,QAAMC,cAAUrM,qBAAuB,IAAjB;AACtB,QAAM,CAACsM,YAAYC,aAAb,QAA8BC,uBAAS,CAAD;AAC5C,QAAM,CAACC,WAAWC,YAAZ,QAA4BF,uBAAS,CAAD;AAE1C9M,kBAAgB,MAAM;AACpB,UAAM;MAAEiN;IAAF,IAAqBhN;AAI3B,QAAIgN,kBAAkB,KAAM;AAE5B,UAAM;MAAEC;MAAaC;MAAcC;MAAaC;QAAiBV,QAAQpM;AACzE,UAAM;MAAEgD;MAAO2E;IAAT,IAAoByE,QAAQpM,QAAS+M,sBAAjB;AAC1B,UAAMC,eAAehK,QAAQ6J,cAAcF;AAC3C,UAAMM,gBAAgBtF,SAASmF,eAAeF;AAE9CN,kBAAcY,uBAAuBF,YAAD,CAAvB;AACbP,iBAAaQ,aAAD;AAEZ,UAAME,iBAAiB,IAAIT,eAAgBU,aAAY;AACrD,YAAMC,OAAOD,QAAQ,CAAD,EAAIE,eAAe,CAA1B;AACbhB,oBAAcY,uBAAuBG,KAAKhB,UAAN,CAAvB;AACbI,mBAAaY,KAAKb,SAAN;IACb,CAJsB;AAKvBW,mBAAeI,QAAQnB,QAAQpM,OAA/B;AAEA,WAAO,MAAM;AACXmN,qBAAeK,WAAf;;KAED,CAAA,CAzBY;AA2Bf,SAAO,CAACpB,SAASC,YAAYG,SAAtB;AACR;AAKD,SAASU,uBAAuBG,MAAc;AAC5C,SAAOA,QAAQI,qBAAqB,IAAI,IAAIhF,KAAKgF,gBAAD;AACjD;AC7CM,SAASC,cAAiDC,IAAO;AACtE,QAAM7O,UAAMiB,qBAAO4N,EAAD;AAElBhO,8BAAU,MAAM;AACdb,QAAIkB,UAAU2N;EACf,CAFQ;AAIT,aAAOC,0BAAY,IAAI/J,SAAwB;AAC7C/E,QAAIkB,QAAQ,GAAG6D,IAAf;KACC,CAAA,CAFe;AAGnB;ACVM,SAASgK,iBAAiB/N,YAAqB;AAEpD,QAAM,CAACgO,gBAAgBC,iBAAjB,QAAsCxB,uBAAS,KAAD;AAEpD,MAAIuB,kBAAkB,CAAChO,YAAY;AACjCiO,sBAAkB,KAAD;EAClB;AAED,QAAMjP,UAAM8O,0BAAa3Q,CAAAA,UAAgC;AACvD,QAAIA,UAAS,KAAM;AACnBiH,mBAAejH,KAAD;AACd,QAAIA,MAAK+Q,SAASC,SAASC,aAAvB,EAAuC;AAC3CjR,IAAAA,MAAKgD,MAAM;MAAEC,eAAe;KAA5B;KACC,CAAA,CALoB;AAOvB,WAASiO,QAAQvJ,OAAyC;AACxD,QAAIA,MAAM3F,WAAW2F,MAAMwJ,eAAe;AACxCL,wBAAkB,IAAD;IAClB;EACF;AAED,QAAMM,YAAYvO,cAAc,CAACgO;AAEjC,SAAO;IACLhP,KAAKgB,aAAahB,MAAMwB;IACxBH,UAAUkO,YAAY,IAAI;IAC1BF,SAASrO,aAAaqO,UAAU7N;;AAEnC;ACdM,SAASgO,mBAA0B;EACxCjJ;EACAc;EACAb;EACAQ;EACA2F;EACAC;EACA9H;EACA2K;EACAC;EACAjJ;AAVwC,GAWX;AAE7B,QAAMkJ,eAAW5E,sBAAQ,MAAM;AAC7B,QAAI4B,wBAAwB,EAAG,QAAO;AAEtC,QAAIgD,YAAWhD;AAEf,UAAMiD,iBAAiB,CAAC5H,QAAgBhD,YAAgC;AACtE,UAAIA,YAAYxD,UAAawG,SAAShD,UAAU2H,qBAAqB;AACnEgD,QAAAA,YAAW3H;AACX,eAAO;MACR;AACD,aAAO;;AAGT,eAAW1F,UAAU+E,gBAAgB;AAEnC,YAAMW,SAAS1F,OAAO6C;AACtB,UAAI6C,UAAU2H,UAAU;AACxB,UAAIC,eAAe5H,QAAQnD,WAAWvC,QAAQwC,uBAAuB;QAAEmC,MAAM;OAAxC,CAAnB,GAAyE;AACzF;MACD;AAGD,eAASP,SAAS+I,qBAAqB/I,UAAUgJ,mBAAmBhJ,UAAU;AAC5E,cAAM3H,OAAMyH,KAAKE,MAAD;AAChB,YAAID,WAAW1H,IAAD,EAAO;AACrB,YACE6Q,eAAe5H,QAAQnD,WAAWvC,QAAQwC,uBAAuB;UAAEmC,MAAM;UAAOlI,KAAAA;SAA/C,CAAnB,GACd;AACA;QACD;MACF;AAGD,UAAIiI,eAAe,MAAM;AACvB,mBAAWjI,QAAOiI,aAAa;AAC7B,cACE4I,eACE5H,QACAnD,WAAWvC,QAAQwC,uBAAuB;YAAEmC,MAAM;YAAWlI,KAAAA;WAAnD,CAFE,GAId;AACA;UACD;QACF;MACF;IACF;AAED,WAAO4Q;EACR,GAAE,CACDF,qBACAC,mBACAlJ,MACAQ,aACA2F,qBACA7H,uBACAuC,gBACAZ,UARC,CAhDqB;AA2DxB,aAAOsE,sBAAQ,MAA0C;AACvD,UAAM8E,kBAA6C,CAAA;AACnD,aAAS7H,SAAS,GAAGA,UAAU4E,mBAAmB5E,UAAU;AAC1D,YAAM1F,SAASiE,QAAQyB,MAAD;AAEtB,UAAIA,SAAS2H,YAAY,CAACrN,OAAOiC,OAAQ;AACzCsL,sBAAgBnE,KAAKpJ,MAArB;IACD;AAED,WAAOuN;KACN,CAACF,UAAU/C,mBAAmBrG,OAA9B,CAVW;AAWf;AC5ED,SAASuJ,gBAAgBC,KAAyC;AAChE,SAAOC,MAAMC,QAAQF,GAAd;AACR;AAEM,SAASG,gBAAmB;EACjCC;EACAC;EACAtC;EACAuC;EACAvF;EACAwF;EACAC;EACAhG;AARiC,GASX;AACtB,QAAM,CAACiG,aAAaC,SAAd,QAA2B1F,sBAAQ,MAAM;AAC7C,QAAID,QAAQ5D,WAAW,KAAKoJ,cAAc,KAAM,QAAO,CAAC9O,QAAW2O,QAAQjJ,MAApB;AAEvD,UAAMwJ,YAAY,CAChBlK,OACA,CAACmK,YAAeC,uBAAhB,GACAC,kBAC6C;AAC7C,UAAIC,iBAAiB;AACrB,YAAMC,SAA+B,CAAA;AACrC,iBAAW,CAACxO,KAAKyO,SAAN,KAAoBC,OAAO3C,QAAQgC,WAAW9J,OAAMmK,UAAP,CAAzB,GAA8C;AAE3E,cAAM,CAACO,aAAaC,cAAd,IACJP,qBAAqB1J,WAAW,IAC5B,CAAC8J,WAAWA,UAAU9J,MAAtB,IACAwJ,UAAUM,WAAWJ,sBAAsBC,gBAAgBC,iBAAiB,CAAnE;AACfC,eAAOxO,GAAD,IAAQ;UAAEyO;UAAWE;UAAaL,eAAeA,gBAAgBC;;AACvEA,0BAAkBK,iBAAiB;MACpC;AAED,aAAO,CAACJ,QAAQD,cAAT;;AAGT,WAAOJ,UAAUP,SAASrF,SAAS,CAAnB;KACf,CAACA,SAASwF,YAAYH,OAAtB,CAxBqC;AA0BxC,QAAM,CAAC3J,MAAMC,UAAP,QAAqBsE,sBAAQ,MAG9B;AACH,UAAMqG,eAAe,oBAAI3L,IAAJ;AACrB,QAAI,CAAC+K,YAAa,QAAO,CAACL,SAAS1J,WAAV;AAEzB,UAAM4K,gBAAwC,CAAA;AAC9C,UAAMC,cAAc,CAClB9K,OACA+K,UACAC,UACS;AACT,UAAI1B,gBAAgBtJ,KAAD,GAAQ;AACzB6K,sBAAc3F,KAAK,GAAGlF,KAAtB;AACA;MACD;AACDyK,aAAOQ,KAAKjL,KAAZ,EAAkBiF,QAAQ,CAAC5I,UAAU6O,UAAUD,SAAS;AAEtD,cAAME,KAAKJ,aAAa/P,SAAa,GAAE+P,QAAS,KAAI1O,QAAS,KAAIA;AACjE,cAAMC,cAAayN,qDAAkBxK,IAAI4L,QAAO;AAChD,cAAM;UAAEX;UAAWE;UAAaL;YAAkBrK,MAAK3D,QAAD;AAEtD,cAAM+O,YAAwB;UAC5BD;UACAJ;UACA1O;UACAC;UACAkO;UACAQ;UACAE;UACAb;UACAgB,SAASJ,KAAKvK;;AAEhBmK,sBAAc3F,KAAKkG,SAAnB;AACAR,qBAAaU,IAAIF,SAAjB;AAEA,YAAI9O,YAAY;AACdwO,sBAAYJ,aAAaS,IAAIH,QAAQ,CAA1B;QACZ;OAtBH;;AA0BFF,gBAAYd,aAAahP,QAAW,CAAzB;AACX,WAAO,CAAC6P,eAAe5K,WAAhB;AAEP,aAASA,YAAW1H,MAA0C;AAC5D,aAAOqS,aAAarL,IAAIhH,IAAjB;IACR;KACA,CAACwR,kBAAkBC,aAAaL,OAAhC,CAjD+B;AAmDlC,QAAM;IAAE4B;IAAgBC;IAAkBC;IAAWC;IAAcC;UAAepH,sBAAQ,MAAM;AAC9F,QAAI,OAAOqF,cAAc,UAAU;AACjC,aAAO;QACL2B,gBAAgB3B,YAAY5J,KAAKU;QACjC8K,kBAAmB,WAAUxL,KAAKU,MAAO,KAAIkJ,SAAU;QACvD6B,WAAYvL,YAAmBA,SAAS0J;QACxC8B,cAAc,MAAM9B;QACpB+B,YAAaC,YAAmB5I,MAAM4I,SAAShC,SAAV;;IAExC;AAED,QAAI2B,kBAAiB;AACrB,QAAIC,oBAAmB;AAIvB,UAAMK,eAAe7L,KAAKwE,IAAKjM,CAAAA,SAAyB;AACtD,YAAMuT,mBAAmB7L,WAAW1H,IAAD,IAC/BqR,UAAU;QAAEnJ,MAAM;QAASlI,KAAAA;OAAlB,IACTqR,UAAU;QAAEnJ,MAAM;QAAOlI,KAAAA;MAAf,CAAD;AACb,YAAMwT,WAAW;QAAEC,KAAKT;QAAgBlJ,QAAQyJ;;AAChDN,MAAAA,qBAAqB,GAAEM,gBAAiB;AACxCP,MAAAA,mBAAkBO;AAClB,aAAOC;IACR,CARoB;AAUrB,UAAME,iBAAkB/L,YAAmB;AACzC,aAAO4C,IAAI,GAAGD,IAAI7C,KAAKU,SAAS,GAAGR,MAAlB,CAAP;;AAGZ,WAAO;MACLqL,gBAAAA;MACAC,kBAAAA;MACAC,WAAYvL,YAAmB2L,aAAaI,eAAe/L,MAAD,CAAf,EAAyB8L;MACpEN,cAAexL,YAAmB2L,aAAaI,eAAe/L,MAAD,CAAf,EAAyBmC;MACvEsJ,WAAWC,QAAgB;AACzB,YAAIM,QAAQ;AACZ,YAAIC,MAAMN,aAAanL,SAAS;AAChC,eAAOwL,SAASC,KAAK;AACnB,gBAAMC,SAASF,QAAQlJ,OAAOmJ,MAAMD,SAAS,CAAjB;AAC5B,gBAAMG,gBAAgBR,aAAaO,MAAD,EAASJ;AAE3C,cAAIK,kBAAkBT,OAAQ,QAAOQ;AAErC,cAAIC,gBAAgBT,QAAQ;AAC1BM,oBAAQE,SAAS;UAClB,WAAUC,gBAAgBT,QAAQ;AACjCO,kBAAMC,SAAS;UAChB;AAED,cAAIF,QAAQC,IAAK,QAAOA;QACzB;AACD,eAAO;MACR;;KAEF,CAAClM,YAAY2J,WAAW5J,IAAxB,CAvDsF;AAyDzF,MAAIiJ,sBAAsB;AAC1B,MAAIC,oBAAoBlJ,KAAKU,SAAS;AAEtC,MAAIqD,sBAAsB;AACxB,UAAMuI,oBAAoB;AAC1B,UAAMC,qBAAqBZ,WAAW9B,SAAD;AACrC,UAAM2C,mBAAmBb,WAAW9B,YAAYvC,YAAb;AACnC2B,0BAAsBnG,IAAI,GAAGyJ,qBAAqBD,iBAAzB;AACzBpD,wBAAoBrG,IAAI7C,KAAKU,SAAS,GAAG8L,mBAAmBF,iBAArC;EACxB;AAED,SAAO;IACLrD;IACAC;IACAlJ;IACAiK;IACAsB;IACAC;IACAvL;IACAwL;IACAC;IACAC;;AAEH;;;AC9LD,IAAMc,iBAAN;AASA,IAAMC,0BAAkDD,wBAAAA,cAAxD;AAEA,IAAME,iBAAN;AAOA,IAAMC,0BAAkDD,wBAAAA,cAAxD;AAEe,SAASE,eAAsB;EAC5C/Q;EACAgR;EACAC;EACAC;EACAzR;AAL4C,GAMf;AAC7B,MAAI,CAACO,OAAOgC,SAAU,YAAO5D,wBAAA2B,6BAAA;IAAA,UAAGC,OAAO2B;GAAjB;AAEtB,aACEvD,wBAAC,oBAAD;IACE;IACA;IACA;IACA;IAJF,UAMG4B,OAAO2B;GAPZ;AAUD;AAWD,SAASwP,mBAA0B;EACjCD;EACAF;EACAC;EACAG;EACA3R;AALiC,GAMA;AACjC,QAAM4R,YAAWhS,qBAAoB,EAAWiS;AAChD,QAAM;IAAE5T;IAAKqB;MAAaN,YAA6BgB,cAAlB;AAErC,WAASiB,cAAc8C,OAA6C;AAClE,QAAIA,MAAMvD,QAAQ,OAAOuD,MAAMvD,QAAQ,SAAS;AAE9CuD,YAAM+N,eAAN/N;AACA0N,aAAO1N,MAAMH,WAAWG,MAAMF,OAAxB;IACP;EACF;AAED,WAASkO,YAAYhO,OAA0C;AAC7D0N,WAAO1N,MAAMH,WAAWG,MAAMF,OAAxB;EACP;AAED,aACErF,yBAAA,QAAA;IACE;IACA;IACA,WAAW2S;IACX,SAASY;IACT,WAAW9Q;IALb,UAOE,KAAAtC,wBAAA,QAAA;MAAM,WAAW0S;MAAjB;IAAA,CAAA,OACA7S,yBAAA,QAAA;MAAA,UAAA,KACEG,wBAACiT,WAAD;QAAU;MAAV,CAAA,GACCJ,QAFH;KARF,CAAA;GADF;AAeD;;;ACrFD,IAAMQ,gBAAN;AAcA,IAAMC,yBAA+CD,sBAAAA,aAArD;AAoBe,SAASE,WAAkB;EACxC3R;EACA0C;EACAjD;EACAmS;EACAzP;EACAC;EACAyP;EACAC;EACAC;EACAC;EACAC;AAXwC,GAYf;AACzB,QAAMC,QAAQD,cAAc;AAC5B,QAAM;IAAEvU;IAAKqB;IAAUgO;MAAYN,iBAAiBhN,cAAD;AACnD,QAAM0S,YAAYN,2CAAaO,UAAWvJ,UAASA,KAAKwJ,cAAcrS,OAAOC;AAC7E,QAAMqS,aACJH,cAAcjT,UAAaiT,YAAY,KAAKN,YAAaM,SAAF,IAAejT;AACxE,QAAM8R,gBAAgBsB,yCAAYL;AAClC,QAAMhB,WAAWqB,eAAepT,UAAa2S,YAAajN,SAAS,IAAIuN,YAAa,IAAIjT;AACxF,QAAMqT,WACJvB,iBAAiB,CAACC,WAAYD,kBAAkB,QAAQ,cAAc,eAAgB9R;AAExF,QAAMsT,YAAY5L,iBAAiB5G,QAAQA,OAAOyS,iBACtBzS,OAAO+B,aAAhC2P,sBAD+B;AAIlC,QAAMX,mBAAiB/Q,OAAOkC,kBAAkBwQ;AAEhD,WAASC,cAAcnP,OAA2C;AAChE,QAAIA,MAAMoP,gBAAgB,WAAWpP,MAAMqP,YAAY,GAAG;AACxD;IACD;AAED,UAAM;MAAE7F;MAAe8F;IAAjB,IAA+BtP;AACrC,UAAM;MAAEuP;MAAOtJ;QAASuD,cAAcrB,sBAAdqB;AACxB,UAAM8C,SAASoC,QAAQ1O,MAAMwP,UAAUvJ,OAAOsJ,QAAQvP,MAAMwP;AAE5D,QAAIlD,SAAS,IAAI;AAEf;IACD;AAED,aAASmD,cAAczP,QAAqB;AAC1C,YAAM;QAAEuP,OAAAA;QAAOtJ,MAAAA;UAASuD,cAAcrB,sBAAdqB;AACxB,YAAMpL,QAAQsQ,QAAQa,SAAQjD,SAAStM,OAAMwP,UAAUxP,OAAMwP,UAAUlD,SAASrG;AAChF,UAAI7H,QAAQ,GAAG;AACbgQ,uBAAe5R,QAAQyH,iBAAiB7F,OAAO5B,MAAR,CAAzB;MACf;IACF;AAED,aAASkT,uBAAuB;AAC9BlG,oBAAcmG,oBAAoB,eAAeF,aAAjDjG;AACAA,oBAAcmG,oBAAoB,sBAAsBD,oBAAxDlG;IACD;AAEDA,kBAAcoG,kBAAkBN,SAAhC9F;AACAA,kBAAcqG,iBAAiB,eAAeJ,aAA9CjG;AACAA,kBAAcqG,iBAAiB,sBAAsBH,oBAArDlG;EACD;AAED,WAASkE,OAAOoC,WAAoB;AAClC,QAAIxB,uBAAuB,KAAM;AACjC,UAAM;MAAEyB;IAAF,IAA0BvT;AAChC,QAAIsS,eAAepT,QAAW;AAE5B,YAAMsU,WAAuB;QAC3BnB,WAAWrS,OAAOC;QAClBgS,WAAWsB,sBAAsB,SAAS;;AAE5CzB,0BAAoBD,eAAeyB,YAAY,CAAC,GAAGzB,aAAa2B,QAAjB,IAA6B,CAACA,QAAD,CAAzD;IANrB,OAOO;AACL,UAAIC;AACJ,UACGF,uBAAuBvC,kBAAkB,UACzC,CAACuC,uBAAuBvC,kBAAkB,OAC3C;AACAyC,yBAAiB;UACfpB,WAAWrS,OAAOC;UAClBgS,WAAWjB,kBAAkB,QAAQ,SAAS;;MAEjD;AACD,UAAIsC,WAAW;AACb,cAAMI,kBAAkB,CAAC,GAAG7B,WAAJ;AACxB,YAAI4B,gBAAgB;AAElBC,0BAAgBvB,SAAD,IAAesB;QAFhC,OAGO;AAELC,0BAAgBC,OAAOxB,WAAY,CAAnCuB;QACD;AACD5B,4BAAoB4B,eAAD;MATrB,OAUO;AACL5B,4BAAoB2B,iBAAiB,CAACA,cAAD,IAAmB,CAAA,CAArC;MACpB;IACF;EACF;AAED,WAASG,UAAU;AACjB7B,eAAW/R,OAAO6C,GAAR;EACX;AAED,WAASgR,cAAcrQ,OAAyC;AAC9D,UAAM;MAAEuP;MAAOtJ;IAAT,IAAkBjG,MAAMwJ,cAAcrB,sBAApBnI;AACxB,UAAMsM,SAASoC,QAAQ1O,MAAMwP,UAAUvJ,OAAOsJ,QAAQvP,MAAMwP;AAE5D,QAAIlD,SAAS,IAAI;AAEf;IACD;AAED8B,mBAAe5R,QAAQ,MAAT;EACf;AAED,WAAS8T,YAAYtQ,OAAyC;AAC5DuJ,uCAAUvJ;AACV,QAAIwO,iBAAiB;AAEnBD,iBAAW,CAAD;IACX;EACF;AAED,aACE3T,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe4B,OAAO6C,MAAM;IAC5B,iBAAepD;IACf,aAAW8S;IACX,gBAAc7P;IACd;IAEA,UAAUsP,kBAAkB,IAAIjT;IAChC;IACA,OAAO;MACL,GAAGyH,aAAaxG,QAAQ0C,OAAT;MACfb,UAAU7B,OAAO6B;MACjBC,UAAU9B,OAAO8B,YAAY5C;;IAE/B,SAAS4U;IACT;IACA,eAAe9T,OAAO+B,YAAY8R,gBAAgB3U;IAClD,eAAec,OAAO+B,YAAY4Q,gBAAgBzT;IAlBpD,cAoBEd,wBAAC2S,kBAAD;MACE;MACA;MACA;MACA;MACA;MACA;MACA;IAPF,CAAA;GArBJ;AAgCD;;;ACzKD,IAAMgD,YAAN;AAkBA,IAAMC,qBAAuCD,kBAAAA,SAA7C;AAEA,SAASE,UAAsC;EAC7ChQ;EACA9B;EACAC;EACAwP;EACAC;EACAC;EACAtP;EACA0R;EACAnC;EACAC;EACAC;AAX6C,GAYlB;AAC3B,QAAMkC,QAAQ,CAAA;AACd,WAASC,QAAQ,GAAGA,QAAQnQ,QAAQW,QAAQwP,SAAS;AACnD,UAAMpU,SAASiE,QAAQmQ,KAAD;AACtB,UAAM1R,UAAUH,WAAWvC,QAAQwC,uBAAuB;MAAEmC,MAAM;IAAR,CAAhC;AAC1B,QAAIjC,YAAYxD,QAAW;AACzBkV,eAAS1R,UAAU;IACpB;AAEDyR,UAAM/K,SACJhL,wBAAC,YAAD;MAEE;MACA;MACA,gBAAgB8V,oBAAoBlU,OAAO6C;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA,iBAAiBmP,mBAAmBoC,UAAU;MAC9C;OAXKpU,OAAOC,GADd,CADFkU;EAgBD;AAED,aACE/V,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe;IACf,WAAWF,eAAK8V,oBACUE,oBAAoB,MAA3CtX,oBADY;IAGf,OAAO0J,YAAY,CAAD;IANpB,UAQG6N;GATL;AAYD;AAED,IAAA,kBAAeE,mBAAKJ,SAAD;;;AC7FnB,IAAMK,aAAN;AAIA,IAAMC,sBAAyCD,mBAAAA,UAA/C;AAEA,IAAME,kBAAN;AAQA,IAAMC,2BAAoDD,yBAAAA,eAA1D;AAEA,SAASE,KAAY;EACnB1U;EACA0C;EACAjD;EACAkV;EACAC;EACAnY,KAAAA;EACAoY;EACAC;EACAC;EACAC;EACAjD;EACA,GAAGtU;AAZgB,GAaQ;AAC3B,QAAM;IAAEC;IAAKqB;IAAUgO;MAAYN,iBAAiBhN,cAAD;AAEnD,QAAM;IAAEwV;EAAF,IAAgBjV;AACtB,QAAMwS,YAAY5L,iBAChB5G,QAKA,OAAOiV,cAAc,aAAaA,UAAUxY,IAAD,IAAQwY,WAH1BN,YAAtBJ,qBAC2BK,iBAA3BH,wBAJ6B;AASlC,WAASS,kBAAkBC,YAA6B;AACtDpD,eAAWtV,MAAKuD,QAAQmV,UAAd;EACX;AAED,WAAS3D,cAAc;;AACrB0D,uBAAkBlV,YAAOoV,kBAAPpV,mBAAsBqV,WAAvB;AACjBP,6CAAarY,MAAKuD;EACnB;AAED,WAASsV,oBAAoB;AAC3BJ,sBAAiB;EAClB;AAED,WAASK,oBAAoB;AAC3BL,sBAAkB,IAAD;AACjBH,yDAAmBtY,MAAKuD;EACzB;AAED,aACE5B,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe4B,OAAO6C,MAAM;IAC5B,iBAAepD;IACf,gBAAciD;IACd,iBAAe,CAAC2B,eAAerE,QAAQvD,IAAT,KAAiByC;IAC/C;IACA;IACA;IACA,OAAOsH,aAAaxG,QAAQ0C,OAAT;IACnB,SAAS8O;IACT,eAAe+D;IACf,eAAeD;IACf;IAbF,GAcM7X;IAdN,UAgBG,CAACuC,OAAOuE,gBACPtG,yBAAA8B,6BAAA;MAAA,UACE,KAAA3B,wBAAC,OAAO,WAAR;QACE;QACA,KAAK3B;QACL;QACA;MAJF,CAAA,GAMCoY,UAPH;IAAA,CAAA;GAlBN;AA8BD;AAED,IAAA,aAAeR,mBAAKK,IAAD;ACzFnB,SAASc,IACP;EACEhD;EACApO;EACAqR;EACAlP;EACA2N;EACA5S;EACAoU;EACAC;EACAnT;EACA/F,KAAAA;EACA8Q;EACAqI;EACAC;EACAf;EACAC;EACAe;EACAC;EACAC;EACAhB;EACAjD;EACA,GAAGtU;AArBL,GAuBAC,KACA;AACA,QAAMuY,kBAAkB3J,cAAe4J,YAAc;AACnDlB,gBAAY5Q,QAAQ8R,MAAT;EACZ,CAFoC;AAIrC,WAASC,gBAAgB3S,OAAyC;AAChEuS,iEAAuB3R;AACvB4R,iDAAexS;EAChB;AAEDgP,cAAYtU,eACVxB,cACC,WAAU0H,SAAS,MAAM,IAAI,SAAS,KAAM,IAI7C0R,qCAAWrZ,OACX+V,WAH0B0B,oBAAoB,MAA3CtX,oBAJW;AAUhB,QAAMuX,QAAQ,CAAA;AAEd,WAASC,QAAQ,GAAGA,QAAQ7G,gBAAgB3I,QAAQwP,SAAS;AAC3D,UAAMpU,SAASuN,gBAAgB6G,KAAD;AAC9B,UAAM;MAAEvR;IAAF,IAAU7C;AAChB,UAAM0C,UAAUH,WAAWvC,QAAQwC,uBAAuB;MAAEmC,MAAM;MAAOlI,KAAAA;IAAf,CAAhC;AAC1B,QAAIiG,YAAYxD,QAAW;AACzBkV,eAAS1R,UAAU;IACpB;AAED,UAAMjD,iBAAiByU,oBAAoBrR;AAE3C,QAAIpD,kBAAkBmW,oBAAoB;AACxCzB,YAAM/K,KAAKwM,kBAAX;IACD,OAAM;AACLzB,YAAM/K,SACJhL,wBAACsW,QAAD;QAEE;QACA;QACA,KAAKjY;QACL,UAAUiZ,kBAAkB7S;QAC5B,eAAe8S,uBAAuB9S;QACtC;QACA,YAAYpD,iBAAiBoW,yBAAyB3W;QACtD;QACA;QACA,aAAa+W;QACb;SAXKjW,OAAOC,GADd,CADF;IAgBD;EACF;AAED,aACE7B,wBAAC,sBAAD;IAAsB,OAAOkD;IAA7B,cACElD,wBAAA,OAAA;MACE,MAAK;MACL;MACA;MACA,cAAc+X;MACd,OAAO7P,YAAYmP,cAAclP,MAAf;MALpB,GAMM9I;MANN,UAQG0W;IARH,CAAA;GAFJ;AAcD;AAED,IAAA,YAAeE,uBAAK9W,yBAAWiY,GAAD,CAAX;ACtFnB,SAASY,UAAiB;EACxB/G;EACA9O;EACAmO;EACAlO;EACAf;EACAO;EACAvD,KAAAA;EACA4Z;EACA5V,aAAa6V;AATW,GAUA;AACxB,QAAM;IAAE5Y;IAAKqB;IAAUgO;MAAYN,iBAAiBhN,cAAD;AAEnD,WAASgB,cAAc;AACrB6V,uBAAmBjH,EAAD;EACnB;AAGD,QAAMkH,kBAAkBvW,OAAOuE,YAAY8R,qBAAqBrW,OAAO6C;AAEvE,aACEzE,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe4B,OAAO6C,MAAM;IAC5B,iBAAepD;IACf;IACA;IAEA,WAAWmH,iBAAiB5G,MAAD;IAC3B,OAAO;MACL,GAAGwG,aAAaxG,MAAD;MACfwW,QAAQD,kBAAkB,YAAY;;IAExC,SAASA,kBAAkB9V,cAAcvB;IACzC;IAbF,WAeI,CAACc,OAAOuE,YAAY8R,qBAAqBrW,OAAO6C,QAAQ7C,OAAOsC,sBAC/DlE,wBAAC,OAAO,gBAAR;MACE;MACA;MACA;MACA,KAAK3B;MACL;MACA;MACA;IAPF,CAAA;KAVGuD,OAAOC,GANd;AA4BH;AAED,IAAA,kBAAeoU,mBAAK+B,SAAD;;;ACzCnB,IAAM9G,WAAN;AAUA,IAAMmH,oBAAqCnH,iBAAAA,QAA3C;AAEA,SAASoH,WAAkB;EACzBrH;EACA9O;EACAgN;EACAmB;EACAtK;EACA3H,KAAAA;EACAgZ;EACAlP;EACA2I;EACA1O;EACA0T;EACA5S;EACAqV;EACAlW;EACA,GAAGhD;AAfsB,GAgBM;AAE/B,QAAMoF,MAAM0K,gBAAgB,CAAD,EAAItN,QAAQmB,oBAAoB8N,QAAQ,IAAIA;AAEvE,WAAS0H,oBAAoB;AAC3BD,gBAAYvS,MAAD;EACZ;AAED,aACEhG,wBAAC,sBAAD;IAAsB,OAAOkD;IAA7B,cACElD,wBAAA,OAAA;MACE,MAAK;MACL,cAAY8Q;MACZ,iBAAe1O;MACf,WAAWtC,eACTxB,cACA+Z,mBAFa,WAGFrS,SAAS,MAAM,IAAI,SAAS,KAAA,IAEb8P,oBAAoB,MAA3CtX,oBALU;MAQf,SAASga;MACT,OAAOtQ,YAAYmP,cAAclP,MAAf;MAbpB,GAcM9I;MAdN,UAgBG8P,gBAAgB7E,IAAK1I,gBACpB5B,wBAACgY,aAAD;QAEE;QACA;QACA;QACA;QACA,gBAAgBlC,oBAAoBlU,OAAO6C;QAC3C;QACA,KAAKpG;QACL,kBAAkBoG;QAClB;SATK7C,OAAOC,GADd,CADDsN;IAhBH,CAAA;GAFJ;AAmCD;AAED,IAAA,uBAAe8G,mBAAKqC,UAAD;;;AC/FZ,IAAMG,uBAAN;AAcP,SAASC,YAAmB;EAC1B9W;EACA0C;EACAjG,KAAAA;EACAgD;EACAsS;AAL0B,GAMA;AAC1B,QAAM;IAAErU;IAAKqB;IAAUgO;MAAYN,iBAAiBhN,cAAD;AACnD,QAAM;IAAEsX,kBAAkBC;IAAkBC;EAAtC,IAA2DjX;AACjE,QAAMwS,YAAY5L,iBAChB5G,QACA6W,sBACA,OAAOI,qBAAqB,aAAaA,iBAAiBxa,IAAD,IAAQwa,gBAHjC;AAMlC,WAASrD,UAAU;AACjB7B,eAAWtV,MAAKuD,MAAN;EACX;AAED,aACE5B,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe4B,OAAO6C,MAAM;IAC5B,gBAAcH;IACd,iBAAejD;IACf;IACA;IACA;IACA,OAAO+G,aAAaxG,QAAQ0C,OAAT;IACnB;IACA;IAVF,UAYGsU,wBACC5Y,wBAAC,kBAAD;MAAkB;MAAgB,KAAK3B;MAAK;IAA5C,CAAA;GAdN;AAkBD;AAED,IAAA,oBAAe4X,mBAAKyC,WAAD;;;ACpCnB,IAAMI,aAAN;AASA,IAAMC,4BAAN;AAMA,IAAMC,sBAAyCF,mBAAAA,UAA/C;AAEA,SAASG,WAAkB;EACzBjT;EACAqR;EACAhZ,KAAAA;EACA8Q;EACA2C;EACAoH;EACA9U;EACA0R;EACAnC;EACA,iBAAiBwF;AAVQ,GAWA;AACzB,QAAMpD,QAAQ,CAAA;AACd,WAASC,QAAQ,GAAGA,QAAQ7G,gBAAgB3I,QAAQwP,SAAS;AAC3D,UAAMpU,SAASuN,gBAAgB6G,KAAD;AAC9B,UAAM1R,UAAUH,WAAWvC,QAAQwC,uBAAuB;MAAEmC,MAAM;MAAWlI,KAAAA;IAAnB,CAAhC;AAC1B,QAAIiG,YAAYxD,QAAW;AACzBkV,eAAS1R,UAAU;IACpB;AAED,UAAMjD,iBAAiByU,oBAAoBlU,OAAO6C;AAElDsR,UAAM/K,SACJhL,wBAAC0Y,eAAD;MAEE;MACA;MACA,KAAKra;MACL;MACA;OALKuD,OAAOC,GADd,CADFkU;EAUD;AAED,aACE/V,wBAAA,OAAA;IACE,MAAK;IACL,iBAAemZ;IACf,WAAWrZ,eACTxB,cACW0H,WAAAA,SAAS,MAAM,IAAI,SAAS,KAF1B,IAGbgT,qBAE+BhT,WAAW,KAAvC+S,2BACuBjD,oBAAoB,MAA3CtX,oBANU;IASf,OAAM;MAEF,GAAG0J,YAAYmP,YAAD;MACd,yBAAyBvF,QAAQhR,SAARgR,GAAuBA,GAAI,OAAMhR;MAC1D,4BAA4BoY,WAAWpY,SAAeoY,GAAAA,MAAAA,OAAapY;;IAhBzE,UAoBGiV;GArBL;AAwBD;AAED,IAAA,mBAAeE,mBAAKgD,UAAD;;;AC9EnB,IAAMG,cAAN;AAce,SAASC,SAAgB;EACtCzX;EACA0C;EACAjG,KAAAA;EACAuY;EACA0C;AALsC,GAMf;;AACvB,QAAMC,sBAAkBhZ,qBAAM;AAC9B,QAAMiZ,yBAAuB5X,YAAOoV,kBAAPpV,mBAAsB4X,0BAAyB;AAK5E,QAAMC,2BAA2BvL,cAAc,MAAM;AACnDwL,YAAQ,IAAD;EADqC,CAAA;AAI9CvZ,8BAAU,MAAM;AACd,QAAI,CAACqZ,qBAAsB;AAE3B,aAASG,2BAA2B;AAClCJ,sBAAgB/Y,UAAUoZ,sBAAsBH,wBAAD;IAChD;AAEDxE,qBAAiB,aAAa0E,0BAA0B;MAAEE,SAAS;IAAX,CAAxC;AAEhB,WAAO,MAAM;AACX9E,0BAAoB,aAAa4E,0BAA0B;QAAEE,SAAS;MAAX,CAAxC;AACnBC,yBAAkB;;EAXb,GAaN,CAACN,sBAAsBC,wBAAvB,CAbM;AAeT,WAASK,qBAAqB;AAC5BC,yBAAqBR,gBAAgB/Y,OAAjB;EACrB;AAED,WAASwZ,UAAU5U,OAA4C;;AAC7D,QAAIA,MAAMvD,QAAQ,UAAU;AAC1BuD,YAAM6U,gBAAN7U;AAEAsU,cAAO;IAHT,WAIWtU,MAAMvD,QAAQ,SAAS;AAChCuD,YAAM6U,gBAAN7U;AACAsU,cAAQ,IAAD;IAFF,OAGA;AACL,YAAMQ,iBAAetY,MAAAA,OAAOoV,kBAAPpV,gBAAAA,IAAsBsY,iBAAgB5U;AAC3D,UAAI,CAAC4U,aAAa9U,KAAD,GAAS;AACxBA,cAAM6U,gBAAN7U;MACD;IACF;EACF;AAED,WAASsU,QAAQS,eAAyB;AACxC,QAAIA,eAAe;AACjBvD,kBAAYvY,MAAK,IAAN;IADb,OAEO;AACLib,kBAAW;IACZ;EACF;AAED,QAAM;IAAEzC;EAAF,IAAgBjV;AACtB,QAAMwS,YAAY5L,iBAChB5G,QACA,wBAEA,OAAOiV,cAAc,aAAaA,UAAUxY,IAAD,IAAQwY,WADnD,GAACjV,YAAOoV,kBAAPpV,mBAAsBwY,oBAAmBhB,WAHV;AAOlC,aACEpZ,wBAAA,OAAA;IACE,MAAK;IACL,iBAAe4B,OAAO6C,MAAM;IAC5B,gBAAcH;IACd,iBAJF;IAKE;IACA,OAAO8D,aAAaxG,QAAQ0C,OAAT;IACnB;IACA,oBAAoBkV,uBAAuBM,qBAAqBhZ;IARlE,UAUGc,OAAOsE,UAAU,YAChBrG,yBAAA8B,6BAAA;MAAA,UACE,KAAA3B,wBAAC,OAAO,QAAR;QAAe;QAAgB,KAAK3B;QAAK;QAA0B;OADrE,KAEGuD,YAAOoV,kBAAPpV,mBAAsBwY,wBACrBpa,wBAAC,OAAO,WAAR;QAAkB;QAAgB,KAAK3B;QAAK,gBAA5C;QAA2D;OAH/D,CAAA;IAAA,CAAA;GAZN;AAqBD;;;ACzHD,IAAMgc,iBAAN;AAiBA,IAAMC,0BAAkDD,wBAAAA,cAAxD;AAYe,SAASE,WAAkB;EACxCzU;EACAD;EACAD;EACA4U;EACAvU,gBAAAA;EACAwU;EACAC;EACAC;EACAhD;AATwC,GAUzB;AACf,WAASiD,gBAAgBxV,OAAqD;AAC5E,QAAIA,MAAMqP,YAAY,EAAG;AACzBkG,gBAAY,IAAD;AACXza,WAAO+U,iBAAiB,aAAa4F,WAArC3a;AACAA,WAAO+U,iBAAiB,WAAW6F,SAAnC5a;AAEA,aAAS2a,YAAYzV,QAAmB;AAItC,UAAIA,OAAMqP,YAAY,EAAGqG,WAAS;IACnC;AAED,aAASA,YAAY;AACnB5a,aAAO6U,oBAAoB,aAAa8F,WAAxC3a;AACAA,aAAO6U,oBAAoB,WAAW+F,SAAtC5a;AACAya,kBAAY,KAAD;AACXI,oBAAa;IACd;EACF;AAED,WAASA,gBAAgB;AACvB,UAAMC,aAAaR,wBAAwBha;AAC3C,QAAIwa,eAAela,OAAW;AAE9B,UAAM;MAAEkF;IAAF,IAAaJ;AACnB,UAAMuK,gBAAgBnK,SAASgV,aAAahV,SAAS,IAAIgV;AACzD,UAAMC,cAAcjV,SAASgV,aAAaA,aAAa,IAAIhV;AAC3DkV,eAAW/K,eAAe8K,WAAhB;AACVtD,yBAAqB7W,MAAD;EACrB;AAED,WAASqW,kBAAkB/R,OAAyC;AAClEA,UAAM6U,gBAAN7U;AACA8V,eAAWtV,iBAAiBI,SAAS,GAAGF,KAAKU,MAAnC;EACX;AAED,WAAS0U,WAAWC,aAAqBC,WAAmB;AAC1D,UAAM;MAAE3W;MAAKuB;IAAP,IAAkBJ;AACxB,UAAMhE,SAASiE,QAAQpB,GAAD;AACtB,UAAM4W,YAAYvV,KAAKE,MAAD;AACtB,UAAMsV,cAAc,CAAC,GAAGxV,IAAJ;AACpB,UAAMyV,UAAoB,CAAA;AAC1B,aAASvP,IAAImP,aAAanP,IAAIoP,WAAWpP,KAAK;AAC5C,UAAI/F,gBAAe;QAAED,QAAQgG;QAAGvH;MAAb,CAAD,GAAsB;AACtC,cAAM+W,aAAad,OAAO;UAAEzG,WAAWrS,OAAOC;UAAKwZ;UAAWI,WAAW3V,KAAKkG,CAAD;QAAnD,CAAD;AACzB,YAAIwP,eAAe1V,KAAKkG,CAAD,GAAK;AAC1BsP,sBAAYtP,CAAD,IAAMwP;AACjBD,kBAAQvQ,KAAKgB,CAAbuP;QACD;MACF;IACF;AAED,QAAIA,QAAQ/U,SAAS,GAAG;AACtBiU,mDAAea,aAAa;QAAEC;QAAS3Z;MAAX;IAC7B;EACF;AAED,aACE5B,wBAAA,OAAA;IACE,WAAWsa;IACX,aAAaM;IACb,eAAezD;GAJnB;AAOD;;;AC3GD,IAAMuE,QAAN;AAQA,IAAMC,iBAAmCD,kBAAAA,KAAzC;AAEe,SAASzI,SAAS;EAAEL;AAAF,GAAkC;AACjE,SAAOA,kBAAkB9R,aACvBd,wBAAA,OAAA;IAAK,SAAQ;IAAW,OAAM;IAAK,QAAO;IAAI,WAAW2b;IAAgB,eAAzE;IAAA,cACE3b,wBAAA,QAAA;MAAM,GAAG4S,kBAAkB,QAAQ,kBAAkB;IAArD,CAAA;EADF,CAAA,IAGE;AACL;AC4DD,IAAMgJ,kBAAmC;EACvCnX,KAAK;EACLuB,QAAQ;EACR6V,MAAM;AAHiC;AA6GzC,SAASC,SACP;EAEEjW,SAAS0D;EACTzD,MAAM2J;EACNnJ;EACAyV;EACAtB;EAEA/K;EACAsM,iBAAiBC;EACjBC,kBAAkBC;EAElBC;EACAC;EACA5I;EACAC;EACA/J;EACAS,SAASR;EACTgG;EACAC;EACAyM;EAEA5F;EACAC;EACA4F;EACA/I;EACAkH;EACA8B;EACAC;EAEA/V,oBAAoBgW;EACpB7S;EAEA8S;EACAvI;EACAwI;EACAlF;EACA7D;EAEA,cAAcvS;EACd,mBAAmBC;EACnB,oBAAoBsb;EACpB,eAAeC;AA1CjB,GA4CAxd,KACA;AAIA,QAAMyd,oBAAoB9b,qBAAoB;AAC9CyO,gBAAAA,YAAc;AACd,QAAMsM,kBAAkBC,uBAAuB,OAAOvM,cAAc,WAAWA,YAAY;AAC3F,QAAMwM,mBAAmBC,wBAAwB,OAAOzM,cAAc,WAAWA,YAAY;AAC7F,QAAMsN,eAAcL,yCAAYM,iBAAeF,uDAAmBE,gBAAe7F;AACjF,QAAMlE,YAAWyJ,yCAAYzJ,cAAY6J,uDAAmB7J,aAAYD;AACxE,QAAMxR,qBACJkb,yCAAYlb,uBAAqBsb,uDAAmBtb,sBAAqBvC;AAC3E,QAAMge,kBAAiBP,yCAAYO,oBAAkBH,uDAAmBG;AACxE,QAAMxW,qBAAqBgW,yBAAyB;AACpD7S,2BAAAA,uBAAyB;AACzBgK,gBAAAA,YAAc;AAKd,QAAM,CAAClE,WAAWwN,YAAZ,QAA4BpQ,uBAAS,CAAD;AAC1C,QAAM,CAACrD,YAAY0T,aAAb,QAA8BrQ,uBAAS,CAAD;AAC5C,QAAM,CAACvD,cAAc6T,eAAf,QAAkCtQ,uBAAsC,MAAM,oBAAI3B,IAAJ,CAApC;AAChD,QAAM,CAACxF,kBAAkB0X,mBAAnB,QAA0CvQ,uBAC9C6O,eADsD;AAGxD,QAAM,CAAC2B,YAAYC,aAAb,QAA8BzQ,uBAA+C,IAAvC;AAC5C,QAAM,CAAC0Q,YAAY9C,WAAb,QAA4B5N,uBAAS,KAAD;AAC1C,QAAM,CAAC2Q,mBAAmBC,aAApB,QAAqC5Q,uBAA6BjM,MAArB;AACnD,QAAM,CAAC8c,kBAAkBC,mBAAnB,QAA0C9Q,uBAAyC,IAAjC;AAKxD,QAAM+Q,2BAAuBvd,qBAAOqF,gBAAD;AACnC,QAAM4U,8BAA0Bja,qBAAOmd,iBAAD;AACtC,QAAMK,yBAAqBxd,qBAAO,EAAD;AACjC,QAAMyd,aAASzd,qBAAuB,IAAjB;AAKrB,QAAM,CAACqM,SAASqR,WAAWC,UAArB,IAAmCvR,kBAAiB;AAC1D,QAAMwR,kBAAkB;AACxB,QAAMC,oBAAmB9X,2CAAaE,WAAU;AAChD,QAAM4G,eAAe8Q,aAAalC,kBAAkBoC,mBAAmBlC;AACvE,QAAMmC,eAAejC,gBAAgB,QAAQC,wBAAwB;AACrE,QAAMiC,sBAAsB1Y,iBAAiBI,WAAW;AACxD,QAAM8N,QAAQD,cAAc;AAC5B,QAAM0K,UAAUzK,QAAQ,eAAe;AACvC,QAAM0K,WAAW1K,QAAQ,cAAc;AAEvC,QAAM2K,4BAAwBpU,sBAC5B,OAAO;IACL6I;IACAzR;EAFK,IAIP,CAACyR,UAAUzR,iBAAX,CALmC;AAQrC,QAAMsC,sBAAkBsG,sBAAQ,MAAe;AAE7C,UAAM;MAAE7D;IAAF,IAAaiJ;AACnB,WACEjJ,WAAW,KACX4V,gBAAgB,QAChBL,gBAAgB,QAChBK,aAAavO,QAAQrH,UACrBiJ,QAAQiP,MAAOrgB,CAAAA,SAAQ+d,aAAa/W,IAAI0W,aAAa1d,IAAD,CAA7B,CAAvB;KAED,CAACoR,SAAS2M,cAAcL,YAAxB,CAV4B;AAY/B,QAAM;IACJlW;IACAc;IACAsF;IACAC;IACAjB;IACAE;IACA/G;IACA8G;IACAd;EATI,IAUFd,qBAAqB;IACvBC;IACAC;IACAE;IACAD,eAAewU;IACftU;IACAC,YAAYgG,aAAahG,aAAa9I;IACtC+I;EAPuB,CAAD;AAUxB,QAAM;IACJkF;IACAC;IACAlJ;IACAiK;IACAsB;IACAC;IACAvL;IACAwL;IACAC;IACAC;EAVI,IAWFjC,gBAAgB;IAClBC;IACArF;IACAwF;IACAF;IACAtC;IACAuC;IACAE;IACAhG;EARkB,CAAD;AAWnB,QAAMsF,kBAAkBL,mBAAmB;IACzCjJ;IACAc;IACAsF;IACAC;IACA9H;IACA2K;IACAC;IACAlJ;IACAQ;IACAP;EAVyC,CAAD;AAa1C,QAAM4Y,YAAYvU,QAAQ5D,SAAS,KAAK,OAAOoJ,eAAe;AAC9D,QAAMgP,YAAYD,YAAY,KAAK;AACnC,QAAM9W,YAAYhC,QAAQW,SAAS;AACnC,QAAMI,YAAY;AAClB,QAAMC,YAAYsX,kBAAkBrY,KAAKU,SAAS4X,mBAAmB;AACrE,QAAMS,sCAAsCC,4BAA4BlZ,gBAAD;AACvE,QAAMmZ,qCAAqCC,2BAA2BpZ,gBAAD;AAKrE,QAAMqZ,kBAAkB/Q,cAAcgR,SAAD;AACrC,QAAMC,sBAAsBjR,cAAckR,aAAD;AACzC,QAAMC,iCAAiCnR,cAAcoR,SAAD;AACpD,QAAMC,2BAA2BrR,cAC/B,CAAC7P,MAAQuD,QAAiC4d,iBAAiC;AACzE,UAAMxZ,SAASF,KAAKgF,QAAQzM,IAAb;AACfsV,eAAW;MAAE3N;MAAQvB,KAAK7C,OAAO6C;OAAO+a,YAA9B;EACX,CAJ2C;AAM9C,QAAMC,oBAAoBvR,cAAelI,YAAmB;AAC1D2N,eAAW;MAAE3N;MAAQvB,KAAK;IAAf,CAAD;EACX,CAFsC;AAGvC,QAAMib,yBAAyBxR,cAAezJ,SAAgB;AAC5DkP,eAAW;MAAE3N,QAAQ;MAAIvB;IAAd,CAAD;EACX,CAF2C;AAG5C,QAAMkb,0BAA0BzR,cAC9B,CAAC4K,aAAgBlX,WAAoC;AACnD,UAAMoE,SAASM,YAAawE,QAAQgO,WAArB,IAAmCqF,kBAAkBrY,KAAKU,SAAS;AAClFmN,eAAW;MAAE3N;MAAQvB,KAAK7C,OAAO6C;IAAtB,CAAD;EACX,CAJ0C;AAM7C,QAAMmb,oBAAoB1R,cAAc7L,WAAD;AAKvCpC,kBAAgB,MAAM;AACpB,QACE,CAAC4e,uCACDgB,eAAeja,kBAAkBkY,qBAAqBtd,OAAxC,GACd;AACAsd,2BAAqBtd,UAAUoF;AAC/B;IACD;AAEDkY,yBAAqBtd,UAAUoF;AAE/B,QAAIA,iBAAiBnB,QAAQ,IAAI;AAC/BuZ,aAAOxd,QAASC,MAAM;QAAEC,eAAe;OAAvC;AACAgE,qBAAesZ,OAAOxd,OAAR;IACf;EACF,CAfc;AAiBfP,kBAAgB,MAAM;AACpB,QAAI2d,qBAAqB,KAAM;AAC/B,UAAMkC,gBAAgBlT,QAAQpM,QAASuf,cACpC,mBAAkBnC,iBAAiBnZ,MAAM,CAAE,IADxB;AAGtB,UAAM;MAAEjB;QAAUsc,cAAcvS,sBAAd;AAClB8P,oBAAiB7T,CAAAA,kBAAiB;AAChC,YAAMwW,kBAAkB,IAAI5U,IAAI5B,aAAR;AACxBwW,sBAAgBtU,IAAIkS,iBAAiB/b,KAAK2B,KAA1C;AACA,aAAOwc;IACR,CAJc;AAKfnC,wBAAoB,IAAD;AACnBrK,qDAAiBoK,iBAAiBnZ,KAAKjB;KACtC,CAACoa,kBAAkBhR,SAAS4G,cAA5B,CAbY;AAefyM,wCAAoB3gB,KAAK,OAAO;IAC9BqF,SAASiI,QAAQpM;IACjB0f;IACAC,YAAYna,QAAgB;AAC1B,YAAM;QAAExF;MAAF,IAAcoM;AACpB,UAAI,CAACpM,QAAS;AACdA,cAAQ4f,SAAS;QACftO,KAAKP,UAAUvL,MAAD;QACdqa,UAAU;OAFZ;;IAKF1M;EAX8B,EAAb;AAiBnB,QAAM2M,yBAAqBlS,0BACzB,CAACxM,QAAiC4B,UAA2B;AAC3D,QAAIA,UAAU,QAAQ;AACpBqa,0BAAoBjc,MAAD;AACnB;IACD;AACDyb,oBAAiB7T,CAAAA,kBAAiB;AAChC,YAAMwW,kBAAkB,IAAI5U,IAAI5B,aAAR;AACxBwW,sBAAgBtU,IAAI9J,OAAOC,KAAK2B,KAAhC;AACA,aAAOwc;IACR,CAJc;AAMfxM,qDAAiB5R,OAAO6C,KAAKjB;EAC9B,GACD,CAACgQ,cAAD,CAdoC;AAiBtC,QAAMmE,2BAAuBvJ,0BAAapI,YAAoB;AAC5D2X,kBAAc3X,MAAD;AACbwU,4BAAwBha,UAAUwF;KACjC,CAAA,CAHqC;AAQxC,WAASkZ,UAAU;IAAE7gB,KAAAA;IAAKqB;IAAS0D;EAAhB,GAAmD;AACpE,QAAI,CAACiZ,qBAAsB;AAE3BlT,2BAA6B4S,YAAP;AACtB,UAAMwE,kBAAkB,IAAIxb,IAAIqX,YAAR;AACxB,QAAIrW,WAAW1H,IAAD,GAAO;AACnB,iBAAWmiB,YAAYniB,KAAIiS,WAAW;AACpC,cAAMmQ,UAAS1E,aAAayE,QAAD;AAC3B,YAAI9gB,SAAS;AACX6gB,0BAAgBnP,IAAIqP,OAApB;QACD,OAAM;AACLF,0BAAgBG,OAAOD,OAAvB;QACD;MACF;AACDpE,2BAAqBkE,eAAD;AACpB;IACD;AAED,UAAME,SAAS1E,aAAa1d,IAAD;AAC3B,QAAIqB,SAAS;AACX6gB,sBAAgBnP,IAAIqP,MAApB;AACA,YAAME,iBAAiB5C,mBAAmBvd;AAC1C,YAAMwF,SAASF,KAAKgF,QAAQzM,IAAb;AACf0f,yBAAmBvd,UAAUwF;AAC7B,UAAI5C,gBAAgBud,mBAAmB,MAAMA,mBAAmB3a,QAAQ;AACtE,cAAM4a,OAAO7X,KAAK/C,SAAS2a,cAAV;AACjB,iBAAS3U,IAAI2U,iBAAiBC,MAAM5U,MAAMhG,QAAQgG,KAAK4U,MAAM;AAC3D,gBAAMviB,OAAMyH,KAAKkG,CAAD;AAChB,cAAIjG,WAAW1H,IAAD,EAAO;AACrBkiB,0BAAgBnP,IAAI2K,aAAa1d,IAAD,CAAhC;QACD;MACF;IACF,OAAM;AACLkiB,sBAAgBG,OAAOD,MAAvB;AACA1C,yBAAmBvd,UAAU;IAC9B;AAED6b,yBAAqBkE,eAAD;EACrB;AAED,WAASnB,cAAc1f,SAAkB;AACvC,QAAI,CAAC2c,qBAAsB;AAE3BlT,2BAA6B4S,YAAP;AACtB,UAAMwE,kBAAkB,IAAIxb,IAAIqX,YAAR;AAExB,eAAW/d,QAAOoR,SAAS;AACzB,YAAMgR,SAAS1E,aAAa1d,IAAD;AAC3B,UAAIqB,SAAS;AACX6gB,wBAAgBnP,IAAIqP,MAApB;MACD,OAAM;AACLF,wBAAgBG,OAAOD,MAAvB;MACD;IACF;AAEDpE,yBAAqBkE,eAAD;EACrB;AAED,WAASle,YAAYwe,iBAA0B;AAC7C,QAAI,CAACvE,yBAA0B;AAC/B,UAAMwE,sBAAsB,IAAI/b,IAAI8K,gBAAR;AAC5B,QAAIiR,oBAAoBzb,IAAIwb,eAAxB,GAA0C;AAC5CC,0BAAoBJ,OAAOG,eAA3B;IACD,OAAM;AACLC,0BAAoB1P,IAAIyP,eAAxB;IACD;AACDvE,6BAAyBwE,mBAAD;EACzB;AAED,WAASxe,cAAc8C,OAA4C;AACjE,QAAI,EAAEA,MAAM3F,kBAAkBshB,SAAU;AACxC,UAAMC,cAAc5b,MAAM3F,OAAOwhB,QAAQ,WAArB,MAAsC;AAC1D,UAAMC,aAAavC,aAAavZ,MAAM3F,WAAWue,OAAOxd;AACxD,QAAI,CAACwgB,eAAe,CAACE,WAAY;AAEjC,UAAM;MAAErf;MAAKsf;IAAP,IAAmB/b;AACzB,UAAM;MAAEY;IAAF,IAAaJ;AAEnB,QACEmZ,uCACCtC,WAAW,QAAQD,UAAU,SAC9BxX,kBAAkBI,KAAD,KACjB,CAACW,WAAWD,KAAKE,MAAD,CAAL,KACXJ,iBAAiBiW,SAAS,UAC1B;AAGA,YAAMuF,OAAO;AACb,YAAMC,OAAO;AACb,UAAIF,YAAYC,MAAM;AACpBE,mBAAU;AACV;MACD;AACD,UAAIH,YAAYE,MAAM;AACpBE,oBAAW;AACX;MACD;IACF;AAED,QAAIC,6BAA6Bxb,MAAD,GAAU;AACxC,YAAM3H,OAAMyH,KAAKE,MAAD;AAEhB,UACED,WAAW1H,IAAD,KACVuH,iBAAiBnB,QAAQ,OAEvB5C,QAAQ0c,WAAWlgB,KAAI+D,cAEtBP,QAAQ2c,YAAY,CAACngB,KAAI+D,aAC5B;AACAgD,cAAM+N,eAAN;AACA9Q,oBAAYhE,KAAI4S,EAAL;AACX;MACD;IACF;AAED,YAAQ7L,MAAMvD,KAAd;MACE,KAAK;AACH2b,sBAAc,IAAD;AACb;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACHiE,iBAASrc,KAAD;AACR;MACF;AACEsc,wBAAgBtc,KAAD;AACf;IAjBJ;EAmBD;AAED,WAASuc,aAAavc,OAAsC;AAC1D,UAAM;MAAEuK,WAAAA;MAAWjG,YAAAA;QAAetE,MAAMwJ;AACxCuO,iBAAaxN,UAAD;AAEZyN,kBAAcpU,IAAIU,WAAD,CAAJ;AACb6S,yCAAWnX;EACZ;AAED,WAASwc,aAAa5b,QAAgB;AACpC,WAAO2Y,YAAYlP,QAAQ3E,QAAQhF,KAAKE,MAAD,CAApB,IAAqCA;EACzD;AAED,WAASsZ,UAAUtZ,QAAgB3H,MAAQ;AACzC,QAAI,OAAOoc,iBAAiB,WAAY;AACxC,UAAMoH,YAAYD,aAAa5b,MAAD;AAC9B,QAAI3H,SAAQoR,QAAQoS,SAAD,EAAa;AAChC,UAAMvG,cAAc,CAAC,GAAG7L,OAAJ;AACpB6L,gBAAYuG,SAAD,IAAcxjB;AACzBoc,iBAAaa,aAAa;MACxBC,SAAS,CAACsG,SAAD;MACTjgB,QAAQiE,QAAQD,iBAAiBnB,GAAlB;IAFS,CAAd;EAIb;AAED,WAASqd,sBAAsB;AAC7B,QAAIlc,iBAAiBiW,SAAS,OAAQ;AACtCyD,cAAU1Z,iBAAiBI,QAAQJ,iBAAiBvH,GAA3C;EACV;AAED,WAASijB,aAAa;AACpB,UAAM;MAAE7c;MAAKuB;IAAP,IAAkBJ;AACxB,UAAMyV,YAAY5L,QAAQmS,aAAa5b,MAAD,CAAb;AACzB,UAAM+b,kBAAkBlc,QAAQpB,GAAD,EAAM5C;AACrC2b,kBAAc;MAAEnf,KAAKgd;MAAWpH,WAAW8N;IAA7B,CAAD;AACbvF,qCAAS;MAAEnB;MAAW0G;IAAb;EACV;AAED,WAASR,cAAc;AACrB,QAAI,CAAC9E,WAAW,CAAChC,gBAAgB8C,eAAe,QAAQ,CAACtX,gBAAeL,gBAAD,GAAoB;AACzF;IACD;AAED,UAAM;MAAEnB;MAAKuB;IAAP,IAAkBJ;AACxB,UAAM6V,YAAYhM,QAAQmS,aAAa5b,MAAD,CAAb;AAEzB,UAAMgc,mBAAmBvF,QAAQ;MAC/BpB,WAAWkC,WAAWlf;MACtB0jB,iBAAiBxE,WAAWtJ;MAC5BwH;MACAwG,iBAAiBpc,QAAQpB,GAAD,EAAM5C;IAJC,CAAD;AAOhCyd,cAAUtZ,QAAQgc,gBAAT;EACV;AAED,WAASN,gBAAgBtc,OAA4C;;AACnE,QAAI,CAAC2Z,mCAAoC;AACzC,UAAM1gB,OAAMyH,KAAKF,iBAAiBI,MAAlB;AAChB,QAAID,WAAW1H,IAAD,EAAO;AACrB,UAAM;MAAEwD;MAAKjC;IAAP,IAAoBwF;AAG1B,QAAIiZ,gBAAgBze,YAAYiC,QAAQ,KAAK;AAC3CsH,6BAA6B4S,YAAP;AACtB,YAAM0E,SAAS1E,aAAa1d,IAAD;AAC3B6gB,gBAAU;QAAE7gB,KAAAA;QAAKqB,SAAS,CAAC0c,aAAa/W,IAAIob,MAAjB;QAA0Brd,cAAc;MAAzD,CAAD;AAETgC,YAAM+N,eAAN;AACA;IACD;AAED,UAAMvR,SAASiE,QAAQD,iBAAiBnB,GAAlB;AACtB7C,uBAAOoV,kBAAPpV,mBAAsBsgB,kBAAtBtgB,4BAAsCwD;AACtC,QAAIA,MAAM+c,mBAAN,EAA4B;AAEhC,QAAIlc,gBAAeL,gBAAD,KAAsBT,mBAAmBC,KAAD,GAAS;AACjEkY,0BAAoB,CAAC;QAAE7Y;QAAKuB;MAAP,OAAqB;QACxCvB;QACAuB;QACA6V,MAAM;QACNxd,KAAAA;QACA+jB,aAAa/jB;MAL2B,EAAvB;IAOpB;EACF;AAKD,WAASgkB,8BAA8B5d,KAAa;AAClD,WAAOA,OAAOma,aAAana,OAAOoD;EACnC;AAED,WAAS2Z,6BAA6Bxb,QAAgB;AACpD,WAAOA,UAAU,KAAKA,SAASF,KAAKU;EACrC;AAED,WAASsY,4BAA4B;IAAEra;IAAKuB;EAAP,GAAoC;AACvE,WAAOA,UAAUY,aAAaZ,UAAUa,aAAawb,8BAA8B5d,GAAD;EACnF;AAED,WAASua,2BAA2B;IAAEva;IAAKuB;EAAP,GAAoC;AACtE,WAAOwb,6BAA6Bxb,MAAD,KAAYqc,8BAA8B5d,GAAD;EAC7E;AAED,WAASwB,gBAAe4L,UAA6B;AACnD,WACEmN,2BAA2BnN,QAAD,KAC1BlM,uBAAuB;MAAEE;MAASC;MAAMF,kBAAkBiM;MAAU9L;IAA7C,CAAD;EAEzB;AAED,WAAS4N,WAAW9B,UAAoB2N,cAAqC;;AAC3E,QAAI,CAACV,4BAA4BjN,QAAD,EAAY;AAC5CiQ,wBAAmB;AAEnB,QAAItC,gBAAgBvZ,gBAAe4L,QAAD,GAAY;AAC5C,YAAMxT,OAAMyH,KAAK+L,SAAS7L,MAAV;AAChBsX,0BAAoB;QAAE,GAAGzL;QAAUgK,MAAM;QAAQxd,KAAAA;QAAK+jB,aAAa/jB;MAA/C,CAAD;eACVwhB,eAAeja,kBAAkBiM,QAAnB,GAA8B;AAErDnN,sBAAekI,aAAQpM,YAARoM,mBAAiBmT,cAAc,iBAAhC;IACf,OAAM;AACLzC,0BAAoB;QAAE,GAAGzL;QAAUgK,MAAM;MAArB,CAAD;IACpB;EACF;AAED,WAASqE,eAAezb,KAAmB;AACzC,UAAM;MAAEjE;IAAF,IAAcoM;AACpB,QAAI,CAACpM,QAAS;AAEd,QAAIiE,MAAML,uBAAuB;AAC/B,YAAM;QAAE4B;MAAF,IAAaJ;AACnB,UAAI,CAACkZ,4BAA4B;QAAE9Y;QAAQvB;MAAV,CAAD,EAAmB;AACnD,YAAM;QAAE0I;MAAF,IAAkB3M;AACxB,YAAMoB,SAASiE,QAAQpB,GAAD;AACtB,YAAM;QAAE4G;QAAM7H;MAAR,IAAkB2H,cAAcS,IAAIhK,MAAlB;AACxB,UAAI+S,QAAQtJ,OAAO7H;AAEnB,YAAMc,UAAU+B,uBAAuB;QACrCP;QACAQ;QACAN;QACA5B;QACAxC;QACAmE;MANqC,CAAD;AAStC,UAAIzB,YAAYxD,QAAW;AACzB,cAAM;UAAEuK,MAAAA;UAAM7H,OAAAA;QAAR,IAAkB2H,cAAcS,IAAI/F,QAAQjE,OAAO6C,MAAMH,UAAU,CAAxB,CAAzB;AACxBqQ,gBAAQtJ,QAAO7H;MAChB;AAED,YAAM8e,uBAAuBjX,OAAO3B,aAAawB;AACjD,YAAMqX,wBAAwB5N,QAAQxH,cAAczD;AACpD,YAAMX,QAAO+K,QAAQ,KAAK;AAC1B,UAAIwO,sBAAsB;AACxB9hB,gBAAQkJ,cAAc2B,OAAOH,0BAA0BnC;iBAC9CwZ,uBAAuB;AAChC/hB,gBAAQkJ,cAAciL,QAAQxH,eAAepE;MAC9C;IACF;EACF;AAED,WAASyZ,gBAAgB3gB,KAAaoD,SAAkBrF,UAA6B;AACnF,UAAM;MAAE6E;MAAKuB;IAAP,IAAkBJ;AACxB,UAAMvH,OAAMyH,KAAKE,MAAD;AAChB,UAAM9C,gBAAgB2b,uCAAuCpa,QAAQ;AAGrE,QAAI5C,QAAQ0c,WAAWrb,iBAAiB6C,WAAW1H,IAAD,KAAS,CAACA,KAAI+D,cAAc/D,KAAIyS,UAAU,GAAG;AAC7F,UAAI2R,eAAe;AACnB,eAASzW,IAAIpG,iBAAiBI,SAAS,GAAGgG,KAAK,GAAGA,KAAK;AACrD,cAAM0W,YAAY5c,KAAKkG,CAAD;AACtB,YAAIjG,WAAW2c,SAAD,KAAeA,UAAUzR,OAAO5S,KAAIwS,UAAU;AAC1D4R,yBAAezW;AACf;QACD;MACF;AACD,UAAIyW,iBAAiB,IAAI;AACvB,eAAO;UAAEhe;UAAKuB,QAAQyc;;MACvB;IACF;AAED,YAAQ5gB,KAAR;MACE,KAAK;AACH,eAAO;UAAE4C;UAAKuB,QAAQA,SAAS;;MACjC,KAAK;AACH,eAAO;UAAEvB;UAAKuB,QAAQA,SAAS;;MACjC,KAAKuY;AACH,eAAO;UAAE9Z,KAAKA,MAAM;UAAGuB;;MACzB,KAAKwY;AACH,eAAO;UAAE/Z,KAAKA,MAAM;UAAGuB;;MACzB,KAAK;AACH,eAAO;UAAEvB,KAAKA,OAAO7E,WAAW,KAAK;UAAIoG;;MAC3C,KAAK;AAEH,YAAI9C,cAAe,QAAO;UAAEuB;UAAKuB,QAAQ;;AACzC,eAAO;UAAEvB,KAAK;UAAGuB,QAAQf,UAAU2B,YAAYZ;;MACjD,KAAK;AAEH,YAAI9C,cAAe,QAAO;UAAEuB;UAAKuB,QAAQF,KAAKU,SAAS;;AACvD,eAAO;UAAE/B,KAAKoD;UAAW7B,QAAQf,UAAU4B,YAAYb;;MACzD,KAAK,UAAU;AACb,YAAIJ,iBAAiBI,WAAWY,UAAW,QAAOhB;AAClD,cAAM+c,WAAWpR,UAAUvL,MAAD,IAAWwL,aAAaxL,MAAD,IAAWoH;AAC5D,eAAO;UAAE3I;UAAKuB,QAAQ2c,WAAW,IAAIlR,WAAWkR,QAAD,IAAa;;MAC7D;MACD,KAAK,YAAY;AACf,YAAI/c,iBAAiBI,UAAUF,KAAKU,OAAQ,QAAOZ;AACnD,cAAM+c,WAAWpR,UAAUvL,MAAD,IAAWoH;AACrC,eAAO;UAAE3I;UAAKuB,QAAQ2c,WAAWtR,iBAAiBI,WAAWkR,QAAD,IAAa7c,KAAKU,SAAS;;MACxF;MACD;AACE,eAAOZ;IA9BX;EAgCD;AAED,WAAS6b,SAASrc,OAA4C;AAC5D,UAAM;MAAEvD;MAAKjC;IAAP,IAAoBwF;AAC1B,QAAIyW,OAAOnV;AACX,QAAI7E,QAAQ,OAAO;AACjB,UACE+F,YAAY;QACVhI;QACA8G;QACAmB;QACAjB;QACAC;QACAjB;MANU,CAAD,GAQX;AACAkc,4BAAmB;AAEnB;MACD;AAEDjG,aAAOnV,uBAAuB,SAAS,eAAeA;IACvD;AAGDtB,UAAM+N,eAAN;AAEA,UAAMlO,UAAUD,kBAAkBI,KAAD;AACjC,UAAM4B,eAAewb,gBAAgB3gB,KAAKoD,SAASrF,QAAf;AACpC,QAAIigB,eAAeja,kBAAkBoB,YAAnB,EAAkC;AAEpD,UAAM4b,2BAA2Bnc,4BAA4B;MAC3DZ;MACAc;MACAb;MACAQ;MACAM;MACAC;MACAzC;MACAsC,oBAAoBmV;MACpB/U,iBAAiBlB;MACjBoB;MACAC,oBAAoB6X;MACpB/Y;IAZ2D,CAAD;AAe5D4N,eAAWiP,wBAAD;EACX;AAED,WAASC,sBAAsBC,eAA2C;AACxE,QAAIpF,sBAAsB5c,OAAW;AACrC,UAAM;MAAEkF;IAAF,IAAaJ;AAEnB,UAAM4Q,gBACJxQ,SAAS0X,oBACL1X,SAAS8c,iBAAiBA,iBAAiBpF,oBAC3C1X,SAAS8c,iBAAiBA,iBAAiBpF;AAEjD,WAAOlH,gBAAgB5Q,iBAAiBnB,MAAM3D;EAC/C;AAED,WAASiiB,mBAAmB;AAC1B,QAAInF,qBAAqB,KAAM,QAAO3S;AACtC,UAAM;MAAEc;IAAF,IAA0Bd;AAChC,UAAM+X,WAAWjX,oBAAoBkX,MAAM,GAA1B;AACjBD,aAASpF,iBAAiBnZ,GAAlB,IAAyB;AACjC,WAAO;MACL,GAAGwG;MACHc,qBAAqBiX,SAASE,KAAK,GAAd;;EAExB;AAED,WAASC,cAAcnd,QAAgB;AACrC,QACEJ,iBAAiBI,WAAWA,UAC5BJ,iBAAiBiW,SAAS,UAC1B8C,aACAjE,UAAU,MACV;AACA;IACD;AAED,eACE1a,wBAAC,YAAD;MACE,MAAMyP;MACN;MACA;MACA,gBAAgBxJ;MAChB;MACA;MACA;MACA;MACA;KAVJ;EAaD;AAED,WAASmd,cAAcpd,QAAgB;AACrC,QAAIJ,iBAAiBI,WAAWA,UAAUJ,iBAAiBiW,SAAS,SAAU;AAE9E,UAAM;MAAEpX;MAAKpG,KAAAA;IAAP,IAAeuH;AACrB,UAAMhE,SAASiE,QAAQpB,GAAD;AACtB,UAAMH,UAAUH,WAAWvC,QAAQwC,uBAAuB;MAAEmC,MAAM;MAAOlI,KAAAA;IAAf,CAAhC;AAE1B,UAAMib,cAAc,MAAM;AACxBgE,0BAAoB,CAAC;QAAE7Y,KAAAA;QAAKuB,QAAAA;MAAP,OAAqB;QAAEvB,KAAAA;QAAKuB,QAAAA;QAAQ6V,MAAM;MAArB,EAAvB;;AAGrB,UAAMjF,cAAc,CAACvY,MAAQ8b,kBAA4B;AACvD,UAAIA,eAAe;AACjBmF,kBAAU1Z,iBAAiBI,QAAQ3H,IAA1B;AACTib,oBAAW;MACZ,OAAM;AACLgE,4BAAqBzL,eAAc;UAAE,GAAGA;UAAUxT,KAAAA;QAAf,EAAhB;MACpB;;AAGH,QAAIyH,KAAKF,iBAAiBI,MAAlB,MAA8BJ,iBAAiBwc,aAAa;AAElE9I,kBAAW;IACZ;AAED,eACEtZ,wBAAC,UAAD;MAEE;MACA;MACA,KAAK3B;MACL;MACA;OALKuD,OAAOC,GADd;EASH;AAED,WAASwhB,sBAAsBrd,QAAgB;AAC7C,UAAMsd,iBAAiBzd,QAAQD,iBAAiBnB,GAAlB;AAC9B,QAGE6e,mBAAmBxiB,UACnB8E,iBAAiBI,WAAWA,UAC5B,CAACmJ,gBAAgB3E,SAAS8Y,cAAzB,GACD;AAEA,aAAO1d,iBAAiBnB,MAAMyH,oBAC1B,CAAC,GAAGiD,iBAAiBmU,cAArB,IACA,CACE,GAAGnU,gBAAgBoU,MAAM,GAAGnf,wBAAwB,CAAjD,GACHkf,gBACA,GAAGnU,gBAAgBoU,MAAMnf,wBAAwB,CAA9C,CAHL;IAKL;AACD,WAAO+K;EACR;AAED,WAASqU,kBAAkB;AACzB,UAAMC,cAAc,CAAA;AACpB,QAAItT,gBAAgB;AAEpB,UAAM;MAAE1L,KAAKif;MAAa1d,QAAQ2d;IAA5B,IAA+C/d;AACrD,UAAMuV,cACJ4D,sCAAsC4E,iBAAiB5U,sBACnDA,sBAAsB,IACtBA;AACN,UAAMqM,YACJ2D,sCAAsC4E,iBAAiB3U,oBACnDA,oBAAoB,IACpBA;AAEN,aAAS4U,iBAAiBzI,aAAayI,kBAAkBxI,WAAWwI,kBAAkB;AACpF,YAAMC,uBACJD,mBAAmB7U,sBAAsB,KAAK6U,mBAAmB5U,oBAAoB;AACvF,YAAMhJ,SAAS6d,uBAAuBF,iBAAiBC;AAEvD,UAAIE,aAAa3U;AACjB,YAAMmU,iBAAiBzd,QAAQ6d,WAAD;AAG9B,UAAIJ,mBAAmBxiB,QAAW;AAChC,YAAI+iB,sBAAsB;AAExBC,uBAAa,CAACR,cAAD;QACd,OAAM;AAELQ,uBAAaT,sBAAsBrd,MAAD;QACnC;MACF;AAED,YAAM3H,OAAMyH,KAAKE,MAAD;AAChB,YAAMqR,eAAe8G,kBAAkBnY,SAAS;AAChD,UAAID,WAAW1H,IAAD,GAAO;AACnB,SAAC;UAAE8R;QAAF,IAAoB9R;AACrB,cAAM0lB,qBACJ1F,gBAAgBhgB,KAAIiS,UAAUoO,MAAOsF,QAAO5H,aAAa/W,IAAI0W,aAAciI,EAAF,CAA7B,CAA5B;AAClBP,oBAAYzY,SACVhL,wBAAC,kBAAD;UACE,cAAY3B,KAAIyS,QAAQ;UACxB,gBAAczS,KAAI8S;UAClB,iBAAe9S,KAAI2S,WAAW;UAC9B,iBAAemN,kBAAkBhO,gBAAgB;UACjD,iBAAekO,eAAe0F,qBAAqBjjB;UAEnD,IAAIzC,KAAI4S;UACR,UAAU5S,KAAI8D;UACd,iBAAiB2hB;UACjB,WAAWzlB,KAAIiS;UACf;UACA,KAAKjS;UACL;UACA,QAAQmT,aAAaxL,MAAD;UACpB,OAAO3H,KAAIyS;UACX,YAAYzS,KAAI+D;UAChB,iBAAiBuhB,mBAAmB3d,SAAS0d,cAAc5iB;UAC3D,eAAeijB;UACf,aAAatE;UACb,aAAaG;WAdRvhB,KAAI4S,EANX,CADF;AAwBA;MACD;AAEDd;AACA,UAAItO;AACJ,UAAIqB,gBAAgB;AACpB,UAAI,OAAO6Y,iBAAiB,YAAY;AACtCla,cAAMka,aAAa1d,IAAD;AAClB6E,yBAAgBkZ,6CAAc/W,IAAIxD,SAAQ;MAC3C,OAAM;AACLA,cAAM8c,YAAYxO,gBAAgBnK;MACnC;AAEDyd,kBAAYzY,SACVhL,wBAAC,aAAD;QACE,iBAAeme,mBAAmBQ,YAAYxO,gBAAgBnK,UAAU;QACxE,iBAAeqY,eAAenb,gBAAgBpC;QAE9C;QACA,KAAKzC;QACL,iBAAiBylB;QACjB;QACA;QACA;QACA;QACA;QACA,QAAQtS,aAAaxL,MAAD;QACpB,eACEuX,eAAe,QAAQA,WAAWlf,QAAQA,OACtCwH,QAAQmO,UAAWiQ,OAAMA,EAAEpiB,QAAQ0b,WAAWtJ,SAA9C,IACAnT;QAEN,iBAAiB6iB,mBAAmB3d,SAAS0d,cAAc5iB;QAC3D,oBAAoB+hB,sBAAsB7c,MAAD;QACzC,sBAAsByX,aAAa9F,uBAAuB7W;QAC1D;QACA,aAAaue;QACb,YAAYE;QACZ,wBAAwB4D,cAAcnd,MAAD;QACrC,oBAAoBod,cAAcpd,MAAD;MAzBnC,GAGOnE,GAHP,CADF;IA6BD;AAED,WAAO4hB;EACR;AAGD,MAAI7d,iBAAiBnB,MAAMoD,aAAajC,iBAAiBI,SAASa,WAAW;AAC3EyW,wBAAoB1B,eAAD;AACnBjE,yBAAqB7W,MAAD;EACrB;AAED,MAAIojB,eAAgB,GAAElI,eAAgB;AACtC,MAAIlW,KAAKU,SAAS,GAAG;AACnB0d,oBAAgB5S;EACjB;AACD,MAAI8M,mBAAmB,GAAG;AACxB8F,oBAAiB,WAAU9F,gBAAiB,KAAIlC,gBAAiB;EAClE;AAED,QAAMiI,oBAAoBve,iBAAiBnB,QAAQ,MAAMmB,iBAAiBI,WAAW;AAErF,aACEnG,yBAAA,OAAA;IACE,MAAM8e,YAAY,aAAa;IAC/B,cAAYrd;IACZ,mBAAiBC;IACjB,oBAAkBsb;IAClB,wBAAsBwB,eAAe,OAAOvd;IAC5C,iBAAe+E,QAAQW;IACvB,iBAAe2X,kBAAkBpO,YAAYqO;IAC7C,WAAWte,eACT7B,eAKAmW,WAH+BqJ,cAA5Btf,2BAC0Byf,qBAAqB,QAA/CjgB,uBAJU;IAQf,OACE;MACE,GAAGif;MAEHwH,0BACExe,iBAAiBnB,MAAML,wBAClB,GAAE8G,sBAAuB,OAC1BpK;MACNujB,oBACEze,iBAAiBI,UAAU,KAAKJ,iBAAiBI,SAASF,KAAKU,SAC1D,GAAEwV,eAAgB,MAAKoC,mBAAmBlC,gBAAiB,OAC5Dpb;MACNwQ,kBAAkB4S;MAClB,2BAA4B,GAAElI,eAAgB;MAC9C,4BAA6B,GAAEE,gBAAiB;MAChD,cAAcpI,QAAQ,KAAK;MAC3B,GAAGiP,iBAAgB;;IAGvB,KAAKlP;IACL,KAAKjH;IACL,UAAU+U;IACV,WAAWrf;IACX,eAAawa;IAvCf,UAAA,CA0CG6B,iBACC3e,wBAAA,OAAA;MACE,KAAKge;MACL,UAAUmG,oBAAoB,IAAI;MAClC,WAAWrkB,eAAK1B,oBACC+lB,qBADF,CACZ5lB,aACiD6F,0BAA0B,MAA3E3F,yBAFY,CAHjB;MAOE,OAAO;QACL4Y,cAAczR,iBAAiBI,SAAS;;MAE1C,WAAW1D;KArDjB,OAwDEzC,yBAAC,mCAAD;MAAmC,OAAO4e;MAA1C,UAAA,KACEze,wBAAC6V,aAAD;QACE,SAASwN,sBAAsB,EAAD;QAC9B,gBAAgB/C;QAChB;QACA,0BAA0BnB;QAC1B;QACA;QACA;QACA,iBAAiBb,sBAAsB1Y,iBAAiBnB,MAAM3D;QAC9D,YAAY4e;QACZ,iBAAiB,CAACb;QAClB;OAZJ,GAcG/Y,KAAKU,WAAW,KAAK0W,iBACpBA,qBAEArd,yBAAA8B,6BAAA;QAAA,UAAA,KACE3B,wBAAC,4BAAD;UAA4B,OAAOif;UAAnC,UACGuE,gBAAe;SAFpB,GAIGld,2CAAagE,IAAI,CAACjM,MAAK2H,WAAW;AACjC,gBAAMqR,eAAe8G,kBAAkBrY,KAAKU,SAASR,SAAS;AAC9D,gBAAMse,gBAAgBnG,kBAAkBrY,KAAKU,SAASR,SAAS;AAC/D,gBAAMue,uBAAuB3e,iBAAiBI,WAAWse;AACzD,gBAAMxS,MACJ1E,eAAeiE,iBACX6M,aAAahC,oBAAoB5V,YAAYE,SAASR,UACtDlF;AACN,gBAAMoY,SACJpH,QAAQhR,SACJob,oBAAoB5V,YAAYE,SAAS,IAAIR,UAC7ClF;AAEN,qBACEd,wBAACiZ,cAAD;YACE,iBAAekF,kBAAkBpO,YAAY/J,SAAS;YAEtD;YACA;YACA,KAAK3H;YACL;YACA;YACA,iBAAiBglB,sBAAsBiB,aAAD;YACtC;YACA,iBAAiBC,uBAAuB3e,iBAAiBnB,MAAM3D;YAC/D,YAAY6e;UAXd,GAEO3Z,MAFP;QAcH,EAhCH;OAjBJ,CAAA;KAxDF,CAAA;GADF;AAgHD;AAED,SAAS6Z,eAAe2E,IAAcC,IAAc;AAClD,SAAOD,GAAG/f,QAAQggB,GAAGhgB,OAAO+f,GAAGxe,WAAWye,GAAGze;AAC9C;AAED,IAAA,iBAAe7G,yBAAW2c,QAAD;;;ACrsCzB,IAAM4I,aAAN;AA2BO,IAAMC,sBAAuB,mBAAkBD,UAA/C;AAEP,SAASE,mBAAmBC,OAAgC;AAC1DA,iCAAOpkB;AACPokB,iCAAOC;AACR;AAEc,SAASC,WAA8B;EACpD1mB,KAAAA;EACAuD;EACAgV;EACA8C;AAJoD,GAKnB;AACjC,aACE1Z,wBAAA,SAAA;IACE,WAAW2kB;IACX,KAAKC;IACL,OAAOvmB,KAAIuD,OAAOC,GAAR;IACV,UAAWuD,WAAUwR,YAAY;MAAE,GAAGvY;MAAK,CAACuD,OAAOC,GAAR,GAAcuD,MAAM3F,OAAO2B;IAArC,CAAD;IAChC,QAAQ,MAAMsY,QAAQ,IAAD;GANzB;AASD;", "names": ["cell", "cellClassname", "cellAutoResizeClassname", "cellFrozen", "cellFrozenClassname", "cellFrozenLast", "cellFrozenLastClassname", "root", "rootClassname", "viewportDragging", "viewportDraggingClassname", "focusSinkClassname", "row", "rowClassname", "rowSelected", "rowSelectedClassname", "rowSelectedWithFrozenCell", "checkboxLabel", "checkboxLabelClassname", "checkboxInput", "checkboxInputClassname", "checkbox", "checkboxClassname", "checkboxLabelDisabled", "checkboxLabelDisabledClassname", "CheckboxFormatter", "forwardRef", "onChange", "props", "ref", "handleChange", "e", "target", "checked", "nativeEvent", "shift<PERSON>ey", "_jsxs", "clsx", "disabled", "_jsx", "useLayoutEffect", "window", "useEffect", "useOriginalLayoutEffect", "useFocusRef", "isSelected", "useRef", "current", "focus", "preventScroll", "tabIndex", "DataGridDefaultComponentsContext", "createContext", "undefined", "DataGridDefaultComponentsProvider", "Provider", "useDefaultComponents", "useContext", "SelectCellFormatter", "value", "isCellSelected", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "<PERSON><PERSON><PERSON>", "checkboxFormatter", "ValueFormatter", "_Fragment", "column", "key", "groupCellContent", "groupCellContentClassname", "caret", "caretClassname", "ToggleGroupFormatter", "groupKey", "isExpanded", "toggleGroup", "handleKeyDown", "d", "RowSelectionContext", "RowSelectionProvider", "RowSelectionChangeContext", "RowSelectionChangeProvider", "useRowSelection", "rowSelectionContext", "rowSelectionChangeContext", "Error", "SELECT_COLUMN_KEY", "SelectFormatter", "isRowSelected", "onRowSelectionChange", "isShiftClick", "SelectGroupFormatter", "SelectColumn", "name", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "resizable", "sortable", "frozen", "<PERSON><PERSON><PERSON><PERSON>", "allRowsSelected", "onAllRowsSelectionChange", "formatter", "groupFormatter", "getColSpan", "lastFrozenColumnIndex", "args", "colSpan", "Number", "isInteger", "idx", "scrollIntoView", "element", "inline", "block", "nonInputKeys", "Set", "isCtrlKeyHeldDown", "ctrl<PERSON>ey", "metaKey", "isDefaultCellInput", "event", "has", "onEditorNavigation", "HTMLInputElement", "HTMLTextAreaElement", "HTMLSelectElement", "matches", "isSelectedCellEditable", "selectedPosition", "columns", "rows", "isGroupRow", "rowIdx", "isCellEditable", "editor", "rowGroup", "editable", "getSelectedCellColSpan", "summaryRows", "type", "length", "getNextSelectedCellPosition", "cellNavigationMode", "colSpanColumns", "minRowIdx", "maxRowIdx", "currentPosition", "currentIdx", "nextPosition", "isCellWithinBounds", "nextIdx", "nextRowIdx", "setColSpan", "moveRight", "colIdx", "columnsCount", "isAfterLastColumn", "isBeforeFirstColumn", "isLastRow", "isFirstRow", "canExitGrid", "maxColIdx", "atLastCellInRow", "atFirstCellInRow", "atLastRow", "atFirstRow", "getRowStyle", "height", "getCellStyle", "gridColumnStart", "gridColumnEnd", "insetInlineStart", "getCellClassname", "extraClasses", "isLastFrozenColumn", "min", "max", "round", "floor", "sign", "abs", "ceil", "Math", "assertIsValidKeyGetter", "keyGetter", "clampCol<PERSON><PERSON><PERSON><PERSON><PERSON>", "useCalculatedColumns", "rawColumns", "columnWidths", "viewportWidth", "scrollLeft", "defaultColumnOptions", "rawGroupBy", "enableVirtualization", "defaultWidth", "defaultMinWidth", "defaultMaxWidth", "defaultFormatter", "defaultSortable", "defaultResizable", "groupBy", "useMemo", "map", "rawColumn", "includes", "sort", "a<PERSON><PERSON>", "frozenA", "b<PERSON><PERSON>", "frozenB", "indexOf", "for<PERSON>ach", "push", "layoutCssVars", "totalFrozenColumnWidth", "columnMetrics", "Map", "left", "templateColumns", "allocated<PERSON><PERSON><PERSON>", "unassignedColumnsCount", "getSpecifiedWidth", "set", "columnMetric", "get", "unallocatedWidth", "unallocatedColumnWidth", "gridTemplateColumns", "i", "colOverscanStartIdx", "colOverscanEndIdx", "viewportLeft", "viewportRight", "lastColIdx", "firstUnfrozenColumnIdx", "colVisibleStartIdx", "colVisibleEndIdx", "test", "parseInt", "useGridDimensions", "gridRef", "inlineSize", "setInlineSize", "useState", "blockSize", "setBlockSize", "ResizeObserver", "clientWidth", "clientHeight", "offsetWidth", "offsetHeight", "getBoundingClientRect", "initialWidth", "initialHeight", "handleDevicePixelRatio", "resizeObserver", "entries", "size", "contentBoxSize", "observe", "disconnect", "devicePixelRatio", "useLatestFunc", "fn", "useCallback", "useRovingCellRef", "isChildFocused", "setIsChildFocused", "contains", "document", "activeElement", "onFocus", "currentTarget", "isFocused", "useViewportColumns", "rowOverscanStartIdx", "rowOverscanEndIdx", "startIdx", "updateStartIdx", "viewportColumns", "isReadonlyArray", "arr", "Array", "isArray", "useViewportRows", "rawRows", "rowHeight", "scrollTop", "rowGrouper", "expandedGroupIds", "groupedRows", "rowsCount", "groupRows", "groupByKey", "remainingGroupByKeys", "startRowIndex", "groupRowsCount", "groups", "childRows", "Object", "childGroups", "childRowsCount", "allGroupRows", "flattenedRows", "expandGroup", "parentId", "level", "keys", "posInSet", "id", "groupRow", "setSize", "add", "totalRowHeight", "gridTemplateRows", "getRowTop", "getRowHeight", "findRowIdx", "offset", "rowPositions", "currentRowHeight", "position", "top", "validateRowIdx", "start", "end", "middle", "currentOffset", "overscanThreshold", "rowVisibleStartIdx", "rowVisibleEndIdx", "headerSortCell", "headerSortCellClassname", "headerSortName", "headerSortNameClassname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortDirection", "priority", "onSort", "SortableHeaderCell", "children", "SortIcon", "sortIcon", "preventDefault", "handleClick", "cellResizable", "cellResizableClassname", "<PERSON><PERSON><PERSON><PERSON>", "onColumnResize", "sortColumns", "onSortColumnsChange", "selectCell", "shouldFocusGrid", "direction", "isRtl", "sortIndex", "findIndex", "column<PERSON>ey", "sortColumn", "ariaSort", "className", "headerCellClass", "DefaultHeader<PERSON><PERSON><PERSON>", "onPointerDown", "pointerType", "buttons", "pointerId", "right", "clientX", "onPointerMove", "onLostPointerCapture", "removeEventListener", "setPointerCapture", "addEventListener", "ctrlClick", "sortDescending<PERSON>irst", "nextSort", "nextSortColumn", "nextSortColumns", "splice", "onClick", "onDoubleClick", "handleFocus", "headerRow", "headerRowClassname", "HeaderRow", "selectedCellIdx", "cells", "index", "memo", "cellCopied", "cellCopiedClassname", "cellDraggedOver", "cellDraggedOverClassname", "Cell", "isCopied", "isDraggedOver", "dragHandle", "onRowClick", "onRowDoubleClick", "onRowChange", "cellClass", "selectCellWrapper", "openEditor", "editorOptions", "editOnClick", "handleContextMenu", "handleDoubleClick", "Row", "gridRowStart", "copiedCellIdx", "draggedOverCellIdx", "selectedCellEditor", "selectedCellDragHandle", "rowClass", "setDraggedOverRowIdx", "onMouseEnter", "handleRowChange", "newRow", "handleDragEnter", "GroupCell", "groupColumnIndex", "toggleGroupWrapper", "isLevelMatching", "cursor", "groupRowClassname", "GroupedRow", "selectGroup", "handleSelectGroup", "summaryCellClassname", "<PERSON><PERSON>ryCell", "summary<PERSON><PERSON>atter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summaryCellClass", "summaryRow", "summaryRowBorderClassname", "summaryRowClassname", "SummaryRow", "bottom", "ariaRowIndex", "cellEditing", "EditCell", "closeEditor", "frameRequestRef", "commitOnOutsideClick", "commitOnOutsideMouseDown", "onClose", "onWindowCaptureMouseDown", "requestAnimationFrame", "capture", "cancelFrameRequest", "cancelAnimationFrame", "onKeyDown", "stopPropagation", "onNavigation", "commitChanges", "renderFormatter", "cellDragHandle", "cellDragHandleClassname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "latestDraggedOverRowIdx", "onRowsChange", "onFill", "setDragging", "handleMouseDown", "onMouseOver", "onMouseUp", "handleDragEnd", "overRowIdx", "endRowIndex", "updateRows", "startRowIdx", "endRowIdx", "sourceRow", "updatedRows", "indexes", "updatedRow", "targetRow", "arrow", "arrowClassname", "initialPosition", "mode", "DataGrid", "rowKeyGetter", "headerRowHeight", "rawHeaderRowHeight", "summaryRowHeight", "rawSummaryRowHeight", "selectedRows", "onSelectedRowsChange", "onExpandedGroupIdsChange", "onScroll", "onCopy", "onPaste", "rawCellNavigationMode", "components", "style", "ariaDescribedBy", "testId", "defaultComponents", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "noRowsFallback", "setScrollTop", "setScrollLeft", "setColumnWidths", "setSelectedPosition", "copiedCell", "setCopiedCell", "isDragging", "draggedOverRowIdx", "setOverRowIdx", "autoResizeColumn", "setAutoResizeColumn", "prevSelectedPosition", "lastSelectedRowIdx", "rowRef", "gridWidth", "gridHeight", "headerRowsCount", "summaryRowsCount", "isSelectable", "isHeaderRowSelected", "leftKey", "<PERSON><PERSON><PERSON>", "defaultGridComponents", "every", "hasGroups", "minColIdx", "selectedCellIsWithinSelectionBounds", "isCellWithinSelectionBounds", "selectedCellIsWithinViewportBounds", "isCellWithinViewportBounds", "selectRowLatest", "selectRow", "selectAllRowsLatest", "selectAllRows", "handleFormatterRowChangeLatest", "updateRow", "selectViewportCellLatest", "enableEditor", "selectGroupLatest", "selectHeaderCellLatest", "selectSummaryCellLatest", "toggleGroupLatest", "isSamePosition", "columnElement", "querySelector", "newColumnWidths", "useImperativeHandle", "scrollToColumn", "scrollToRow", "scrollTo", "behavior", "handleColumnResize", "newSelectedRows", "childRow", "<PERSON><PERSON><PERSON>", "delete", "previousRowIdx", "step", "expandedGroupId", "newExpandedGroupIds", "Element", "isCellEvent", "closest", "isRowEvent", "keyCode", "c<PERSON><PERSON>", "vKey", "handleCopy", "handlePaste", "isRowIdxWithinViewportBounds", "navigate", "handleCellInput", "handleScroll", "getRawRowIdx", "rawRowIdx", "commitEditorChanges", "sourceColumnKey", "updatedTargetRow", "targetColumnKey", "onCellKeyDown", "isDefaultPrevented", "originalRow", "isColIdxWithinSelectionBounds", "isCellAtLeftBoundary", "isCellAtRightBoundary", "getNextPosition", "parentRowIdx", "parentRow", "nextRowY", "nextSelectedCellPosition", "getDraggedOverCellIdx", "currentRowIdx", "getLayoutCssVars", "newSizes", "split", "join", "getDragHandle", "getCellEditor", "getRowViewportColumns", "selectedColumn", "slice", "getViewportRows", "rowElements", "selectedIdx", "selectedRowIdx", "viewportRowIdx", "isRowOutsideViewport", "rowColumns", "isGroupRowSelected", "cr", "c", "templateRows", "isGroupRowFocused", "scrollPaddingInlineStart", "scrollPaddingBlock", "summaryRowIdx", "isSummaryRowSelected", "p1", "p2", "textEditor", "textEditorClassname", "autoFocusAndSelect", "input", "select", "TextEditor"]}
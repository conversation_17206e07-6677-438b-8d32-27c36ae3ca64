{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/hint/show-hint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n// declare global: DOMRect\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var HINT_ELEMENT_CLASS        = \"CodeMirror-hint\";\n  var ACTIVE_HINT_ELEMENT_CLASS = \"CodeMirror-hint-active\";\n\n  // This is the old interface, kept around for now to stay\n  // backwards-compatible.\n  CodeMirror.showHint = function(cm, getHints, options) {\n    if (!getHints) return cm.showHint(options);\n    if (options && options.async) getHints.async = true;\n    var newOpts = {hint: getHints};\n    if (options) for (var prop in options) newOpts[prop] = options[prop];\n    return cm.showHint(newOpts);\n  };\n\n  CodeMirror.defineExtension(\"showHint\", function(options) {\n    options = parseOptions(this, this.getCursor(\"start\"), options);\n    var selections = this.listSelections()\n    if (selections.length > 1) return;\n    // By default, don't allow completion when something is selected.\n    // A hint function can have a `supportsSelection` property to\n    // indicate that it can handle selections.\n    if (this.somethingSelected()) {\n      if (!options.hint.supportsSelection) return;\n      // Don't try with cross-line selections\n      for (var i = 0; i < selections.length; i++)\n        if (selections[i].head.line != selections[i].anchor.line) return;\n    }\n\n    if (this.state.completionActive) this.state.completionActive.close();\n    var completion = this.state.completionActive = new Completion(this, options);\n    if (!completion.options.hint) return;\n\n    CodeMirror.signal(this, \"startCompletion\", this);\n    completion.update(true);\n  });\n\n  CodeMirror.defineExtension(\"closeHint\", function() {\n    if (this.state.completionActive) this.state.completionActive.close()\n  })\n\n  function Completion(cm, options) {\n    this.cm = cm;\n    this.options = options;\n    this.widget = null;\n    this.debounce = 0;\n    this.tick = 0;\n    this.startPos = this.cm.getCursor(\"start\");\n    this.startLen = this.cm.getLine(this.startPos.line).length - this.cm.getSelection().length;\n\n    if (this.options.updateOnCursorActivity) {\n      var self = this;\n      cm.on(\"cursorActivity\", this.activityFunc = function() { self.cursorActivity(); });\n    }\n  }\n\n  var requestAnimationFrame = window.requestAnimationFrame || function(fn) {\n    return setTimeout(fn, 1000/60);\n  };\n  var cancelAnimationFrame = window.cancelAnimationFrame || clearTimeout;\n\n  Completion.prototype = {\n    close: function() {\n      if (!this.active()) return;\n      this.cm.state.completionActive = null;\n      this.tick = null;\n      if (this.options.updateOnCursorActivity) {\n        this.cm.off(\"cursorActivity\", this.activityFunc);\n      }\n\n      if (this.widget && this.data) CodeMirror.signal(this.data, \"close\");\n      if (this.widget) this.widget.close();\n      CodeMirror.signal(this.cm, \"endCompletion\", this.cm);\n    },\n\n    active: function() {\n      return this.cm.state.completionActive == this;\n    },\n\n    pick: function(data, i) {\n      var completion = data.list[i], self = this;\n      this.cm.operation(function() {\n        if (completion.hint)\n          completion.hint(self.cm, data, completion);\n        else\n          self.cm.replaceRange(getText(completion), completion.from || data.from,\n                               completion.to || data.to, \"complete\");\n        CodeMirror.signal(data, \"pick\", completion);\n        self.cm.scrollIntoView();\n      });\n      if (this.options.closeOnPick) {\n        this.close();\n      }\n    },\n\n    cursorActivity: function() {\n      if (this.debounce) {\n        cancelAnimationFrame(this.debounce);\n        this.debounce = 0;\n      }\n\n      var identStart = this.startPos;\n      if(this.data) {\n        identStart = this.data.from;\n      }\n\n      var pos = this.cm.getCursor(), line = this.cm.getLine(pos.line);\n      if (pos.line != this.startPos.line || line.length - pos.ch != this.startLen - this.startPos.ch ||\n          pos.ch < identStart.ch || this.cm.somethingSelected() ||\n          (!pos.ch || this.options.closeCharacters.test(line.charAt(pos.ch - 1)))) {\n        this.close();\n      } else {\n        var self = this;\n        this.debounce = requestAnimationFrame(function() {self.update();});\n        if (this.widget) this.widget.disable();\n      }\n    },\n\n    update: function(first) {\n      if (this.tick == null) return\n      var self = this, myTick = ++this.tick\n      fetchHints(this.options.hint, this.cm, this.options, function(data) {\n        if (self.tick == myTick) self.finishUpdate(data, first)\n      })\n    },\n\n    finishUpdate: function(data, first) {\n      if (this.data) CodeMirror.signal(this.data, \"update\");\n\n      var picked = (this.widget && this.widget.picked) || (first && this.options.completeSingle);\n      if (this.widget) this.widget.close();\n\n      this.data = data;\n\n      if (data && data.list.length) {\n        if (picked && data.list.length == 1) {\n          this.pick(data, 0);\n        } else {\n          this.widget = new Widget(this, data);\n          CodeMirror.signal(data, \"shown\");\n        }\n      }\n    }\n  };\n\n  function parseOptions(cm, pos, options) {\n    var editor = cm.options.hintOptions;\n    var out = {};\n    for (var prop in defaultOptions) out[prop] = defaultOptions[prop];\n    if (editor) for (var prop in editor)\n      if (editor[prop] !== undefined) out[prop] = editor[prop];\n    if (options) for (var prop in options)\n      if (options[prop] !== undefined) out[prop] = options[prop];\n    if (out.hint.resolve) out.hint = out.hint.resolve(cm, pos)\n    return out;\n  }\n\n  function getText(completion) {\n    if (typeof completion == \"string\") return completion;\n    else return completion.text;\n  }\n\n  function buildKeyMap(completion, handle) {\n    var baseMap = {\n      Up: function() {handle.moveFocus(-1);},\n      Down: function() {handle.moveFocus(1);},\n      PageUp: function() {handle.moveFocus(-handle.menuSize() + 1, true);},\n      PageDown: function() {handle.moveFocus(handle.menuSize() - 1, true);},\n      Home: function() {handle.setFocus(0);},\n      End: function() {handle.setFocus(handle.length - 1);},\n      Enter: handle.pick,\n      Tab: handle.pick,\n      Esc: handle.close\n    };\n\n    var mac = /Mac/.test(navigator.platform);\n\n    if (mac) {\n      baseMap[\"Ctrl-P\"] = function() {handle.moveFocus(-1);};\n      baseMap[\"Ctrl-N\"] = function() {handle.moveFocus(1);};\n    }\n\n    var custom = completion.options.customKeys;\n    var ourMap = custom ? {} : baseMap;\n    function addBinding(key, val) {\n      var bound;\n      if (typeof val != \"string\")\n        bound = function(cm) { return val(cm, handle); };\n      // This mechanism is deprecated\n      else if (baseMap.hasOwnProperty(val))\n        bound = baseMap[val];\n      else\n        bound = val;\n      ourMap[key] = bound;\n    }\n    if (custom)\n      for (var key in custom) if (custom.hasOwnProperty(key))\n        addBinding(key, custom[key]);\n    var extra = completion.options.extraKeys;\n    if (extra)\n      for (var key in extra) if (extra.hasOwnProperty(key))\n        addBinding(key, extra[key]);\n    return ourMap;\n  }\n\n  function getHintElement(hintsElement, el) {\n    while (el && el != hintsElement) {\n      if (el.nodeName.toUpperCase() === \"LI\" && el.parentNode == hintsElement) return el;\n      el = el.parentNode;\n    }\n  }\n\n  function Widget(completion, data) {\n    this.id = \"cm-complete-\" + Math.floor(Math.random(1e6))\n    this.completion = completion;\n    this.data = data;\n    this.picked = false;\n    var widget = this, cm = completion.cm;\n    var ownerDocument = cm.getInputField().ownerDocument;\n    var parentWindow = ownerDocument.defaultView || ownerDocument.parentWindow;\n\n    var hints = this.hints = ownerDocument.createElement(\"ul\");\n    hints.setAttribute(\"role\", \"listbox\")\n    hints.setAttribute(\"aria-expanded\", \"true\")\n    hints.id = this.id\n    var theme = completion.cm.options.theme;\n    hints.className = \"CodeMirror-hints \" + theme;\n    this.selectedHint = data.selectedHint || 0;\n\n    var completions = data.list;\n    for (var i = 0; i < completions.length; ++i) {\n      var elt = hints.appendChild(ownerDocument.createElement(\"li\")), cur = completions[i];\n      var className = HINT_ELEMENT_CLASS + (i != this.selectedHint ? \"\" : \" \" + ACTIVE_HINT_ELEMENT_CLASS);\n      if (cur.className != null) className = cur.className + \" \" + className;\n      elt.className = className;\n      if (i == this.selectedHint) elt.setAttribute(\"aria-selected\", \"true\")\n      elt.id = this.id + \"-\" + i\n      elt.setAttribute(\"role\", \"option\")\n      if (cur.render) cur.render(elt, data, cur);\n      else elt.appendChild(ownerDocument.createTextNode(cur.displayText || getText(cur)));\n      elt.hintId = i;\n    }\n\n    var container = completion.options.container || ownerDocument.body;\n    var pos = cm.cursorCoords(completion.options.alignWithWord ? data.from : null);\n    var left = pos.left, top = pos.bottom, below = true;\n    var offsetLeft = 0, offsetTop = 0;\n    if (container !== ownerDocument.body) {\n      // We offset the cursor position because left and top are relative to the offsetParent's top left corner.\n      var isContainerPositioned = ['absolute', 'relative', 'fixed'].indexOf(parentWindow.getComputedStyle(container).position) !== -1;\n      var offsetParent = isContainerPositioned ? container : container.offsetParent;\n      var offsetParentPosition = offsetParent.getBoundingClientRect();\n      var bodyPosition = ownerDocument.body.getBoundingClientRect();\n      offsetLeft = (offsetParentPosition.left - bodyPosition.left - offsetParent.scrollLeft);\n      offsetTop = (offsetParentPosition.top - bodyPosition.top - offsetParent.scrollTop);\n    }\n    hints.style.left = (left - offsetLeft) + \"px\";\n    hints.style.top = (top - offsetTop) + \"px\";\n\n    // If we're at the edge of the screen, then we want the menu to appear on the left of the cursor.\n    var winW = parentWindow.innerWidth || Math.max(ownerDocument.body.offsetWidth, ownerDocument.documentElement.offsetWidth);\n    var winH = parentWindow.innerHeight || Math.max(ownerDocument.body.offsetHeight, ownerDocument.documentElement.offsetHeight);\n    container.appendChild(hints);\n    cm.getInputField().setAttribute(\"aria-autocomplete\", \"list\")\n    cm.getInputField().setAttribute(\"aria-owns\", this.id)\n    cm.getInputField().setAttribute(\"aria-activedescendant\", this.id + \"-\" + this.selectedHint)\n\n    var box = completion.options.moveOnOverlap ? hints.getBoundingClientRect() : new DOMRect();\n    var scrolls = completion.options.paddingForScrollbar ? hints.scrollHeight > hints.clientHeight + 1 : false;\n\n    // Compute in the timeout to avoid reflow on init\n    var startScroll;\n    setTimeout(function() { startScroll = cm.getScrollInfo(); });\n\n    var overlapY = box.bottom - winH;\n    if (overlapY > 0) {\n      var height = box.bottom - box.top, curTop = pos.top - (pos.bottom - box.top);\n      if (curTop - height > 0) { // Fits above cursor\n        hints.style.top = (top = pos.top - height - offsetTop) + \"px\";\n        below = false;\n      } else if (height > winH) {\n        hints.style.height = (winH - 5) + \"px\";\n        hints.style.top = (top = pos.bottom - box.top - offsetTop) + \"px\";\n        var cursor = cm.getCursor();\n        if (data.from.ch != cursor.ch) {\n          pos = cm.cursorCoords(cursor);\n          hints.style.left = (left = pos.left - offsetLeft) + \"px\";\n          box = hints.getBoundingClientRect();\n        }\n      }\n    }\n    var overlapX = box.right - winW;\n    if (scrolls) overlapX += cm.display.nativeBarWidth;\n    if (overlapX > 0) {\n      if (box.right - box.left > winW) {\n        hints.style.width = (winW - 5) + \"px\";\n        overlapX -= (box.right - box.left) - winW;\n      }\n      hints.style.left = (left = pos.left - overlapX - offsetLeft) + \"px\";\n    }\n    if (scrolls) for (var node = hints.firstChild; node; node = node.nextSibling)\n      node.style.paddingRight = cm.display.nativeBarWidth + \"px\"\n\n    cm.addKeyMap(this.keyMap = buildKeyMap(completion, {\n      moveFocus: function(n, avoidWrap) { widget.changeActive(widget.selectedHint + n, avoidWrap); },\n      setFocus: function(n) { widget.changeActive(n); },\n      menuSize: function() { return widget.screenAmount(); },\n      length: completions.length,\n      close: function() { completion.close(); },\n      pick: function() { widget.pick(); },\n      data: data\n    }));\n\n    if (completion.options.closeOnUnfocus) {\n      var closingOnBlur;\n      cm.on(\"blur\", this.onBlur = function() { closingOnBlur = setTimeout(function() { completion.close(); }, 100); });\n      cm.on(\"focus\", this.onFocus = function() { clearTimeout(closingOnBlur); });\n    }\n\n    cm.on(\"scroll\", this.onScroll = function() {\n      var curScroll = cm.getScrollInfo(), editor = cm.getWrapperElement().getBoundingClientRect();\n      if (!startScroll) startScroll = cm.getScrollInfo();\n      var newTop = top + startScroll.top - curScroll.top;\n      var point = newTop - (parentWindow.pageYOffset || (ownerDocument.documentElement || ownerDocument.body).scrollTop);\n      if (!below) point += hints.offsetHeight;\n      if (point <= editor.top || point >= editor.bottom) return completion.close();\n      hints.style.top = newTop + \"px\";\n      hints.style.left = (left + startScroll.left - curScroll.left) + \"px\";\n    });\n\n    CodeMirror.on(hints, \"dblclick\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {widget.changeActive(t.hintId); widget.pick();}\n    });\n\n    CodeMirror.on(hints, \"click\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {\n        widget.changeActive(t.hintId);\n        if (completion.options.completeOnSingleClick) widget.pick();\n      }\n    });\n\n    CodeMirror.on(hints, \"mousedown\", function() {\n      setTimeout(function(){cm.focus();}, 20);\n    });\n\n    // The first hint doesn't need to be scrolled to on init\n    var selectedHintRange = this.getSelectedHintRange();\n    if (selectedHintRange.from !== 0 || selectedHintRange.to !== 0) {\n      this.scrollToActive();\n    }\n\n    CodeMirror.signal(data, \"select\", completions[this.selectedHint], hints.childNodes[this.selectedHint]);\n    return true;\n  }\n\n  Widget.prototype = {\n    close: function() {\n      if (this.completion.widget != this) return;\n      this.completion.widget = null;\n      if (this.hints.parentNode) this.hints.parentNode.removeChild(this.hints);\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var input = this.completion.cm.getInputField()\n      input.removeAttribute(\"aria-activedescendant\")\n      input.removeAttribute(\"aria-owns\")\n\n      var cm = this.completion.cm;\n      if (this.completion.options.closeOnUnfocus) {\n        cm.off(\"blur\", this.onBlur);\n        cm.off(\"focus\", this.onFocus);\n      }\n      cm.off(\"scroll\", this.onScroll);\n    },\n\n    disable: function() {\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var widget = this;\n      this.keyMap = {Enter: function() { widget.picked = true; }};\n      this.completion.cm.addKeyMap(this.keyMap);\n    },\n\n    pick: function() {\n      this.completion.pick(this.data, this.selectedHint);\n    },\n\n    changeActive: function(i, avoidWrap) {\n      if (i >= this.data.list.length)\n        i = avoidWrap ? this.data.list.length - 1 : 0;\n      else if (i < 0)\n        i = avoidWrap ? 0  : this.data.list.length - 1;\n      if (this.selectedHint == i) return;\n      var node = this.hints.childNodes[this.selectedHint];\n      if (node) {\n        node.className = node.className.replace(\" \" + ACTIVE_HINT_ELEMENT_CLASS, \"\");\n        node.removeAttribute(\"aria-selected\")\n      }\n      node = this.hints.childNodes[this.selectedHint = i];\n      node.className += \" \" + ACTIVE_HINT_ELEMENT_CLASS;\n      node.setAttribute(\"aria-selected\", \"true\")\n      this.completion.cm.getInputField().setAttribute(\"aria-activedescendant\", node.id)\n      this.scrollToActive()\n      CodeMirror.signal(this.data, \"select\", this.data.list[this.selectedHint], node);\n    },\n\n    scrollToActive: function() {\n      var selectedHintRange = this.getSelectedHintRange();\n      var node1 = this.hints.childNodes[selectedHintRange.from];\n      var node2 = this.hints.childNodes[selectedHintRange.to];\n      var firstNode = this.hints.firstChild;\n      if (node1.offsetTop < this.hints.scrollTop)\n        this.hints.scrollTop = node1.offsetTop - firstNode.offsetTop;\n      else if (node2.offsetTop + node2.offsetHeight > this.hints.scrollTop + this.hints.clientHeight)\n        this.hints.scrollTop = node2.offsetTop + node2.offsetHeight - this.hints.clientHeight + firstNode.offsetTop;\n    },\n\n    screenAmount: function() {\n      return Math.floor(this.hints.clientHeight / this.hints.firstChild.offsetHeight) || 1;\n    },\n\n    getSelectedHintRange: function() {\n      var margin = this.completion.options.scrollMargin || 0;\n      return {\n        from: Math.max(0, this.selectedHint - margin),\n        to: Math.min(this.data.list.length - 1, this.selectedHint + margin),\n      };\n    }\n  };\n\n  function applicableHelpers(cm, helpers) {\n    if (!cm.somethingSelected()) return helpers\n    var result = []\n    for (var i = 0; i < helpers.length; i++)\n      if (helpers[i].supportsSelection) result.push(helpers[i])\n    return result\n  }\n\n  function fetchHints(hint, cm, options, callback) {\n    if (hint.async) {\n      hint(cm, callback, options)\n    } else {\n      var result = hint(cm, options)\n      if (result && result.then) result.then(callback)\n      else callback(result)\n    }\n  }\n\n  function resolveAutoHints(cm, pos) {\n    var helpers = cm.getHelpers(pos, \"hint\"), words\n    if (helpers.length) {\n      var resolved = function(cm, callback, options) {\n        var app = applicableHelpers(cm, helpers);\n        function run(i) {\n          if (i == app.length) return callback(null)\n          fetchHints(app[i], cm, options, function(result) {\n            if (result && result.list.length > 0) callback(result)\n            else run(i + 1)\n          })\n        }\n        run(0)\n      }\n      resolved.async = true\n      resolved.supportsSelection = true\n      return resolved\n    } else if (words = cm.getHelper(cm.getCursor(), \"hintWords\")) {\n      return function(cm) { return CodeMirror.hint.fromList(cm, {words: words}) }\n    } else if (CodeMirror.hint.anyword) {\n      return function(cm, options) { return CodeMirror.hint.anyword(cm, options) }\n    } else {\n      return function() {}\n    }\n  }\n\n  CodeMirror.registerHelper(\"hint\", \"auto\", {\n    resolve: resolveAutoHints\n  });\n\n  CodeMirror.registerHelper(\"hint\", \"fromList\", function(cm, options) {\n    var cur = cm.getCursor(), token = cm.getTokenAt(cur)\n    var term, from = CodeMirror.Pos(cur.line, token.start), to = cur\n    if (token.start < cur.ch && /\\w/.test(token.string.charAt(cur.ch - token.start - 1))) {\n      term = token.string.substr(0, cur.ch - token.start)\n    } else {\n      term = \"\"\n      from = cur\n    }\n    var found = [];\n    for (var i = 0; i < options.words.length; i++) {\n      var word = options.words[i];\n      if (word.slice(0, term.length) == term)\n        found.push(word);\n    }\n\n    if (found.length) return {list: found, from: from, to: to};\n  });\n\n  CodeMirror.commands.autocomplete = CodeMirror.showHint;\n\n  var defaultOptions = {\n    hint: CodeMirror.hint.auto,\n    completeSingle: true,\n    alignWithWord: true,\n    closeCharacters: /[\\s()\\[\\]{};:>,]/,\n    closeOnPick: true,\n    closeOnUnfocus: true,\n    updateOnCursorActivity: true,\n    completeOnSingleClick: true,\n    container: null,\n    customKeys: null,\n    extraKeys: null,\n    paddingForScrollbar: true,\n    moveOnOverlap: true,\n  };\n\n  CodeMirror.defineOption(\"hintOptions\", null);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA+B,CAAA;EAKtC,GAAE,SAAS,YAAY;AAGtB,QAAI,qBAA4B;AAChC,QAAI,4BAA4B;AAIhC,eAAW,WAAW,SAAS,IAAI,UAAU,SAAS;AACpD,UAAI,CAAC,SAAU,QAAO,GAAG,SAAS,OAAO;AACzC,UAAI,WAAW,QAAQ,MAAO,UAAS,QAAQ;AAC/C,UAAI,UAAU,EAAC,MAAM,SAAQ;AAC7B,UAAI,QAAS,UAAS,QAAQ,QAAS,SAAQ,IAAI,IAAI,QAAQ,IAAI;AACnE,aAAO,GAAG,SAAS,OAAO;IAC9B;AAEE,eAAW,gBAAgB,YAAY,SAAS,SAAS;AACvD,gBAAU,aAAa,MAAM,KAAK,UAAU,OAAO,GAAG,OAAO;AAC7D,UAAI,aAAa,KAAK,eAAgB;AACtC,UAAI,WAAW,SAAS,EAAG;AAI3B,UAAI,KAAK,kBAAA,GAAqB;AAC5B,YAAI,CAAC,QAAQ,KAAK,kBAAmB;AAErC,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ;AACrC,cAAI,WAAW,CAAC,EAAE,KAAK,QAAQ,WAAW,CAAC,EAAE,OAAO,KAAM;MAC7D;AAED,UAAI,KAAK,MAAM,iBAAkB,MAAK,MAAM,iBAAiB,MAAA;AAC7D,UAAI,aAAa,KAAK,MAAM,mBAAmB,IAAI,WAAW,MAAM,OAAO;AAC3E,UAAI,CAAC,WAAW,QAAQ,KAAM;AAE9B,iBAAW,OAAO,MAAM,mBAAmB,IAAI;AAC/C,iBAAW,OAAO,IAAI;IAC1B,CAAG;AAED,eAAW,gBAAgB,aAAa,WAAW;AACjD,UAAI,KAAK,MAAM,iBAAkB,MAAK,MAAM,iBAAiB,MAAO;IACxE,CAAG;AAED,aAAS,WAAW,IAAI,SAAS;AAC/B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,WAAW,KAAK,GAAG,UAAU,OAAO;AACzC,WAAK,WAAW,KAAK,GAAG,QAAQ,KAAK,SAAS,IAAI,EAAE,SAAS,KAAK,GAAG,aAAY,EAAG;AAEpF,UAAI,KAAK,QAAQ,wBAAwB;AACvC,YAAI,OAAO;AACX,WAAG,GAAG,kBAAkB,KAAK,eAAe,WAAW;AAAE,eAAK,eAAA;QAAiB,CAAE;MAClF;IACF;AAED,QAAI,wBAAwB,OAAO,yBAAyB,SAAS,IAAI;AACvE,aAAO,WAAW,IAAI,MAAK,EAAE;IACjC;AACE,QAAI,uBAAuB,OAAO,wBAAwB;AAE1D,eAAW,YAAY;MACrB,OAAO,WAAW;AAChB,YAAI,CAAC,KAAK,OAAM,EAAI;AACpB,aAAK,GAAG,MAAM,mBAAmB;AACjC,aAAK,OAAO;AACZ,YAAI,KAAK,QAAQ,wBAAwB;AACvC,eAAK,GAAG,IAAI,kBAAkB,KAAK,YAAY;QAChD;AAED,YAAI,KAAK,UAAU,KAAK,KAAM,YAAW,OAAO,KAAK,MAAM,OAAO;AAClE,YAAI,KAAK,OAAQ,MAAK,OAAO,MAAK;AAClC,mBAAW,OAAO,KAAK,IAAI,iBAAiB,KAAK,EAAE;MACpD;MAED,QAAQ,WAAW;AACjB,eAAO,KAAK,GAAG,MAAM,oBAAoB;MAC1C;MAED,MAAM,SAAS,MAAM,GAAG;AACtB,YAAI,aAAa,KAAK,KAAK,CAAC,GAAG,OAAO;AACtC,aAAK,GAAG,UAAU,WAAW;AAC3B,cAAI,WAAW;AACb,uBAAW,KAAK,KAAK,IAAI,MAAM,UAAU;;AAEzC,iBAAK,GAAG;cAAa,QAAQ,UAAU;cAAG,WAAW,QAAQ,KAAK;cAC7C,WAAW,MAAM,KAAK;cAAI;YAAU;AAC3D,qBAAW,OAAO,MAAM,QAAQ,UAAU;AAC1C,eAAK,GAAG,eAAA;QAChB,CAAO;AACD,YAAI,KAAK,QAAQ,aAAa;AAC5B,eAAK,MAAK;QACX;MACF;MAED,gBAAgB,WAAW;AACzB,YAAI,KAAK,UAAU;AACjB,+BAAqB,KAAK,QAAQ;AAClC,eAAK,WAAW;QACjB;AAED,YAAI,aAAa,KAAK;AACtB,YAAG,KAAK,MAAM;AACZ,uBAAa,KAAK,KAAK;QACxB;AAED,YAAI,MAAM,KAAK,GAAG,UAAS,GAAI,OAAO,KAAK,GAAG,QAAQ,IAAI,IAAI;AAC9D,YAAI,IAAI,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,IAAI,MAAM,KAAK,WAAW,KAAK,SAAS,MACxF,IAAI,KAAK,WAAW,MAAM,KAAK,GAAG,kBAAmB,MACpD,CAAC,IAAI,MAAM,KAAK,QAAQ,gBAAgB,KAAK,KAAK,OAAO,IAAI,KAAK,CAAC,CAAC,IAAI;AAC3E,eAAK,MAAK;QAClB,OAAa;AACL,cAAI,OAAO;AACX,eAAK,WAAW,sBAAsB,WAAW;AAAC,iBAAK,OAAM;UAAG,CAAC;AACjE,cAAI,KAAK,OAAQ,MAAK,OAAO,QAAO;QACrC;MACF;MAED,QAAQ,SAAS,OAAO;AACtB,YAAI,KAAK,QAAQ,KAAM;AACvB,YAAI,OAAO,MAAM,SAAS,EAAE,KAAK;AACjC,mBAAW,KAAK,QAAQ,MAAM,KAAK,IAAI,KAAK,SAAS,SAAS,MAAM;AAClE,cAAI,KAAK,QAAQ,OAAQ,MAAK,aAAa,MAAM,KAAK;QAC9D,CAAO;MACF;MAED,cAAc,SAAS,MAAM,OAAO;AAClC,YAAI,KAAK,KAAM,YAAW,OAAO,KAAK,MAAM,QAAQ;AAEpD,YAAI,SAAU,KAAK,UAAU,KAAK,OAAO,UAAY,SAAS,KAAK,QAAQ;AAC3E,YAAI,KAAK,OAAQ,MAAK,OAAO,MAAK;AAElC,aAAK,OAAO;AAEZ,YAAI,QAAQ,KAAK,KAAK,QAAQ;AAC5B,cAAI,UAAU,KAAK,KAAK,UAAU,GAAG;AACnC,iBAAK,KAAK,MAAM,CAAC;UAC3B,OAAe;AACL,iBAAK,SAAS,IAAI,OAAO,MAAM,IAAI;AACnC,uBAAW,OAAO,MAAM,OAAO;UAChC;QACF;MACF;IACL;AAEE,aAAS,aAAa,IAAI,KAAK,SAAS;AACtC,UAAI,SAAS,GAAG,QAAQ;AACxB,UAAI,MAAM,CAAA;AACV,eAAS,QAAQ,eAAgB,KAAI,IAAI,IAAI,eAAe,IAAI;AAChE,UAAI,QAAA;AAAQ,iBAAS,QAAQ;AAC3B,cAAI,OAAO,IAAI,MAAM,OAAW,KAAI,IAAI,IAAI,OAAO,IAAI;MAAA;AACzD,UAAI,SAAA;AAAS,iBAAS,QAAQ;AAC5B,cAAI,QAAQ,IAAI,MAAM,OAAW,KAAI,IAAI,IAAI,QAAQ,IAAI;MAAA;AAC3D,UAAI,IAAI,KAAK,QAAS,KAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,GAAG;AACzD,aAAO;IACR;AAED,aAAS,QAAQ,YAAY;AAC3B,UAAI,OAAO,cAAc,SAAU,QAAO;UACrC,QAAO,WAAW;IACxB;AAED,aAAS,YAAY,YAAY,QAAQ;AACvC,UAAI,UAAU;QACZ,IAAI,WAAW;AAAC,iBAAO,UAAU,EAAE;QAAE;QACrC,MAAM,WAAW;AAAC,iBAAO,UAAU,CAAC;QAAE;QACtC,QAAQ,WAAW;AAAC,iBAAO,UAAU,CAAC,OAAO,SAAA,IAAa,GAAG,IAAI;QAAE;QACnE,UAAU,WAAW;AAAC,iBAAO,UAAU,OAAO,SAAA,IAAa,GAAG,IAAI;QAAE;QACpE,MAAM,WAAW;AAAC,iBAAO,SAAS,CAAC;QAAE;QACrC,KAAK,WAAW;AAAC,iBAAO,SAAS,OAAO,SAAS,CAAC;QAAE;QACpD,OAAO,OAAO;QACd,KAAK,OAAO;QACZ,KAAK,OAAO;MAClB;AAEI,UAAI,MAAM,MAAM,KAAK,UAAU,QAAQ;AAEvC,UAAI,KAAK;AACP,gBAAQ,QAAQ,IAAI,WAAW;AAAC,iBAAO,UAAU,EAAE;QAAE;AACrD,gBAAQ,QAAQ,IAAI,WAAW;AAAC,iBAAO,UAAU,CAAC;QAAE;MACrD;AAED,UAAI,SAAS,WAAW,QAAQ;AAChC,UAAI,SAAS,SAAS,CAAE,IAAG;AAC3B,eAAS,WAAWC,MAAK,KAAK;AAC5B,YAAI;AACJ,YAAI,OAAO,OAAO;AAChB,kBAAQ,SAAS,IAAI;AAAE,mBAAO,IAAI,IAAI,MAAM;UAAA;iBAErC,QAAQ,eAAe,GAAG;AACjC,kBAAQ,QAAQ,GAAG;;AAEnB,kBAAQ;AACV,eAAOA,IAAG,IAAI;MACf;AACD,UAAI,QAAA;AACF,iBAAS,OAAO,OAAQ,KAAI,OAAO,eAAe,GAAG;AACnD,qBAAW,KAAK,OAAO,GAAG,CAAC;MAAA;AAC/B,UAAI,QAAQ,WAAW,QAAQ;AAC/B,UAAI,OAAA;AACF,iBAAS,OAAO,MAAO,KAAI,MAAM,eAAe,GAAG;AACjD,qBAAW,KAAK,MAAM,GAAG,CAAC;MAAA;AAC9B,aAAO;IACR;AAED,aAAS,eAAe,cAAc,IAAI;AACxC,aAAO,MAAM,MAAM,cAAc;AAC/B,YAAI,GAAG,SAAS,YAAA,MAAkB,QAAQ,GAAG,cAAc,aAAc,QAAO;AAChF,aAAK,GAAG;MACT;IACF;AAED,aAAS,OAAO,YAAY,MAAM;AAChC,WAAK,KAAK,iBAAiB,KAAK,MAAM,KAAK,OAAO,GAAG,CAAC;AACtD,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,UAAI,SAAS,MAAM,KAAK,WAAW;AACnC,UAAI,gBAAgB,GAAG,cAAa,EAAG;AACvC,UAAI,eAAe,cAAc,eAAe,cAAc;AAE9D,UAAI,QAAQ,KAAK,QAAQ,cAAc,cAAc,IAAI;AACzD,YAAM,aAAa,QAAQ,SAAS;AACpC,YAAM,aAAa,iBAAiB,MAAM;AAC1C,YAAM,KAAK,KAAK;AAChB,UAAI,QAAQ,WAAW,GAAG,QAAQ;AAClC,YAAM,YAAY,sBAAsB;AACxC,WAAK,eAAe,KAAK,gBAAgB;AAEzC,UAAI,cAAc,KAAK;AACvB,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,EAAE,GAAG;AAC3C,YAAI,MAAM,MAAM,YAAY,cAAc,cAAc,IAAI,CAAC,GAAG,MAAM,YAAY,CAAC;AACnF,YAAI,YAAY,sBAAsB,KAAK,KAAK,eAAe,KAAK,MAAM;AAC1E,YAAI,IAAI,aAAa,KAAM,aAAY,IAAI,YAAY,MAAM;AAC7D,YAAI,YAAY;AAChB,YAAI,KAAK,KAAK,aAAc,KAAI,aAAa,iBAAiB,MAAM;AACpE,YAAI,KAAK,KAAK,KAAK,MAAM;AACzB,YAAI,aAAa,QAAQ,QAAQ;AACjC,YAAI,IAAI,OAAQ,KAAI,OAAO,KAAK,MAAM,GAAG;YACpC,KAAI,YAAY,cAAc,eAAe,IAAI,eAAe,QAAQ,GAAG,CAAC,CAAC;AAClF,YAAI,SAAS;MACd;AAED,UAAI,YAAY,WAAW,QAAQ,aAAa,cAAc;AAC9D,UAAI,MAAM,GAAG,aAAa,WAAW,QAAQ,gBAAgB,KAAK,OAAO,IAAI;AAC7E,UAAI,OAAO,IAAI,MAAM,MAAM,IAAI,QAAQ,QAAQ;AAC/C,UAAI,aAAa,GAAG,YAAY;AAChC,UAAI,cAAc,cAAc,MAAM;AAEpC,YAAI,wBAAwB,CAAC,YAAY,YAAY,OAAO,EAAE,QAAQ,aAAa,iBAAiB,SAAS,EAAE,QAAQ,MAAM;AAC7H,YAAI,eAAe,wBAAwB,YAAY,UAAU;AACjE,YAAI,uBAAuB,aAAa,sBAAA;AACxC,YAAI,eAAe,cAAc,KAAK,sBAAqB;AAC3D,qBAAc,qBAAqB,OAAO,aAAa,OAAO,aAAa;AAC3E,oBAAa,qBAAqB,MAAM,aAAa,MAAM,aAAa;MACzE;AACD,YAAM,MAAM,OAAQ,OAAO,aAAc;AACzC,YAAM,MAAM,MAAO,MAAM,YAAa;AAGtC,UAAI,OAAO,aAAa,cAAc,KAAK,IAAI,cAAc,KAAK,aAAa,cAAc,gBAAgB,WAAW;AACxH,UAAI,OAAO,aAAa,eAAe,KAAK,IAAI,cAAc,KAAK,cAAc,cAAc,gBAAgB,YAAY;AAC3H,gBAAU,YAAY,KAAK;AAC3B,SAAG,cAAe,EAAC,aAAa,qBAAqB,MAAM;AAC3D,SAAG,cAAa,EAAG,aAAa,aAAa,KAAK,EAAE;AACpD,SAAG,cAAA,EAAgB,aAAa,yBAAyB,KAAK,KAAK,MAAM,KAAK,YAAY;AAE1F,UAAI,MAAM,WAAW,QAAQ,gBAAgB,MAAM,sBAAqB,IAAK,IAAI,QAAA;AACjF,UAAI,UAAU,WAAW,QAAQ,sBAAsB,MAAM,eAAe,MAAM,eAAe,IAAI;AAGrG,UAAI;AACJ,iBAAW,WAAW;AAAE,sBAAc,GAAG,cAAa;MAAG,CAAE;AAE3D,UAAI,WAAW,IAAI,SAAS;AAC5B,UAAI,WAAW,GAAG;AAChB,YAAI,SAAS,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI;AACxE,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,MAAM,OAAO,MAAM,IAAI,MAAM,SAAS,aAAa;AACzD,kBAAQ;QAChB,WAAiB,SAAS,MAAM;AACxB,gBAAM,MAAM,SAAU,OAAO,IAAK;AAClC,gBAAM,MAAM,OAAO,MAAM,IAAI,SAAS,IAAI,MAAM,aAAa;AAC7D,cAAI,SAAS,GAAG,UAAA;AAChB,cAAI,KAAK,KAAK,MAAM,OAAO,IAAI;AAC7B,kBAAM,GAAG,aAAa,MAAM;AAC5B,kBAAM,MAAM,QAAQ,OAAO,IAAI,OAAO,cAAc;AACpD,kBAAM,MAAM,sBAAA;UACb;QACF;MACF;AACD,UAAI,WAAW,IAAI,QAAQ;AAC3B,UAAI,QAAS,aAAY,GAAG,QAAQ;AACpC,UAAI,WAAW,GAAG;AAChB,YAAI,IAAI,QAAQ,IAAI,OAAO,MAAM;AAC/B,gBAAM,MAAM,QAAS,OAAO,IAAK;AACjC,sBAAa,IAAI,QAAQ,IAAI,OAAQ;QACtC;AACD,cAAM,MAAM,QAAQ,OAAO,IAAI,OAAO,WAAW,cAAc;MAChE;AACD,UAAI,QAAS,UAAS,OAAO,MAAM,YAAY,MAAM,OAAO,KAAK;AAC/D,aAAK,MAAM,eAAe,GAAG,QAAQ,iBAAiB;AAExD,SAAG,UAAU,KAAK,SAAS,YAAY,YAAY;QACjD,WAAW,SAAS,GAAG,WAAW;AAAE,iBAAO,aAAa,OAAO,eAAe,GAAG,SAAS;QAAI;QAC9F,UAAU,SAAS,GAAG;AAAE,iBAAO,aAAa,CAAC;QAAI;QACjD,UAAU,WAAW;AAAE,iBAAO,OAAO,aAAc;QAAG;QACtD,QAAQ,YAAY;QACpB,OAAO,WAAW;AAAE,qBAAW,MAAO;QAAG;QACzC,MAAM,WAAW;AAAE,iBAAO,KAAM;QAAG;QACnC;MACD,CAAA,CAAC;AAEF,UAAI,WAAW,QAAQ,gBAAgB;AACrC,YAAI;AACJ,WAAG,GAAG,QAAQ,KAAK,SAAS,WAAW;AAAE,0BAAgB,WAAW,WAAW;AAAE,uBAAW,MAAO;UAAG,GAAE,GAAG;QAAE,CAAE;AAC/G,WAAG,GAAG,SAAS,KAAK,UAAU,WAAW;AAAE,uBAAa,aAAa;QAAE,CAAE;MAC1E;AAED,SAAG,GAAG,UAAU,KAAK,WAAW,WAAW;AACzC,YAAI,YAAY,GAAG,cAAe,GAAE,SAAS,GAAG,kBAAA,EAAoB,sBAAA;AACpE,YAAI,CAAC,YAAa,eAAc,GAAG,cAAa;AAChD,YAAI,SAAS,MAAM,YAAY,MAAM,UAAU;AAC/C,YAAI,QAAQ,UAAU,aAAa,gBAAgB,cAAc,mBAAmB,cAAc,MAAM;AACxG,YAAI,CAAC,MAAO,UAAS,MAAM;AAC3B,YAAI,SAAS,OAAO,OAAO,SAAS,OAAO,OAAQ,QAAO,WAAW,MAAA;AACrE,cAAM,MAAM,MAAM,SAAS;AAC3B,cAAM,MAAM,OAAQ,OAAO,YAAY,OAAO,UAAU,OAAQ;MACtE,CAAK;AAED,iBAAW,GAAG,OAAO,YAAY,SAAS,GAAG;AAC3C,YAAI,IAAI,eAAe,OAAO,EAAE,UAAU,EAAE,UAAU;AACtD,YAAI,KAAK,EAAE,UAAU,MAAM;AAAC,iBAAO,aAAa,EAAE,MAAM;AAAG,iBAAO,KAAM;QAAC;MAC/E,CAAK;AAED,iBAAW,GAAG,OAAO,SAAS,SAAS,GAAG;AACxC,YAAI,IAAI,eAAe,OAAO,EAAE,UAAU,EAAE,UAAU;AACtD,YAAI,KAAK,EAAE,UAAU,MAAM;AACzB,iBAAO,aAAa,EAAE,MAAM;AAC5B,cAAI,WAAW,QAAQ,sBAAuB,QAAO,KAAI;QAC1D;MACP,CAAK;AAED,iBAAW,GAAG,OAAO,aAAa,WAAW;AAC3C,mBAAW,WAAU;AAAC,aAAG,MAAK;QAAG,GAAG,EAAE;MAC5C,CAAK;AAGD,UAAI,oBAAoB,KAAK,qBAAA;AAC7B,UAAI,kBAAkB,SAAS,KAAK,kBAAkB,OAAO,GAAG;AAC9D,aAAK,eAAc;MACpB;AAED,iBAAW,OAAO,MAAM,UAAU,YAAY,KAAK,YAAY,GAAG,MAAM,WAAW,KAAK,YAAY,CAAC;AACrG,aAAO;IACR;AAED,WAAO,YAAY;MACjB,OAAO,WAAW;AAChB,YAAI,KAAK,WAAW,UAAU,KAAM;AACpC,aAAK,WAAW,SAAS;AACzB,YAAI,KAAK,MAAM,WAAY,MAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AACvE,aAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,YAAI,QAAQ,KAAK,WAAW,GAAG,cAAe;AAC9C,cAAM,gBAAgB,uBAAuB;AAC7C,cAAM,gBAAgB,WAAW;AAEjC,YAAI,KAAK,KAAK,WAAW;AACzB,YAAI,KAAK,WAAW,QAAQ,gBAAgB;AAC1C,aAAG,IAAI,QAAQ,KAAK,MAAM;AAC1B,aAAG,IAAI,SAAS,KAAK,OAAO;QAC7B;AACD,WAAG,IAAI,UAAU,KAAK,QAAQ;MAC/B;MAED,SAAS,WAAW;AAClB,aAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,YAAI,SAAS;AACb,aAAK,SAAS,EAAC,OAAO,WAAW;AAAE,iBAAO,SAAS;QAAK,EAAE;AAC1D,aAAK,WAAW,GAAG,UAAU,KAAK,MAAM;MACzC;MAED,MAAM,WAAW;AACf,aAAK,WAAW,KAAK,KAAK,MAAM,KAAK,YAAY;MAClD;MAED,cAAc,SAAS,GAAG,WAAW;AACnC,YAAI,KAAK,KAAK,KAAK,KAAK;AACtB,cAAI,YAAY,KAAK,KAAK,KAAK,SAAS,IAAI;iBACrC,IAAI;AACX,cAAI,YAAY,IAAK,KAAK,KAAK,KAAK,SAAS;AAC/C,YAAI,KAAK,gBAAgB,EAAG;AAC5B,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,YAAY;AAClD,YAAI,MAAM;AACR,eAAK,YAAY,KAAK,UAAU,QAAQ,MAAM,2BAA2B,EAAE;AAC3E,eAAK,gBAAgB,eAAe;QACrC;AACD,eAAO,KAAK,MAAM,WAAW,KAAK,eAAe,CAAC;AAClD,aAAK,aAAa,MAAM;AACxB,aAAK,aAAa,iBAAiB,MAAM;AACzC,aAAK,WAAW,GAAG,cAAa,EAAG,aAAa,yBAAyB,KAAK,EAAE;AAChF,aAAK,eAAgB;AACrB,mBAAW,OAAO,KAAK,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,YAAY,GAAG,IAAI;MAC/E;MAED,gBAAgB,WAAW;AACzB,YAAI,oBAAoB,KAAK,qBAAA;AAC7B,YAAI,QAAQ,KAAK,MAAM,WAAW,kBAAkB,IAAI;AACxD,YAAI,QAAQ,KAAK,MAAM,WAAW,kBAAkB,EAAE;AACtD,YAAI,YAAY,KAAK,MAAM;AAC3B,YAAI,MAAM,YAAY,KAAK,MAAM;AAC/B,eAAK,MAAM,YAAY,MAAM,YAAY,UAAU;iBAC5C,MAAM,YAAY,MAAM,eAAe,KAAK,MAAM,YAAY,KAAK,MAAM;AAChF,eAAK,MAAM,YAAY,MAAM,YAAY,MAAM,eAAe,KAAK,MAAM,eAAe,UAAU;MACrG;MAED,cAAc,WAAW;AACvB,eAAO,KAAK,MAAM,KAAK,MAAM,eAAe,KAAK,MAAM,WAAW,YAAY,KAAK;MACpF;MAED,sBAAsB,WAAW;AAC/B,YAAI,SAAS,KAAK,WAAW,QAAQ,gBAAgB;AACrD,eAAO;UACL,MAAM,KAAK,IAAI,GAAG,KAAK,eAAe,MAAM;UAC5C,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,GAAG,KAAK,eAAe,MAAM;QAC1E;MACK;IACL;AAEE,aAAS,kBAAkB,IAAI,SAAS;AACtC,UAAI,CAAC,GAAG,kBAAmB,EAAE,QAAO;AACpC,UAAI,SAAS,CAAE;AACf,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,YAAI,QAAQ,CAAC,EAAE,kBAAmB,QAAO,KAAK,QAAQ,CAAC,CAAC;AAC1D,aAAO;IACR;AAED,aAAS,WAAW,MAAM,IAAI,SAAS,UAAU;AAC/C,UAAI,KAAK,OAAO;AACd,aAAK,IAAI,UAAU,OAAO;MAChC,OAAW;AACL,YAAI,SAAS,KAAK,IAAI,OAAO;AAC7B,YAAI,UAAU,OAAO,KAAM,QAAO,KAAK,QAAQ;YAC1C,UAAS,MAAM;MACrB;IACF;AAED,aAAS,iBAAiB,IAAI,KAAK;AACjC,UAAI,UAAU,GAAG,WAAW,KAAK,MAAM,GAAG;AAC1C,UAAI,QAAQ,QAAQ;AAClB,YAAI,WAAW,SAASC,KAAI,UAAU,SAAS;AAC7C,cAAI,MAAM,kBAAkBA,KAAI,OAAO;AACvC,mBAAS,IAAI,GAAG;AACd,gBAAI,KAAK,IAAI,OAAQ,QAAO,SAAS,IAAI;AACzC,uBAAW,IAAI,CAAC,GAAGA,KAAI,SAAS,SAAS,QAAQ;AAC/C,kBAAI,UAAU,OAAO,KAAK,SAAS,EAAG,UAAS,MAAM;kBAChD,KAAI,IAAI,CAAC;YAC1B,CAAW;UACF;AACD,cAAI,CAAC;QACN;AACD,iBAAS,QAAQ;AACjB,iBAAS,oBAAoB;AAC7B,eAAO;MACb,WAAe,QAAQ,GAAG,UAAU,GAAG,UAAS,GAAI,WAAW,GAAG;AAC5D,eAAO,SAASA,KAAI;AAAE,iBAAO,WAAW,KAAK,SAASA,KAAI,EAAC,MAAY,CAAC;QAAG;MACjF,WAAe,WAAW,KAAK,SAAS;AAClC,eAAO,SAASA,KAAI,SAAS;AAAE,iBAAO,WAAW,KAAK,QAAQA,KAAI,OAAO;QAAG;MAClF,OAAW;AACL,eAAO,WAAW;QAAE;MACrB;IACF;AAED,eAAW,eAAe,QAAQ,QAAQ;MACxC,SAAS;IACb,CAAG;AAED,eAAW,eAAe,QAAQ,YAAY,SAAS,IAAI,SAAS;AAClE,UAAI,MAAM,GAAG,UAAS,GAAI,QAAQ,GAAG,WAAW,GAAG;AACnD,UAAI,MAAM,OAAO,WAAW,IAAI,IAAI,MAAM,MAAM,KAAK,GAAG,KAAK;AAC7D,UAAI,MAAM,QAAQ,IAAI,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AACpF,eAAO,MAAM,OAAO,OAAO,GAAG,IAAI,KAAK,MAAM,KAAK;MACxD,OAAW;AACL,eAAO;AACP,eAAO;MACR;AACD,UAAI,QAAQ,CAAA;AACZ,eAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC7C,YAAI,OAAO,QAAQ,MAAM,CAAC;AAC1B,YAAI,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK;AAChC,gBAAM,KAAK,IAAI;MAClB;AAED,UAAI,MAAM,OAAQ,QAAO,EAAC,MAAM,OAAO,MAAY,GAAM;IAC7D,CAAG;AAED,eAAW,SAAS,eAAe,WAAW;AAE9C,QAAI,iBAAiB;MACnB,MAAM,WAAW,KAAK;MACtB,gBAAgB;MAChB,eAAe;MACf,iBAAiB;MACjB,aAAa;MACb,gBAAgB;MAChB,wBAAwB;MACxB,uBAAuB;MACvB,WAAW;MACX,YAAY;MACZ,WAAW;MACX,qBAAqB;MACrB,eAAe;IACnB;AAEE,eAAW,aAAa,eAAe,IAAI;EAC7C,CAAC;;;;;;;;", "names": ["require$$0", "key", "cm"]}
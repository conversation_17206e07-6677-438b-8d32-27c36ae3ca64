{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/csp/csp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csp/csp.ts\nvar conf = {\n  brackets: [],\n  autoClosingPairs: [],\n  surroundingPairs: []\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  keywords: [],\n  typeKeywords: [],\n  tokenPostfix: \".csp\",\n  operators: [],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/child-src/, \"string.quote\"],\n      [/connect-src/, \"string.quote\"],\n      [/default-src/, \"string.quote\"],\n      [/font-src/, \"string.quote\"],\n      [/frame-src/, \"string.quote\"],\n      [/img-src/, \"string.quote\"],\n      [/manifest-src/, \"string.quote\"],\n      [/media-src/, \"string.quote\"],\n      [/object-src/, \"string.quote\"],\n      [/script-src/, \"string.quote\"],\n      [/style-src/, \"string.quote\"],\n      [/worker-src/, \"string.quote\"],\n      [/base-uri/, \"string.quote\"],\n      [/plugin-types/, \"string.quote\"],\n      [/sandbox/, \"string.quote\"],\n      [/disown-opener/, \"string.quote\"],\n      [/form-action/, \"string.quote\"],\n      [/frame-ancestors/, \"string.quote\"],\n      [/report-uri/, \"string.quote\"],\n      [/report-to/, \"string.quote\"],\n      [/upgrade-insecure-requests/, \"string.quote\"],\n      [/block-all-mixed-content/, \"string.quote\"],\n      [/require-sri-for/, \"string.quote\"],\n      [/reflected-xss/, \"string.quote\"],\n      [/referrer/, \"string.quote\"],\n      [/policy-uri/, \"string.quote\"],\n      [/'self'/, \"string.quote\"],\n      [/'unsafe-inline'/, \"string.quote\"],\n      [/'unsafe-eval'/, \"string.quote\"],\n      [/'strict-dynamic'/, \"string.quote\"],\n      [/'unsafe-hashed-attributes'/, \"string.quote\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAKA;AAdJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU,CAAC;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,kBAAkB,CAAC;AAAA,IACrB;AACA,IAAI,WAAW;AAAA;AAAA;AAAA,MAGb,UAAU,CAAC;AAAA,MACX,cAAc,CAAC;AAAA,MACf,cAAc;AAAA,MACd,WAAW,CAAC;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,eAAe,cAAc;AAAA,UAC9B,CAAC,eAAe,cAAc;AAAA,UAC9B,CAAC,YAAY,cAAc;AAAA,UAC3B,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,WAAW,cAAc;AAAA,UAC1B,CAAC,gBAAgB,cAAc;AAAA,UAC/B,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,cAAc,cAAc;AAAA,UAC7B,CAAC,cAAc,cAAc;AAAA,UAC7B,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,cAAc,cAAc;AAAA,UAC7B,CAAC,YAAY,cAAc;AAAA,UAC3B,CAAC,gBAAgB,cAAc;AAAA,UAC/B,CAAC,WAAW,cAAc;AAAA,UAC1B,CAAC,iBAAiB,cAAc;AAAA,UAChC,CAAC,eAAe,cAAc;AAAA,UAC9B,CAAC,mBAAmB,cAAc;AAAA,UAClC,CAAC,cAAc,cAAc;AAAA,UAC7B,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,6BAA6B,cAAc;AAAA,UAC5C,CAAC,2BAA2B,cAAc;AAAA,UAC1C,CAAC,mBAAmB,cAAc;AAAA,UAClC,CAAC,iBAAiB,cAAc;AAAA,UAChC,CAAC,YAAY,cAAc;AAAA,UAC3B,CAAC,cAAc,cAAc;AAAA,UAC7B,CAAC,UAAU,cAAc;AAAA,UACzB,CAAC,mBAAmB,cAAc;AAAA,UAClC,CAAC,iBAAiB,cAAc;AAAA,UAChC,CAAC,oBAAoB,cAAc;AAAA,UACnC,CAAC,8BAA8B,cAAc;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
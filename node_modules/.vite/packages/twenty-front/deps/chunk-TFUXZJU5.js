import {
  require_client
} from "./chunk-XLS6HLMX.js";
import {
  tabbable
} from "./chunk-2UHP7A3I.js";
import {
  tippy_esm_default
} from "./chunk-WESWXL2S.js";
import {
  flip,
  offset,
  shift,
  size,
  useFloating
} from "./chunk-C6RYHFZP.js";
import {
  floor,
  getComputedStyle,
  getOverflowAncestors,
  getParentNode,
  isElement,
  isHTMLElement,
  isLastTraversableNode,
  isShadowRoot
} from "./chunk-YAGSMJYR.js";
import {
  require_react_dom
} from "./chunk-VZQMTKPA.js";
import {
  $r,
  Ac,
  Be,
  Dc,
  F,
  Fc,
  Hc,
  Ie,
  Kc,
  Kt,
  Oc,
  Os,
  Pc,
  Pe,
  Q,
  Rc,
  Rn,
  Uc,
  Un,
  Ur,
  Ve,
  Vn,
  Vs,
  Vt,
  Z,
  Zr,
  _c,
  ar,
  as,
  br,
  bt,
  cr,
  es,
  fn,
  fr,
  gr,
  ir,
  it,
  j,
  js,
  ls,
  mn,
  mr,
  oe,
  pa,
  pr,
  q,
  ro,
  rt,
  st,
  ua,
  ur,
  ve,
  yn
} from "./chunk-CXRODUKU.js";
import {
  AllSelection,
  DOMParser,
  Fragment,
  Node as Node2,
  NodeSelection,
  Plugin,
  PluginKey,
  ReplaceAroundStep,
  ReplaceStep,
  Schema,
  Selection,
  Slice,
  TextSelection,
  canJoin,
  canSplit,
  createParagraphNear,
  deleteSelection,
  exitCode,
  joinBackward,
  joinDown,
  joinForward,
  joinPoint,
  joinTextblockBackward,
  joinTextblockForward,
  joinUp,
  lift,
  liftEmptyBlock,
  liftListItem,
  liftTarget,
  newlineInCode,
  selectNodeBackward,
  selectNodeForward,
  selectParentNode,
  selectTextblockEnd,
  selectTextblockStart,
  setBlockType,
  sinkListItem,
  wrapIn,
  wrapInList
} from "./chunk-44NKIK5J.js";
import {
  require_jsx_runtime
} from "./chunk-VV4ISGQI.js";
import {
  require_react
} from "./chunk-LABDTKBP.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/@blocknote/react/dist/blocknote-react.js
var import_react3 = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());

// node_modules/@blocknote/react/node_modules/@floating-ui/react/dist/floating-ui.react.mjs
var React = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);

// node_modules/@blocknote/react/node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs
function activeElement(doc) {
  let activeElement2 = doc.activeElement;
  while (((_activeElement = activeElement2) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {
    var _activeElement;
    activeElement2 = activeElement2.shadowRoot.activeElement;
  }
  return activeElement2;
}
function contains(parent, child) {
  if (!parent || !child) {
    return false;
  }
  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();
  if (parent.contains(child)) {
    return true;
  }
  if (rootNode && isShadowRoot(rootNode)) {
    let next = child;
    while (next) {
      if (parent === next) {
        return true;
      }
      next = next.parentNode || next.host;
    }
  }
  return false;
}
function getPlatform() {
  const uaData = navigator.userAgentData;
  if (uaData != null && uaData.platform) {
    return uaData.platform;
  }
  return navigator.platform;
}
function isSafari() {
  return /apple/i.test(navigator.vendor);
}
function isReactEvent(event) {
  return "nativeEvent" in event;
}
function isRootElement(element) {
  return element.matches("html,body");
}
function getDocument(node) {
  return (node == null ? void 0 : node.ownerDocument) || document;
}
function isEventTargetWithin(event, node) {
  if (node == null) {
    return false;
  }
  if ("composedPath" in event) {
    return event.composedPath().includes(node);
  }
  const e = event;
  return e.target != null && node.contains(e.target);
}
function getTarget(event) {
  if ("composedPath" in event) {
    return event.composedPath()[0];
  }
  return event.target;
}
function stopEvent(event) {
  event.preventDefault();
  event.stopPropagation();
}

// node_modules/@blocknote/react/node_modules/@floating-ui/react/dist/floating-ui.react.mjs
var ReactDOM = __toESM(require_react_dom(), 1);
function useMergeRefs(refs) {
  return React.useMemo(() => {
    if (refs.every((ref) => ref == null)) {
      return null;
    }
    return (value) => {
      refs.forEach((ref) => {
        if (typeof ref === "function") {
          ref(value);
        } else if (ref != null) {
          ref.current = value;
        }
      });
    };
  }, refs);
}
var SafeReact = {
  ...React
};
var useInsertionEffect = SafeReact.useInsertionEffect;
var useSafeInsertionEffect = useInsertionEffect || ((fn2) => fn2());
function useEffectEvent(callback) {
  const ref = React.useRef(() => {
    if (true) {
      throw new Error("Cannot call an event handler while rendering.");
    }
  });
  useSafeInsertionEffect(() => {
    ref.current = callback;
  });
  return React.useCallback(function() {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    return ref.current == null ? void 0 : ref.current(...args);
  }, []);
}
var ARROW_UP = "ArrowUp";
var ARROW_DOWN = "ArrowDown";
var ARROW_LEFT = "ArrowLeft";
var ARROW_RIGHT = "ArrowRight";
function isDifferentRow(index3, cols, prevRow) {
  return Math.floor(index3 / cols) !== prevRow;
}
function isIndexOutOfBounds(listRef, index3) {
  return index3 < 0 || index3 >= listRef.current.length;
}
function getMinIndex(listRef, disabledIndices) {
  return findNonDisabledIndex(listRef, {
    disabledIndices
  });
}
function getMaxIndex(listRef, disabledIndices) {
  return findNonDisabledIndex(listRef, {
    decrement: true,
    startingIndex: listRef.current.length,
    disabledIndices
  });
}
function findNonDisabledIndex(listRef, _temp) {
  let {
    startingIndex = -1,
    decrement = false,
    disabledIndices,
    amount = 1
  } = _temp === void 0 ? {} : _temp;
  const list = listRef.current;
  let index3 = startingIndex;
  do {
    index3 += decrement ? -amount : amount;
  } while (index3 >= 0 && index3 <= list.length - 1 && isDisabled(list, index3, disabledIndices));
  return index3;
}
function getGridNavigatedIndex(elementsRef, _ref) {
  let {
    event,
    orientation,
    loop,
    cols,
    disabledIndices,
    minIndex,
    maxIndex,
    prevIndex,
    stopEvent: stop = false
  } = _ref;
  let nextIndex = prevIndex;
  if (event.key === ARROW_UP) {
    stop && stopEvent(event);
    if (prevIndex === -1) {
      nextIndex = maxIndex;
    } else {
      nextIndex = findNonDisabledIndex(elementsRef, {
        startingIndex: nextIndex,
        amount: cols,
        decrement: true,
        disabledIndices
      });
      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {
        const col = prevIndex % cols;
        const maxCol = maxIndex % cols;
        const offset2 = maxIndex - (maxCol - col);
        if (maxCol === col) {
          nextIndex = maxIndex;
        } else {
          nextIndex = maxCol > col ? offset2 : offset2 - cols;
        }
      }
    }
    if (isIndexOutOfBounds(elementsRef, nextIndex)) {
      nextIndex = prevIndex;
    }
  }
  if (event.key === ARROW_DOWN) {
    stop && stopEvent(event);
    if (prevIndex === -1) {
      nextIndex = minIndex;
    } else {
      nextIndex = findNonDisabledIndex(elementsRef, {
        startingIndex: prevIndex,
        amount: cols,
        disabledIndices
      });
      if (loop && prevIndex + cols > maxIndex) {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex % cols - cols,
          amount: cols,
          disabledIndices
        });
      }
    }
    if (isIndexOutOfBounds(elementsRef, nextIndex)) {
      nextIndex = prevIndex;
    }
  }
  if (orientation === "both") {
    const prevRow = floor(prevIndex / cols);
    if (event.key === ARROW_RIGHT) {
      stop && stopEvent(event);
      if (prevIndex % cols !== cols - 1) {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex,
          disabledIndices
        });
        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {
          nextIndex = findNonDisabledIndex(elementsRef, {
            startingIndex: prevIndex - prevIndex % cols - 1,
            disabledIndices
          });
        }
      } else if (loop) {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex - prevIndex % cols - 1,
          disabledIndices
        });
      }
      if (isDifferentRow(nextIndex, cols, prevRow)) {
        nextIndex = prevIndex;
      }
    }
    if (event.key === ARROW_LEFT) {
      stop && stopEvent(event);
      if (prevIndex % cols !== 0) {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex,
          decrement: true,
          disabledIndices
        });
        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {
          nextIndex = findNonDisabledIndex(elementsRef, {
            startingIndex: prevIndex + (cols - prevIndex % cols),
            decrement: true,
            disabledIndices
          });
        }
      } else if (loop) {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex + (cols - prevIndex % cols),
          decrement: true,
          disabledIndices
        });
      }
      if (isDifferentRow(nextIndex, cols, prevRow)) {
        nextIndex = prevIndex;
      }
    }
    const lastRow = floor(maxIndex / cols) === prevRow;
    if (isIndexOutOfBounds(elementsRef, nextIndex)) {
      if (loop && lastRow) {
        nextIndex = event.key === ARROW_LEFT ? maxIndex : findNonDisabledIndex(elementsRef, {
          startingIndex: prevIndex - prevIndex % cols - 1,
          disabledIndices
        });
      } else {
        nextIndex = prevIndex;
      }
    }
  }
  return nextIndex;
}
function buildCellMap(sizes, cols, dense) {
  const cellMap = [];
  let startIndex = 0;
  sizes.forEach((_ref2, index3) => {
    let {
      width,
      height
    } = _ref2;
    if (width > cols) {
      if (true) {
        throw new Error("[Floating UI]: Invalid grid - item width at index " + index3 + " is greater than grid columns");
      }
    }
    let itemPlaced = false;
    if (dense) {
      startIndex = 0;
    }
    while (!itemPlaced) {
      const targetCells = [];
      for (let i2 = 0; i2 < width; i2++) {
        for (let j2 = 0; j2 < height; j2++) {
          targetCells.push(startIndex + i2 + j2 * cols);
        }
      }
      if (startIndex % cols + width <= cols && targetCells.every((cell) => cellMap[cell] == null)) {
        targetCells.forEach((cell) => {
          cellMap[cell] = index3;
        });
        itemPlaced = true;
      } else {
        startIndex++;
      }
    }
  });
  return [...cellMap];
}
function getCellIndexOfCorner(index3, sizes, cellMap, cols, corner) {
  if (index3 === -1) return -1;
  const firstCellIndex = cellMap.indexOf(index3);
  const sizeItem = sizes[index3];
  switch (corner) {
    case "tl":
      return firstCellIndex;
    case "tr":
      if (!sizeItem) {
        return firstCellIndex;
      }
      return firstCellIndex + sizeItem.width - 1;
    case "bl":
      if (!sizeItem) {
        return firstCellIndex;
      }
      return firstCellIndex + (sizeItem.height - 1) * cols;
    case "br":
      return cellMap.lastIndexOf(index3);
  }
}
function getCellIndices(indices, cellMap) {
  return cellMap.flatMap((index3, cellIndex) => indices.includes(index3) ? [cellIndex] : []);
}
function isDisabled(list, index3, disabledIndices) {
  if (disabledIndices) {
    return disabledIndices.includes(index3);
  }
  const element = list[index3];
  return element == null || element.hasAttribute("disabled") || element.getAttribute("aria-disabled") === "true";
}
var rafId = 0;
function enqueueFocus(el2, options) {
  if (options === void 0) {
    options = {};
  }
  const {
    preventScroll = false,
    cancelPrevious = true,
    sync = false
  } = options;
  cancelPrevious && cancelAnimationFrame(rafId);
  const exec = () => el2 == null ? void 0 : el2.focus({
    preventScroll
  });
  if (sync) {
    exec();
  } else {
    rafId = requestAnimationFrame(exec);
  }
}
var index = typeof document !== "undefined" ? import_react.useLayoutEffect : import_react.useEffect;
function sortByDocumentPosition(a, b2) {
  const position = a.compareDocumentPosition(b2);
  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {
    return -1;
  }
  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {
    return 1;
  }
  return 0;
}
function areMapsEqual(map1, map2) {
  if (map1.size !== map2.size) {
    return false;
  }
  for (const [key, value] of map1.entries()) {
    if (value !== map2.get(key)) {
      return false;
    }
  }
  return true;
}
var FloatingListContext = React.createContext({
  register: () => {
  },
  unregister: () => {
  },
  map: /* @__PURE__ */ new Map(),
  elementsRef: {
    current: []
  }
});
function FloatingList(props) {
  const {
    children,
    elementsRef,
    labelsRef
  } = props;
  const [map, setMap] = React.useState(() => /* @__PURE__ */ new Map());
  const register = React.useCallback((node) => {
    setMap((prevMap) => new Map(prevMap).set(node, null));
  }, []);
  const unregister = React.useCallback((node) => {
    setMap((prevMap) => {
      const map2 = new Map(prevMap);
      map2.delete(node);
      return map2;
    });
  }, []);
  index(() => {
    const newMap = new Map(map);
    const nodes = Array.from(newMap.keys()).sort(sortByDocumentPosition);
    nodes.forEach((node, index3) => {
      newMap.set(node, index3);
    });
    if (!areMapsEqual(map, newMap)) {
      setMap(newMap);
    }
  }, [map]);
  return React.createElement(FloatingListContext.Provider, {
    value: React.useMemo(() => ({
      register,
      unregister,
      map,
      elementsRef,
      labelsRef
    }), [register, unregister, map, elementsRef, labelsRef])
  }, children);
}
function useListItem(props) {
  if (props === void 0) {
    props = {};
  }
  const {
    label
  } = props;
  const {
    register,
    unregister,
    map,
    elementsRef,
    labelsRef
  } = React.useContext(FloatingListContext);
  const [index$1, setIndex] = React.useState(null);
  const componentRef = React.useRef(null);
  const ref = React.useCallback((node) => {
    componentRef.current = node;
    if (index$1 !== null) {
      elementsRef.current[index$1] = node;
      if (labelsRef) {
        var _node$textContent;
        const isLabelDefined = label !== void 0;
        labelsRef.current[index$1] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;
      }
    }
  }, [index$1, elementsRef, labelsRef, label]);
  index(() => {
    const node = componentRef.current;
    if (node) {
      register(node);
      return () => {
        unregister(node);
      };
    }
  }, [register, unregister]);
  index(() => {
    const index3 = componentRef.current ? map.get(componentRef.current) : null;
    if (index3 != null) {
      setIndex(index3);
    }
  }, [map]);
  return React.useMemo(() => ({
    ref,
    index: index$1 == null ? -1 : index$1
  }), [index$1, ref]);
}
function renderJsx(render, computedProps) {
  if (typeof render === "function") {
    return render(computedProps);
  }
  if (render) {
    return React.cloneElement(render, computedProps);
  }
  return React.createElement("div", computedProps);
}
var CompositeContext = React.createContext({
  activeIndex: 0,
  onNavigate: () => {
  }
});
var horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];
var verticalKeys = [ARROW_UP, ARROW_DOWN];
var allKeys = [...horizontalKeys, ...verticalKeys];
var Composite = React.forwardRef(function Composite2(props, forwardedRef) {
  const {
    render,
    orientation = "both",
    loop = true,
    cols = 1,
    disabledIndices,
    activeIndex: externalActiveIndex,
    onNavigate: externalSetActiveIndex,
    itemSizes,
    dense = false,
    ...domProps
  } = props;
  const [internalActiveIndex, internalSetActiveIndex] = React.useState(0);
  const activeIndex = externalActiveIndex != null ? externalActiveIndex : internalActiveIndex;
  const onNavigate = useEffectEvent(externalSetActiveIndex != null ? externalSetActiveIndex : internalSetActiveIndex);
  const elementsRef = React.useRef([]);
  const renderElementProps = render && typeof render !== "function" ? render.props : {};
  const contextValue = React.useMemo(() => ({
    activeIndex,
    onNavigate
  }), [activeIndex, onNavigate]);
  const isGrid = cols > 1;
  function handleKeyDown(event) {
    if (!allKeys.includes(event.key)) return;
    let nextIndex = activeIndex;
    const minIndex = getMinIndex(elementsRef, disabledIndices);
    const maxIndex = getMaxIndex(elementsRef, disabledIndices);
    if (isGrid) {
      const sizes = itemSizes || Array.from({
        length: elementsRef.current.length
      }, () => ({
        width: 1,
        height: 1
      }));
      const cellMap = buildCellMap(sizes, cols, dense);
      const minGridIndex = cellMap.findIndex((index3) => index3 != null && !isDisabled(elementsRef.current, index3, disabledIndices));
      const maxGridIndex = cellMap.reduce((foundIndex, index3, cellIndex) => index3 != null && !isDisabled(elementsRef.current, index3, disabledIndices) ? cellIndex : foundIndex, -1);
      nextIndex = cellMap[getGridNavigatedIndex({
        current: cellMap.map((itemIndex) => itemIndex ? elementsRef.current[itemIndex] : null)
      }, {
        event,
        orientation,
        loop,
        cols,
        // treat undefined (empty grid spaces) as disabled indices so we
        // don't end up in them
        disabledIndices: getCellIndices([...disabledIndices || elementsRef.current.map((_2, index3) => isDisabled(elementsRef.current, index3) ? index3 : void 0), void 0], cellMap),
        minIndex: minGridIndex,
        maxIndex: maxGridIndex,
        prevIndex: getCellIndexOfCorner(
          activeIndex > maxIndex ? minIndex : activeIndex,
          sizes,
          cellMap,
          cols,
          // use a corner matching the edge closest to the direction we're
          // moving in so we don't end up in the same item. Prefer
          // top/left over bottom/right.
          event.key === ARROW_DOWN ? "bl" : event.key === ARROW_RIGHT ? "tr" : "tl"
        )
      })];
    }
    const toEndKeys = {
      horizontal: [ARROW_RIGHT],
      vertical: [ARROW_DOWN],
      both: [ARROW_RIGHT, ARROW_DOWN]
    }[orientation];
    const toStartKeys = {
      horizontal: [ARROW_LEFT],
      vertical: [ARROW_UP],
      both: [ARROW_LEFT, ARROW_UP]
    }[orientation];
    const preventedKeys = isGrid ? allKeys : {
      horizontal: horizontalKeys,
      vertical: verticalKeys,
      both: allKeys
    }[orientation];
    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {
      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {
        nextIndex = minIndex;
      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {
        nextIndex = maxIndex;
      } else {
        nextIndex = findNonDisabledIndex(elementsRef, {
          startingIndex: nextIndex,
          decrement: toStartKeys.includes(event.key),
          disabledIndices
        });
      }
    }
    if (nextIndex !== activeIndex && !isIndexOutOfBounds(elementsRef, nextIndex)) {
      event.stopPropagation();
      if (preventedKeys.includes(event.key)) {
        event.preventDefault();
      }
      onNavigate(nextIndex);
      queueMicrotask(() => {
        enqueueFocus(elementsRef.current[nextIndex]);
      });
    }
  }
  const computedProps = {
    ...domProps,
    ...renderElementProps,
    ref: forwardedRef,
    "aria-orientation": orientation === "both" ? void 0 : orientation,
    onKeyDown(e) {
      domProps.onKeyDown == null || domProps.onKeyDown(e);
      renderElementProps.onKeyDown == null || renderElementProps.onKeyDown(e);
      handleKeyDown(e);
    }
  };
  return React.createElement(CompositeContext.Provider, {
    value: contextValue
  }, React.createElement(FloatingList, {
    elementsRef
  }, renderJsx(render, computedProps)));
});
var CompositeItem = React.forwardRef(function CompositeItem2(props, forwardedRef) {
  const {
    render,
    ...domProps
  } = props;
  const renderElementProps = render && typeof render !== "function" ? render.props : {};
  const {
    activeIndex,
    onNavigate
  } = React.useContext(CompositeContext);
  const {
    ref,
    index: index3
  } = useListItem();
  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);
  const isActive = activeIndex === index3;
  const computedProps = {
    ...domProps,
    ...renderElementProps,
    ref: mergedRef,
    tabIndex: isActive ? 0 : -1,
    "data-active": isActive ? "" : void 0,
    onFocus(e) {
      domProps.onFocus == null || domProps.onFocus(e);
      renderElementProps.onFocus == null || renderElementProps.onFocus(e);
      onNavigate(index3);
    }
  };
  return renderJsx(render, computedProps);
});
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i2 = 1; i2 < arguments.length; i2++) {
      var source = arguments[i2];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var serverHandoffComplete = false;
var count = 0;
var genId = () => (
  // Ensure the id is unique with multiple independent versions of Floating UI
  // on <React 18
  "floating-ui-" + Math.random().toString(36).slice(2, 6) + count++
);
function useFloatingId() {
  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : void 0);
  index(() => {
    if (id == null) {
      setId(genId());
    }
  }, []);
  React.useEffect(() => {
    serverHandoffComplete = true;
  }, []);
  return id;
}
var useReactId = SafeReact.useId;
var useId = useReactId || useFloatingId;
var devMessageSet;
if (true) {
  devMessageSet = /* @__PURE__ */ new Set();
}
function warn() {
  var _devMessageSet;
  for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {
    messages[_key] = arguments[_key];
  }
  const message = "Floating UI: " + messages.join(" ");
  if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(message))) {
    var _devMessageSet2;
    (_devMessageSet2 = devMessageSet) == null || _devMessageSet2.add(message);
    console.warn(message);
  }
}
function error() {
  var _devMessageSet3;
  for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    messages[_key2] = arguments[_key2];
  }
  const message = "Floating UI: " + messages.join(" ");
  if (!((_devMessageSet3 = devMessageSet) != null && _devMessageSet3.has(message))) {
    var _devMessageSet4;
    (_devMessageSet4 = devMessageSet) == null || _devMessageSet4.add(message);
    console.error(message);
  }
}
var FloatingArrow = React.forwardRef(function FloatingArrow2(props, ref) {
  const {
    context: {
      placement,
      elements: {
        floating
      },
      middlewareData: {
        arrow: arrow2
      }
    },
    width = 14,
    height = 7,
    tipRadius = 0,
    strokeWidth = 0,
    staticOffset,
    stroke,
    d,
    style: {
      transform,
      ...restStyle
    } = {},
    ...rest
  } = props;
  if (true) {
    if (!ref) {
      warn("The `ref` prop is required for `FloatingArrow`.");
    }
  }
  const clipPathId = useId();
  const [isRTL, setIsRTL] = React.useState(false);
  index(() => {
    if (!floating) return;
    const isRTL2 = getComputedStyle(floating).direction === "rtl";
    if (isRTL2) {
      setIsRTL(true);
    }
  }, [floating]);
  if (!floating) {
    return null;
  }
  const computedStrokeWidth = strokeWidth * 2;
  const halfStrokeWidth = computedStrokeWidth / 2;
  const svgX = width / 2 * (tipRadius / -8 + 1);
  const svgY = height / 2 * tipRadius / 4;
  const [side, alignment] = placement.split("-");
  const isCustomShape = !!d;
  const isVerticalSide = side === "top" || side === "bottom";
  const yOffsetProp = staticOffset && alignment === "end" ? "bottom" : "top";
  let xOffsetProp = staticOffset && alignment === "end" ? "right" : "left";
  if (staticOffset && isRTL) {
    xOffsetProp = alignment === "end" ? "left" : "right";
  }
  const arrowX = (arrow2 == null ? void 0 : arrow2.x) != null ? staticOffset || arrow2.x : "";
  const arrowY = (arrow2 == null ? void 0 : arrow2.y) != null ? staticOffset || arrow2.y : "";
  const dValue = d || "M0,0" + (" H" + width) + (" L" + (width - svgX) + "," + (height - svgY)) + (" Q" + width / 2 + "," + height + " " + svgX + "," + (height - svgY)) + " Z";
  const rotation = {
    top: isCustomShape ? "rotate(180deg)" : "",
    left: isCustomShape ? "rotate(90deg)" : "rotate(-90deg)",
    bottom: isCustomShape ? "" : "rotate(180deg)",
    right: isCustomShape ? "rotate(-90deg)" : "rotate(90deg)"
  }[side];
  return React.createElement("svg", _extends({}, rest, {
    "aria-hidden": true,
    ref,
    width: isCustomShape ? width : width + computedStrokeWidth,
    height: width,
    viewBox: "0 0 " + width + " " + (height > width ? height : width),
    style: {
      position: "absolute",
      pointerEvents: "none",
      [xOffsetProp]: arrowX,
      [yOffsetProp]: arrowY,
      [side]: isVerticalSide || isCustomShape ? "100%" : "calc(100% - " + computedStrokeWidth / 2 + "px)",
      transform: "" + rotation + (transform != null ? transform : ""),
      ...restStyle
    }
  }), computedStrokeWidth > 0 && React.createElement("path", {
    clipPath: "url(#" + clipPathId + ")",
    fill: "none",
    stroke,
    strokeWidth: computedStrokeWidth + (d ? 0 : 1),
    d: dValue
  }), React.createElement("path", {
    stroke: computedStrokeWidth && !d ? rest.fill : "none",
    d: dValue
  }), React.createElement("clipPath", {
    id: clipPathId
  }, React.createElement("rect", {
    x: -halfStrokeWidth,
    y: halfStrokeWidth * (isCustomShape ? -1 : 1),
    width: width + computedStrokeWidth,
    height: width
  })));
});
function createPubSub() {
  const map = /* @__PURE__ */ new Map();
  return {
    emit(event, data) {
      var _map$get;
      (_map$get = map.get(event)) == null || _map$get.forEach((handler) => handler(data));
    },
    on(event, listener) {
      map.set(event, [...map.get(event) || [], listener]);
    },
    off(event, listener) {
      var _map$get2;
      map.set(event, ((_map$get2 = map.get(event)) == null ? void 0 : _map$get2.filter((l) => l !== listener)) || []);
    }
  };
}
var FloatingNodeContext = React.createContext(null);
var FloatingTreeContext = React.createContext(null);
var useFloatingParentNodeId = () => {
  var _React$useContext;
  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;
};
var useFloatingTree = () => React.useContext(FloatingTreeContext);
function createAttribute(name) {
  return "data-floating-ui-" + name;
}
function useLatestRef(value) {
  const ref = (0, import_react.useRef)(value);
  index(() => {
    ref.current = value;
  });
  return ref;
}
var safePolygonIdentifier = createAttribute("safe-polygon");
var NOOP = () => {
};
var FloatingDelayGroupContext = React.createContext({
  delay: 0,
  initialDelay: 0,
  timeoutMs: 0,
  currentId: null,
  setCurrentId: NOOP,
  setState: NOOP,
  isInstantPhase: false
});
function getChildren(nodes, id) {
  let allChildren = nodes.filter((node) => {
    var _node$context;
    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);
  });
  let currentChildren = allChildren;
  while (currentChildren.length) {
    currentChildren = nodes.filter((node) => {
      var _currentChildren;
      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some((n) => {
        var _node$context2;
        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);
      });
    });
    allChildren = allChildren.concat(currentChildren);
  }
  return allChildren;
}
var getTabbableOptions = () => ({
  getShadowRoot: true,
  displayCheck: (
    // JSDOM does not support the `tabbable` library. To solve this we can
    // check if `ResizeObserver` is a real function (not polyfilled), which
    // determines if the current environment is JSDOM-like.
    typeof ResizeObserver === "function" && ResizeObserver.toString().includes("[native code]") ? "full" : "none"
  )
});
function getTabbableIn(container, direction) {
  const allTabbable = tabbable(container, getTabbableOptions());
  if (direction === "prev") {
    allTabbable.reverse();
  }
  const activeIndex = allTabbable.indexOf(activeElement(getDocument(container)));
  const nextTabbableElements = allTabbable.slice(activeIndex + 1);
  return nextTabbableElements[0];
}
function getNextTabbable() {
  return getTabbableIn(document.body, "next");
}
function getPreviousTabbable() {
  return getTabbableIn(document.body, "prev");
}
function isOutsideEvent(event, container) {
  const containerElement = container || event.currentTarget;
  const relatedTarget = event.relatedTarget;
  return !relatedTarget || !contains(containerElement, relatedTarget);
}
function disableFocusInside(container) {
  const tabbableElements = tabbable(container, getTabbableOptions());
  tabbableElements.forEach((element) => {
    element.dataset.tabindex = element.getAttribute("tabindex") || "";
    element.setAttribute("tabindex", "-1");
  });
}
function enableFocusInside(container) {
  const elements = container.querySelectorAll("[data-tabindex]");
  elements.forEach((element) => {
    const tabindex = element.dataset.tabindex;
    delete element.dataset.tabindex;
    if (tabindex) {
      element.setAttribute("tabindex", tabindex);
    } else {
      element.removeAttribute("tabindex");
    }
  });
}
var HIDDEN_STYLES = {
  border: 0,
  clip: "rect(0 0 0 0)",
  height: "1px",
  margin: "-1px",
  overflow: "hidden",
  padding: 0,
  position: "fixed",
  whiteSpace: "nowrap",
  width: "1px",
  top: 0,
  left: 0
};
var timeoutId;
function setActiveElementOnTab(event) {
  if (event.key === "Tab") {
    event.target;
    clearTimeout(timeoutId);
  }
}
var FocusGuard = React.forwardRef(function FocusGuard2(props, ref) {
  const [role, setRole] = React.useState();
  index(() => {
    if (isSafari()) {
      setRole("button");
    }
    document.addEventListener("keydown", setActiveElementOnTab);
    return () => {
      document.removeEventListener("keydown", setActiveElementOnTab);
    };
  }, []);
  const restProps = {
    ref,
    tabIndex: 0,
    // Role is only for VoiceOver
    role,
    "aria-hidden": role ? void 0 : true,
    [createAttribute("focus-guard")]: "",
    style: HIDDEN_STYLES
  };
  return React.createElement("span", _extends({}, props, restProps));
});
var PortalContext = React.createContext(null);
var attr = createAttribute("portal");
function useFloatingPortalNode(props) {
  if (props === void 0) {
    props = {};
  }
  const {
    id,
    root
  } = props;
  const uniqueId = useId();
  const portalContext = usePortalContext();
  const [portalNode, setPortalNode] = React.useState(null);
  const portalNodeRef = React.useRef(null);
  index(() => {
    return () => {
      portalNode == null || portalNode.remove();
      queueMicrotask(() => {
        portalNodeRef.current = null;
      });
    };
  }, [portalNode]);
  index(() => {
    if (!uniqueId) return;
    if (portalNodeRef.current) return;
    const existingIdRoot = id ? document.getElementById(id) : null;
    if (!existingIdRoot) return;
    const subRoot = document.createElement("div");
    subRoot.id = uniqueId;
    subRoot.setAttribute(attr, "");
    existingIdRoot.appendChild(subRoot);
    portalNodeRef.current = subRoot;
    setPortalNode(subRoot);
  }, [id, uniqueId]);
  index(() => {
    if (!uniqueId) return;
    if (portalNodeRef.current) return;
    let container = root || (portalContext == null ? void 0 : portalContext.portalNode);
    if (container && !isElement(container)) container = container.current;
    container = container || document.body;
    let idWrapper = null;
    if (id) {
      idWrapper = document.createElement("div");
      idWrapper.id = id;
      container.appendChild(idWrapper);
    }
    const subRoot = document.createElement("div");
    subRoot.id = uniqueId;
    subRoot.setAttribute(attr, "");
    container = idWrapper || container;
    container.appendChild(subRoot);
    portalNodeRef.current = subRoot;
    setPortalNode(subRoot);
  }, [id, root, uniqueId, portalContext]);
  return portalNode;
}
function FloatingPortal(props) {
  const {
    children,
    id,
    root = null,
    preserveTabOrder = true
  } = props;
  const portalNode = useFloatingPortalNode({
    id,
    root
  });
  const [focusManagerState, setFocusManagerState] = React.useState(null);
  const beforeOutsideRef = React.useRef(null);
  const afterOutsideRef = React.useRef(null);
  const beforeInsideRef = React.useRef(null);
  const afterInsideRef = React.useRef(null);
  const shouldRenderGuards = (
    // The FocusManager and therefore floating element are currently open/
    // rendered.
    !!focusManagerState && // Guards are only for non-modal focus management.
    !focusManagerState.modal && // Don't render if unmount is transitioning.
    focusManagerState.open && preserveTabOrder && !!(root || portalNode)
  );
  React.useEffect(() => {
    if (!portalNode || !preserveTabOrder || focusManagerState != null && focusManagerState.modal) {
      return;
    }
    function onFocus(event) {
      if (portalNode && isOutsideEvent(event)) {
        const focusing = event.type === "focusin";
        const manageFocus = focusing ? enableFocusInside : disableFocusInside;
        manageFocus(portalNode);
      }
    }
    portalNode.addEventListener("focusin", onFocus, true);
    portalNode.addEventListener("focusout", onFocus, true);
    return () => {
      portalNode.removeEventListener("focusin", onFocus, true);
      portalNode.removeEventListener("focusout", onFocus, true);
    };
  }, [portalNode, preserveTabOrder, focusManagerState == null ? void 0 : focusManagerState.modal]);
  return React.createElement(PortalContext.Provider, {
    value: React.useMemo(() => ({
      preserveTabOrder,
      beforeOutsideRef,
      afterOutsideRef,
      beforeInsideRef,
      afterInsideRef,
      portalNode,
      setFocusManagerState
    }), [preserveTabOrder, portalNode])
  }, shouldRenderGuards && portalNode && React.createElement(FocusGuard, {
    "data-type": "outside",
    ref: beforeOutsideRef,
    onFocus: (event) => {
      if (isOutsideEvent(event, portalNode)) {
        var _beforeInsideRef$curr;
        (_beforeInsideRef$curr = beforeInsideRef.current) == null || _beforeInsideRef$curr.focus();
      } else {
        const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);
        prevTabbable == null || prevTabbable.focus();
      }
    }
  }), shouldRenderGuards && portalNode && React.createElement("span", {
    "aria-owns": portalNode.id,
    style: HIDDEN_STYLES
  }), portalNode && ReactDOM.createPortal(children, portalNode), shouldRenderGuards && portalNode && React.createElement(FocusGuard, {
    "data-type": "outside",
    ref: afterOutsideRef,
    onFocus: (event) => {
      if (isOutsideEvent(event, portalNode)) {
        var _afterInsideRef$curre;
        (_afterInsideRef$curre = afterInsideRef.current) == null || _afterInsideRef$curre.focus();
      } else {
        const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);
        nextTabbable == null || nextTabbable.focus();
        (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent));
      }
    }
  }));
}
var usePortalContext = () => React.useContext(PortalContext);
var VisuallyHiddenDismiss = React.forwardRef(function VisuallyHiddenDismiss2(props, ref) {
  return React.createElement("button", _extends({}, props, {
    type: "button",
    ref,
    tabIndex: -1,
    style: HIDDEN_STYLES
  }));
});
var activeLocks = /* @__PURE__ */ new Set();
var FloatingOverlay = React.forwardRef(function FloatingOverlay2(props, ref) {
  const {
    lockScroll = false,
    ...rest
  } = props;
  const lockId = useId();
  index(() => {
    if (!lockScroll) return;
    activeLocks.add(lockId);
    const isIOS = /iP(hone|ad|od)|iOS/.test(getPlatform());
    const bodyStyle = document.body.style;
    const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;
    const paddingProp = scrollbarX ? "paddingLeft" : "paddingRight";
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.scrollX;
    const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.scrollY;
    bodyStyle.overflow = "hidden";
    if (scrollbarWidth) {
      bodyStyle[paddingProp] = scrollbarWidth + "px";
    }
    if (isIOS) {
      var _window$visualViewpor, _window$visualViewpor2;
      const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;
      const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;
      Object.assign(bodyStyle, {
        position: "fixed",
        top: -(scrollY - Math.floor(offsetTop)) + "px",
        left: -(scrollX - Math.floor(offsetLeft)) + "px",
        right: "0"
      });
    }
    return () => {
      activeLocks.delete(lockId);
      if (activeLocks.size === 0) {
        Object.assign(bodyStyle, {
          overflow: "",
          [paddingProp]: ""
        });
        if (isIOS) {
          Object.assign(bodyStyle, {
            position: "",
            top: "",
            left: "",
            right: ""
          });
          window.scrollTo(scrollX, scrollY);
        }
      }
    };
  }, [lockId, lockScroll]);
  return React.createElement("div", _extends({
    ref
  }, rest, {
    style: {
      position: "fixed",
      overflow: "auto",
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      ...rest.style
    }
  }));
});
var bubbleHandlerKeys = {
  pointerdown: "onPointerDown",
  mousedown: "onMouseDown",
  click: "onClick"
};
var captureHandlerKeys = {
  pointerdown: "onPointerDownCapture",
  mousedown: "onMouseDownCapture",
  click: "onClickCapture"
};
var normalizeProp = (normalizable) => {
  var _normalizable$escapeK, _normalizable$outside;
  return {
    escapeKey: typeof normalizable === "boolean" ? normalizable : (_normalizable$escapeK = normalizable == null ? void 0 : normalizable.escapeKey) != null ? _normalizable$escapeK : false,
    outsidePress: typeof normalizable === "boolean" ? normalizable : (_normalizable$outside = normalizable == null ? void 0 : normalizable.outsidePress) != null ? _normalizable$outside : true
  };
};
function useDismiss(context, props) {
  if (props === void 0) {
    props = {};
  }
  const {
    open,
    onOpenChange,
    elements,
    dataRef
  } = context;
  const {
    enabled = true,
    escapeKey = true,
    outsidePress: unstable_outsidePress = true,
    outsidePressEvent = "pointerdown",
    referencePress = false,
    referencePressEvent = "pointerdown",
    ancestorScroll = false,
    bubbles,
    capture
  } = props;
  const tree = useFloatingTree();
  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === "function" ? unstable_outsidePress : () => false);
  const outsidePress = typeof unstable_outsidePress === "function" ? outsidePressFn : unstable_outsidePress;
  const insideReactTreeRef = React.useRef(false);
  const endedOrStartedInsideRef = React.useRef(false);
  const {
    escapeKey: escapeKeyBubbles,
    outsidePress: outsidePressBubbles
  } = normalizeProp(bubbles);
  const {
    escapeKey: escapeKeyCapture,
    outsidePress: outsidePressCapture
  } = normalizeProp(capture);
  const closeOnEscapeKeyDown = useEffectEvent((event) => {
    var _dataRef$current$floa;
    if (!open || !enabled || !escapeKey || event.key !== "Escape") {
      return;
    }
    const nodeId = (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;
    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];
    if (!escapeKeyBubbles) {
      event.stopPropagation();
      if (children.length > 0) {
        let shouldDismiss = true;
        children.forEach((child) => {
          var _child$context;
          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {
            shouldDismiss = false;
            return;
          }
        });
        if (!shouldDismiss) {
          return;
        }
      }
    }
    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event, "escape-key");
  });
  const closeOnEscapeKeyDownCapture = useEffectEvent((event) => {
    var _getTarget2;
    const callback = () => {
      var _getTarget;
      closeOnEscapeKeyDown(event);
      (_getTarget = getTarget(event)) == null || _getTarget.removeEventListener("keydown", callback);
    };
    (_getTarget2 = getTarget(event)) == null || _getTarget2.addEventListener("keydown", callback);
  });
  const closeOnPressOutside = useEffectEvent((event) => {
    var _dataRef$current$floa2;
    const insideReactTree = insideReactTreeRef.current;
    insideReactTreeRef.current = false;
    const endedOrStartedInside = endedOrStartedInsideRef.current;
    endedOrStartedInsideRef.current = false;
    if (outsidePressEvent === "click" && endedOrStartedInside) {
      return;
    }
    if (insideReactTree) {
      return;
    }
    if (typeof outsidePress === "function" && !outsidePress(event)) {
      return;
    }
    const target = getTarget(event);
    const inertSelector = "[" + createAttribute("inert") + "]";
    const markers = getDocument(elements.floating).querySelectorAll(inertSelector);
    let targetRootAncestor = isElement(target) ? target : null;
    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {
      const nextParent = getParentNode(targetRootAncestor);
      if (isLastTraversableNode(nextParent) || !isElement(nextParent)) {
        break;
      }
      targetRootAncestor = nextParent;
    }
    if (markers.length && isElement(target) && !isRootElement(target) && // Clicked on a direct ancestor (e.g. FloatingOverlay).
    !contains(target, elements.floating) && // If the target root element contains none of the markers, then the
    // element was injected after the floating element rendered.
    Array.from(markers).every((marker) => !contains(targetRootAncestor, marker))) {
      return;
    }
    if (isHTMLElement(target) && floating) {
      const canScrollX = target.clientWidth > 0 && target.scrollWidth > target.clientWidth;
      const canScrollY = target.clientHeight > 0 && target.scrollHeight > target.clientHeight;
      let xCond = canScrollY && event.offsetX > target.clientWidth;
      if (canScrollY) {
        const isRTL = getComputedStyle(target).direction === "rtl";
        if (isRTL) {
          xCond = event.offsetX <= target.offsetWidth - target.clientWidth;
        }
      }
      if (xCond || canScrollX && event.offsetY > target.clientHeight) {
        return;
      }
    }
    const nodeId = (_dataRef$current$floa2 = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa2.nodeId;
    const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some((node) => {
      var _node$context;
      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);
    });
    if (isEventTargetWithin(event, elements.floating) || isEventTargetWithin(event, elements.domReference) || targetIsInsideChildren) {
      return;
    }
    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];
    if (children.length > 0) {
      let shouldDismiss = true;
      children.forEach((child) => {
        var _child$context2;
        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {
          shouldDismiss = false;
          return;
        }
      });
      if (!shouldDismiss) {
        return;
      }
    }
    onOpenChange(false, event, "outside-press");
  });
  const closeOnPressOutsideCapture = useEffectEvent((event) => {
    var _getTarget4;
    const callback = () => {
      var _getTarget3;
      closeOnPressOutside(event);
      (_getTarget3 = getTarget(event)) == null || _getTarget3.removeEventListener(outsidePressEvent, callback);
    };
    (_getTarget4 = getTarget(event)) == null || _getTarget4.addEventListener(outsidePressEvent, callback);
  });
  React.useEffect(() => {
    if (!open || !enabled) {
      return;
    }
    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;
    dataRef.current.__outsidePressBubbles = outsidePressBubbles;
    function onScroll(event) {
      onOpenChange(false, event, "ancestor-scroll");
    }
    const doc = getDocument(elements.floating);
    escapeKey && doc.addEventListener("keydown", escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);
    outsidePress && doc.addEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);
    let ancestors = [];
    if (ancestorScroll) {
      if (isElement(elements.domReference)) {
        ancestors = getOverflowAncestors(elements.domReference);
      }
      if (isElement(elements.floating)) {
        ancestors = ancestors.concat(getOverflowAncestors(elements.floating));
      }
      if (!isElement(elements.reference) && elements.reference && elements.reference.contextElement) {
        ancestors = ancestors.concat(getOverflowAncestors(elements.reference.contextElement));
      }
    }
    ancestors = ancestors.filter((ancestor) => {
      var _doc$defaultView;
      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);
    });
    ancestors.forEach((ancestor) => {
      ancestor.addEventListener("scroll", onScroll, {
        passive: true
      });
    });
    return () => {
      escapeKey && doc.removeEventListener("keydown", escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);
      outsidePress && doc.removeEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);
      ancestors.forEach((ancestor) => {
        ancestor.removeEventListener("scroll", onScroll);
      });
    };
  }, [dataRef, elements, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, escapeKeyCapture, closeOnEscapeKeyDownCapture, closeOnPressOutside, outsidePressCapture, closeOnPressOutsideCapture]);
  React.useEffect(() => {
    insideReactTreeRef.current = false;
  }, [outsidePress, outsidePressEvent]);
  const reference = React.useMemo(() => ({
    onKeyDown: closeOnEscapeKeyDown,
    [bubbleHandlerKeys[referencePressEvent]]: (event) => {
      if (referencePress) {
        onOpenChange(false, event.nativeEvent, "reference-press");
      }
    }
  }), [closeOnEscapeKeyDown, onOpenChange, referencePress, referencePressEvent]);
  const floating = React.useMemo(() => ({
    onKeyDown: closeOnEscapeKeyDown,
    onMouseDown() {
      endedOrStartedInsideRef.current = true;
    },
    onMouseUp() {
      endedOrStartedInsideRef.current = true;
    },
    [captureHandlerKeys[outsidePressEvent]]: () => {
      insideReactTreeRef.current = true;
    }
  }), [closeOnEscapeKeyDown, outsidePressEvent]);
  return React.useMemo(() => enabled ? {
    reference,
    floating
  } : {}, [enabled, reference, floating]);
}
function useFloatingRootContext(options) {
  const {
    open = false,
    onOpenChange: onOpenChangeProp,
    elements: elementsProp
  } = options;
  const floatingId = useId();
  const dataRef = React.useRef({});
  const [events] = React.useState(() => createPubSub());
  const nested = useFloatingParentNodeId() != null;
  if (true) {
    const optionDomReference = elementsProp.reference;
    if (optionDomReference && !isElement(optionDomReference)) {
      error("Cannot pass a virtual element to the `elements.reference` option,", "as it must be a real DOM element. Use `refs.setPositionReference()`", "instead.");
    }
  }
  const [positionReference, setPositionReference] = React.useState(elementsProp.reference);
  const onOpenChange = useEffectEvent((open2, event, reason) => {
    dataRef.current.openEvent = open2 ? event : void 0;
    events.emit("openchange", {
      open: open2,
      event,
      reason,
      nested
    });
    onOpenChangeProp == null || onOpenChangeProp(open2, event, reason);
  });
  const refs = React.useMemo(() => ({
    setPositionReference
  }), []);
  const elements = React.useMemo(() => ({
    reference: positionReference || elementsProp.reference || null,
    floating: elementsProp.floating || null,
    domReference: elementsProp.reference
  }), [positionReference, elementsProp.reference, elementsProp.floating]);
  return React.useMemo(() => ({
    dataRef,
    open,
    onOpenChange,
    elements,
    events,
    floatingId,
    refs
  }), [open, onOpenChange, elements, events, floatingId, refs]);
}
function useFloating2(options) {
  if (options === void 0) {
    options = {};
  }
  const {
    nodeId
  } = options;
  const internalRootContext = useFloatingRootContext({
    ...options,
    elements: {
      reference: null,
      floating: null,
      ...options.elements
    }
  });
  const rootContext = options.rootContext || internalRootContext;
  const computedElements = rootContext.elements;
  const [_domReference, setDomReference] = React.useState(null);
  const [positionReference, _setPositionReference] = React.useState(null);
  const optionDomReference = computedElements == null ? void 0 : computedElements.reference;
  const domReference = optionDomReference || _domReference;
  const domReferenceRef = React.useRef(null);
  const tree = useFloatingTree();
  index(() => {
    if (domReference) {
      domReferenceRef.current = domReference;
    }
  }, [domReference]);
  const position = useFloating({
    ...options,
    elements: {
      ...computedElements,
      ...positionReference && {
        reference: positionReference
      }
    }
  });
  const setPositionReference = React.useCallback((node) => {
    const computedPositionReference = isElement(node) ? {
      getBoundingClientRect: () => node.getBoundingClientRect(),
      contextElement: node
    } : node;
    _setPositionReference(computedPositionReference);
    position.refs.setReference(computedPositionReference);
  }, [position.refs]);
  const setReference = React.useCallback((node) => {
    if (isElement(node) || node === null) {
      domReferenceRef.current = node;
      setDomReference(node);
    }
    if (isElement(position.refs.reference.current) || position.refs.reference.current === null || // Don't allow setting virtual elements using the old technique back to
    // `null` to support `positionReference` + an unstable `reference`
    // callback ref.
    node !== null && !isElement(node)) {
      position.refs.setReference(node);
    }
  }, [position.refs]);
  const refs = React.useMemo(() => ({
    ...position.refs,
    setReference,
    setPositionReference,
    domReference: domReferenceRef
  }), [position.refs, setReference, setPositionReference]);
  const elements = React.useMemo(() => ({
    ...position.elements,
    domReference
  }), [position.elements, domReference]);
  const context = React.useMemo(() => ({
    ...position,
    ...rootContext,
    refs,
    elements,
    nodeId
  }), [position, refs, elements, nodeId, rootContext]);
  index(() => {
    rootContext.dataRef.current.floatingContext = context;
    const node = tree == null ? void 0 : tree.nodesRef.current.find((node2) => node2.id === nodeId);
    if (node) {
      node.context = context;
    }
  });
  return React.useMemo(() => ({
    ...position,
    context,
    refs,
    elements
  }), [position, refs, elements, context]);
}
var ACTIVE_KEY = "active";
var SELECTED_KEY = "selected";
function mergeProps(userProps, propsList, elementKey) {
  const map = /* @__PURE__ */ new Map();
  const isItem = elementKey === "item";
  let domUserProps = userProps;
  if (isItem && userProps) {
    const {
      [ACTIVE_KEY]: _2,
      [SELECTED_KEY]: __,
      ...validProps
    } = userProps;
    domUserProps = validProps;
  }
  return {
    ...elementKey === "floating" && {
      tabIndex: -1
    },
    ...domUserProps,
    ...propsList.map((value) => {
      const propsOrGetProps = value ? value[elementKey] : null;
      if (typeof propsOrGetProps === "function") {
        return userProps ? propsOrGetProps(userProps) : null;
      }
      return propsOrGetProps;
    }).concat(userProps).reduce((acc, props) => {
      if (!props) {
        return acc;
      }
      Object.entries(props).forEach((_ref) => {
        let [key, value] = _ref;
        if (isItem && [ACTIVE_KEY, SELECTED_KEY].includes(key)) {
          return;
        }
        if (key.indexOf("on") === 0) {
          if (!map.has(key)) {
            map.set(key, []);
          }
          if (typeof value === "function") {
            var _map$get;
            (_map$get = map.get(key)) == null || _map$get.push(value);
            acc[key] = function() {
              var _map$get2;
              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
                args[_key] = arguments[_key];
              }
              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map((fn2) => fn2(...args)).find((val) => val !== void 0);
            };
          }
        } else {
          acc[key] = value;
        }
      });
      return acc;
    }, {})
  };
}
function useInteractions(propsList) {
  if (propsList === void 0) {
    propsList = [];
  }
  const referenceDeps = propsList.map((key) => key == null ? void 0 : key.reference);
  const floatingDeps = propsList.map((key) => key == null ? void 0 : key.floating);
  const itemDeps = propsList.map((key) => key == null ? void 0 : key.item);
  const getReferenceProps = React.useCallback(
    (userProps) => mergeProps(userProps, propsList, "reference"),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    referenceDeps
  );
  const getFloatingProps = React.useCallback(
    (userProps) => mergeProps(userProps, propsList, "floating"),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    floatingDeps
  );
  const getItemProps = React.useCallback(
    (userProps) => mergeProps(userProps, propsList, "item"),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    itemDeps
  );
  return React.useMemo(() => ({
    getReferenceProps,
    getFloatingProps,
    getItemProps
  }), [getReferenceProps, getFloatingProps, getItemProps]);
}
var camelCaseToKebabCase = (str) => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? "-" : "") + $.toLowerCase());
function execWithArgsOrReturn(valueOrFn, args) {
  return typeof valueOrFn === "function" ? valueOrFn(args) : valueOrFn;
}
function useDelayUnmount(open, durationMs) {
  const [isMounted, setIsMounted] = React.useState(open);
  if (open && !isMounted) {
    setIsMounted(true);
  }
  React.useEffect(() => {
    if (!open && isMounted) {
      const timeout = setTimeout(() => setIsMounted(false), durationMs);
      return () => clearTimeout(timeout);
    }
  }, [open, isMounted, durationMs]);
  return isMounted;
}
function useTransitionStatus(context, props) {
  if (props === void 0) {
    props = {};
  }
  const {
    open,
    elements: {
      floating
    }
  } = context;
  const {
    duration = 250
  } = props;
  const isNumberDuration = typeof duration === "number";
  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;
  const [status, setStatus] = React.useState("unmounted");
  const isMounted = useDelayUnmount(open, closeDuration);
  if (!isMounted && status === "close") {
    setStatus("unmounted");
  }
  index(() => {
    if (!floating) return;
    if (open) {
      setStatus("initial");
      const frame = requestAnimationFrame(() => {
        setStatus("open");
      });
      return () => {
        cancelAnimationFrame(frame);
      };
    }
    setStatus("close");
  }, [open, floating]);
  return {
    isMounted,
    status
  };
}
function useTransitionStyles(context, props) {
  if (props === void 0) {
    props = {};
  }
  const {
    initial: unstable_initial = {
      opacity: 0
    },
    open: unstable_open,
    close: unstable_close,
    common: unstable_common,
    duration = 250
  } = props;
  const placement = context.placement;
  const side = placement.split("-")[0];
  const fnArgs = React.useMemo(() => ({
    side,
    placement
  }), [side, placement]);
  const isNumberDuration = typeof duration === "number";
  const openDuration = (isNumberDuration ? duration : duration.open) || 0;
  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;
  const [styles, setStyles] = React.useState(() => ({
    ...execWithArgsOrReturn(unstable_common, fnArgs),
    ...execWithArgsOrReturn(unstable_initial, fnArgs)
  }));
  const {
    isMounted,
    status
  } = useTransitionStatus(context, {
    duration
  });
  const initialRef = useLatestRef(unstable_initial);
  const openRef = useLatestRef(unstable_open);
  const closeRef = useLatestRef(unstable_close);
  const commonRef = useLatestRef(unstable_common);
  index(() => {
    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);
    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);
    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);
    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {
      acc[key] = "";
      return acc;
    }, {});
    if (status === "initial") {
      setStyles((styles2) => ({
        transitionProperty: styles2.transitionProperty,
        ...commonStyles,
        ...initialStyles
      }));
    }
    if (status === "open") {
      setStyles({
        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(","),
        transitionDuration: openDuration + "ms",
        ...commonStyles,
        ...openStyles
      });
    }
    if (status === "close") {
      const styles2 = closeStyles || initialStyles;
      setStyles({
        transitionProperty: Object.keys(styles2).map(camelCaseToKebabCase).join(","),
        transitionDuration: closeDuration + "ms",
        ...commonStyles,
        ...styles2
      });
    }
  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);
  return {
    isMounted,
    styles
  };
}

// node_modules/@blocknote/react/dist/blocknote-react.js
var import_react_dom4 = __toESM(require_react_dom());

// node_modules/@blocknote/react/node_modules/@tiptap/core/dist/index.js
function createChainableState(config) {
  const { state, transaction } = config;
  let { selection } = transaction;
  let { doc } = transaction;
  let { storedMarks } = transaction;
  return {
    ...state,
    apply: state.apply.bind(state),
    applyTransaction: state.applyTransaction.bind(state),
    plugins: state.plugins,
    schema: state.schema,
    reconfigure: state.reconfigure.bind(state),
    toJSON: state.toJSON.bind(state),
    get storedMarks() {
      return storedMarks;
    },
    get selection() {
      return selection;
    },
    get doc() {
      return doc;
    },
    get tr() {
      selection = transaction.selection;
      doc = transaction.doc;
      storedMarks = transaction.storedMarks;
      return transaction;
    }
  };
}
var CommandManager = class {
  constructor(props) {
    this.editor = props.editor;
    this.rawCommands = this.editor.extensionManager.commands;
    this.customState = props.state;
  }
  get hasCustomState() {
    return !!this.customState;
  }
  get state() {
    return this.customState || this.editor.state;
  }
  get commands() {
    const { rawCommands, editor, state } = this;
    const { view } = editor;
    const { tr: tr2 } = state;
    const props = this.buildProps(tr2);
    return Object.fromEntries(Object.entries(rawCommands).map(([name, command2]) => {
      const method = (...args) => {
        const callback = command2(...args)(props);
        if (!tr2.getMeta("preventDispatch") && !this.hasCustomState) {
          view.dispatch(tr2);
        }
        return callback;
      };
      return [name, method];
    }));
  }
  get chain() {
    return () => this.createChain();
  }
  get can() {
    return () => this.createCan();
  }
  createChain(startTr, shouldDispatch = true) {
    const { rawCommands, editor, state } = this;
    const { view } = editor;
    const callbacks = [];
    const hasStartTransaction = !!startTr;
    const tr2 = startTr || state.tr;
    const run = () => {
      if (!hasStartTransaction && shouldDispatch && !tr2.getMeta("preventDispatch") && !this.hasCustomState) {
        view.dispatch(tr2);
      }
      return callbacks.every((callback) => callback === true);
    };
    const chain = {
      ...Object.fromEntries(Object.entries(rawCommands).map(([name, command2]) => {
        const chainedCommand = (...args) => {
          const props = this.buildProps(tr2, shouldDispatch);
          const callback = command2(...args)(props);
          callbacks.push(callback);
          return chain;
        };
        return [name, chainedCommand];
      })),
      run
    };
    return chain;
  }
  createCan(startTr) {
    const { rawCommands, state } = this;
    const dispatch = false;
    const tr2 = startTr || state.tr;
    const props = this.buildProps(tr2, dispatch);
    const formattedCommands = Object.fromEntries(Object.entries(rawCommands).map(([name, command2]) => {
      return [name, (...args) => command2(...args)({ ...props, dispatch: void 0 })];
    }));
    return {
      ...formattedCommands,
      chain: () => this.createChain(tr2, dispatch)
    };
  }
  buildProps(tr2, shouldDispatch = true) {
    const { rawCommands, editor, state } = this;
    const { view } = editor;
    const props = {
      tr: tr2,
      editor,
      view,
      state: createChainableState({
        state,
        transaction: tr2
      }),
      dispatch: shouldDispatch ? () => void 0 : void 0,
      chain: () => this.createChain(tr2, shouldDispatch),
      can: () => this.createCan(tr2),
      get commands() {
        return Object.fromEntries(Object.entries(rawCommands).map(([name, command2]) => {
          return [name, (...args) => command2(...args)(props)];
        }));
      }
    };
    return props;
  }
};
function getExtensionField(extension, field, context) {
  if (extension.config[field] === void 0 && extension.parent) {
    return getExtensionField(extension.parent, field, context);
  }
  if (typeof extension.config[field] === "function") {
    const value = extension.config[field].bind({
      ...context,
      parent: extension.parent ? getExtensionField(extension.parent, field, context) : null
    });
    return value;
  }
  return extension.config[field];
}
function splitExtensions(extensions) {
  const baseExtensions = extensions.filter((extension) => extension.type === "extension");
  const nodeExtensions = extensions.filter((extension) => extension.type === "node");
  const markExtensions = extensions.filter((extension) => extension.type === "mark");
  return {
    baseExtensions,
    nodeExtensions,
    markExtensions
  };
}
function getNodeType(nameOrType, schema) {
  if (typeof nameOrType === "string") {
    if (!schema.nodes[nameOrType]) {
      throw Error(`There is no node type named '${nameOrType}'. Maybe you forgot to add the extension?`);
    }
    return schema.nodes[nameOrType];
  }
  return nameOrType;
}
function mergeAttributes(...objects) {
  return objects.filter((item) => !!item).reduce((items, item) => {
    const mergedAttributes = { ...items };
    Object.entries(item).forEach(([key, value]) => {
      const exists = mergedAttributes[key];
      if (!exists) {
        mergedAttributes[key] = value;
        return;
      }
      if (key === "class") {
        const valueClasses = value ? String(value).split(" ") : [];
        const existingClasses = mergedAttributes[key] ? mergedAttributes[key].split(" ") : [];
        const insertClasses = valueClasses.filter((valueClass) => !existingClasses.includes(valueClass));
        mergedAttributes[key] = [...existingClasses, ...insertClasses].join(" ");
      } else if (key === "style") {
        const newStyles = value ? value.split(";").map((style) => style.trim()).filter(Boolean) : [];
        const existingStyles = mergedAttributes[key] ? mergedAttributes[key].split(";").map((style) => style.trim()).filter(Boolean) : [];
        const styleMap = /* @__PURE__ */ new Map();
        existingStyles.forEach((style) => {
          const [property, val] = style.split(":").map((part) => part.trim());
          styleMap.set(property, val);
        });
        newStyles.forEach((style) => {
          const [property, val] = style.split(":").map((part) => part.trim());
          styleMap.set(property, val);
        });
        mergedAttributes[key] = Array.from(styleMap.entries()).map(([property, val]) => `${property}: ${val}`).join("; ");
      } else {
        mergedAttributes[key] = value;
      }
    });
    return mergedAttributes;
  }, {});
}
function getRenderedAttributes(nodeOrMark, extensionAttributes) {
  return extensionAttributes.filter((attribute) => attribute.type === nodeOrMark.type.name).filter((item) => item.attribute.rendered).map((item) => {
    if (!item.attribute.renderHTML) {
      return {
        [item.name]: nodeOrMark.attrs[item.name]
      };
    }
    return item.attribute.renderHTML(nodeOrMark.attrs) || {};
  }).reduce((attributes, attribute) => mergeAttributes(attributes, attribute), {});
}
function isFunction(value) {
  return typeof value === "function";
}
function callOrReturn(value, context = void 0, ...props) {
  if (isFunction(value)) {
    if (context) {
      return value.bind(context)(...props);
    }
    return value(...props);
  }
  return value;
}
function isRegExp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
function getType(value) {
  return Object.prototype.toString.call(value).slice(8, -1);
}
function isPlainObject(value) {
  if (getType(value) !== "Object") {
    return false;
  }
  return value.constructor === Object && Object.getPrototypeOf(value) === Object.prototype;
}
function mergeDeep(target, source) {
  const output = { ...target };
  if (isPlainObject(target) && isPlainObject(source)) {
    Object.keys(source).forEach((key) => {
      if (isPlainObject(source[key]) && isPlainObject(target[key])) {
        output[key] = mergeDeep(target[key], source[key]);
      } else {
        output[key] = source[key];
      }
    });
  }
  return output;
}
var Mark = class _Mark {
  constructor(config = {}) {
    this.type = "mark";
    this.name = "mark";
    this.parent = null;
    this.child = null;
    this.config = {
      name: this.name,
      defaultOptions: {}
    };
    this.config = {
      ...this.config,
      ...config
    };
    this.name = this.config.name;
    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {
      console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`);
    }
    this.options = this.config.defaultOptions;
    if (this.config.addOptions) {
      this.options = callOrReturn(getExtensionField(this, "addOptions", {
        name: this.name
      }));
    }
    this.storage = callOrReturn(getExtensionField(this, "addStorage", {
      name: this.name,
      options: this.options
    })) || {};
  }
  static create(config = {}) {
    return new _Mark(config);
  }
  configure(options = {}) {
    const extension = this.extend({
      ...this.config,
      addOptions: () => {
        return mergeDeep(this.options, options);
      }
    });
    extension.name = this.name;
    extension.parent = this.parent;
    return extension;
  }
  extend(extendedConfig = {}) {
    const extension = new _Mark(extendedConfig);
    extension.parent = this;
    this.child = extension;
    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name;
    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {
      console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${extension.name}".`);
    }
    extension.options = callOrReturn(getExtensionField(extension, "addOptions", {
      name: extension.name
    }));
    extension.storage = callOrReturn(getExtensionField(extension, "addStorage", {
      name: extension.name,
      options: extension.options
    }));
    return extension;
  }
  static handleExit({ editor, mark }) {
    const { tr: tr2 } = editor.state;
    const currentPos = editor.state.selection.$from;
    const isAtEnd = currentPos.pos === currentPos.end();
    if (isAtEnd) {
      const currentMarks = currentPos.marks();
      const isInMark = !!currentMarks.find((m) => (m === null || m === void 0 ? void 0 : m.type.name) === mark.name);
      if (!isInMark) {
        return false;
      }
      const removeMark = currentMarks.find((m) => (m === null || m === void 0 ? void 0 : m.type.name) === mark.name);
      if (removeMark) {
        tr2.removeStoredMark(removeMark);
      }
      tr2.insertText(" ", currentPos.pos);
      editor.view.dispatch(tr2);
      return true;
    }
    return false;
  }
};
var Extension = class _Extension {
  constructor(config = {}) {
    this.type = "extension";
    this.name = "extension";
    this.parent = null;
    this.child = null;
    this.config = {
      name: this.name,
      defaultOptions: {}
    };
    this.config = {
      ...this.config,
      ...config
    };
    this.name = this.config.name;
    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {
      console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`);
    }
    this.options = this.config.defaultOptions;
    if (this.config.addOptions) {
      this.options = callOrReturn(getExtensionField(this, "addOptions", {
        name: this.name
      }));
    }
    this.storage = callOrReturn(getExtensionField(this, "addStorage", {
      name: this.name,
      options: this.options
    })) || {};
  }
  static create(config = {}) {
    return new _Extension(config);
  }
  configure(options = {}) {
    const extension = this.extend({
      ...this.config,
      addOptions: () => {
        return mergeDeep(this.options, options);
      }
    });
    extension.name = this.name;
    extension.parent = this.parent;
    return extension;
  }
  extend(extendedConfig = {}) {
    const extension = new _Extension({ ...this.config, ...extendedConfig });
    extension.parent = this;
    this.child = extension;
    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name;
    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {
      console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${extension.name}".`);
    }
    extension.options = callOrReturn(getExtensionField(extension, "addOptions", {
      name: extension.name
    }));
    extension.storage = callOrReturn(getExtensionField(extension, "addStorage", {
      name: extension.name,
      options: extension.options
    }));
    return extension;
  }
};
function getTextBetween(startNode, range, options) {
  const { from, to: to2 } = range;
  const { blockSeparator = "\n\n", textSerializers = {} } = options || {};
  let text = "";
  startNode.nodesBetween(from, to2, (node, pos, parent, index3) => {
    var _a;
    if (node.isBlock && pos > from) {
      text += blockSeparator;
    }
    const textSerializer = textSerializers === null || textSerializers === void 0 ? void 0 : textSerializers[node.type.name];
    if (textSerializer) {
      if (parent) {
        text += textSerializer({
          node,
          pos,
          parent,
          index: index3,
          range
        });
      }
      return false;
    }
    if (node.isText) {
      text += (_a = node === null || node === void 0 ? void 0 : node.text) === null || _a === void 0 ? void 0 : _a.slice(Math.max(from, pos) - pos, to2 - pos);
    }
  });
  return text;
}
function getTextSerializersFromSchema(schema) {
  return Object.fromEntries(Object.entries(schema.nodes).filter(([, node]) => node.spec.toText).map(([name, node]) => [name, node.spec.toText]));
}
var ClipboardTextSerializer = Extension.create({
  name: "clipboardTextSerializer",
  addOptions() {
    return {
      blockSeparator: void 0
    };
  },
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("clipboardTextSerializer"),
        props: {
          clipboardTextSerializer: () => {
            const { editor } = this;
            const { state, schema } = editor;
            const { doc, selection } = state;
            const { ranges } = selection;
            const from = Math.min(...ranges.map((range2) => range2.$from.pos));
            const to2 = Math.max(...ranges.map((range2) => range2.$to.pos));
            const textSerializers = getTextSerializersFromSchema(schema);
            const range = { from, to: to2 };
            return getTextBetween(doc, range, {
              ...this.options.blockSeparator !== void 0 ? { blockSeparator: this.options.blockSeparator } : {},
              textSerializers
            });
          }
        }
      })
    ];
  }
});
var blur = () => ({ editor, view }) => {
  requestAnimationFrame(() => {
    var _a;
    if (!editor.isDestroyed) {
      view.dom.blur();
      (_a = window === null || window === void 0 ? void 0 : window.getSelection()) === null || _a === void 0 ? void 0 : _a.removeAllRanges();
    }
  });
  return true;
};
var clearContent = (emitUpdate = false) => ({ commands: commands2 }) => {
  return commands2.setContent("", emitUpdate);
};
var clearNodes = () => ({ state, tr: tr2, dispatch }) => {
  const { selection } = tr2;
  const { ranges } = selection;
  if (!dispatch) {
    return true;
  }
  ranges.forEach(({ $from, $to }) => {
    state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
      if (node.type.isText) {
        return;
      }
      const { doc, mapping } = tr2;
      const $mappedFrom = doc.resolve(mapping.map(pos));
      const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize));
      const nodeRange = $mappedFrom.blockRange($mappedTo);
      if (!nodeRange) {
        return;
      }
      const targetLiftDepth = liftTarget(nodeRange);
      if (node.type.isTextblock) {
        const { defaultType } = $mappedFrom.parent.contentMatchAt($mappedFrom.index());
        tr2.setNodeMarkup(nodeRange.start, defaultType);
      }
      if (targetLiftDepth || targetLiftDepth === 0) {
        tr2.lift(nodeRange, targetLiftDepth);
      }
    });
  });
  return true;
};
var command = (fn2) => (props) => {
  return fn2(props);
};
var createParagraphNear2 = () => ({ state, dispatch }) => {
  return createParagraphNear(state, dispatch);
};
var cut = (originRange, targetPos) => ({ editor, tr: tr2 }) => {
  const { state } = editor;
  const contentSlice = state.doc.slice(originRange.from, originRange.to);
  tr2.deleteRange(originRange.from, originRange.to);
  const newPos = tr2.mapping.map(targetPos);
  tr2.insert(newPos, contentSlice.content);
  tr2.setSelection(new TextSelection(tr2.doc.resolve(newPos - 1)));
  return true;
};
var deleteCurrentNode = () => ({ tr: tr2, dispatch }) => {
  const { selection } = tr2;
  const currentNode = selection.$anchor.node();
  if (currentNode.content.size > 0) {
    return false;
  }
  const $pos = tr2.selection.$anchor;
  for (let depth = $pos.depth; depth > 0; depth -= 1) {
    const node = $pos.node(depth);
    if (node.type === currentNode.type) {
      if (dispatch) {
        const from = $pos.before(depth);
        const to2 = $pos.after(depth);
        tr2.delete(from, to2).scrollIntoView();
      }
      return true;
    }
  }
  return false;
};
var deleteNode = (typeOrName) => ({ tr: tr2, state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  const $pos = tr2.selection.$anchor;
  for (let depth = $pos.depth; depth > 0; depth -= 1) {
    const node = $pos.node(depth);
    if (node.type === type) {
      if (dispatch) {
        const from = $pos.before(depth);
        const to2 = $pos.after(depth);
        tr2.delete(from, to2).scrollIntoView();
      }
      return true;
    }
  }
  return false;
};
var deleteRange = (range) => ({ tr: tr2, dispatch }) => {
  const { from, to: to2 } = range;
  if (dispatch) {
    tr2.delete(from, to2);
  }
  return true;
};
var deleteSelection2 = () => ({ state, dispatch }) => {
  return deleteSelection(state, dispatch);
};
var enter = () => ({ commands: commands2 }) => {
  return commands2.keyboardShortcut("Enter");
};
var exitCode2 = () => ({ state, dispatch }) => {
  return exitCode(state, dispatch);
};
function objectIncludes(object1, object2, options = { strict: true }) {
  const keys = Object.keys(object2);
  if (!keys.length) {
    return true;
  }
  return keys.every((key) => {
    if (options.strict) {
      return object2[key] === object1[key];
    }
    if (isRegExp(object2[key])) {
      return object2[key].test(object1[key]);
    }
    return object2[key] === object1[key];
  });
}
function findMarkInSet(marks, type, attributes = {}) {
  return marks.find((item) => {
    return item.type === type && objectIncludes(
      // Only check equality for the attributes that are provided
      Object.fromEntries(Object.keys(attributes).map((k2) => [k2, item.attrs[k2]])),
      attributes
    );
  });
}
function isMarkInSet(marks, type, attributes = {}) {
  return !!findMarkInSet(marks, type, attributes);
}
function getMarkRange($pos, type, attributes) {
  var _a;
  if (!$pos || !type) {
    return;
  }
  let start = $pos.parent.childAfter($pos.parentOffset);
  if (!start.node || !start.node.marks.some((mark2) => mark2.type === type)) {
    start = $pos.parent.childBefore($pos.parentOffset);
  }
  if (!start.node || !start.node.marks.some((mark2) => mark2.type === type)) {
    return;
  }
  attributes = attributes || ((_a = start.node.marks[0]) === null || _a === void 0 ? void 0 : _a.attrs);
  const mark = findMarkInSet([...start.node.marks], type, attributes);
  if (!mark) {
    return;
  }
  let startIndex = start.index;
  let startPos = $pos.start() + start.offset;
  let endIndex = startIndex + 1;
  let endPos = startPos + start.node.nodeSize;
  while (startIndex > 0 && isMarkInSet([...$pos.parent.child(startIndex - 1).marks], type, attributes)) {
    startIndex -= 1;
    startPos -= $pos.parent.child(startIndex).nodeSize;
  }
  while (endIndex < $pos.parent.childCount && isMarkInSet([...$pos.parent.child(endIndex).marks], type, attributes)) {
    endPos += $pos.parent.child(endIndex).nodeSize;
    endIndex += 1;
  }
  return {
    from: startPos,
    to: endPos
  };
}
function getMarkType(nameOrType, schema) {
  if (typeof nameOrType === "string") {
    if (!schema.marks[nameOrType]) {
      throw Error(`There is no mark type named '${nameOrType}'. Maybe you forgot to add the extension?`);
    }
    return schema.marks[nameOrType];
  }
  return nameOrType;
}
var extendMarkRange = (typeOrName, attributes = {}) => ({ tr: tr2, state, dispatch }) => {
  const type = getMarkType(typeOrName, state.schema);
  const { doc, selection } = tr2;
  const { $from, from, to: to2 } = selection;
  if (dispatch) {
    const range = getMarkRange($from, type, attributes);
    if (range && range.from <= from && range.to >= to2) {
      const newSelection = TextSelection.create(doc, range.from, range.to);
      tr2.setSelection(newSelection);
    }
  }
  return true;
};
var first = (commands2) => (props) => {
  const items = typeof commands2 === "function" ? commands2(props) : commands2;
  for (let i2 = 0; i2 < items.length; i2 += 1) {
    if (items[i2](props)) {
      return true;
    }
  }
  return false;
};
function isTextSelection(value) {
  return value instanceof TextSelection;
}
function minMax(value = 0, min = 0, max = 0) {
  return Math.min(Math.max(value, min), max);
}
function resolveFocusPosition(doc, position = null) {
  if (!position) {
    return null;
  }
  const selectionAtStart = Selection.atStart(doc);
  const selectionAtEnd = Selection.atEnd(doc);
  if (position === "start" || position === true) {
    return selectionAtStart;
  }
  if (position === "end") {
    return selectionAtEnd;
  }
  const minPos = selectionAtStart.from;
  const maxPos = selectionAtEnd.to;
  if (position === "all") {
    return TextSelection.create(doc, minMax(0, minPos, maxPos), minMax(doc.content.size, minPos, maxPos));
  }
  return TextSelection.create(doc, minMax(position, minPos, maxPos), minMax(position, minPos, maxPos));
}
function isAndroid() {
  return navigator.platform === "Android" || /android/i.test(navigator.userAgent);
}
function isiOS() {
  return [
    "iPad Simulator",
    "iPhone Simulator",
    "iPod Simulator",
    "iPad",
    "iPhone",
    "iPod"
  ].includes(navigator.platform) || navigator.userAgent.includes("Mac") && "ontouchend" in document;
}
var focus = (position = null, options = {}) => ({ editor, view, tr: tr2, dispatch }) => {
  options = {
    scrollIntoView: true,
    ...options
  };
  const delayedFocus = () => {
    if (isiOS() || isAndroid()) {
      view.dom.focus();
    }
    requestAnimationFrame(() => {
      if (!editor.isDestroyed) {
        view.focus();
        if (options === null || options === void 0 ? void 0 : options.scrollIntoView) {
          editor.commands.scrollIntoView();
        }
      }
    });
  };
  if (view.hasFocus() && position === null || position === false) {
    return true;
  }
  if (dispatch && position === null && !isTextSelection(editor.state.selection)) {
    delayedFocus();
    return true;
  }
  const selection = resolveFocusPosition(tr2.doc, position) || editor.state.selection;
  const isSameSelection = editor.state.selection.eq(selection);
  if (dispatch) {
    if (!isSameSelection) {
      tr2.setSelection(selection);
    }
    if (isSameSelection && tr2.storedMarks) {
      tr2.setStoredMarks(tr2.storedMarks);
    }
    delayedFocus();
  }
  return true;
};
var forEach = (items, fn2) => (props) => {
  return items.every((item, index3) => fn2(item, { ...props, index: index3 }));
};
var insertContent = (value, options) => ({ tr: tr2, commands: commands2 }) => {
  return commands2.insertContentAt({ from: tr2.selection.from, to: tr2.selection.to }, value, options);
};
var removeWhitespaces = (node) => {
  const children = node.childNodes;
  for (let i2 = children.length - 1; i2 >= 0; i2 -= 1) {
    const child = children[i2];
    if (child.nodeType === 3 && child.nodeValue && /^(\n\s\s|\n)$/.test(child.nodeValue)) {
      node.removeChild(child);
    } else if (child.nodeType === 1) {
      removeWhitespaces(child);
    }
  }
  return node;
};
function elementFromString(value) {
  const wrappedValue = `<body>${value}</body>`;
  const html = new window.DOMParser().parseFromString(wrappedValue, "text/html").body;
  return removeWhitespaces(html);
}
function createNodeFromContent(content, schema, options) {
  if (content instanceof Node2 || content instanceof Fragment) {
    return content;
  }
  options = {
    slice: true,
    parseOptions: {},
    ...options
  };
  const isJSONContent = typeof content === "object" && content !== null;
  const isTextContent = typeof content === "string";
  if (isJSONContent) {
    try {
      const isArrayContent = Array.isArray(content) && content.length > 0;
      if (isArrayContent) {
        return Fragment.fromArray(content.map((item) => schema.nodeFromJSON(item)));
      }
      const node = schema.nodeFromJSON(content);
      if (options.errorOnInvalidContent) {
        node.check();
      }
      return node;
    } catch (error2) {
      if (options.errorOnInvalidContent) {
        throw new Error("[tiptap error]: Invalid JSON content", { cause: error2 });
      }
      console.warn("[tiptap warn]: Invalid content.", "Passed value:", content, "Error:", error2);
      return createNodeFromContent("", schema, options);
    }
  }
  if (isTextContent) {
    if (options.errorOnInvalidContent) {
      let hasInvalidContent = false;
      let invalidContent = "";
      const contentCheckSchema = new Schema({
        topNode: schema.spec.topNode,
        marks: schema.spec.marks,
        // Prosemirror's schemas are executed such that: the last to execute, matches last
        // This means that we can add a catch-all node at the end of the schema to catch any content that we don't know how to handle
        nodes: schema.spec.nodes.append({
          __tiptap__private__unknown__catch__all__node: {
            content: "inline*",
            group: "block",
            parseDOM: [
              {
                tag: "*",
                getAttrs: (e) => {
                  hasInvalidContent = true;
                  invalidContent = typeof e === "string" ? e : e.outerHTML;
                  return null;
                }
              }
            ]
          }
        })
      });
      if (options.slice) {
        DOMParser.fromSchema(contentCheckSchema).parseSlice(elementFromString(content), options.parseOptions);
      } else {
        DOMParser.fromSchema(contentCheckSchema).parse(elementFromString(content), options.parseOptions);
      }
      if (options.errorOnInvalidContent && hasInvalidContent) {
        throw new Error("[tiptap error]: Invalid HTML content", { cause: new Error(`Invalid element found: ${invalidContent}`) });
      }
    }
    const parser = DOMParser.fromSchema(schema);
    if (options.slice) {
      return parser.parseSlice(elementFromString(content), options.parseOptions).content;
    }
    return parser.parse(elementFromString(content), options.parseOptions);
  }
  return createNodeFromContent("", schema, options);
}
function selectionToInsertionEnd(tr2, startLen, bias) {
  const last = tr2.steps.length - 1;
  if (last < startLen) {
    return;
  }
  const step = tr2.steps[last];
  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {
    return;
  }
  const map = tr2.mapping.maps[last];
  let end = 0;
  map.forEach((_from, _to, _newFrom, newTo) => {
    if (end === 0) {
      end = newTo;
    }
  });
  tr2.setSelection(Selection.near(tr2.doc.resolve(end), bias));
}
var isFragment = (nodeOrFragment) => {
  return !("type" in nodeOrFragment);
};
var insertContentAt = (position, value, options) => ({ tr: tr2, dispatch, editor }) => {
  var _a;
  if (dispatch) {
    options = {
      parseOptions: editor.options.parseOptions,
      updateSelection: true,
      applyInputRules: false,
      applyPasteRules: false,
      ...options
    };
    let content;
    try {
      content = createNodeFromContent(value, editor.schema, {
        parseOptions: {
          preserveWhitespace: "full",
          ...options.parseOptions
        },
        errorOnInvalidContent: (_a = options.errorOnInvalidContent) !== null && _a !== void 0 ? _a : editor.options.enableContentCheck
      });
    } catch (e) {
      editor.emit("contentError", {
        editor,
        error: e,
        disableCollaboration: () => {
          if (editor.storage.collaboration) {
            editor.storage.collaboration.isDisabled = true;
          }
        }
      });
      return false;
    }
    let { from, to: to2 } = typeof position === "number" ? { from: position, to: position } : { from: position.from, to: position.to };
    let isOnlyTextContent = true;
    let isOnlyBlockContent = true;
    const nodes = isFragment(content) ? content : [content];
    nodes.forEach((node) => {
      node.check();
      isOnlyTextContent = isOnlyTextContent ? node.isText && node.marks.length === 0 : false;
      isOnlyBlockContent = isOnlyBlockContent ? node.isBlock : false;
    });
    if (from === to2 && isOnlyBlockContent) {
      const { parent } = tr2.doc.resolve(from);
      const isEmptyTextBlock = parent.isTextblock && !parent.type.spec.code && !parent.childCount;
      if (isEmptyTextBlock) {
        from -= 1;
        to2 += 1;
      }
    }
    let newContent;
    if (isOnlyTextContent) {
      if (Array.isArray(value)) {
        newContent = value.map((v2) => v2.text || "").join("");
      } else if (value instanceof Fragment) {
        let text = "";
        value.forEach((node) => {
          if (node.text) {
            text += node.text;
          }
        });
        newContent = text;
      } else if (typeof value === "object" && !!value && !!value.text) {
        newContent = value.text;
      } else {
        newContent = value;
      }
      tr2.insertText(newContent, from, to2);
    } else {
      newContent = content;
      tr2.replaceWith(from, to2, newContent);
    }
    if (options.updateSelection) {
      selectionToInsertionEnd(tr2, tr2.steps.length - 1, -1);
    }
    if (options.applyInputRules) {
      tr2.setMeta("applyInputRules", { from, text: newContent });
    }
    if (options.applyPasteRules) {
      tr2.setMeta("applyPasteRules", { from, text: newContent });
    }
  }
  return true;
};
var joinUp2 = () => ({ state, dispatch }) => {
  return joinUp(state, dispatch);
};
var joinDown2 = () => ({ state, dispatch }) => {
  return joinDown(state, dispatch);
};
var joinBackward2 = () => ({ state, dispatch }) => {
  return joinBackward(state, dispatch);
};
var joinForward2 = () => ({ state, dispatch }) => {
  return joinForward(state, dispatch);
};
var joinItemBackward = () => ({ state, dispatch, tr: tr2 }) => {
  try {
    const point = joinPoint(state.doc, state.selection.$from.pos, -1);
    if (point === null || point === void 0) {
      return false;
    }
    tr2.join(point, 2);
    if (dispatch) {
      dispatch(tr2);
    }
    return true;
  } catch {
    return false;
  }
};
var joinItemForward = () => ({ state, dispatch, tr: tr2 }) => {
  try {
    const point = joinPoint(state.doc, state.selection.$from.pos, 1);
    if (point === null || point === void 0) {
      return false;
    }
    tr2.join(point, 2);
    if (dispatch) {
      dispatch(tr2);
    }
    return true;
  } catch {
    return false;
  }
};
var joinTextblockBackward2 = () => ({ state, dispatch }) => {
  return joinTextblockBackward(state, dispatch);
};
var joinTextblockForward2 = () => ({ state, dispatch }) => {
  return joinTextblockForward(state, dispatch);
};
function isMacOS() {
  return typeof navigator !== "undefined" ? /Mac/.test(navigator.platform) : false;
}
function normalizeKeyName(name) {
  const parts = name.split(/-(?!$)/);
  let result = parts[parts.length - 1];
  if (result === "Space") {
    result = " ";
  }
  let alt;
  let ctrl;
  let shift2;
  let meta;
  for (let i2 = 0; i2 < parts.length - 1; i2 += 1) {
    const mod = parts[i2];
    if (/^(cmd|meta|m)$/i.test(mod)) {
      meta = true;
    } else if (/^a(lt)?$/i.test(mod)) {
      alt = true;
    } else if (/^(c|ctrl|control)$/i.test(mod)) {
      ctrl = true;
    } else if (/^s(hift)?$/i.test(mod)) {
      shift2 = true;
    } else if (/^mod$/i.test(mod)) {
      if (isiOS() || isMacOS()) {
        meta = true;
      } else {
        ctrl = true;
      }
    } else {
      throw new Error(`Unrecognized modifier name: ${mod}`);
    }
  }
  if (alt) {
    result = `Alt-${result}`;
  }
  if (ctrl) {
    result = `Ctrl-${result}`;
  }
  if (meta) {
    result = `Meta-${result}`;
  }
  if (shift2) {
    result = `Shift-${result}`;
  }
  return result;
}
var keyboardShortcut = (name) => ({ editor, view, tr: tr2, dispatch }) => {
  const keys = normalizeKeyName(name).split(/-(?!$)/);
  const key = keys.find((item) => !["Alt", "Ctrl", "Meta", "Shift"].includes(item));
  const event = new KeyboardEvent("keydown", {
    key: key === "Space" ? " " : key,
    altKey: keys.includes("Alt"),
    ctrlKey: keys.includes("Ctrl"),
    metaKey: keys.includes("Meta"),
    shiftKey: keys.includes("Shift"),
    bubbles: true,
    cancelable: true
  });
  const capturedTransaction = editor.captureTransaction(() => {
    view.someProp("handleKeyDown", (f) => f(view, event));
  });
  capturedTransaction === null || capturedTransaction === void 0 ? void 0 : capturedTransaction.steps.forEach((step) => {
    const newStep = step.map(tr2.mapping);
    if (newStep && dispatch) {
      tr2.maybeStep(newStep);
    }
  });
  return true;
};
function isNodeActive(state, typeOrName, attributes = {}) {
  const { from, to: to2, empty } = state.selection;
  const type = typeOrName ? getNodeType(typeOrName, state.schema) : null;
  const nodeRanges = [];
  state.doc.nodesBetween(from, to2, (node, pos) => {
    if (node.isText) {
      return;
    }
    const relativeFrom = Math.max(from, pos);
    const relativeTo = Math.min(to2, pos + node.nodeSize);
    nodeRanges.push({
      node,
      from: relativeFrom,
      to: relativeTo
    });
  });
  const selectionRange = to2 - from;
  const matchedNodeRanges = nodeRanges.filter((nodeRange) => {
    if (!type) {
      return true;
    }
    return type.name === nodeRange.node.type.name;
  }).filter((nodeRange) => objectIncludes(nodeRange.node.attrs, attributes, { strict: false }));
  if (empty) {
    return !!matchedNodeRanges.length;
  }
  const range = matchedNodeRanges.reduce((sum, nodeRange) => sum + nodeRange.to - nodeRange.from, 0);
  return range >= selectionRange;
}
var lift2 = (typeOrName, attributes = {}) => ({ state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  const isActive = isNodeActive(state, type, attributes);
  if (!isActive) {
    return false;
  }
  return lift(state, dispatch);
};
var liftEmptyBlock2 = () => ({ state, dispatch }) => {
  return liftEmptyBlock(state, dispatch);
};
var liftListItem2 = (typeOrName) => ({ state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  return liftListItem(type)(state, dispatch);
};
var newlineInCode2 = () => ({ state, dispatch }) => {
  return newlineInCode(state, dispatch);
};
function getSchemaTypeNameByName(name, schema) {
  if (schema.nodes[name]) {
    return "node";
  }
  if (schema.marks[name]) {
    return "mark";
  }
  return null;
}
function deleteProps(obj, propOrProps) {
  const props = typeof propOrProps === "string" ? [propOrProps] : propOrProps;
  return Object.keys(obj).reduce((newObj, prop) => {
    if (!props.includes(prop)) {
      newObj[prop] = obj[prop];
    }
    return newObj;
  }, {});
}
var resetAttributes = (typeOrName, attributes) => ({ tr: tr2, state, dispatch }) => {
  let nodeType = null;
  let markType = null;
  const schemaType = getSchemaTypeNameByName(typeof typeOrName === "string" ? typeOrName : typeOrName.name, state.schema);
  if (!schemaType) {
    return false;
  }
  if (schemaType === "node") {
    nodeType = getNodeType(typeOrName, state.schema);
  }
  if (schemaType === "mark") {
    markType = getMarkType(typeOrName, state.schema);
  }
  if (dispatch) {
    tr2.selection.ranges.forEach((range) => {
      state.doc.nodesBetween(range.$from.pos, range.$to.pos, (node, pos) => {
        if (nodeType && nodeType === node.type) {
          tr2.setNodeMarkup(pos, void 0, deleteProps(node.attrs, attributes));
        }
        if (markType && node.marks.length) {
          node.marks.forEach((mark) => {
            if (markType === mark.type) {
              tr2.addMark(pos, pos + node.nodeSize, markType.create(deleteProps(mark.attrs, attributes)));
            }
          });
        }
      });
    });
  }
  return true;
};
var scrollIntoView = () => ({ tr: tr2, dispatch }) => {
  if (dispatch) {
    tr2.scrollIntoView();
  }
  return true;
};
var selectAll = () => ({ tr: tr2, dispatch }) => {
  if (dispatch) {
    const selection = new AllSelection(tr2.doc);
    tr2.setSelection(selection);
  }
  return true;
};
var selectNodeBackward2 = () => ({ state, dispatch }) => {
  return selectNodeBackward(state, dispatch);
};
var selectNodeForward2 = () => ({ state, dispatch }) => {
  return selectNodeForward(state, dispatch);
};
var selectParentNode2 = () => ({ state, dispatch }) => {
  return selectParentNode(state, dispatch);
};
var selectTextblockEnd2 = () => ({ state, dispatch }) => {
  return selectTextblockEnd(state, dispatch);
};
var selectTextblockStart2 = () => ({ state, dispatch }) => {
  return selectTextblockStart(state, dispatch);
};
function createDocument(content, schema, parseOptions = {}, options = {}) {
  return createNodeFromContent(content, schema, {
    slice: false,
    parseOptions,
    errorOnInvalidContent: options.errorOnInvalidContent
  });
}
var setContent = (content, emitUpdate = false, parseOptions = {}, options = {}) => ({ editor, tr: tr2, dispatch, commands: commands2 }) => {
  var _a, _b;
  const { doc } = tr2;
  if (parseOptions.preserveWhitespace !== "full") {
    const document2 = createDocument(content, editor.schema, parseOptions, {
      errorOnInvalidContent: (_a = options.errorOnInvalidContent) !== null && _a !== void 0 ? _a : editor.options.enableContentCheck
    });
    if (dispatch) {
      tr2.replaceWith(0, doc.content.size, document2).setMeta("preventUpdate", !emitUpdate);
    }
    return true;
  }
  if (dispatch) {
    tr2.setMeta("preventUpdate", !emitUpdate);
  }
  return commands2.insertContentAt({ from: 0, to: doc.content.size }, content, {
    parseOptions,
    errorOnInvalidContent: (_b = options.errorOnInvalidContent) !== null && _b !== void 0 ? _b : editor.options.enableContentCheck
  });
};
function getMarkAttributes(state, typeOrName) {
  const type = getMarkType(typeOrName, state.schema);
  const { from, to: to2, empty } = state.selection;
  const marks = [];
  if (empty) {
    if (state.storedMarks) {
      marks.push(...state.storedMarks);
    }
    marks.push(...state.selection.$head.marks());
  } else {
    state.doc.nodesBetween(from, to2, (node) => {
      marks.push(...node.marks);
    });
  }
  const mark = marks.find((markItem) => markItem.type.name === type.name);
  if (!mark) {
    return {};
  }
  return { ...mark.attrs };
}
function defaultBlockAt(match) {
  for (let i2 = 0; i2 < match.edgeCount; i2 += 1) {
    const { type } = match.edge(i2);
    if (type.isTextblock && !type.hasRequiredAttrs()) {
      return type;
    }
  }
  return null;
}
function findParentNodeClosestToPos($pos, predicate) {
  for (let i2 = $pos.depth; i2 > 0; i2 -= 1) {
    const node = $pos.node(i2);
    if (predicate(node)) {
      return {
        pos: i2 > 0 ? $pos.before(i2) : 0,
        start: $pos.start(i2),
        depth: i2,
        node
      };
    }
  }
}
function findParentNode(predicate) {
  return (selection) => findParentNodeClosestToPos(selection.$from, predicate);
}
function getText(node, options) {
  const range = {
    from: 0,
    to: node.content.size
  };
  return getTextBetween(node, range, options);
}
function getSplittedAttributes(extensionAttributes, typeName, attributes) {
  return Object.fromEntries(Object.entries(attributes).filter(([name]) => {
    const extensionAttribute = extensionAttributes.find((item) => {
      return item.type === typeName && item.name === name;
    });
    if (!extensionAttribute) {
      return false;
    }
    return extensionAttribute.attribute.keepOnSplit;
  }));
}
function isMarkActive(state, typeOrName, attributes = {}) {
  const { empty, ranges } = state.selection;
  const type = typeOrName ? getMarkType(typeOrName, state.schema) : null;
  if (empty) {
    return !!(state.storedMarks || state.selection.$from.marks()).filter((mark) => {
      if (!type) {
        return true;
      }
      return type.name === mark.type.name;
    }).find((mark) => objectIncludes(mark.attrs, attributes, { strict: false }));
  }
  let selectionRange = 0;
  const markRanges = [];
  ranges.forEach(({ $from, $to }) => {
    const from = $from.pos;
    const to2 = $to.pos;
    state.doc.nodesBetween(from, to2, (node, pos) => {
      if (!node.isText && !node.marks.length) {
        return;
      }
      const relativeFrom = Math.max(from, pos);
      const relativeTo = Math.min(to2, pos + node.nodeSize);
      const range2 = relativeTo - relativeFrom;
      selectionRange += range2;
      markRanges.push(...node.marks.map((mark) => ({
        mark,
        from: relativeFrom,
        to: relativeTo
      })));
    });
  });
  if (selectionRange === 0) {
    return false;
  }
  const matchedRange = markRanges.filter((markRange) => {
    if (!type) {
      return true;
    }
    return type.name === markRange.mark.type.name;
  }).filter((markRange) => objectIncludes(markRange.mark.attrs, attributes, { strict: false })).reduce((sum, markRange) => sum + markRange.to - markRange.from, 0);
  const excludedRange = markRanges.filter((markRange) => {
    if (!type) {
      return true;
    }
    return markRange.mark.type !== type && markRange.mark.type.excludes(type);
  }).reduce((sum, markRange) => sum + markRange.to - markRange.from, 0);
  const range = matchedRange > 0 ? matchedRange + excludedRange : matchedRange;
  return range >= selectionRange;
}
function isList(name, extensions) {
  const { nodeExtensions } = splitExtensions(extensions);
  const extension = nodeExtensions.find((item) => item.name === name);
  if (!extension) {
    return false;
  }
  const context = {
    name: extension.name,
    options: extension.options,
    storage: extension.storage
  };
  const group = callOrReturn(getExtensionField(extension, "group", context));
  if (typeof group !== "string") {
    return false;
  }
  return group.split(" ").includes("list");
}
function isNodeEmpty(node, { checkChildren = true, ignoreWhitespace = false } = {}) {
  var _a;
  if (ignoreWhitespace) {
    if (node.type.name === "hardBreak") {
      return true;
    }
    if (node.isText) {
      return /^\s*$/m.test((_a = node.text) !== null && _a !== void 0 ? _a : "");
    }
  }
  if (node.isText) {
    return !node.text;
  }
  if (node.isAtom || node.isLeaf) {
    return false;
  }
  if (node.content.childCount === 0) {
    return true;
  }
  if (checkChildren) {
    let isContentEmpty = true;
    node.content.forEach((childNode) => {
      if (isContentEmpty === false) {
        return;
      }
      if (!isNodeEmpty(childNode, { ignoreWhitespace, checkChildren })) {
        isContentEmpty = false;
      }
    });
    return isContentEmpty;
  }
  return false;
}
function isNodeSelection(value) {
  return value instanceof NodeSelection;
}
function posToDOMRect(view, from, to2) {
  const minPos = 0;
  const maxPos = view.state.doc.content.size;
  const resolvedFrom = minMax(from, minPos, maxPos);
  const resolvedEnd = minMax(to2, minPos, maxPos);
  const start = view.coordsAtPos(resolvedFrom);
  const end = view.coordsAtPos(resolvedEnd, -1);
  const top = Math.min(start.top, end.top);
  const bottom = Math.max(start.bottom, end.bottom);
  const left = Math.min(start.left, end.left);
  const right = Math.max(start.right, end.right);
  const width = right - left;
  const height = bottom - top;
  const x2 = left;
  const y2 = top;
  const data = {
    top,
    bottom,
    left,
    right,
    width,
    height,
    x: x2,
    y: y2
  };
  return {
    ...data,
    toJSON: () => data
  };
}
function canSetMark(state, tr2, newMarkType) {
  var _a;
  const { selection } = tr2;
  let cursor = null;
  if (isTextSelection(selection)) {
    cursor = selection.$cursor;
  }
  if (cursor) {
    const currentMarks = (_a = state.storedMarks) !== null && _a !== void 0 ? _a : cursor.marks();
    return !!newMarkType.isInSet(currentMarks) || !currentMarks.some((mark) => mark.type.excludes(newMarkType));
  }
  const { ranges } = selection;
  return ranges.some(({ $from, $to }) => {
    let someNodeSupportsMark = $from.depth === 0 ? state.doc.inlineContent && state.doc.type.allowsMarkType(newMarkType) : false;
    state.doc.nodesBetween($from.pos, $to.pos, (node, _pos, parent) => {
      if (someNodeSupportsMark) {
        return false;
      }
      if (node.isInline) {
        const parentAllowsMarkType = !parent || parent.type.allowsMarkType(newMarkType);
        const currentMarksAllowMarkType = !!newMarkType.isInSet(node.marks) || !node.marks.some((otherMark) => otherMark.type.excludes(newMarkType));
        someNodeSupportsMark = parentAllowsMarkType && currentMarksAllowMarkType;
      }
      return !someNodeSupportsMark;
    });
    return someNodeSupportsMark;
  });
}
var setMark = (typeOrName, attributes = {}) => ({ tr: tr2, state, dispatch }) => {
  const { selection } = tr2;
  const { empty, ranges } = selection;
  const type = getMarkType(typeOrName, state.schema);
  if (dispatch) {
    if (empty) {
      const oldAttributes = getMarkAttributes(state, type);
      tr2.addStoredMark(type.create({
        ...oldAttributes,
        ...attributes
      }));
    } else {
      ranges.forEach((range) => {
        const from = range.$from.pos;
        const to2 = range.$to.pos;
        state.doc.nodesBetween(from, to2, (node, pos) => {
          const trimmedFrom = Math.max(pos, from);
          const trimmedTo = Math.min(pos + node.nodeSize, to2);
          const someHasMark = node.marks.find((mark) => mark.type === type);
          if (someHasMark) {
            node.marks.forEach((mark) => {
              if (type === mark.type) {
                tr2.addMark(trimmedFrom, trimmedTo, type.create({
                  ...mark.attrs,
                  ...attributes
                }));
              }
            });
          } else {
            tr2.addMark(trimmedFrom, trimmedTo, type.create(attributes));
          }
        });
      });
    }
  }
  return canSetMark(state, tr2, type);
};
var setMeta = (key, value) => ({ tr: tr2 }) => {
  tr2.setMeta(key, value);
  return true;
};
var setNode = (typeOrName, attributes = {}) => ({ state, dispatch, chain }) => {
  const type = getNodeType(typeOrName, state.schema);
  let attributesToCopy;
  if (state.selection.$anchor.sameParent(state.selection.$head)) {
    attributesToCopy = state.selection.$anchor.parent.attrs;
  }
  if (!type.isTextblock) {
    console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.');
    return false;
  }
  return chain().command(({ commands: commands2 }) => {
    const canSetBlock = setBlockType(type, { ...attributesToCopy, ...attributes })(state);
    if (canSetBlock) {
      return true;
    }
    return commands2.clearNodes();
  }).command(({ state: updatedState }) => {
    return setBlockType(type, { ...attributesToCopy, ...attributes })(updatedState, dispatch);
  }).run();
};
var setNodeSelection = (position) => ({ tr: tr2, dispatch }) => {
  if (dispatch) {
    const { doc } = tr2;
    const from = minMax(position, 0, doc.content.size);
    const selection = NodeSelection.create(doc, from);
    tr2.setSelection(selection);
  }
  return true;
};
var setTextSelection = (position) => ({ tr: tr2, dispatch }) => {
  if (dispatch) {
    const { doc } = tr2;
    const { from, to: to2 } = typeof position === "number" ? { from: position, to: position } : position;
    const minPos = TextSelection.atStart(doc).from;
    const maxPos = TextSelection.atEnd(doc).to;
    const resolvedFrom = minMax(from, minPos, maxPos);
    const resolvedEnd = minMax(to2, minPos, maxPos);
    const selection = TextSelection.create(doc, resolvedFrom, resolvedEnd);
    tr2.setSelection(selection);
  }
  return true;
};
var sinkListItem2 = (typeOrName) => ({ state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  return sinkListItem(type)(state, dispatch);
};
function ensureMarks(state, splittableMarks) {
  const marks = state.storedMarks || state.selection.$to.parentOffset && state.selection.$from.marks();
  if (marks) {
    const filteredMarks = marks.filter((mark) => splittableMarks === null || splittableMarks === void 0 ? void 0 : splittableMarks.includes(mark.type.name));
    state.tr.ensureMarks(filteredMarks);
  }
}
var splitBlock = ({ keepMarks = true } = {}) => ({ tr: tr2, state, dispatch, editor }) => {
  const { selection, doc } = tr2;
  const { $from, $to } = selection;
  const extensionAttributes = editor.extensionManager.attributes;
  const newAttributes = getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs);
  if (selection instanceof NodeSelection && selection.node.isBlock) {
    if (!$from.parentOffset || !canSplit(doc, $from.pos)) {
      return false;
    }
    if (dispatch) {
      if (keepMarks) {
        ensureMarks(state, editor.extensionManager.splittableMarks);
      }
      tr2.split($from.pos).scrollIntoView();
    }
    return true;
  }
  if (!$from.parent.isBlock) {
    return false;
  }
  const atEnd = $to.parentOffset === $to.parent.content.size;
  const deflt = $from.depth === 0 ? void 0 : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)));
  let types = atEnd && deflt ? [
    {
      type: deflt,
      attrs: newAttributes
    }
  ] : void 0;
  let can = canSplit(tr2.doc, tr2.mapping.map($from.pos), 1, types);
  if (!types && !can && canSplit(tr2.doc, tr2.mapping.map($from.pos), 1, deflt ? [{ type: deflt }] : void 0)) {
    can = true;
    types = deflt ? [
      {
        type: deflt,
        attrs: newAttributes
      }
    ] : void 0;
  }
  if (dispatch) {
    if (can) {
      if (selection instanceof TextSelection) {
        tr2.deleteSelection();
      }
      tr2.split(tr2.mapping.map($from.pos), 1, types);
      if (deflt && !atEnd && !$from.parentOffset && $from.parent.type !== deflt) {
        const first2 = tr2.mapping.map($from.before());
        const $first = tr2.doc.resolve(first2);
        if ($from.node(-1).canReplaceWith($first.index(), $first.index() + 1, deflt)) {
          tr2.setNodeMarkup(tr2.mapping.map($from.before()), deflt);
        }
      }
    }
    if (keepMarks) {
      ensureMarks(state, editor.extensionManager.splittableMarks);
    }
    tr2.scrollIntoView();
  }
  return can;
};
var splitListItem = (typeOrName, overrideAttrs = {}) => ({ tr: tr2, state, dispatch, editor }) => {
  var _a;
  const type = getNodeType(typeOrName, state.schema);
  const { $from, $to } = state.selection;
  const node = state.selection.node;
  if (node && node.isBlock || $from.depth < 2 || !$from.sameParent($to)) {
    return false;
  }
  const grandParent = $from.node(-1);
  if (grandParent.type !== type) {
    return false;
  }
  const extensionAttributes = editor.extensionManager.attributes;
  if ($from.parent.content.size === 0 && $from.node(-1).childCount === $from.indexAfter(-1)) {
    if ($from.depth === 2 || $from.node(-3).type !== type || $from.index(-2) !== $from.node(-2).childCount - 1) {
      return false;
    }
    if (dispatch) {
      let wrap = Fragment.empty;
      const depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3;
      for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d -= 1) {
        wrap = Fragment.from($from.node(d).copy(wrap));
      }
      const depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1 : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3;
      const newNextTypeAttributes2 = {
        ...getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs),
        ...overrideAttrs
      };
      const nextType2 = ((_a = type.contentMatch.defaultType) === null || _a === void 0 ? void 0 : _a.createAndFill(newNextTypeAttributes2)) || void 0;
      wrap = wrap.append(Fragment.from(type.createAndFill(null, nextType2) || void 0));
      const start = $from.before($from.depth - (depthBefore - 1));
      tr2.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0));
      let sel = -1;
      tr2.doc.nodesBetween(start, tr2.doc.content.size, (n, pos) => {
        if (sel > -1) {
          return false;
        }
        if (n.isTextblock && n.content.size === 0) {
          sel = pos + 1;
        }
      });
      if (sel > -1) {
        tr2.setSelection(TextSelection.near(tr2.doc.resolve(sel)));
      }
      tr2.scrollIntoView();
    }
    return true;
  }
  const nextType = $to.pos === $from.end() ? grandParent.contentMatchAt(0).defaultType : null;
  const newTypeAttributes = {
    ...getSplittedAttributes(extensionAttributes, grandParent.type.name, grandParent.attrs),
    ...overrideAttrs
  };
  const newNextTypeAttributes = {
    ...getSplittedAttributes(extensionAttributes, $from.node().type.name, $from.node().attrs),
    ...overrideAttrs
  };
  tr2.delete($from.pos, $to.pos);
  const types = nextType ? [
    { type, attrs: newTypeAttributes },
    { type: nextType, attrs: newNextTypeAttributes }
  ] : [{ type, attrs: newTypeAttributes }];
  if (!canSplit(tr2.doc, $from.pos, 2)) {
    return false;
  }
  if (dispatch) {
    const { selection, storedMarks } = state;
    const { splittableMarks } = editor.extensionManager;
    const marks = storedMarks || selection.$to.parentOffset && selection.$from.marks();
    tr2.split($from.pos, 2, types).scrollIntoView();
    if (!marks || !dispatch) {
      return true;
    }
    const filteredMarks = marks.filter((mark) => splittableMarks.includes(mark.type.name));
    tr2.ensureMarks(filteredMarks);
  }
  return true;
};
var joinListBackwards = (tr2, listType) => {
  const list = findParentNode((node) => node.type === listType)(tr2.selection);
  if (!list) {
    return true;
  }
  const before = tr2.doc.resolve(Math.max(0, list.pos - 1)).before(list.depth);
  if (before === void 0) {
    return true;
  }
  const nodeBefore = tr2.doc.nodeAt(before);
  const canJoinBackwards = list.node.type === (nodeBefore === null || nodeBefore === void 0 ? void 0 : nodeBefore.type) && canJoin(tr2.doc, list.pos);
  if (!canJoinBackwards) {
    return true;
  }
  tr2.join(list.pos);
  return true;
};
var joinListForwards = (tr2, listType) => {
  const list = findParentNode((node) => node.type === listType)(tr2.selection);
  if (!list) {
    return true;
  }
  const after = tr2.doc.resolve(list.start).after(list.depth);
  if (after === void 0) {
    return true;
  }
  const nodeAfter = tr2.doc.nodeAt(after);
  const canJoinForwards = list.node.type === (nodeAfter === null || nodeAfter === void 0 ? void 0 : nodeAfter.type) && canJoin(tr2.doc, after);
  if (!canJoinForwards) {
    return true;
  }
  tr2.join(after);
  return true;
};
var toggleList = (listTypeOrName, itemTypeOrName, keepMarks, attributes = {}) => ({ editor, tr: tr2, state, dispatch, chain, commands: commands2, can }) => {
  const { extensions, splittableMarks } = editor.extensionManager;
  const listType = getNodeType(listTypeOrName, state.schema);
  const itemType = getNodeType(itemTypeOrName, state.schema);
  const { selection, storedMarks } = state;
  const { $from, $to } = selection;
  const range = $from.blockRange($to);
  const marks = storedMarks || selection.$to.parentOffset && selection.$from.marks();
  if (!range) {
    return false;
  }
  const parentList = findParentNode((node) => isList(node.type.name, extensions))(selection);
  if (range.depth >= 1 && parentList && range.depth - parentList.depth <= 1) {
    if (parentList.node.type === listType) {
      return commands2.liftListItem(itemType);
    }
    if (isList(parentList.node.type.name, extensions) && listType.validContent(parentList.node.content) && dispatch) {
      return chain().command(() => {
        tr2.setNodeMarkup(parentList.pos, listType);
        return true;
      }).command(() => joinListBackwards(tr2, listType)).command(() => joinListForwards(tr2, listType)).run();
    }
  }
  if (!keepMarks || !marks || !dispatch) {
    return chain().command(() => {
      const canWrapInList = can().wrapInList(listType, attributes);
      if (canWrapInList) {
        return true;
      }
      return commands2.clearNodes();
    }).wrapInList(listType, attributes).command(() => joinListBackwards(tr2, listType)).command(() => joinListForwards(tr2, listType)).run();
  }
  return chain().command(() => {
    const canWrapInList = can().wrapInList(listType, attributes);
    const filteredMarks = marks.filter((mark) => splittableMarks.includes(mark.type.name));
    tr2.ensureMarks(filteredMarks);
    if (canWrapInList) {
      return true;
    }
    return commands2.clearNodes();
  }).wrapInList(listType, attributes).command(() => joinListBackwards(tr2, listType)).command(() => joinListForwards(tr2, listType)).run();
};
var toggleMark = (typeOrName, attributes = {}, options = {}) => ({ state, commands: commands2 }) => {
  const { extendEmptyMarkRange = false } = options;
  const type = getMarkType(typeOrName, state.schema);
  const isActive = isMarkActive(state, type, attributes);
  if (isActive) {
    return commands2.unsetMark(type, { extendEmptyMarkRange });
  }
  return commands2.setMark(type, attributes);
};
var toggleNode = (typeOrName, toggleTypeOrName, attributes = {}) => ({ state, commands: commands2 }) => {
  const type = getNodeType(typeOrName, state.schema);
  const toggleType = getNodeType(toggleTypeOrName, state.schema);
  const isActive = isNodeActive(state, type, attributes);
  let attributesToCopy;
  if (state.selection.$anchor.sameParent(state.selection.$head)) {
    attributesToCopy = state.selection.$anchor.parent.attrs;
  }
  if (isActive) {
    return commands2.setNode(toggleType, attributesToCopy);
  }
  return commands2.setNode(type, { ...attributesToCopy, ...attributes });
};
var toggleWrap = (typeOrName, attributes = {}) => ({ state, commands: commands2 }) => {
  const type = getNodeType(typeOrName, state.schema);
  const isActive = isNodeActive(state, type, attributes);
  if (isActive) {
    return commands2.lift(type);
  }
  return commands2.wrapIn(type, attributes);
};
var undoInputRule = () => ({ state, dispatch }) => {
  const plugins = state.plugins;
  for (let i2 = 0; i2 < plugins.length; i2 += 1) {
    const plugin = plugins[i2];
    let undoable;
    if (plugin.spec.isInputRules && (undoable = plugin.getState(state))) {
      if (dispatch) {
        const tr2 = state.tr;
        const toUndo = undoable.transform;
        for (let j2 = toUndo.steps.length - 1; j2 >= 0; j2 -= 1) {
          tr2.step(toUndo.steps[j2].invert(toUndo.docs[j2]));
        }
        if (undoable.text) {
          const marks = tr2.doc.resolve(undoable.from).marks();
          tr2.replaceWith(undoable.from, undoable.to, state.schema.text(undoable.text, marks));
        } else {
          tr2.delete(undoable.from, undoable.to);
        }
      }
      return true;
    }
  }
  return false;
};
var unsetAllMarks = () => ({ tr: tr2, dispatch }) => {
  const { selection } = tr2;
  const { empty, ranges } = selection;
  if (empty) {
    return true;
  }
  if (dispatch) {
    ranges.forEach((range) => {
      tr2.removeMark(range.$from.pos, range.$to.pos);
    });
  }
  return true;
};
var unsetMark = (typeOrName, options = {}) => ({ tr: tr2, state, dispatch }) => {
  var _a;
  const { extendEmptyMarkRange = false } = options;
  const { selection } = tr2;
  const type = getMarkType(typeOrName, state.schema);
  const { $from, empty, ranges } = selection;
  if (!dispatch) {
    return true;
  }
  if (empty && extendEmptyMarkRange) {
    let { from, to: to2 } = selection;
    const attrs = (_a = $from.marks().find((mark) => mark.type === type)) === null || _a === void 0 ? void 0 : _a.attrs;
    const range = getMarkRange($from, type, attrs);
    if (range) {
      from = range.from;
      to2 = range.to;
    }
    tr2.removeMark(from, to2, type);
  } else {
    ranges.forEach((range) => {
      tr2.removeMark(range.$from.pos, range.$to.pos, type);
    });
  }
  tr2.removeStoredMark(type);
  return true;
};
var updateAttributes = (typeOrName, attributes = {}) => ({ tr: tr2, state, dispatch }) => {
  let nodeType = null;
  let markType = null;
  const schemaType = getSchemaTypeNameByName(typeof typeOrName === "string" ? typeOrName : typeOrName.name, state.schema);
  if (!schemaType) {
    return false;
  }
  if (schemaType === "node") {
    nodeType = getNodeType(typeOrName, state.schema);
  }
  if (schemaType === "mark") {
    markType = getMarkType(typeOrName, state.schema);
  }
  if (dispatch) {
    tr2.selection.ranges.forEach((range) => {
      const from = range.$from.pos;
      const to2 = range.$to.pos;
      let lastPos;
      let lastNode;
      let trimmedFrom;
      let trimmedTo;
      if (tr2.selection.empty) {
        state.doc.nodesBetween(from, to2, (node, pos) => {
          if (nodeType && nodeType === node.type) {
            trimmedFrom = Math.max(pos, from);
            trimmedTo = Math.min(pos + node.nodeSize, to2);
            lastPos = pos;
            lastNode = node;
          }
        });
      } else {
        state.doc.nodesBetween(from, to2, (node, pos) => {
          if (pos < from && nodeType && nodeType === node.type) {
            trimmedFrom = Math.max(pos, from);
            trimmedTo = Math.min(pos + node.nodeSize, to2);
            lastPos = pos;
            lastNode = node;
          }
          if (pos >= from && pos <= to2) {
            if (nodeType && nodeType === node.type) {
              tr2.setNodeMarkup(pos, void 0, {
                ...node.attrs,
                ...attributes
              });
            }
            if (markType && node.marks.length) {
              node.marks.forEach((mark) => {
                if (markType === mark.type) {
                  const trimmedFrom2 = Math.max(pos, from);
                  const trimmedTo2 = Math.min(pos + node.nodeSize, to2);
                  tr2.addMark(trimmedFrom2, trimmedTo2, markType.create({
                    ...mark.attrs,
                    ...attributes
                  }));
                }
              });
            }
          }
        });
      }
      if (lastNode) {
        if (lastPos !== void 0) {
          tr2.setNodeMarkup(lastPos, void 0, {
            ...lastNode.attrs,
            ...attributes
          });
        }
        if (markType && lastNode.marks.length) {
          lastNode.marks.forEach((mark) => {
            if (markType === mark.type) {
              tr2.addMark(trimmedFrom, trimmedTo, markType.create({
                ...mark.attrs,
                ...attributes
              }));
            }
          });
        }
      }
    });
  }
  return true;
};
var wrapIn2 = (typeOrName, attributes = {}) => ({ state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  return wrapIn(type, attributes)(state, dispatch);
};
var wrapInList2 = (typeOrName, attributes = {}) => ({ state, dispatch }) => {
  const type = getNodeType(typeOrName, state.schema);
  return wrapInList(type, attributes)(state, dispatch);
};
var commands = Object.freeze({
  __proto__: null,
  blur,
  clearContent,
  clearNodes,
  command,
  createParagraphNear: createParagraphNear2,
  cut,
  deleteCurrentNode,
  deleteNode,
  deleteRange,
  deleteSelection: deleteSelection2,
  enter,
  exitCode: exitCode2,
  extendMarkRange,
  first,
  focus,
  forEach,
  insertContent,
  insertContentAt,
  joinBackward: joinBackward2,
  joinDown: joinDown2,
  joinForward: joinForward2,
  joinItemBackward,
  joinItemForward,
  joinTextblockBackward: joinTextblockBackward2,
  joinTextblockForward: joinTextblockForward2,
  joinUp: joinUp2,
  keyboardShortcut,
  lift: lift2,
  liftEmptyBlock: liftEmptyBlock2,
  liftListItem: liftListItem2,
  newlineInCode: newlineInCode2,
  resetAttributes,
  scrollIntoView,
  selectAll,
  selectNodeBackward: selectNodeBackward2,
  selectNodeForward: selectNodeForward2,
  selectParentNode: selectParentNode2,
  selectTextblockEnd: selectTextblockEnd2,
  selectTextblockStart: selectTextblockStart2,
  setContent,
  setMark,
  setMeta,
  setNode,
  setNodeSelection,
  setTextSelection,
  sinkListItem: sinkListItem2,
  splitBlock,
  splitListItem,
  toggleList,
  toggleMark,
  toggleNode,
  toggleWrap,
  undoInputRule,
  unsetAllMarks,
  unsetMark,
  updateAttributes,
  wrapIn: wrapIn2,
  wrapInList: wrapInList2
});
var Commands = Extension.create({
  name: "commands",
  addCommands() {
    return {
      ...commands
    };
  }
});
var Drop = Extension.create({
  name: "drop",
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("tiptapDrop"),
        props: {
          handleDrop: (_2, e, slice, moved) => {
            this.editor.emit("drop", {
              editor: this.editor,
              event: e,
              slice,
              moved
            });
          }
        }
      })
    ];
  }
});
var Editable = Extension.create({
  name: "editable",
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("editable"),
        props: {
          editable: () => this.editor.options.editable
        }
      })
    ];
  }
});
var focusEventsPluginKey = new PluginKey("focusEvents");
var FocusEvents = Extension.create({
  name: "focusEvents",
  addProseMirrorPlugins() {
    const { editor } = this;
    return [
      new Plugin({
        key: focusEventsPluginKey,
        props: {
          handleDOMEvents: {
            focus: (view, event) => {
              editor.isFocused = true;
              const transaction = editor.state.tr.setMeta("focus", { event }).setMeta("addToHistory", false);
              view.dispatch(transaction);
              return false;
            },
            blur: (view, event) => {
              editor.isFocused = false;
              const transaction = editor.state.tr.setMeta("blur", { event }).setMeta("addToHistory", false);
              view.dispatch(transaction);
              return false;
            }
          }
        }
      })
    ];
  }
});
var Keymap = Extension.create({
  name: "keymap",
  addKeyboardShortcuts() {
    const handleBackspace = () => this.editor.commands.first(({ commands: commands2 }) => [
      () => commands2.undoInputRule(),
      // maybe convert first text block node to default node
      () => commands2.command(({ tr: tr2 }) => {
        const { selection, doc } = tr2;
        const { empty, $anchor } = selection;
        const { pos, parent } = $anchor;
        const $parentPos = $anchor.parent.isTextblock && pos > 0 ? tr2.doc.resolve(pos - 1) : $anchor;
        const parentIsIsolating = $parentPos.parent.type.spec.isolating;
        const parentPos = $anchor.pos - $anchor.parentOffset;
        const isAtStart = parentIsIsolating && $parentPos.parent.childCount === 1 ? parentPos === $anchor.pos : Selection.atStart(doc).from === pos;
        if (!empty || !parent.type.isTextblock || parent.textContent.length || !isAtStart || isAtStart && $anchor.parent.type.name === "paragraph") {
          return false;
        }
        return commands2.clearNodes();
      }),
      () => commands2.deleteSelection(),
      () => commands2.joinBackward(),
      () => commands2.selectNodeBackward()
    ]);
    const handleDelete = () => this.editor.commands.first(({ commands: commands2 }) => [
      () => commands2.deleteSelection(),
      () => commands2.deleteCurrentNode(),
      () => commands2.joinForward(),
      () => commands2.selectNodeForward()
    ]);
    const handleEnter = () => this.editor.commands.first(({ commands: commands2 }) => [
      () => commands2.newlineInCode(),
      () => commands2.createParagraphNear(),
      () => commands2.liftEmptyBlock(),
      () => commands2.splitBlock()
    ]);
    const baseKeymap = {
      Enter: handleEnter,
      "Mod-Enter": () => this.editor.commands.exitCode(),
      Backspace: handleBackspace,
      "Mod-Backspace": handleBackspace,
      "Shift-Backspace": handleBackspace,
      Delete: handleDelete,
      "Mod-Delete": handleDelete,
      "Mod-a": () => this.editor.commands.selectAll()
    };
    const pcKeymap = {
      ...baseKeymap
    };
    const macKeymap = {
      ...baseKeymap,
      "Ctrl-h": handleBackspace,
      "Alt-Backspace": handleBackspace,
      "Ctrl-d": handleDelete,
      "Ctrl-Alt-Backspace": handleDelete,
      "Alt-Delete": handleDelete,
      "Alt-d": handleDelete,
      "Ctrl-a": () => this.editor.commands.selectTextblockStart(),
      "Ctrl-e": () => this.editor.commands.selectTextblockEnd()
    };
    if (isiOS() || isMacOS()) {
      return macKeymap;
    }
    return pcKeymap;
  },
  addProseMirrorPlugins() {
    return [
      // With this plugin we check if the whole document was selected and deleted.
      // In this case we will additionally call `clearNodes()` to convert e.g. a heading
      // to a paragraph if necessary.
      // This is an alternative to ProseMirror's `AllSelection`, which doesn’t work well
      // with many other commands.
      new Plugin({
        key: new PluginKey("clearDocument"),
        appendTransaction: (transactions, oldState, newState) => {
          if (transactions.some((tr3) => tr3.getMeta("composition"))) {
            return;
          }
          const docChanges = transactions.some((transaction) => transaction.docChanged) && !oldState.doc.eq(newState.doc);
          const ignoreTr = transactions.some((transaction) => transaction.getMeta("preventClearDocument"));
          if (!docChanges || ignoreTr) {
            return;
          }
          const { empty, from, to: to2 } = oldState.selection;
          const allFrom = Selection.atStart(oldState.doc).from;
          const allEnd = Selection.atEnd(oldState.doc).to;
          const allWasSelected = from === allFrom && to2 === allEnd;
          if (empty || !allWasSelected) {
            return;
          }
          const isEmpty = isNodeEmpty(newState.doc);
          if (!isEmpty) {
            return;
          }
          const tr2 = newState.tr;
          const state = createChainableState({
            state: newState,
            transaction: tr2
          });
          const { commands: commands2 } = new CommandManager({
            editor: this.editor,
            state
          });
          commands2.clearNodes();
          if (!tr2.steps.length) {
            return;
          }
          return tr2;
        }
      })
    ];
  }
});
var Paste = Extension.create({
  name: "paste",
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("tiptapPaste"),
        props: {
          handlePaste: (_view, e, slice) => {
            this.editor.emit("paste", {
              editor: this.editor,
              event: e,
              slice
            });
          }
        }
      })
    ];
  }
});
var Tabindex = Extension.create({
  name: "tabindex",
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("tabindex"),
        props: {
          attributes: () => this.editor.isEditable ? { tabindex: "0" } : {}
        }
      })
    ];
  }
});
var index2 = Object.freeze({
  __proto__: null,
  ClipboardTextSerializer,
  Commands,
  Drop,
  Editable,
  FocusEvents,
  Keymap,
  Paste,
  Tabindex,
  focusEventsPluginKey
});
var NodeView = class {
  constructor(component, props, options) {
    this.isDragging = false;
    this.component = component;
    this.editor = props.editor;
    this.options = {
      stopEvent: null,
      ignoreMutation: null,
      ...options
    };
    this.extension = props.extension;
    this.node = props.node;
    this.decorations = props.decorations;
    this.innerDecorations = props.innerDecorations;
    this.view = props.view;
    this.HTMLAttributes = props.HTMLAttributes;
    this.getPos = props.getPos;
    this.mount();
  }
  mount() {
    return;
  }
  get dom() {
    return this.editor.view.dom;
  }
  get contentDOM() {
    return null;
  }
  onDragStart(event) {
    var _a, _b, _c2, _d, _e2, _f, _g;
    const { view } = this.editor;
    const target = event.target;
    const dragHandle = target.nodeType === 3 ? (_a = target.parentElement) === null || _a === void 0 ? void 0 : _a.closest("[data-drag-handle]") : target.closest("[data-drag-handle]");
    if (!this.dom || ((_b = this.contentDOM) === null || _b === void 0 ? void 0 : _b.contains(target)) || !dragHandle) {
      return;
    }
    let x2 = 0;
    let y2 = 0;
    if (this.dom !== dragHandle) {
      const domBox = this.dom.getBoundingClientRect();
      const handleBox = dragHandle.getBoundingClientRect();
      const offsetX = (_c2 = event.offsetX) !== null && _c2 !== void 0 ? _c2 : (_d = event.nativeEvent) === null || _d === void 0 ? void 0 : _d.offsetX;
      const offsetY = (_e2 = event.offsetY) !== null && _e2 !== void 0 ? _e2 : (_f = event.nativeEvent) === null || _f === void 0 ? void 0 : _f.offsetY;
      x2 = handleBox.x - domBox.x + offsetX;
      y2 = handleBox.y - domBox.y + offsetY;
    }
    const clonedNode = this.dom.cloneNode(true);
    (_g = event.dataTransfer) === null || _g === void 0 ? void 0 : _g.setDragImage(clonedNode, x2, y2);
    const pos = this.getPos();
    if (typeof pos !== "number") {
      return;
    }
    const selection = NodeSelection.create(view.state.doc, pos);
    const transaction = view.state.tr.setSelection(selection);
    view.dispatch(transaction);
  }
  stopEvent(event) {
    var _a;
    if (!this.dom) {
      return false;
    }
    if (typeof this.options.stopEvent === "function") {
      return this.options.stopEvent({ event });
    }
    const target = event.target;
    const isInElement = this.dom.contains(target) && !((_a = this.contentDOM) === null || _a === void 0 ? void 0 : _a.contains(target));
    if (!isInElement) {
      return false;
    }
    const isDragEvent = event.type.startsWith("drag");
    const isDropEvent = event.type === "drop";
    const isInput = ["INPUT", "BUTTON", "SELECT", "TEXTAREA"].includes(target.tagName) || target.isContentEditable;
    if (isInput && !isDropEvent && !isDragEvent) {
      return true;
    }
    const { isEditable } = this.editor;
    const { isDragging } = this;
    const isDraggable = !!this.node.type.spec.draggable;
    const isSelectable = NodeSelection.isSelectable(this.node);
    const isCopyEvent = event.type === "copy";
    const isPasteEvent = event.type === "paste";
    const isCutEvent = event.type === "cut";
    const isClickEvent = event.type === "mousedown";
    if (!isDraggable && isSelectable && isDragEvent && event.target === this.dom) {
      event.preventDefault();
    }
    if (isDraggable && isDragEvent && !isDragging && event.target === this.dom) {
      event.preventDefault();
      return false;
    }
    if (isDraggable && isEditable && !isDragging && isClickEvent) {
      const dragHandle = target.closest("[data-drag-handle]");
      const isValidDragHandle = dragHandle && (this.dom === dragHandle || this.dom.contains(dragHandle));
      if (isValidDragHandle) {
        this.isDragging = true;
        document.addEventListener("dragend", () => {
          this.isDragging = false;
        }, { once: true });
        document.addEventListener("drop", () => {
          this.isDragging = false;
        }, { once: true });
        document.addEventListener("mouseup", () => {
          this.isDragging = false;
        }, { once: true });
      }
    }
    if (isDragging || isDropEvent || isCopyEvent || isPasteEvent || isCutEvent || isClickEvent && isSelectable) {
      return false;
    }
    return true;
  }
  /**
   * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.
   * @return `false` if the editor should re-read the selection or re-parse the range around the mutation
   * @return `true` if it can safely be ignored.
   */
  ignoreMutation(mutation) {
    if (!this.dom || !this.contentDOM) {
      return true;
    }
    if (typeof this.options.ignoreMutation === "function") {
      return this.options.ignoreMutation({ mutation });
    }
    if (this.node.isLeaf || this.node.isAtom) {
      return true;
    }
    if (mutation.type === "selection") {
      return false;
    }
    if (this.dom.contains(mutation.target) && mutation.type === "childList" && (isiOS() || isAndroid()) && this.editor.isFocused) {
      const changedNodes = [
        ...Array.from(mutation.addedNodes),
        ...Array.from(mutation.removedNodes)
      ];
      if (changedNodes.every((node) => node.isContentEditable)) {
        return false;
      }
    }
    if (this.contentDOM === mutation.target && mutation.type === "attributes") {
      return true;
    }
    if (this.contentDOM.contains(mutation.target)) {
      return false;
    }
    return true;
  }
  /**
   * Update the attributes of the prosemirror node.
   */
  updateAttributes(attributes) {
    this.editor.commands.command(({ tr: tr2 }) => {
      const pos = this.getPos();
      if (typeof pos !== "number") {
        return false;
      }
      tr2.setNodeMarkup(pos, void 0, {
        ...this.node.attrs,
        ...attributes
      });
      return true;
    });
  }
  /**
   * Delete the node.
   */
  deleteNode() {
    const from = this.getPos();
    if (typeof from !== "number") {
      return;
    }
    const to2 = from + this.node.nodeSize;
    this.editor.commands.deleteRange({ from, to: to2 });
  }
};

// node_modules/@blocknote/react/node_modules/@tiptap/extension-bubble-menu/dist/index.js
var BubbleMenuView = class {
  constructor({ editor, element, view, tippyOptions = {}, updateDelay = 250, shouldShow }) {
    this.preventHide = false;
    this.shouldShow = ({ view: view2, state, from, to: to2 }) => {
      const { doc, selection } = state;
      const { empty } = selection;
      const isEmptyTextBlock = !doc.textBetween(from, to2).length && isTextSelection(state.selection);
      const isChildOfMenu = this.element.contains(document.activeElement);
      const hasEditorFocus = view2.hasFocus() || isChildOfMenu;
      if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {
        return false;
      }
      return true;
    };
    this.mousedownHandler = () => {
      this.preventHide = true;
    };
    this.dragstartHandler = () => {
      this.hide();
    };
    this.focusHandler = () => {
      setTimeout(() => this.update(this.editor.view));
    };
    this.blurHandler = ({ event }) => {
      var _a;
      if (this.preventHide) {
        this.preventHide = false;
        return;
      }
      if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {
        return;
      }
      if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {
        return;
      }
      this.hide();
    };
    this.tippyBlurHandler = (event) => {
      this.blurHandler({ event });
    };
    this.handleDebouncedUpdate = (view2, oldState) => {
      const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view2.state.selection));
      const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view2.state.doc));
      if (!selectionChanged && !docChanged) {
        return;
      }
      if (this.updateDebounceTimer) {
        clearTimeout(this.updateDebounceTimer);
      }
      this.updateDebounceTimer = window.setTimeout(() => {
        this.updateHandler(view2, selectionChanged, docChanged, oldState);
      }, this.updateDelay);
    };
    this.updateHandler = (view2, selectionChanged, docChanged, oldState) => {
      var _a, _b, _c2;
      const { state, composing } = view2;
      const { selection } = state;
      const isSame = !selectionChanged && !docChanged;
      if (composing || isSame) {
        return;
      }
      this.createTooltip();
      const { ranges } = selection;
      const from = Math.min(...ranges.map((range) => range.$from.pos));
      const to2 = Math.max(...ranges.map((range) => range.$to.pos));
      const shouldShow2 = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {
        editor: this.editor,
        element: this.element,
        view: view2,
        state,
        oldState,
        from,
        to: to2
      });
      if (!shouldShow2) {
        this.hide();
        return;
      }
      (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({
        getReferenceClientRect: ((_c2 = this.tippyOptions) === null || _c2 === void 0 ? void 0 : _c2.getReferenceClientRect) || (() => {
          if (isNodeSelection(state.selection)) {
            let node = view2.nodeDOM(from);
            if (node) {
              const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector("[data-node-view-wrapper]");
              if (nodeViewWrapper) {
                node = nodeViewWrapper.firstChild;
              }
              if (node) {
                return node.getBoundingClientRect();
              }
            }
          }
          return posToDOMRect(view2, from, to2);
        })
      });
      this.show();
    };
    this.editor = editor;
    this.element = element;
    this.view = view;
    this.updateDelay = updateDelay;
    if (shouldShow) {
      this.shouldShow = shouldShow;
    }
    this.element.addEventListener("mousedown", this.mousedownHandler, { capture: true });
    this.view.dom.addEventListener("dragstart", this.dragstartHandler);
    this.editor.on("focus", this.focusHandler);
    this.editor.on("blur", this.blurHandler);
    this.tippyOptions = tippyOptions;
    this.element.remove();
    this.element.style.visibility = "visible";
  }
  createTooltip() {
    const { element: editorElement } = this.editor.options;
    const editorIsAttached = !!editorElement.parentElement;
    if (this.tippy || !editorIsAttached) {
      return;
    }
    this.tippy = tippy_esm_default(editorElement, {
      duration: 0,
      getReferenceClientRect: null,
      content: this.element,
      interactive: true,
      trigger: "manual",
      placement: "top",
      hideOnClick: "toggle",
      ...this.tippyOptions
    });
    if (this.tippy.popper.firstChild) {
      this.tippy.popper.firstChild.addEventListener("blur", this.tippyBlurHandler);
    }
  }
  update(view, oldState) {
    const { state } = view;
    const hasValidSelection = state.selection.from !== state.selection.to;
    if (this.updateDelay > 0 && hasValidSelection) {
      this.handleDebouncedUpdate(view, oldState);
      return;
    }
    const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));
    const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));
    this.updateHandler(view, selectionChanged, docChanged, oldState);
  }
  show() {
    var _a;
    (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();
  }
  hide() {
    var _a;
    (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();
  }
  destroy() {
    var _a, _b;
    if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {
      this.tippy.popper.firstChild.removeEventListener("blur", this.tippyBlurHandler);
    }
    (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();
    this.element.removeEventListener("mousedown", this.mousedownHandler, { capture: true });
    this.view.dom.removeEventListener("dragstart", this.dragstartHandler);
    this.editor.off("focus", this.focusHandler);
    this.editor.off("blur", this.blurHandler);
  }
};
var BubbleMenuPlugin = (options) => {
  return new Plugin({
    key: typeof options.pluginKey === "string" ? new PluginKey(options.pluginKey) : options.pluginKey,
    view: (view) => new BubbleMenuView({ view, ...options })
  });
};
var BubbleMenu = Extension.create({
  name: "bubbleMenu",
  addOptions() {
    return {
      element: null,
      tippyOptions: {},
      pluginKey: "bubbleMenu",
      updateDelay: void 0,
      shouldShow: null
    };
  },
  addProseMirrorPlugins() {
    if (!this.options.element) {
      return [];
    }
    return [
      BubbleMenuPlugin({
        pluginKey: this.options.pluginKey,
        editor: this.editor,
        element: this.options.element,
        tippyOptions: this.options.tippyOptions,
        updateDelay: this.options.updateDelay,
        shouldShow: this.options.shouldShow
      })
    ];
  }
});

// node_modules/@blocknote/react/node_modules/@tiptap/react/dist/index.js
var import_react2 = __toESM(require_react());
var import_react_dom3 = __toESM(require_react_dom());

// node_modules/@blocknote/react/node_modules/@tiptap/extension-floating-menu/dist/index.js
var FloatingMenuView = class {
  getTextContent(node) {
    return getText(node, { textSerializers: getTextSerializersFromSchema(this.editor.schema) });
  }
  constructor({ editor, element, view, tippyOptions = {}, shouldShow }) {
    this.preventHide = false;
    this.shouldShow = ({ view: view2, state }) => {
      const { selection } = state;
      const { $anchor, empty } = selection;
      const isRootDepth = $anchor.depth === 1;
      const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent);
      if (!view2.hasFocus() || !empty || !isRootDepth || !isEmptyTextBlock || !this.editor.isEditable) {
        return false;
      }
      return true;
    };
    this.mousedownHandler = () => {
      this.preventHide = true;
    };
    this.focusHandler = () => {
      setTimeout(() => this.update(this.editor.view));
    };
    this.blurHandler = ({ event }) => {
      var _a;
      if (this.preventHide) {
        this.preventHide = false;
        return;
      }
      if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {
        return;
      }
      if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {
        return;
      }
      this.hide();
    };
    this.tippyBlurHandler = (event) => {
      this.blurHandler({ event });
    };
    this.editor = editor;
    this.element = element;
    this.view = view;
    if (shouldShow) {
      this.shouldShow = shouldShow;
    }
    this.element.addEventListener("mousedown", this.mousedownHandler, { capture: true });
    this.editor.on("focus", this.focusHandler);
    this.editor.on("blur", this.blurHandler);
    this.tippyOptions = tippyOptions;
    this.element.remove();
    this.element.style.visibility = "visible";
  }
  createTooltip() {
    const { element: editorElement } = this.editor.options;
    const editorIsAttached = !!editorElement.parentElement;
    if (this.tippy || !editorIsAttached) {
      return;
    }
    this.tippy = tippy_esm_default(editorElement, {
      duration: 0,
      getReferenceClientRect: null,
      content: this.element,
      interactive: true,
      trigger: "manual",
      placement: "right",
      hideOnClick: "toggle",
      ...this.tippyOptions
    });
    if (this.tippy.popper.firstChild) {
      this.tippy.popper.firstChild.addEventListener("blur", this.tippyBlurHandler);
    }
  }
  update(view, oldState) {
    var _a, _b, _c2;
    const { state } = view;
    const { doc, selection } = state;
    const { from, to: to2 } = selection;
    const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection);
    if (isSame) {
      return;
    }
    this.createTooltip();
    const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {
      editor: this.editor,
      view,
      state,
      oldState
    });
    if (!shouldShow) {
      this.hide();
      return;
    }
    (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({
      getReferenceClientRect: ((_c2 = this.tippyOptions) === null || _c2 === void 0 ? void 0 : _c2.getReferenceClientRect) || (() => posToDOMRect(view, from, to2))
    });
    this.show();
  }
  show() {
    var _a;
    (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();
  }
  hide() {
    var _a;
    (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();
  }
  destroy() {
    var _a, _b;
    if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {
      this.tippy.popper.firstChild.removeEventListener("blur", this.tippyBlurHandler);
    }
    (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();
    this.element.removeEventListener("mousedown", this.mousedownHandler, { capture: true });
    this.editor.off("focus", this.focusHandler);
    this.editor.off("blur", this.blurHandler);
  }
};
var FloatingMenuPlugin = (options) => {
  return new Plugin({
    key: typeof options.pluginKey === "string" ? new PluginKey(options.pluginKey) : options.pluginKey,
    view: (view) => new FloatingMenuView({ view, ...options })
  });
};
var FloatingMenu = Extension.create({
  name: "floatingMenu",
  addOptions() {
    return {
      element: null,
      tippyOptions: {},
      pluginKey: "floatingMenu",
      shouldShow: null
    };
  },
  addProseMirrorPlugins() {
    if (!this.options.element) {
      return [];
    }
    return [
      FloatingMenuPlugin({
        pluginKey: this.options.pluginKey,
        editor: this.editor,
        element: this.options.element,
        tippyOptions: this.options.tippyOptions,
        shouldShow: this.options.shouldShow
      })
    ];
  }
});

// node_modules/@blocknote/react/node_modules/@tiptap/react/dist/index.js
function getDefaultExportFromCjs(x2) {
  return x2 && x2.__esModule && Object.prototype.hasOwnProperty.call(x2, "default") ? x2["default"] : x2;
}
var shim = { exports: {} };
var useSyncExternalStoreShim_development = {};
var hasRequiredUseSyncExternalStoreShim_development;
function requireUseSyncExternalStoreShim_development() {
  if (hasRequiredUseSyncExternalStoreShim_development) return useSyncExternalStoreShim_development;
  hasRequiredUseSyncExternalStoreShim_development = 1;
  if (true) {
    (function() {
      if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== "undefined" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === "function") {
        __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());
      }
      var React$1 = import_react2.default;
      var ReactSharedInternals = React$1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
      function error2(format) {
        {
          {
            for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
              args[_key2 - 1] = arguments[_key2];
            }
            printWarning("error", format, args);
          }
        }
      }
      function printWarning(level, format, args) {
        {
          var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;
          var stack = ReactDebugCurrentFrame.getStackAddendum();
          if (stack !== "") {
            format += "%s";
            args = args.concat([stack]);
          }
          var argsWithFormat = args.map(function(item) {
            return String(item);
          });
          argsWithFormat.unshift("Warning: " + format);
          Function.prototype.apply.call(console[level], console, argsWithFormat);
        }
      }
      function is(x2, y2) {
        return x2 === y2 && (x2 !== 0 || 1 / x2 === 1 / y2) || x2 !== x2 && y2 !== y2;
      }
      var objectIs = typeof Object.is === "function" ? Object.is : is;
      var useState3 = React$1.useState, useEffect4 = React$1.useEffect, useLayoutEffect3 = React$1.useLayoutEffect, useDebugValue2 = React$1.useDebugValue;
      var didWarnOld18Alpha = false;
      var didWarnUncachedGetSnapshot = false;
      function useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {
        {
          if (!didWarnOld18Alpha) {
            if (React$1.startTransition !== void 0) {
              didWarnOld18Alpha = true;
              error2("You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.");
            }
          }
        }
        var value = getSnapshot();
        {
          if (!didWarnUncachedGetSnapshot) {
            var cachedValue = getSnapshot();
            if (!objectIs(value, cachedValue)) {
              error2("The result of getSnapshot should be cached to avoid an infinite loop");
              didWarnUncachedGetSnapshot = true;
            }
          }
        }
        var _useState = useState3({
          inst: {
            value,
            getSnapshot
          }
        }), inst = _useState[0].inst, forceUpdate = _useState[1];
        useLayoutEffect3(function() {
          inst.value = value;
          inst.getSnapshot = getSnapshot;
          if (checkIfSnapshotChanged(inst)) {
            forceUpdate({
              inst
            });
          }
        }, [subscribe, value, getSnapshot]);
        useEffect4(function() {
          if (checkIfSnapshotChanged(inst)) {
            forceUpdate({
              inst
            });
          }
          var handleStoreChange = function() {
            if (checkIfSnapshotChanged(inst)) {
              forceUpdate({
                inst
              });
            }
          };
          return subscribe(handleStoreChange);
        }, [subscribe]);
        useDebugValue2(value);
        return value;
      }
      function checkIfSnapshotChanged(inst) {
        var latestGetSnapshot = inst.getSnapshot;
        var prevValue = inst.value;
        try {
          var nextValue = latestGetSnapshot();
          return !objectIs(prevValue, nextValue);
        } catch (error3) {
          return true;
        }
      }
      function useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {
        return getSnapshot();
      }
      var canUseDOM = !!(typeof window !== "undefined" && typeof window.document !== "undefined" && typeof window.document.createElement !== "undefined");
      var isServerEnvironment = !canUseDOM;
      var shim2 = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;
      var useSyncExternalStore$2 = React$1.useSyncExternalStore !== void 0 ? React$1.useSyncExternalStore : shim2;
      useSyncExternalStoreShim_development.useSyncExternalStore = useSyncExternalStore$2;
      if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== "undefined" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === "function") {
        __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());
      }
    })();
  }
  return useSyncExternalStoreShim_development;
}
if (false) {
  shim.exports = requireUseSyncExternalStoreShim_production_min();
} else {
  shim.exports = requireUseSyncExternalStoreShim_development();
}
var shimExports = shim.exports;
var mergeRefs = (...refs) => {
  return (node) => {
    refs.forEach((ref) => {
      if (typeof ref === "function") {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    });
  };
};
var Portals = ({ contentComponent }) => {
  const renderers = shimExports.useSyncExternalStore(contentComponent.subscribe, contentComponent.getSnapshot, contentComponent.getServerSnapshot);
  return import_react2.default.createElement(import_react2.default.Fragment, null, Object.values(renderers));
};
function getInstance() {
  const subscribers = /* @__PURE__ */ new Set();
  let renderers = {};
  return {
    /**
     * Subscribe to the editor instance's changes.
     */
    subscribe(callback) {
      subscribers.add(callback);
      return () => {
        subscribers.delete(callback);
      };
    },
    getSnapshot() {
      return renderers;
    },
    getServerSnapshot() {
      return renderers;
    },
    /**
     * Adds a new NodeView Renderer to the editor.
     */
    setRenderer(id, renderer) {
      renderers = {
        ...renderers,
        [id]: import_react_dom3.default.createPortal(renderer.reactElement, renderer.element, id)
      };
      subscribers.forEach((subscriber) => subscriber());
    },
    /**
     * Removes a NodeView Renderer from the editor.
     */
    removeRenderer(id) {
      const nextRenderers = { ...renderers };
      delete nextRenderers[id];
      renderers = nextRenderers;
      subscribers.forEach((subscriber) => subscriber());
    }
  };
}
var PureEditorContent = class extends import_react2.default.Component {
  constructor(props) {
    var _a;
    super(props);
    this.editorContentRef = import_react2.default.createRef();
    this.initialized = false;
    this.state = {
      hasContentComponentInitialized: Boolean((_a = props.editor) === null || _a === void 0 ? void 0 : _a.contentComponent)
    };
  }
  componentDidMount() {
    this.init();
  }
  componentDidUpdate() {
    this.init();
  }
  init() {
    const editor = this.props.editor;
    if (editor && !editor.isDestroyed && editor.options.element) {
      if (editor.contentComponent) {
        return;
      }
      const element = this.editorContentRef.current;
      element.append(...editor.options.element.childNodes);
      editor.setOptions({
        element
      });
      editor.contentComponent = getInstance();
      if (!this.state.hasContentComponentInitialized) {
        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {
          this.setState((prevState) => {
            if (!prevState.hasContentComponentInitialized) {
              return {
                hasContentComponentInitialized: true
              };
            }
            return prevState;
          });
          if (this.unsubscribeToContentComponent) {
            this.unsubscribeToContentComponent();
          }
        });
      }
      editor.createNodeViews();
      this.initialized = true;
    }
  }
  componentWillUnmount() {
    const editor = this.props.editor;
    if (!editor) {
      return;
    }
    this.initialized = false;
    if (!editor.isDestroyed) {
      editor.view.setProps({
        nodeViews: {}
      });
    }
    if (this.unsubscribeToContentComponent) {
      this.unsubscribeToContentComponent();
    }
    editor.contentComponent = null;
    if (!editor.options.element.firstChild) {
      return;
    }
    const newElement = document.createElement("div");
    newElement.append(...editor.options.element.childNodes);
    editor.setOptions({
      element: newElement
    });
  }
  render() {
    const { editor, innerRef, ...rest } = this.props;
    return import_react2.default.createElement(
      import_react2.default.Fragment,
      null,
      import_react2.default.createElement("div", { ref: mergeRefs(innerRef, this.editorContentRef), ...rest }),
      (editor === null || editor === void 0 ? void 0 : editor.contentComponent) && import_react2.default.createElement(Portals, { contentComponent: editor.contentComponent })
    );
  }
};
var EditorContentWithKey = (0, import_react2.forwardRef)((props, ref) => {
  const key = import_react2.default.useMemo(() => {
    return Math.floor(Math.random() * 4294967295).toString();
  }, [props.editor]);
  return import_react2.default.createElement(PureEditorContent, {
    key,
    innerRef: ref,
    ...props
  });
});
var EditorContent = import_react2.default.memo(EditorContentWithKey);
var react = function equal(a, b2) {
  if (a === b2) return true;
  if (a && b2 && typeof a == "object" && typeof b2 == "object") {
    if (a.constructor !== b2.constructor) return false;
    var length, i2, keys;
    if (Array.isArray(a)) {
      length = a.length;
      if (length != b2.length) return false;
      for (i2 = length; i2-- !== 0; )
        if (!equal(a[i2], b2[i2])) return false;
      return true;
    }
    if (a instanceof Map && b2 instanceof Map) {
      if (a.size !== b2.size) return false;
      for (i2 of a.entries())
        if (!b2.has(i2[0])) return false;
      for (i2 of a.entries())
        if (!equal(i2[1], b2.get(i2[0]))) return false;
      return true;
    }
    if (a instanceof Set && b2 instanceof Set) {
      if (a.size !== b2.size) return false;
      for (i2 of a.entries())
        if (!b2.has(i2[0])) return false;
      return true;
    }
    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b2)) {
      length = a.length;
      if (length != b2.length) return false;
      for (i2 = length; i2-- !== 0; )
        if (a[i2] !== b2[i2]) return false;
      return true;
    }
    if (a.constructor === RegExp) return a.source === b2.source && a.flags === b2.flags;
    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b2.valueOf();
    if (a.toString !== Object.prototype.toString) return a.toString() === b2.toString();
    keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b2).length) return false;
    for (i2 = length; i2-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b2, keys[i2])) return false;
    for (i2 = length; i2-- !== 0; ) {
      var key = keys[i2];
      if (key === "_owner" && a.$$typeof) {
        continue;
      }
      if (!equal(a[key], b2[key])) return false;
    }
    return true;
  }
  return a !== a && b2 !== b2;
};
var deepEqual = getDefaultExportFromCjs(react);
var withSelector = { exports: {} };
var withSelector_development = {};
var hasRequiredWithSelector_development;
function requireWithSelector_development() {
  if (hasRequiredWithSelector_development) return withSelector_development;
  hasRequiredWithSelector_development = 1;
  if (true) {
    (function() {
      if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== "undefined" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === "function") {
        __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());
      }
      var React$1 = import_react2.default;
      var shim2 = shimExports;
      function is(x2, y2) {
        return x2 === y2 && (x2 !== 0 || 1 / x2 === 1 / y2) || x2 !== x2 && y2 !== y2;
      }
      var objectIs = typeof Object.is === "function" ? Object.is : is;
      var useSyncExternalStore = shim2.useSyncExternalStore;
      var useRef4 = React$1.useRef, useEffect4 = React$1.useEffect, useMemo2 = React$1.useMemo, useDebugValue2 = React$1.useDebugValue;
      function useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {
        var instRef = useRef4(null);
        var inst;
        if (instRef.current === null) {
          inst = {
            hasValue: false,
            value: null
          };
          instRef.current = inst;
        } else {
          inst = instRef.current;
        }
        var _useMemo = useMemo2(function() {
          var hasMemo = false;
          var memoizedSnapshot;
          var memoizedSelection;
          var memoizedSelector = function(nextSnapshot) {
            if (!hasMemo) {
              hasMemo = true;
              memoizedSnapshot = nextSnapshot;
              var _nextSelection = selector(nextSnapshot);
              if (isEqual !== void 0) {
                if (inst.hasValue) {
                  var currentSelection = inst.value;
                  if (isEqual(currentSelection, _nextSelection)) {
                    memoizedSelection = currentSelection;
                    return currentSelection;
                  }
                }
              }
              memoizedSelection = _nextSelection;
              return _nextSelection;
            }
            var prevSnapshot = memoizedSnapshot;
            var prevSelection = memoizedSelection;
            if (objectIs(prevSnapshot, nextSnapshot)) {
              return prevSelection;
            }
            var nextSelection = selector(nextSnapshot);
            if (isEqual !== void 0 && isEqual(prevSelection, nextSelection)) {
              return prevSelection;
            }
            memoizedSnapshot = nextSnapshot;
            memoizedSelection = nextSelection;
            return nextSelection;
          };
          var maybeGetServerSnapshot = getServerSnapshot === void 0 ? null : getServerSnapshot;
          var getSnapshotWithSelector = function() {
            return memoizedSelector(getSnapshot());
          };
          var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? void 0 : function() {
            return memoizedSelector(maybeGetServerSnapshot());
          };
          return [getSnapshotWithSelector, getServerSnapshotWithSelector];
        }, [getSnapshot, getServerSnapshot, selector, isEqual]), getSelection = _useMemo[0], getServerSelection = _useMemo[1];
        var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);
        useEffect4(function() {
          inst.hasValue = true;
          inst.value = value;
        }, [value]);
        useDebugValue2(value);
        return value;
      }
      withSelector_development.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;
      if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== "undefined" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === "function") {
        __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());
      }
    })();
  }
  return withSelector_development;
}
if (false) {
  withSelector.exports = requireWithSelector_production_min();
} else {
  withSelector.exports = requireWithSelector_development();
}
var withSelectorExports = withSelector.exports;
var isSSR = typeof window === "undefined";
var isNext = isSSR || Boolean(typeof window !== "undefined" && window.next);
var EditorContext = (0, import_react2.createContext)({
  editor: null
});
var EditorConsumer = EditorContext.Consumer;
var ReactNodeViewContext = (0, import_react2.createContext)({
  onDragStart: void 0
});
var useReactNodeView = () => (0, import_react2.useContext)(ReactNodeViewContext);
var NodeViewWrapper = import_react2.default.forwardRef((props, ref) => {
  const { onDragStart } = useReactNodeView();
  const Tag = props.as || "div";
  return (
    // @ts-ignore
    import_react2.default.createElement(Tag, { ...props, ref, "data-node-view-wrapper": "", onDragStart, style: {
      whiteSpace: "normal",
      ...props.style
    } })
  );
});
function isClassComponent(Component) {
  return !!(typeof Component === "function" && Component.prototype && Component.prototype.isReactComponent);
}
function isForwardRefComponent(Component) {
  var _a;
  return !!(typeof Component === "object" && ((_a = Component.$$typeof) === null || _a === void 0 ? void 0 : _a.toString()) === "Symbol(react.forward_ref)");
}
var ReactRenderer = class {
  /**
   * Immediately creates element and renders the provided React component.
   */
  constructor(component, { editor, props = {}, as: as2 = "div", className = "" }) {
    this.ref = null;
    this.id = Math.floor(Math.random() * 4294967295).toString();
    this.component = component;
    this.editor = editor;
    this.props = props;
    this.element = document.createElement(as2);
    this.element.classList.add("react-renderer");
    if (className) {
      this.element.classList.add(...className.split(" "));
    }
    if (this.editor.isInitialized) {
      (0, import_react_dom3.flushSync)(() => {
        this.render();
      });
    } else {
      this.render();
    }
  }
  /**
   * Render the React component.
   */
  render() {
    var _a;
    const Component = this.component;
    const props = this.props;
    const editor = this.editor;
    if (isClassComponent(Component) || isForwardRefComponent(Component)) {
      props.ref = (ref) => {
        this.ref = ref;
      };
    }
    this.reactElement = import_react2.default.createElement(Component, { ...props });
    (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.setRenderer(this.id, this);
  }
  /**
   * Re-renders the React component with new props.
   */
  updateProps(props = {}) {
    this.props = {
      ...this.props,
      ...props
    };
    this.render();
  }
  /**
   * Destroy the React component.
   */
  destroy() {
    var _a;
    const editor = this.editor;
    (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.removeRenderer(this.id);
  }
  /**
   * Update the attributes of the element that holds the React component.
   */
  updateAttributes(attributes) {
    Object.keys(attributes).forEach((key) => {
      this.element.setAttribute(key, attributes[key]);
    });
  }
};
var ReactNodeView = class extends NodeView {
  /**
   * Setup the React component.
   * Called on initialization.
   */
  mount() {
    const props = {
      editor: this.editor,
      node: this.node,
      decorations: this.decorations,
      innerDecorations: this.innerDecorations,
      view: this.view,
      selected: false,
      extension: this.extension,
      HTMLAttributes: this.HTMLAttributes,
      getPos: () => this.getPos(),
      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),
      deleteNode: () => this.deleteNode()
    };
    if (!this.component.displayName) {
      const capitalizeFirstChar = (string) => {
        return string.charAt(0).toUpperCase() + string.substring(1);
      };
      this.component.displayName = capitalizeFirstChar(this.extension.name);
    }
    const onDragStart = this.onDragStart.bind(this);
    const nodeViewContentRef = (element) => {
      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {
        element.appendChild(this.contentDOMElement);
      }
    };
    const context = { onDragStart, nodeViewContentRef };
    const Component = this.component;
    const ReactNodeViewProvider = import_react2.default.memo((componentProps) => {
      return import_react2.default.createElement(ReactNodeViewContext.Provider, { value: context }, import_react2.default.createElement(Component, componentProps));
    });
    ReactNodeViewProvider.displayName = "ReactNodeView";
    if (this.node.isLeaf) {
      this.contentDOMElement = null;
    } else if (this.options.contentDOMElementTag) {
      this.contentDOMElement = document.createElement(this.options.contentDOMElementTag);
    } else {
      this.contentDOMElement = document.createElement(this.node.isInline ? "span" : "div");
    }
    if (this.contentDOMElement) {
      this.contentDOMElement.dataset.nodeViewContentReact = "";
      this.contentDOMElement.style.whiteSpace = "inherit";
    }
    let as2 = this.node.isInline ? "span" : "div";
    if (this.options.as) {
      as2 = this.options.as;
    }
    const { className = "" } = this.options;
    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this);
    this.renderer = new ReactRenderer(ReactNodeViewProvider, {
      editor: this.editor,
      props,
      as: as2,
      className: `node-${this.node.type.name} ${className}`.trim()
    });
    this.editor.on("selectionUpdate", this.handleSelectionUpdate);
    this.updateElementAttributes();
  }
  /**
   * Return the DOM element.
   * This is the element that will be used to display the node view.
   */
  get dom() {
    var _a;
    if (this.renderer.element.firstElementChild && !((_a = this.renderer.element.firstElementChild) === null || _a === void 0 ? void 0 : _a.hasAttribute("data-node-view-wrapper"))) {
      throw Error("Please use the NodeViewWrapper component for your node view.");
    }
    return this.renderer.element;
  }
  /**
   * Return the content DOM element.
   * This is the element that will be used to display the rich-text content of the node.
   */
  get contentDOM() {
    if (this.node.isLeaf) {
      return null;
    }
    return this.contentDOMElement;
  }
  /**
   * On editor selection update, check if the node is selected.
   * If it is, call `selectNode`, otherwise call `deselectNode`.
   */
  handleSelectionUpdate() {
    const { from, to: to2 } = this.editor.state.selection;
    const pos = this.getPos();
    if (typeof pos !== "number") {
      return;
    }
    if (from <= pos && to2 >= pos + this.node.nodeSize) {
      if (this.renderer.props.selected) {
        return;
      }
      this.selectNode();
    } else {
      if (!this.renderer.props.selected) {
        return;
      }
      this.deselectNode();
    }
  }
  /**
   * On update, update the React component.
   * To prevent unnecessary updates, the `update` option can be used.
   */
  update(node, decorations, innerDecorations) {
    const rerenderComponent = (props) => {
      this.renderer.updateProps(props);
      if (typeof this.options.attrs === "function") {
        this.updateElementAttributes();
      }
    };
    if (node.type !== this.node.type) {
      return false;
    }
    if (typeof this.options.update === "function") {
      const oldNode = this.node;
      const oldDecorations = this.decorations;
      const oldInnerDecorations = this.innerDecorations;
      this.node = node;
      this.decorations = decorations;
      this.innerDecorations = innerDecorations;
      return this.options.update({
        oldNode,
        oldDecorations,
        newNode: node,
        newDecorations: decorations,
        oldInnerDecorations,
        innerDecorations,
        updateProps: () => rerenderComponent({ node, decorations, innerDecorations })
      });
    }
    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {
      return true;
    }
    this.node = node;
    this.decorations = decorations;
    this.innerDecorations = innerDecorations;
    rerenderComponent({ node, decorations, innerDecorations });
    return true;
  }
  /**
   * Select the node.
   * Add the `selected` prop and the `ProseMirror-selectednode` class.
   */
  selectNode() {
    this.renderer.updateProps({
      selected: true
    });
    this.renderer.element.classList.add("ProseMirror-selectednode");
  }
  /**
   * Deselect the node.
   * Remove the `selected` prop and the `ProseMirror-selectednode` class.
   */
  deselectNode() {
    this.renderer.updateProps({
      selected: false
    });
    this.renderer.element.classList.remove("ProseMirror-selectednode");
  }
  /**
   * Destroy the React component instance.
   */
  destroy() {
    this.renderer.destroy();
    this.editor.off("selectionUpdate", this.handleSelectionUpdate);
    this.contentDOMElement = null;
  }
  /**
   * Update the attributes of the top-level element that holds the React component.
   * Applying the attributes defined in the `attrs` option.
   */
  updateElementAttributes() {
    if (this.options.attrs) {
      let attrsObj = {};
      if (typeof this.options.attrs === "function") {
        const extensionAttributes = this.editor.extensionManager.attributes;
        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes);
        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes });
      } else {
        attrsObj = this.options.attrs;
      }
      this.renderer.updateAttributes(attrsObj);
    }
  }
};
function ReactNodeViewRenderer(component, options) {
  return (props) => {
    if (!props.editor.contentComponent) {
      return {};
    }
    return new ReactNodeView(component, props, options);
  };
}

// node_modules/@blocknote/react/dist/blocknote-react.js
var import_client = __toESM(require_client());
var Zt = Object.defineProperty;
var Oe = (e) => {
  throw TypeError(e);
};
var Ft = (e, t, n) => t in e ? Zt(e, t, { enumerable: true, configurable: true, writable: true, value: n }) : e[t] = n;
var L = (e, t, n) => Ft(e, typeof t != "symbol" ? t + "" : t, n);
var At = (e, t, n) => t.has(e) || Oe("Cannot " + n);
var Ze = (e, t, n) => t.has(e) ? Oe("Cannot add the same private member more than once") : t instanceof WeakSet ? t.add(e) : t.set(e, n);
var ge = (e, t, n) => (At(e, t, "access private method"), n);
var dt = (0, import_react3.createContext)(void 0);
function O(e) {
  return (0, import_react3.useContext)(dt);
}
function b(e) {
  const t = O();
  if (!(t != null && t.editor))
    throw new Error(
      "useBlockNoteEditor was called outside of a BlockNoteContext provider or BlockNoteView component"
    );
  return t.editor;
}
function se(e, t, n) {
  const o = O();
  t || (t = o == null ? void 0 : o.editor), (0, import_react3.useEffect)(() => {
    if (!t)
      throw new Error(
        "'editor' is required, either from BlockNoteContext or as a function argument"
      );
    return t.onSelectionChange(e, n);
  }, [e, t, n]);
}
function Pn(e, t) {
  const n = b();
  t = t || n;
  const [o, r] = (0, import_react3.useState)(() => {
    if (e)
      return t.getSelectionBoundingBox();
  }), l = (0, import_react3.useCallback)(() => {
    if (!e)
      return;
    const c = t.getSelectionBoundingBox();
    r(c);
  }, [t, e]);
  return se(l, t, true), (0, import_react3.useEffect)(() => {
    r(e ? t.getSelectionBoundingBox() : void 0);
  }, [e, t]), o;
}
function Dn(e) {
  return e.getBoundingClientRect !== void 0;
}
function G(e, t, n, o) {
  const { refs: r, update: l, context: c, floatingStyles: s, isPositioned: d } = useFloating2({
    open: e,
    ...o
  }), { isMounted: a, styles: u } = useTransitionStyles(c), m = typeof (o == null ? void 0 : o.canDismiss) == "object" ? o.canDismiss : {
    enabled: o == null ? void 0 : o.canDismiss
  }, f = useDismiss(c, m), { getReferenceProps: h, getFloatingProps: H } = useInteractions([f]);
  return (0, import_react3.useEffect)(() => {
    l();
  }, [t, l]), (0, import_react3.useEffect)(() => {
    t !== null && (t instanceof HTMLElement || Dn(t) ? r.setReference(t) : r.setReference({
      getBoundingClientRect: () => t
    }));
  }, [t, r]), (0, import_react3.useMemo)(() => ({
    isMounted: a,
    ref: r.setFloating,
    setReference: r.setReference,
    style: {
      display: "flex",
      ...u,
      ...s,
      zIndex: n
    },
    getFloatingProps: H,
    getReferenceProps: h,
    isPositioned: d
  }), [
    d,
    s,
    a,
    r.setFloating,
    r.setReference,
    u,
    n,
    H,
    h
  ]);
}
function D(e) {
  const [t, n] = (0, import_react3.useState)();
  return (0, import_react3.useEffect)(() => e((o) => {
    n({ ...o });
  }), [e]), t;
}
var On = (0, import_react3.createContext)(
  void 0
);
function C() {
  return (0, import_react3.useContext)(On);
}
var de = (e = {}, t = []) => (0, import_react3.useMemo)(() => {
  const n = ro.create(e);
  return window && (window.ProseMirror = n._tiptapEditor), n;
}, t);
var Fi = de;
function M() {
  return O().editor.dictionary;
}
function ue(e, t) {
  const n = O();
  t || (t = n == null ? void 0 : n.editor), (0, import_react3.useEffect)(() => {
    if (!t)
      throw new Error(
        "'editor' is required, either from BlockNoteContext or as a function argument"
      );
    return t.onChange(e);
  }, [e, t]);
}
var Le = (e) => {
  const [t, n] = (0, import_react3.useState)(false), [o, r] = (0, import_react3.useState)(e.editor.isEmpty), l = C();
  ue(() => {
    r(e.editor.isEmpty);
  }, e.editor);
  const c = (0, import_react3.useCallback)(() => {
    n(true);
  }, []), s = (0, import_react3.useCallback)(() => {
    n(false);
  }, []);
  return (0, import_react3.useEffect)(() => {
    e.editable && e.autoFocus && e.editor.focus();
  }, [e.autoFocus, e.editable, e.editor]), (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      l.Comments.Editor,
      {
        autoFocus: e.autoFocus,
        className: "bn-comment-editor",
        editor: e.editor,
        onFocus: c,
        onBlur: s,
        editable: e.editable
      }
    ),
    e.actions && (0, import_jsx_runtime.jsx)("div", { className: "bn-comment-actions-wrapper", children: (0, import_jsx_runtime.jsx)(e.actions, { isFocused: t, isEmpty: o }) })
  ] });
};
var Zn = oe(
  q(
    Vn.paragraph.implementation.node.config
  ),
  // disable default props on paragraph (such as textalignment and colors)
  {}
);
var { textColor: Ai, backgroundColor: Gi, ...Fn } = Un;
var Ee = Pe.create({
  blockSpecs: {
    paragraph: Zn
  },
  styleSpecs: Fn
});
function An() {
  const e = b();
  if (!e.comments)
    throw new Error("Comments plugin not found");
  const t = e.comments, n = C(), o = M(), r = de({
    trailingBlock: false,
    dictionary: {
      ...o,
      placeholders: {
        emptyDocument: o.placeholders.new_comment
      }
    },
    schema: Ee,
    sideMenuDetection: "editor"
  });
  return (0, import_jsx_runtime.jsx)(n.Comments.Card, { className: "bn-thread", children: (0, import_jsx_runtime.jsx)(
    Le,
    {
      autoFocus: true,
      editable: true,
      editor: r,
      actions: ({ isEmpty: l }) => (0, import_jsx_runtime.jsx)(
        n.Generic.Toolbar.Root,
        {
          className: Q(
            "bn-action-toolbar",
            "bn-comment-actions"
          ),
          variant: "action-toolbar",
          children: (0, import_jsx_runtime.jsx)(
            n.Generic.Toolbar.Button,
            {
              className: "bn-button",
              mainTooltip: "Save",
              variant: "compact",
              isDisabled: l,
              onClick: async () => {
                await t.createThread({
                  initialComment: {
                    body: r.document
                  }
                }), t.stopPendingComment();
              },
              children: "Save"
            }
          )
        }
      )
    }
  ) });
}
var Gn = (e) => {
  const t = b();
  if (!t.comments)
    throw new Error(
      "FloatingComposerController can only be used when BlockNote editor has enabled comments"
    );
  const n = t.comments;
  (0, import_react3.useEffect)(() => {
    const u = n.onUpdate(
      (m) => t.setForceSelectionVisible(m.pendingComment)
    );
    return () => u();
  }, [n, t]);
  const o = D(n.onUpdate.bind(n)), r = Pn(o == null ? void 0 : o.pendingComment), { isMounted: l, ref: c, style: s, getFloatingProps: d } = G(
    (o == null ? void 0 : o.pendingComment) || false,
    r || null,
    5e3,
    {
      placement: "bottom",
      middleware: [offset(10), shift(), flip()],
      onOpenChange: (u) => {
        u || (n.stopPendingComment(), t.focus());
      },
      ...e.floatingOptions
    }
  );
  if (!l || !o)
    return null;
  const a = e.floatingComposer || An;
  return (0, import_jsx_runtime.jsx)("div", { ref: c, style: s, ...d(), children: (0, import_jsx_runtime.jsx)(a, {}) });
};
var ut = {
  color: void 0,
  size: void 0,
  className: void 0,
  style: void 0,
  attr: void 0
};
var Ge = import_react3.default.createContext && import_react3.default.createContext(ut);
var Un2 = ["attr", "size", "title"];
function zn(e, t) {
  if (e == null) return {};
  var n = jn(e, t), o, r;
  if (Object.getOwnPropertySymbols) {
    var l = Object.getOwnPropertySymbols(e);
    for (r = 0; r < l.length; r++)
      o = l[r], !(t.indexOf(o) >= 0) && Object.prototype.propertyIsEnumerable.call(e, o) && (n[o] = e[o]);
  }
  return n;
}
function jn(e, t) {
  if (e == null) return {};
  var n = {};
  for (var o in e)
    if (Object.prototype.hasOwnProperty.call(e, o)) {
      if (t.indexOf(o) >= 0) continue;
      n[o] = e[o];
    }
  return n;
}
function oe2() {
  return oe2 = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var o in n)
        Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
    }
    return e;
  }, oe2.apply(this, arguments);
}
function Ue(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    t && (o = o.filter(function(r) {
      return Object.getOwnPropertyDescriptor(e, r).enumerable;
    })), n.push.apply(n, o);
  }
  return n;
}
function re(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = arguments[t] != null ? arguments[t] : {};
    t % 2 ? Ue(Object(n), true).forEach(function(o) {
      Wn(e, o, n[o]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ue(Object(n)).forEach(function(o) {
      Object.defineProperty(e, o, Object.getOwnPropertyDescriptor(n, o));
    });
  }
  return e;
}
function Wn(e, t, n) {
  return t = $n(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: true, configurable: true, writable: true }) : e[t] = n, e;
}
function $n(e) {
  var t = qn(e, "string");
  return typeof t == "symbol" ? t : t + "";
}
function qn(e, t) {
  if (typeof e != "object" || !e) return e;
  var n = e[Symbol.toPrimitive];
  if (n !== void 0) {
    var o = n.call(e, t);
    if (typeof o != "object") return o;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (t === "string" ? String : Number)(e);
}
function mt(e) {
  return e && e.map((t, n) => import_react3.default.createElement(t.tag, re({
    key: n
  }, t.attr), mt(t.child)));
}
function k(e) {
  return (t) => import_react3.default.createElement(Kn, oe2({
    attr: re({}, e.attr)
  }, t), mt(e.child));
}
function Kn(e) {
  var t = (n) => {
    var {
      attr: o,
      size: r,
      title: l
    } = e, c = zn(e, Un2), s = r || n.size || "1em", d;
    return n.className && (d = n.className), e.className && (d = (d ? d + " " : "") + e.className), import_react3.default.createElement("svg", oe2({
      stroke: "currentColor",
      fill: "currentColor",
      strokeWidth: "0"
    }, n.attr, o, c, {
      className: d,
      style: re(re({
        color: e.color || n.color
      }, n.style), e.style),
      height: s,
      width: s,
      xmlns: "http://www.w3.org/2000/svg"
    }), l && import_react3.default.createElement("title", null, l), e.children);
  };
  return Ge !== void 0 ? import_react3.default.createElement(Ge.Consumer, null, (n) => t(n)) : t(ut);
}
function Xn(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 7V11L2 6L8 1V5H13C17.4183 5 21 8.58172 21 13C21 17.4183 17.4183 21 13 21H4V19H13C16.3137 19 19 16.3137 19 13C19 9.68629 16.3137 7 13 7H8Z" }, child: [] }] })(e);
}
function ft(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M7.29117 20.8242L2 22L3.17581 16.7088C2.42544 15.3056 2 13.7025 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C10.2975 22 8.6944 21.5746 7.29117 20.8242ZM7.58075 18.711L8.23428 19.0605C9.38248 19.6745 10.6655 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 13.3345 4.32549 14.6175 4.93949 15.7657L5.28896 16.4192L4.63416 19.3658L7.58075 18.711Z" }, child: [] }] })(e);
}
function Yn(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M7.24264 17.9967H3V13.754L14.435 2.319C14.8256 1.92848 15.4587 1.92848 15.8492 2.319L18.6777 5.14743C19.0682 5.53795 19.0682 6.17112 18.6777 6.56164L7.24264 17.9967ZM3 19.9967H21V21.9967H3V19.9967Z" }, child: [] }] })(e);
}
function Jn(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z" }, child: [] }] })(e);
}
function Re(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 8L9.00319 2H19.9978C20.5513 2 21 2.45531 21 2.9918V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8ZM10 4V9H5V20H19V4H10Z" }, child: [] }] })(e);
}
function Qn(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM5 19H19V21H5V19ZM3 14H21V16H3V14ZM5 9H19V11H5V9Z" }, child: [] }] })(e);
}
function eo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM3 19H21V21H3V19ZM3 14H21V16H3V14ZM3 9H21V11H3V9Z" }, child: [] }] })(e);
}
function to(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM3 19H17V21H3V19ZM3 14H21V16H3V14ZM3 9H17V11H3V9Z" }, child: [] }] })(e);
}
function no(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM7 19H21V21H7V19ZM3 14H21V16H3V14ZM7 9H21V11H7V9Z" }, child: [] }] })(e);
}
function oo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 11H12.5C13.8807 11 15 9.88071 15 8.5C15 7.11929 13.8807 6 12.5 6H8V11ZM18 15.5C18 17.9853 15.9853 20 13.5 20H6V4H12.5C14.9853 4 17 6.01472 17 8.5C17 9.70431 16.5269 10.7981 15.7564 11.6058C17.0979 12.3847 18 13.837 18 15.5ZM8 13V18H13.5C14.8807 18 16 16.8807 16 15.5C16 14.1193 14.8807 13 13.5 13H8Z" }, child: [] }] })(e);
}
function ro2(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3.41436 5.99995L5.70726 3.70706L4.29304 2.29285L0.585938 5.99995L4.29304 9.70706L5.70726 8.29285L3.41436 5.99995ZM9.58594 5.99995L7.29304 3.70706L8.70726 2.29285L12.4144 5.99995L8.70726 9.70706L7.29304 8.29285L9.58594 5.99995ZM14.0002 2.99995H21.0002C21.5524 2.99995 22.0002 3.44767 22.0002 3.99995V20C22.0002 20.5522 21.5524 21 21.0002 21H3.00015C2.44787 21 2.00015 20.5522 2.00015 20V12H4.00015V19H20.0002V4.99995H14.0002V2.99995Z" }, child: [] }] })(e);
}
function ze(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M5.55397 22H3.3999L10.9999 3H12.9999L20.5999 22H18.4458L16.0458 16H7.95397L5.55397 22ZM8.75397 14H15.2458L11.9999 5.88517L8.75397 14Z" }, child: [] }] })(e);
}
function ht(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M13 20H11V13H4V20H2V4H4V11H11V4H13V20ZM21.0005 8V20H19.0005L19 10.204L17 10.74V8.67L19.5005 8H21.0005Z" }, child: [] }] })(e);
}
function gt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M4 4V11H11V4H13V20H11V13H4V20H2V4H4ZM18.5 8C20.5711 8 22.25 9.67893 22.25 11.75C22.25 12.6074 21.9623 13.3976 21.4781 14.0292L21.3302 14.2102L18.0343 18H22V20H15L14.9993 18.444L19.8207 12.8981C20.0881 12.5908 20.25 12.1893 20.25 11.75C20.25 10.7835 19.4665 10 18.5 10C17.5818 10 16.8288 10.7071 16.7558 11.6065L16.75 11.75H14.75C14.75 9.67893 16.4289 8 18.5 8Z" }, child: [] }] })(e);
}
function bt2(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M22 8L21.9984 10L19.4934 12.883C21.0823 13.3184 22.25 14.7728 22.25 16.5C22.25 18.5711 20.5711 20.25 18.5 20.25C16.674 20.25 15.1528 18.9449 14.8184 17.2166L16.7821 16.8352C16.9384 17.6413 17.6481 18.25 18.5 18.25C19.4665 18.25 20.25 17.4665 20.25 16.5C20.25 15.5335 19.4665 14.75 18.5 14.75C18.214 14.75 17.944 14.8186 17.7056 14.9403L16.3992 13.3932L19.3484 10H15V8H22ZM4 4V11H11V4H13V20H11V13H4V20H2V4H4Z" }, child: [] }] })(e);
}
function io(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM3 19H21V21H3V19ZM11 14H21V16H11V14ZM11 9H21V11H11V9ZM3 12.5L7 9V16L3 12.5Z" }, child: [] }] })(e);
}
function lo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 4H21V6H3V4ZM3 19H21V21H3V19ZM11 14H21V16H11V14ZM11 9H21V11H11V9ZM7 12.5L3 16V9L7 12.5Z" }, child: [] }] })(e);
}
function je(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 5H11V19H8V21H16V19H13V5H16V3H8V5ZM2 7C1.44772 7 1 7.44772 1 8V16C1 16.5523 1.44772 17 2 17H8V15H3V9H8V7H2ZM16 9H21V15H16V17H22C22.5523 17 23 16.5523 23 16V8C23 7.44772 22.5523 7 22 7H16V9Z" }, child: [] }] })(e);
}
function co(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M15 20H7V18H9.92661L12.0425 6H9V4H17V6H14.0734L11.9575 18H15V20Z" }, child: [] }] })(e);
}
function ao(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M17 17H22V19H19V22H17V17ZM7 7H2V5H5V2H7V7ZM18.364 15.5355L16.9497 14.1213L18.364 12.7071C20.3166 10.7545 20.3166 7.58866 18.364 5.63604C16.4113 3.68342 13.2455 3.68342 11.2929 5.63604L9.87868 7.05025L8.46447 5.63604L9.87868 4.22183C12.6123 1.48816 17.0445 1.48816 19.7782 4.22183C22.5118 6.9555 22.5118 11.3877 19.7782 14.1213L18.364 15.5355ZM15.5355 18.364L14.1213 19.7782C11.3877 22.5118 6.9555 22.5118 4.22183 19.7782C1.48816 17.0445 1.48816 12.6123 4.22183 9.87868L5.63604 8.46447L7.05025 9.87868L5.63604 11.2929C3.68342 13.2455 3.68342 16.4113 5.63604 18.364C7.58866 20.3166 10.7545 20.3166 12.7071 18.364L14.1213 16.9497L15.5355 18.364ZM14.8284 7.75736L16.2426 9.17157L9.17157 16.2426L7.75736 14.8284L14.8284 7.75736Z" }, child: [] }] })(e);
}
function Ct(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M18.3638 15.5355L16.9496 14.1213L18.3638 12.7071C20.3164 10.7545 20.3164 7.58866 18.3638 5.63604C16.4112 3.68341 13.2453 3.68341 11.2927 5.63604L9.87849 7.05025L8.46428 5.63604L9.87849 4.22182C12.6122 1.48815 17.0443 1.48815 19.778 4.22182C22.5117 6.95549 22.5117 11.3876 19.778 14.1213L18.3638 15.5355ZM15.5353 18.364L14.1211 19.7782C11.3875 22.5118 6.95531 22.5118 4.22164 19.7782C1.48797 17.0445 1.48797 12.6123 4.22164 9.87868L5.63585 8.46446L7.05007 9.87868L5.63585 11.2929C3.68323 13.2455 3.68323 16.4113 5.63585 18.364C7.58847 20.3166 10.7543 20.3166 12.7069 18.364L14.1211 16.9497L15.5353 18.364ZM14.8282 7.75736L16.2425 9.17157L9.17139 16.2426L7.75717 14.8284L14.8282 7.75736Z" }, child: [] }] })(e);
}
function pt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8.00008 6V9H5.00008V6H8.00008ZM3.00008 4V11H10.0001V4H3.00008ZM13.0001 4H21.0001V6H13.0001V4ZM13.0001 11H21.0001V13H13.0001V11ZM13.0001 18H21.0001V20H13.0001V18ZM10.7072 16.2071L9.29297 14.7929L6.00008 18.0858L4.20718 16.2929L2.79297 17.7071L6.00008 20.9142L10.7072 16.2071Z" }, child: [] }] })(e);
}
function kt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 4H21V6H8V4ZM5 3V6H6V7H3V6H4V4H3V3H5ZM3 14V11.5H5V11H3V10H6V12.5H4V13H6V14H3ZM5 19.5H3V18.5H5V18H3V17H6V21H3V20H5V19.5ZM8 11H21V13H8V11ZM8 18H21V20H8V18Z" }, child: [] }] })(e);
}
function wt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 4H21V6H8V4ZM4.5 6.5C3.67157 6.5 3 5.82843 3 5C3 4.17157 3.67157 3.5 4.5 3.5C5.32843 3.5 6 4.17157 6 5C6 5.82843 5.32843 6.5 4.5 6.5ZM4.5 13.5C3.67157 13.5 3 12.8284 3 12C3 11.1716 3.67157 10.5 4.5 10.5C5.32843 10.5 6 11.1716 6 12C6 12.8284 5.32843 13.5 4.5 13.5ZM4.5 20.4C3.67157 20.4 3 19.7284 3 18.9C3 18.0716 3.67157 17.4 4.5 17.4C5.32843 17.4 6 18.0716 6 18.9C6 19.7284 5.32843 20.4 4.5 20.4ZM8 11H21V13H8V11ZM8 18H21V20H8V18Z" }, child: [] }] })(e);
}
function so(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M20 3C20.5523 3 21 3.44772 21 4V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3H20ZM11 5H5V10.999H7V9L10 12L7 15V13H5V19H11V17H13V19H19V13H17V15L14 12L17 9V10.999H19V5H13V7H11V5ZM13 13V15H11V13H13ZM13 9V11H11V9H13Z" }, child: [] }] })(e);
}
function uo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M21 20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3H20C20.5523 3 21 3.44772 21 4V20ZM19 11V5H13.001V7H15L12 10L9 7H11V5H5V11H7V13H5V19H11V17H9L12 14L15 17H13.001V19H19V13H17V11H19ZM11 13H9V11H11V13ZM15 13H13V11H15V13Z" }, child: [] }] })(e);
}
function vt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M21 4H3V6H21V4ZM21 11H8V13H21V11ZM21 18H8V20H21V18ZM5 11H3V20H5V11Z" }, child: [] }] })(e);
}
function mo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M17.1538 14C17.3846 14.5161 17.5 15.0893 17.5 15.7196C17.5 17.0625 16.9762 18.1116 15.9286 18.867C14.8809 19.6223 13.4335 20 11.5862 20C9.94674 20 8.32335 19.6185 6.71592 18.8555V16.6009C8.23538 17.4783 9.7908 17.917 11.3822 17.917C13.9333 17.917 15.2128 17.1846 15.2208 15.7196C15.2208 15.0939 15.0049 14.5598 14.5731 14.1173C14.5339 14.0772 14.4939 14.0381 14.4531 14H3V12H21V14H17.1538ZM13.076 11H7.62908C7.4566 10.8433 7.29616 10.6692 7.14776 10.4778C6.71592 9.92084 6.5 9.24559 6.5 8.45207C6.5 7.21602 6.96583 6.165 7.89749 5.299C8.82916 4.43299 10.2706 4 12.2219 4C13.6934 4 15.1009 4.32808 16.4444 4.98426V7.13591C15.2448 6.44921 13.9293 6.10587 12.4978 6.10587C10.0187 6.10587 8.77917 6.88793 8.77917 8.45207C8.77917 8.87172 8.99709 9.23796 9.43293 9.55079C9.86878 9.86362 10.4066 10.1135 11.0463 10.3004C11.6665 10.4816 12.3431 10.7148 13.076 11H13.076Z" }, child: [] }] })(e);
}
function fo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M13 10V14H19V10H13ZM11 10H5V14H11V10ZM13 19H19V16H13V19ZM11 19V16H5V19H11ZM13 5V8H19V5H13ZM11 5H5V8H11V5ZM4 3H20C20.5523 3 21 3.44772 21 4V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3Z" }, child: [] }] })(e);
}
function _e(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M13 6V21H11V6H5V4H19V6H13Z" }, child: [] }] })(e);
}
function ho(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M8 3V12C8 14.2091 9.79086 16 12 16C14.2091 16 16 14.2091 16 12V3H18V12C18 15.3137 15.3137 18 12 18C8.68629 18 6 15.3137 6 12V3H8ZM4 20H20V22H4V20Z" }, child: [] }] })(e);
}
function go(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M2 3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.44495 22 3.9934V20.0066C22 20.5552 21.5447 21 21.0082 21H2.9918C2.44405 21 2 20.5551 2 20.0066V3.9934ZM8 5V19H16V5H8ZM4 5V7H6V5H4ZM18 5V7H20V5H18ZM4 9V11H6V9H4ZM18 9V11H20V9H18ZM4 13V15H6V13H4ZM18 13V15H20V13H18ZM4 17V19H6V17H4ZM18 17V19H20V17H18Z" }, child: [] }] })(e);
}
function Ht(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M5 11.1005L7 9.1005L12.5 14.6005L16 11.1005L19 14.1005V5H5V11.1005ZM4 3H20C20.5523 3 21 3.44772 21 4V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3ZM15.5 10C14.6716 10 14 9.32843 14 8.5C14 7.67157 14.6716 7 15.5 7C16.3284 7 17 7.67157 17 8.5C17 9.32843 16.3284 10 15.5 10Z" }, child: [] }] })(e);
}
function bo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M21 15V18H24V20H21V23H19V20H16V18H19V15H21ZM21.0082 3C21.556 3 22 3.44495 22 3.9934L22.0007 13.3417C21.3749 13.1204 20.7015 13 20 13V5H4L4.001 19L13.2929 9.70715C13.6528 9.34604 14.22 9.31823 14.6123 9.62322L14.7065 9.70772L18.2521 13.2586C15.791 14.0069 14 16.2943 14 19C14 19.7015 14.1204 20.3749 14.3417 21.0007L2.9918 21C2.44405 21 2 20.5551 2 20.0066V3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082ZM8 7C9.10457 7 10 7.89543 10 9C10 10.1046 9.10457 11 8 11C6.89543 11 6 10.1046 6 9C6 7.89543 6.89543 7 8 7Z" }, child: [] }] })(e);
}
function Co(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M20 3C20.5523 3 21 3.44772 21 4V5.757L19 7.757V5H5V13.1L9 9.1005L13.328 13.429L12.0012 14.7562L11.995 18.995L16.2414 19.0012L17.571 17.671L18.8995 19H19V16.242L21 14.242V20C21 20.5523 20.5523 21 20 21H4C3.44772 21 3 20.5523 3 20V4C3 3.44772 3.44772 3 4 3H20ZM21.7782 7.80761L23.1924 9.22183L15.4142 17L13.9979 16.9979L14 15.5858L21.7782 7.80761ZM15.5 7C16.3284 7 17 7.67157 17 8.5C17 9.32843 16.3284 10 15.5 10C14.6716 10 14 9.32843 14 8.5C14 7.67157 14.6716 7 15.5 7Z" }, child: [] }] })(e);
}
function po(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M3 3.9934C3 3.44476 3.44495 3 3.9934 3H20.0066C20.5552 3 21 3.44495 21 3.9934V20.0066C21 20.5552 20.5551 21 20.0066 21H3.9934C3.44476 21 3 20.5551 3 20.0066V3.9934ZM10.6219 8.41459C10.5562 8.37078 10.479 8.34741 10.4 8.34741C10.1791 8.34741 10 8.52649 10 8.74741V15.2526C10 15.3316 10.0234 15.4088 10.0672 15.4745C10.1897 15.6583 10.4381 15.708 10.6219 15.5854L15.5008 12.3328C15.5447 12.3035 15.5824 12.2658 15.6117 12.2219C15.7343 12.0381 15.6846 11.7897 15.5008 11.6672L10.6219 8.41459Z" }, child: [] }] })(e);
}
function Mt(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M2 16.0001H5.88889L11.1834 20.3319C11.2727 20.405 11.3846 20.4449 11.5 20.4449C11.7761 20.4449 12 20.2211 12 19.9449V4.05519C12 3.93977 11.9601 3.8279 11.887 3.73857C11.7121 3.52485 11.3971 3.49335 11.1834 3.66821L5.88889 8.00007H2C1.44772 8.00007 1 8.44778 1 9.00007V15.0001C1 15.5524 1.44772 16.0001 2 16.0001ZM23 12C23 15.292 21.5539 18.2463 19.2622 20.2622L17.8445 18.8444C19.7758 17.1937 21 14.7398 21 12C21 9.26016 19.7758 6.80629 17.8445 5.15557L19.2622 3.73779C21.5539 5.75368 23 8.70795 23 12ZM18 12C18 10.0883 17.106 8.38548 15.7133 7.28673L14.2842 8.71584C15.3213 9.43855 16 10.64 16 12C16 13.36 15.3213 14.5614 14.2842 15.2841L15.7133 16.7132C17.106 15.6145 18 13.9116 18 12Z" }, child: [] }] })(e);
}
function ko(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z" }, child: [] }] })(e);
}
function wo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M9.9997 15.1709L19.1921 5.97852L20.6063 7.39273L9.9997 17.9993L3.63574 11.6354L5.04996 10.2212L9.9997 15.1709Z" }, child: [] }] })(e);
}
function vo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM18 8H6V20H18V8ZM9 4V6H15V4H9Z" }, child: [] }] })(e);
}
function Ho(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM9 11V17H11V11H9ZM13 11V17H15V11H13ZM9 4V6H15V4H9Z" }, child: [] }] })(e);
}
function Mo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M4 19H20V12H22V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V12H4V19ZM14 9H19L12 16L5 9H10V3H14V9Z" }, child: [] }] })(e);
}
function xo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M10 6V8H5V19H16V14H18V20C18 20.5523 17.5523 21 17 21H4C3.44772 21 3 20.5523 3 20V7C3 6.44772 3.44772 6 4 6H10ZM21 3V12L17.206 8.207L11.2071 14.2071L9.79289 12.7929L15.792 6.793L12 3H21Z" }, child: [] }] })(e);
}
function yo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M5 10C3.9 10 3 10.9 3 12C3 13.1 3.9 14 5 14C6.1 14 7 13.1 7 12C7 10.9 6.1 10 5 10ZM19 10C17.9 10 17 10.9 17 12C17 13.1 17.9 14 19 14C20.1 14 21 13.1 21 12C21 10.9 20.1 10 19 10ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10Z" }, child: [] }] })(e);
}
function Bo(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM8 13C8 15.2091 9.79086 17 12 17C14.2091 17 16 15.2091 16 13H8ZM8 11C8.82843 11 9.5 10.3284 9.5 9.5C9.5 8.67157 8.82843 8 8 8C7.17157 8 6.5 8.67157 6.5 9.5C6.5 10.3284 7.17157 11 8 11ZM16 11C16.8284 11 17.5 10.3284 17.5 9.5C17.5 8.67157 16.8284 8 16 8C15.1716 8 14.5 8.67157 14.5 9.5C14.5 10.3284 15.1716 11 16 11Z" }, child: [] }] })(e);
}
function We(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM8 13H16C16 15.2091 14.2091 17 12 17C9.79086 17 8 15.2091 8 13ZM8 11C7.17157 11 6.5 10.3284 6.5 9.5C6.5 8.67157 7.17157 8 8 8C8.82843 8 9.5 8.67157 9.5 9.5C9.5 10.3284 8.82843 11 8 11ZM16 11C15.1716 11 14.5 10.3284 14.5 9.5C14.5 8.67157 15.1716 8 16 8C16.8284 8 17.5 8.67157 17.5 9.5C17.5 10.3284 16.8284 11 16 11Z" }, child: [] }] })(e);
}
var te;
async function Vo() {
  return te || (te = (async () => {
    const [e, t] = await Promise.all([
      import("./module-OAL6LJTV.js"),
      // use a dynamic import to encourage bundle-splitting
      // and a smaller initial client bundle size
      import("./native-7OJ4TN36.js")
    ]), n = "default" in e ? e.default : e, o = "default" in t ? t.default : t;
    return await n.init({ data: o }), { emojiMart: n, emojiData: o };
  })(), te);
}
function So(e) {
  const t = (0, import_react3.useRef)(null), n = (0, import_react3.useRef)(null);
  return n.current && n.current.update(e), (0, import_react3.useEffect)(() => ((async () => {
    const { emojiMart: o } = await Vo();
    n.current = new o.Picker({ ...e, ref: t });
  })(), () => {
    n.current = null;
  }), []), import_react3.default.createElement("div", { ref: t });
}
var $e = (e) => {
  const [t, n] = (0, import_react3.useState)(false), o = C(), r = O();
  return (0, import_jsx_runtime.jsxs)(o.Generic.Popover.Root, { opened: t, children: [
    (0, import_jsx_runtime.jsx)(o.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      "div",
      {
        onClick: (l) => {
          l.preventDefault(), l.stopPropagation(), n(!t);
        },
        style: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center"
        },
        children: e.children
      }
    ) }),
    (0, import_jsx_runtime.jsx)(o.Generic.Popover.Content, { variant: "panel-popover", children: (0, import_jsx_runtime.jsx)(
      So,
      {
        perLine: 7,
        onClickOutside: () => n(false),
        onEmojiSelect: (l) => {
          e.onEmojiSelect(l), n(false);
        },
        theme: r == null ? void 0 : r.colorSchemePreference
      }
    ) })
  ] });
};
function To(e, t) {
  return Ie2(e, [t]).get(t);
}
function Ie2(e, t) {
  const n = e.comments;
  if (!n)
    throw new Error("Comments plugin not found");
  const o = n.userStore, r = (0, import_react3.useCallback)(() => {
    const s = /* @__PURE__ */ new Map();
    for (const d of t) {
      const a = o.getUser(d);
      a && s.set(d, a);
    }
    return s;
  }, [o, t]), l = (0, import_react3.useMemo)(() => ({
    current: r()
  }), [r]), c = (0, import_react3.useCallback)(
    (s) => {
      const d = o.subscribe((a) => {
        l.current = r(), s();
      });
      return o.loadUsers(t), d;
    },
    [o, r, t, l]
  );
  return (0, import_react3.useSyncExternalStore)(c, () => l.current);
}
var Lo = (e) => {
  const t = C(), n = M(), o = b();
  if (!o.comments)
    throw new Error(
      "ReactionBadge must be used inside a BlockNote editor with comments enabled"
    );
  const r = e.comment.reactions.find(
    (d) => d.emoji === e.emoji
  );
  if (!r)
    throw new Error(
      "Trying to render reaction badge for non-existing reaction"
    );
  const [l, c] = (0, import_react3.useState)([]), s = Ie2(o, l);
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Badge.Root,
    {
      className: Q("bn-badge", "bn-comment-reaction"),
      text: r.userIds.length.toString(),
      icon: r.emoji,
      isSelected: o.comments.threadStore.auth.canDeleteReaction(
        e.comment,
        r.emoji
      ),
      onClick: () => e.onReactionSelect(r.emoji),
      onMouseEnter: () => c(r.userIds),
      mainTooltip: n.comments.reactions.reacted_by,
      secondaryTooltip: `${Array.from(s.values()).map((d) => d.username).join(`
`)}`
    },
    r.emoji
  );
};
var Eo = ({
  comment: e,
  thread: t,
  showResolveButton: n
}) => {
  const o = M(), r = de(
    {
      initialContent: e.body,
      trailingBlock: false,
      dictionary: {
        ...o,
        placeholders: {
          emptyDocument: o.placeholders.edit_comment
        }
      },
      schema: Ee,
      sideMenuDetection: "editor"
    },
    [e.body]
  ), l = C(), [c, s] = (0, import_react3.useState)(false), d = b();
  if (!d.comments)
    throw new Error("Comments plugin not found");
  const a = d.comments.threadStore, u = (0, import_react3.useCallback)(() => {
    s(true);
  }, []), m = (0, import_react3.useCallback)(() => {
    r.replaceBlocks(r.document, e.body), s(false);
  }, [r, e.body]), f = (0, import_react3.useCallback)(
    async (R) => {
      await a.updateComment({
        commentId: e.id,
        comment: {
          body: r.document
        },
        threadId: t.id
      }), s(false);
    },
    [e, t.id, r, a]
  ), h = (0, import_react3.useCallback)(async () => {
    await a.deleteComment({
      commentId: e.id,
      threadId: t.id
    });
  }, [e, t.id, a]), H = (0, import_react3.useCallback)(
    async (R) => {
      a.auth.canAddReaction(e, R) ? await a.addReaction({
        threadId: t.id,
        commentId: e.id,
        emoji: R
      }) : a.auth.canDeleteReaction(e, R) && await a.deleteReaction({
        threadId: t.id,
        commentId: e.id,
        emoji: R
      });
    },
    [a, e, t.id]
  ), p = (0, import_react3.useCallback)(async () => {
    await a.resolveThread({
      threadId: t.id
    });
  }, [t.id, a]), V = (0, import_react3.useCallback)(async () => {
    await a.unresolveThread({
      threadId: t.id
    });
  }, [t.id, a]), B = To(d, e.userId);
  if (!e.body)
    return null;
  let S;
  const I = a.auth.canAddReaction(e), z = a.auth.canDeleteComment(e), j2 = a.auth.canUpdateComment(e), Z2 = n && (t.resolved ? a.auth.canUnresolveThread(t) : a.auth.canResolveThread(t));
  c || (S = (0, import_jsx_runtime.jsxs)(
    l.Generic.Toolbar.Root,
    {
      className: Q("bn-action-toolbar", "bn-comment-actions"),
      variant: "action-toolbar",
      children: [
        I && (0, import_jsx_runtime.jsx)(
          $e,
          {
            onEmojiSelect: (R) => H(R.native),
            children: (0, import_jsx_runtime.jsx)(
              l.Generic.Toolbar.Button,
              {
                mainTooltip: o.comments.actions.add_reaction,
                variant: "compact",
                children: (0, import_jsx_runtime.jsx)(We, { size: 16 })
              },
              "add-reaction"
            )
          }
        ),
        Z2 && (t.resolved ? (0, import_jsx_runtime.jsx)(
          l.Generic.Toolbar.Button,
          {
            mainTooltip: "Re-open",
            variant: "compact",
            onClick: V,
            children: (0, import_jsx_runtime.jsx)(Xn, { size: 16 })
          },
          "reopen"
        ) : (0, import_jsx_runtime.jsx)(
          l.Generic.Toolbar.Button,
          {
            mainTooltip: o.comments.actions.resolve,
            variant: "compact",
            onClick: p,
            children: (0, import_jsx_runtime.jsx)(wo, { size: 16 })
          },
          "resolve"
        )),
        (z || j2) && (0, import_jsx_runtime.jsxs)(l.Generic.Menu.Root, { position: "bottom-start", children: [
          (0, import_jsx_runtime.jsx)(l.Generic.Menu.Trigger, { children: (0, import_jsx_runtime.jsx)(
            l.Generic.Toolbar.Button,
            {
              mainTooltip: o.comments.actions.more_actions,
              variant: "compact",
              children: (0, import_jsx_runtime.jsx)(yo, { size: 16 })
            },
            "more-actions"
          ) }),
          (0, import_jsx_runtime.jsxs)(l.Generic.Menu.Dropdown, { className: "bn-menu-dropdown", children: [
            j2 && (0, import_jsx_runtime.jsx)(
              l.Generic.Menu.Item,
              {
                icon: (0, import_jsx_runtime.jsx)(Yn, {}),
                onClick: u,
                children: o.comments.actions.edit_comment
              },
              "edit-comment"
            ),
            z && (0, import_jsx_runtime.jsx)(
              l.Generic.Menu.Item,
              {
                icon: (0, import_jsx_runtime.jsx)(Ho, {}),
                onClick: h,
                children: o.comments.actions.delete_comment
              },
              "delete-comment"
            )
          ] })
        ] })
      ]
    }
  ));
  const $ = e.createdAt.toLocaleDateString(void 0, {
    month: "short",
    day: "numeric"
  });
  if (!e.body)
    throw new Error("soft deletes are not yet supported");
  return (0, import_jsx_runtime.jsx)(
    l.Comments.Comment,
    {
      authorInfo: B ?? "loading",
      timeString: $,
      edited: e.updatedAt.getTime() !== e.createdAt.getTime(),
      showActions: "hover",
      actions: S,
      className: "bn-thread-comment",
      children: (0, import_jsx_runtime.jsx)(
        Le,
        {
          autoFocus: c,
          editor: r,
          editable: c,
          actions: e.reactions.length > 0 || c ? ({ isEmpty: R }) => (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
            e.reactions.length > 0 && !c && (0, import_jsx_runtime.jsxs)(
              l.Generic.Badge.Group,
              {
                className: Q(
                  "bn-badge-group",
                  "bn-comment-reactions"
                ),
                children: [
                  e.reactions.map((F2) => (0, import_jsx_runtime.jsx)(
                    Lo,
                    {
                      comment: e,
                      emoji: F2.emoji,
                      onReactionSelect: H
                    },
                    F2.emoji
                  )),
                  (0, import_jsx_runtime.jsx)(
                    $e,
                    {
                      onEmojiSelect: (F2) => H(F2.native),
                      children: (0, import_jsx_runtime.jsx)(
                        l.Generic.Badge.Root,
                        {
                          className: Q(
                            "bn-badge",
                            "bn-comment-add-reaction"
                          ),
                          text: "+",
                          icon: (0, import_jsx_runtime.jsx)(We, { size: 16 }),
                          mainTooltip: o.comments.actions.add_reaction
                        }
                      )
                    }
                  )
                ]
              }
            ),
            c && (0, import_jsx_runtime.jsxs)(
              l.Generic.Toolbar.Root,
              {
                variant: "action-toolbar",
                className: Q(
                  "bn-action-toolbar",
                  "bn-comment-actions"
                ),
                children: [
                  (0, import_jsx_runtime.jsx)(
                    l.Generic.Toolbar.Button,
                    {
                      mainTooltip: "Save",
                      variant: "compact",
                      onClick: f,
                      isDisabled: R,
                      children: "Save"
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(
                    l.Generic.Toolbar.Button,
                    {
                      className: "bn-button",
                      mainTooltip: "Cancel",
                      variant: "compact",
                      onClick: m,
                      children: "Cancel"
                    }
                  )
                ]
              }
            )
          ] }) : void 0
        }
      )
    }
  );
};
var Ro = ({
  thread: e,
  maxCommentsBeforeCollapse: t
}) => {
  const n = C(), o = M(), r = b(), l = Ie2(r, e.resolvedBy ? [e.resolvedBy] : []), c = e.comments.map((s, d) => (0, import_jsx_runtime.jsx)(
    Eo,
    {
      thread: e,
      comment: s,
      showResolveButton: d === 0
    },
    s.id
  ));
  if (e.resolved && e.resolvedUpdatedAt && e.resolvedBy) {
    if (!l.get(e.resolvedBy))
      throw new Error(
        `User ${e.resolvedBy} resolved thread ${e.id}, but their data could not be found.`
      );
    const d = e.comments.findLastIndex(
      (a) => e.resolvedUpdatedAt.getTime() > a.createdAt.getTime()
    ) + 1;
    c.splice(
      d,
      0,
      (0, import_jsx_runtime.jsx)(
        n.Comments.Comment,
        {
          className: "bn-thread-comment",
          authorInfo: e.resolvedBy && l.get(e.resolvedBy) || "loading",
          timeString: e.resolvedUpdatedAt.toLocaleDateString(void 0, {
            month: "short",
            day: "numeric"
          }),
          edited: false,
          showActions: false,
          children: (0, import_jsx_runtime.jsx)("div", { className: "bn-resolved-text", children: o.comments.sidebar.marked_as_resolved })
        },
        "resolved-comment"
      )
    );
  }
  return t && c.length > t && c.splice(
    1,
    c.length - 2,
    (0, import_jsx_runtime.jsx)(
      n.Comments.ExpandSectionsPrompt,
      {
        className: "bn-thread-expand-prompt",
        children: o.comments.sidebar.more_replies(e.comments.length - 2)
      },
      "expand-prompt"
    )
  ), c;
};
var xt = ({
  thread: e,
  selected: t,
  referenceText: n,
  maxCommentsBeforeCollapse: o,
  onFocus: r,
  onBlur: l,
  tabIndex: c
}) => {
  const s = C(), d = M(), u = b().comments;
  if (!u)
    throw new Error("Comments plugin not found");
  const m = de({
    trailingBlock: false,
    dictionary: {
      ...d,
      placeholders: {
        emptyDocument: d.placeholders.comment_reply
      }
    },
    schema: Ee,
    sideMenuDetection: "editor"
  }), f = (0, import_react3.useCallback)(async () => {
    await u.threadStore.addComment({
      comment: {
        body: m.document
      },
      threadId: e.id
    }), m.removeBlocks(m.document);
  }, [u, m, e.id]);
  return (0, import_jsx_runtime.jsxs)(
    s.Comments.Card,
    {
      className: "bn-thread",
      headerText: n,
      onFocus: r,
      onBlur: l,
      selected: t,
      tabIndex: c,
      children: [
        (0, import_jsx_runtime.jsx)(s.Comments.CardSection, { className: "bn-thread-comments", children: (0, import_jsx_runtime.jsx)(
          Ro,
          {
            thread: e,
            maxCommentsBeforeCollapse: t ? void 0 : o || 5
          }
        ) }),
        t && (0, import_jsx_runtime.jsx)(s.Comments.CardSection, { className: "bn-thread-composer", children: (0, import_jsx_runtime.jsx)(
          Le,
          {
            autoFocus: false,
            editable: true,
            editor: m,
            actions: ({ isEmpty: h }) => h ? null : (0, import_jsx_runtime.jsx)(
              s.Generic.Toolbar.Root,
              {
                variant: "action-toolbar",
                className: Q(
                  "bn-action-toolbar",
                  "bn-comment-actions"
                ),
                children: (0, import_jsx_runtime.jsx)(
                  s.Generic.Toolbar.Button,
                  {
                    mainTooltip: "Save",
                    variant: "compact",
                    isDisabled: h,
                    onClick: f,
                    children: "Save"
                  }
                )
              }
            )
          }
        ) })
      ]
    }
  );
};
function yt(e) {
  const t = e.comments;
  if (!t)
    throw new Error("Comments plugin not found");
  const n = t.threadStore, o = (0, import_react3.useRef)();
  o.current || (o.current = n.getThreads());
  const r = (0, import_react3.useCallback)(
    (l) => n.subscribe((c) => {
      o.current = c, l();
    }),
    [n]
  );
  return (0, import_react3.useSyncExternalStore)(r, () => o.current);
}
var _o = (e) => {
  const t = b();
  if (!t.comments)
    throw new Error(
      "FloatingComposerController can only be used when BlockNote editor has enabled comments"
    );
  const n = D(
    t.comments.onUpdate.bind(t.comments)
  ), { isMounted: o, ref: r, style: l, getFloatingProps: c, setReference: s } = G(!!(n != null && n.selectedThreadId), null, 5e3, {
    placement: "bottom",
    middleware: [offset(10), shift(), flip()],
    onOpenChange: (m) => {
      var f;
      m || ((f = t.comments) == null || f.selectThread(void 0), t.focus());
    },
    ...e.floatingOptions
  }), d = (0, import_react3.useCallback)(() => {
    var f;
    if (!(n != null && n.selectedThreadId))
      return;
    const m = (f = t.domElement) == null ? void 0 : f.querySelector(
      `[data-bn-thread-id="${n == null ? void 0 : n.selectedThreadId}"]`
    );
    m && s(m);
  }, [s, t, n == null ? void 0 : n.selectedThreadId]);
  (0, import_react3.useEffect)(() => {
    if (n != null && n.selectedThreadId)
      return t.onChange(() => {
        d();
      });
  }, [t, d, n == null ? void 0 : n.selectedThreadId]), (0, import_react3.useLayoutEffect)(d, [d]);
  const a = yt(t);
  if (!o || !n || !n.selectedThreadId)
    return null;
  const u = e.floatingThread || xt;
  return (0, import_jsx_runtime.jsx)("div", { ref: r, style: l, ...c(), children: (0, import_jsx_runtime.jsx)(
    u,
    {
      thread: a.get(n.selectedThreadId),
      selected: true
    }
  ) });
};
var Io = (e) => {
  const t = C(), n = M(), { block: o } = e, r = b(), [l, c] = (0, import_react3.useState)(""), s = (0, import_react3.useCallback)(
    (u) => {
      c(u.currentTarget.value);
    },
    []
  ), d = (0, import_react3.useCallback)(
    (u) => {
      u.key === "Enter" && (u.preventDefault(), r.updateBlock(o, {
        props: {
          name: Pc(l),
          url: l
        }
      }));
    },
    [r, o, l]
  ), a = (0, import_react3.useCallback)(() => {
    r.updateBlock(o, {
      props: {
        name: Pc(l),
        url: l
      }
    });
  }, [r, o, l]);
  return (0, import_jsx_runtime.jsxs)(t.FilePanel.TabPanel, { className: "bn-tab-panel", children: [
    (0, import_jsx_runtime.jsx)(
      t.FilePanel.TextInput,
      {
        className: "bn-text-input",
        placeholder: n.file_panel.embed.url_placeholder,
        value: l,
        onChange: s,
        onKeyDown: d,
        "data-test": "embed-input"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      t.FilePanel.Button,
      {
        className: "bn-button",
        onClick: a,
        "data-test": "embed-input-button",
        children: n.file_panel.embed.embed_button[o.type] || n.file_panel.embed.embed_button.file
      }
    )
  ] });
};
var No = (e) => {
  var m;
  const t = C(), n = M(), { block: o, setLoading: r } = e, l = b(), [c, s] = (0, import_react3.useState)(false);
  (0, import_react3.useEffect)(() => {
    c && setTimeout(() => {
      s(false);
    }, 3e3);
  }, [c]);
  const d = (0, import_react3.useCallback)(
    (f) => {
      if (f === null)
        return;
      async function h(H) {
        if (r(true), l.uploadFile !== void 0)
          try {
            let p = await l.uploadFile(H, o.id);
            typeof p == "string" && (p = {
              props: {
                name: H.name,
                url: p
              }
            }), l.updateBlock(o, p);
          } catch {
            s(true);
          } finally {
            r(false);
          }
      }
      h(f);
    },
    [o, l, r]
  ), a = l.schema.blockSchema[o.type], u = a.isFileBlock && ((m = a.fileBlockAccept) != null && m.length) ? a.fileBlockAccept.join(",") : "*/*";
  return (0, import_jsx_runtime.jsxs)(t.FilePanel.TabPanel, { className: "bn-tab-panel", children: [
    (0, import_jsx_runtime.jsx)(
      t.FilePanel.FileInput,
      {
        className: "bn-file-input",
        "data-test": "upload-input",
        accept: u,
        placeholder: n.file_panel.upload.file_placeholder[o.type] || n.file_panel.upload.file_placeholder.file,
        value: null,
        onChange: d
      }
    ),
    c && (0, import_jsx_runtime.jsx)("div", { className: "bn-error-text", children: n.file_panel.upload.upload_error })
  ] });
};
var Bt = (e) => {
  const t = C(), n = M(), o = b(), [r, l] = (0, import_react3.useState)(false), c = e.tabs ?? [
    ...o.uploadFile !== void 0 ? [
      {
        name: n.file_panel.upload.title,
        tabPanel: (0, import_jsx_runtime.jsx)(No, { block: e.block, setLoading: l })
      }
    ] : [],
    {
      name: n.file_panel.embed.title,
      tabPanel: (0, import_jsx_runtime.jsx)(Io, { block: e.block })
    }
  ], [s, d] = (0, import_react3.useState)(
    e.defaultOpenTab || c[0].name
  );
  return (0, import_jsx_runtime.jsx)(
    t.FilePanel.Root,
    {
      className: "bn-panel",
      defaultOpenTab: s,
      openTab: s,
      setOpenTab: d,
      tabs: c,
      loading: r
    }
  );
};
var Po = (e) => {
  const t = b();
  if (!t.filePanel)
    throw new Error(
      "FileToolbarController can only be used when BlockNote editor schema contains file block"
    );
  const n = D(
    t.filePanel.onUpdate.bind(t.filePanel)
  ), { isMounted: o, ref: r, style: l, getFloatingProps: c } = G(
    (n == null ? void 0 : n.show) || false,
    (n == null ? void 0 : n.referencePos) || null,
    5e3,
    {
      placement: "bottom",
      middleware: [offset(10), flip()],
      onOpenChange: (m) => {
        m || (t.filePanel.closeMenu(), t.focus());
      },
      ...e.floatingOptions
    }
  );
  if (!o || !n)
    return null;
  const { show: s, referencePos: d, ...a } = n, u = e.filePanel || Bt;
  return (0, import_jsx_runtime.jsx)("div", { ref: r, style: l, ...c(), children: (0, import_jsx_runtime.jsx)(u, { ...a }) });
};
function U(e, t) {
  ue(e, t), se(e, t);
}
function Do(e) {
  return (t) => {
    e.forEach((n) => {
      typeof n == "function" ? n(t) : n != null && (n.current = t);
    });
  };
}
function _(e) {
  const t = O();
  if (e || (e = t == null ? void 0 : t.editor), !e)
    throw new Error(
      "'editor' is required, either from BlockNoteContext or as a function argument"
    );
  const n = e, [o, r] = (0, import_react3.useState)(() => {
    var l;
    return ((l = n.getSelection()) == null ? void 0 : l.blocks) || [n.getTextCursorPosition().block];
  });
  return U(
    () => {
      var l;
      return r(
        ((l = n.getSelection()) == null ? void 0 : l.blocks) || [n.getTextCursorPosition().block]
      );
    },
    n
  ), o;
}
var Oo = {
  bold: oo,
  italic: co,
  underline: ho,
  strike: mo,
  code: Jn
};
function Zo(e, t) {
  return e in t.schema.styleSchema && t.schema.styleSchema[e].type === e && t.schema.styleSchema[e].propSchema === "boolean";
}
var ne = (e) => {
  const t = M(), n = C(), o = b(), r = Zo(
    e.basicTextStyle,
    o
  ), l = _(o), [c, s] = (0, import_react3.useState)(
    e.basicTextStyle in o.getActiveStyles()
  );
  U(() => {
    r && s(e.basicTextStyle in o.getActiveStyles());
  }, o);
  const d = (m) => {
    if (o.focus(), !!r) {
      if (o.schema.styleSchema[m].propSchema !== "boolean")
        throw new Error("can only toggle boolean styles");
      o.toggleStyles({ [m]: true });
    }
  };
  if (!(0, import_react3.useMemo)(() => r ? !!l.find((m) => m.content !== void 0) : false, [r, l]) || !o.isEditable)
    return null;
  const u = Oo[e.basicTextStyle];
  return (0, import_jsx_runtime.jsx)(
    n.FormattingToolbar.Button,
    {
      className: "bn-button",
      "data-test": e.basicTextStyle,
      onClick: () => d(e.basicTextStyle),
      isSelected: c,
      label: t.formatting_toolbar[e.basicTextStyle].tooltip,
      mainTooltip: t.formatting_toolbar[e.basicTextStyle].tooltip,
      secondaryTooltip: Z(
        t.formatting_toolbar[e.basicTextStyle].secondary_tooltip,
        t.generic.ctrl_shortcut
      ),
      icon: (0, import_jsx_runtime.jsx)(u, {})
    }
  );
};
var we = (e) => {
  const t = e.textColor || "default", n = e.backgroundColor || "default", o = e.size || 16, r = (0, import_react3.useMemo)(
    () => ({
      pointerEvents: "none",
      fontSize: (o * 0.75).toString() + "px",
      height: o.toString() + "px",
      lineHeight: o.toString() + "px",
      textAlign: "center",
      width: o.toString() + "px"
    }),
    [o]
  );
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: "bn-color-icon",
      "data-background-color": n,
      "data-text-color": t,
      style: r,
      children: "A"
    }
  );
};
var qe = [
  "default",
  "gray",
  "brown",
  "red",
  "orange",
  "yellow",
  "green",
  "blue",
  "purple",
  "pink"
];
var me = (e) => {
  const t = C(), n = M();
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(() => e.text ? (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsx)(t.Generic.Menu.Label, { children: n.color_picker.text_title }),
      qe.map((l) => (0, import_jsx_runtime.jsx)(
        t.Generic.Menu.Item,
        {
          onClick: () => {
            e.onClick && e.onClick(), e.text.setColor(l);
          },
          "data-test": "text-color-" + l,
          icon: (0, import_jsx_runtime.jsx)(we, { textColor: l, size: e.iconSize }),
          checked: e.text.color === l,
          children: n.color_picker.colors[l]
        },
        "text-color-" + l
      ))
    ] }) : null, {}),
    (0, import_jsx_runtime.jsx)(() => e.background ? (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsx)(t.Generic.Menu.Label, { children: n.color_picker.background_title }),
      qe.map((l) => (0, import_jsx_runtime.jsx)(
        t.Generic.Menu.Item,
        {
          onClick: () => {
            e.onClick && e.onClick(), e.background.setColor(l);
          },
          "data-test": "background-color-" + l,
          icon: (0, import_jsx_runtime.jsx)(we, { backgroundColor: l, size: e.iconSize }),
          checked: e.background.color === l,
          children: n.color_picker.colors[l]
        },
        "background-color-" + l
      ))
    ] }) : null, {})
  ] });
};
function Ke(e, t) {
  return `${e}Color` in t.schema.styleSchema && t.schema.styleSchema[`${e}Color`].type === `${e}Color` && t.schema.styleSchema[`${e}Color`].propSchema === "string";
}
var Fo = () => {
  const e = C(), t = M(), n = b(), o = Ke("text", n), r = Ke("background", n), l = _(n), [c, s] = (0, import_react3.useState)(
    o && n.getActiveStyles().textColor || "default"
  ), [d, a] = (0, import_react3.useState)(
    r && n.getActiveStyles().backgroundColor || "default"
  );
  U(() => {
    o && s(n.getActiveStyles().textColor || "default"), r && a(
      n.getActiveStyles().backgroundColor || "default"
    );
  }, n);
  const u = (0, import_react3.useCallback)(
    (h) => {
      if (!o)
        throw Error(
          "Tried to set text color, but style does not exist in editor schema."
        );
      h === "default" ? n.removeStyles({ textColor: h }) : n.addStyles({ textColor: h }), setTimeout(() => {
        n.focus();
      });
    },
    [n, o]
  ), m = (0, import_react3.useCallback)(
    (h) => {
      if (!r)
        throw Error(
          "Tried to set background color, but style does not exist in editor schema."
        );
      h === "default" ? n.removeStyles({ backgroundColor: h }) : n.addStyles({ backgroundColor: h }), setTimeout(() => {
        n.focus();
      });
    },
    [r, n]
  );
  return !(0, import_react3.useMemo)(() => {
    if (!o && !r)
      return false;
    for (const h of l)
      if (h.content !== void 0)
        return true;
    return false;
  }, [r, l, o]) || !n.isEditable ? null : (0, import_jsx_runtime.jsxs)(e.Generic.Menu.Root, { children: [
    (0, import_jsx_runtime.jsx)(e.Generic.Menu.Trigger, { children: (0, import_jsx_runtime.jsx)(
      e.FormattingToolbar.Button,
      {
        className: "bn-button",
        "data-test": "colors",
        label: t.formatting_toolbar.colors.tooltip,
        mainTooltip: t.formatting_toolbar.colors.tooltip,
        icon: (0, import_jsx_runtime.jsx)(
          we,
          {
            textColor: c,
            backgroundColor: d,
            size: 20
          }
        )
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      e.Generic.Menu.Dropdown,
      {
        className: "bn-menu-dropdown bn-color-picker-dropdown",
        children: (0, import_jsx_runtime.jsx)(
          me,
          {
            text: o ? {
              color: c,
              setColor: u
            } : void 0,
            background: r ? {
              color: d,
              setColor: m
            } : void 0
          }
        )
      }
    )
  ] });
};
var Xe = (e) => {
  for (const t of ua)
    if (e.startsWith(t))
      return e;
  return `${pa}://${e}`;
};
var Vt2 = (e) => {
  const t = C(), n = M(), { url: o, text: r, editLink: l, showTextField: c } = e, [s, d] = (0, import_react3.useState)(o), [a, u] = (0, import_react3.useState)(r);
  (0, import_react3.useEffect)(() => {
    d(o), u(r);
  }, [r, o]);
  const m = (0, import_react3.useCallback)(
    (p) => {
      p.key === "Enter" && (p.preventDefault(), l(Xe(s), a));
    },
    [l, s, a]
  ), f = (0, import_react3.useCallback)(
    (p) => d(p.currentTarget.value),
    []
  ), h = (0, import_react3.useCallback)(
    (p) => u(p.currentTarget.value),
    []
  ), H = (0, import_react3.useCallback)(
    () => l(Xe(s), a),
    [l, s, a]
  );
  return (0, import_jsx_runtime.jsxs)(t.Generic.Form.Root, { children: [
    (0, import_jsx_runtime.jsx)(
      t.Generic.Form.TextInput,
      {
        className: "bn-text-input",
        name: "url",
        icon: (0, import_jsx_runtime.jsx)(Ct, {}),
        autoFocus: true,
        placeholder: n.link_toolbar.form.url_placeholder,
        value: s,
        onKeyDown: m,
        onChange: f,
        onSubmit: H
      }
    ),
    c !== false && (0, import_jsx_runtime.jsx)(
      t.Generic.Form.TextInput,
      {
        className: "bn-text-input",
        name: "title",
        icon: (0, import_jsx_runtime.jsx)(_e, {}),
        placeholder: n.link_toolbar.form.title_placeholder,
        value: a,
        onKeyDown: m,
        onChange: h,
        onSubmit: H
      }
    )
  ] });
};
function Ao(e) {
  return "link" in e.schema.inlineContentSchema && e.schema.inlineContentSchema.link === "link";
}
var Go = () => {
  var H;
  const e = b(), t = C(), n = M(), o = Ao(e), r = _(e), [l, c] = (0, import_react3.useState)(false), [s, d] = (0, import_react3.useState)(e.getSelectedLinkUrl() || ""), [a, u] = (0, import_react3.useState)(e.getSelectedText());
  U(() => {
    u(e.getSelectedText() || ""), d(e.getSelectedLinkUrl() || "");
  }, e), (0, import_react3.useEffect)(() => {
    var V;
    const p = (B) => {
      (B.ctrlKey || B.metaKey) && B.key === "k" && (c(true), B.preventDefault());
    };
    return (V = e.prosemirrorView) == null || V.dom.addEventListener("keydown", p), () => {
      var B;
      (B = e.prosemirrorView) == null || B.dom.removeEventListener("keydown", p);
    };
  }, [(H = e.prosemirrorView) == null ? void 0 : H.dom]);
  const m = (0, import_react3.useCallback)(
    (p) => {
      e.createLink(p), e.focus();
    },
    [e]
  ), f = e.transact(
    (p) => Kt(p.selection)
  );
  return !(0, import_react3.useMemo)(() => {
    if (!o)
      return false;
    for (const p of r)
      if (p.content === void 0)
        return false;
    return !f;
  }, [o, r, f]) || !("link" in e.schema.inlineContentSchema) || !e.isEditable ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Popover.Root, { opened: l, children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      t.FormattingToolbar.Button,
      {
        className: "bn-button",
        "data-test": "createLink",
        label: n.formatting_toolbar.link.tooltip,
        mainTooltip: n.formatting_toolbar.link.tooltip,
        secondaryTooltip: Z(
          n.formatting_toolbar.link.secondary_tooltip,
          n.generic.ctrl_shortcut
        ),
        icon: (0, import_jsx_runtime.jsx)(Ct, {}),
        onClick: () => c(true)
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Popover.Content,
      {
        className: "bn-popover-content bn-form-popover",
        variant: "form-popover",
        children: (0, import_jsx_runtime.jsx)(
          Vt2,
          {
            url: s,
            text: a,
            editLink: m,
            showTextField: false
          }
        )
      }
    )
  ] });
};
var Uo = () => {
  const e = M(), t = C(), n = b(), [o, r] = (0, import_react3.useState)(), l = _(n), c = (0, import_react3.useMemo)(() => {
    if (l.length !== 1)
      return;
    const a = l[0];
    if (Hc(a, n))
      return r(a.props.caption), a;
  }, [n, l]), s = (0, import_react3.useCallback)(
    (a) => {
      c && a.key === "Enter" && (a.preventDefault(), n.updateBlock(c, {
        props: {
          caption: o
          // TODO
        }
      }));
    },
    [o, n, c]
  ), d = (0, import_react3.useCallback)(
    (a) => r(a.currentTarget.value),
    []
  );
  return !c || Oc(c, n) || !n.isEditable ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Popover.Root, { children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      t.FormattingToolbar.Button,
      {
        className: "bn-button",
        label: e.formatting_toolbar.file_caption.tooltip,
        mainTooltip: e.formatting_toolbar.file_caption.tooltip,
        icon: (0, import_jsx_runtime.jsx)(je, {}),
        isSelected: c.props.caption !== ""
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Popover.Content,
      {
        className: "bn-popover-content bn-form-popover",
        variant: "form-popover",
        children: (0, import_jsx_runtime.jsx)(t.Generic.Form.Root, { children: (0, import_jsx_runtime.jsx)(
          t.Generic.Form.TextInput,
          {
            name: "file-caption",
            icon: (0, import_jsx_runtime.jsx)(je, {}),
            value: o || "",
            autoFocus: true,
            placeholder: e.formatting_toolbar.file_caption.input_placeholder,
            onKeyDown: s,
            onChange: d
          }
        ) })
      }
    )
  ] });
};
var zo = () => {
  const e = M(), t = C(), n = b(), o = _(n), r = (0, import_react3.useMemo)(() => {
    if (o.length !== 1)
      return;
    const c = o[0];
    if (Hc(c, n))
      return c;
  }, [n, o]), l = (0, import_react3.useCallback)(() => {
    n.focus(), n.removeBlocks([r]);
  }, [n, r]);
  return !r || Oc(r, n) || !n.isEditable ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      label: e.formatting_toolbar.file_delete.tooltip[r.type] || e.formatting_toolbar.file_delete.tooltip.file,
      mainTooltip: e.formatting_toolbar.file_delete.tooltip[r.type] || e.formatting_toolbar.file_delete.tooltip.file,
      icon: (0, import_jsx_runtime.jsx)(vo, {}),
      onClick: l
    }
  );
};
var jo = () => {
  const e = M(), t = C(), n = b(), [o, r] = (0, import_react3.useState)(), l = _(n), c = (0, import_react3.useMemo)(() => {
    if (l.length !== 1)
      return;
    const a = l[0];
    if (Hc(a, n))
      return r(a.props.name), a;
  }, [n, l]), s = (0, import_react3.useCallback)(
    (a) => {
      c && a.key === "Enter" && (a.preventDefault(), n.updateBlock(c, {
        props: {
          name: o
          // TODO
        }
      }));
    },
    [o, n, c]
  ), d = (0, import_react3.useCallback)(
    (a) => r(a.currentTarget.value),
    []
  );
  return !c || Oc(c, n) || !n.isEditable ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Popover.Root, { children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      t.FormattingToolbar.Button,
      {
        className: "bn-button",
        label: e.formatting_toolbar.file_rename.tooltip[c.type] || e.formatting_toolbar.file_rename.tooltip.file,
        mainTooltip: e.formatting_toolbar.file_rename.tooltip[c.type] || e.formatting_toolbar.file_rename.tooltip.file,
        icon: (0, import_jsx_runtime.jsx)(ze, {})
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Popover.Content,
      {
        className: "bn-popover-content bn-form-popover",
        variant: "form-popover",
        children: (0, import_jsx_runtime.jsx)(t.Generic.Form.Root, { children: (0, import_jsx_runtime.jsx)(
          t.Generic.Form.TextInput,
          {
            name: "file-name",
            icon: (0, import_jsx_runtime.jsx)(ze, {}),
            value: o || "",
            autoFocus: true,
            placeholder: e.formatting_toolbar.file_rename.input_placeholder[c.type] || e.formatting_toolbar.file_rename.input_placeholder.file,
            onKeyDown: s,
            onChange: d
          }
        ) })
      }
    )
  ] });
};
var Wo = () => {
  const e = M(), t = C(), n = b(), o = _(n), [r, l] = (0, import_react3.useState)(false);
  (0, import_react3.useEffect)(() => {
    l(false);
  }, [o]);
  const c = o.length === 1 ? o[0] : void 0;
  return c === void 0 || !Hc(c, n) || !n.isEditable ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Popover.Root, { opened: r, position: "bottom", children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      t.FormattingToolbar.Button,
      {
        className: "bn-button",
        onClick: () => l(!r),
        isSelected: r,
        mainTooltip: e.formatting_toolbar.file_replace.tooltip[c.type] || e.formatting_toolbar.file_replace.tooltip.file,
        label: e.formatting_toolbar.file_replace.tooltip[c.type] || e.formatting_toolbar.file_replace.tooltip.file,
        icon: (0, import_jsx_runtime.jsx)(Co, {})
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Popover.Content,
      {
        className: "bn-popover-content bn-panel-popover",
        variant: "panel-popover",
        children: (0, import_jsx_runtime.jsx)(Bt, { block: c })
      }
    )
  ] });
};
var $o = () => {
  const e = M(), t = C(), n = b(), o = _(n), [r, l] = (0, import_react3.useState)(
    () => n.canNestBlock()
  );
  U(() => {
    l(n.canNestBlock());
  }, n);
  const c = (0, import_react3.useCallback)(() => {
    n.focus(), n.nestBlock();
  }, [n]);
  return !(0, import_react3.useMemo)(() => !o.find(
    (d) => n.schema.blockSchema[d.type].content !== "inline"
  ), [n.schema.blockSchema, o]) || !n.isEditable ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      "data-test": "nestBlock",
      onClick: c,
      isDisabled: !r,
      label: e.formatting_toolbar.nest.tooltip,
      mainTooltip: e.formatting_toolbar.nest.tooltip,
      secondaryTooltip: Z(
        e.formatting_toolbar.nest.secondary_tooltip,
        e.generic.ctrl_shortcut
      ),
      icon: (0, import_jsx_runtime.jsx)(lo, {})
    }
  );
};
var qo = () => {
  const e = M(), t = C(), n = b(), o = _(n), [r, l] = (0, import_react3.useState)(
    () => n.canUnnestBlock()
  );
  U(() => {
    l(n.canUnnestBlock());
  }, n);
  const c = (0, import_react3.useCallback)(() => {
    n.focus(), n.unnestBlock();
  }, [n]);
  return !(0, import_react3.useMemo)(() => !o.find(
    (d) => n.schema.blockSchema[d.type].content !== "inline"
  ), [n.schema.blockSchema, o]) || !n.isEditable ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      "data-test": "unnestBlock",
      onClick: c,
      isDisabled: !r,
      label: e.formatting_toolbar.unnest.tooltip,
      mainTooltip: e.formatting_toolbar.unnest.tooltip,
      secondaryTooltip: Z(
        e.formatting_toolbar.unnest.secondary_tooltip,
        e.generic.ctrl_shortcut
      ),
      icon: (0, import_jsx_runtime.jsx)(io, {})
    }
  );
};
var Ko = (e) => [
  {
    name: e.slash_menu.paragraph.title,
    type: "paragraph",
    icon: _e,
    isSelected: (t) => t.type === "paragraph"
  },
  {
    name: e.slash_menu.heading.title,
    type: "heading",
    props: { level: 1 },
    icon: ht,
    isSelected: (t) => t.type === "heading" && "level" in t.props && t.props.level === 1
  },
  {
    name: e.slash_menu.heading_2.title,
    type: "heading",
    props: { level: 2 },
    icon: gt,
    isSelected: (t) => t.type === "heading" && "level" in t.props && t.props.level === 2
  },
  {
    name: e.slash_menu.heading_3.title,
    type: "heading",
    props: { level: 3 },
    icon: bt2,
    isSelected: (t) => t.type === "heading" && "level" in t.props && t.props.level === 3
  },
  {
    name: e.slash_menu.quote.title,
    type: "quote",
    icon: vt,
    isSelected: (t) => t.type === "quote"
  },
  {
    name: e.slash_menu.bullet_list.title,
    type: "bulletListItem",
    icon: wt,
    isSelected: (t) => t.type === "bulletListItem"
  },
  {
    name: e.slash_menu.numbered_list.title,
    type: "numberedListItem",
    icon: kt,
    isSelected: (t) => t.type === "numberedListItem"
  },
  {
    name: e.slash_menu.check_list.title,
    type: "checkListItem",
    icon: pt,
    isSelected: (t) => t.type === "checkListItem"
  }
];
var Xo = (e) => {
  const t = C(), n = M(), o = b(), r = _(o), [l, c] = (0, import_react3.useState)(o.getTextCursorPosition().block), s = (0, import_react3.useMemo)(() => (e.items || Ko(n)).filter(
    (u) => u.type in o.schema.blockSchema
  ), [o, n, e.items]), d = (0, import_react3.useMemo)(
    () => s.find((u) => u.type === l.type) !== void 0,
    [l.type, s]
  ), a = (0, import_react3.useMemo)(() => {
    const u = (m) => {
      o.focus(), o.transact(() => {
        for (const f of r)
          o.updateBlock(f, {
            type: m.type,
            props: m.props
          });
      });
    };
    return s.map((m) => {
      const f = m.icon;
      return {
        text: m.name,
        icon: (0, import_jsx_runtime.jsx)(f, { size: 16 }),
        onClick: () => u(m),
        isSelected: m.isSelected(l)
      };
    });
  }, [l, s, o, r]);
  return U(() => {
    c(o.getTextCursorPosition().block);
  }, o), !d || !o.isEditable ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Select,
    {
      className: "bn-select",
      items: a
    }
  );
};
var Yo = () => {
  const e = M(), t = C(), n = b(), o = (0, import_react3.useCallback)(() => {
    var r;
    (r = n.comments) == null || r.startPendingComment(), n.formattingToolbar.closeMenu();
  }, [n]);
  return n.comments ? (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      label: e.formatting_toolbar.comment.tooltip,
      mainTooltip: e.formatting_toolbar.comment.tooltip,
      icon: (0, import_jsx_runtime.jsx)(ft, {}),
      onClick: o
    }
  ) : null;
};
var Jo = () => {
  const e = M(), t = C(), n = b(), o = (0, import_react3.useCallback)(() => {
    n._tiptapEditor.chain().focus().addPendingComment().run();
  }, [n]);
  return (
    // We manually check if a comment extension (like liveblocks) is installed
    // By adding default support for this, the user doesn't need to customize the formatting toolbar
    !n._tiptapEditor.commands.addPendingComment || !n.isEditable ? null : (0, import_jsx_runtime.jsx)(
      t.FormattingToolbar.Button,
      {
        className: "bn-button",
        label: e.formatting_toolbar.comment.tooltip,
        mainTooltip: e.formatting_toolbar.comment.tooltip,
        icon: (0, import_jsx_runtime.jsx)(ft, {}),
        onClick: o
      }
    )
  );
};
function ve2(e, t) {
  try {
    const n = new URL(e, t);
    if (n.protocol !== "javascript:")
      return n.href;
  } catch {
  }
  return "#";
}
var Qo = () => {
  const e = M(), t = C(), n = b(), o = _(n), r = (0, import_react3.useMemo)(() => {
    if (o.length !== 1)
      return;
    const c = o[0];
    if (Hc(c, n))
      return c;
  }, [n, o]), l = (0, import_react3.useCallback)(() => {
    r && r.props.url && (n.focus(), n.resolveFileUrl ? n.resolveFileUrl(r.props.url).then(
      (c) => window.open(ve2(c, window.location.href))
    ) : window.open(ve2(r.props.url, window.location.href)));
  }, [n, r]);
  return !r || Oc(r, n) ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      label: e.formatting_toolbar.file_download.tooltip[r.type] || e.formatting_toolbar.file_download.tooltip.file,
      mainTooltip: e.formatting_toolbar.file_download.tooltip[r.type] || e.formatting_toolbar.file_download.tooltip.file,
      icon: (0, import_jsx_runtime.jsx)(Mo, {}),
      onClick: l
    }
  );
};
var er = () => {
  const e = M(), t = C(), n = b(), o = _(n), r = (0, import_react3.useMemo)(() => {
    if (o.length !== 1)
      return;
    const c = o[0];
    if (Dc(c, n))
      return c;
  }, [n, o]), l = (0, import_react3.useCallback)(() => {
    r && n.updateBlock(r, {
      props: {
        showPreview: !r.props.showPreview
        // TODO
      }
    });
  }, [n, r]);
  return !r || Oc(r, n) || !n.isEditable ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      label: "Toggle preview",
      mainTooltip: e.formatting_toolbar.file_preview_toggle.tooltip,
      icon: (0, import_jsx_runtime.jsx)(bo, {}),
      isSelected: r.props.showPreview,
      onClick: l
    }
  );
};
var tr = () => {
  const e = M(), t = C(), n = b(), o = _(n), r = (0, import_react3.useMemo)(() => {
    var s;
    if (o.length !== 1)
      return;
    const c = o[0];
    if (c.type === "table")
      return (s = n.tableHandles) == null ? void 0 : s.getMergeDirection(c);
  }, [n, o]), l = (0, import_react3.useCallback)(() => {
    var c;
    (c = n.tableHandles) == null || c.mergeCells();
  }, [n]);
  return !n.isEditable || r === void 0 || !n.settings.tables.splitCells ? null : (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      label: e.formatting_toolbar.table_cell_merge.tooltip,
      mainTooltip: e.formatting_toolbar.table_cell_merge.tooltip,
      icon: r === "horizontal" ? (0, import_jsx_runtime.jsx)(so, {}) : (0, import_jsx_runtime.jsx)(uo, {}),
      onClick: l
    }
  );
};
var nr = {
  left: to,
  center: Qn,
  right: no,
  justify: eo
};
var be = (e) => {
  const t = C(), n = M(), o = b(), r = _(o), l = (0, import_react3.useMemo)(() => {
    var u;
    const a = r[0];
    if (Rc("textAlignment", a, o))
      return a.props.textAlignment;
    if (a.type === "table") {
      const m = (u = o.tableHandles) == null ? void 0 : u.getCellSelection();
      if (!m)
        return;
      const f = m.cells.map(
        ({ row: H, col: p }) => rt(
          a.content.rows[H].cells[p]
        ).props.textAlignment
      ), h = f[0];
      if (f.every((H) => H === h))
        return h;
    }
  }, [o, r]), c = (0, import_react3.useCallback)(
    (a) => {
      var u;
      o.focus();
      for (const m of r)
        if (js("textAlignment", m.type, o))
          o.updateBlock(m, {
            props: { textAlignment: a }
          });
        else if (m.type === "table") {
          const f = (u = o.tableHandles) == null ? void 0 : u.getCellSelection();
          if (!f)
            continue;
          const h = m.content.rows.map(
            (H) => ({
              ...H,
              cells: H.cells.map((p) => rt(p))
            })
          );
          f.cells.forEach(({ row: H, col: p }) => {
            h[H].cells[p].props.textAlignment = a;
          }), o.updateBlock(m, {
            type: "table",
            content: {
              ...m.content,
              type: "tableContent",
              rows: h
            }
          }), o.setTextCursorPosition(m);
        }
    },
    [o, r]
  );
  if (!(0, import_react3.useMemo)(() => !!r.find(
    (a) => "textAlignment" in a.props || a.type === "table" && a.children
  ), [r]) || !o.isEditable)
    return null;
  const d = nr[e.textAlignment];
  return (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Button,
    {
      className: "bn-button",
      "data-test": `alignText${e.textAlignment.slice(0, 1).toUpperCase() + e.textAlignment.slice(1)}`,
      onClick: () => c(e.textAlignment),
      isSelected: l === e.textAlignment,
      label: n.formatting_toolbar[`align_${e.textAlignment}`].tooltip,
      mainTooltip: n.formatting_toolbar[`align_${e.textAlignment}`].tooltip,
      icon: (0, import_jsx_runtime.jsx)(d, {})
    }
  );
};
var or = (e) => [
  (0, import_jsx_runtime.jsx)(Xo, { items: e }, "blockTypeSelect"),
  (0, import_jsx_runtime.jsx)(tr, {}, "tableCellMergeButton"),
  (0, import_jsx_runtime.jsx)(Uo, {}, "fileCaptionButton"),
  (0, import_jsx_runtime.jsx)(Wo, {}, "replaceFileButton"),
  (0, import_jsx_runtime.jsx)(jo, {}, "fileRenameButton"),
  (0, import_jsx_runtime.jsx)(zo, {}, "fileDeleteButton"),
  (0, import_jsx_runtime.jsx)(Qo, {}, "fileDownloadButton"),
  (0, import_jsx_runtime.jsx)(er, {}, "filePreviewButton"),
  (0, import_jsx_runtime.jsx)(ne, { basicTextStyle: "bold" }, "boldStyleButton"),
  (0, import_jsx_runtime.jsx)(ne, { basicTextStyle: "italic" }, "italicStyleButton"),
  (0, import_jsx_runtime.jsx)(
    ne,
    {
      basicTextStyle: "underline"
    },
    "underlineStyleButton"
  ),
  (0, import_jsx_runtime.jsx)(ne, { basicTextStyle: "strike" }, "strikeStyleButton"),
  (0, import_jsx_runtime.jsx)(be, { textAlignment: "left" }, "textAlignLeftButton"),
  (0, import_jsx_runtime.jsx)(be, { textAlignment: "center" }, "textAlignCenterButton"),
  (0, import_jsx_runtime.jsx)(be, { textAlignment: "right" }, "textAlignRightButton"),
  (0, import_jsx_runtime.jsx)(Fo, {}, "colorStyleButton"),
  (0, import_jsx_runtime.jsx)($o, {}, "nestBlockButton"),
  (0, import_jsx_runtime.jsx)(qo, {}, "unnestBlockButton"),
  (0, import_jsx_runtime.jsx)(Go, {}, "createLinkButton"),
  (0, import_jsx_runtime.jsx)(Yo, {}, "addCommentButton"),
  (0, import_jsx_runtime.jsx)(Jo, {}, "addTiptapCommentButton")
];
var St = (e) => {
  const t = C();
  return (0, import_jsx_runtime.jsx)(
    t.FormattingToolbar.Root,
    {
      className: "bn-toolbar bn-formatting-toolbar",
      children: e.children || or(e.blockTypeSelectItems)
    }
  );
};
var Ye = (e) => {
  switch (e) {
    case "left":
      return "top-start";
    case "center":
      return "top";
    case "right":
      return "top-end";
    default:
      return "top-start";
  }
};
var rr = (e) => {
  const t = (0, import_react3.useRef)(null), n = b(), [o, r] = (0, import_react3.useState)(
    () => {
      const f = n.getTextCursorPosition().block;
      return "textAlignment" in f.props ? Ye(
        f.props.textAlignment
      ) : "top-start";
    }
  );
  U(() => {
    const f = n.getTextCursorPosition().block;
    "textAlignment" in f.props ? r(
      Ye(
        f.props.textAlignment
      )
    ) : r("top-start");
  }, n);
  const l = D(
    n.formattingToolbar.onUpdate.bind(n.formattingToolbar)
  ), { isMounted: c, ref: s, style: d, getFloatingProps: a } = G(
    (l == null ? void 0 : l.show) || false,
    (l == null ? void 0 : l.referencePos) || null,
    3e3,
    {
      placement: o,
      middleware: [offset(10), shift(), flip()],
      onOpenChange: (f, h) => {
        f || (n.formattingToolbar.closeMenu(), n.focus());
      },
      ...e.floatingOptions
    }
  ), u = (0, import_react3.useMemo)(() => Do([t, s]), [t, s]);
  if (!c || !l)
    return null;
  if (!l.show && t.current)
    return (0, import_jsx_runtime.jsx)(
      "div",
      {
        ref: u,
        style: d,
        dangerouslySetInnerHTML: { __html: t.current.innerHTML }
      }
    );
  const m = e.formattingToolbar || St;
  return (0, import_jsx_runtime.jsx)("div", { ref: u, style: d, ...a(), children: (0, import_jsx_runtime.jsx)(m, {}) });
};
var ir2 = (e) => {
  const t = C(), n = M();
  return (0, import_jsx_runtime.jsx)(
    t.LinkToolbar.Button,
    {
      className: "bn-button",
      label: n.link_toolbar.delete.tooltip,
      mainTooltip: n.link_toolbar.delete.tooltip,
      isSelected: false,
      onClick: e.deleteLink,
      icon: (0, import_jsx_runtime.jsx)(ao, {})
    }
  );
};
var lr = (e) => {
  const t = C(), n = M();
  return (0, import_jsx_runtime.jsxs)(t.Generic.Popover.Root, { children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      t.LinkToolbar.Button,
      {
        className: "bn-button",
        mainTooltip: n.link_toolbar.edit.tooltip,
        isSelected: false,
        children: n.link_toolbar.edit.text
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Popover.Content,
      {
        className: "bn-popover-content bn-form-popover",
        variant: "form-popover",
        children: (0, import_jsx_runtime.jsx)(Vt2, { ...e })
      }
    )
  ] });
};
var cr2 = (e) => {
  const t = C(), n = M();
  return (0, import_jsx_runtime.jsx)(
    t.LinkToolbar.Button,
    {
      className: "bn-button",
      mainTooltip: n.link_toolbar.open.tooltip,
      label: n.link_toolbar.open.tooltip,
      isSelected: false,
      onClick: () => {
        window.open(ve2(e.url, window.location.href), "_blank");
      },
      icon: (0, import_jsx_runtime.jsx)(xo, {})
    }
  );
};
var ar2 = (e) => {
  const t = C();
  return e.children ? (0, import_jsx_runtime.jsx)(t.LinkToolbar.Root, { className: "bn-toolbar bn-link-toolbar", children: e.children }) : (0, import_jsx_runtime.jsxs)(
    t.LinkToolbar.Root,
    {
      className: "bn-toolbar bn-link-toolbar",
      onMouseEnter: e.stopHideTimer,
      onMouseLeave: e.startHideTimer,
      children: [
        (0, import_jsx_runtime.jsx)(
          lr,
          {
            url: e.url,
            text: e.text,
            editLink: e.editLink
          }
        ),
        (0, import_jsx_runtime.jsx)(cr2, { url: e.url }),
        (0, import_jsx_runtime.jsx)(ir2, { deleteLink: e.deleteLink })
      ]
    }
  );
};
var sr = (e) => {
  const t = b(), n = {
    deleteLink: t.linkToolbar.deleteLink,
    editLink: t.linkToolbar.editLink,
    startHideTimer: t.linkToolbar.startHideTimer,
    stopHideTimer: t.linkToolbar.stopHideTimer
  }, o = D(
    t.linkToolbar.onUpdate.bind(t.linkToolbar)
  ), { isMounted: r, ref: l, style: c, getFloatingProps: s } = G(
    (o == null ? void 0 : o.show) || false,
    (o == null ? void 0 : o.referencePos) || null,
    4e3,
    {
      placement: "top-start",
      middleware: [offset(10), flip()],
      onOpenChange: (f) => {
        f || (t.linkToolbar.closeMenu(), t.focus());
      },
      ...e.floatingOptions
    }
  );
  if (!r || !o)
    return null;
  const { show: d, referencePos: a, ...u } = o, m = e.linkToolbar || ar2;
  return (0, import_jsx_runtime.jsx)("div", { ref: l, style: c, ...s(), children: (0, import_jsx_runtime.jsx)(m, { ...u, ...n }) });
};
function dr(e) {
  return k({ attr: { viewBox: "0 0 1024 1024" }, child: [{ tag: "path", attr: { d: "M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8Z" }, child: [] }, { tag: "path", attr: { d: "M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8Z" }, child: [] }] })(e);
}
var ur2 = (e) => {
  const t = C(), n = M(), o = b(), r = (0, import_react3.useCallback)(() => {
    const l = e.block.content;
    if (l !== void 0 && Array.isArray(l) && l.length === 0)
      o.setTextCursorPosition(e.block), o.openSuggestionMenu("/");
    else {
      const s = o.insertBlocks(
        [{ type: "paragraph" }],
        e.block,
        "after"
      )[0];
      o.setTextCursorPosition(s), o.openSuggestionMenu("/");
    }
  }, [o, e.block]);
  return (0, import_jsx_runtime.jsx)(
    t.SideMenu.Button,
    {
      className: "bn-button",
      label: n.side_menu.add_block_label,
      icon: (0, import_jsx_runtime.jsx)(dr, { size: 24, onClick: r, "data-test": "dragHandleAdd" })
    }
  );
};
function Tt(e) {
  return k({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { fill: "none", d: "M0 0h24v24H0V0z" }, child: [] }, { tag: "path", attr: { d: "M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" }, child: [] }] })(e);
}
function mr2(e) {
  return k({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { fill: "none", d: "M0 0h24v24H0z" }, child: [] }, { tag: "path", attr: { d: "m7 10 5 5 5-5z" }, child: [] }] })(e);
}
var fr2 = (e) => {
  const t = C(), n = b();
  return !js("textColor", e.block.type, n) && !js("backgroundColor", e.block.type, n) ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Menu.Root, { position: "right", sub: true, children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { sub: true, children: (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Item,
      {
        className: "bn-menu-item",
        subTrigger: true,
        children: e.children
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Dropdown,
      {
        sub: true,
        className: "bn-menu-dropdown bn-color-picker-dropdown",
        children: (0, import_jsx_runtime.jsx)(
          me,
          {
            iconSize: 18,
            text: js(
              "textColor",
              e.block.type,
              n
            ) && Rc("textColor", e.block, n) ? {
              color: e.block.props.textColor,
              setColor: (o) => n.updateBlock(e.block, {
                type: e.block.type,
                props: { textColor: o }
              })
            } : void 0,
            background: js(
              "backgroundColor",
              e.block.type,
              n
            ) && Rc("backgroundColor", e.block, n) ? {
              color: e.block.props.backgroundColor,
              setColor: (o) => n.updateBlock(e.block, {
                props: { backgroundColor: o }
              })
            } : void 0
          }
        )
      }
    )
  ] });
};
var hr = (e) => {
  const t = C(), n = b();
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      className: "bn-menu-item",
      onClick: () => n.removeBlocks([e.block]),
      children: e.children
    }
  );
};
var gr2 = (e) => {
  const t = C(), n = b();
  if (e.block.type !== "table" || !n.settings.tables.headers)
    return null;
  const o = !!e.block.content.headerRows;
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      className: "bn-menu-item",
      checked: o,
      onClick: () => {
        const r = n.getBlock(e.block.id);
        r && n.updateBlock(r, {
          ...r,
          content: {
            ...r.content,
            headerRows: o ? void 0 : 1
          }
        });
      },
      children: e.children
    }
  );
};
var br2 = (e) => {
  const t = C(), n = b();
  if (e.block.type !== "table" || !n.settings.tables.headers)
    return null;
  const o = !!e.block.content.headerCols;
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      className: "bn-menu-item",
      checked: o,
      onClick: () => {
        n.updateBlock(e.block, {
          type: "table",
          content: {
            ...e.block.content,
            type: "tableContent",
            headerCols: o ? void 0 : 1
          }
        });
      },
      children: e.children
    }
  );
};
var Cr = (e) => {
  const t = C(), n = M();
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Dropdown,
    {
      className: "bn-menu-dropdown bn-drag-handle-menu",
      children: e.children || (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
        (0, import_jsx_runtime.jsx)(hr, { ...e, children: n.drag_handle.delete_menuitem }),
        (0, import_jsx_runtime.jsx)(fr2, { ...e, children: n.drag_handle.colors_menuitem }),
        (0, import_jsx_runtime.jsx)(gr2, { ...e, children: n.drag_handle.header_row_menuitem }),
        (0, import_jsx_runtime.jsx)(br2, { ...e, children: n.drag_handle.header_column_menuitem })
      ] })
    }
  );
};
var pr2 = (e) => {
  const t = C(), n = M(), o = e.dragHandleMenu || Cr;
  return (0, import_jsx_runtime.jsxs)(
    t.Generic.Menu.Root,
    {
      onOpenChange: (r) => {
        r ? e.freezeMenu() : e.unfreezeMenu();
      },
      position: "left",
      children: [
        (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { children: (0, import_jsx_runtime.jsx)(
          t.SideMenu.Button,
          {
            label: n.side_menu.drag_handle_label,
            draggable: true,
            onDragStart: (r) => e.blockDragStart(r, e.block),
            onDragEnd: e.blockDragEnd,
            className: "bn-button",
            icon: (0, import_jsx_runtime.jsx)(Tt, { size: 24, "data-test": "dragHandle" })
          }
        ) }),
        (0, import_jsx_runtime.jsx)(o, { block: e.block, children: e.children })
      ]
    }
  );
};
var kr = (e) => {
  const t = C(), n = (0, import_react3.useMemo)(() => {
    const o = {
      "data-block-type": e.block.type
    };
    return e.block.type === "heading" && (o["data-level"] = e.block.props.level.toString()), e.editor.schema.blockSchema[e.block.type].isFileBlock && (e.block.props.url ? o["data-url"] = "true" : o["data-url"] = "false"), o;
  }, [e.block, e.editor.schema.blockSchema]);
  return (0, import_jsx_runtime.jsx)(t.SideMenu.Root, { className: "bn-side-menu", ...n, children: e.children || (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(ur2, { ...e }),
    (0, import_jsx_runtime.jsx)(pr2, { ...e })
  ] }) });
};
var wr = (e) => {
  const t = b(), n = {
    blockDragStart: t.sideMenu.blockDragStart,
    blockDragEnd: t.sideMenu.blockDragEnd,
    freezeMenu: t.sideMenu.freezeMenu,
    unfreezeMenu: t.sideMenu.unfreezeMenu
  }, o = D(
    t.sideMenu.onUpdate.bind(t.sideMenu)
  ), { isMounted: r, ref: l, style: c, getFloatingProps: s } = G(
    (o == null ? void 0 : o.show) || false,
    (o == null ? void 0 : o.referencePos) || null,
    1e3,
    {
      placement: "left-start",
      ...e.floatingOptions
    }
  );
  if (!r || !o)
    return null;
  const { show: d, referencePos: a, ...u } = o, m = e.sideMenu || kr;
  return (0, import_jsx_runtime.jsx)("div", { ref: l, style: c, ...s(), children: (0, import_jsx_runtime.jsx)(m, { ...u, ...n, editor: t }) });
};
async function vr(e, t) {
  return (await Kc(e, t)).map(
    ({ id: n, onItemClick: o }) => ({
      id: n,
      onItemClick: o,
      icon: n
    })
  );
}
function Hr(e) {
  const t = C(), n = M(), { items: o, loadingState: r, selectedIndex: l, onItemClick: c, columns: s } = e, d = r === "loading-initial" || r === "loading" ? (0, import_jsx_runtime.jsx)(
    t.GridSuggestionMenu.Loader,
    {
      className: "bn-grid-suggestion-menu-loader",
      columns: s
    }
  ) : null, a = (0, import_react3.useMemo)(() => {
    const u = [];
    for (let m = 0; m < o.length; m++) {
      const f = o[m];
      u.push(
        (0, import_jsx_runtime.jsx)(
          t.GridSuggestionMenu.Item,
          {
            className: "bn-grid-suggestion-menu-item",
            item: f,
            id: `bn-grid-suggestion-menu-item-${m}`,
            isSelected: m === l,
            onClick: () => c == null ? void 0 : c(f)
          },
          f.id
        )
      );
    }
    return u;
  }, [t, o, c, l]);
  return (0, import_jsx_runtime.jsxs)(
    t.GridSuggestionMenu.Root,
    {
      id: "bn-grid-suggestion-menu",
      columns: s,
      className: "bn-grid-suggestion-menu",
      children: [
        d,
        a,
        a.length === 0 && e.loadingState === "loaded" && (0, import_jsx_runtime.jsx)(
          t.GridSuggestionMenu.EmptyItem,
          {
            className: "bn-grid-suggestion-menu-empty-item",
            columns: s,
            children: n.suggestion_menu.no_items_title
          }
        )
      ]
    }
  );
}
function Lt(e, t, n, o = 3) {
  const r = (0, import_react3.useRef)(0);
  (0, import_react3.useEffect)(() => {
    t !== void 0 && (e.length > 0 ? r.current = t.length : t.length - r.current > o && n());
  }, [n, o, e.length, t]);
}
function Et(e, t) {
  const [n, o] = (0, import_react3.useState)([]), [r, l] = (0, import_react3.useState)(false), c = (0, import_react3.useRef)(), s = (0, import_react3.useRef)();
  return (0, import_react3.useEffect)(() => {
    const d = e;
    c.current = e, l(true), t(e).then((a) => {
      c.current === d && (o(a), l(false), s.current = d);
    });
  }, [e, t]), {
    items: n || [],
    // The query that was used to retrieve the last set of items may not be the
    // same as the current query as the items from the current query may not
    // have been retrieved yet. This is useful when using the returns of this
    // hook in other hooks.
    usedQuery: s.current,
    loadingState: s.current === void 0 ? "loading-initial" : r ? "loading" : "loaded"
  };
}
function Mr(e, t, n, o, r) {
  const [l, c] = (0, import_react3.useState)(0), s = o !== void 0 && o > 1;
  return (0, import_react3.useEffect)(() => {
    var a;
    const d = (u) => (u.key === "ArrowLeft" && (u.preventDefault(), n.length && c((l - 1 + n.length) % n.length)), u.key === "ArrowRight" && (u.preventDefault(), n.length && c((l + 1 + n.length) % n.length)), u.key === "ArrowUp" ? (u.preventDefault(), n.length && c(
      (l - o + n.length) % n.length
    ), true) : u.key === "ArrowDown" ? (u.preventDefault(), n.length && c((l + o) % n.length), true) : u.key === "Enter" && !u.isComposing ? (u.stopPropagation(), u.preventDefault(), n.length && (r == null || r(n[l])), true) : false);
    return (a = e.domElement) == null || a.addEventListener(
      "keydown",
      d,
      true
    ), () => {
      var u;
      (u = e.domElement) == null || u.removeEventListener(
        "keydown",
        d,
        true
      );
    };
  }, [e.domElement, n, l, r, o, s]), (0, import_react3.useEffect)(() => {
    c(0);
  }, [t]), {
    selectedIndex: n.length === 0 ? void 0 : l
  };
}
function xr(e) {
  const n = O().setContentEditableProps, o = b(), {
    getItems: r,
    gridSuggestionMenuComponent: l,
    query: c,
    clearQuery: s,
    closeMenu: d,
    onItemClick: a,
    columns: u
  } = e, m = (0, import_react3.useCallback)(
    (B) => {
      d(), s(), a == null || a(B);
    },
    [a, d, s]
  ), { items: f, usedQuery: h, loadingState: H } = Et(
    c,
    r
  );
  Lt(f, h, d);
  const { selectedIndex: p } = Mr(
    o,
    c,
    f,
    u,
    m
  );
  return (0, import_react3.useEffect)(() => (n((B) => ({
    ...B,
    "aria-expanded": true,
    "aria-controls": "bn-suggestion-menu"
  })), () => {
    n((B) => ({
      ...B,
      "aria-expanded": false,
      "aria-controls": void 0
    }));
  }), [n]), (0, import_react3.useEffect)(() => (n((B) => ({
    ...B,
    "aria-activedescendant": p ? "bn-suggestion-menu-item-" + p : void 0
  })), () => {
    n((B) => ({
      ...B,
      "aria-activedescendant": void 0
    }));
  }), [n, p]), (0, import_jsx_runtime.jsx)(
    l,
    {
      items: f,
      onItemClick: m,
      loadingState: H,
      selectedIndex: p,
      columns: u
    }
  );
}
function yr(e) {
  const t = b(), {
    triggerCharacter: n,
    gridSuggestionMenuComponent: o,
    columns: r,
    minQueryLength: l,
    onItemClick: c,
    getItems: s,
    floatingOptions: d
  } = e, a = (0, import_react3.useMemo)(() => c || ((S) => {
    S.onItemClick(t);
  }), [t, c]), u = (0, import_react3.useMemo)(() => s || (async (S) => await vr(
    t,
    S
  )), [t, s]), m = {
    closeMenu: t.suggestionMenus.closeMenu,
    clearQuery: t.suggestionMenus.clearQuery
  }, f = (0, import_react3.useCallback)(
    (S) => t.suggestionMenus.onUpdate(n, S),
    [t.suggestionMenus, n]
  ), h = D(f), { isMounted: H, ref: p, style: V, getFloatingProps: B } = G(
    (h == null ? void 0 : h.show) || false,
    (h == null ? void 0 : h.referencePos) || null,
    2e3,
    {
      placement: "bottom-start",
      middleware: [
        offset(10),
        // Flips the menu placement to maximize the space available, and prevents
        // the menu from being cut off by the confines of the screen.
        flip(),
        size({
          apply({ availableHeight: S, elements: I }) {
            Object.assign(I.floating.style, {
              maxHeight: `${S - 10}px`
            });
          }
        })
      ],
      onOpenChange(S) {
        S || t.suggestionMenus.closeMenu();
      },
      ...d
    }
  );
  return !H || !h || !(h != null && h.ignoreQueryLength) && l && (h.query.startsWith(" ") || h.query.length < l) ? null : (0, import_jsx_runtime.jsx)("div", { ref: p, style: V, ...B(), children: (0, import_jsx_runtime.jsx)(
    xr,
    {
      query: h.query,
      closeMenu: m.closeMenu,
      clearQuery: m.clearQuery,
      getItems: u,
      columns: r,
      gridSuggestionMenuComponent: o || Hr,
      onItemClick: a
    }
  ) });
}
function Br(e) {
  const t = C(), n = M(), { items: o, loadingState: r, selectedIndex: l, onItemClick: c } = e, s = r === "loading-initial" || r === "loading" ? (0, import_jsx_runtime.jsx)(
    t.SuggestionMenu.Loader,
    {
      className: "bn-suggestion-menu-loader"
    }
  ) : null, d = (0, import_react3.useMemo)(() => {
    let a;
    const u = [];
    for (let m = 0; m < o.length; m++) {
      const f = o[m];
      f.group !== a && (a = f.group, u.push(
        (0, import_jsx_runtime.jsx)(
          t.SuggestionMenu.Label,
          {
            className: "bn-suggestion-menu-label",
            children: a
          },
          a
        )
      )), u.push(
        (0, import_jsx_runtime.jsx)(
          t.SuggestionMenu.Item,
          {
            className: Q(
              "bn-suggestion-menu-item",
              f.size === "small" ? "bn-suggestion-menu-item-small" : ""
            ),
            item: f,
            id: `bn-suggestion-menu-item-${m}`,
            isSelected: m === l,
            onClick: () => c == null ? void 0 : c(f)
          },
          f.title
        )
      );
    }
    return u;
  }, [t, o, c, l]);
  return (0, import_jsx_runtime.jsxs)(
    t.SuggestionMenu.Root,
    {
      id: "bn-suggestion-menu",
      className: "bn-suggestion-menu",
      children: [
        d,
        d.length === 0 && (e.loadingState === "loading" || e.loadingState === "loaded") && (0, import_jsx_runtime.jsx)(
          t.SuggestionMenu.EmptyItem,
          {
            className: "bn-suggestion-menu-item",
            children: n.suggestion_menu.no_items_title
          }
        ),
        s
      ]
    }
  );
}
function Vr(e, t) {
  const [n, o] = (0, import_react3.useState)(0);
  return {
    selectedIndex: n,
    setSelectedIndex: o,
    handler: (r) => {
      if (r.key === "ArrowUp")
        return r.preventDefault(), e.length && o((n - 1 + e.length) % e.length), true;
      if (r.key === "ArrowDown")
        return r.preventDefault(), e.length && o((n + 1) % e.length), true;
      const l = Sr(r) ? r.nativeEvent.isComposing : r.isComposing;
      return r.key === "Enter" && !l ? (r.preventDefault(), r.stopPropagation(), e.length && (t == null || t(e[n])), true) : false;
    }
  };
}
function Sr(e) {
  return e.nativeEvent !== void 0;
}
function Tr(e, t, n, o, r) {
  const { selectedIndex: l, setSelectedIndex: c, handler: s } = Vr(n, o);
  return (0, import_react3.useEffect)(() => {
    var d;
    return (d = r || e.domElement) == null || d.addEventListener("keydown", s, true), () => {
      var a;
      (a = r || e.domElement) == null || a.removeEventListener(
        "keydown",
        s,
        true
      );
    };
  }, [e.domElement, n, l, o, r, s]), (0, import_react3.useEffect)(() => {
    c(0);
  }, [t, c]), {
    selectedIndex: n.length === 0 ? void 0 : l
  };
}
function Lr(e) {
  const n = O().setContentEditableProps, o = b(), {
    getItems: r,
    suggestionMenuComponent: l,
    query: c,
    clearQuery: s,
    closeMenu: d,
    onItemClick: a
  } = e, u = (0, import_react3.useCallback)(
    (V) => {
      d(), s(), a == null || a(V);
    },
    [a, d, s]
  ), { items: m, usedQuery: f, loadingState: h } = Et(
    c,
    r
  );
  Lt(m, f, d);
  const { selectedIndex: H } = Tr(
    o,
    c,
    m,
    u
  );
  return (0, import_react3.useEffect)(() => (n((V) => ({
    ...V,
    "aria-expanded": true,
    "aria-controls": "bn-suggestion-menu"
  })), () => {
    n((V) => ({
      ...V,
      "aria-expanded": false,
      "aria-controls": void 0
    }));
  }), [n]), (0, import_react3.useEffect)(() => (n((V) => ({
    ...V,
    "aria-activedescendant": H ? "bn-suggestion-menu-item-" + H : void 0
  })), () => {
    n((V) => ({
      ...V,
      "aria-activedescendant": void 0
    }));
  }), [n, H]), (0, import_jsx_runtime.jsx)(
    l,
    {
      items: m,
      onItemClick: u,
      loadingState: h,
      selectedIndex: H
    }
  );
}
var Er = {
  heading: ht,
  heading_2: gt,
  heading_3: bt2,
  quote: vt,
  numbered_list: kt,
  bullet_list: wt,
  check_list: pt,
  paragraph: _e,
  table: fo,
  image: Ht,
  video: go,
  audio: Mt,
  file: Re,
  emoji: Bo,
  code_block: ro2
};
function Rr(e) {
  return Uc(e).map((t) => {
    const n = Er[t.key];
    return {
      ...t,
      icon: (0, import_jsx_runtime.jsx)(n, { size: 18 })
    };
  });
}
function _r(e) {
  const t = b(), {
    triggerCharacter: n,
    suggestionMenuComponent: o,
    minQueryLength: r,
    onItemClick: l,
    getItems: c,
    floatingOptions: s
  } = e, d = (0, import_react3.useMemo)(() => l || ((B) => {
    B.onItemClick(t);
  }), [t, l]), a = (0, import_react3.useMemo)(() => c || (async (B) => _c(
    Rr(t),
    B
  )), [t, c]), u = {
    closeMenu: t.suggestionMenus.closeMenu,
    clearQuery: t.suggestionMenus.clearQuery
  }, m = (0, import_react3.useCallback)(
    (B) => t.suggestionMenus.onUpdate(n, B),
    [t.suggestionMenus, n]
  ), f = D(m), { isMounted: h, ref: H, style: p, getFloatingProps: V } = G(
    (f == null ? void 0 : f.show) || false,
    (f == null ? void 0 : f.referencePos) || null,
    2e3,
    {
      placement: "bottom-start",
      middleware: [
        offset(10),
        // Flips the menu placement to maximize the space available, and prevents
        // the menu from being cut off by the confines of the screen.
        flip({
          mainAxis: true,
          crossAxis: false
        }),
        shift(),
        size({
          apply({ availableHeight: B, elements: S }) {
            Object.assign(S.floating.style, {
              maxHeight: `${B - 10}px`
            });
          }
        })
      ],
      onOpenChange(B) {
        B || t.suggestionMenus.closeMenu();
      },
      ...s
    }
  );
  return !h || !f || !(f != null && f.ignoreQueryLength) && r && (f.query.startsWith(" ") || f.query.length < r) ? null : (0, import_jsx_runtime.jsx)("div", { ref: H, style: p, ...V(), children: (0, import_jsx_runtime.jsx)(
    Lr,
    {
      query: f.query,
      closeMenu: u.closeMenu,
      clearQuery: u.clearQuery,
      getItems: a,
      suggestionMenuComponent: o || Br,
      onItemClick: d
    }
  ) });
}
var Ir = (e, t = 0.3) => {
  const n = Math.floor(e) + t, o = Math.ceil(e) - t;
  return e >= n && e <= o ? Math.round(e) : e < n ? Math.floor(e) : Math.ceil(e);
};
var Nr = (e) => {
  const t = C(), n = (0, import_react3.useRef)(false), [o, r] = (0, import_react3.useState)(), l = (0, import_react3.useCallback)(
    (s) => {
      e.onMouseDown(), r({
        originalContent: e.block.content,
        originalCroppedContent: {
          rows: e.editor.tableHandles.cropEmptyRowsOrColumns(
            e.block,
            e.orientation === "addOrRemoveColumns" ? "columns" : "rows"
          )
        },
        startPos: e.orientation === "addOrRemoveColumns" ? s.clientX : s.clientY
      }), n.current = false, s.preventDefault();
    },
    [e]
  ), c = (0, import_react3.useCallback)(() => {
    n.current || e.editor.updateBlock(e.block, {
      type: "table",
      content: {
        ...e.block.content,
        rows: e.orientation === "addOrRemoveColumns" ? e.editor.tableHandles.addRowsOrColumns(
          e.block,
          "columns",
          1
        ) : e.editor.tableHandles.addRowsOrColumns(
          e.block,
          "rows",
          1
        )
      }
    });
  }, [e.block, e.orientation, e.editor]);
  return (0, import_react3.useEffect)(() => {
    const s = (d) => {
      var H, p;
      if (!o)
        throw new Error("editingState is undefined");
      n.current = true;
      const a = (e.orientation === "addOrRemoveColumns" ? d.clientX : d.clientY) - o.startPos, u = e.orientation === "addOrRemoveColumns" ? ((H = o.originalCroppedContent.rows[0]) == null ? void 0 : H.cells.length) ?? 0 : o.originalCroppedContent.rows.length, m = e.orientation === "addOrRemoveColumns" ? ((p = o.originalContent.rows[0]) == null ? void 0 : p.cells.length) ?? 0 : o.originalContent.rows.length, f = e.orientation === "addOrRemoveColumns" ? e.block.content.rows[0].cells.length : e.block.content.rows.length, h = m + Ir(
        a / (e.orientation === "addOrRemoveColumns" ? Rn : Ac),
        0.3
      );
      h >= u && h > 0 && h !== f && (e.editor.updateBlock(e.block, {
        type: "table",
        content: {
          ...e.block.content,
          rows: e.orientation === "addOrRemoveColumns" ? e.editor.tableHandles.addRowsOrColumns(
            {
              type: "table",
              content: o.originalCroppedContent
            },
            "columns",
            h - u
          ) : e.editor.tableHandles.addRowsOrColumns(
            {
              type: "table",
              content: o.originalCroppedContent
            },
            "rows",
            h - u
          )
        }
      }), e.block.content && e.editor.setTextCursorPosition(e.block));
    };
    return o && window.addEventListener("mousemove", s), () => {
      window.removeEventListener("mousemove", s);
    };
  }, [o, e.block, e.editor, e.orientation]), (0, import_react3.useEffect)(() => {
    const s = e.onMouseUp, d = () => {
      r(void 0), s();
    };
    return o && window.addEventListener("mouseup", d), () => {
      window.removeEventListener("mouseup", d);
    };
  }, [o, e.onMouseUp]), (0, import_jsx_runtime.jsx)(
    t.TableHandle.ExtendButton,
    {
      className: Q(
        "bn-extend-button",
        e.orientation === "addOrRemoveColumns" ? "bn-extend-button-add-remove-columns" : "bn-extend-button-add-remove-rows",
        o !== null ? "bn-extend-button-editing" : ""
      ),
      onClick: c,
      onMouseDown: l,
      children: e.children || (0, import_jsx_runtime.jsx)(ko, { size: 18, "data-test": "extendButton" })
    }
  );
};
var Je = (e) => {
  const t = C(), n = M(), r = b().tableHandles;
  return r ? (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      onClick: () => {
        r.addRowOrColumn(
          e.index,
          e.orientation === "row" ? { orientation: "row", side: e.side } : { orientation: "column", side: e.side }
        );
      },
      children: n.table_handle[`add_${e.side}_menuitem`]
    }
  ) : null;
};
var Pr = (e) => {
  const t = C(), n = M(), r = b().tableHandles;
  return r ? (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      onClick: () => {
        r.removeRowOrColumn(e.index, e.orientation);
      },
      children: e.orientation === "row" ? n.table_handle.delete_row_menuitem : n.table_handle.delete_column_menuitem
    }
  ) : null;
};
var Dr = (e) => {
  const t = C(), n = M(), o = b(), r = o.tableHandles, l = (0, import_react3.useMemo)(() => !r || !e.block ? [] : e.orientation === "row" ? r.getCellsAtRowHandle(e.block, e.index) : r.getCellsAtColumnHandle(e.block, e.index), [e.block, e.index, e.orientation, r]), c = (d, a) => {
    const u = e.block.content.rows.map((m) => ({
      ...m,
      cells: m.cells.map((f) => rt(f))
    }));
    l.forEach(({ row: m, col: f }) => {
      a === "text" ? u[m].cells[f].props.textColor = d : u[m].cells[f].props.backgroundColor = d;
    }), o.updateBlock(e.block, {
      type: "table",
      content: {
        ...e.block.content,
        rows: u
      }
    }), o.setTextCursorPosition(e.block);
  };
  if (!l || !l[0] || !r || o.settings.tables.cellTextColor === false && o.settings.tables.cellBackgroundColor === false)
    return null;
  const s = rt(l[0].cell);
  return (0, import_jsx_runtime.jsxs)(t.Generic.Menu.Root, { position: "right", sub: true, children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { sub: true, children: (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Item,
      {
        className: "bn-menu-item",
        subTrigger: true,
        children: e.children || n.drag_handle.colors_menuitem
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Dropdown,
      {
        sub: true,
        className: "bn-menu-dropdown bn-color-picker-dropdown",
        children: (0, import_jsx_runtime.jsx)(
          me,
          {
            iconSize: 18,
            text: o.settings.tables.cellTextColor ? {
              // All cells have the same text color
              color: l.every(
                ({ cell: d }) => bt(d) && d.props.textColor === s.props.textColor
              ) ? s.props.textColor : "default",
              setColor: (d) => {
                c(d, "text");
              }
            } : void 0,
            background: o.settings.tables.cellBackgroundColor ? {
              color: l.every(
                ({ cell: d }) => bt(d) && d.props.backgroundColor === s.props.backgroundColor
              ) ? s.props.backgroundColor : "default",
              setColor: (d) => c(d, "background")
            } : void 0
          }
        )
      }
    )
  ] });
};
var Or = (e) => {
  const t = C(), n = M(), o = b();
  if (!o.tableHandles || e.index !== 0 || e.orientation !== "row" || !o.settings.tables.headers)
    return null;
  const l = !!e.block.content.headerRows;
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      className: "bn-menu-item",
      checked: l,
      onClick: () => {
        const c = o.getBlock(e.block.id);
        c && o.updateBlock(c, {
          ...c,
          content: {
            ...c.content,
            headerRows: l ? void 0 : 1
          }
        });
      },
      children: n.drag_handle.header_row_menuitem
    }
  );
};
var Zr2 = (e) => {
  const t = C(), n = M(), o = b();
  if (!o.tableHandles || e.index !== 0 || e.orientation !== "column" || !o.settings.tables.headers)
    return null;
  const l = !!e.block.content.headerCols;
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      className: "bn-menu-item",
      checked: l,
      onClick: () => {
        const c = o.getBlock(e.block.id);
        c && o.updateBlock(c, {
          ...c,
          content: {
            ...c.content,
            headerCols: l ? void 0 : 1
          }
        });
      },
      children: n.drag_handle.header_column_menuitem
    }
  );
};
var Fr = (e) => {
  const t = C();
  return (0, import_jsx_runtime.jsx)(t.Generic.Menu.Dropdown, { className: "bn-table-handle-menu", children: e.children || (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      Pr,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Je,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index,
        side: e.orientation === "row" ? "above" : "left"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Je,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index,
        side: e.orientation === "row" ? "below" : "right"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Or,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Zr2,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Dr,
      {
        orientation: e.orientation,
        block: e.block,
        index: e.index
      }
    )
  ] }) });
};
var Ar = (e) => {
  const t = C(), [n, o] = (0, import_react3.useState)(false), r = e.tableHandleMenu || Fr, l = (0, import_react3.useMemo)(() => {
    const c = e.editor.tableHandles;
    return !c || !e.block || e.block.type !== "table" ? false : e.orientation === "column" ? c.getCellsAtColumnHandle(e.block, e.index).every(({ cell: s }) => ve(s) === 1) : c.getCellsAtRowHandle(e.block, e.index).every(({ cell: s }) => st(s) === 1);
  }, [e.block, e.editor.tableHandles, e.index, e.orientation]);
  return (0, import_jsx_runtime.jsxs)(
    t.Generic.Menu.Root,
    {
      onOpenChange: (c) => {
        c ? (e.freezeHandles(), e.hideOtherSide()) : (e.unfreezeHandles(), e.showOtherSide(), e.editor.focus());
      },
      position: "right",
      children: [
        (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { children: (0, import_jsx_runtime.jsx)(
          t.TableHandle.Root,
          {
            className: Q(
              "bn-table-handle",
              n ? "bn-table-handle-dragging" : "",
              l ? "" : "bn-table-handle-not-draggable"
            ),
            draggable: l,
            onDragStart: (c) => {
              o(true), e.dragStart(c);
            },
            onDragEnd: () => {
              e.dragEnd(), o(false);
            },
            style: e.orientation === "column" ? { transform: "rotate(0.25turn)" } : void 0,
            children: e.children || (0, import_jsx_runtime.jsx)(Tt, { size: 24, "data-test": "tableHandle" })
          }
        ) }),
        (0, import_react_dom4.createPortal)(
          (0, import_jsx_runtime.jsx)(
            r,
            {
              orientation: e.orientation,
              block: e.block,
              index: e.index
            }
          ),
          e.menuContainer
        )
      ]
    }
  );
};
function Qe(e, t, n) {
  const { refs: o, update: r, context: l, floatingStyles: c } = useFloating2({
    open: t,
    placement: e === "addOrRemoveColumns" ? "right" : "bottom",
    middleware: [
      size({
        apply({ rects: a, elements: u }) {
          Object.assign(
            u.floating.style,
            e === "addOrRemoveColumns" ? {
              height: `${a.reference.height}px`
            } : {
              width: `${a.reference.width}px`
            }
          );
        }
      })
    ]
  }), { isMounted: s, styles: d } = useTransitionStyles(l);
  return (0, import_react3.useEffect)(() => {
    r();
  }, [n, r]), (0, import_react3.useEffect)(() => {
    n !== null && o.setReference({
      getBoundingClientRect: () => n
    });
  }, [e, n, o]), (0, import_react3.useMemo)(
    () => ({
      isMounted: s,
      ref: o.setFloating,
      style: {
        display: "flex",
        ...d,
        ...c
      }
    }),
    [c, s, o.setFloating, d]
  );
}
function Gr(e, t, n) {
  const o = Qe(
    "addOrRemoveRows",
    t,
    n
  ), r = Qe(
    "addOrRemoveColumns",
    e,
    n
  );
  return (0, import_react3.useMemo)(
    () => ({
      addOrRemoveRowsButton: o,
      addOrRemoveColumnsButton: r
    }),
    [r, o]
  );
}
function Ur2(e, t, n) {
  return n && n.draggedCellOrientation === "row" ? new DOMRect(
    t.x,
    n.mousePos,
    t.width,
    0
  ) : new DOMRect(
    t.x,
    e.y,
    t.width,
    e.height
  );
}
function zr(e, t, n) {
  return n && n.draggedCellOrientation === "col" ? new DOMRect(
    n.mousePos,
    t.y,
    0,
    t.height
  ) : new DOMRect(
    e.x,
    t.y,
    e.width,
    t.height
  );
}
function jr(e) {
  return new DOMRect(
    e.x,
    e.y,
    e.width,
    0
  );
}
function Ce(e, t, n, o, r) {
  const { refs: l, update: c, context: s, floatingStyles: d } = useFloating2({
    open: t,
    placement: e === "row" ? "left" : e === "col" ? "top" : "bottom-end",
    middleware: [
      offset(
        e === "row" ? -10 : e === "col" ? -12 : { mainAxis: 1, crossAxis: -1 }
      )
    ]
  }), { isMounted: a, styles: u } = useTransitionStyles(s);
  return (0, import_react3.useEffect)(() => {
    c();
  }, [n, o, c]), (0, import_react3.useEffect)(() => {
    n === null || o === null || // Ignore cell handle when dragging
    r && e === "cell" || l.setReference({
      getBoundingClientRect: () => (e === "row" ? Ur2 : e === "col" ? zr : jr)(n, o, r)
    });
  }, [r, e, n, o, l]), (0, import_react3.useMemo)(
    () => ({
      isMounted: a,
      ref: l.setFloating,
      style: {
        display: "flex",
        ...u,
        ...d
      }
    }),
    [d, a, l.setFloating, u]
  );
}
function Wr(e, t, n, o) {
  const r = Ce(
    "row",
    e,
    t,
    n,
    o
  ), l = Ce(
    "col",
    e,
    t,
    n,
    o
  ), c = Ce(
    "cell",
    e,
    t,
    n,
    o
  );
  return (0, import_react3.useMemo)(
    () => ({
      rowHandle: r,
      colHandle: l,
      cellHandle: c
    }),
    [l, r, c]
  );
}
var $r2 = (e) => {
  var c, s;
  const t = C(), n = M(), o = b(), r = (d, a) => {
    const u = e.block.content.rows.map((m) => ({
      ...m,
      cells: m.cells.map((f) => rt(f))
    }));
    a === "text" ? u[e.rowIndex].cells[e.colIndex].props.textColor = d : u[e.rowIndex].cells[e.colIndex].props.backgroundColor = d, o.updateBlock(e.block, {
      type: "table",
      content: {
        ...e.block.content,
        rows: u
      }
    }), o.setTextCursorPosition(e.block);
  }, l = (s = (c = e.block.content.rows[e.rowIndex]) == null ? void 0 : c.cells) == null ? void 0 : s[e.colIndex];
  return !l || o.settings.tables.cellTextColor === false && o.settings.tables.cellBackgroundColor === false ? null : (0, import_jsx_runtime.jsxs)(t.Generic.Menu.Root, { position: "right", sub: true, children: [
    (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { sub: true, children: (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Item,
      {
        className: "bn-menu-item",
        subTrigger: true,
        children: e.children || n.drag_handle.colors_menuitem
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      t.Generic.Menu.Dropdown,
      {
        sub: true,
        className: "bn-menu-dropdown bn-color-picker-dropdown",
        children: (0, import_jsx_runtime.jsx)(
          me,
          {
            iconSize: 18,
            text: o.settings.tables.cellTextColor ? {
              color: bt(l) ? l.props.textColor : "default",
              setColor: (d) => r(d, "text")
            } : void 0,
            background: o.settings.tables.cellBackgroundColor ? {
              color: bt(l) ? l.props.backgroundColor : "default",
              setColor: (d) => r(d, "background")
            } : void 0
          }
        )
      }
    )
  ] });
};
var qr = (e) => {
  var l, c;
  const t = C(), n = M(), o = b(), r = (c = (l = e.block.content.rows[e.rowIndex]) == null ? void 0 : l.cells) == null ? void 0 : c[e.colIndex];
  return !r || !bt(r) || st(r) === 1 && ve(r) === 1 || !o.settings.tables.splitCells ? null : (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Item,
    {
      onClick: () => {
        var s;
        (s = o.tableHandles) == null || s.splitCell({
          row: e.rowIndex,
          col: e.colIndex
        });
      },
      children: n.table_handle.split_cell_menuitem
    }
  );
};
var Kr = (e) => {
  const t = C();
  return (0, import_jsx_runtime.jsx)(
    t.Generic.Menu.Dropdown,
    {
      className: "bn-menu-dropdown bn-drag-handle-menu",
      children: e.children || (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
        (0, import_jsx_runtime.jsx)(
          qr,
          {
            block: e.block,
            rowIndex: e.rowIndex,
            colIndex: e.colIndex
          }
        ),
        (0, import_jsx_runtime.jsx)(
          $r2,
          {
            block: e.block,
            rowIndex: e.rowIndex,
            colIndex: e.colIndex
          }
        )
      ] })
    }
  );
};
var Xr = (e) => {
  const t = C(), n = e.tableCellMenu || Kr;
  return !e.editor.settings.tables.splitCells && !e.editor.settings.tables.cellBackgroundColor && !e.editor.settings.tables.cellTextColor ? null : (0, import_jsx_runtime.jsxs)(
    t.Generic.Menu.Root,
    {
      onOpenChange: (o) => {
        o ? e.freezeHandles() : (e.unfreezeHandles(), e.editor.focus());
      },
      position: "right",
      children: [
        (0, import_jsx_runtime.jsx)(t.Generic.Menu.Trigger, { children: (0, import_jsx_runtime.jsx)(t.Generic.Menu.Button, { className: "bn-table-cell-handle", children: e.children || (0, import_jsx_runtime.jsx)(mr2, { size: 12, "data-test": "tableCellHandle" }) }) }),
        (0, import_react_dom4.createPortal)(
          (0, import_jsx_runtime.jsx)(
            n,
            {
              block: e.block,
              rowIndex: e.rowIndex,
              colIndex: e.colIndex
            }
          ),
          e.menuContainer
        )
      ]
    }
  );
};
var Yr = (e) => {
  var $, R;
  const t = b(), [n, o] = (0, import_react3.useState)(null);
  if (!t.tableHandles)
    throw new Error(
      "TableHandlesController can only be used when BlockNote editor schema contains table block"
    );
  const r = {
    rowDragStart: t.tableHandles.rowDragStart,
    colDragStart: t.tableHandles.colDragStart,
    dragEnd: t.tableHandles.dragEnd,
    freezeHandles: t.tableHandles.freezeHandles,
    unfreezeHandles: t.tableHandles.unfreezeHandles
  }, { freezeHandles: l, unfreezeHandles: c } = r, s = (0, import_react3.useCallback)(() => {
    l(), I(true), B(true);
  }, [l]), d = (0, import_react3.useCallback)(() => {
    c(), I(false), B(false);
  }, [c]), a = D(
    t.tableHandles.onUpdate.bind(t.tableHandles)
  ), u = (0, import_react3.useMemo)(() => {
    var F2, ee;
    return a != null && a.draggingState ? {
      draggedCellOrientation: (F2 = a == null ? void 0 : a.draggingState) == null ? void 0 : F2.draggedCellOrientation,
      mousePos: (ee = a == null ? void 0 : a.draggingState) == null ? void 0 : ee.mousePos
    } : void 0;
  }, [
    a == null ? void 0 : a.draggingState,
    ($ = a == null ? void 0 : a.draggingState) == null ? void 0 : $.draggedCellOrientation,
    (R = a == null ? void 0 : a.draggingState) == null ? void 0 : R.mousePos
  ]), { rowHandle: m, colHandle: f, cellHandle: h } = Wr(
    (a == null ? void 0 : a.show) || false,
    (a == null ? void 0 : a.referencePosCell) || null,
    (a == null ? void 0 : a.referencePosTable) || null,
    u
  ), { addOrRemoveColumnsButton: H, addOrRemoveRowsButton: p } = Gr(
    (a == null ? void 0 : a.showAddOrRemoveColumnsButton) || false,
    (a == null ? void 0 : a.showAddOrRemoveRowsButton) || false,
    (a == null ? void 0 : a.referencePosTable) || null
  ), [V, B] = (0, import_react3.useState)(false), [S, I] = (0, import_react3.useState)(false);
  if (!a)
    return null;
  const z = e.tableHandle || Ar, j2 = e.extendButton || Nr, Z2 = e.tableCellHandle || Xr;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)("div", { ref: o }),
    (0, import_jsx_runtime.jsxs)(FloatingPortal, { root: a.widgetContainer, children: [
      !V && n && m.isMounted && a.rowIndex !== void 0 && (0, import_jsx_runtime.jsx)("div", { ref: m.ref, style: m.style, children: (0, import_jsx_runtime.jsx)(
        z,
        {
          editor: t,
          orientation: "row",
          showOtherSide: () => I(false),
          hideOtherSide: () => I(true),
          index: a.rowIndex,
          block: a.block,
          dragStart: r.rowDragStart,
          dragEnd: r.dragEnd,
          freezeHandles: r.freezeHandles,
          unfreezeHandles: r.unfreezeHandles,
          menuContainer: n
        }
      ) }),
      !S && n && f.isMounted && a.colIndex !== void 0 && (0, import_jsx_runtime.jsx)("div", { ref: f.ref, style: f.style, children: (0, import_jsx_runtime.jsx)(
        z,
        {
          editor: t,
          orientation: "column",
          showOtherSide: () => B(false),
          hideOtherSide: () => B(true),
          index: a.colIndex,
          block: a.block,
          dragStart: r.colDragStart,
          dragEnd: r.dragEnd,
          freezeHandles: r.freezeHandles,
          unfreezeHandles: r.unfreezeHandles,
          menuContainer: n
        }
      ) }),
      n && h.isMounted && a.colIndex !== void 0 && a.rowIndex !== void 0 && (0, import_jsx_runtime.jsx)("div", { ref: h.ref, style: h.style, children: (0, import_jsx_runtime.jsx)(
        Z2,
        {
          editor: t,
          block: a.block,
          rowIndex: a.rowIndex,
          colIndex: a.colIndex,
          menuContainer: n,
          freezeHandles: r.freezeHandles,
          unfreezeHandles: r.unfreezeHandles
        }
      ) }),
      (0, import_jsx_runtime.jsx)(
        "div",
        {
          ref: p.ref,
          style: p.style,
          children: (0, import_jsx_runtime.jsx)(
            j2,
            {
              editor: t,
              orientation: "addOrRemoveRows",
              block: a.block,
              onMouseDown: s,
              onMouseUp: d
            }
          )
        }
      ),
      (0, import_jsx_runtime.jsx)(
        "div",
        {
          ref: H.ref,
          style: H.style,
          children: (0, import_jsx_runtime.jsx)(
            j2,
            {
              editor: t,
              orientation: "addOrRemoveColumns",
              block: a.block,
              onMouseDown: s,
              onMouseUp: d
            }
          )
        }
      )
    ] })
  ] });
};
function Jr(e) {
  const t = b();
  if (!t)
    throw new Error(
      "BlockNoteDefaultUI must be used within a BlockNoteContext.Provider"
    );
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    e.formattingToolbar !== false && (0, import_jsx_runtime.jsx)(rr, {}),
    e.linkToolbar !== false && (0, import_jsx_runtime.jsx)(sr, {}),
    e.slashMenu !== false && (0, import_jsx_runtime.jsx)(_r, { triggerCharacter: "/" }),
    e.emojiPicker !== false && (0, import_jsx_runtime.jsx)(
      yr,
      {
        triggerCharacter: ":",
        columns: 10,
        minQueryLength: 2
      }
    ),
    e.sideMenu !== false && (0, import_jsx_runtime.jsx)(wr, {}),
    t.filePanel && e.filePanel !== false && (0, import_jsx_runtime.jsx)(Po, {}),
    t.tableHandles && e.tableHandles !== false && (0, import_jsx_runtime.jsx)(Yr, {}),
    t.comments && e.comments !== false && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsx)(Gn, {}),
      (0, import_jsx_runtime.jsx)(_o, {})
    ] })
  ] });
}
var Qr = () => {
  const e = (0, import_react3.useMemo)(
    () => {
      var c;
      return (c = window.matchMedia) == null ? void 0 : c.call(window, "(prefers-color-scheme: dark)");
    },
    []
  ), t = (0, import_react3.useMemo)(
    () => {
      var c;
      return (c = window.matchMedia) == null ? void 0 : c.call(window, "(prefers-color-scheme: light)");
    },
    []
  ), n = e == null ? void 0 : e.matches, o = t == null ? void 0 : t.matches, [r, l] = (0, import_react3.useState)(n ? "dark" : o ? "light" : "no-preference");
  return (0, import_react3.useEffect)(() => {
    l(n ? "dark" : o ? "light" : "no-preference");
  }, [n, o]), (0, import_react3.useEffect)(() => {
    if (typeof (e == null ? void 0 : e.addEventListener) == "function") {
      const c = ({ matches: d }) => d && l("dark"), s = ({ matches: d }) => d && l("light");
      return e == null || e.addEventListener("change", c), t == null || t.addEventListener("change", s), () => {
        e == null || e.removeEventListener("change", c), t == null || t.removeEventListener("change", s);
      };
    } else {
      const c = () => l(
        e.matches ? "dark" : t.matches ? "light" : "no-preference"
      );
      return e == null || e.addEventListener("change", c), t == null || t.addEventListener("change", c), () => {
        e == null || e.removeEventListener("change", c), t == null || t.removeEventListener("change", c);
      };
    }
  }, [e, t]), typeof window.matchMedia != "function", r;
};
var Rt = (0, import_react3.createContext)(void 0);
function ei() {
  return (0, import_react3.useContext)(Rt);
}
function ti() {
  const e = /* @__PURE__ */ new Set();
  let t = {};
  return {
    /**
     * Subscribe to the editor instance's changes.
     */
    subscribe(n) {
      return e.add(n), () => {
        e.delete(n);
      };
    },
    getSnapshot() {
      return t;
    },
    getServerSnapshot() {
      return t;
    },
    /**
     * Adds a new NodeView Renderer to the editor.
     */
    setRenderer(n, o) {
      t = {
        ...t,
        [n]: (0, import_react_dom4.createPortal)(o.reactElement, o.element, n)
      }, e.forEach((r) => r());
    },
    /**
     * Removes a NodeView Renderer from the editor.
     */
    removeRenderer(n) {
      const o = { ...t };
      delete o[n], t = o, e.forEach((r) => r());
    }
  };
}
var ni = ({
  contentComponent: e
}) => {
  const t = (0, import_react3.useSyncExternalStore)(
    e.subscribe,
    e.getSnapshot,
    e.getServerSnapshot
  );
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: Object.values(t) });
};
var oi = (0, import_react3.forwardRef)((e, t) => {
  const [n, o] = (0, import_react3.useState)();
  return (0, import_react3.useImperativeHandle)(t, () => (r, l) => {
    (0, import_react_dom4.flushSync)(() => {
      o({ node: r, container: l });
    }), o(void 0);
  }, []), (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: n && (0, import_react_dom4.createPortal)(n.node, n.container) });
});
var et = () => {
};
function ri(e, t) {
  const {
    editor: n,
    className: o,
    theme: r,
    children: l,
    editable: c,
    onSelectionChange: s,
    onChange: d,
    formattingToolbar: a,
    linkToolbar: u,
    slashMenu: m,
    emojiPicker: f,
    sideMenu: h,
    filePanel: H,
    tableHandles: p,
    comments: V,
    autoFocus: B,
    renderEditor: S = !n.headless,
    ...I
  } = e, [z, j2] = (0, import_react3.useState)(), Z2 = O(), $ = Qr(), R = (Z2 == null ? void 0 : Z2.colorSchemePreference) || $, F2 = r || (R === "dark" ? "dark" : "light");
  ue(d || et, n), se(s || et, n), (0, import_react3.useEffect)(() => {
    n.isEditable = c !== false;
  }, [c, n]);
  const ee = (0, import_react3.useCallback)(
    (Ot) => {
      n.elementRenderer = Ot;
    },
    [n]
  ), Nt = (0, import_react3.useMemo)(() => ({
    ...Z2,
    editor: n,
    setContentEditableProps: j2
  }), [Z2, n]), Pt = {
    formattingToolbar: a,
    linkToolbar: u,
    slashMenu: m,
    emojiPicker: f,
    sideMenu: h,
    filePanel: H,
    tableHandles: p,
    comments: V
  }, Dt = {
    autoFocus: B,
    contentEditableProps: z
  };
  return (0, import_jsx_runtime.jsx)(dt.Provider, { value: Nt, children: (0, import_jsx_runtime.jsxs)(
    Rt.Provider,
    {
      value: {
        editorProps: Dt,
        defaultUIProps: Pt
      },
      children: [
        (0, import_jsx_runtime.jsx)(oi, { ref: ee }),
        (0, import_jsx_runtime.jsx)(
          ii,
          {
            className: o,
            renderEditor: S,
            editorColorScheme: F2,
            ref: t,
            ...I,
            children: l
          }
        )
      ]
    }
  ) });
}
var ii = import_react3.default.forwardRef(({ className: e, renderEditor: t, editorColorScheme: n, children: o, ...r }, l) => (0, import_jsx_runtime.jsx)(
  "div",
  {
    className: Q("bn-container", n, e),
    "data-color-scheme": n,
    ...r,
    ref: l,
    children: t ? (0, import_jsx_runtime.jsx)(li, { children: o }) : o
  }
));
var Ui = import_react3.default.forwardRef(ri);
var li = (e) => {
  const t = ei(), n = b(), o = (0, import_react3.useMemo)(() => ti(), []), r = (0, import_react3.useCallback)(
    (l) => {
      n.mount(l, o);
    },
    [n, o]
  );
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(ni, { contentComponent: o }),
    (0, import_jsx_runtime.jsx)(ci, { ...t.editorProps, ...e, mount: r }),
    (0, import_jsx_runtime.jsx)(Jr, { ...t.defaultUIProps }),
    e.children
  ] });
};
var ci = (e) => {
  const { autoFocus: t, mount: n, contentEditableProps: o } = e;
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      "aria-autocomplete": "list",
      "aria-haspopup": "listbox",
      "data-bn-autofocus": t,
      ref: n,
      ...o
    }
  );
};
function ie(e, t) {
  let n;
  const o = document.createElement("div");
  let r;
  if (t != null && t.elementRenderer)
    t.elementRenderer(
      e((d) => n = d || void 0),
      o
    );
  else {
    if (!(t != null && t.headless))
      throw new Error(
        "elementRenderer not available, expected headless editor"
      );
    r = (0, import_client.createRoot)(o), (0, import_react_dom4.flushSync)(() => {
      r.render(e((d) => n = d || void 0));
    });
  }
  if (!o.childElementCount)
    return console.warn("ReactInlineContentSpec: renderHTML() failed"), {
      dom: document.createElement("span")
    };
  n == null || n.setAttribute("data-tmp-find", "true");
  const l = o.cloneNode(true), c = l.firstElementChild, s = l.querySelector(
    "[data-tmp-find]"
  );
  return s == null || s.removeAttribute("data-tmp-find"), r == null || r.unmount(), {
    dom: c,
    contentDOM: s || void 0
  };
}
function pe(e) {
  var t;
  return (
    // Creates `blockContent` element
    (0, import_jsx_runtime.jsx)(
      NodeViewWrapper,
      {
        onDragOver: (n) => n.preventDefault(),
        ...Object.fromEntries(
          Object.entries(e.domAttributes || {}).filter(
            ([n]) => n !== "class"
          )
        ),
        className: Q(
          "bn-block-content",
          ((t = e.domAttributes) == null ? void 0 : t.class) || ""
        ),
        "data-content-type": e.blockType,
        ...Object.fromEntries(
          Object.entries(e.blockProps).filter(([n, o]) => {
            const r = e.propSchema[n];
            return !fn.includes(n) && o !== r.default;
          }).map(([n, o]) => [Ve(n), o])
        ),
        "data-file-block": e.isFileBlock === true || void 0,
        children: e.children
      }
    )
  );
}
function fe(e, t) {
  const n = q({
    name: e.type,
    content: e.content === "inline" ? "inline*" : "",
    group: "blockContent",
    selectable: e.isSelectable ?? true,
    isolating: true,
    addAttributes() {
      return Be(e.propSchema);
    },
    parseHTML() {
      return cr(e, t.parse);
    },
    renderHTML({ HTMLAttributes: o }) {
      const r = document.createElement("div");
      return Ie(
        {
          dom: r,
          contentDOM: e.content === "inline" ? r : void 0
        },
        e.type,
        {},
        e.propSchema,
        e.isFileBlock,
        o
      );
    },
    addNodeView() {
      return (o) => {
        const r = ReactNodeViewRenderer(
          (l) => {
            var m;
            const c = this.options.editor, s = ir(
              l.getPos,
              c,
              this.editor,
              e.type
            ), d = ((m = this.options.domAttributes) == null ? void 0 : m.blockContent) || {}, a = useReactNodeView().nodeViewContentRef;
            if (!a)
              throw new Error("nodeViewContentRef is not set");
            const u = t.render;
            return (0, import_jsx_runtime.jsx)(
              pe,
              {
                blockType: s.type,
                blockProps: s.props,
                propSchema: e.propSchema,
                isFileBlock: e.isFileBlock,
                domAttributes: d,
                children: (0, import_jsx_runtime.jsx)(
                  u,
                  {
                    block: s,
                    editor: c,
                    contentRef: (f) => {
                      a(f), f && (f.className = Q(
                        "bn-inline-content",
                        f.className
                      ));
                    }
                  }
                )
              }
            );
          },
          {
            className: "bn-react-node-view-renderer"
          }
        )(o);
        return e.isSelectable === false && ar(r, this.editor), r;
      };
    }
  });
  return mn(e, {
    node: n,
    toInternalHTML: (o, r) => {
      var d;
      const l = ((d = n.options.domAttributes) == null ? void 0 : d.blockContent) || {}, c = t.render;
      return ie(
        (a) => (0, import_jsx_runtime.jsx)(
          pe,
          {
            blockType: o.type,
            blockProps: o.props,
            propSchema: e.propSchema,
            domAttributes: l,
            children: (0, import_jsx_runtime.jsx)(
              c,
              {
                block: o,
                editor: r,
                contentRef: (u) => {
                  a(u), u && (u.className = Q(
                    "bn-inline-content",
                    u.className
                  ));
                }
              }
            )
          }
        ),
        r
      );
    },
    toExternalHTML: (o, r) => {
      var d;
      const l = ((d = n.options.domAttributes) == null ? void 0 : d.blockContent) || {}, c = t.toExternalHTML || t.render;
      return ie((a) => (0, import_jsx_runtime.jsx)(
        pe,
        {
          blockType: o.type,
          blockProps: o.props,
          propSchema: e.propSchema,
          domAttributes: l,
          children: (0, import_jsx_runtime.jsx)(
            c,
            {
              block: o,
              editor: r,
              contentRef: (u) => {
                a(u), u && (u.className = Q(
                  "bn-inline-content",
                  u.className
                ));
              }
            }
          )
        }
      ), r);
    }
  });
}
function Ne(e) {
  const t = b(), [n, o] = (0, import_react3.useState)("loading"), [r, l] = (0, import_react3.useState)();
  if ((0, import_react3.useEffect)(() => {
    let c = true;
    return (async () => {
      let s = "";
      o("loading");
      try {
        s = t.resolveFileUrl ? await t.resolveFileUrl(e) : e;
      } catch {
        o("error");
        return;
      }
      c && (o("loaded"), l(s));
    })(), () => {
      c = false;
    };
  }, [t, e]), n !== "loaded")
    return {
      loadingState: n
    };
  if (!r)
    throw new Error("Finished fetching file but did not get download URL.");
  return {
    loadingState: n,
    downloadUrl: r
  };
}
var Pe2 = (e) => (0, import_jsx_runtime.jsxs)("figure", { children: [
  e.children,
  (0, import_jsx_runtime.jsx)("figcaption", { children: e.caption })
] });
function ai(e) {
  const t = b();
  (0, import_react3.useEffect)(() => t.onUploadEnd(e), [e, t]);
}
function si(e) {
  const t = b();
  (0, import_react3.useEffect)(() => t.onUploadStart(e), [e, t]);
}
function _t(e) {
  const [t, n] = (0, import_react3.useState)(false);
  return si((o) => {
    o === e && n(true);
  }), ai((o) => {
    o === e && n(false);
  }), t;
}
var di = (e) => {
  const t = M(), n = (0, import_react3.useCallback)(
    (r) => {
      r.preventDefault();
    },
    []
  ), o = (0, import_react3.useCallback)(() => {
    e.editor.transact(
      (r) => r.setMeta(e.editor.filePanel.plugins[0], {
        block: e.block
      })
    );
  }, [e.block, e.editor]);
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: "bn-add-file-button",
      onMouseDown: n,
      onClick: o,
      children: [
        (0, import_jsx_runtime.jsx)("div", { className: "bn-add-file-button-icon", children: e.buttonIcon || (0, import_jsx_runtime.jsx)(Re, { size: 24 }) }),
        (0, import_jsx_runtime.jsx)("div", { className: "bn-add-file-button-text", children: e.buttonText || t.file_blocks.file.add_button_text })
      ]
    }
  );
};
var ui = (e) => (0, import_jsx_runtime.jsxs)(
  "div",
  {
    className: "bn-file-name-with-icon",
    contentEditable: false,
    draggable: false,
    children: [
      (0, import_jsx_runtime.jsx)("div", { className: "bn-file-icon", children: (0, import_jsx_runtime.jsx)(Re, { size: 24 }) }),
      (0, import_jsx_runtime.jsx)("p", { className: "bn-file-name", children: e.block.props.name })
    ]
  }
);
var De = (e) => {
  const t = _t(e.block.id);
  return (0, import_jsx_runtime.jsx)(
    "div",
    {
      className: "bn-file-block-content-wrapper",
      onMouseEnter: e.onMouseEnter,
      onMouseLeave: e.onMouseLeave,
      style: e.style,
      children: t ? (
        // Show loader while a file is being uploaded.
        (0, import_jsx_runtime.jsx)("div", { className: "bn-file-loading-preview", children: "Loading..." })
      ) : e.block.props.url === "" ? (
        // Show the add file button if the file has not been uploaded yet.
        (0, import_jsx_runtime.jsx)(di, { ...e })
      ) : (
        // Show the file preview, or the file name and icon.
        (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
          e.block.props.showPreview === false || !e.children ? (
            // Show file name and icon.
            (0, import_jsx_runtime.jsx)(ui, { ...e })
          ) : (
            // Show preview.
            e.children
          ),
          e.block.props.caption && // Show the caption if there is one.
          (0, import_jsx_runtime.jsx)("p", { className: "bn-file-caption", children: e.block.props.caption })
        ] })
      )
    }
  );
};
var he = (e) => (0, import_jsx_runtime.jsxs)("div", { children: [
  e.children,
  (0, import_jsx_runtime.jsx)("p", { children: e.caption })
] });
var mi = (e) => {
  const t = Ne(e.block.props.url);
  return (0, import_jsx_runtime.jsx)(
    "audio",
    {
      className: "bn-audio",
      src: t.loadingState === "loading" ? e.block.props.url : t.downloadUrl,
      controls: true,
      contentEditable: false,
      draggable: false
    }
  );
};
var fi = (e) => {
  if (!e.block.props.url)
    return (0, import_jsx_runtime.jsx)("p", { children: "Add audio" });
  const t = e.block.props.showPreview ? (0, import_jsx_runtime.jsx)("audio", { src: e.block.props.url }) : (0, import_jsx_runtime.jsx)("a", { href: e.block.props.url, children: e.block.props.name || e.block.props.url });
  return e.block.props.caption ? e.block.props.showPreview ? (0, import_jsx_runtime.jsx)(Pe2, { caption: e.block.props.caption, children: t }) : (0, import_jsx_runtime.jsx)(he, { caption: e.block.props.caption, children: t }) : t;
};
var hi = (e) => (0, import_jsx_runtime.jsx)(
  De,
  {
    ...e,
    buttonText: e.editor.dictionary.file_blocks.audio.add_button_text,
    buttonIcon: (0, import_jsx_runtime.jsx)(Mt, { size: 24 }),
    children: (0, import_jsx_runtime.jsx)(mi, { ...e })
  }
);
var zi = fe(Ur, {
  render: hi,
  parse: $r,
  toExternalHTML: fi
});
var gi = (e) => {
  if (!e.block.props.url)
    return (0, import_jsx_runtime.jsx)("p", { children: "Add file" });
  const t = (0, import_jsx_runtime.jsx)("a", { href: e.block.props.url, children: e.block.props.name || e.block.props.url });
  return e.block.props.caption ? (0, import_jsx_runtime.jsx)(he, { caption: e.block.props.caption, children: t }) : t;
};
var bi = (e) => (0, import_jsx_runtime.jsx)(De, { ...e });
var ji = fe(Zr, {
  render: bi,
  parse: es,
  toExternalHTML: gi
});
var It = (e) => {
  const [t, n] = (0, import_react3.useState)(void 0), [o, r] = (0, import_react3.useState)(
    e.block.props.previewWidth
  ), [l, c] = (0, import_react3.useState)(false), s = (0, import_react3.useRef)(null);
  (0, import_react3.useEffect)(() => {
    const h = (p) => {
      var S, I;
      let V;
      e.block.props.textAlignment === "center" ? t.handleUsed === "left" ? V = t.initialWidth + (t.initialClientX - p.clientX) * 2 : V = t.initialWidth + (p.clientX - t.initialClientX) * 2 : t.handleUsed === "left" ? V = t.initialWidth + t.initialClientX - p.clientX : V = t.initialWidth + p.clientX - t.initialClientX, r(
        Math.min(
          Math.max(V, 64),
          ((I = (S = e.editor.domElement) == null ? void 0 : S.firstElementChild) == null ? void 0 : I.clientWidth) || Number.MAX_VALUE
        )
      );
    }, H = () => {
      n(void 0), e.editor.updateBlock(e.block, {
        props: {
          previewWidth: o
        }
      });
    };
    return t && (window.addEventListener("mousemove", h), window.addEventListener("mouseup", H)), () => {
      window.removeEventListener("mousemove", h), window.removeEventListener("mouseup", H);
    };
  }, [e, t, o]);
  const d = (0, import_react3.useCallback)(() => {
    e.editor.isEditable && c(true);
  }, [e.editor.isEditable]), a = (0, import_react3.useCallback)(() => {
    c(false);
  }, []), u = (0, import_react3.useCallback)(
    (h) => {
      h.preventDefault(), n({
        handleUsed: "left",
        initialWidth: s.current.clientWidth,
        initialClientX: h.clientX
      });
    },
    []
  ), m = (0, import_react3.useCallback)(
    (h) => {
      h.preventDefault(), n({
        handleUsed: "right",
        initialWidth: s.current.clientWidth,
        initialClientX: h.clientX
      });
    },
    []
  ), f = _t(e.block.id);
  return (0, import_jsx_runtime.jsx)(
    De,
    {
      ...e,
      onMouseEnter: d,
      onMouseLeave: a,
      style: e.block.props.url && !f && e.block.props.showPreview ? {
        width: o ? `${o}px` : "fit-content"
      } : void 0,
      children: (0, import_jsx_runtime.jsxs)("div", { className: "bn-visual-media-wrapper", ref: s, children: [
        e.children,
        (l || t) && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
          (0, import_jsx_runtime.jsx)(
            "div",
            {
              className: "bn-resize-handle",
              style: { left: "4px" },
              onMouseDown: u
            }
          ),
          (0, import_jsx_runtime.jsx)(
            "div",
            {
              className: "bn-resize-handle",
              style: { right: "4px" },
              onMouseDown: m
            }
          )
        ] })
      ] })
    }
  );
};
var Ci = (e) => {
  const t = Ne(e.block.props.url);
  return (0, import_jsx_runtime.jsx)(
    "img",
    {
      className: "bn-visual-media",
      src: t.loadingState === "loading" ? e.block.props.url : t.downloadUrl,
      alt: e.block.props.caption || "BlockNote image",
      contentEditable: false,
      draggable: false
    }
  );
};
var pi = (e) => {
  if (!e.block.props.url)
    return (0, import_jsx_runtime.jsx)("p", { children: "Add image" });
  const t = e.block.props.showPreview ? (0, import_jsx_runtime.jsx)(
    "img",
    {
      src: e.block.props.url,
      alt: e.block.props.name || e.block.props.caption || "BlockNote image",
      width: e.block.props.previewWidth
    }
  ) : (0, import_jsx_runtime.jsx)("a", { href: e.block.props.url, children: e.block.props.name || e.block.props.url });
  return e.block.props.caption ? e.block.props.showPreview ? (0, import_jsx_runtime.jsx)(Pe2, { caption: e.block.props.caption, children: t }) : (0, import_jsx_runtime.jsx)(he, { caption: e.block.props.caption, children: t }) : t;
};
var ki = (e) => (0, import_jsx_runtime.jsx)(
  It,
  {
    ...e,
    buttonText: e.editor.dictionary.file_blocks.image.add_button_text,
    buttonIcon: (0, import_jsx_runtime.jsx)(Ht, { size: 24 }),
    children: (0, import_jsx_runtime.jsx)(Ci, { ...e })
  }
);
var Wi = fe(as, {
  render: ki,
  parse: ls,
  toExternalHTML: pi
});
function wi(e) {
  return k({ attr: { viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }, child: [{ tag: "path", attr: { d: "M14 3v4a1 1 0 0 0 1 1h4" }, child: [] }, { tag: "path", attr: { d: "M19 18v1a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-1" }, child: [] }, { tag: "path", attr: { d: "M3 14h3m4.5 0h3m4.5 0h3" }, child: [] }, { tag: "path", attr: { d: "M5 10v-5a2 2 0 0 1 2 -2h7l5 5v2" }, child: [] }] })(e);
}
var vi = {
  page_break: wi
};
function $i(e) {
  return Fc(e).map((t) => {
    const n = vi[t.key];
    return {
      ...t,
      icon: (0, import_jsx_runtime.jsx)(n, { size: 18 })
    };
  });
}
var Hi = (e) => {
  const t = Ne(e.block.props.url);
  return (0, import_jsx_runtime.jsx)(
    "video",
    {
      className: "bn-visual-media",
      src: t.loadingState === "loading" ? e.block.props.url : t.downloadUrl,
      controls: true,
      contentEditable: false,
      draggable: false
    }
  );
};
var Mi = (e) => {
  if (!e.block.props.url)
    return (0, import_jsx_runtime.jsx)("p", { children: "Add video" });
  const t = e.block.props.showPreview ? (0, import_jsx_runtime.jsx)("video", { src: e.block.props.url }) : (0, import_jsx_runtime.jsx)("a", { href: e.block.props.url, children: e.block.props.name || e.block.props.url });
  return e.block.props.caption ? e.block.props.showPreview ? (0, import_jsx_runtime.jsx)(Pe2, { caption: e.block.props.caption, children: t }) : (0, import_jsx_runtime.jsx)(he, { caption: e.block.props.caption, children: t }) : t;
};
var xi = (e) => (0, import_jsx_runtime.jsx)(
  It,
  {
    ...e,
    buttonText: e.editor.dictionary.file_blocks.video.add_button_text,
    buttonIcon: (0, import_jsx_runtime.jsx)(po, { size: 24 }),
    children: (0, import_jsx_runtime.jsx)(Hi, { ...e })
  }
);
var qi = fe(Os, {
  render: xi,
  parse: Vs,
  toExternalHTML: Mi
});
var Ki = (e) => {
  const [t, n] = (0, import_react3.useState)("none"), o = (0, import_react3.useRef)(null), r = b(), l = D(
    r.formattingToolbar.onUpdate.bind(r.formattingToolbar)
  ), c = (0, import_react3.useMemo)(() => ({
    display: "flex",
    position: "fixed",
    bottom: 0,
    zIndex: 3e3,
    transform: t
  }), [t]);
  if ((0, import_react3.useEffect)(() => {
    const d = window.visualViewport;
    function a() {
      const u = document.body, m = d.offsetLeft, f = d.height - u.getBoundingClientRect().height + d.offsetTop;
      n(
        `translate(${m}px, ${f}px) scale(${1 / d.scale})`
      );
    }
    return window.visualViewport.addEventListener("scroll", a), window.visualViewport.addEventListener("resize", a), a(), () => {
      window.visualViewport.removeEventListener("scroll", a), window.visualViewport.removeEventListener("resize", a);
    };
  }, []), !l)
    return null;
  if (!l.show && o.current)
    return (0, import_jsx_runtime.jsx)(
      "div",
      {
        ref: o,
        style: c,
        dangerouslySetInnerHTML: { __html: o.current.innerHTML }
      }
    );
  const s = e.formattingToolbar || St;
  return (0, import_jsx_runtime.jsx)("div", { ref: o, style: c, children: (0, import_jsx_runtime.jsx)(s, {}) });
};
var yi = import_react3.default.memo(
  ({
    thread: e,
    selectedThreadId: t,
    editor: n,
    maxCommentsBeforeCollapse: o,
    referenceText: r
  }) => {
    const l = (0, import_react3.useCallback)(
      (s) => {
        var d;
        s.target.closest(".bn-action-toolbar") || (d = n.comments) == null || d.selectThread(e.id);
      },
      [n.comments, e.id]
    ), c = (0, import_react3.useCallback)(
      (s) => {
        var u;
        if (!s.relatedTarget || s.relatedTarget.closest(".bn-action-toolbar"))
          return;
        const d = s.target instanceof Node ? s.target : null, a = s.relatedTarget instanceof Node ? s.relatedTarget.closest(".bn-thread") : null;
        (!d || !a || !a.contains(d)) && ((u = n.comments) == null || u.selectThread(void 0));
      },
      [n.comments]
    );
    return (0, import_jsx_runtime.jsx)(
      xt,
      {
        thread: e,
        selected: e.id === t,
        referenceText: r,
        maxCommentsBeforeCollapse: o,
        onFocus: l,
        onBlur: c,
        tabIndex: 0
      }
    );
  }
);
function Bi(e, t, n) {
  if (t === "recent-activity")
    return e.sort(
      (o, r) => r.comments[r.comments.length - 1].createdAt.getTime() - o.comments[o.comments.length - 1].createdAt.getTime()
    );
  if (t === "oldest")
    return e.sort(
      (o, r) => o.createdAt.getTime() - r.createdAt.getTime()
    );
  if (t === "position")
    return e.sort((o, r) => {
      var s, d;
      const l = ((s = n == null ? void 0 : n.get(o.id)) == null ? void 0 : s.from) || Number.MAX_VALUE, c = ((d = n == null ? void 0 : n.get(r.id)) == null ? void 0 : d.from) || Number.MAX_VALUE;
      return l - c;
    });
  throw new j(t);
}
function tt(e, t) {
  return e.transact((n) => {
    if (!t)
      return "Original content deleted";
    if (n.doc.nodeSize < t.to)
      return "";
    const o = n.doc.textBetween(
      t.from,
      t.to
    );
    return o.length > 15 ? `${o.slice(0, 15)}…` : o;
  });
}
function Xi(e) {
  const t = b();
  if (!t.comments)
    throw new Error("Comments plugin not found");
  const n = D(
    t.comments.onUpdate.bind(t.comments)
  ), o = n == null ? void 0 : n.selectedThreadId, r = yt(t), l = (0, import_react3.useMemo)(() => {
    const c = Array.from(r.values()), s = Bi(
      c,
      e.sort || "position",
      n == null ? void 0 : n.threadPositions
    ), d = [];
    for (const a of s)
      a.resolved ? (e.filter === "resolved" || e.filter === "all") && d.push({
        thread: a,
        referenceText: tt(
          t,
          n == null ? void 0 : n.threadPositions.get(a.id)
        )
      }) : (e.filter === "open" || e.filter === "all") && d.push({
        thread: a,
        referenceText: tt(
          t,
          n == null ? void 0 : n.threadPositions.get(a.id)
        )
      });
    return d;
  }, [r, n == null ? void 0 : n.threadPositions, e.filter, e.sort, t]);
  return (0, import_jsx_runtime.jsx)("div", { className: "bn-threads-sidebar", children: l.map((c) => (0, import_jsx_runtime.jsx)(
    yi,
    {
      thread: c.thread,
      selectedThreadId: o,
      editor: t,
      referenceText: c.referenceText,
      maxCommentsBeforeCollapse: e.maxCommentsBeforeCollapse
    },
    c.thread.id
  )) });
}
function Yi(e) {
  const t = O();
  if (e || (e = t == null ? void 0 : t.editor), !e)
    throw new Error(
      "'editor' is required, either from BlockNoteContext or as a function argument"
    );
  const n = e, [o, r] = (0, import_react3.useState)(() => n.getActiveStyles());
  return ue(() => {
    r(n.getActiveStyles());
  }, n), se(() => {
    r(n.getActiveStyles());
  }, n), o;
}
function Vi() {
  const [, e] = (0, import_react3.useState)(0);
  return () => e((t) => t + 1);
}
var Ji = (e) => {
  const t = Vi();
  (0, import_react3.useEffect)(() => {
    const n = () => {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          t();
        });
      });
    };
    return e.on("transaction", n), () => {
      e.off("transaction", n);
    };
  }, [e]);
};
function Si(e) {
  return e.currentTarget instanceof HTMLElement && e.relatedTarget instanceof HTMLElement ? e.currentTarget.contains(e.relatedTarget) : false;
}
function Qi({
  onBlur: e,
  onFocus: t
} = {}) {
  const n = (0, import_react3.useRef)(null), [o, r] = (0, import_react3.useState)(false), l = (0, import_react3.useRef)(false), c = (a) => {
    r(a), l.current = a;
  }, s = (a) => {
    l.current || (c(true), t == null || t(a));
  }, d = (a) => {
    l.current && !Si(a) && (c(false), e == null || e(a));
  };
  return (0, import_react3.useEffect)(() => {
    const a = n.current;
    if (a)
      return a.addEventListener("focusin", s), a.addEventListener("focusout", d), () => {
        a == null || a.removeEventListener("focusin", s), a == null || a.removeEventListener("focusout", d);
      };
  }, [s, d]), { ref: n, focused: o };
}
function Ti(e) {
  return (
    // Creates inline content section element
    (0, import_jsx_runtime.jsx)(
      NodeViewWrapper,
      {
        as: "span",
        className: "bn-inline-content-section",
        "data-inline-content-type": e.inlineContentType,
        ...Object.fromEntries(
          Object.entries(e.inlineContentProps).filter(([t, n]) => {
            const o = e.propSchema[t];
            return n !== o.default;
          }).map(([t, n]) => [Ve(t), n])
        ),
        children: e.children
      }
    )
  );
}
function el(e, t) {
  const n = q({
    name: e.type,
    inline: true,
    group: "inline",
    selectable: e.content === "styled",
    atom: e.content === "none",
    content: e.content === "styled" ? "inline*" : "",
    addAttributes() {
      return Be(e.propSchema);
    },
    addKeyboardShortcuts() {
      return ur(e);
    },
    parseHTML() {
      return fr(e);
    },
    renderHTML({ node: o }) {
      const r = this.options.editor, l = it(
        o,
        r.schema.inlineContentSchema,
        r.schema.styleSchema
      ), c = t.render, s = ie(
        (d) => (0, import_jsx_runtime.jsx)(
          c,
          {
            inlineContent: l,
            updateInlineContent: () => {
            },
            contentRef: d
          }
        ),
        r
      );
      return Vt(
        s,
        e.type,
        o.attrs,
        e.propSchema
      );
    },
    // TODO: needed?
    addNodeView() {
      const o = this.options.editor;
      return (r) => ReactNodeViewRenderer(
        (l) => {
          const c = useReactNodeView().nodeViewContentRef;
          if (!c)
            throw new Error("nodeViewContentRef is not set");
          const s = t.render;
          return (0, import_jsx_runtime.jsx)(
            Ti,
            {
              inlineContentProps: l.node.attrs,
              inlineContentType: e.type,
              propSchema: e.propSchema,
              children: (0, import_jsx_runtime.jsx)(
                s,
                {
                  contentRef: c,
                  inlineContent: it(
                    l.node,
                    o.schema.inlineContentSchema,
                    o.schema.styleSchema
                  ),
                  updateInlineContent: (d) => {
                    const a = F(
                      [d],
                      o.pmSchema
                    );
                    o.transact(
                      (u) => u.replaceWith(
                        l.getPos(),
                        l.getPos() + l.node.nodeSize,
                        a
                      )
                    );
                  }
                }
              )
            }
          );
        },
        {
          className: "bn-ic-react-node-view-renderer",
          as: "span"
          // contentDOMElementTag: "span", (requires tt upgrade)
        }
      )(r);
    }
  });
  return pr(e, {
    node: n
  });
}
var Y;
var He;
var Li = class {
  constructor({
    mark: t,
    view: n,
    inline: o,
    options: r,
    editor: l
    // BlockNote specific
  }) {
    Ze(this, Y);
    L(this, "dom");
    L(this, "contentDOM");
    L(this, "mark");
    L(this, "view");
    L(this, "inline");
    L(this, "options");
    L(this, "editor");
    L(this, "shouldIgnoreMutation", (t2) => !this.dom || !this.contentDOM ? true : t2.type === "selection" ? false : this.contentDOM === t2.target && t2.type === "attributes" ? true : !this.contentDOM.contains(t2.target));
    L(this, "ignoreMutation", (t2) => {
      if (!this.dom || !this.contentDOM) return true;
      let n2;
      const o2 = this.options.ignoreMutation;
      return o2 && (n2 = o2(t2)), typeof n2 != "boolean" && (n2 = this.shouldIgnoreMutation(t2)), n2;
    });
    this.mark = t, this.view = n, this.inline = o, this.options = r, this.editor = l, this.dom = this.createDOM(r.as), this.contentDOM = r.contentAs ? this.createContentDOM(r.contentAs) : void 0, this.dom.setAttribute("data-mark-view-root", "true"), this.contentDOM && (this.contentDOM.setAttribute("data-mark-view-content", "true"), this.contentDOM.style.whiteSpace = "inherit");
  }
  createDOM(t) {
    return ge(this, Y, He).call(this, t);
  }
  createContentDOM(t) {
    return ge(this, Y, He).call(this, t);
  }
  get component() {
    return this.options.component;
  }
  destroy() {
    var t, n, o;
    (n = (t = this.options).destroy) == null || n.call(t), this.dom.remove(), (o = this.contentDOM) == null || o.remove();
  }
};
Y = /* @__PURE__ */ new WeakSet(), He = function(t) {
  const { inline: n, mark: o } = this;
  return t == null ? document.createElement(n ? "span" : "div") : t instanceof HTMLElement ? t : t instanceof Function ? t(o) : document.createElement(t);
};
var Ei = class extends Li {
  constructor() {
    super(...arguments);
    L(this, "id", Math.floor(Math.random() * 4294967295).toString());
    L(this, "context", {
      contentRef: (n) => {
        n && this.contentDOM && n.firstChild !== this.contentDOM && n.appendChild(this.contentDOM);
      },
      view: this.view,
      mark: this.mark
    });
    L(this, "updateContext", () => {
      Object.assign(this.context, {
        mark: this.mark
      });
    });
    L(this, "render", () => {
      this.editor._tiptapEditor.contentComponent.setRenderer(
        this.id,
        this.renderer()
      );
    });
    L(this, "destroy", () => {
      super.destroy(), this.editor._tiptapEditor.contentComponent.removeRenderer(this.id);
    });
    L(this, "renderer", () => {
      const n = this.component, o = {};
      return this.mark.attrs.stringValue && (o.value = this.mark.attrs.stringValue), {
        reactElement: (
          // <markViewContext.Provider value={this.context}>
          (0, import_jsx_runtime.jsx)(n, { contentRef: this.context.contentRef, ...o })
        ),
        element: this.dom
      };
    });
  }
};
function tl(e, t) {
  const n = Mark.create({
    name: e.type,
    addAttributes() {
      return mr(e.propSchema);
    },
    parseHTML() {
      return br(e);
    },
    renderHTML({ mark: o }) {
      const r = {};
      e.propSchema === "string" && (r.value = o.attrs.stringValue);
      const l = t.render, c = ie(
        (s) => (0, import_jsx_runtime.jsx)(l, { ...r, contentRef: s }),
        this.options.editor
      );
      return gr(
        c,
        e.type,
        o.attrs.stringValue,
        e.propSchema
      );
    }
  });
  return n.config.addMarkView = (o) => (r, l) => {
    const c = new Ei({
      editor: o,
      inline: true,
      mark: r,
      options: {
        component: t.render,
        contentAs: "span"
      },
      view: l
    });
    return c.render(), c;
  }, yn(e, {
    mark: n
  });
}
function nl(e, t) {
  const n = e.getBoundingClientRect(), o = t.getBoundingClientRect(), r = n.top < o.top, l = n.bottom > o.bottom;
  return r && l ? "both" : r ? "top" : l ? "bottom" : "none";
}

export {
  dt,
  O,
  b,
  se,
  Pn,
  G,
  D,
  On,
  C,
  de,
  Fi,
  M,
  ue,
  An,
  Gn,
  To,
  Ie2 as Ie,
  Eo,
  Ro,
  xt,
  yt,
  _o,
  Io,
  No,
  Bt,
  Po,
  U,
  Do,
  _,
  ne,
  Fo,
  Vt2 as Vt,
  Go,
  Uo,
  zo,
  jo,
  Wo,
  $o,
  qo,
  Ko,
  Xo,
  Yo,
  Jo,
  Qo,
  er,
  tr,
  be,
  or,
  St,
  rr,
  ir2 as ir,
  lr,
  cr2 as cr,
  ar2 as ar,
  sr,
  ur2 as ur,
  fr2 as fr,
  hr,
  gr2 as gr,
  br2 as br,
  Cr,
  pr2 as pr,
  kr,
  wr,
  vr,
  Lt,
  Et,
  Mr,
  xr,
  yr,
  Vr,
  Tr,
  Lr,
  Rr,
  _r,
  Nr,
  Je,
  Pr,
  Fr,
  Ar,
  Gr,
  Wr,
  $r2 as $r,
  qr,
  Kr,
  Xr,
  Yr,
  Jr,
  Qr,
  Ui,
  li,
  pe,
  fe,
  Ne,
  Pe2 as Pe,
  ai,
  si,
  _t,
  di,
  ui,
  De,
  he,
  mi,
  fi,
  hi,
  zi,
  gi,
  bi,
  ji,
  It,
  Ci,
  pi,
  ki,
  Wi,
  $i,
  Hi,
  Mi,
  xi,
  qi,
  Ki,
  tt,
  Xi,
  Yi,
  Ji,
  Qi,
  Ti,
  el,
  tl,
  nl
};
/*! Bundled license information:

@tiptap/react/dist/index.js:
  (**
   * @license React
   * use-sync-external-store-shim.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@tiptap/react/dist/index.js:
  (**
   * @license React
   * use-sync-external-store-shim.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@tiptap/react/dist/index.js:
  (**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@tiptap/react/dist/index.js:
  (**
   * @license React
   * use-sync-external-store-shim/with-selector.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-TFUXZJU5.js.map

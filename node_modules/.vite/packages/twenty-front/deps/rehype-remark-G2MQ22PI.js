import {
  rehypeMinifyWhitespace,
  toText
} from "./chunk-EQ6OFAN5.js";
import {
  phrasing as phrasing2
} from "./chunk-VRPX3MPE.js";
import {
  esm_default
} from "./chunk-FPMN7SAE.js";
import {
  phrasing
} from "./chunk-M2KGN5WX.js";
import {
  visit
} from "./chunk-TFQJNSQ7.js";
import {
  EXIT,
  SKIP
} from "./chunk-OBJQZ5YF.js";
import {
  whitespace
} from "./chunk-4IFNTA3D.js";
import "./chunk-XPZLJQLW.js";

// node_modules/hast-util-to-mdast/node_modules/unist-util-position/lib/index.js
var pointEnd = point("end");
var pointStart = point("start");
function point(type) {
  return point2;
  function point2(node2) {
    const point3 = node2 && node2.position && node2.position[type] || {};
    if (typeof point3.line === "number" && point3.line > 0 && typeof point3.column === "number" && point3.column > 0) {
      return {
        line: point3.line,
        column: point3.column,
        offset: typeof point3.offset === "number" && point3.offset > -1 ? point3.offset : void 0
      };
    }
  }
}
function position(node2) {
  const start = pointStart(node2);
  const end = pointEnd(node2);
  if (start && end) {
    return { start, end };
  }
}

// node_modules/hast-util-to-mdast/lib/handlers/a.js
function a(state, node2) {
  const properties = node2.properties || {};
  const children = (
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = {
    type: "link",
    url: state.resolve(String(properties.href || "") || null),
    title: properties.title ? String(properties.title) : null,
    children
  };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/base.js
function base(state, node2) {
  if (!state.baseFound) {
    state.frozenBaseUrl = String(node2.properties && node2.properties.href || "") || void 0;
    state.baseFound = true;
  }
}

// node_modules/hast-util-to-mdast/lib/handlers/blockquote.js
function blockquote(state, node2) {
  const result = { type: "blockquote", children: state.toFlow(state.all(node2)) };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/br.js
function br(state, node2) {
  const result = { type: "break" };
  state.patch(node2, result);
  return result;
}

// node_modules/trim-trailing-lines/index.js
function trimTrailingLines(value) {
  const input2 = String(value);
  let end = input2.length;
  while (end > 0) {
    const code2 = input2.codePointAt(end - 1);
    if (code2 !== void 0 && (code2 === 10 || code2 === 13)) {
      end--;
    } else {
      break;
    }
  }
  return input2.slice(0, end);
}

// node_modules/hast-util-to-mdast/lib/handlers/code.js
var prefix = "language-";
function code(state, node2) {
  const children = node2.children;
  let index = -1;
  let classList;
  let lang;
  if (node2.tagName === "pre") {
    while (++index < children.length) {
      const child = children[index];
      if (child.type === "element" && child.tagName === "code" && child.properties && child.properties.className && Array.isArray(child.properties.className)) {
        classList = child.properties.className;
        break;
      }
    }
  }
  if (classList) {
    index = -1;
    while (++index < classList.length) {
      if (String(classList[index]).slice(0, prefix.length) === prefix) {
        lang = String(classList[index]).slice(prefix.length);
        break;
      }
    }
  }
  const result = {
    type: "code",
    lang: lang || null,
    meta: null,
    value: trimTrailingLines(toText(node2))
  };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/comment.js
function comment(state, node2) {
  const result = {
    type: "html",
    value: "<!--" + node2.value + "-->"
  };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/del.js
function del(state, node2) {
  const children = (
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = { type: "delete", children };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/util/list-items-spread.js
function listItemsSpread(children) {
  let index = -1;
  if (children.length > 1) {
    while (++index < children.length) {
      if (children[index].spread) {
        return true;
      }
    }
  }
  return false;
}

// node_modules/hast-util-to-mdast/lib/handlers/dl.js
function dl(state, node2) {
  const clean = [];
  const groups = [];
  let index = -1;
  while (++index < node2.children.length) {
    const child = node2.children[index];
    if (child.type === "element" && child.tagName === "div") {
      clean.push(...child.children);
    } else {
      clean.push(child);
    }
  }
  let group = { definitions: [], titles: [] };
  index = -1;
  while (++index < clean.length) {
    const child = clean[index];
    if (child.type === "element" && child.tagName === "dt") {
      const previous = clean[index - 1];
      if (previous && previous.type === "element" && previous.tagName === "dd") {
        groups.push(group);
        group = { definitions: [], titles: [] };
      }
      group.titles.push(child);
    } else {
      group.definitions.push(child);
    }
  }
  groups.push(group);
  index = -1;
  const content = [];
  while (++index < groups.length) {
    const result = [
      ...handle(state, groups[index].titles),
      ...handle(state, groups[index].definitions)
    ];
    if (result.length > 0) {
      content.push({
        type: "listItem",
        spread: result.length > 1,
        checked: null,
        children: result
      });
    }
  }
  if (content.length > 0) {
    const result = {
      type: "list",
      ordered: false,
      start: null,
      spread: listItemsSpread(content),
      children: content
    };
    state.patch(node2, result);
    return result;
  }
}
function handle(state, children) {
  const nodes = state.all({ type: "root", children });
  const listItems = state.toSpecificContent(nodes, create);
  if (listItems.length === 0) {
    return [];
  }
  if (listItems.length === 1) {
    return listItems[0].children;
  }
  return [
    {
      type: "list",
      ordered: false,
      start: null,
      spread: listItemsSpread(listItems),
      children: listItems
    }
  ];
}
function create() {
  return { type: "listItem", spread: false, checked: null, children: [] };
}

// node_modules/hast-util-to-mdast/lib/handlers/em.js
function em(state, node2) {
  const children = (
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = { type: "emphasis", children };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/util/drop-surrounding-breaks.js
function dropSurroundingBreaks(nodes) {
  let start = 0;
  let end = nodes.length;
  while (start < end && nodes[start].type === "break") start++;
  while (end > start && nodes[end - 1].type === "break") end--;
  return start === 0 && end === nodes.length ? nodes : nodes.slice(start, end);
}

// node_modules/hast-util-to-mdast/lib/handlers/heading.js
function heading(state, node2) {
  const depth = (
    /** @type {Heading['depth']} */
    /* c8 ignore next */
    Number(node2.tagName.charAt(1)) || 1
  );
  const children = dropSurroundingBreaks(
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = { type: "heading", depth, children };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/hr.js
function hr(state, node2) {
  const result = { type: "thematicBreak" };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/iframe.js
function iframe(state, node2) {
  const properties = node2.properties || {};
  const source = String(properties.src || "");
  const title = String(properties.title || "");
  if (source && title) {
    const result = {
      type: "link",
      title: null,
      url: state.resolve(source),
      children: [{ type: "text", value: title }]
    };
    state.patch(node2, result);
    return result;
  }
}

// node_modules/hast-util-to-mdast/lib/handlers/img.js
function img(state, node2) {
  const properties = node2.properties || {};
  const result = {
    type: "image",
    url: state.resolve(String(properties.src || "") || null),
    title: properties.title ? String(properties.title) : null,
    alt: properties.alt ? String(properties.alt) : ""
  };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/inline-code.js
function inlineCode(state, node2) {
  const result = { type: "inlineCode", value: toText(node2) };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/util/find-selected-options.js
function findSelectedOptions(node2, explicitProperties) {
  const selectedOptions = [];
  const values = [];
  const properties = explicitProperties || node2.properties || {};
  const options = findOptions(node2);
  const size = Math.min(Number.parseInt(String(properties.size), 10), 0) || (properties.multiple ? 4 : 1);
  let index = -1;
  while (++index < options.length) {
    const option = options[index];
    if (option && option.properties && option.properties.selected) {
      selectedOptions.push(option);
    }
  }
  const list2 = selectedOptions.length > 0 ? selectedOptions : options;
  const max = Math.min(list2.length, size);
  index = -1;
  while (++index < max) {
    const option = list2[index];
    const properties2 = option.properties || {};
    const content = toText(option);
    const label = content || String(properties2.label || "");
    const value = String(properties2.value || "") || content;
    values.push([value, label === value ? void 0 : label]);
  }
  return values;
}
function findOptions(node2) {
  const results = [];
  let index = -1;
  while (++index < node2.children.length) {
    const child = node2.children[index];
    if ("children" in child && Array.isArray(child.children)) {
      results.push(...findOptions(child));
    }
    if (child.type === "element" && child.tagName === "option" && (!child.properties || !child.properties.disabled)) {
      results.push(child);
    }
  }
  return results;
}

// node_modules/hast-util-to-mdast/lib/handlers/input.js
var defaultChecked = "[x]";
var defaultUnchecked = "[ ]";
function input(state, node2) {
  const properties = node2.properties || {};
  const value = String(properties.value || properties.placeholder || "");
  if (properties.disabled || properties.type === "hidden" || properties.type === "file") {
    return;
  }
  if (properties.type === "checkbox" || properties.type === "radio") {
    const result2 = {
      type: "text",
      value: properties.checked ? state.options.checked || defaultChecked : state.options.unchecked || defaultUnchecked
    };
    state.patch(node2, result2);
    return result2;
  }
  if (properties.type === "image") {
    const alt = properties.alt || value;
    if (alt) {
      const result2 = {
        type: "image",
        url: state.resolve(String(properties.src || "") || null),
        title: String(properties.title || "") || null,
        alt: String(alt)
      };
      state.patch(node2, result2);
      return result2;
    }
    return;
  }
  let values = [];
  if (value) {
    values = [[value, void 0]];
  } else if (
    // `list` is not supported on these types:
    properties.type !== "button" && properties.type !== "file" && properties.type !== "password" && properties.type !== "reset" && properties.type !== "submit" && properties.list
  ) {
    const list2 = String(properties.list);
    const datalist = state.elementById.get(list2);
    if (datalist && datalist.tagName === "datalist") {
      values = findSelectedOptions(datalist, properties);
    }
  }
  if (values.length === 0) {
    return;
  }
  if (properties.type === "password") {
    values[0] = ["•".repeat(values[0][0].length), void 0];
  }
  if (properties.type === "email" || properties.type === "url") {
    const results = [];
    let index2 = -1;
    while (++index2 < values.length) {
      const value2 = state.resolve(values[index2][0]);
      const result2 = {
        type: "link",
        title: null,
        url: properties.type === "email" ? "mailto:" + value2 : value2,
        children: [{ type: "text", value: values[index2][1] || value2 }]
      };
      results.push(result2);
      if (index2 !== values.length - 1) {
        results.push({ type: "text", value: ", " });
      }
    }
    return results;
  }
  const texts = [];
  let index = -1;
  while (++index < values.length) {
    texts.push(
      values[index][1] ? values[index][1] + " (" + values[index][0] + ")" : values[index][0]
    );
  }
  const result = { type: "text", value: texts.join(", ") };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/li.js
function li(state, node2) {
  const { rest, checkbox } = extractLeadingCheckbox(node2);
  const checked = checkbox ? Boolean(checkbox.properties.checked) : null;
  const spread = spreadout(rest);
  const children = state.toFlow(state.all(rest));
  const result = { type: "listItem", spread, checked, children };
  state.patch(node2, result);
  return result;
}
function spreadout(node2) {
  let index = -1;
  let seenFlow = false;
  while (++index < node2.children.length) {
    const child = node2.children[index];
    if (child.type === "element") {
      if (phrasing(child)) continue;
      if (child.tagName === "p" || seenFlow || spreadout(child)) {
        return true;
      }
      seenFlow = true;
    }
  }
  return false;
}
function extractLeadingCheckbox(node2) {
  const head = node2.children[0];
  if (head && head.type === "element" && head.tagName === "input" && head.properties && (head.properties.type === "checkbox" || head.properties.type === "radio")) {
    const rest = { ...node2, children: node2.children.slice(1) };
    return { checkbox: head, rest };
  }
  if (head && head.type === "element" && head.tagName === "p") {
    const { checkbox, rest: restHead } = extractLeadingCheckbox(head);
    if (checkbox) {
      const rest = { ...node2, children: [restHead, ...node2.children.slice(1)] };
      return { checkbox, rest };
    }
  }
  return { checkbox: void 0, rest: node2 };
}

// node_modules/hast-util-to-mdast/lib/handlers/list.js
function list(state, node2) {
  const ordered = node2.tagName === "ol";
  const children = state.toSpecificContent(state.all(node2), create2);
  let start = null;
  if (ordered) {
    start = node2.properties && node2.properties.start ? Number.parseInt(String(node2.properties.start), 10) : 1;
  }
  const result = {
    type: "list",
    ordered,
    start,
    spread: listItemsSpread(children),
    children
  };
  state.patch(node2, result);
  return result;
}
function create2() {
  return { type: "listItem", spread: false, checked: null, children: [] };
}

// node_modules/hast-util-to-mdast/node_modules/mdast-util-to-string/lib/index.js
var emptyOptions = {};
function toString(value, options) {
  const settings = options || emptyOptions;
  const includeImageAlt = typeof settings.includeImageAlt === "boolean" ? settings.includeImageAlt : true;
  const includeHtml = typeof settings.includeHtml === "boolean" ? settings.includeHtml : true;
  return one(value, includeImageAlt, includeHtml);
}
function one(value, includeImageAlt, includeHtml) {
  if (node(value)) {
    if ("value" in value) {
      return value.type === "html" && !includeHtml ? "" : value.value;
    }
    if (includeImageAlt && "alt" in value && value.alt) {
      return value.alt;
    }
    if ("children" in value) {
      return all(value.children, includeImageAlt, includeHtml);
    }
  }
  if (Array.isArray(value)) {
    return all(value, includeImageAlt, includeHtml);
  }
  return "";
}
function all(values, includeImageAlt, includeHtml) {
  const result = [];
  let index = -1;
  while (++index < values.length) {
    result[index] = one(values[index], includeImageAlt, includeHtml);
  }
  return result.join("");
}
function node(value) {
  return Boolean(value && typeof value === "object");
}

// node_modules/hast-util-to-mdast/lib/util/wrap.js
function wrapNeeded(nodes) {
  let index = -1;
  while (++index < nodes.length) {
    const node2 = nodes[index];
    if (!phrasing3(node2) || "children" in node2 && wrapNeeded(node2.children)) {
      return true;
    }
  }
  return false;
}
function wrap(nodes) {
  return runs(nodes, onphrasing, function(d) {
    return d;
  });
  function onphrasing(nodes2) {
    return nodes2.every(function(d) {
      return d.type === "text" ? whitespace(d.value) : false;
    }) ? [] : [{ type: "paragraph", children: dropSurroundingBreaks(nodes2) }];
  }
}
function split(node2) {
  return runs(node2.children, onphrasing, onnonphrasing);
  function onphrasing(nodes) {
    const newParent = cloneWithoutChildren(node2);
    newParent.children = nodes;
    return [newParent];
  }
  function onnonphrasing(child) {
    if ("children" in child && "children" in node2) {
      const newParent = cloneWithoutChildren(node2);
      const newChild = cloneWithoutChildren(child);
      newParent.children = child.children;
      newChild.children.push(newParent);
      return newChild;
    }
    return { ...child };
  }
}
function runs(nodes, onphrasing, onnonphrasing) {
  const flattened = flatten(nodes);
  const result = [];
  let queue = [];
  let index = -1;
  while (++index < flattened.length) {
    const node2 = flattened[index];
    if (phrasing3(node2)) {
      queue.push(node2);
    } else {
      if (queue.length > 0) {
        result.push(...onphrasing(queue));
        queue = [];
      }
      result.push(onnonphrasing(node2));
    }
  }
  if (queue.length > 0) {
    result.push(...onphrasing(queue));
    queue = [];
  }
  return result;
}
function flatten(nodes) {
  const flattened = [];
  let index = -1;
  while (++index < nodes.length) {
    const node2 = nodes[index];
    if ((node2.type === "delete" || node2.type === "link") && wrapNeeded(node2.children)) {
      flattened.push(...split(node2));
    } else {
      flattened.push(node2);
    }
  }
  return flattened;
}
function phrasing3(node2) {
  const tagName = node2.data && node2.data.hName;
  return tagName ? phrasing({ type: "element", tagName, properties: {}, children: [] }) : phrasing2(node2);
}
function cloneWithoutChildren(node2) {
  return esm_default({ ...node2, children: [] });
}

// node_modules/hast-util-to-mdast/lib/handlers/media.js
function media(state, node2) {
  const properties = node2.properties || {};
  const poster = node2.tagName === "video" ? String(properties.poster || "") : "";
  let source = String(properties.src || "");
  let index = -1;
  let linkInFallbackContent = false;
  let nodes = state.all(node2);
  const fragment = { type: "root", children: nodes };
  visit(fragment, function(node3) {
    if (node3.type === "link") {
      linkInFallbackContent = true;
      return EXIT;
    }
  });
  if (linkInFallbackContent || wrapNeeded(nodes)) {
    return nodes;
  }
  while (!source && ++index < node2.children.length) {
    const child = node2.children[index];
    if (child.type === "element" && child.tagName === "source" && child.properties) {
      source = String(child.properties.src || "");
    }
  }
  if (poster) {
    const image = {
      type: "image",
      title: null,
      url: state.resolve(poster),
      alt: toString(nodes)
    };
    state.patch(node2, image);
    nodes = [image];
  }
  const children = (
    /** @type {Array<PhrasingContent>} */
    nodes
  );
  const result = {
    type: "link",
    title: properties.title ? String(properties.title) : null,
    url: state.resolve(source),
    children
  };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/p.js
function p(state, node2) {
  const children = dropSurroundingBreaks(
    // Allow potentially “invalid” nodes, they might be unknown.
    // We also support straddling later.
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  if (children.length > 0) {
    const result = { type: "paragraph", children };
    state.patch(node2, result);
    return result;
  }
}

// node_modules/hast-util-to-mdast/lib/handlers/q.js
var defaultQuotes = ['"'];
function q(state, node2) {
  const quotes = state.options.quotes || defaultQuotes;
  state.qNesting++;
  const contents = state.all(node2);
  state.qNesting--;
  const quote = quotes[state.qNesting % quotes.length];
  const head = contents[0];
  const tail = contents[contents.length - 1];
  const open = quote.charAt(0);
  const close = quote.length > 1 ? quote.charAt(1) : quote;
  if (head && head.type === "text") {
    head.value = open + head.value;
  } else {
    contents.unshift({ type: "text", value: open });
  }
  if (tail && tail.type === "text") {
    tail.value += close;
  } else {
    contents.push({ type: "text", value: close });
  }
  return contents;
}

// node_modules/hast-util-to-mdast/lib/handlers/root.js
function root(state, node2) {
  let children = state.all(node2);
  if (state.options.document || wrapNeeded(children)) {
    children = wrap(children);
  }
  const result = { type: "root", children };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/select.js
function select(state, node2) {
  const values = findSelectedOptions(node2);
  let index = -1;
  const results = [];
  while (++index < values.length) {
    const value = values[index];
    results.push(value[1] ? value[1] + " (" + value[0] + ")" : value[0]);
  }
  if (results.length > 0) {
    const result = { type: "text", value: results.join(", ") };
    state.patch(node2, result);
    return result;
  }
}

// node_modules/hast-util-to-mdast/lib/handlers/strong.js
function strong(state, node2) {
  const children = (
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = { type: "strong", children };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/table-cell.js
function tableCell(state, node2) {
  const children = (
    /** @type {Array<PhrasingContent>} */
    state.all(node2)
  );
  const result = { type: "tableCell", children };
  state.patch(node2, result);
  if (node2.properties) {
    const rowSpan = node2.properties.rowSpan;
    const colSpan = node2.properties.colSpan;
    if (rowSpan || colSpan) {
      const data = (
        /** @type {Record<string, unknown>} */
        result.data || (result.data = {})
      );
      if (rowSpan) data.hastUtilToMdastTemporaryRowSpan = rowSpan;
      if (colSpan) data.hastUtilToMdastTemporaryColSpan = colSpan;
    }
  }
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/table-row.js
function tableRow(state, node2) {
  const children = state.toSpecificContent(state.all(node2), create3);
  const result = { type: "tableRow", children };
  state.patch(node2, result);
  return result;
}
function create3() {
  return { type: "tableCell", children: [] };
}

// node_modules/hast-util-to-mdast/lib/handlers/table.js
function table(state, node2) {
  if (state.inTable) {
    const result2 = { type: "text", value: toText(node2) };
    state.patch(node2, result2);
    return result2;
  }
  state.inTable = true;
  const { align, headless } = inspect(node2);
  const rows = state.toSpecificContent(state.all(node2), createRow);
  if (headless) {
    rows.unshift(createRow());
  }
  let rowIndex = -1;
  while (++rowIndex < rows.length) {
    const row = rows[rowIndex];
    const cells = state.toSpecificContent(row.children, createCell);
    row.children = cells;
  }
  let columns = 1;
  rowIndex = -1;
  while (++rowIndex < rows.length) {
    const cells = rows[rowIndex].children;
    let cellIndex = -1;
    while (++cellIndex < cells.length) {
      const cell = cells[cellIndex];
      if (cell.data) {
        const data = (
          /** @type {Record<string, unknown>} */
          cell.data
        );
        const colSpan = Number.parseInt(String(data.hastUtilToMdastTemporaryColSpan), 10) || 1;
        const rowSpan = Number.parseInt(String(data.hastUtilToMdastTemporaryRowSpan), 10) || 1;
        if (colSpan > 1 || rowSpan > 1) {
          let otherRowIndex = rowIndex - 1;
          while (++otherRowIndex < rowIndex + rowSpan) {
            let colIndex = cellIndex - 1;
            while (++colIndex < cellIndex + colSpan) {
              if (!rows[otherRowIndex]) {
                break;
              }
              const newCells = [];
              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {
                newCells.push({ type: "tableCell", children: [] });
              }
              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells);
            }
          }
        }
        if ("hastUtilToMdastTemporaryColSpan" in cell.data)
          delete cell.data.hastUtilToMdastTemporaryColSpan;
        if ("hastUtilToMdastTemporaryRowSpan" in cell.data)
          delete cell.data.hastUtilToMdastTemporaryRowSpan;
        if (Object.keys(cell.data).length === 0) delete cell.data;
      }
    }
    if (cells.length > columns) columns = cells.length;
  }
  rowIndex = -1;
  while (++rowIndex < rows.length) {
    const cells = rows[rowIndex].children;
    let cellIndex = cells.length - 1;
    while (++cellIndex < columns) {
      cells.push({ type: "tableCell", children: [] });
    }
  }
  let alignIndex = align.length - 1;
  while (++alignIndex < columns) {
    align.push(null);
  }
  state.inTable = false;
  const result = { type: "table", align, children: rows };
  state.patch(node2, result);
  return result;
}
function inspect(node2) {
  const info = { align: [null], headless: true };
  let rowIndex = 0;
  let cellIndex = 0;
  visit(node2, function(child) {
    if (child.type === "element") {
      if (child.tagName === "table" && node2 !== child) {
        return SKIP;
      }
      if ((child.tagName === "th" || child.tagName === "td") && child.properties) {
        if (!info.align[cellIndex]) {
          const value = String(child.properties.align || "") || null;
          if (value === "center" || value === "left" || value === "right" || value === null) {
            info.align[cellIndex] = value;
          }
        }
        if (info.headless && rowIndex < 2 && child.tagName === "th") {
          info.headless = false;
        }
        cellIndex++;
      } else if (child.tagName === "thead") {
        info.headless = false;
      } else if (child.tagName === "tr") {
        rowIndex++;
        cellIndex = 0;
      }
    }
  });
  return info;
}
function createCell() {
  return { type: "tableCell", children: [] };
}
function createRow() {
  return { type: "tableRow", children: [] };
}

// node_modules/hast-util-to-mdast/lib/handlers/text.js
function text(state, node2) {
  const result = { type: "text", value: node2.value };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/textarea.js
function textarea(state, node2) {
  const result = { type: "text", value: toText(node2) };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/wbr.js
function wbr(state, node2) {
  const result = { type: "text", value: "​" };
  state.patch(node2, result);
  return result;
}

// node_modules/hast-util-to-mdast/lib/handlers/index.js
var nodeHandlers = {
  comment,
  doctype: ignore,
  root,
  text
};
var handlers = {
  // Ignore:
  applet: ignore,
  area: ignore,
  basefont: ignore,
  bgsound: ignore,
  caption: ignore,
  col: ignore,
  colgroup: ignore,
  command: ignore,
  content: ignore,
  datalist: ignore,
  dialog: ignore,
  element: ignore,
  embed: ignore,
  frame: ignore,
  frameset: ignore,
  isindex: ignore,
  keygen: ignore,
  link: ignore,
  math: ignore,
  menu: ignore,
  menuitem: ignore,
  meta: ignore,
  nextid: ignore,
  noembed: ignore,
  noframes: ignore,
  optgroup: ignore,
  option: ignore,
  param: ignore,
  script: ignore,
  shadow: ignore,
  source: ignore,
  spacer: ignore,
  style: ignore,
  svg: ignore,
  template: ignore,
  title: ignore,
  track: ignore,
  // Use children:
  abbr: all2,
  acronym: all2,
  bdi: all2,
  bdo: all2,
  big: all2,
  blink: all2,
  button: all2,
  canvas: all2,
  cite: all2,
  data: all2,
  details: all2,
  dfn: all2,
  font: all2,
  ins: all2,
  label: all2,
  map: all2,
  marquee: all2,
  meter: all2,
  nobr: all2,
  noscript: all2,
  object: all2,
  output: all2,
  progress: all2,
  rb: all2,
  rbc: all2,
  rp: all2,
  rt: all2,
  rtc: all2,
  ruby: all2,
  slot: all2,
  small: all2,
  span: all2,
  sup: all2,
  sub: all2,
  tbody: all2,
  tfoot: all2,
  thead: all2,
  time: all2,
  // Use children as flow.
  address: flow,
  article: flow,
  aside: flow,
  body: flow,
  center: flow,
  div: flow,
  fieldset: flow,
  figcaption: flow,
  figure: flow,
  form: flow,
  footer: flow,
  header: flow,
  hgroup: flow,
  html: flow,
  legend: flow,
  main: flow,
  multicol: flow,
  nav: flow,
  picture: flow,
  section: flow,
  // Handle.
  a,
  audio: media,
  b: strong,
  base,
  blockquote,
  br,
  code: inlineCode,
  dir: list,
  dl,
  dt: li,
  dd: li,
  del,
  em,
  h1: heading,
  h2: heading,
  h3: heading,
  h4: heading,
  h5: heading,
  h6: heading,
  hr,
  i: em,
  iframe,
  img,
  image: img,
  input,
  kbd: inlineCode,
  li,
  listing: code,
  mark: em,
  ol: list,
  p,
  plaintext: code,
  pre: code,
  q,
  s: del,
  samp: inlineCode,
  select,
  strike: del,
  strong,
  summary: p,
  table,
  td: tableCell,
  textarea,
  th: tableCell,
  tr: tableRow,
  tt: inlineCode,
  u: em,
  ul: list,
  var: inlineCode,
  video: media,
  wbr,
  xmp: code
};
function all2(state, node2) {
  return state.all(node2);
}
function flow(state, node2) {
  return state.toFlow(state.all(node2));
}
function ignore() {
}

// node_modules/hast-util-to-mdast/lib/state.js
var own = {}.hasOwnProperty;
function createState(options) {
  return {
    all: all3,
    baseFound: false,
    elementById: /* @__PURE__ */ new Map(),
    frozenBaseUrl: void 0,
    handlers: { ...handlers, ...options.handlers },
    inTable: false,
    nodeHandlers: { ...nodeHandlers, ...options.nodeHandlers },
    one: one2,
    options,
    patch,
    qNesting: 0,
    resolve,
    toFlow,
    toSpecificContent
  };
}
function all3(parent) {
  const children = parent.children || [];
  const results = [];
  let index = -1;
  while (++index < children.length) {
    const child = children[index];
    const result = (
      /** @type {Array<MdastRootContent> | MdastRootContent | undefined} */
      this.one(child, parent)
    );
    if (Array.isArray(result)) {
      results.push(...result);
    } else if (result) {
      results.push(result);
    }
  }
  return results;
}
function one2(node2, parent) {
  if (node2.type === "element") {
    if (node2.properties && node2.properties.dataMdast === "ignore") {
      return;
    }
    if (own.call(this.handlers, node2.tagName)) {
      return this.handlers[node2.tagName](this, node2, parent) || void 0;
    }
  } else if (own.call(this.nodeHandlers, node2.type)) {
    return this.nodeHandlers[node2.type](this, node2, parent) || void 0;
  }
  if ("value" in node2 && typeof node2.value === "string") {
    const result = { type: "text", value: node2.value };
    this.patch(node2, result);
    return result;
  }
  if ("children" in node2) {
    return this.all(node2);
  }
}
function patch(origin, node2) {
  if (origin.position) node2.position = position(origin);
}
function resolve(url) {
  const base2 = this.frozenBaseUrl;
  if (url === null || url === void 0) {
    return "";
  }
  if (base2) {
    return String(new URL(url, base2));
  }
  return url;
}
function toFlow(nodes) {
  return wrap(nodes);
}
function toSpecificContent(nodes, build) {
  const reference = build();
  const results = [];
  let queue = [];
  let index = -1;
  while (++index < nodes.length) {
    const node2 = nodes[index];
    if (expectedParent(node2)) {
      if (queue.length > 0) {
        node2.children.unshift(...queue);
        queue = [];
      }
      results.push(node2);
    } else {
      const child = (
        /** @type {ChildType} */
        node2
      );
      queue.push(child);
    }
  }
  if (queue.length > 0) {
    let node2 = results[results.length - 1];
    if (!node2) {
      node2 = build();
      results.push(node2);
    }
    node2.children.push(...queue);
    queue = [];
  }
  return results;
  function expectedParent(node2) {
    return node2.type === reference.type;
  }
}

// node_modules/hast-util-to-mdast/lib/index.js
var emptyOptions2 = {};
function toMdast(tree, options) {
  const cleanTree = esm_default(tree);
  const settings = options || emptyOptions2;
  const transformWhitespace = rehypeMinifyWhitespace({
    newlines: settings.newlines === true
  });
  const state = createState(settings);
  let mdast;
  transformWhitespace(cleanTree);
  visit(cleanTree, function(node2) {
    if (node2 && node2.type === "element" && node2.properties) {
      const id = String(node2.properties.id || "") || void 0;
      if (id && !state.elementById.has(id)) {
        state.elementById.set(id, node2);
      }
    }
  });
  const result = state.one(cleanTree, void 0);
  if (!result) {
    mdast = { type: "root", children: [] };
  } else if (Array.isArray(result)) {
    const children = (
      /** @type {Array<MdastRootContent>} */
      result
    );
    mdast = { type: "root", children };
  } else {
    mdast = result;
  }
  visit(mdast, function(node2, index, parent) {
    if (node2.type === "text" && index !== void 0 && parent) {
      const previous = parent.children[index - 1];
      if (previous && previous.type === node2.type) {
        previous.value += node2.value;
        parent.children.splice(index, 1);
        if (previous.position && node2.position) {
          previous.position.end = node2.position.end;
        }
        return index - 1;
      }
      node2.value = node2.value.replace(/[\t ]*(\r?\n|\r)[\t ]*/, "$1");
      if (parent && (parent.type === "heading" || parent.type === "paragraph" || parent.type === "root")) {
        if (!index) {
          node2.value = node2.value.replace(/^[\t ]+/, "");
        }
        if (index === parent.children.length - 1) {
          node2.value = node2.value.replace(/[\t ]+$/, "");
        }
      }
      if (!node2.value) {
        parent.children.splice(index, 1);
        return index;
      }
    }
  });
  return mdast;
}

// node_modules/rehype-remark/lib/index.js
var defaults = { document: true };
function rehypeRemark(destination, options) {
  if (destination && "run" in destination) {
    return async function(tree, file) {
      const mdastTree = toMdast(tree, { ...defaults, ...options });
      await destination.run(mdastTree, file);
    };
  }
  return function(tree) {
    return (
      /** @type {MdastRoot} */
      toMdast(tree, { ...defaults, ...destination })
    );
  };
}
export {
  rehypeRemark as default
};
//# sourceMappingURL=rehype-remark-G2MQ22PI.js.map

{"version": 3, "sources": ["../../../../rehype-minify-whitespace/node_modules/unist-util-is/lib/index.js", "../../../../rehype-minify-whitespace/lib/block.js", "../../../../rehype-minify-whitespace/lib/content.js", "../../../../rehype-minify-whitespace/lib/skippable.js", "../../../../rehype-minify-whitespace/lib/index.js", "../../../../unist-util-find-after/node_modules/unist-util-is/lib/index.js", "../../../../unist-util-find-after/lib/index.js", "../../../../hast-util-to-text/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n", "// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nexport const blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n", "export const content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n", "export const skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n", "/**\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {isElement} from 'hast-util-is-element'\nimport {whitespace} from 'hast-util-whitespace'\nimport {convert} from 'unist-util-is'\nimport {blocks} from './block.js'\nimport {content as contents} from './content.js'\nimport {skippable as skippables} from './skippable.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = convert(['doctype', 'comment'])\n\n/**\n * Minify whitespace.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeMinifyWhitespace(options) {\n  const settings = options || emptyOptions\n  const collapse = collapseFactory(\n    settings.newlines ? replaceNewlines : replaceWhitespace\n  )\n\n  /**\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    minify(tree, {collapse, whitespace: 'normal'})\n  }\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!whitespace(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return embedded(node) || isElement(node, contents)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return isElement(node, blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    isElement(node, skippables)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n", "/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {(\n *   Kind extends {children: Array<infer Child>}\n *   ? Child\n *   : never\n * )} Child\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Kind\n *   All node types.\n */\n\nimport {convert} from 'unist-util-is'\n\n/**\n * Find the first node in `parent` after another `node` or after an index,\n * that passes `test`.\n *\n * @param parent\n *   Parent node.\n * @param index\n *   Child node or index.\n * @param [test=undefined]\n *   Test for child to look for (optional).\n * @returns\n *   A child (matching `test`, if given) or `undefined`.\n */\nexport const findAfter =\n  // Note: overloads like this are needed to support optional generics.\n  /**\n   * @type {(\n   *   (<Kind extends UnistParent, Check extends Test>(parent: Kind, index: Child<Kind> | number, test: Check) => Matches<Child<Kind>, Check> | undefined) &\n   *   (<Kind extends UnistParent>(parent: Kind, index: Child<Kind> | number, test?: null | undefined) => Child<Kind> | undefined)\n   * )}\n   */\n  (\n    /**\n     * @param {UnistParent} parent\n     * @param {UnistNode | number} index\n     * @param {Test} [test]\n     * @returns {UnistNode | undefined}\n     */\n    function (parent, index, test) {\n      const is = convert(test)\n\n      if (!parent || !parent.type || !parent.children) {\n        throw new Error('Expected parent node')\n      }\n\n      if (typeof index === 'number') {\n        if (index < 0 || index === Number.POSITIVE_INFINITY) {\n          throw new Error('Expected positive finite number as index')\n        }\n      } else {\n        index = parent.children.indexOf(index)\n\n        if (index < 0) {\n          throw new Error('Expected child node or index')\n        }\n      }\n\n      while (++index < parent.children.length) {\n        if (is(parent.children[index], index, parent)) {\n          return parent.children[index]\n        }\n      }\n\n      return undefined\n    }\n  )\n", "/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast-util-is-element').TestFunction} TestFunction\n */\n\n/**\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Valid and useful whitespace values (from CSS).\n *\n * @typedef {0 | 1 | 2} BreakNumber\n *   Specific break:\n *\n *   *   `0` — space\n *   *   `1` — line ending\n *   *   `2` — blank line\n *\n * @typedef {'\\n'} BreakForce\n *   Forced break.\n *\n * @typedef {boolean} BreakValue\n *   Whether there was a break.\n *\n * @typedef {BreakNumber | BreakValue | undefined} BreakBefore\n *   Any value for a break before.\n *\n * @typedef {BreakForce | BreakNumber | BreakValue | undefined} BreakAfter\n *   Any value for a break after.\n *\n * @typedef CollectionInfo\n *   Info on current collection.\n * @property {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @property {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @property {Whitespace} whitespace\n *   Current whitespace setting.\n *\n * @typedef Options\n *   Configuration.\n * @property {Whitespace | null | undefined} [whitespace='normal']\n *   Initial CSS whitespace setting to use (default: `'normal'`).\n */\n\nimport {findAfter} from 'unist-util-find-after'\nimport {convertElement} from 'hast-util-is-element'\n\nconst searchLineFeeds = /\\n/g\nconst searchTabOrSpaces = /[\\t ]+/g\n\nconst br = convertElement('br')\nconst cell = convertElement(isCell)\nconst p = convertElement('p')\nconst row = convertElement('tr')\n\n// Note that we don’t need to include void elements here as they don’t have text.\n// See: <https://github.com/wooorm/html-void-elements>\nconst notRendered = convertElement([\n  // List from: <https://html.spec.whatwg.org/multipage/rendering.html#hidden-elements>\n  'datalist',\n  'head',\n  'noembed',\n  'noframes',\n  'noscript', // Act as if we support scripting.\n  'rp',\n  'script',\n  'style',\n  'template',\n  'title',\n  // Hidden attribute.\n  hidden,\n  // From: <https://html.spec.whatwg.org/multipage/rendering.html#flow-content-3>\n  closedDialog\n])\n\n// See: <https://html.spec.whatwg.org/multipage/rendering.html#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blockOrCaption = convertElement([\n  'address', // Flow content\n  'article', // Sections and headings\n  'aside', // Sections and headings\n  'blockquote', // Flow content\n  'body', // Page\n  'caption', // `table-caption`\n  'center', // Flow content (legacy)\n  'dd', // Lists\n  'dialog', // Flow content\n  'dir', // Lists (legacy)\n  'dl', // Lists\n  'dt', // Lists\n  'div', // Flow content\n  'figure', // Flow content\n  'figcaption', // Flow content\n  'footer', // Flow content\n  'form,', // Flow content\n  'h1', // Sections and headings\n  'h2', // Sections and headings\n  'h3', // Sections and headings\n  'h4', // Sections and headings\n  'h5', // Sections and headings\n  'h6', // Sections and headings\n  'header', // Flow content\n  'hgroup', // Sections and headings\n  'hr', // Flow content\n  'html', // Page\n  'legend', // Flow content\n  'li', // Lists (as `display: list-item`)\n  'listing', // Flow content (legacy)\n  'main', // Flow content\n  'menu', // Lists\n  'nav', // Sections and headings\n  'ol', // Lists\n  'p', // Flow content\n  'plaintext', // Flow content (legacy)\n  'pre', // Flow content\n  'section', // Sections and headings\n  'ul', // Lists\n  'xmp' // Flow content (legacy)\n])\n\n/**\n * Get the plain-text value of a node.\n *\n * ###### Algorithm\n *\n * *   if `tree` is a comment, returns its `value`\n * *   if `tree` is a text, applies normal whitespace collapsing to its\n *     `value`, as defined by the CSS Text spec\n * *   if `tree` is a root or element, applies an algorithm similar to the\n *     `innerText` getter as defined by HTML\n *\n * ###### Notes\n *\n * > 👉 **Note**: the algorithm acts as if `tree` is being rendered, and as if\n * > we’re a CSS-supporting user agent, with scripting enabled.\n *\n * *   if `tree` is an element that is not displayed (such as a `head`), we’ll\n *     still use the `innerText` algorithm instead of switching to `textContent`\n * *   if descendants of `tree` are elements that are not displayed, they are\n *     ignored\n * *   CSS is not considered, except for the default user agent style sheet\n * *   a line feed is collapsed instead of ignored in cases where Fullwidth, Wide,\n *     or Halfwidth East Asian Width characters are used, the same goes for a case\n *     with Chinese, Japanese, or Yi writing systems\n * *   replaced elements (such as `audio`) are treated like non-replaced elements\n *\n * @param {Nodes} tree\n *   Tree to turn into text.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `tree`.\n */\nexport function toText(tree, options) {\n  const options_ = options || {}\n  const children = 'children' in tree ? tree.children : []\n  const block = blockOrCaption(tree)\n  const whitespace = inferWhitespace(tree, {\n    whitespace: options_.whitespace || 'normal',\n    breakBefore: false,\n    breakAfter: false\n  })\n\n  /** @type {Array<BreakNumber | string>} */\n  const results = []\n\n  // Treat `text` and `comment` as having normal white-space.\n  // This deviates from the spec as in the DOM the node’s `.data` has to be\n  // returned.\n  // If you want that behavior use `hast-util-to-string`.\n  // All other nodes are later handled as if they are `element`s (so the\n  // algorithm also works on a `root`).\n  // Nodes without children are treated as a void element, so `doctype` is thus\n  // ignored.\n  if (tree.type === 'text' || tree.type === 'comment') {\n    results.push(\n      ...collectText(tree, {\n        whitespace,\n        breakBefore: true,\n        breakAfter: true\n      })\n    )\n  }\n\n  // 1.  If this element is not being rendered, or if the user agent is a\n  //     non-CSS user agent, then return the same value as the textContent IDL\n  //     attribute on this element.\n  //\n  //     Note: we’re not supporting stylesheets so we’re acting as if the node\n  //     is rendered.\n  //\n  //     If you want that behavior use `hast-util-to-string`.\n  //     Important: we’ll have to account for this later though.\n\n  // 2.  Let results be a new empty list.\n  let index = -1\n\n  // 3.  For each child node node of this element:\n  while (++index < children.length) {\n    // 3.1. Let current be the list resulting in running the inner text\n    //      collection steps with node.\n    //      Each item in results will either be a JavaScript string or a\n    //      positive integer (a required line break count).\n    // 3.2. For each item item in current, append item to results.\n    results.push(\n      ...renderedTextCollection(\n        children[index],\n        // @ts-expect-error: `tree` is a parent if we’re here.\n        tree,\n        {\n          whitespace,\n          breakBefore: index ? undefined : block,\n          breakAfter:\n            index < children.length - 1 ? br(children[index + 1]) : block\n        }\n      )\n    )\n  }\n\n  // 4.  Remove any items from results that are the empty string.\n  // 5.  Remove any runs of consecutive required line break count items at the\n  //     start or end of results.\n  // 6.  Replace each remaining run of consecutive required line break count\n  //     items with a string consisting of as many U+000A LINE FEED (LF)\n  //     characters as the maximum of the values in the required line break\n  //     count items.\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {number | undefined} */\n  let count\n\n  index = -1\n\n  while (++index < results.length) {\n    const value = results[index]\n\n    if (typeof value === 'number') {\n      if (count !== undefined && value > count) count = value\n    } else if (value) {\n      if (count !== undefined && count > -1) {\n        result.push('\\n'.repeat(count) || ' ')\n      }\n\n      count = -1\n      result.push(value)\n    }\n  }\n\n  // 7.  Return the concatenation of the string items in results.\n  return result.join('')\n}\n\n/**\n * <https://html.spec.whatwg.org/multipage/dom.html#rendered-text-collection-steps>\n *\n * @param {Nodes} node\n * @param {Parents} parent\n * @param {CollectionInfo} info\n * @returns {Array<BreakNumber | string>}\n */\nfunction renderedTextCollection(node, parent, info) {\n  if (node.type === 'element') {\n    return collectElement(node, parent, info)\n  }\n\n  if (node.type === 'text') {\n    return info.whitespace === 'normal'\n      ? collectText(node, info)\n      : collectPreText(node)\n  }\n\n  return []\n}\n\n/**\n * Collect an element.\n *\n * @param {Element} node\n *   Element node.\n * @param {Parents} parent\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n */\nfunction collectElement(node, parent, info) {\n  // First we infer the `white-space` property.\n  const whitespace = inferWhitespace(node, info)\n  const children = node.children || []\n  let index = -1\n  /** @type {Array<BreakNumber | string>} */\n  let items = []\n\n  // We’re ignoring point 3, and exiting without any content here, because we\n  // deviated from the spec in `toText` at step 3.\n  if (notRendered(node)) {\n    return items\n  }\n\n  /** @type {BreakNumber | undefined} */\n  let prefix\n  /** @type {BreakForce | BreakNumber | undefined} */\n  let suffix\n  // Note: we first detect if there is going to be a break before or after the\n  // contents, as that changes the white-space handling.\n\n  // 2.  If node’s computed value of `visibility` is not `visible`, then return\n  //     items.\n  //\n  //     Note: Ignored, as everything is visible by default user agent styles.\n\n  // 3.  If node is not being rendered, then return items. [...]\n  //\n  //     Note: We already did this above.\n\n  // See `collectText` for step 4.\n\n  // 5.  If node is a `<br>` element, then append a string containing a single\n  //     U+000A LINE FEED (LF) character to items.\n  if (br(node)) {\n    suffix = '\\n'\n  }\n\n  // 7.  If node’s computed value of `display` is `table-row`, and node’s CSS\n  //     box is not the last `table-row` box of the nearest ancestor `table`\n  //     box, then append a string containing a single U+000A LINE FEED (LF)\n  //     character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  //     Note: needs further investigation as this does not account for implicit\n  //     rows.\n  else if (\n    row(node) &&\n    // @ts-expect-error: something up with types of parents.\n    findAfter(parent, node, row)\n  ) {\n    suffix = '\\n'\n  }\n\n  // 8.  If node is a `<p>` element, then append 2 (a required line break count)\n  //     at the beginning and end of items.\n  else if (p(node)) {\n    prefix = 2\n    suffix = 2\n  }\n\n  // 9.  If node’s used value of `display` is block-level or `table-caption`,\n  //     then append 1 (a required line break count) at the beginning and end of\n  //     items.\n  else if (blockOrCaption(node)) {\n    prefix = 1\n    suffix = 1\n  }\n\n  // 1.  Let items be the result of running the inner text collection steps with\n  //     each child node of node in tree order, and then concatenating the\n  //     results to a single list.\n  while (++index < children.length) {\n    items = items.concat(\n      renderedTextCollection(children[index], node, {\n        whitespace,\n        breakBefore: index ? undefined : prefix,\n        breakAfter:\n          index < children.length - 1 ? br(children[index + 1]) : suffix\n      })\n    )\n  }\n\n  // 6.  If node’s computed value of `display` is `table-cell`, and node’s CSS\n  //     box is not the last `table-cell` box of its enclosing `table-row` box,\n  //     then append a string containing a single U+0009 CHARACTER TABULATION\n  //     (tab) character to items.\n  //\n  //     See: <https://html.spec.whatwg.org/multipage/rendering.html#tables-2>\n  if (\n    cell(node) &&\n    // @ts-expect-error: something up with types of parents.\n    findAfter(parent, node, cell)\n  ) {\n    items.push('\\t')\n  }\n\n  // Add the pre- and suffix.\n  if (prefix) items.unshift(prefix)\n  if (suffix) items.push(suffix)\n\n  return items\n}\n\n/**\n * 4.  If node is a Text node, then for each CSS text box produced by node,\n *     in content order, compute the text of the box after application of the\n *     CSS `white-space` processing rules and `text-transform` rules, set\n *     items to the list of the resulting strings, and return items.\n *     The CSS `white-space` processing rules are slightly modified:\n *     collapsible spaces at the end of lines are always collapsed, but they\n *     are only removed if the line is the last line of the block, or it ends\n *     with a br element.\n *     Soft hyphens should be preserved.\n *\n *     Note: See `collectText` and `collectPreText`.\n *     Note: we don’t deal with `text-transform`, no element has that by\n *     default.\n *\n * See: <https://drafts.csswg.org/css-text/#white-space-phase-1>\n *\n * @param {Comment | Text} node\n *   Text node.\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectText(node, info) {\n  const value = String(node.value)\n  /** @type {Array<string>} */\n  const lines = []\n  /** @type {Array<BreakNumber | string>} */\n  const result = []\n  let start = 0\n\n  while (start <= value.length) {\n    searchLineFeeds.lastIndex = start\n\n    const match = searchLineFeeds.exec(value)\n    const end = match && 'index' in match ? match.index : value.length\n\n    lines.push(\n      // Any sequence of collapsible spaces and tabs immediately preceding or\n      // following a segment break is removed.\n      trimAndCollapseSpacesAndTabs(\n        // […] ignoring bidi formatting characters (characters with the\n        // Bidi_Control property [UAX9]: ALM, LTR, RTL, LRE-RLO, LRI-PDI) as if\n        // they were not there.\n        value\n          .slice(start, end)\n          .replace(/[\\u061C\\u200E\\u200F\\u202A-\\u202E\\u2066-\\u2069]/g, ''),\n        start === 0 ? info.breakBefore : true,\n        end === value.length ? info.breakAfter : true\n      )\n    )\n\n    start = end + 1\n  }\n\n  // Collapsible segment breaks are transformed for rendering according to the\n  // segment break transformation rules.\n  // So here we jump to 4.1.2 of [CSSTEXT]:\n  // Any collapsible segment break immediately following another collapsible\n  // segment break is removed\n  let index = -1\n  /** @type {BreakNumber | undefined} */\n  let join\n\n  while (++index < lines.length) {\n    // *   If the character immediately before or immediately after the segment\n    //     break is the zero-width space character (U+200B), then the break is\n    //     removed, leaving behind the zero-width space.\n    if (\n      lines[index].charCodeAt(lines[index].length - 1) === 0x20_0b /* ZWSP */ ||\n      (index < lines.length - 1 &&\n        lines[index + 1].charCodeAt(0) === 0x20_0b) /* ZWSP */\n    ) {\n      result.push(lines[index])\n      join = undefined\n    }\n\n    // *   Otherwise, if the East Asian Width property [UAX11] of both the\n    //     character before and after the segment break is Fullwidth, Wide, or\n    //     Halfwidth (not Ambiguous), and neither side is Hangul, then the\n    //     segment break is removed.\n    //\n    //     Note: ignored.\n    // *   Otherwise, if the writing system of the segment break is Chinese,\n    //     Japanese, or Yi, and the character before or after the segment break\n    //     is punctuation or a symbol (Unicode general category P* or S*) and\n    //     has an East Asian Width property of Ambiguous, and the character on\n    //     the other side of the segment break is Fullwidth, Wide, or Halfwidth,\n    //     and not Hangul, then the segment break is removed.\n    //\n    //     Note: ignored.\n\n    // *   Otherwise, the segment break is converted to a space (U+0020).\n    else if (lines[index]) {\n      if (typeof join === 'number') result.push(join)\n      result.push(lines[index])\n      join = 0\n    } else if (index === 0 || index === lines.length - 1) {\n      // If this line is empty, and it’s the first or last, add a space.\n      // Note that this function is only called in normal whitespace, so we\n      // don’t worry about `pre`.\n      result.push(0)\n    }\n  }\n\n  return result\n}\n\n/**\n * Collect a text node as “pre” whitespace.\n *\n * @param {Text} node\n *   Text node.\n * @returns {Array<BreakNumber | string>}\n *   Result.\n */\nfunction collectPreText(node) {\n  return [String(node.value)]\n}\n\n/**\n * 3.  Every collapsible tab is converted to a collapsible space (U+0020).\n * 4.  Any collapsible space immediately following another collapsible\n *     space—even one outside the boundary of the inline containing that\n *     space, provided both spaces are within the same inline formatting\n *     context—is collapsed to have zero advance width. (It is invisible,\n *     but retains its soft wrap opportunity, if any.)\n *\n * @param {string} value\n *   Value to collapse.\n * @param {BreakBefore} breakBefore\n *   Whether there was a break before.\n * @param {BreakAfter} breakAfter\n *   Whether there was a break after.\n * @returns {string}\n *   Result.\n */\nfunction trimAndCollapseSpacesAndTabs(value, breakBefore, breakAfter) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  /** @type {number | undefined} */\n  let end\n\n  while (start < value.length) {\n    searchTabOrSpaces.lastIndex = start\n    const match = searchTabOrSpaces.exec(value)\n    end = match ? match.index : value.length\n\n    // If we’re not directly after a segment break, but there was white space,\n    // add an empty value that will be turned into a space.\n    if (!start && !end && match && !breakBefore) {\n      result.push('')\n    }\n\n    if (start !== end) {\n      result.push(value.slice(start, end))\n    }\n\n    start = match ? end + match[0].length : end\n  }\n\n  // If we reached the end, there was trailing white space, and there’s no\n  // segment break after this node, add an empty value that will be turned\n  // into a space.\n  if (start !== end && !breakAfter) {\n    result.push('')\n  }\n\n  return result.join(' ')\n}\n\n/**\n * Figure out the whitespace of a node.\n *\n * We don’t support void elements here (so `nobr wbr` -> `normal` is ignored).\n *\n * @param {Nodes} node\n *   Node (typically `Element`).\n * @param {CollectionInfo} info\n *   Info on current collection.\n * @returns {Whitespace}\n *   Applied whitespace.\n */\nfunction inferWhitespace(node, info) {\n  if (node.type === 'element') {\n    const properties = node.properties || {}\n    switch (node.tagName) {\n      case 'listing':\n      case 'plaintext':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return properties.noWrap ? 'nowrap' : info.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return info.whitespace\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {properties: {hidden: true}}}\n */\nfunction hidden(node) {\n  return Boolean((node.properties || {}).hidden)\n}\n\n/**\n * @type {TestFunction}\n * @param {Element} node\n * @returns {node is {tagName: 'td' | 'th'}}\n */\nfunction isCell(node) {\n  return node.tagName === 'td' || node.tagName === 'th'\n}\n\n/**\n * @type {TestFunction}\n */\nfunction closedDialog(node) {\n  return node.tagName === 'dialog' && !(node.properties || {}).open\n}\n"], "mappings": ";;;;;;;;;;AA2IO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBT,SAAU,MAAM;AACd,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,IAAI,IAAI,aAAa,IAAI;AAAA,IACnE;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AAAA;AAOJ,SAAS,WAAW,OAAO;AAEzB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,WAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO,YAAY,GAAG;AAMtB,WAAS,OAAO,YAAY;AAC1B,QAAIA,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAOA,MAAK,EAAE,MAAM,MAAM,UAAU,EAAG,QAAO;AAAA,IACpD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,aAAa,OAAO;AAC3B,QAAM;AAAA;AAAA,IAAwD;AAAA;AAE9D,SAAO,YAAYC,IAAG;AAMtB,WAASA,KAAI,MAAM;AACjB,UAAM;AAAA;AAAA;AAAA,MACoB;AAAA;AAI1B,QAAI;AAEJ,SAAK,OAAO,OAAO;AACjB,UAAI,aAAa,GAAG,MAAM,cAAc,GAAG,EAAG,QAAO;AAAA,IACvD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,YAAY,OAAO;AAC1B,SAAO,YAAY,IAAI;AAKvB,WAAS,KAAK,MAAM;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B;AACF;AAQA,SAAS,YAAY,cAAc;AACjC,SAAO;AAMP,WAAS,MAAM,OAAO,OAAO,QAAQ;AACnC,WAAO;AAAA,MACL,eAAe,KAAK,KAClB,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA,OAAO,UAAU,WAAW,QAAQ;AAAA,QACpC,UAAU;AAAA,MACZ;AAAA,IACJ;AAAA,EACF;AACF;AAEA,SAAS,KAAK;AACZ,SAAO;AACT;AAMA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE;;;ACjSO,IAAM,SAAS;AAAA,EACpB;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AACF;;;AC5DO,IAAM,UAAU;AAAA;AAAA,EAErB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACNO,IAAM,YAAY;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACmCA,IAAM,eAAe,CAAC;AACtB,IAAM,gBAAgB,QAAQ,CAAC,WAAW,SAAS,CAAC;AAUrC,SAAR,uBAAwC,SAAS;AACtD,QAAM,WAAW,WAAW;AAC5B,QAAM,WAAW;AAAA,IACf,SAAS,WAAW,kBAAkB;AAAA,EACxC;AAQA,SAAO,SAAU,MAAM;AACrB,WAAO,MAAM,EAAC,UAAU,YAAY,SAAQ,CAAC;AAAA,EAC/C;AACF;AAUA,SAAS,OAAO,MAAM,OAAO;AAC3B,MAAI,cAAc,MAAM;AACtB,UAAM,WAAW,EAAC,GAAG,MAAK;AAE1B,QAAI,KAAK,SAAS,UAAU,UAAU,IAAI,GAAG;AAC3C,eAAS,SAAS;AAClB,eAAS,QAAQ;AAAA,IACnB;AAEA,aAAS,aAAa,gBAAgB,MAAM,KAAK;AAEjD,WAAO,IAAI,MAAM,QAAQ;AAAA,EAC3B;AAEA,MAAI,KAAK,SAAS,QAAQ;AACxB,QAAI,MAAM,eAAe,UAAU;AACjC,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B;AAGA,QAAI,MAAM,eAAe,UAAU;AACjC,WAAK,QAAQ,MAAM,SAAS,KAAK,KAAK;AAAA,IACxC;AAAA,EAIF;AAEA,SAAO,EAAC,QAAQ,cAAc,IAAI,GAAG,cAAc,OAAO,QAAQ,MAAK;AACzE;AAUA,SAAS,WAAW,MAAM,OAAO;AAC/B,QAAM,QAAQ,MAAM,SAAS,KAAK,KAAK;AACvC,QAAM,SAAS,EAAC,QAAQ,OAAO,cAAc,OAAO,QAAQ,MAAK;AACjE,MAAI,QAAQ;AACZ,MAAI,MAAM,MAAM;AAEhB,MAAI,MAAM,UAAU,UAAU,MAAM,OAAO,CAAC,CAAC,GAAG;AAC9C;AAAA,EACF;AAEA,MAAI,UAAU,OAAO,UAAU,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG;AACrD,QAAI,MAAM,OAAO;AACf;AAAA,IACF,OAAO;AACL,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,UAAU,KAAK;AACjB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,SAAK,QAAQ,MAAM,MAAM,OAAO,GAAG;AAAA,EACrC;AAEA,SAAO;AACT;AAUA,SAAS,IAAI,QAAQ,OAAO;AAC1B,MAAI,SAAS,MAAM;AACnB,QAAM,QAAQ,MAAM;AACpB,QAAM,WAAW,OAAO;AACxB,MAAI,SAAS,SAAS;AACtB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ;AACvB,UAAM,SAAS,OAAO,SAAS,KAAK,GAAG;AAAA,MACrC,GAAG;AAAA,MACH,OAAO,iBAAiB,UAAU,OAAO,KAAK;AAAA,MAC9C;AAAA,IACF,CAAC;AAED,QAAI,OAAO,QAAQ;AACjB,eAAS,OAAO,OAAO,CAAC;AACxB;AACA;AAAA,IACF,WAAW,CAAC,OAAO,QAAQ;AACzB,eAAS,OAAO;AAAA,IAClB;AAIA,QAAIC,SAAQ,SAAS,KAAK,CAAC,GAAG;AAC5B,eAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO,EAAC,QAAQ,OAAO,cAAc,QAAQ,UAAU,KAAK,GAAG,QAAQ,MAAK;AAC9E;AAYA,SAAS,iBAAiB,OAAO,OAAO,OAAO;AAC7C,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAM,OAAO,MAAM,KAAK;AACxB,QAAI,SAAS,cAAc,IAAI;AAE/B,QAAI,WAAW,UAAa,cAAc,QAAQ,CAACC,WAAU,IAAI,GAAG;AAClE,eAAS,iBAAiB,KAAK,UAAU,EAAE;AAAA,IAC7C;AAEA,QAAI,OAAO,WAAW,WAAW;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAgBA,SAAS,cAAc,MAAM;AAC3B,MAAI,KAAK,SAAS,WAAW;AAC3B,QAAID,SAAQ,IAAI,GAAG;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO;AAAA,IACT;AAAA,EAIF,WAAW,KAAK,SAAS,QAAQ;AAC/B,QAAI,CAAC,WAAW,IAAI,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF,WAAW,CAAC,cAAc,IAAI,GAAG;AAC/B,WAAO;AAAA,EACT;AACF;AAUA,SAASA,SAAQ,MAAM;AACrB,SAAO,SAAS,IAAI,KAAK,UAAU,MAAM,OAAQ;AACnD;AAUA,SAAS,UAAU,MAAM;AACvB,SAAO,UAAU,MAAM,MAAM;AAC/B;AAQA,SAASC,WAAU,MAAM;AACvB,SACE,QAAQ,KAAK,SAAS,aAAa,KAAK,WAAW,MAAM,KACzD,cAAc,IAAI,KAClB,UAAU,MAAM,SAAU;AAE9B;AAQA,SAAS,UAAU,WAAW;AAC5B,SAAO,cAAc,OAAO,cAAc;AAC5C;AAKA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,QAAQ,WAAW,KAAK,KAAK;AACnC,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC5B;AAKA,SAAS,oBAAoB;AAC3B,SAAO;AACT;AAOA,SAAS,gBAAgB,SAAS;AAChC,SAAO;AAKP,WAAS,SAAS,OAAO;AACvB,WAAO,OAAO,KAAK,EAAE,QAAQ,mBAAmB,OAAO;AAAA,EACzD;AACF;AAaA,SAAS,gBAAgB,MAAM,OAAO;AACpC,MAAI,aAAa,QAAQ,KAAK,YAAY;AACxC,YAAQ,KAAK,SAAS;AAAA;AAAA;AAAA,MAGpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACV,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,OAAO;AACV,eAAO,KAAK,WAAW,OAAO,aAAa;AAAA,MAC7C;AAAA,MAEA,KAAK;AAAA,MACL,KAAK,MAAM;AACT,eAAO,KAAK,WAAW,SAAS,WAAW,MAAM;AAAA,MACnD;AAAA,MAEA,KAAK,YAAY;AACf,eAAO;AAAA,MACT;AAAA,MAEA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,MAAM;AACf;;;ACpPO,IAAMC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBT,SAAU,MAAM;AACd,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,aAAOC;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAOC,aAAY,IAAI;AAAA,IACzB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,MAAM,QAAQ,IAAI,IAAIC,YAAW,IAAI,IAAIC,cAAa,IAAI;AAAA,IACnE;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAOC,aAAY,IAAI;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AAAA;AAOJ,SAASF,YAAW,OAAO;AAEzB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,WAAO,KAAK,IAAIH,SAAQ,MAAM,KAAK,CAAC;AAAA,EACtC;AAEA,SAAOE,aAAY,GAAG;AAMtB,WAAS,OAAO,YAAY;AAC1B,QAAII,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAOA,MAAK,EAAE,MAAM,MAAM,UAAU,EAAG,QAAO;AAAA,IACpD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAASF,cAAa,OAAO;AAC3B,QAAM;AAAA;AAAA,IAAwD;AAAA;AAE9D,SAAOF,aAAYK,IAAG;AAMtB,WAASA,KAAI,MAAM;AACjB,UAAM;AAAA;AAAA;AAAA,MACoB;AAAA;AAI1B,QAAI;AAEJ,SAAK,OAAO,OAAO;AACjB,UAAI,aAAa,GAAG,MAAM,cAAc,GAAG,EAAG,QAAO;AAAA,IACvD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAASF,aAAY,OAAO;AAC1B,SAAOH,aAAY,IAAI;AAKvB,WAAS,KAAK,MAAM;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B;AACF;AAQA,SAASA,aAAY,cAAc;AACjC,SAAO;AAMP,WAAS,MAAM,OAAO,OAAO,QAAQ;AACnC,WAAO;AAAA,MACLM,gBAAe,KAAK,KAClB,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA,OAAO,UAAU,WAAW,QAAQ;AAAA,QACpC,UAAU;AAAA,MACZ;AAAA,IACJ;AAAA,EACF;AACF;AAEA,SAASP,MAAK;AACZ,SAAO;AACT;AAMA,SAASO,gBAAe,OAAO;AAC7B,SAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE;;;AC3MO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,QAAQ,OAAO,MAAM;AAC7B,UAAMC,MAAKC,SAAQ,IAAI;AAEvB,QAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,CAAC,OAAO,UAAU;AAC/C,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AAEA,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,QAAQ,KAAK,UAAU,OAAO,mBAAmB;AACnD,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAAA,IACF,OAAO;AACL,cAAQ,OAAO,SAAS,QAAQ,KAAK;AAErC,UAAI,QAAQ,GAAG;AACb,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,OAAO,SAAS,QAAQ;AACvC,UAAID,IAAG,OAAO,SAAS,KAAK,GAAG,OAAO,MAAM,GAAG;AAC7C,eAAO,OAAO,SAAS,KAAK;AAAA,MAC9B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;;;AC9EJ,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAE1B,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,OAAO,eAAe,MAAM;AAClC,IAAM,IAAI,eAAe,GAAG;AAC5B,IAAM,MAAM,eAAe,IAAI;AAI/B,IAAM,cAAc,eAAe;AAAA;AAAA,EAEjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AACF,CAAC;AAGD,IAAM,iBAAiB,eAAe;AAAA,EACpC;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AACF,CAAC;AAmCM,SAAS,OAAO,MAAM,SAAS;AACpC,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,WAAW,cAAc,OAAO,KAAK,WAAW,CAAC;AACvD,QAAM,QAAQ,eAAe,IAAI;AACjC,QAAME,cAAa,gBAAgB,MAAM;AAAA,IACvC,YAAY,SAAS,cAAc;AAAA,IACnC,aAAa;AAAA,IACb,YAAY;AAAA,EACd,CAAC;AAGD,QAAM,UAAU,CAAC;AAUjB,MAAI,KAAK,SAAS,UAAU,KAAK,SAAS,WAAW;AACnD,YAAQ;AAAA,MACN,GAAG,YAAY,MAAM;AAAA,QACnB,YAAAA;AAAA,QACA,aAAa;AAAA,QACb,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAaA,MAAI,QAAQ;AAGZ,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAMhC,YAAQ;AAAA,MACN,GAAG;AAAA,QACD,SAAS,KAAK;AAAA;AAAA,QAEd;AAAA,QACA;AAAA,UACE,YAAAA;AAAA,UACA,aAAa,QAAQ,SAAY;AAAA,UACjC,YACE,QAAQ,SAAS,SAAS,IAAI,GAAG,SAAS,QAAQ,CAAC,CAAC,IAAI;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAUA,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,UAAQ;AAER,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQ,KAAK;AAE3B,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,UAAU,UAAa,QAAQ,MAAO,SAAQ;AAAA,IACpD,WAAW,OAAO;AAChB,UAAI,UAAU,UAAa,QAAQ,IAAI;AACrC,eAAO,KAAK,KAAK,OAAO,KAAK,KAAK,GAAG;AAAA,MACvC;AAEA,cAAQ;AACR,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AAGA,SAAO,OAAO,KAAK,EAAE;AACvB;AAUA,SAAS,uBAAuB,MAAM,QAAQ,MAAM;AAClD,MAAI,KAAK,SAAS,WAAW;AAC3B,WAAO,eAAe,MAAM,QAAQ,IAAI;AAAA,EAC1C;AAEA,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO,KAAK,eAAe,WACvB,YAAY,MAAM,IAAI,IACtB,eAAe,IAAI;AAAA,EACzB;AAEA,SAAO,CAAC;AACV;AAYA,SAAS,eAAe,MAAM,QAAQ,MAAM;AAE1C,QAAMA,cAAa,gBAAgB,MAAM,IAAI;AAC7C,QAAM,WAAW,KAAK,YAAY,CAAC;AACnC,MAAI,QAAQ;AAEZ,MAAI,QAAQ,CAAC;AAIb,MAAI,YAAY,IAAI,GAAG;AACrB,WAAO;AAAA,EACT;AAGA,MAAI;AAEJ,MAAI;AAiBJ,MAAI,GAAG,IAAI,GAAG;AACZ,aAAS;AAAA,EACX,WAWE,IAAI,IAAI;AAAA,EAER,UAAU,QAAQ,MAAM,GAAG,GAC3B;AACA,aAAS;AAAA,EACX,WAIS,EAAE,IAAI,GAAG;AAChB,aAAS;AACT,aAAS;AAAA,EACX,WAKS,eAAe,IAAI,GAAG;AAC7B,aAAS;AACT,aAAS;AAAA,EACX;AAKA,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,YAAQ,MAAM;AAAA,MACZ,uBAAuB,SAAS,KAAK,GAAG,MAAM;AAAA,QAC5C,YAAAA;AAAA,QACA,aAAa,QAAQ,SAAY;AAAA,QACjC,YACE,QAAQ,SAAS,SAAS,IAAI,GAAG,SAAS,QAAQ,CAAC,CAAC,IAAI;AAAA,MAC5D,CAAC;AAAA,IACH;AAAA,EACF;AAQA,MACE,KAAK,IAAI;AAAA,EAET,UAAU,QAAQ,MAAM,IAAI,GAC5B;AACA,UAAM,KAAK,GAAI;AAAA,EACjB;AAGA,MAAI,OAAQ,OAAM,QAAQ,MAAM;AAChC,MAAI,OAAQ,OAAM,KAAK,MAAM;AAE7B,SAAO;AACT;AA0BA,SAAS,YAAY,MAAM,MAAM;AAC/B,QAAM,QAAQ,OAAO,KAAK,KAAK;AAE/B,QAAM,QAAQ,CAAC;AAEf,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,SAAS,MAAM,QAAQ;AAC5B,oBAAgB,YAAY;AAE5B,UAAM,QAAQ,gBAAgB,KAAK,KAAK;AACxC,UAAM,MAAM,SAAS,WAAW,QAAQ,MAAM,QAAQ,MAAM;AAE5D,UAAM;AAAA;AAAA;AAAA,MAGJ;AAAA;AAAA;AAAA;AAAA,QAIE,MACG,MAAM,OAAO,GAAG,EAChB,QAAQ,mDAAmD,EAAE;AAAA,QAChE,UAAU,IAAI,KAAK,cAAc;AAAA,QACjC,QAAQ,MAAM,SAAS,KAAK,aAAa;AAAA,MAC3C;AAAA,IACF;AAEA,YAAQ,MAAM;AAAA,EAChB;AAOA,MAAI,QAAQ;AAEZ,MAAI;AAEJ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAI7B,QACE,MAAM,KAAK,EAAE,WAAW,MAAM,KAAK,EAAE,SAAS,CAAC,MAAM,QACpD,QAAQ,MAAM,SAAS,KACtB,MAAM,QAAQ,CAAC,EAAE,WAAW,CAAC,MAAM,MACrC;AACA,aAAO,KAAK,MAAM,KAAK,CAAC;AACxB,aAAO;AAAA,IACT,WAkBS,MAAM,KAAK,GAAG;AACrB,UAAI,OAAO,SAAS,SAAU,QAAO,KAAK,IAAI;AAC9C,aAAO,KAAK,MAAM,KAAK,CAAC;AACxB,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,UAAU,MAAM,SAAS,GAAG;AAIpD,aAAO,KAAK,CAAC;AAAA,IACf;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,eAAe,MAAM;AAC5B,SAAO,CAAC,OAAO,KAAK,KAAK,CAAC;AAC5B;AAmBA,SAAS,6BAA6B,OAAO,aAAa,YAAY;AAEpE,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,MAAI;AAEJ,SAAO,QAAQ,MAAM,QAAQ;AAC3B,sBAAkB,YAAY;AAC9B,UAAM,QAAQ,kBAAkB,KAAK,KAAK;AAC1C,UAAM,QAAQ,MAAM,QAAQ,MAAM;AAIlC,QAAI,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,aAAa;AAC3C,aAAO,KAAK,EAAE;AAAA,IAChB;AAEA,QAAI,UAAU,KAAK;AACjB,aAAO,KAAK,MAAM,MAAM,OAAO,GAAG,CAAC;AAAA,IACrC;AAEA,YAAQ,QAAQ,MAAM,MAAM,CAAC,EAAE,SAAS;AAAA,EAC1C;AAKA,MAAI,UAAU,OAAO,CAAC,YAAY;AAChC,WAAO,KAAK,EAAE;AAAA,EAChB;AAEA,SAAO,OAAO,KAAK,GAAG;AACxB;AAcA,SAAS,gBAAgB,MAAM,MAAM;AACnC,MAAI,KAAK,SAAS,WAAW;AAC3B,UAAM,aAAa,KAAK,cAAc,CAAC;AACvC,YAAQ,KAAK,SAAS;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACV,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,OAAO;AACV,eAAO,WAAW,OAAO,aAAa;AAAA,MACxC;AAAA,MAEA,KAAK;AAAA,MACL,KAAK,MAAM;AACT,eAAO,WAAW,SAAS,WAAW,KAAK;AAAA,MAC7C;AAAA,MAEA,KAAK,YAAY;AACf,eAAO;AAAA,MACT;AAAA,MAEA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK;AACd;AAOA,SAAS,OAAO,MAAM;AACpB,SAAO,SAAS,KAAK,cAAc,CAAC,GAAG,MAAM;AAC/C;AAOA,SAAS,OAAO,MAAM;AACpB,SAAO,KAAK,YAAY,QAAQ,KAAK,YAAY;AACnD;AAKA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,YAAY,YAAY,EAAE,KAAK,cAAc,CAAC,GAAG;AAC/D;", "names": ["index", "all", "content", "skippable", "convert", "ok", "castFactory", "anyFactory", "propsFactory", "typeFactory", "index", "all", "looksLikeANode", "is", "convert", "whitespace"]}
{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/edit/closebrackets.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  var defaults = {\n    pairs: \"()[]{}''\\\"\\\"\",\n    closeBefore: \")]}'\\\":;>\",\n    triples: \"\",\n    explode: \"[]{}\"\n  };\n\n  var Pos = CodeMirror.Pos;\n\n  CodeMirror.defineOption(\"autoCloseBrackets\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.removeKeyMap(keyMap);\n      cm.state.closeBrackets = null;\n    }\n    if (val) {\n      ensureBound(getOption(val, \"pairs\"))\n      cm.state.closeBrackets = val;\n      cm.addKeyMap(keyMap);\n    }\n  });\n\n  function getOption(conf, name) {\n    if (name == \"pairs\" && typeof conf == \"string\") return conf;\n    if (typeof conf == \"object\" && conf[name] != null) return conf[name];\n    return defaults[name];\n  }\n\n  var keyMap = {Backspace: handleBackspace, Enter: handleEnter};\n  function ensureBound(chars) {\n    for (var i = 0; i < chars.length; i++) {\n      var ch = chars.charAt(i), key = \"'\" + ch + \"'\"\n      if (!keyMap[key]) keyMap[key] = handler(ch)\n    }\n  }\n  ensureBound(defaults.pairs + \"`\")\n\n  function handler(ch) {\n    return function(cm) { return handleChar(cm, ch); };\n  }\n\n  function getConfig(cm) {\n    var deflt = cm.state.closeBrackets;\n    if (!deflt || deflt.override) return deflt;\n    var mode = cm.getModeAt(cm.getCursor());\n    return mode.closeBrackets || deflt;\n  }\n\n  function handleBackspace(cm) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || pairs.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var cur = ranges[i].head;\n      cm.replaceRange(\"\", Pos(cur.line, cur.ch - 1), Pos(cur.line, cur.ch + 1), \"+delete\");\n    }\n  }\n\n  function handleEnter(cm) {\n    var conf = getConfig(cm);\n    var explode = conf && getOption(conf, \"explode\");\n    if (!explode || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || explode.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    cm.operation(function() {\n      var linesep = cm.lineSeparator() || \"\\n\";\n      cm.replaceSelection(linesep + linesep, null);\n      moveSel(cm, -1)\n      ranges = cm.listSelections();\n      for (var i = 0; i < ranges.length; i++) {\n        var line = ranges[i].head.line;\n        cm.indentLine(line, null, true);\n        cm.indentLine(line + 1, null, true);\n      }\n    });\n  }\n\n  function moveSel(cm, dir) {\n    var newRanges = [], ranges = cm.listSelections(), primary = 0\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i]\n      if (range.head == cm.getCursor()) primary = i\n      var pos = range.head.ch || dir > 0 ? {line: range.head.line, ch: range.head.ch + dir} : {line: range.head.line - 1}\n      newRanges.push({anchor: pos, head: pos})\n    }\n    cm.setSelections(newRanges, primary)\n  }\n\n  function contractSelection(sel) {\n    var inverted = CodeMirror.cmpPos(sel.anchor, sel.head) > 0;\n    return {anchor: new Pos(sel.anchor.line, sel.anchor.ch + (inverted ? -1 : 1)),\n            head: new Pos(sel.head.line, sel.head.ch + (inverted ? 1 : -1))};\n  }\n\n  function handleChar(cm, ch) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var pos = pairs.indexOf(ch);\n    if (pos == -1) return CodeMirror.Pass;\n\n    var closeBefore = getOption(conf,\"closeBefore\");\n\n    var triples = getOption(conf, \"triples\");\n\n    var identical = pairs.charAt(pos + 1) == ch;\n    var ranges = cm.listSelections();\n    var opening = pos % 2 == 0;\n\n    var type;\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], cur = range.head, curType;\n      var next = cm.getRange(cur, Pos(cur.line, cur.ch + 1));\n      if (opening && !range.empty()) {\n        curType = \"surround\";\n      } else if ((identical || !opening) && next == ch) {\n        if (identical && stringStartsAfter(cm, cur))\n          curType = \"both\";\n        else if (triples.indexOf(ch) >= 0 && cm.getRange(cur, Pos(cur.line, cur.ch + 3)) == ch + ch + ch)\n          curType = \"skipThree\";\n        else\n          curType = \"skip\";\n      } else if (identical && cur.ch > 1 && triples.indexOf(ch) >= 0 &&\n                 cm.getRange(Pos(cur.line, cur.ch - 2), cur) == ch + ch) {\n        if (cur.ch > 2 && /\\bstring/.test(cm.getTokenTypeAt(Pos(cur.line, cur.ch - 2)))) return CodeMirror.Pass;\n        curType = \"addFour\";\n      } else if (identical) {\n        var prev = cur.ch == 0 ? \" \" : cm.getRange(Pos(cur.line, cur.ch - 1), cur)\n        if (!CodeMirror.isWordChar(next) && prev != ch && !CodeMirror.isWordChar(prev)) curType = \"both\";\n        else return CodeMirror.Pass;\n      } else if (opening && (next.length === 0 || /\\s/.test(next) || closeBefore.indexOf(next) > -1)) {\n        curType = \"both\";\n      } else {\n        return CodeMirror.Pass;\n      }\n      if (!type) type = curType;\n      else if (type != curType) return CodeMirror.Pass;\n    }\n\n    var left = pos % 2 ? pairs.charAt(pos - 1) : ch;\n    var right = pos % 2 ? ch : pairs.charAt(pos + 1);\n    cm.operation(function() {\n      if (type == \"skip\") {\n        moveSel(cm, 1)\n      } else if (type == \"skipThree\") {\n        moveSel(cm, 3)\n      } else if (type == \"surround\") {\n        var sels = cm.getSelections();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = left + sels[i] + right;\n        cm.replaceSelections(sels, \"around\");\n        sels = cm.listSelections().slice();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = contractSelection(sels[i]);\n        cm.setSelections(sels);\n      } else if (type == \"both\") {\n        cm.replaceSelection(left + right, null);\n        cm.triggerElectric(left + right);\n        moveSel(cm, -1)\n      } else if (type == \"addFour\") {\n        cm.replaceSelection(left + left + left + left, \"before\");\n        moveSel(cm, 1)\n      }\n    });\n  }\n\n  function charsAround(cm, pos) {\n    var str = cm.getRange(Pos(pos.line, pos.ch - 1),\n                          Pos(pos.line, pos.ch + 1));\n    return str.length == 2 ? str : null;\n  }\n\n  function stringStartsAfter(cm, pos) {\n    var token = cm.getTokenAt(Pos(pos.line, pos.ch + 1))\n    return /\\bstring/.test(token.type) && token.start == pos.ch &&\n      (pos.ch == 0 || !/\\bstring/.test(cm.getTokenTypeAt(pos)))\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA+B,CAAA;EAKtC,GAAE,SAAS,YAAY;AACtB,QAAI,WAAW;MACb,OAAO;MACP,aAAa;MACb,SAAS;MACT,SAAS;IACb;AAEE,QAAI,MAAM,WAAW;AAErB,eAAW,aAAa,qBAAqB,OAAO,SAAS,IAAI,KAAK,KAAK;AACzE,UAAI,OAAO,OAAO,WAAW,MAAM;AACjC,WAAG,aAAa,MAAM;AACtB,WAAG,MAAM,gBAAgB;MAC1B;AACD,UAAI,KAAK;AACP,oBAAY,UAAU,KAAK,OAAO,CAAC;AACnC,WAAG,MAAM,gBAAgB;AACzB,WAAG,UAAU,MAAM;MACpB;IACL,CAAG;AAED,aAAS,UAAU,MAAM,MAAM;AAC7B,UAAI,QAAQ,WAAW,OAAO,QAAQ,SAAU,QAAO;AACvD,UAAI,OAAO,QAAQ,YAAY,KAAK,IAAI,KAAK,KAAM,QAAO,KAAK,IAAI;AACnE,aAAO,SAAS,IAAI;IACrB;AAED,QAAI,SAAS,EAAC,WAAW,iBAAiB,OAAO,YAAW;AAC5D,aAAS,YAAY,OAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,KAAK,MAAM,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAC3C,YAAI,CAAC,OAAO,GAAG,EAAG,QAAO,GAAG,IAAI,QAAQ,EAAE;MAC3C;IACF;AACD,gBAAY,SAAS,QAAQ,GAAG;AAEhC,aAAS,QAAQ,IAAI;AACnB,aAAO,SAAS,IAAI;AAAE,eAAO,WAAW,IAAI,EAAE;MAAA;IAC/C;AAED,aAAS,UAAU,IAAI;AACrB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,SAAS,MAAM,SAAU,QAAO;AACrC,UAAI,OAAO,GAAG,UAAU,GAAG,UAAW,CAAA;AACtC,aAAO,KAAK,iBAAiB;IAC9B;AAED,aAAS,gBAAgB,IAAI;AAC3B,UAAI,OAAO,UAAU,EAAE;AACvB,UAAI,CAAC,QAAQ,GAAG,UAAU,cAAc,EAAG,QAAO,WAAW;AAE7D,UAAI,QAAQ,UAAU,MAAM,OAAO;AACnC,UAAI,SAAS,GAAG,eAAA;AAChB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,CAAC,OAAO,CAAC,EAAE,MAAK,EAAI,QAAO,WAAW;AAC1C,YAAI,SAAS,YAAY,IAAI,OAAO,CAAC,EAAE,IAAI;AAC3C,YAAI,CAAC,UAAU,MAAM,QAAQ,MAAM,IAAI,KAAK,EAAG,QAAO,WAAW;MAClE;AACD,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,MAAM,OAAO,CAAC,EAAE;AACpB,WAAG,aAAa,IAAI,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS;MACpF;IACF;AAED,aAAS,YAAY,IAAI;AACvB,UAAI,OAAO,UAAU,EAAE;AACvB,UAAI,UAAU,QAAQ,UAAU,MAAM,SAAS;AAC/C,UAAI,CAAC,WAAW,GAAG,UAAU,cAAc,EAAG,QAAO,WAAW;AAEhE,UAAI,SAAS,GAAG,eAAA;AAChB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,CAAC,OAAO,CAAC,EAAE,MAAK,EAAI,QAAO,WAAW;AAC1C,YAAI,SAAS,YAAY,IAAI,OAAO,CAAC,EAAE,IAAI;AAC3C,YAAI,CAAC,UAAU,QAAQ,QAAQ,MAAM,IAAI,KAAK,EAAG,QAAO,WAAW;MACpE;AACD,SAAG,UAAU,WAAW;AACtB,YAAI,UAAU,GAAG,cAAa,KAAM;AACpC,WAAG,iBAAiB,UAAU,SAAS,IAAI;AAC3C,gBAAQ,IAAI,EAAE;AACd,iBAAS,GAAG,eAAA;AACZ,iBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,cAAI,OAAO,OAAOA,EAAC,EAAE,KAAK;AAC1B,aAAG,WAAW,MAAM,MAAM,IAAI;AAC9B,aAAG,WAAW,OAAO,GAAG,MAAM,IAAI;QACnC;MACP,CAAK;IACF;AAED,aAAS,QAAQ,IAAI,KAAK;AACxB,UAAI,YAAY,CAAA,GAAI,SAAS,GAAG,eAAc,GAAI,UAAU;AAC5D,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,QAAQ,GAAG,UAAW,EAAE,WAAU;AAC5C,YAAI,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI,EAAC,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,KAAK,IAAG,IAAI,EAAC,MAAM,MAAM,KAAK,OAAO,EAAC;AAClH,kBAAU,KAAK,EAAC,QAAQ,KAAK,MAAM,IAAG,CAAC;MACxC;AACD,SAAG,cAAc,WAAW,OAAO;IACpC;AAED,aAAS,kBAAkB,KAAK;AAC9B,UAAI,WAAW,WAAW,OAAO,IAAI,QAAQ,IAAI,IAAI,IAAI;AACzD,aAAO;QAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,KAAK,EAAE;QACpE,MAAM,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;MAAC;IACxE;AAED,aAAS,WAAW,IAAI,IAAI;AAC1B,UAAI,OAAO,UAAU,EAAE;AACvB,UAAI,CAAC,QAAQ,GAAG,UAAU,cAAc,EAAG,QAAO,WAAW;AAE7D,UAAI,QAAQ,UAAU,MAAM,OAAO;AACnC,UAAI,MAAM,MAAM,QAAQ,EAAE;AAC1B,UAAI,OAAO,GAAI,QAAO,WAAW;AAEjC,UAAI,cAAc,UAAU,MAAK,aAAa;AAE9C,UAAI,UAAU,UAAU,MAAM,SAAS;AAEvC,UAAI,YAAY,MAAM,OAAO,MAAM,CAAC,KAAK;AACzC,UAAI,SAAS,GAAG,eAAA;AAChB,UAAI,UAAU,MAAM,KAAK;AAEzB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,MAAM,MAAM;AACzC,YAAI,OAAO,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACrD,YAAI,WAAW,CAAC,MAAM,MAAA,GAAS;AAC7B,oBAAU;QACX,YAAW,aAAa,CAAC,YAAY,QAAQ,IAAI;AAChD,cAAI,aAAa,kBAAkB,IAAI,GAAG;AACxC,sBAAU;mBACH,QAAQ,QAAQ,EAAE,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK;AAC5F,sBAAU;;AAEV,sBAAU;QACpB,WAAiB,aAAa,IAAI,KAAK,KAAK,QAAQ,QAAQ,EAAE,KAAK,KAClD,GAAG,SAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,KAAK,IAAI;AACjE,cAAI,IAAI,KAAK,KAAK,WAAW,KAAK,GAAG,eAAe,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAG,QAAO,WAAW;AACnG,oBAAU;QACX,WAAU,WAAW;AACpB,cAAI,OAAO,IAAI,MAAM,IAAI,MAAM,GAAG,SAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG;AACzE,cAAI,CAAC,WAAW,WAAW,IAAI,KAAK,QAAQ,MAAM,CAAC,WAAW,WAAW,IAAI,EAAG,WAAU;cACrF,QAAO,WAAW;QACxB,WAAU,YAAY,KAAK,WAAW,KAAK,KAAK,KAAK,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AAC9F,oBAAU;QAClB,OAAa;AACL,iBAAO,WAAW;QACnB;AACD,YAAI,CAAC,KAAM,QAAO;iBACT,QAAQ,QAAS,QAAO,WAAW;MAC7C;AAED,UAAI,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,CAAC,IAAI;AAC7C,UAAI,QAAQ,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,CAAC;AAC/C,SAAG,UAAU,WAAW;AACtB,YAAI,QAAQ,QAAQ;AAClB,kBAAQ,IAAI,CAAC;QACrB,WAAiB,QAAQ,aAAa;AAC9B,kBAAQ,IAAI,CAAC;QACrB,WAAiB,QAAQ,YAAY;AAC7B,cAAI,OAAO,GAAG,cAAA;AACd,mBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAC/B,iBAAKA,EAAC,IAAI,OAAO,KAAKA,EAAC,IAAI;AAC7B,aAAG,kBAAkB,MAAM,QAAQ;AACnC,iBAAO,GAAG,eAAgB,EAAC,MAAK;AAChC,mBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAC/B,iBAAKA,EAAC,IAAI,kBAAkB,KAAKA,EAAC,CAAC;AACrC,aAAG,cAAc,IAAI;QAC7B,WAAiB,QAAQ,QAAQ;AACzB,aAAG,iBAAiB,OAAO,OAAO,IAAI;AACtC,aAAG,gBAAgB,OAAO,KAAK;AAC/B,kBAAQ,IAAI,EAAE;QACtB,WAAiB,QAAQ,WAAW;AAC5B,aAAG,iBAAiB,OAAO,OAAO,OAAO,MAAM,QAAQ;AACvD,kBAAQ,IAAI,CAAC;QACd;MACP,CAAK;IACF;AAED,aAAS,YAAY,IAAI,KAAK;AAC5B,UAAI,MAAM,GAAG;QAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;QACxB,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;MAAC;AAC/C,aAAO,IAAI,UAAU,IAAI,MAAM;IAChC;AAED,aAAS,kBAAkB,IAAI,KAAK;AAClC,UAAI,QAAQ,GAAG,WAAW,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACnD,aAAO,WAAW,KAAK,MAAM,IAAI,KAAK,MAAM,SAAS,IAAI,OACtD,IAAI,MAAM,KAAK,CAAC,WAAW,KAAK,GAAG,eAAe,GAAG,CAAC;IAC1D;EACH,CAAC;;;;;;;;", "names": ["require$$0", "i"]}
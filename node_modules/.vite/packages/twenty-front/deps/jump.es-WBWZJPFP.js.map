{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/jump-addon.js", "../../../../@graphiql/codemirror-graphql/esm/jump.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nCodeMirror.defineOption('jump', false, (cm, options, old) => {\n    if (old && old !== CodeMirror.Init) {\n        const oldOnMouseOver = cm.state.jump.onMouseOver;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseover', oldOnMouseOver);\n        const oldOnMouseOut = cm.state.jump.onMouseOut;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', oldOnMouseOut);\n        CodeMirror.off(document, 'keydown', cm.state.jump.onKeyDown);\n        delete cm.state.jump;\n    }\n    if (options) {\n        const state = (cm.state.jump = {\n            options,\n            onMouseOver: onMouseOver.bind(null, cm),\n            onMouseOut: onMouseOut.bind(null, cm),\n            onKeyDown: onKeyDown.bind(null, cm),\n        });\n        CodeMirror.on(cm.getWrapperElement(), 'mouseover', state.onMouseOver);\n        CodeMirror.on(cm.getWrapperElement(), 'mouseout', state.onMouseOut);\n        CodeMirror.on(document, 'keydown', state.onKeyDown);\n    }\n});\nfunction onMouseOver(cm, event) {\n    const target = event.target || event.srcElement;\n    if (!(target instanceof HTMLElement)) {\n        return;\n    }\n    if ((target === null || target === void 0 ? void 0 : target.nodeName) !== 'SPAN') {\n        return;\n    }\n    const box = target.getBoundingClientRect();\n    const cursor = {\n        left: (box.left + box.right) / 2,\n        top: (box.top + box.bottom) / 2,\n    };\n    cm.state.jump.cursor = cursor;\n    if (cm.state.jump.isHoldingModifier) {\n        enableJumpMode(cm);\n    }\n}\nfunction onMouseOut(cm) {\n    if (!cm.state.jump.isHoldingModifier && cm.state.jump.cursor) {\n        cm.state.jump.cursor = null;\n        return;\n    }\n    if (cm.state.jump.isHoldingModifier && cm.state.jump.marker) {\n        disableJumpMode(cm);\n    }\n}\nfunction onKeyDown(cm, event) {\n    if (cm.state.jump.isHoldingModifier || !isJumpModifier(event.key)) {\n        return;\n    }\n    cm.state.jump.isHoldingModifier = true;\n    if (cm.state.jump.cursor) {\n        enableJumpMode(cm);\n    }\n    const onKeyUp = (upEvent) => {\n        if (upEvent.code !== event.code) {\n            return;\n        }\n        cm.state.jump.isHoldingModifier = false;\n        if (cm.state.jump.marker) {\n            disableJumpMode(cm);\n        }\n        CodeMirror.off(document, 'keyup', onKeyUp);\n        CodeMirror.off(document, 'click', onClick);\n        cm.off('mousedown', onMouseDown);\n    };\n    const onClick = (clickEvent) => {\n        const { destination, options } = cm.state.jump;\n        if (destination) {\n            options.onClick(destination, clickEvent);\n        }\n    };\n    const onMouseDown = (_, downEvent) => {\n        if (cm.state.jump.destination) {\n            downEvent.codemirrorIgnore = true;\n        }\n    };\n    CodeMirror.on(document, 'keyup', onKeyUp);\n    CodeMirror.on(document, 'click', onClick);\n    cm.on('mousedown', onMouseDown);\n}\nconst isMac = typeof navigator !== 'undefined' && (navigator === null || navigator === void 0 ? void 0 : navigator.appVersion.includes('Mac'));\nfunction isJumpModifier(key) {\n    return key === (isMac ? 'Meta' : 'Control');\n}\nfunction enableJumpMode(cm) {\n    if (cm.state.jump.marker) {\n        return;\n    }\n    const { cursor, options } = cm.state.jump;\n    const pos = cm.coordsChar(cursor);\n    const token = cm.getTokenAt(pos, true);\n    const getDestination = options.getDestination || cm.getHelper(pos, 'jump');\n    if (getDestination) {\n        const destination = getDestination(token, options, cm);\n        if (destination) {\n            const marker = cm.markText({ line: pos.line, ch: token.start }, { line: pos.line, ch: token.end }, { className: 'CodeMirror-jump-token' });\n            cm.state.jump.marker = marker;\n            cm.state.jump.destination = destination;\n        }\n    }\n}\nfunction disableJumpMode(cm) {\n    const { marker } = cm.state.jump;\n    cm.state.jump.marker = null;\n    cm.state.jump.destination = null;\n    marker.clear();\n}\n//# sourceMappingURL=jump-addon.js.map", "import CodeMirror from 'codemirror';\nimport getTypeInfo from './utils/getTypeInfo';\nimport { getArgumentReference, getDirectiveReference, getEnumValueReference, getFieldReference, getTypeReference, } from './utils/SchemaReference';\nimport './utils/jump-addon';\nCodeMirror.registerHelper('jump', 'graphql', (token, options) => {\n    if (!options.schema || !options.onClick || !token.state) {\n        return;\n    }\n    const { state } = token;\n    const { kind, step } = state;\n    const typeInfo = getTypeInfo(options.schema, state);\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef)) {\n        return getFieldReference(typeInfo);\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        return getDirectiveReference(typeInfo);\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        return getArgumentReference(typeInfo);\n    }\n    if (kind === 'EnumValue' && typeInfo.enumValue) {\n        return getEnumValueReference(typeInfo);\n    }\n    if (kind === 'NamedType' && typeInfo.type) {\n        return getTypeReference(typeInfo);\n    }\n});\n//# sourceMappingURL=jump.js.map"], "mappings": ";;;;;;;;;;;;;;;;;AACA,WAAW,aAAa,QAAQ,OAAO,CAAC,IAAI,SAAS,QAAQ;AACzD,MAAI,OAAO,QAAQ,WAAW,MAAM;AAChC,UAAM,iBAAiB,GAAG,MAAM,KAAK;AACrC,eAAW,IAAI,GAAG,kBAAiB,GAAI,aAAa,cAAc;AAClE,UAAM,gBAAgB,GAAG,MAAM,KAAK;AACpC,eAAW,IAAI,GAAG,kBAAiB,GAAI,YAAY,aAAa;AAChE,eAAW,IAAI,UAAU,WAAW,GAAG,MAAM,KAAK,SAAS;AAC3D,WAAO,GAAG,MAAM;EACnB;AACD,MAAI,SAAS;AACT,UAAM,QAAS,GAAG,MAAM,OAAO;MAC3B;MACA,aAAa,YAAY,KAAK,MAAM,EAAE;MACtC,YAAY,WAAW,KAAK,MAAM,EAAE;MACpC,WAAW,UAAU,KAAK,MAAM,EAAE;IAC9C;AACQ,eAAW,GAAG,GAAG,kBAAmB,GAAE,aAAa,MAAM,WAAW;AACpE,eAAW,GAAG,GAAG,kBAAmB,GAAE,YAAY,MAAM,UAAU;AAClE,eAAW,GAAG,UAAU,WAAW,MAAM,SAAS;EACrD;AACL,CAAC;AACD,SAAS,YAAY,IAAI,OAAO;AAC5B,QAAM,SAAS,MAAM,UAAU,MAAM;AACrC,MAAI,EAAE,kBAAkB,cAAc;AAClC;EACH;AACD,OAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,cAAc,QAAQ;AAC9E;EACH;AACD,QAAM,MAAM,OAAO,sBAAA;AACnB,QAAM,SAAS;IACX,OAAO,IAAI,OAAO,IAAI,SAAS;IAC/B,MAAM,IAAI,MAAM,IAAI,UAAU;EACtC;AACI,KAAG,MAAM,KAAK,SAAS;AACvB,MAAI,GAAG,MAAM,KAAK,mBAAmB;AACjC,mBAAe,EAAE;EACpB;AACL;AACA,SAAS,WAAW,IAAI;AACpB,MAAI,CAAC,GAAG,MAAM,KAAK,qBAAqB,GAAG,MAAM,KAAK,QAAQ;AAC1D,OAAG,MAAM,KAAK,SAAS;AACvB;EACH;AACD,MAAI,GAAG,MAAM,KAAK,qBAAqB,GAAG,MAAM,KAAK,QAAQ;AACzD,oBAAgB,EAAE;EACrB;AACL;AACA,SAAS,UAAU,IAAI,OAAO;AAC1B,MAAI,GAAG,MAAM,KAAK,qBAAqB,CAAC,eAAe,MAAM,GAAG,GAAG;AAC/D;EACH;AACD,KAAG,MAAM,KAAK,oBAAoB;AAClC,MAAI,GAAG,MAAM,KAAK,QAAQ;AACtB,mBAAe,EAAE;EACpB;AACD,QAAM,UAAU,CAAC,YAAY;AACzB,QAAI,QAAQ,SAAS,MAAM,MAAM;AAC7B;IACH;AACD,OAAG,MAAM,KAAK,oBAAoB;AAClC,QAAI,GAAG,MAAM,KAAK,QAAQ;AACtB,sBAAgB,EAAE;IACrB;AACD,eAAW,IAAI,UAAU,SAAS,OAAO;AACzC,eAAW,IAAI,UAAU,SAAS,OAAO;AACzC,OAAG,IAAI,aAAa,WAAW;EACvC;AACI,QAAM,UAAU,CAAC,eAAe;AAC5B,UAAM,EAAE,aAAa,QAAO,IAAK,GAAG,MAAM;AAC1C,QAAI,aAAa;AACb,cAAQ,QAAQ,aAAa,UAAU;IAC1C;EACT;AACI,QAAM,cAAc,CAAC,GAAG,cAAc;AAClC,QAAI,GAAG,MAAM,KAAK,aAAa;AAC3B,gBAAU,mBAAmB;IAChC;EACT;AACI,aAAW,GAAG,UAAU,SAAS,OAAO;AACxC,aAAW,GAAG,UAAU,SAAS,OAAO;AACxC,KAAG,GAAG,aAAa,WAAW;AAClC;AACA,IAAM,QAAQ,OAAO,cAAc,gBAAgB,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,WAAW,SAAS,KAAK;AAC5I,SAAS,eAAe,KAAK;AACzB,SAAO,SAAS,QAAQ,SAAS;AACrC;AACA,SAAS,eAAe,IAAI;AACxB,MAAI,GAAG,MAAM,KAAK,QAAQ;AACtB;EACH;AACD,QAAM,EAAE,QAAQ,QAAO,IAAK,GAAG,MAAM;AACrC,QAAM,MAAM,GAAG,WAAW,MAAM;AAChC,QAAM,QAAQ,GAAG,WAAW,KAAK,IAAI;AACrC,QAAM,iBAAiB,QAAQ,kBAAkB,GAAG,UAAU,KAAK,MAAM;AACzE,MAAI,gBAAgB;AAChB,UAAM,cAAc,eAAe,OAAO,SAAS,EAAE;AACrD,QAAI,aAAa;AACb,YAAM,SAAS,GAAG,SAAS,EAAE,MAAM,IAAI,MAAM,IAAI,MAAM,MAAK,GAAI,EAAE,MAAM,IAAI,MAAM,IAAI,MAAM,IAAG,GAAI,EAAE,WAAW,wBAAuB,CAAE;AACzI,SAAG,MAAM,KAAK,SAAS;AACvB,SAAG,MAAM,KAAK,cAAc;IAC/B;EACJ;AACL;AACA,SAAS,gBAAgB,IAAI;AACzB,QAAM,EAAE,OAAQ,IAAG,GAAG,MAAM;AAC5B,KAAG,MAAM,KAAK,SAAS;AACvB,KAAG,MAAM,KAAK,cAAc;AAC5B,SAAO,MAAK;AAChB;AC1GA,WAAW,eAAe,QAAQ,WAAW,CAAC,OAAO,YAAY;AAC7D,MAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,WAAW,CAAC,MAAM,OAAO;AACrD;EACH;AACD,QAAM,EAAE,MAAO,IAAG;AAClB,QAAM,EAAE,MAAM,KAAM,IAAG;AACvB,QAAM,WAAW,YAAY,QAAQ,QAAQ,KAAK;AAClD,MAAK,SAAS,WAAW,SAAS,KAAK,SAAS,YAC3C,SAAS,kBAAkB,SAAS,KAAK,SAAS,UAAW;AAC9D,WAAO,kBAAkB,QAAQ;EACpC;AACD,MAAI,SAAS,eAAe,SAAS,KAAK,SAAS,cAAc;AAC7D,WAAO,sBAAsB,QAAQ;EACxC;AACD,MAAI,SAAS,cAAc,SAAS,KAAK,SAAS,QAAQ;AACtD,WAAO,qBAAqB,QAAQ;EACvC;AACD,MAAI,SAAS,eAAe,SAAS,WAAW;AAC5C,WAAO,sBAAsB,QAAQ;EACxC;AACD,MAAI,SAAS,eAAe,SAAS,MAAM;AACvC,WAAO,iBAAiB,QAAQ;EACnC;AACL,CAAC;", "names": []}
{"version": 3, "sources": ["../../../../hast-util-is-element/lib/index.js", "../../../../hast-util-embedded/lib/index.js", "../../../../hast-util-has-property/lib/index.js", "../../../../hast-util-is-body-ok-link/lib/index.js", "../../../../hast-util-phrasing/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is an element.\n * @param {unknown} this\n *   Context object (`this`) to call `test` with\n * @param {unknown} [element]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | null | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean}\n *   Whether this is an element and passes a test.\n *\n * @typedef {Array<TestFunction | string> | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary element.\n *\n *   * when `string`, checks that the element has that tag name\n *   * when `function`, see `TestFunction`\n *   * when `Array`, checks if one of the subtests pass\n *\n * @callback TestFunction\n *   Check if an element passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Element} element\n *   An element.\n * @param {number | undefined} [index]\n *   Position of `element` in its parent.\n * @param {Parents | undefined} [parent]\n *   Parent of `element`.\n * @returns {boolean | undefined | void}\n *   Whether this element passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `element` is an `Element` and whether it passes the given test.\n *\n * @param element\n *   Thing to check, typically `element`.\n * @param test\n *   Check for a specific element.\n * @param index\n *   Position of `element` in its parent.\n * @param parent\n *   Parent of `element`.\n * @param context\n *   Context object (`this`) to call `test` with.\n * @returns\n *   Whether `element` is an `Element` and passes a test.\n * @throws\n *   When an incorrect `test`, `index`, or `parent` is given; there is no error\n *   thrown when `element` is not a node or not an element.\n */\nexport const isElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((element?: null | undefined) => false) &\n   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [element]\n     * @param {Test | undefined} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parents | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (element, test, index, parent, context) {\n      const check = convertElement(test)\n\n      if (\n        index !== null &&\n        index !== undefined &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite `index`')\n      }\n\n      if (\n        parent !== null &&\n        parent !== undefined &&\n        (!parent.type || !parent.children)\n      ) {\n        throw new Error('Expected valid `parent`')\n      }\n\n      if (\n        (index === null || index === undefined) !==\n        (parent === null || parent === undefined)\n      ) {\n        throw new Error('Expected both `index` and `parent`')\n      }\n\n      return looksLikeAnElement(element)\n        ? check.call(context, element, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate a check from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * an `element`, `index`, and `parent`.\n *\n * @param test\n *   A test for a specific element.\n * @returns\n *   A check.\n */\nexport const convertElement =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &\n   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &\n   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test | null | undefined} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return element\n      }\n\n      if (typeof test === 'string') {\n        return tagNameFactory(test)\n      }\n\n      // Assume array.\n      if (typeof test === 'object') {\n        return anyFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or array as `test`')\n    }\n  )\n\n/**\n * Handle multiple tests.\n *\n * @param {Array<TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convertElement(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn a string into a test for an element with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction tagNameFactory(check) {\n  return castFactory(tagName)\n\n  /**\n   * @param {Element} element\n   * @returns {boolean}\n   */\n  function tagName(element) {\n    return element.tagName === check\n  }\n}\n\n/**\n * Turn a custom test into a test for an element that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeAnElement(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\n/**\n * Make sure something is an element.\n *\n * @param {unknown} element\n * @returns {element is Element}\n */\nfunction element(element) {\n  return Boolean(\n    element &&\n      typeof element === 'object' &&\n      'type' in element &&\n      element.type === 'element' &&\n      'tagName' in element &&\n      typeof element.tagName === 'string'\n  )\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Element}\n */\nfunction looksLikeAnElement(value) {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    'tagName' in value\n  )\n}\n", "import {convertElement} from 'hast-util-is-element'\n\n/**\n * Check if a node is a *embedded content*.\n *\n * @param value\n *   Thing to check (typically `Node`).\n * @returns\n *   Whether `value` is an element considered embedded content.\n *\n *   The elements `audio`, `canvas`, `embed`, `iframe`, `img`, `math`,\n *   `object`, `picture`, `svg`, and `video` are embedded content.\n */\nexport const embedded = convertElement(\n  /**\n   * @param element\n   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}\n   */\n  function (element) {\n    return (\n      element.tagName === 'audio' ||\n      element.tagName === 'canvas' ||\n      element.tagName === 'embed' ||\n      element.tagName === 'iframe' ||\n      element.tagName === 'img' ||\n      element.tagName === 'math' ||\n      element.tagName === 'object' ||\n      element.tagName === 'picture' ||\n      element.tagName === 'svg' ||\n      element.tagName === 'video'\n    )\n  }\n)\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Check if `node` is an element and has a `name` property.\n *\n * @template {string} Key\n *   Type of key.\n * @param {Nodes} node\n *   Node to check (typically `Element`).\n * @param {Key} name\n *   Property name to check.\n * @returns {node is Element & {properties: Record<Key, Array<number | string> | number | string | true>}}}\n *   Whether `node` is an element that has a `name` property.\n *\n *   Note: see <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/27c9274/types/hast/index.d.ts#L37C29-L37C98>.\n */\nexport function hasProperty(node, name) {\n  const value =\n    node.type === 'element' &&\n    own.call(node.properties, name) &&\n    node.properties[name]\n\n  return value !== null && value !== undefined && value !== false\n}\n", "/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\nconst list = new Set(['pingback', 'prefetch', 'stylesheet'])\n\n/**\n * Checks whether a node is a “body OK” link.\n *\n * @param {Nodes} node\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is a “body OK” link.\n */\nexport function isBodyOkLink(node) {\n  if (node.type !== 'element' || node.tagName !== 'link') {\n    return false\n  }\n\n  if (node.properties.itemProp) {\n    return true\n  }\n\n  const rel = node.properties.rel\n  let index = -1\n\n  if (!Array.isArray(rel) || rel.length === 0) {\n    return false\n  }\n\n  while (++index < rel.length) {\n    if (!list.has(String(rel[index]))) {\n      return false\n    }\n  }\n\n  return true\n}\n", "/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {hasProperty} from 'hast-util-has-property'\nimport {isBodyOkLink} from 'hast-util-is-body-ok-link'\nimport {convertElement} from 'hast-util-is-element'\n\nconst basic = convertElement([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = convertElement('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nexport function phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      embedded(value) ||\n      isBodyOkLink(value) ||\n      (meta(value) && hasProperty(value, 'itemProp'))\n  )\n}\n"], "mappings": ";AAmEO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBT,SAAUA,UAAS,MAAM,OAAO,QAAQ,SAAS;AAC/C,UAAM,QAAQ,eAAe,IAAI;AAEjC,QACE,UAAU,QACV,UAAU,WACT,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,oBACnB;AACA,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAEA,QACE,WAAW,QACX,WAAW,WACV,CAAC,OAAO,QAAQ,CAAC,OAAO,WACzB;AACA,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AAEA,SACG,UAAU,QAAQ,UAAU,aAC5B,WAAW,QAAQ,WAAW,SAC/B;AACA,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAEA,WAAO,mBAAmBA,QAAO,IAC7B,MAAM,KAAK,SAASA,UAAS,OAAO,MAAM,IAC1C;AAAA,EACN;AAAA;AAiBG,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,MAAM;AACd,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,eAAe,IAAI;AAAA,IAC5B;AAGA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACjE;AAAA;AASJ,SAAS,WAAW,OAAO;AAEzB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,WAAO,KAAK,IAAI,eAAe,MAAM,KAAK,CAAC;AAAA,EAC7C;AAEA,SAAO,YAAY,GAAG;AAMtB,WAAS,OAAO,YAAY;AAC1B,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAOA,MAAK,EAAE,MAAM,MAAM,UAAU,EAAG,QAAO;AAAA,IACpD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,eAAe,OAAO;AAC7B,SAAO,YAAY,OAAO;AAM1B,WAAS,QAAQD,UAAS;AACxB,WAAOA,SAAQ,YAAY;AAAA,EAC7B;AACF;AAQA,SAAS,YAAY,cAAc;AACjC,SAAO;AAMP,WAAS,MAAM,OAAO,OAAO,QAAQ;AACnC,WAAO;AAAA,MACL,mBAAmB,KAAK,KACtB,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA,OAAO,UAAU,WAAW,QAAQ;AAAA,QACpC,UAAU;AAAA,MACZ;AAAA,IACJ;AAAA,EACF;AACF;AAQA,SAAS,QAAQA,UAAS;AACxB,SAAO;AAAA,IACLA,YACE,OAAOA,aAAY,YACnB,UAAUA,YACVA,SAAQ,SAAS,aACjB,aAAaA,YACb,OAAOA,SAAQ,YAAY;AAAA,EAC/B;AACF;AAMA,SAAS,mBAAmB,OAAO;AACjC,SACE,UAAU,QACV,OAAO,UAAU,YACjB,UAAU,SACV,aAAa;AAEjB;;;ACxQO,IAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,SAAUE,UAAS;AACjB,WACEA,SAAQ,YAAY,WACpBA,SAAQ,YAAY,YACpBA,SAAQ,YAAY,WACpBA,SAAQ,YAAY,YACpBA,SAAQ,YAAY,SACpBA,SAAQ,YAAY,UACpBA,SAAQ,YAAY,YACpBA,SAAQ,YAAY,aACpBA,SAAQ,YAAY,SACpBA,SAAQ,YAAY;AAAA,EAExB;AACF;;;AC3BA,IAAM,MAAM,CAAC,EAAE;AAgBR,SAAS,YAAY,MAAM,MAAM;AACtC,QAAM,QACJ,KAAK,SAAS,aACd,IAAI,KAAK,KAAK,YAAY,IAAI,KAC9B,KAAK,WAAW,IAAI;AAEtB,SAAO,UAAU,QAAQ,UAAU,UAAa,UAAU;AAC5D;;;ACxBA,IAAM,OAAO,oBAAI,IAAI,CAAC,YAAY,YAAY,YAAY,CAAC;AAUpD,SAAS,aAAa,MAAM;AACjC,MAAI,KAAK,SAAS,aAAa,KAAK,YAAY,QAAQ;AACtD,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,WAAW,UAAU;AAC5B,WAAO;AAAA,EACT;AAEA,QAAM,MAAM,KAAK,WAAW;AAC5B,MAAI,QAAQ;AAEZ,MAAI,CAAC,MAAM,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAG;AAC3C,WAAO;AAAA,EACT;AAEA,SAAO,EAAE,QAAQ,IAAI,QAAQ;AAC3B,QAAI,CAAC,KAAK,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AC5BA,IAAM,QAAQ,eAAe;AAAA,EAC3B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,OAAO,eAAe,MAAM;AAU3B,SAAS,SAAS,OAAO;AAC9B,SAAO;AAAA,IACL,MAAM,SAAS,UACb,MAAM,KAAK,KACX,SAAS,KAAK,KACd,aAAa,KAAK,KACjB,KAAK,KAAK,KAAK,YAAY,OAAO,UAAU;AAAA,EACjD;AACF;", "names": ["element", "index", "element"]}
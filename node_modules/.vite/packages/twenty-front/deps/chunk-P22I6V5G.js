import {
  editor_api_exports,
  init_editor_api
} from "./chunk-6ZYQTGC5.js";
import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js
var __defProp, __getOwnPropDesc, __getOwnPropNames, __hasOwnProp, __copyProps, __reExport, monaco_editor_core_exports, conf, language;
var init_typescript = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/typescript/typescript.js"() {
    init_editor_api();
    __defProp = Object.defineProperty;
    __getOwnPropDesc = Object.getOwnPropertyDescriptor;
    __getOwnPropNames = Object.getOwnPropertyNames;
    __hasOwnProp = Object.prototype.hasOwnProperty;
    __copyProps = (to, from, except, desc) => {
      if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))
          if (!__hasOwnProp.call(to, key) && key !== except)
            __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
      }
      return to;
    };
    __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
    monaco_editor_core_exports = {};
    __reExport(monaco_editor_core_exports, editor_api_exports);
    conf = {
      wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
      comments: {
        lineComment: "//",
        blockComment: ["/*", "*/"]
      },
      brackets: [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
      ],
      onEnterRules: [
        {
          // e.g. /** | */
          beforeText: /^\s*\/\*\*(?!\/)([^\*]|\*(?!\/))*$/,
          afterText: /^\s*\*\/$/,
          action: {
            indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent,
            appendText: " * "
          }
        },
        {
          // e.g. /** ...|
          beforeText: /^\s*\/\*\*(?!\/)([^\*]|\*(?!\/))*$/,
          action: {
            indentAction: monaco_editor_core_exports.languages.IndentAction.None,
            appendText: " * "
          }
        },
        {
          // e.g.  * ...|
          beforeText: /^(\t|(\ \ ))*\ \*(\ ([^\*]|\*(?!\/))*)?$/,
          action: {
            indentAction: monaco_editor_core_exports.languages.IndentAction.None,
            appendText: "* "
          }
        },
        {
          // e.g.  */|
          beforeText: /^(\t|(\ \ ))*\ \*\/\s*$/,
          action: {
            indentAction: monaco_editor_core_exports.languages.IndentAction.None,
            removeText: 1
          }
        }
      ],
      autoClosingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: '"', close: '"', notIn: ["string"] },
        { open: "'", close: "'", notIn: ["string", "comment"] },
        { open: "`", close: "`", notIn: ["string", "comment"] },
        { open: "/**", close: " */", notIn: ["string"] }
      ],
      folding: {
        markers: {
          start: new RegExp("^\\s*//\\s*#?region\\b"),
          end: new RegExp("^\\s*//\\s*#?endregion\\b")
        }
      }
    };
    language = {
      // Set defaultToken to invalid to see what you do not tokenize yet
      defaultToken: "invalid",
      tokenPostfix: ".ts",
      keywords: [
        // Should match the keys of textToKeywordObj in
        // https://github.com/microsoft/TypeScript/blob/master/src/compiler/scanner.ts
        "abstract",
        "any",
        "as",
        "asserts",
        "bigint",
        "boolean",
        "break",
        "case",
        "catch",
        "class",
        "continue",
        "const",
        "constructor",
        "debugger",
        "declare",
        "default",
        "delete",
        "do",
        "else",
        "enum",
        "export",
        "extends",
        "false",
        "finally",
        "for",
        "from",
        "function",
        "get",
        "if",
        "implements",
        "import",
        "in",
        "infer",
        "instanceof",
        "interface",
        "is",
        "keyof",
        "let",
        "module",
        "namespace",
        "never",
        "new",
        "null",
        "number",
        "object",
        "out",
        "package",
        "private",
        "protected",
        "public",
        "override",
        "readonly",
        "require",
        "global",
        "return",
        "satisfies",
        "set",
        "static",
        "string",
        "super",
        "switch",
        "symbol",
        "this",
        "throw",
        "true",
        "try",
        "type",
        "typeof",
        "undefined",
        "unique",
        "unknown",
        "var",
        "void",
        "while",
        "with",
        "yield",
        "async",
        "await",
        "of"
      ],
      operators: [
        "<=",
        ">=",
        "==",
        "!=",
        "===",
        "!==",
        "=>",
        "+",
        "-",
        "**",
        "*",
        "/",
        "%",
        "++",
        "--",
        "<<",
        "</",
        ">>",
        ">>>",
        "&",
        "|",
        "^",
        "!",
        "~",
        "&&",
        "||",
        "??",
        "?",
        ":",
        "=",
        "+=",
        "-=",
        "*=",
        "**=",
        "/=",
        "%=",
        "<<=",
        ">>=",
        ">>>=",
        "&=",
        "|=",
        "^=",
        "@"
      ],
      // we include these common regular expressions
      symbols: /[=><!~?:&|+\-*\/\^%]+/,
      escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
      digits: /\d+(_+\d+)*/,
      octaldigits: /[0-7]+(_+[0-7]+)*/,
      binarydigits: /[0-1]+(_+[0-1]+)*/,
      hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,
      regexpctl: /[(){}\[\]\$\^|\-*+?\.]/,
      regexpesc: /\\(?:[bBdDfnrstvwWn0\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,
      // The main tokenizer for our languages
      tokenizer: {
        root: [[/[{}]/, "delimiter.bracket"], { include: "common" }],
        common: [
          // identifiers and keywords
          [
            /#?[a-z_$][\w$]*/,
            {
              cases: {
                "@keywords": "keyword",
                "@default": "identifier"
              }
            }
          ],
          [/[A-Z][\w\$]*/, "type.identifier"],
          // to show class names nicely
          // [/[A-Z][\w\$]*/, 'identifier'],
          // whitespace
          { include: "@whitespace" },
          // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)
          [
            /\/(?=([^\\\/]|\\.)+\/([dgimsuy]*)(\s*)(\.|;|,|\)|\]|\}|$))/,
            { token: "regexp", bracket: "@open", next: "@regexp" }
          ],
          // delimiters and operators
          [/[()\[\]]/, "@brackets"],
          [/[<>](?!@symbols)/, "@brackets"],
          [/!(?=([^=]|$))/, "delimiter"],
          [
            /@symbols/,
            {
              cases: {
                "@operators": "delimiter",
                "@default": ""
              }
            }
          ],
          // numbers
          [/(@digits)[eE]([\-+]?(@digits))?/, "number.float"],
          [/(@digits)\.(@digits)([eE][\-+]?(@digits))?/, "number.float"],
          [/0[xX](@hexdigits)n?/, "number.hex"],
          [/0[oO]?(@octaldigits)n?/, "number.octal"],
          [/0[bB](@binarydigits)n?/, "number.binary"],
          [/(@digits)n?/, "number"],
          // delimiter: after number because of .\d floats
          [/[;,.]/, "delimiter"],
          // strings
          [/"([^"\\]|\\.)*$/, "string.invalid"],
          // non-teminated string
          [/'([^'\\]|\\.)*$/, "string.invalid"],
          // non-teminated string
          [/"/, "string", "@string_double"],
          [/'/, "string", "@string_single"],
          [/`/, "string", "@string_backtick"]
        ],
        whitespace: [
          [/[ \t\r\n]+/, ""],
          [/\/\*\*(?!\/)/, "comment.doc", "@jsdoc"],
          [/\/\*/, "comment", "@comment"],
          [/\/\/.*$/, "comment"]
        ],
        comment: [
          [/[^\/*]+/, "comment"],
          [/\*\//, "comment", "@pop"],
          [/[\/*]/, "comment"]
        ],
        jsdoc: [
          [/[^\/*]+/, "comment.doc"],
          [/\*\//, "comment.doc", "@pop"],
          [/[\/*]/, "comment.doc"]
        ],
        // We match regular expression quite precisely
        regexp: [
          [
            /(\{)(\d+(?:,\d*)?)(\})/,
            ["regexp.escape.control", "regexp.escape.control", "regexp.escape.control"]
          ],
          [
            /(\[)(\^?)(?=(?:[^\]\\\/]|\\.)+)/,
            ["regexp.escape.control", { token: "regexp.escape.control", next: "@regexrange" }]
          ],
          [/(\()(\?:|\?=|\?!)/, ["regexp.escape.control", "regexp.escape.control"]],
          [/[()]/, "regexp.escape.control"],
          [/@regexpctl/, "regexp.escape.control"],
          [/[^\\\/]/, "regexp"],
          [/@regexpesc/, "regexp.escape"],
          [/\\\./, "regexp.invalid"],
          [/(\/)([dgimsuy]*)/, [{ token: "regexp", bracket: "@close", next: "@pop" }, "keyword.other"]]
        ],
        regexrange: [
          [/-/, "regexp.escape.control"],
          [/\^/, "regexp.invalid"],
          [/@regexpesc/, "regexp.escape"],
          [/[^\]]/, "regexp"],
          [
            /\]/,
            {
              token: "regexp.escape.control",
              next: "@pop",
              bracket: "@close"
            }
          ]
        ],
        string_double: [
          [/[^\\"]+/, "string"],
          [/@escapes/, "string.escape"],
          [/\\./, "string.escape.invalid"],
          [/"/, "string", "@pop"]
        ],
        string_single: [
          [/[^\\']+/, "string"],
          [/@escapes/, "string.escape"],
          [/\\./, "string.escape.invalid"],
          [/'/, "string", "@pop"]
        ],
        string_backtick: [
          [/\$\{/, { token: "delimiter.bracket", next: "@bracketCounting" }],
          [/[^\\`$]+/, "string"],
          [/@escapes/, "string.escape"],
          [/\\./, "string.escape.invalid"],
          [/`/, "string", "@pop"]
        ],
        bracketCounting: [
          [/\{/, "delimiter.bracket", "@bracketCounting"],
          [/\}/, "delimiter.bracket", "@pop"],
          { include: "common" }
        ]
      }
    };
  }
});

export {
  conf,
  language,
  init_typescript
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/typescript/typescript.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=chunk-P22I6V5G.js.map

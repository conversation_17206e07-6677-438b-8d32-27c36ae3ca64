{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/hintList.js", "../../../../@graphiql/codemirror-graphql/esm/variables/hint.js"], "sourcesContent": ["export default function hintList(cursor, token, list) {\n    const hints = filterAndSortList(list, normalizeText(token.string));\n    if (!hints) {\n        return;\n    }\n    const tokenStart = token.type !== null && /\"|\\w/.test(token.string[0])\n        ? token.start\n        : token.end;\n    return {\n        list: hints,\n        from: { line: cursor.line, ch: tokenStart },\n        to: { line: cursor.line, ch: token.end },\n    };\n}\nfunction filterAndSortList(list, text) {\n    if (!text) {\n        return filterNonEmpty(list, entry => !entry.isDeprecated);\n    }\n    const byProximity = list.map(entry => ({\n        proximity: getProximity(normalizeText(entry.text), text),\n        entry,\n    }));\n    const conciseMatches = filterNonEmpty(filterNonEmpty(byProximity, pair => pair.proximity <= 2), pair => !pair.entry.isDeprecated);\n    const sortedMatches = conciseMatches.sort((a, b) => (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.text.length - b.entry.text.length);\n    return sortedMatches.map(pair => pair.entry);\n}\nfunction filterNonEmpty(array, predicate) {\n    const filtered = array.filter(predicate);\n    return filtered.length === 0 ? array : filtered;\n}\nfunction normalizeText(text) {\n    return text.toLowerCase().replaceAll(/\\W/g, '');\n}\nfunction getProximity(suggestion, text) {\n    let proximity = lexicalDistance(text, suggestion);\n    if (suggestion.length > text.length) {\n        proximity -= suggestion.length - text.length - 1;\n        proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n    }\n    return proximity;\n}\nfunction lexicalDistance(a, b) {\n    let i;\n    let j;\n    const d = [];\n    const aLength = a.length;\n    const bLength = b.length;\n    for (i = 0; i <= aLength; i++) {\n        d[i] = [i];\n    }\n    for (j = 1; j <= bLength; j++) {\n        d[0][j] = j;\n    }\n    for (i = 1; i <= aLength; i++) {\n        for (j = 1; j <= bLength; j++) {\n            const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n            d[i][j] = Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);\n            if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n                d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);\n            }\n        }\n    }\n    return d[aLength][bLength];\n}\n//# sourceMappingURL=hintList.js.map", "import CodeMirror from 'codemirror';\nimport { getNullableType, getNamedType, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, GraphQLBoolean, } from 'graphql';\nimport forEachState from '../utils/forEachState';\nimport hintList from '../utils/hintList';\nCodeMirror.registerHelper('hint', 'graphql-variables', (editor, options) => {\n    const cur = editor.getCursor();\n    const token = editor.getTokenAt(cur);\n    const results = getVariablesHint(cur, token, options);\n    if ((results === null || results === void 0 ? void 0 : results.list) && results.list.length > 0) {\n        results.from = CodeMirror.Pos(results.from.line, results.from.ch);\n        results.to = CodeMirror.Pos(results.to.line, results.to.ch);\n        CodeMirror.signal(editor, 'hasCompletion', editor, results, token);\n    }\n    return results;\n});\nfunction getVariablesHint(cur, token, options) {\n    const state = token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n    const { kind, step } = state;\n    if (kind === 'Document' && step === 0) {\n        return hintList(cur, token, [{ text: '{' }]);\n    }\n    const { variableToType } = options;\n    if (!variableToType) {\n        return;\n    }\n    const typeInfo = getTypeInfo(variableToType, token.state);\n    if (kind === 'Document' || (kind === 'Variable' && step === 0)) {\n        const variableNames = Object.keys(variableToType);\n        return hintList(cur, token, variableNames.map(name => ({\n            text: `\"${name}\": `,\n            type: variableToType[name],\n        })));\n    }\n    if ((kind === 'ObjectValue' || (kind === 'ObjectField' && step === 0)) &&\n        typeInfo.fields) {\n        const inputFields = Object.keys(typeInfo.fields).map(fieldName => typeInfo.fields[fieldName]);\n        return hintList(cur, token, inputFields.map(field => ({\n            text: `\"${field.name}\": `,\n            type: field.type,\n            description: field.description,\n        })));\n    }\n    if (kind === 'StringValue' ||\n        kind === 'NumberValue' ||\n        kind === 'BooleanValue' ||\n        kind === 'NullValue' ||\n        (kind === 'ListValue' && step === 1) ||\n        (kind === 'ObjectField' && step === 2) ||\n        (kind === 'Variable' && step === 2)) {\n        const namedInputType = typeInfo.type\n            ? getNamedType(typeInfo.type)\n            : undefined;\n        if (namedInputType instanceof GraphQLInputObjectType) {\n            return hintList(cur, token, [{ text: '{' }]);\n        }\n        if (namedInputType instanceof GraphQLEnumType) {\n            const values = namedInputType.getValues();\n            return hintList(cur, token, values.map(value => ({\n                text: `\"${value.name}\"`,\n                type: namedInputType,\n                description: value.description,\n            })));\n        }\n        if (namedInputType === GraphQLBoolean) {\n            return hintList(cur, token, [\n                { text: 'true', type: GraphQLBoolean, description: 'Not false.' },\n                { text: 'false', type: GraphQLBoolean, description: 'Not true.' },\n            ]);\n        }\n    }\n}\nfunction getTypeInfo(variableToType, tokenState) {\n    const info = {\n        type: null,\n        fields: null,\n    };\n    forEachState(tokenState, state => {\n        switch (state.kind) {\n            case 'Variable': {\n                info.type = variableToType[state.name];\n                break;\n            }\n            case 'ListValue': {\n                const nullableType = info.type ? getNullableType(info.type) : undefined;\n                info.type =\n                    nullableType instanceof GraphQLList ? nullableType.ofType : null;\n                break;\n            }\n            case 'ObjectValue': {\n                const objectType = info.type ? getNamedType(info.type) : undefined;\n                info.fields =\n                    objectType instanceof GraphQLInputObjectType\n                        ? objectType.getFields()\n                        : null;\n                break;\n            }\n            case 'ObjectField': {\n                const objectField = state.name && info.fields ? info.fields[state.name] : null;\n                info.type = objectField === null || objectField === void 0 ? void 0 : objectField.type;\n                break;\n            }\n        }\n    });\n    return info;\n}\n//# sourceMappingURL=hint.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAe,SAAS,SAAS,QAAQ,OAAO,MAAM;AAClD,QAAM,QAAQ,kBAAkB,MAAM,cAAc,MAAM,MAAM,CAAC;AACjE,MAAI,CAAC,OAAO;AACR;EACH;AACD,QAAM,aAAa,MAAM,SAAS,QAAQ,OAAO,KAAK,MAAM,OAAO,CAAC,CAAC,IAC/D,MAAM,QACN,MAAM;AACZ,SAAO;IACH,MAAM;IACN,MAAM,EAAE,MAAM,OAAO,MAAM,IAAI,WAAY;IAC3C,IAAI,EAAE,MAAM,OAAO,MAAM,IAAI,MAAM,IAAK;EAChD;AACA;AACA,SAAS,kBAAkB,MAAM,MAAM;AACnC,MAAI,CAAC,MAAM;AACP,WAAO,eAAe,MAAM,CAAA,UAAS,CAAC,MAAM,YAAY;EAC3D;AACD,QAAM,cAAc,KAAK,IAAI,CAAA,WAAU;IACnC,WAAW,aAAa,cAAc,MAAM,IAAI,GAAG,IAAI;IACvD;EACH,EAAC;AACF,QAAM,iBAAiB,eAAe,eAAe,aAAa,CAAA,SAAQ,KAAK,aAAa,CAAC,GAAG,CAAA,SAAQ,CAAC,KAAK,MAAM,YAAY;AAChI,QAAM,gBAAgB,eAAe,KAAK,CAAC,GAAG,OAAO,EAAE,MAAM,eAAe,IAAI,MAAM,EAAE,MAAM,eAAe,IAAI,MAC7G,EAAE,YAAY,EAAE,aAChB,EAAE,MAAM,KAAK,SAAS,EAAE,MAAM,KAAK,MAAM;AAC7C,SAAO,cAAc,IAAI,CAAA,SAAQ,KAAK,KAAK;AAC/C;AACA,SAAS,eAAe,OAAO,WAAW;AACtC,QAAM,WAAW,MAAM,OAAO,SAAS;AACvC,SAAO,SAAS,WAAW,IAAI,QAAQ;AAC3C;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,KAAK,YAAa,EAAC,WAAW,OAAO,EAAE;AAClD;AACA,SAAS,aAAa,YAAY,MAAM;AACpC,MAAI,YAAY,gBAAgB,MAAM,UAAU;AAChD,MAAI,WAAW,SAAS,KAAK,QAAQ;AACjC,iBAAa,WAAW,SAAS,KAAK,SAAS;AAC/C,iBAAa,WAAW,QAAQ,IAAI,MAAM,IAAI,IAAI;EACrD;AACD,SAAO;AACX;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,MAAI;AACJ,MAAI;AACJ,QAAM,IAAI,CAAA;AACV,QAAM,UAAU,EAAE;AAClB,QAAM,UAAU,EAAE;AAClB,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC3B,MAAE,CAAC,IAAI,CAAC,CAAC;EACZ;AACD,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC3B,MAAE,CAAC,EAAE,CAAC,IAAI;EACb;AACD,OAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC3B,SAAK,IAAI,GAAG,KAAK,SAAS,KAAK;AAC3B,YAAM,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;AACzC,QAAE,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;AAC3E,UAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AAClE,UAAE,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;MACrD;IACJ;EACJ;AACD,SAAO,EAAE,OAAO,EAAE,OAAO;AAC7B;AC7DA,WAAW,eAAe,QAAQ,qBAAqB,CAAC,QAAQ,YAAY;AACxE,QAAM,MAAM,OAAO,UAAA;AACnB,QAAM,QAAQ,OAAO,WAAW,GAAG;AACnC,QAAM,UAAU,iBAAiB,KAAK,OAAO,OAAO;AACpD,OAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,QAAQ,KAAK,SAAS,GAAG;AAC7F,YAAQ,OAAO,WAAW,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,EAAE;AAChE,YAAQ,KAAK,WAAW,IAAI,QAAQ,GAAG,MAAM,QAAQ,GAAG,EAAE;AAC1D,eAAW,OAAO,QAAQ,iBAAiB,QAAQ,SAAS,KAAK;EACpE;AACD,SAAO;AACX,CAAC;AACD,SAAS,iBAAiB,KAAK,OAAO,SAAS;AAC3C,QAAM,QAAQ,MAAM,MAAM,SAAS,YAAY,MAAM,MAAM,YAAY,MAAM;AAC7E,QAAM,EAAE,MAAM,KAAM,IAAG;AACvB,MAAI,SAAS,cAAc,SAAS,GAAG;AACnC,WAAO,SAAS,KAAK,OAAO,CAAC,EAAE,MAAM,IAAK,CAAA,CAAC;EAC9C;AACD,QAAM,EAAE,eAAgB,IAAG;AAC3B,MAAI,CAAC,gBAAgB;AACjB;EACH;AACD,QAAM,WAAW,YAAY,gBAAgB,MAAM,KAAK;AACxD,MAAI,SAAS,cAAe,SAAS,cAAc,SAAS,GAAI;AAC5D,UAAM,gBAAgB,OAAO,KAAK,cAAc;AAChD,WAAO,SAAS,KAAK,OAAO,cAAc,IAAI,CAAA,UAAS;MACnD,MAAM,IAAI,IAAI;MACd,MAAM,eAAe,IAAI;IAC5B,EAAC,CAAC;EACN;AACD,OAAK,SAAS,iBAAkB,SAAS,iBAAiB,SAAS,MAC/D,SAAS,QAAQ;AACjB,UAAM,cAAc,OAAO,KAAK,SAAS,MAAM,EAAE,IAAI,CAAA,cAAa,SAAS,OAAO,SAAS,CAAC;AAC5F,WAAO,SAAS,KAAK,OAAO,YAAY,IAAI,CAAA,WAAU;MAClD,MAAM,IAAI,MAAM,IAAI;MACpB,MAAM,MAAM;MACZ,aAAa,MAAM;IACtB,EAAC,CAAC;EACN;AACD,MAAI,SAAS,iBACT,SAAS,iBACT,SAAS,kBACT,SAAS,eACR,SAAS,eAAe,SAAS,KACjC,SAAS,iBAAiB,SAAS,KACnC,SAAS,cAAc,SAAS,GAAI;AACrC,UAAM,iBAAiB,SAAS,OAC1B,aAAa,SAAS,IAAI,IAC1B;AACN,QAAI,0BAA0B,wBAAwB;AAClD,aAAO,SAAS,KAAK,OAAO,CAAC,EAAE,MAAM,IAAK,CAAA,CAAC;IAC9C;AACD,QAAI,0BAA0B,iBAAiB;AAC3C,YAAM,SAAS,eAAe,UAAA;AAC9B,aAAO,SAAS,KAAK,OAAO,OAAO,IAAI,CAAA,WAAU;QAC7C,MAAM,IAAI,MAAM,IAAI;QACpB,MAAM;QACN,aAAa,MAAM;MACtB,EAAC,CAAC;IACN;AACD,QAAI,mBAAmB,gBAAgB;AACnC,aAAO,SAAS,KAAK,OAAO;QACxB,EAAE,MAAM,QAAQ,MAAM,gBAAgB,aAAa,aAAc;QACjE,EAAE,MAAM,SAAS,MAAM,gBAAgB,aAAa,YAAa;MACjF,CAAa;IACJ;EACJ;AACL;AACA,SAAS,YAAY,gBAAgB,YAAY;AAC7C,QAAM,OAAO;IACT,MAAM;IACN,QAAQ;EAChB;AACI,eAAa,YAAY,CAAA,UAAS;AAC9B,YAAQ,MAAM,MAAI;MACd,KAAK,YAAY;AACb,aAAK,OAAO,eAAe,MAAM,IAAI;AACrC;MACH;MACD,KAAK,aAAa;AACd,cAAM,eAAe,KAAK,OAAO,gBAAgB,KAAK,IAAI,IAAI;AAC9D,aAAK,OACD,wBAAwB,cAAc,aAAa,SAAS;AAChE;MACH;MACD,KAAK,eAAe;AAChB,cAAM,aAAa,KAAK,OAAO,aAAa,KAAK,IAAI,IAAI;AACzD,aAAK,SACD,sBAAsB,yBAChB,WAAW,UAAW,IACtB;AACV;MACH;MACD,KAAK,eAAe;AAChB,cAAM,cAAc,MAAM,QAAQ,KAAK,SAAS,KAAK,OAAO,MAAM,IAAI,IAAI;AAC1E,aAAK,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAClF;MACH;IACJ;EACT,CAAK;AACD,SAAO;AACX;", "names": []}
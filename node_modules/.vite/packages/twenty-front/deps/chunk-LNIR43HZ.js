import {
  createCommentVNode,
  createElementBlock,
  defineComponent,
  guardReactiveProps,
  i2 as i,
  normalizeProps,
  openBlock,
  renderSlot,
  unref
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/api-client/dist/components/ViewLayout/ViewLayoutSection.vue.js
var a = {
  key: 0,
  class: "bg-b-1 z-1 sticky top-0 flex min-h-11 items-center border-b px-2.5 text-sm font-medium xl:rounded-none"
};
var _ = defineComponent({
  __name: "ViewLayoutSection",
  setup(d) {
    const { cx: n } = i();
    return (e, f) => (openBlock(), createElementBlock("section", normalizeProps(guardReactiveProps(
      unref(n)("xl:custom-scroll bg-b-1 flex flex-1 flex-col xl:h-full xl:min-w-0")
    )), [
      e.$slots.title ? (openBlock(), createElementBlock("div", a, [
        renderSlot(e.$slots, "title")
      ])) : createCommentVNode("", true),
      renderSlot(e.$slots, "default")
    ], 16));
  }
});

export {
  _
};
//# sourceMappingURL=chunk-LNIR43HZ.js.map

{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/hint.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport 'codemirror/addon/hint/show-hint';\nimport { getAutocompleteSuggestions, Position } from 'graphql-language-service';\nCodeMirror.registerHelper('hint', 'graphql', (editor, options) => {\n    const { schema, externalFragments, autocompleteOptions } = options;\n    if (!schema) {\n        return;\n    }\n    const cur = editor.getCursor();\n    const token = editor.getTokenAt(cur);\n    const tokenStart = token.type !== null && /\"|\\w/.test(token.string[0])\n        ? token.start\n        : token.end;\n    const position = new Position(cur.line, tokenStart);\n    const rawResults = getAutocompleteSuggestions(schema, editor.getValue(), position, token, externalFragments, autocompleteOptions);\n    const results = {\n        list: rawResults.map(item => {\n            var _a;\n            return ({\n                text: (_a = item === null || item === void 0 ? void 0 : item.rawInsert) !== null && _a !== void 0 ? _a : item.label,\n                type: item.type,\n                description: item.documentation,\n                isDeprecated: item.isDeprecated,\n                deprecationReason: item.deprecationReason,\n            });\n        }),\n        from: { line: cur.line, ch: tokenStart },\n        to: { line: cur.line, ch: token.end },\n    };\n    if ((results === null || results === void 0 ? void 0 : results.list) && results.list.length > 0) {\n        results.from = CodeMirror.Pos(results.from.line, results.from.ch);\n        results.to = CodeMirror.Pos(results.to.line, results.to.ch);\n        CodeMirror.signal(editor, 'hasCompletion', editor, results, token);\n    }\n    return results;\n});\n//# sourceMappingURL=hint.js.map"], "mappings": ";;;;;;;;;;;;;AAGA,WAAW,eAAe,QAAQ,WAAW,CAAC,QAAQ,YAAY;AAC9D,QAAM,EAAE,QAAQ,mBAAmB,oBAAmB,IAAK;AAC3D,MAAI,CAAC,QAAQ;AACT;EACH;AACD,QAAM,MAAM,OAAO,UAAA;AACnB,QAAM,QAAQ,OAAO,WAAW,GAAG;AACnC,QAAM,aAAa,MAAM,SAAS,QAAQ,OAAO,KAAK,MAAM,OAAO,CAAC,CAAC,IAC/D,MAAM,QACN,MAAM;AACZ,QAAM,WAAW,IAAI,SAAS,IAAI,MAAM,UAAU;AAClD,QAAM,aAAa,2BAA2B,QAAQ,OAAO,SAAA,GAAY,UAAU,OAAO,mBAAmB,mBAAmB;AAChI,QAAM,UAAU;IACZ,MAAM,WAAW,IAAI,CAAA,SAAQ;AACzB,UAAI;AACJ,aAAQ;QACJ,OAAO,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,eAAe,QAAQ,OAAO,SAAS,KAAK,KAAK;QAC9G,MAAM,KAAK;QACX,aAAa,KAAK;QAClB,cAAc,KAAK;QACnB,mBAAmB,KAAK;MACxC;IACA,CAAS;IACD,MAAM,EAAE,MAAM,IAAI,MAAM,IAAI,WAAY;IACxC,IAAI,EAAE,MAAM,IAAI,MAAM,IAAI,MAAM,IAAK;EAC7C;AACI,OAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,QAAQ,KAAK,SAAS,GAAG;AAC7F,YAAQ,OAAO,WAAW,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,EAAE;AAChE,YAAQ,KAAK,WAAW,IAAI,QAAQ,GAAG,MAAM,QAAQ,GAAG,EAAE;AAC1D,eAAW,OAAO,QAAQ,iBAAiB,QAAQ,SAAS,KAAK;EACpE;AACD,SAAO;AACX,CAAC;", "names": []}
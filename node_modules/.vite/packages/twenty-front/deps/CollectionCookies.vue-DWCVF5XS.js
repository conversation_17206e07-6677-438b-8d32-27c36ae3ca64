import {
  _
} from "./chunk-LNIR43HZ.js";
import {
  createBlock,
  createTextVNode,
  defineComponent,
  openBlock,
  withCtx
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/CollectionCookies.vue2.js
var c = defineComponent({
  __name: "CollectionCookies",
  setup(l) {
    return (m, e) => (openBlock(), createBlock(_, null, {
      title: withCtx(() => e[0] || (e[0] = [
        createTextVNode("Cookies")
      ])),
      _: 1
    }));
  }
});
export {
  c as default
};
//# sourceMappingURL=CollectionCookies.vue-DWCVF5XS.js.map

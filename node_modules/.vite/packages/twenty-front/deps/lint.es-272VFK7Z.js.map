{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/lint/lint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var GUTTER_ID = \"CodeMirror-lint-markers\";\n  var LINT_LINE_ID = \"CodeMirror-lint-line-\";\n\n  function showTooltip(cm, e, content) {\n    var tt = document.createElement(\"div\");\n    tt.className = \"CodeMirror-lint-tooltip cm-s-\" + cm.options.theme;\n    tt.appendChild(content.cloneNode(true));\n    if (cm.state.lint.options.selfContain)\n      cm.getWrapperElement().appendChild(tt);\n    else\n      document.body.appendChild(tt);\n\n    function position(e) {\n      if (!tt.parentNode) return CodeMirror.off(document, \"mousemove\", position);\n      tt.style.top = Math.max(0, e.clientY - tt.offsetHeight - 5) + \"px\";\n      tt.style.left = (e.clientX + 5) + \"px\";\n    }\n    CodeMirror.on(document, \"mousemove\", position);\n    position(e);\n    if (tt.style.opacity != null) tt.style.opacity = 1;\n    return tt;\n  }\n  function rm(elt) {\n    if (elt.parentNode) elt.parentNode.removeChild(elt);\n  }\n  function hideTooltip(tt) {\n    if (!tt.parentNode) return;\n    if (tt.style.opacity == null) rm(tt);\n    tt.style.opacity = 0;\n    setTimeout(function() { rm(tt); }, 600);\n  }\n\n  function showTooltipFor(cm, e, content, node) {\n    var tooltip = showTooltip(cm, e, content);\n    function hide() {\n      CodeMirror.off(node, \"mouseout\", hide);\n      if (tooltip) { hideTooltip(tooltip); tooltip = null; }\n    }\n    var poll = setInterval(function() {\n      if (tooltip) for (var n = node;; n = n.parentNode) {\n        if (n && n.nodeType == 11) n = n.host;\n        if (n == document.body) return;\n        if (!n) { hide(); break; }\n      }\n      if (!tooltip) return clearInterval(poll);\n    }, 400);\n    CodeMirror.on(node, \"mouseout\", hide);\n  }\n\n  function LintState(cm, conf, hasGutter) {\n    this.marked = [];\n    if (conf instanceof Function) conf = {getAnnotations: conf};\n    if (!conf || conf === true) conf = {};\n    this.options = {};\n    this.linterOptions = conf.options || {};\n    for (var prop in defaults) this.options[prop] = defaults[prop];\n    for (var prop in conf) {\n      if (defaults.hasOwnProperty(prop)) {\n        if (conf[prop] != null) this.options[prop] = conf[prop];\n      } else if (!conf.options) {\n        this.linterOptions[prop] = conf[prop];\n      }\n    }\n    this.timeout = null;\n    this.hasGutter = hasGutter;\n    this.onMouseOver = function(e) { onMouseOver(cm, e); };\n    this.waitingFor = 0\n  }\n\n  var defaults = {\n    highlightLines: false,\n    tooltips: true,\n    delay: 500,\n    lintOnChange: true,\n    getAnnotations: null,\n    async: false,\n    selfContain: null,\n    formatAnnotation: null,\n    onUpdateLinting: null\n  }\n\n  function clearMarks(cm) {\n    var state = cm.state.lint;\n    if (state.hasGutter) cm.clearGutter(GUTTER_ID);\n    if (state.options.highlightLines) clearErrorLines(cm);\n    for (var i = 0; i < state.marked.length; ++i)\n      state.marked[i].clear();\n    state.marked.length = 0;\n  }\n\n  function clearErrorLines(cm) {\n    cm.eachLine(function(line) {\n      var has = line.wrapClass && /\\bCodeMirror-lint-line-\\w+\\b/.exec(line.wrapClass);\n      if (has) cm.removeLineClass(line, \"wrap\", has[0]);\n    })\n  }\n\n  function makeMarker(cm, labels, severity, multiple, tooltips) {\n    var marker = document.createElement(\"div\"), inner = marker;\n    marker.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-\" + severity;\n    if (multiple) {\n      inner = marker.appendChild(document.createElement(\"div\"));\n      inner.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-multiple\";\n    }\n\n    if (tooltips != false) CodeMirror.on(inner, \"mouseover\", function(e) {\n      showTooltipFor(cm, e, labels, inner);\n    });\n\n    return marker;\n  }\n\n  function getMaxSeverity(a, b) {\n    if (a == \"error\") return a;\n    else return b;\n  }\n\n  function groupByLine(annotations) {\n    var lines = [];\n    for (var i = 0; i < annotations.length; ++i) {\n      var ann = annotations[i], line = ann.from.line;\n      (lines[line] || (lines[line] = [])).push(ann);\n    }\n    return lines;\n  }\n\n  function annotationTooltip(ann) {\n    var severity = ann.severity;\n    if (!severity) severity = \"error\";\n    var tip = document.createElement(\"div\");\n    tip.className = \"CodeMirror-lint-message CodeMirror-lint-message-\" + severity;\n    if (typeof ann.messageHTML != 'undefined') {\n      tip.innerHTML = ann.messageHTML;\n    } else {\n      tip.appendChild(document.createTextNode(ann.message));\n    }\n    return tip;\n  }\n\n  function lintAsync(cm, getAnnotations) {\n    var state = cm.state.lint\n    var id = ++state.waitingFor\n    function abort() {\n      id = -1\n      cm.off(\"change\", abort)\n    }\n    cm.on(\"change\", abort)\n    getAnnotations(cm.getValue(), function(annotations, arg2) {\n      cm.off(\"change\", abort)\n      if (state.waitingFor != id) return\n      if (arg2 && annotations instanceof CodeMirror) annotations = arg2\n      cm.operation(function() {updateLinting(cm, annotations)})\n    }, state.linterOptions, cm);\n  }\n\n  function startLinting(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    /*\n     * Passing rules in `options` property prevents JSHint (and other linters) from complaining\n     * about unrecognized rules like `onUpdateLinting`, `delay`, `lintOnChange`, etc.\n     */\n    var getAnnotations = options.getAnnotations || cm.getHelper(CodeMirror.Pos(0, 0), \"lint\");\n    if (!getAnnotations) return;\n    if (options.async || getAnnotations.async) {\n      lintAsync(cm, getAnnotations)\n    } else {\n      var annotations = getAnnotations(cm.getValue(), state.linterOptions, cm);\n      if (!annotations) return;\n      if (annotations.then) annotations.then(function(issues) {\n        cm.operation(function() {updateLinting(cm, issues)})\n      });\n      else cm.operation(function() {updateLinting(cm, annotations)})\n    }\n  }\n\n  function updateLinting(cm, annotationsNotSorted) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    clearMarks(cm);\n\n    var annotations = groupByLine(annotationsNotSorted);\n\n    for (var line = 0; line < annotations.length; ++line) {\n      var anns = annotations[line];\n      if (!anns) continue;\n\n      // filter out duplicate messages\n      var message = [];\n      anns = anns.filter(function(item) { return message.indexOf(item.message) > -1 ? false : message.push(item.message) });\n\n      var maxSeverity = null;\n      var tipLabel = state.hasGutter && document.createDocumentFragment();\n\n      for (var i = 0; i < anns.length; ++i) {\n        var ann = anns[i];\n        var severity = ann.severity;\n        if (!severity) severity = \"error\";\n        maxSeverity = getMaxSeverity(maxSeverity, severity);\n\n        if (options.formatAnnotation) ann = options.formatAnnotation(ann);\n        if (state.hasGutter) tipLabel.appendChild(annotationTooltip(ann));\n\n        if (ann.to) state.marked.push(cm.markText(ann.from, ann.to, {\n          className: \"CodeMirror-lint-mark CodeMirror-lint-mark-\" + severity,\n          __annotation: ann\n        }));\n      }\n      // use original annotations[line] to show multiple messages\n      if (state.hasGutter)\n        cm.setGutterMarker(line, GUTTER_ID, makeMarker(cm, tipLabel, maxSeverity, annotations[line].length > 1,\n                                                       options.tooltips));\n\n      if (options.highlightLines)\n        cm.addLineClass(line, \"wrap\", LINT_LINE_ID + maxSeverity);\n    }\n    if (options.onUpdateLinting) options.onUpdateLinting(annotationsNotSorted, annotations, cm);\n  }\n\n  function onChange(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(function(){startLinting(cm);}, state.options.delay);\n  }\n\n  function popupTooltips(cm, annotations, e) {\n    var target = e.target || e.srcElement;\n    var tooltip = document.createDocumentFragment();\n    for (var i = 0; i < annotations.length; i++) {\n      var ann = annotations[i];\n      tooltip.appendChild(annotationTooltip(ann));\n    }\n    showTooltipFor(cm, e, tooltip, target);\n  }\n\n  function onMouseOver(cm, e) {\n    var target = e.target || e.srcElement;\n    if (!/\\bCodeMirror-lint-mark-/.test(target.className)) return;\n    var box = target.getBoundingClientRect(), x = (box.left + box.right) / 2, y = (box.top + box.bottom) / 2;\n    var spans = cm.findMarksAt(cm.coordsChar({left: x, top: y}, \"client\"));\n\n    var annotations = [];\n    for (var i = 0; i < spans.length; ++i) {\n      var ann = spans[i].__annotation;\n      if (ann) annotations.push(ann);\n    }\n    if (annotations.length) popupTooltips(cm, annotations, e);\n  }\n\n  CodeMirror.defineOption(\"lint\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      clearMarks(cm);\n      if (cm.state.lint.options.lintOnChange !== false)\n        cm.off(\"change\", onChange);\n      CodeMirror.off(cm.getWrapperElement(), \"mouseover\", cm.state.lint.onMouseOver);\n      clearTimeout(cm.state.lint.timeout);\n      delete cm.state.lint;\n    }\n\n    if (val) {\n      var gutters = cm.getOption(\"gutters\"), hasLintGutter = false;\n      for (var i = 0; i < gutters.length; ++i) if (gutters[i] == GUTTER_ID) hasLintGutter = true;\n      var state = cm.state.lint = new LintState(cm, val, hasLintGutter);\n      if (state.options.lintOnChange)\n        cm.on(\"change\", onChange);\n      if (state.options.tooltips != false && state.options.tooltips != \"gutter\")\n        CodeMirror.on(cm.getWrapperElement(), \"mouseover\", state.onMouseOver);\n\n      startLinting(cm);\n    }\n  });\n\n  CodeMirror.defineExtension(\"performLint\", function() {\n    startLinting(this);\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA+B,CAAA;EAKtC,GAAE,SAAS,YAAY;AAEtB,QAAI,YAAY;AAChB,QAAI,eAAe;AAEnB,aAAS,YAAY,IAAI,GAAG,SAAS;AACnC,UAAI,KAAK,SAAS,cAAc,KAAK;AACrC,SAAG,YAAY,kCAAkC,GAAG,QAAQ;AAC5D,SAAG,YAAY,QAAQ,UAAU,IAAI,CAAC;AACtC,UAAI,GAAG,MAAM,KAAK,QAAQ;AACxB,WAAG,kBAAiB,EAAG,YAAY,EAAE;;AAErC,iBAAS,KAAK,YAAY,EAAE;AAE9B,eAAS,SAASC,IAAG;AACnB,YAAI,CAAC,GAAG,WAAY,QAAO,WAAW,IAAI,UAAU,aAAa,QAAQ;AACzE,WAAG,MAAM,MAAM,KAAK,IAAI,GAAGA,GAAE,UAAU,GAAG,eAAe,CAAC,IAAI;AAC9D,WAAG,MAAM,OAAQA,GAAE,UAAU,IAAK;MACnC;AACD,iBAAW,GAAG,UAAU,aAAa,QAAQ;AAC7C,eAAS,CAAC;AACV,UAAI,GAAG,MAAM,WAAW,KAAM,IAAG,MAAM,UAAU;AACjD,aAAO;IACR;AACD,aAAS,GAAG,KAAK;AACf,UAAI,IAAI,WAAY,KAAI,WAAW,YAAY,GAAG;IACnD;AACD,aAAS,YAAY,IAAI;AACvB,UAAI,CAAC,GAAG,WAAY;AACpB,UAAI,GAAG,MAAM,WAAW,KAAM,IAAG,EAAE;AACnC,SAAG,MAAM,UAAU;AACnB,iBAAW,WAAW;AAAE,WAAG,EAAE;MAAE,GAAI,GAAG;IACvC;AAED,aAAS,eAAe,IAAI,GAAG,SAAS,MAAM;AAC5C,UAAI,UAAU,YAAY,IAAI,GAAG,OAAO;AACxC,eAAS,OAAO;AACd,mBAAW,IAAI,MAAM,YAAY,IAAI;AACrC,YAAI,SAAS;AAAE,sBAAY,OAAO;AAAG,oBAAU;QAAO;MACvD;AACD,UAAI,OAAO,YAAY,WAAW;AAChC,YAAI,QAAS,UAAS,IAAI,QAAO,IAAI,EAAE,YAAY;AACjD,cAAI,KAAK,EAAE,YAAY,GAAI,KAAI,EAAE;AACjC,cAAI,KAAK,SAAS,KAAM;AACxB,cAAI,CAAC,GAAG;AAAE,iBAAM;AAAE;UAAQ;QAC3B;AACD,YAAI,CAAC,QAAS,QAAO,cAAc,IAAI;MACxC,GAAE,GAAG;AACN,iBAAW,GAAG,MAAM,YAAY,IAAI;IACrC;AAED,aAAS,UAAU,IAAI,MAAM,WAAW;AACtC,WAAK,SAAS,CAAA;AACd,UAAI,gBAAgB,SAAU,QAAO,EAAC,gBAAgB,KAAI;AAC1D,UAAI,CAAC,QAAQ,SAAS,KAAM,QAAO,CAAA;AACnC,WAAK,UAAU,CAAA;AACf,WAAK,gBAAgB,KAAK,WAAW,CAAA;AACrC,eAAS,QAAQ,SAAU,MAAK,QAAQ,IAAI,IAAI,SAAS,IAAI;AAC7D,eAAS,QAAQ,MAAM;AACrB,YAAI,SAAS,eAAe,IAAI,GAAG;AACjC,cAAI,KAAK,IAAI,KAAK,KAAM,MAAK,QAAQ,IAAI,IAAI,KAAK,IAAI;QAC9D,WAAiB,CAAC,KAAK,SAAS;AACxB,eAAK,cAAc,IAAI,IAAI,KAAK,IAAI;QACrC;MACF;AACD,WAAK,UAAU;AACf,WAAK,YAAY;AACjB,WAAK,cAAc,SAAS,GAAG;AAAE,oBAAY,IAAI,CAAC;MAAA;AAClD,WAAK,aAAa;IACnB;AAED,QAAI,WAAW;MACb,gBAAgB;MAChB,UAAU;MACV,OAAO;MACP,cAAc;MACd,gBAAgB;MAChB,OAAO;MACP,aAAa;MACb,kBAAkB;MAClB,iBAAiB;IAClB;AAED,aAAS,WAAW,IAAI;AACtB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,MAAM,UAAW,IAAG,YAAY,SAAS;AAC7C,UAAI,MAAM,QAAQ,eAAgB,iBAAgB,EAAE;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM,OAAO,QAAQ,EAAE;AACzC,cAAM,OAAO,CAAC,EAAE,MAAK;AACvB,YAAM,OAAO,SAAS;IACvB;AAED,aAAS,gBAAgB,IAAI;AAC3B,SAAG,SAAS,SAAS,MAAM;AACzB,YAAI,MAAM,KAAK,aAAa,+BAA+B,KAAK,KAAK,SAAS;AAC9E,YAAI,IAAK,IAAG,gBAAgB,MAAM,QAAQ,IAAI,CAAC,CAAC;MACtD,CAAK;IACF;AAED,aAAS,WAAW,IAAI,QAAQ,UAAU,UAAU,UAAU;AAC5D,UAAI,SAAS,SAAS,cAAc,KAAK,GAAG,QAAQ;AACpD,aAAO,YAAY,mDAAmD;AACtE,UAAI,UAAU;AACZ,gBAAQ,OAAO,YAAY,SAAS,cAAc,KAAK,CAAC;AACxD,cAAM,YAAY;MACnB;AAED,UAAI,YAAY,MAAO,YAAW,GAAG,OAAO,aAAa,SAAS,GAAG;AACnE,uBAAe,IAAI,GAAG,QAAQ,KAAK;MACzC,CAAK;AAED,aAAO;IACR;AAED,aAAS,eAAe,GAAG,GAAG;AAC5B,UAAI,KAAK,QAAS,QAAO;UACpB,QAAO;IACb;AAED,aAAS,YAAY,aAAa;AAChC,UAAI,QAAQ,CAAA;AACZ,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,EAAE,GAAG;AAC3C,YAAI,MAAM,YAAY,CAAC,GAAG,OAAO,IAAI,KAAK;AAC1C,SAAC,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,CAAA,IAAK,KAAK,GAAG;MAC7C;AACD,aAAO;IACR;AAED,aAAS,kBAAkB,KAAK;AAC9B,UAAI,WAAW,IAAI;AACnB,UAAI,CAAC,SAAU,YAAW;AAC1B,UAAI,MAAM,SAAS,cAAc,KAAK;AACtC,UAAI,YAAY,qDAAqD;AACrE,UAAI,OAAO,IAAI,eAAe,aAAa;AACzC,YAAI,YAAY,IAAI;MAC1B,OAAW;AACL,YAAI,YAAY,SAAS,eAAe,IAAI,OAAO,CAAC;MACrD;AACD,aAAO;IACR;AAED,aAAS,UAAU,IAAI,gBAAgB;AACrC,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,KAAK,EAAE,MAAM;AACjB,eAAS,QAAQ;AACf,aAAK;AACL,WAAG,IAAI,UAAU,KAAK;MACvB;AACD,SAAG,GAAG,UAAU,KAAK;AACrB,qBAAe,GAAG,SAAU,GAAE,SAAS,aAAa,MAAM;AACxD,WAAG,IAAI,UAAU,KAAK;AACtB,YAAI,MAAM,cAAc,GAAI;AAC5B,YAAI,QAAQ,uBAAuB,WAAY,eAAc;AAC7D,WAAG,UAAU,WAAW;AAAC,wBAAc,IAAI,WAAW;QAAC,CAAC;MAC9D,GAAO,MAAM,eAAe,EAAE;IAC3B;AAED,aAAS,aAAa,IAAI;AACxB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,UAAU,MAAM;AAKpB,UAAI,iBAAiB,QAAQ,kBAAkB,GAAG,UAAU,WAAW,IAAI,GAAG,CAAC,GAAG,MAAM;AACxF,UAAI,CAAC,eAAgB;AACrB,UAAI,QAAQ,SAAS,eAAe,OAAO;AACzC,kBAAU,IAAI,cAAc;MAClC,OAAW;AACL,YAAI,cAAc,eAAe,GAAG,SAAU,GAAE,MAAM,eAAe,EAAE;AACvE,YAAI,CAAC,YAAa;AAClB,YAAI,YAAY,KAAM,aAAY,KAAK,SAAS,QAAQ;AACtD,aAAG,UAAU,WAAW;AAAC,0BAAc,IAAI,MAAM;UAAC,CAAC;QAC3D,CAAO;YACI,IAAG,UAAU,WAAW;AAAC,wBAAc,IAAI,WAAW;QAAC,CAAC;MAC9D;IACF;AAED,aAAS,cAAc,IAAI,sBAAsB;AAC/C,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,UAAU,MAAM;AACpB,iBAAW,EAAE;AAEb,UAAI,cAAc,YAAY,oBAAoB;AAElD,eAAS,OAAO,GAAG,OAAO,YAAY,QAAQ,EAAE,MAAM;AACpD,YAAI,OAAO,YAAY,IAAI;AAC3B,YAAI,CAAC,KAAM;AAGX,YAAI,UAAU,CAAA;AACd,eAAO,KAAK,OAAO,SAAS,MAAM;AAAE,iBAAO,QAAQ,QAAQ,KAAK,OAAO,IAAI,KAAK,QAAQ,QAAQ,KAAK,KAAK,OAAO;QAAC,CAAE;AAEpH,YAAI,cAAc;AAClB,YAAI,WAAW,MAAM,aAAa,SAAS,uBAAsB;AAEjE,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,WAAW,IAAI;AACnB,cAAI,CAAC,SAAU,YAAW;AAC1B,wBAAc,eAAe,aAAa,QAAQ;AAElD,cAAI,QAAQ,iBAAkB,OAAM,QAAQ,iBAAiB,GAAG;AAChE,cAAI,MAAM,UAAW,UAAS,YAAY,kBAAkB,GAAG,CAAC;AAEhE,cAAI,IAAI,GAAI,OAAM,OAAO,KAAK,GAAG,SAAS,IAAI,MAAM,IAAI,IAAI;YAC1D,WAAW,+CAA+C;YAC1D,cAAc;UACf,CAAA,CAAC;QACH;AAED,YAAI,MAAM;AACR,aAAG,gBAAgB,MAAM,WAAW;YAAW;YAAI;YAAU;YAAa,YAAY,IAAI,EAAE,SAAS;YACtD,QAAQ;UAAQ,CAAC;AAElE,YAAI,QAAQ;AACV,aAAG,aAAa,MAAM,QAAQ,eAAe,WAAW;MAC3D;AACD,UAAI,QAAQ,gBAAiB,SAAQ,gBAAgB,sBAAsB,aAAa,EAAE;IAC3F;AAED,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,mBAAa,MAAM,OAAO;AAC1B,YAAM,UAAU,WAAW,WAAU;AAAC,qBAAa,EAAE;MAAE,GAAG,MAAM,QAAQ,KAAK;IAC9E;AAED,aAAS,cAAc,IAAI,aAAa,GAAG;AACzC,UAAI,SAAS,EAAE,UAAU,EAAE;AAC3B,UAAI,UAAU,SAAS,uBAAA;AACvB,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,MAAM,YAAY,CAAC;AACvB,gBAAQ,YAAY,kBAAkB,GAAG,CAAC;MAC3C;AACD,qBAAe,IAAI,GAAG,SAAS,MAAM;IACtC;AAED,aAAS,YAAY,IAAI,GAAG;AAC1B,UAAI,SAAS,EAAE,UAAU,EAAE;AAC3B,UAAI,CAAC,0BAA0B,KAAK,OAAO,SAAS,EAAG;AACvD,UAAI,MAAM,OAAO,sBAAqB,GAAI,KAAK,IAAI,OAAO,IAAI,SAAS,GAAG,KAAK,IAAI,MAAM,IAAI,UAAU;AACvG,UAAI,QAAQ,GAAG,YAAY,GAAG,WAAW,EAAC,MAAM,GAAG,KAAK,EAAC,GAAG,QAAQ,CAAC;AAErE,UAAI,cAAc,CAAA;AAClB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,MAAM,CAAC,EAAE;AACnB,YAAI,IAAK,aAAY,KAAK,GAAG;MAC9B;AACD,UAAI,YAAY,OAAQ,eAAc,IAAI,aAAa,CAAC;IACzD;AAED,eAAW,aAAa,QAAQ,OAAO,SAAS,IAAI,KAAK,KAAK;AAC5D,UAAI,OAAO,OAAO,WAAW,MAAM;AACjC,mBAAW,EAAE;AACb,YAAI,GAAG,MAAM,KAAK,QAAQ,iBAAiB;AACzC,aAAG,IAAI,UAAU,QAAQ;AAC3B,mBAAW,IAAI,GAAG,kBAAmB,GAAE,aAAa,GAAG,MAAM,KAAK,WAAW;AAC7E,qBAAa,GAAG,MAAM,KAAK,OAAO;AAClC,eAAO,GAAG,MAAM;MACjB;AAED,UAAI,KAAK;AACP,YAAI,UAAU,GAAG,UAAU,SAAS,GAAG,gBAAgB;AACvD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,EAAG,KAAI,QAAQ,CAAC,KAAK,UAAW,iBAAgB;AACtF,YAAI,QAAQ,GAAG,MAAM,OAAO,IAAI,UAAU,IAAI,KAAK,aAAa;AAChE,YAAI,MAAM,QAAQ;AAChB,aAAG,GAAG,UAAU,QAAQ;AAC1B,YAAI,MAAM,QAAQ,YAAY,SAAS,MAAM,QAAQ,YAAY;AAC/D,qBAAW,GAAG,GAAG,kBAAmB,GAAE,aAAa,MAAM,WAAW;AAEtE,qBAAa,EAAE;MAChB;IACL,CAAG;AAED,eAAW,gBAAgB,eAAe,WAAW;AACnD,mBAAa,IAAI;IACrB,CAAG;EACH,CAAC;;;;;;;;", "names": ["require$$0", "e"]}
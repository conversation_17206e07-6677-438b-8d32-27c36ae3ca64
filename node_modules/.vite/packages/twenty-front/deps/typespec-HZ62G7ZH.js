import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js
var bounded, notBefore, identifierStart, identifierContinue, identifier, directive, keywords, namedLiterals, nonCommentWs, numericLiteral, conf, language;
var init_typespec = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js"() {
    bounded = (text) => `\\b${text}\\b`;
    notBefore = (regex) => `(?!${regex})`;
    identifierStart = "[_a-zA-Z]";
    identifierContinue = "[_a-zA-Z0-9]";
    identifier = bounded(`${identifierStart}${identifierContinue}*`);
    directive = bounded(`[_a-zA-Z-0-9]+`);
    keywords = [
      "import",
      "model",
      "scalar",
      "namespace",
      "op",
      "interface",
      "union",
      "using",
      "is",
      "extends",
      "enum",
      "alias",
      "return",
      "void",
      "if",
      "else",
      "projection",
      "dec",
      "extern",
      "fn"
    ];
    namedLiterals = ["true", "false", "null", "unknown", "never"];
    nonCommentWs = `[ \\t\\r\\n]`;
    numericLiteral = `[0-9]+`;
    conf = {
      comments: {
        lineComment: "//",
        blockComment: ["/*", "*/"]
      },
      brackets: [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
      ],
      autoClosingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: '"', close: '"' },
        { open: "/**", close: " */", notIn: ["string"] }
      ],
      surroundingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: '"', close: '"' }
      ],
      indentationRules: {
        decreaseIndentPattern: new RegExp("^((?!.*?/\\*).*\\*/)?\\s*[\\}\\]].*$"),
        increaseIndentPattern: new RegExp(
          "^((?!//).)*(\\{([^}\"'`/]*|(\\t|[ ])*//.*)|\\([^)\"'`/]*|\\[[^\\]\"'`/]*)$"
        ),
        // e.g.  * ...| or */| or *-----*/|
        unIndentedLinePattern: new RegExp(
          "^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$|^(\\t|[ ])*[ ]\\*/\\s*$|^(\\t|[ ])*[ ]\\*([ ]([^\\*]|\\*(?!/))*)?$"
        )
      }
    };
    language = {
      defaultToken: "",
      tokenPostfix: ".tsp",
      brackets: [
        { open: "{", close: "}", token: "delimiter.curly" },
        { open: "[", close: "]", token: "delimiter.square" },
        { open: "(", close: ")", token: "delimiter.parenthesis" }
      ],
      symbols: /[=:;<>]+/,
      keywords,
      namedLiterals,
      escapes: `\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\|"|\\\${)`,
      tokenizer: {
        root: [{ include: "@expression" }, { include: "@whitespace" }],
        stringVerbatim: [
          { regex: `(|"|"")[^"]`, action: { token: "string" } },
          { regex: `"""${notBefore(`"`)}`, action: { token: "string", next: "@pop" } }
        ],
        stringLiteral: [
          { regex: `\\\${`, action: { token: "delimiter.bracket", next: "@bracketCounting" } },
          { regex: `[^\\\\"$]+`, action: { token: "string" } },
          { regex: "@escapes", action: { token: "string.escape" } },
          { regex: `\\\\.`, action: { token: "string.escape.invalid" } },
          { regex: `"`, action: { token: "string", next: "@pop" } }
        ],
        bracketCounting: [
          { regex: `{`, action: { token: "delimiter.bracket", next: "@bracketCounting" } },
          { regex: `}`, action: { token: "delimiter.bracket", next: "@pop" } },
          { include: "@expression" }
        ],
        comment: [
          { regex: `[^\\*]+`, action: { token: "comment" } },
          { regex: `\\*\\/`, action: { token: "comment", next: "@pop" } },
          { regex: `[\\/*]`, action: { token: "comment" } }
        ],
        whitespace: [
          { regex: nonCommentWs },
          { regex: `\\/\\*`, action: { token: "comment", next: "@comment" } },
          { regex: `\\/\\/.*$`, action: { token: "comment" } }
        ],
        expression: [
          { regex: `"""`, action: { token: "string", next: "@stringVerbatim" } },
          { regex: `"${notBefore(`""`)}`, action: { token: "string", next: "@stringLiteral" } },
          { regex: numericLiteral, action: { token: "number" } },
          {
            regex: identifier,
            action: {
              cases: {
                "@keywords": { token: "keyword" },
                "@namedLiterals": { token: "keyword" },
                "@default": { token: "identifier" }
              }
            }
          },
          { regex: `@${identifier}`, action: { token: "tag" } },
          { regex: `#${directive}`, action: { token: "directive" } }
        ]
      }
    };
  }
});
init_typespec();
export {
  conf,
  language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/typespec/typespec.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=typespec-HZ62G7ZH.js.map

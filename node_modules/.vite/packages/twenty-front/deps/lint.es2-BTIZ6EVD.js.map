{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/lint.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport { getDiagnostics } from 'graphql-language-service';\nconst SEVERITY = ['error', 'warning', 'information', 'hint'];\nconst TYPE = {\n    'GraphQL: Validation': 'validation',\n    'GraphQL: Deprecation': 'deprecation',\n    'GraphQL: Syntax': 'syntax',\n};\nCodeMirror.registerHelper('lint', 'graphql', (text, options) => {\n    const { schema, validationRules, externalFragments } = options;\n    const rawResults = getDiagnostics(text, schema, validationRules, undefined, externalFragments);\n    const results = rawResults.map(error => ({\n        message: error.message,\n        severity: error.severity ? SEVERITY[error.severity - 1] : SEVERITY[0],\n        type: error.source ? TYPE[error.source] : undefined,\n        from: CodeMirror.Pos(error.range.start.line, error.range.start.character),\n        to: CodeMirror.Pos(error.range.end.line, error.range.end.character),\n    }));\n    return results;\n});\n//# sourceMappingURL=lint.js.map"], "mappings": ";;;;;;;;;;;AAEA,IAAM,WAAW,CAAC,SAAS,WAAW,eAAe,MAAM;AAC3D,IAAM,OAAO;EACT,uBAAuB;EACvB,wBAAwB;EACxB,mBAAmB;AACvB;AACA,WAAW,eAAe,QAAQ,WAAW,CAAC,MAAM,YAAY;AAC5D,QAAM,EAAE,QAAQ,iBAAiB,kBAAiB,IAAK;AACvD,QAAM,aAAa,eAAe,MAAM,QAAQ,iBAAiB,QAAW,iBAAiB;AAC7F,QAAM,UAAU,WAAW,IAAI,CAAA,WAAU;IACrC,SAAS,MAAM;IACf,UAAU,MAAM,WAAW,SAAS,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC;IACpE,MAAM,MAAM,SAAS,KAAK,MAAM,MAAM,IAAI;IAC1C,MAAM,WAAW,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,SAAS;IACxE,IAAI,WAAW,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,SAAS;EACrE,EAAC;AACF,SAAO;AACX,CAAC;", "names": []}
{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/comment/comment.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var noOptions = {};\n  var nonWS = /[^\\s\\u00a0]/;\n  var Pos = CodeMirror.Pos, cmp = CodeMirror.cmpPos;\n\n  function firstNonWS(str) {\n    var found = str.search(nonWS);\n    return found == -1 ? 0 : found;\n  }\n\n  CodeMirror.commands.toggleComment = function(cm) {\n    cm.toggleComment();\n  };\n\n  CodeMirror.defineExtension(\"toggleComment\", function(options) {\n    if (!options) options = noOptions;\n    var cm = this;\n    var minLine = Infinity, ranges = this.listSelections(), mode = null;\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var from = ranges[i].from(), to = ranges[i].to();\n      if (from.line >= minLine) continue;\n      if (to.line >= minLine) to = Pos(minLine, 0);\n      minLine = from.line;\n      if (mode == null) {\n        if (cm.uncomment(from, to, options)) mode = \"un\";\n        else { cm.lineComment(from, to, options); mode = \"line\"; }\n      } else if (mode == \"un\") {\n        cm.uncomment(from, to, options);\n      } else {\n        cm.lineComment(from, to, options);\n      }\n    }\n  });\n\n  // Rough heuristic to try and detect lines that are part of multi-line string\n  function probablyInsideString(cm, pos, line) {\n    return /\\bstring\\b/.test(cm.getTokenTypeAt(Pos(pos.line, 0))) && !/^[\\'\\\"\\`]/.test(line)\n  }\n\n  function getMode(cm, pos) {\n    var mode = cm.getMode()\n    return mode.useInnerComments === false || !mode.innerMode ? mode : cm.getModeAt(pos)\n  }\n\n  CodeMirror.defineExtension(\"lineComment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var firstLine = self.getLine(from.line);\n    if (firstLine == null || probablyInsideString(self, from, firstLine)) return;\n\n    var commentString = options.lineComment || mode.lineComment;\n    if (!commentString) {\n      if (options.blockCommentStart || mode.blockCommentStart) {\n        options.fullLines = true;\n        self.blockComment(from, to, options);\n      }\n      return;\n    }\n\n    var end = Math.min(to.ch != 0 || to.line == from.line ? to.line + 1 : to.line, self.lastLine() + 1);\n    var pad = options.padding == null ? \" \" : options.padding;\n    var blankLines = options.commentBlankLines || from.line == to.line;\n\n    self.operation(function() {\n      if (options.indent) {\n        var baseString = null;\n        for (var i = from.line; i < end; ++i) {\n          var line = self.getLine(i);\n          var whitespace = line.slice(0, firstNonWS(line));\n          if (baseString == null || baseString.length > whitespace.length) {\n            baseString = whitespace;\n          }\n        }\n        for (var i = from.line; i < end; ++i) {\n          var line = self.getLine(i), cut = baseString.length;\n          if (!blankLines && !nonWS.test(line)) continue;\n          if (line.slice(0, cut) != baseString) cut = firstNonWS(line);\n          self.replaceRange(baseString + commentString + pad, Pos(i, 0), Pos(i, cut));\n        }\n      } else {\n        for (var i = from.line; i < end; ++i) {\n          if (blankLines || nonWS.test(self.getLine(i)))\n            self.replaceRange(commentString + pad, Pos(i, 0));\n        }\n      }\n    });\n  });\n\n  CodeMirror.defineExtension(\"blockComment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var startString = options.blockCommentStart || mode.blockCommentStart;\n    var endString = options.blockCommentEnd || mode.blockCommentEnd;\n    if (!startString || !endString) {\n      if ((options.lineComment || mode.lineComment) && options.fullLines != false)\n        self.lineComment(from, to, options);\n      return;\n    }\n    if (/\\bcomment\\b/.test(self.getTokenTypeAt(Pos(from.line, 0)))) return\n\n    var end = Math.min(to.line, self.lastLine());\n    if (end != from.line && to.ch == 0 && nonWS.test(self.getLine(end))) --end;\n\n    var pad = options.padding == null ? \" \" : options.padding;\n    if (from.line > end) return;\n\n    self.operation(function() {\n      if (options.fullLines != false) {\n        var lastLineHasText = nonWS.test(self.getLine(end));\n        self.replaceRange(pad + endString, Pos(end));\n        self.replaceRange(startString + pad, Pos(from.line, 0));\n        var lead = options.blockCommentLead || mode.blockCommentLead;\n        if (lead != null) for (var i = from.line + 1; i <= end; ++i)\n          if (i != end || lastLineHasText)\n            self.replaceRange(lead + pad, Pos(i, 0));\n      } else {\n        var atCursor = cmp(self.getCursor(\"to\"), to) == 0, empty = !self.somethingSelected()\n        self.replaceRange(endString, to);\n        if (atCursor) self.setSelection(empty ? to : self.getCursor(\"from\"), to)\n        self.replaceRange(startString, from);\n      }\n    });\n  });\n\n  CodeMirror.defineExtension(\"uncomment\", function(from, to, options) {\n    if (!options) options = noOptions;\n    var self = this, mode = getMode(self, from);\n    var end = Math.min(to.ch != 0 || to.line == from.line ? to.line : to.line - 1, self.lastLine()), start = Math.min(from.line, end);\n\n    // Try finding line comments\n    var lineString = options.lineComment || mode.lineComment, lines = [];\n    var pad = options.padding == null ? \" \" : options.padding, didSomething;\n    lineComment: {\n      if (!lineString) break lineComment;\n      for (var i = start; i <= end; ++i) {\n        var line = self.getLine(i);\n        var found = line.indexOf(lineString);\n        if (found > -1 && !/comment/.test(self.getTokenTypeAt(Pos(i, found + 1)))) found = -1;\n        if (found == -1 && nonWS.test(line)) break lineComment;\n        if (found > -1 && nonWS.test(line.slice(0, found))) break lineComment;\n        lines.push(line);\n      }\n      self.operation(function() {\n        for (var i = start; i <= end; ++i) {\n          var line = lines[i - start];\n          var pos = line.indexOf(lineString), endPos = pos + lineString.length;\n          if (pos < 0) continue;\n          if (line.slice(endPos, endPos + pad.length) == pad) endPos += pad.length;\n          didSomething = true;\n          self.replaceRange(\"\", Pos(i, pos), Pos(i, endPos));\n        }\n      });\n      if (didSomething) return true;\n    }\n\n    // Try block comments\n    var startString = options.blockCommentStart || mode.blockCommentStart;\n    var endString = options.blockCommentEnd || mode.blockCommentEnd;\n    if (!startString || !endString) return false;\n    var lead = options.blockCommentLead || mode.blockCommentLead;\n    var startLine = self.getLine(start), open = startLine.indexOf(startString)\n    if (open == -1) return false\n    var endLine = end == start ? startLine : self.getLine(end)\n    var close = endLine.indexOf(endString, end == start ? open + startString.length : 0);\n    var insideStart = Pos(start, open + 1), insideEnd = Pos(end, close + 1)\n    if (close == -1 ||\n        !/comment/.test(self.getTokenTypeAt(insideStart)) ||\n        !/comment/.test(self.getTokenTypeAt(insideEnd)) ||\n        self.getRange(insideStart, insideEnd, \"\\n\").indexOf(endString) > -1)\n      return false;\n\n    // Avoid killing block comments completely outside the selection.\n    // Positions of the last startString before the start of the selection, and the first endString after it.\n    var lastStart = startLine.lastIndexOf(startString, from.ch);\n    var firstEnd = lastStart == -1 ? -1 : startLine.slice(0, from.ch).indexOf(endString, lastStart + startString.length);\n    if (lastStart != -1 && firstEnd != -1 && firstEnd + endString.length != from.ch) return false;\n    // Positions of the first endString after the end of the selection, and the last startString before it.\n    firstEnd = endLine.indexOf(endString, to.ch);\n    var almostLastStart = endLine.slice(to.ch).lastIndexOf(startString, firstEnd - to.ch);\n    lastStart = (firstEnd == -1 || almostLastStart == -1) ? -1 : to.ch + almostLastStart;\n    if (firstEnd != -1 && lastStart != -1 && lastStart != to.ch) return false;\n\n    self.operation(function() {\n      self.replaceRange(\"\", Pos(end, close - (pad && endLine.slice(close - pad.length, close) == pad ? pad.length : 0)),\n                        Pos(end, close + endString.length));\n      var openEnd = open + startString.length;\n      if (pad && startLine.slice(openEnd, openEnd + pad.length) == pad) openEnd += pad.length;\n      self.replaceRange(\"\", Pos(start, open), Pos(start, openEnd));\n      if (lead) for (var i = start + 1; i <= end; ++i) {\n        var line = self.getLine(i), found = line.indexOf(lead);\n        if (found == -1 || nonWS.test(line.slice(0, found))) continue;\n        var foundEnd = found + lead.length;\n        if (pad && line.slice(foundEnd, foundEnd + pad.length) == pad) foundEnd += pad.length;\n        self.replaceRange(\"\", Pos(i, found), Pos(i, foundEnd));\n      }\n    });\n    return true;\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,GAAC,SAAS,KAAK;AAEX,QAAIA,kBAA+B,CAAA;EAKtC,GAAE,SAAS,YAAY;AAGtB,QAAI,YAAY,CAAA;AAChB,QAAI,QAAQ;AACZ,QAAI,MAAM,WAAW,KAAK,MAAM,WAAW;AAE3C,aAAS,WAAW,KAAK;AACvB,UAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,aAAO,SAAS,KAAK,IAAI;IAC1B;AAED,eAAW,SAAS,gBAAgB,SAAS,IAAI;AAC/C,SAAG,cAAa;IACpB;AAEE,eAAW,gBAAgB,iBAAiB,SAAS,SAAS;AAC5D,UAAI,CAAC,QAAS,WAAU;AACxB,UAAI,KAAK;AACT,UAAI,UAAU,UAAU,SAAS,KAAK,eAAgB,GAAE,OAAO;AAC/D,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,OAAO,CAAC,EAAE,KAAI,GAAI,KAAK,OAAO,CAAC,EAAE,GAAA;AAC5C,YAAI,KAAK,QAAQ,QAAS;AAC1B,YAAI,GAAG,QAAQ,QAAS,MAAK,IAAI,SAAS,CAAC;AAC3C,kBAAU,KAAK;AACf,YAAI,QAAQ,MAAM;AAChB,cAAI,GAAG,UAAU,MAAM,IAAI,OAAO,EAAG,QAAO;eACvC;AAAE,eAAG,YAAY,MAAM,IAAI,OAAO;AAAG,mBAAO;UAAS;QAClE,WAAiB,QAAQ,MAAM;AACvB,aAAG,UAAU,MAAM,IAAI,OAAO;QACtC,OAAa;AACL,aAAG,YAAY,MAAM,IAAI,OAAO;QACjC;MACF;IACL,CAAG;AAGD,aAAS,qBAAqB,IAAI,KAAK,MAAM;AAC3C,aAAO,aAAa,KAAK,GAAG,eAAe,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,KAAK,IAAI;IACxF;AAED,aAAS,QAAQ,IAAI,KAAK;AACxB,UAAI,OAAO,GAAG,QAAS;AACvB,aAAO,KAAK,qBAAqB,SAAS,CAAC,KAAK,YAAY,OAAO,GAAG,UAAU,GAAG;IACpF;AAED,eAAW,gBAAgB,eAAe,SAAS,MAAM,IAAI,SAAS;AACpE,UAAI,CAAC,QAAS,WAAU;AACxB,UAAI,OAAO,MAAM,OAAO,QAAQ,MAAM,IAAI;AAC1C,UAAI,YAAY,KAAK,QAAQ,KAAK,IAAI;AACtC,UAAI,aAAa,QAAQ,qBAAqB,MAAM,MAAM,SAAS,EAAG;AAEtE,UAAI,gBAAgB,QAAQ,eAAe,KAAK;AAChD,UAAI,CAAC,eAAe;AAClB,YAAI,QAAQ,qBAAqB,KAAK,mBAAmB;AACvD,kBAAQ,YAAY;AACpB,eAAK,aAAa,MAAM,IAAI,OAAO;QACpC;AACD;MACD;AAED,UAAI,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG,QAAQ,KAAK,OAAO,GAAG,OAAO,IAAI,GAAG,MAAM,KAAK,SAAA,IAAa,CAAC;AAClG,UAAI,MAAM,QAAQ,WAAW,OAAO,MAAM,QAAQ;AAClD,UAAI,aAAa,QAAQ,qBAAqB,KAAK,QAAQ,GAAG;AAE9D,WAAK,UAAU,WAAW;AACxB,YAAI,QAAQ,QAAQ;AAClB,cAAI,aAAa;AACjB,mBAAS,IAAI,KAAK,MAAM,IAAI,KAAK,EAAE,GAAG;AACpC,gBAAI,OAAO,KAAK,QAAQ,CAAC;AACzB,gBAAI,aAAa,KAAK,MAAM,GAAG,WAAW,IAAI,CAAC;AAC/C,gBAAI,cAAc,QAAQ,WAAW,SAAS,WAAW,QAAQ;AAC/D,2BAAa;YACd;UACF;AACD,mBAAS,IAAI,KAAK,MAAM,IAAI,KAAK,EAAE,GAAG;AACpC,gBAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM,WAAW;AAC7C,gBAAI,CAAC,cAAc,CAAC,MAAM,KAAK,IAAI,EAAG;AACtC,gBAAI,KAAK,MAAM,GAAG,GAAG,KAAK,WAAY,OAAM,WAAW,IAAI;AAC3D,iBAAK,aAAa,aAAa,gBAAgB,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;UAC3E;QACT,OAAa;AACL,mBAAS,IAAI,KAAK,MAAM,IAAI,KAAK,EAAE,GAAG;AACpC,gBAAI,cAAc,MAAM,KAAK,KAAK,QAAQ,CAAC,CAAC;AAC1C,mBAAK,aAAa,gBAAgB,KAAK,IAAI,GAAG,CAAC,CAAC;UACnD;QACF;MACP,CAAK;IACL,CAAG;AAED,eAAW,gBAAgB,gBAAgB,SAAS,MAAM,IAAI,SAAS;AACrE,UAAI,CAAC,QAAS,WAAU;AACxB,UAAI,OAAO,MAAM,OAAO,QAAQ,MAAM,IAAI;AAC1C,UAAI,cAAc,QAAQ,qBAAqB,KAAK;AACpD,UAAI,YAAY,QAAQ,mBAAmB,KAAK;AAChD,UAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,aAAK,QAAQ,eAAe,KAAK,gBAAgB,QAAQ,aAAa;AACpE,eAAK,YAAY,MAAM,IAAI,OAAO;AACpC;MACD;AACD,UAAI,cAAc,KAAK,KAAK,eAAe,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,EAAG;AAEhE,UAAI,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,SAAQ,CAAE;AAC3C,UAAI,OAAO,KAAK,QAAQ,GAAG,MAAM,KAAK,MAAM,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAG,GAAE;AAEvE,UAAI,MAAM,QAAQ,WAAW,OAAO,MAAM,QAAQ;AAClD,UAAI,KAAK,OAAO,IAAK;AAErB,WAAK,UAAU,WAAW;AACxB,YAAI,QAAQ,aAAa,OAAO;AAC9B,cAAI,kBAAkB,MAAM,KAAK,KAAK,QAAQ,GAAG,CAAC;AAClD,eAAK,aAAa,MAAM,WAAW,IAAI,GAAG,CAAC;AAC3C,eAAK,aAAa,cAAc,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC;AACtD,cAAI,OAAO,QAAQ,oBAAoB,KAAK;AAC5C,cAAI,QAAQ,MAAA;AAAM,qBAAS,IAAI,KAAK,OAAO,GAAG,KAAK,KAAK,EAAE;AACxD,kBAAI,KAAK,OAAO;AACd,qBAAK,aAAa,OAAO,KAAK,IAAI,GAAG,CAAC,CAAC;UAAA;QACnD,OAAa;AACL,cAAI,WAAW,IAAI,KAAK,UAAU,IAAI,GAAG,EAAE,KAAK,GAAG,QAAQ,CAAC,KAAK,kBAAmB;AACpF,eAAK,aAAa,WAAW,EAAE;AAC/B,cAAI,SAAU,MAAK,aAAa,QAAQ,KAAK,KAAK,UAAU,MAAM,GAAG,EAAE;AACvE,eAAK,aAAa,aAAa,IAAI;QACpC;MACP,CAAK;IACL,CAAG;AAED,eAAW,gBAAgB,aAAa,SAAS,MAAM,IAAI,SAAS;AAClE,UAAI,CAAC,QAAS,WAAU;AACxB,UAAI,OAAO,MAAM,OAAO,QAAQ,MAAM,IAAI;AAC1C,UAAI,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,KAAK,SAAQ,CAAE,GAAG,QAAQ,KAAK,IAAI,KAAK,MAAM,GAAG;AAGhI,UAAI,aAAa,QAAQ,eAAe,KAAK,aAAa,QAAQ,CAAA;AAClE,UAAI,MAAM,QAAQ,WAAW,OAAO,MAAM,QAAQ,SAAS;AAC3D,mBAAa;AACX,YAAI,CAAC,WAAY,OAAM;AACvB,iBAAS,IAAI,OAAO,KAAK,KAAK,EAAE,GAAG;AACjC,cAAI,OAAO,KAAK,QAAQ,CAAC;AACzB,cAAI,QAAQ,KAAK,QAAQ,UAAU;AACnC,cAAI,QAAQ,MAAM,CAAC,UAAU,KAAK,KAAK,eAAe,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAG,SAAQ;AACnF,cAAI,SAAS,MAAM,MAAM,KAAK,IAAI,EAAG,OAAM;AAC3C,cAAI,QAAQ,MAAM,MAAM,KAAK,KAAK,MAAM,GAAG,KAAK,CAAC,EAAG,OAAM;AAC1D,gBAAM,KAAK,IAAI;QAChB;AACD,aAAK,UAAU,WAAW;AACxB,mBAASC,KAAI,OAAOA,MAAK,KAAK,EAAEA,IAAG;AACjC,gBAAIC,QAAO,MAAMD,KAAI,KAAK;AAC1B,gBAAI,MAAMC,MAAK,QAAQ,UAAU,GAAG,SAAS,MAAM,WAAW;AAC9D,gBAAI,MAAM,EAAG;AACb,gBAAIA,MAAK,MAAM,QAAQ,SAAS,IAAI,MAAM,KAAK,IAAK,WAAU,IAAI;AAClE,2BAAe;AACf,iBAAK,aAAa,IAAI,IAAID,IAAG,GAAG,GAAG,IAAIA,IAAG,MAAM,CAAC;UAClD;QACT,CAAO;AACD,YAAI,aAAc,QAAO;MAC1B;AAGD,UAAI,cAAc,QAAQ,qBAAqB,KAAK;AACpD,UAAI,YAAY,QAAQ,mBAAmB,KAAK;AAChD,UAAI,CAAC,eAAe,CAAC,UAAW,QAAO;AACvC,UAAI,OAAO,QAAQ,oBAAoB,KAAK;AAC5C,UAAI,YAAY,KAAK,QAAQ,KAAK,GAAG,OAAO,UAAU,QAAQ,WAAW;AACzE,UAAI,QAAQ,GAAI,QAAO;AACvB,UAAI,UAAU,OAAO,QAAQ,YAAY,KAAK,QAAQ,GAAG;AACzD,UAAI,QAAQ,QAAQ,QAAQ,WAAW,OAAO,QAAQ,OAAO,YAAY,SAAS,CAAC;AACnF,UAAI,cAAc,IAAI,OAAO,OAAO,CAAC,GAAG,YAAY,IAAI,KAAK,QAAQ,CAAC;AACtE,UAAI,SAAS,MACT,CAAC,UAAU,KAAK,KAAK,eAAe,WAAW,CAAC,KAChD,CAAC,UAAU,KAAK,KAAK,eAAe,SAAS,CAAC,KAC9C,KAAK,SAAS,aAAa,WAAW,IAAI,EAAE,QAAQ,SAAS,IAAI;AACnE,eAAO;AAIT,UAAI,YAAY,UAAU,YAAY,aAAa,KAAK,EAAE;AAC1D,UAAI,WAAW,aAAa,KAAK,KAAK,UAAU,MAAM,GAAG,KAAK,EAAE,EAAE,QAAQ,WAAW,YAAY,YAAY,MAAM;AACnH,UAAI,aAAa,MAAM,YAAY,MAAM,WAAW,UAAU,UAAU,KAAK,GAAI,QAAO;AAExF,iBAAW,QAAQ,QAAQ,WAAW,GAAG,EAAE;AAC3C,UAAI,kBAAkB,QAAQ,MAAM,GAAG,EAAE,EAAE,YAAY,aAAa,WAAW,GAAG,EAAE;AACpF,kBAAa,YAAY,MAAM,mBAAmB,KAAM,KAAK,GAAG,KAAK;AACrE,UAAI,YAAY,MAAM,aAAa,MAAM,aAAa,GAAG,GAAI,QAAO;AAEpE,WAAK,UAAU,WAAW;AACxB,aAAK;UAAa;UAAI,IAAI,KAAK,SAAS,OAAO,QAAQ,MAAM,QAAQ,IAAI,QAAQ,KAAK,KAAK,MAAM,IAAI,SAAS,EAAE;UAC9F,IAAI,KAAK,QAAQ,UAAU,MAAM;QAAC;AACpD,YAAI,UAAU,OAAO,YAAY;AACjC,YAAI,OAAO,UAAU,MAAM,SAAS,UAAU,IAAI,MAAM,KAAK,IAAK,YAAW,IAAI;AACjF,aAAK,aAAa,IAAI,IAAI,OAAO,IAAI,GAAG,IAAI,OAAO,OAAO,CAAC;AAC3D,YAAI,KAAM,UAASA,KAAI,QAAQ,GAAGA,MAAK,KAAK,EAAEA,IAAG;AAC/C,cAAIC,QAAO,KAAK,QAAQD,EAAC,GAAGE,SAAQD,MAAK,QAAQ,IAAI;AACrD,cAAIC,UAAS,MAAM,MAAM,KAAKD,MAAK,MAAM,GAAGC,MAAK,CAAC,EAAG;AACrD,cAAI,WAAWA,SAAQ,KAAK;AAC5B,cAAI,OAAOD,MAAK,MAAM,UAAU,WAAW,IAAI,MAAM,KAAK,IAAK,aAAY,IAAI;AAC/E,eAAK,aAAa,IAAI,IAAID,IAAGE,MAAK,GAAG,IAAIF,IAAG,QAAQ,CAAC;QACtD;MACP,CAAK;AACD,aAAO;IACX,CAAG;EACH,CAAC;;;;;;;;", "names": ["require$$0", "i", "line", "found"]}
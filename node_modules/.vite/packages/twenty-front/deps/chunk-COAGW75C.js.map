{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/getTypeInfo.js", "../../../../@graphiql/codemirror-graphql/esm/utils/SchemaReference.js"], "sourcesContent": ["import { isCompositeType, getNullableType, getNamedType, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, } from 'graphql';\nimport forEachState from './forEachState';\nexport default function getTypeInfo(schema, tokenState) {\n    const info = {\n        schema,\n        type: null,\n        parentType: null,\n        inputType: null,\n        directiveDef: null,\n        fieldDef: null,\n        argDef: null,\n        argDefs: null,\n        objectFieldDefs: null,\n    };\n    forEachState(tokenState, (state) => {\n        var _a, _b;\n        switch (state.kind) {\n            case 'Query':\n            case 'ShortQuery':\n                info.type = schema.getQueryType();\n                break;\n            case 'Mutation':\n                info.type = schema.getMutationType();\n                break;\n            case 'Subscription':\n                info.type = schema.getSubscriptionType();\n                break;\n            case 'InlineFragment':\n            case 'FragmentDefinition':\n                if (state.type) {\n                    info.type = schema.getType(state.type);\n                }\n                break;\n            case 'Field':\n            case 'AliasedField':\n                info.fieldDef =\n                    info.type && state.name\n                        ? getFieldDef(schema, info.parentType, state.name)\n                        : null;\n                info.type = (_a = info.fieldDef) === null || _a === void 0 ? void 0 : _a.type;\n                break;\n            case 'SelectionSet':\n                info.parentType = info.type ? getNamedType(info.type) : null;\n                break;\n            case 'Directive':\n                info.directiveDef = state.name ? schema.getDirective(state.name) : null;\n                break;\n            case 'Arguments':\n                const parentDef = state.prevState\n                    ? state.prevState.kind === 'Field'\n                        ? info.fieldDef\n                        : state.prevState.kind === 'Directive'\n                            ? info.directiveDef\n                            : state.prevState.kind === 'AliasedField'\n                                ? state.prevState.name &&\n                                    getFieldDef(schema, info.parentType, state.prevState.name)\n                                : null\n                    : null;\n                info.argDefs = parentDef ? parentDef.args : null;\n                break;\n            case 'Argument':\n                info.argDef = null;\n                if (info.argDefs) {\n                    for (let i = 0; i < info.argDefs.length; i++) {\n                        if (info.argDefs[i].name === state.name) {\n                            info.argDef = info.argDefs[i];\n                            break;\n                        }\n                    }\n                }\n                info.inputType = (_b = info.argDef) === null || _b === void 0 ? void 0 : _b.type;\n                break;\n            case 'EnumValue':\n                const enumType = info.inputType ? getNamedType(info.inputType) : null;\n                info.enumValue =\n                    enumType instanceof GraphQLEnumType\n                        ? find(enumType.getValues(), val => val.value === state.name)\n                        : null;\n                break;\n            case 'ListValue':\n                const nullableType = info.inputType\n                    ? getNullableType(info.inputType)\n                    : null;\n                info.inputType =\n                    nullableType instanceof GraphQLList ? nullableType.ofType : null;\n                break;\n            case 'ObjectValue':\n                const objectType = info.inputType ? getNamedType(info.inputType) : null;\n                info.objectFieldDefs =\n                    objectType instanceof GraphQLInputObjectType\n                        ? objectType.getFields()\n                        : null;\n                break;\n            case 'ObjectField':\n                const objectField = state.name && info.objectFieldDefs\n                    ? info.objectFieldDefs[state.name]\n                    : null;\n                info.inputType = objectField === null || objectField === void 0 ? void 0 : objectField.type;\n                info.fieldDef = objectField;\n                break;\n            case 'NamedType':\n                info.type = state.name ? schema.getType(state.name) : null;\n                break;\n        }\n    });\n    return info;\n}\nfunction getFieldDef(schema, type, fieldName) {\n    if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === type) {\n        return SchemaMetaFieldDef;\n    }\n    if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === type) {\n        return TypeMetaFieldDef;\n    }\n    if (fieldName === TypeNameMetaFieldDef.name && isCompositeType(type)) {\n        return TypeNameMetaFieldDef;\n    }\n    if (type && type.getFields) {\n        return type.getFields()[fieldName];\n    }\n}\nfunction find(array, predicate) {\n    for (let i = 0; i < array.length; i++) {\n        if (predicate(array[i])) {\n            return array[i];\n        }\n    }\n}\n//# sourceMappingURL=getTypeInfo.js.map", "import { getNamedType } from 'graphql';\nexport function getFieldReference(typeInfo) {\n    return {\n        kind: 'Field',\n        schema: typeInfo.schema,\n        field: typeInfo.fieldDef,\n        type: isMetaField(typeInfo.fieldDef) ? null : typeInfo.parentType,\n    };\n}\nexport function getDirectiveReference(typeInfo) {\n    return {\n        kind: 'Directive',\n        schema: typeInfo.schema,\n        directive: typeInfo.directiveDef,\n    };\n}\nexport function getArgumentReference(typeInfo) {\n    return typeInfo.directiveDef\n        ? {\n            kind: 'Argument',\n            schema: typeInfo.schema,\n            argument: typeInfo.argDef,\n            directive: typeInfo.directiveDef,\n        }\n        : {\n            kind: 'Argument',\n            schema: typeInfo.schema,\n            argument: typeInfo.argDef,\n            field: typeInfo.fieldDef,\n            type: isMetaField(typeInfo.fieldDef) ? null : typeInfo.parentType,\n        };\n}\nexport function getEnumValueReference(typeInfo) {\n    return {\n        kind: 'EnumValue',\n        value: typeInfo.enumValue || undefined,\n        type: typeInfo.inputType\n            ? getNamedType(typeInfo.inputType)\n            : undefined,\n    };\n}\nexport function getTypeReference(typeInfo, type) {\n    return {\n        kind: 'Type',\n        schema: typeInfo.schema,\n        type: type || typeInfo.type,\n    };\n}\nfunction isMetaField(fieldDef) {\n    return fieldDef.name.slice(0, 2) === '__';\n}\n//# sourceMappingURL=SchemaReference.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;AAEe,SAAS,YAAY,QAAQ,YAAY;AACpD,QAAM,OAAO;IACT;IACA,MAAM;IACN,YAAY;IACZ,WAAW;IACX,cAAc;IACd,UAAU;IACV,QAAQ;IACR,SAAS;IACT,iBAAiB;EACzB;AACI,eAAa,YAAY,CAAC,UAAU;AAChC,QAAI,IAAI;AACR,YAAQ,MAAM,MAAI;MACd,KAAK;MACL,KAAK;AACD,aAAK,OAAO,OAAO,aAAA;AACnB;MACJ,KAAK;AACD,aAAK,OAAO,OAAO,gBAAA;AACnB;MACJ,KAAK;AACD,aAAK,OAAO,OAAO,oBAAA;AACnB;MACJ,KAAK;MACL,KAAK;AACD,YAAI,MAAM,MAAM;AACZ,eAAK,OAAO,OAAO,QAAQ,MAAM,IAAI;QACxC;AACD;MACJ,KAAK;MACL,KAAK;AACD,aAAK,WACD,KAAK,QAAQ,MAAM,OACb,YAAY,QAAQ,KAAK,YAAY,MAAM,IAAI,IAC/C;AACV,aAAK,QAAQ,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG;AACzE;MACJ,KAAK;AACD,aAAK,aAAa,KAAK,OAAO,aAAa,KAAK,IAAI,IAAI;AACxD;MACJ,KAAK;AACD,aAAK,eAAe,MAAM,OAAO,OAAO,aAAa,MAAM,IAAI,IAAI;AACnE;MACJ,KAAK;AACD,cAAM,YAAY,MAAM,YAClB,MAAM,UAAU,SAAS,UACrB,KAAK,WACL,MAAM,UAAU,SAAS,cACrB,KAAK,eACL,MAAM,UAAU,SAAS,iBACrB,MAAM,UAAU,QACd,YAAY,QAAQ,KAAK,YAAY,MAAM,UAAU,IAAI,IAC3D,OACZ;AACN,aAAK,UAAU,YAAY,UAAU,OAAO;AAC5C;MACJ,KAAK;AACD,aAAK,SAAS;AACd,YAAI,KAAK,SAAS;AACd,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,gBAAI,KAAK,QAAQ,CAAC,EAAE,SAAS,MAAM,MAAM;AACrC,mBAAK,SAAS,KAAK,QAAQ,CAAC;AAC5B;YACH;UACJ;QACJ;AACD,aAAK,aAAa,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E;MACJ,KAAK;AACD,cAAM,WAAW,KAAK,YAAY,aAAa,KAAK,SAAS,IAAI;AACjE,aAAK,YACD,oBAAoB,kBACd,KAAK,SAAS,UAAA,GAAa,CAAA,QAAO,IAAI,UAAU,MAAM,IAAI,IAC1D;AACV;MACJ,KAAK;AACD,cAAM,eAAe,KAAK,YACpB,gBAAgB,KAAK,SAAS,IAC9B;AACN,aAAK,YACD,wBAAwB,cAAc,aAAa,SAAS;AAChE;MACJ,KAAK;AACD,cAAM,aAAa,KAAK,YAAY,aAAa,KAAK,SAAS,IAAI;AACnE,aAAK,kBACD,sBAAsB,yBAChB,WAAW,UAAW,IACtB;AACV;MACJ,KAAK;AACD,cAAM,cAAc,MAAM,QAAQ,KAAK,kBACjC,KAAK,gBAAgB,MAAM,IAAI,IAC/B;AACN,aAAK,YAAY,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACvF,aAAK,WAAW;AAChB;MACJ,KAAK;AACD,aAAK,OAAO,MAAM,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI;AACtD;IACP;EACT,CAAK;AACD,SAAO;AACX;AACA,SAAS,YAAY,QAAQ,MAAM,WAAW;AAC1C,MAAI,cAAc,mBAAmB,QAAQ,OAAO,aAAc,MAAK,MAAM;AACzE,WAAO;EACV;AACD,MAAI,cAAc,iBAAiB,QAAQ,OAAO,aAAc,MAAK,MAAM;AACvE,WAAO;EACV;AACD,MAAI,cAAc,qBAAqB,QAAQ,gBAAgB,IAAI,GAAG;AAClE,WAAO;EACV;AACD,MAAI,QAAQ,KAAK,WAAW;AACxB,WAAO,KAAK,UAAA,EAAY,SAAS;EACpC;AACL;AACA,SAAS,KAAK,OAAO,WAAW;AAC5B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AACrB,aAAO,MAAM,CAAC;IACjB;EACJ;AACL;AC9HO,SAAS,kBAAkB,UAAU;AACxC,SAAO;IACH,MAAM;IACN,QAAQ,SAAS;IACjB,OAAO,SAAS;IAChB,MAAM,YAAY,SAAS,QAAQ,IAAI,OAAO,SAAS;EAC/D;AACA;AACO,SAAS,sBAAsB,UAAU;AAC5C,SAAO;IACH,MAAM;IACN,QAAQ,SAAS;IACjB,WAAW,SAAS;EAC5B;AACA;AACO,SAAS,qBAAqB,UAAU;AAC3C,SAAO,SAAS,eACV;IACE,MAAM;IACN,QAAQ,SAAS;IACjB,UAAU,SAAS;IACnB,WAAW,SAAS;EACvB,IACC;IACE,MAAM;IACN,QAAQ,SAAS;IACjB,UAAU,SAAS;IACnB,OAAO,SAAS;IAChB,MAAM,YAAY,SAAS,QAAQ,IAAI,OAAO,SAAS;EACnE;AACA;AACO,SAAS,sBAAsB,UAAU;AAC5C,SAAO;IACH,MAAM;IACN,OAAO,SAAS,aAAa;IAC7B,MAAM,SAAS,YACT,aAAa,SAAS,SAAS,IAC/B;EACd;AACA;AACO,SAAS,iBAAiB,UAAU,MAAM;AAC7C,SAAO;IACH,MAAM;IACN,QAAQ,SAAS;IACjB,MAAM,QAAQ,SAAS;EAC/B;AACA;AACA,SAAS,YAAY,UAAU;AAC3B,SAAO,SAAS,KAAK,MAAM,GAAG,CAAC,MAAM;AACzC;", "names": []}
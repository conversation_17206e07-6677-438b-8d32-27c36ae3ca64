{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/dart/dart.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dart/dart.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: /^\\s*\\s*#?region\\b/,\n      end: /^\\s*\\s*#?endregion\\b/\n    }\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".dart\",\n  keywords: [\n    \"abstract\",\n    \"dynamic\",\n    \"implements\",\n    \"show\",\n    \"as\",\n    \"else\",\n    \"import\",\n    \"static\",\n    \"assert\",\n    \"enum\",\n    \"in\",\n    \"super\",\n    \"async\",\n    \"export\",\n    \"interface\",\n    \"switch\",\n    \"await\",\n    \"extends\",\n    \"is\",\n    \"sync\",\n    \"break\",\n    \"external\",\n    \"library\",\n    \"this\",\n    \"case\",\n    \"factory\",\n    \"mixin\",\n    \"throw\",\n    \"catch\",\n    \"false\",\n    \"new\",\n    \"true\",\n    \"class\",\n    \"final\",\n    \"null\",\n    \"try\",\n    \"const\",\n    \"finally\",\n    \"on\",\n    \"typedef\",\n    \"continue\",\n    \"for\",\n    \"operator\",\n    \"var\",\n    \"covariant\",\n    \"Function\",\n    \"part\",\n    \"void\",\n    \"default\",\n    \"get\",\n    \"rethrow\",\n    \"while\",\n    \"deferred\",\n    \"hide\",\n    \"return\",\n    \"with\",\n    \"do\",\n    \"if\",\n    \"set\",\n    \"yield\"\n  ],\n  typeKeywords: [\"int\", \"double\", \"String\", \"bool\"],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"~/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"==\",\n    \"!=\",\n    \">\",\n    \"<\",\n    \">=\",\n    \"<=\",\n    \"=\",\n    \"-=\",\n    \"/=\",\n    \"%=\",\n    \">>=\",\n    \"^=\",\n    \"+=\",\n    \"*=\",\n    \"~/=\",\n    \"<<=\",\n    \"&=\",\n    \"!=\",\n    \"||\",\n    \"&&\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"~\",\n    \"<<\",\n    \">>\",\n    \"!\",\n    \">>>\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"|=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type.identifier\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z_$][\\w\\$]*/, \"type.identifier\"],\n      // show class names\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // @ annotations.\n      [/@[a-zA-Z]+/, \"annotation\"],\n      // variable\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"]\n      //   [/[a-zA-Z]+/, \"variable\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([gimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ],\n    string_single: [\n      [/[^\\\\'\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAoCA;AA7CJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,QAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAE;AAAA,MACjD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO,UAAU,UAAU,MAAM;AAAA,MAChD,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,cAAc;AAAA,MACd,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAEX,WAAW;AAAA,QACT,MAAM,CAAC,CAAC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,QAC3D,QAAQ;AAAA;AAAA,UAEN;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,iBAAiB;AAAA,gBACjB,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,kBAAkB,iBAAiB;AAAA;AAAA;AAAA;AAAA,UAIpC,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB;AAAA,YACE;AAAA,YACA,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,UAAU;AAAA,UACvD;AAAA;AAAA,UAEA,CAAC,cAAc,YAAY;AAAA;AAAA;AAAA,UAG3B,CAAC,YAAY,WAAW;AAAA,UACxB,CAAC,oBAAoB,WAAW;AAAA,UAChC,CAAC,iBAAiB,WAAW;AAAA,UAC7B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,mCAAmC,cAAc;AAAA,UAClD,CAAC,8CAA8C,cAAc;AAAA,UAC7D,CAAC,uBAAuB,YAAY;AAAA,UACpC,CAAC,0BAA0B,cAAc;AAAA,UACzC,CAAC,0BAA0B,eAAe;AAAA,UAC1C,CAAC,eAAe,QAAQ;AAAA;AAAA,UAExB,CAAC,SAAS,WAAW;AAAA;AAAA,UAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,KAAK,UAAU,gBAAgB;AAAA,UAChC,CAAC,KAAK,UAAU,gBAAgB;AAAA;AAAA,QAElC;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,gBAAgB,eAAe,QAAQ;AAAA,UACxC,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,aAAa,aAAa;AAAA,UAC3B,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA,QACA,OAAO;AAAA,UACL,CAAC,WAAW,aAAa;AAAA,UACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,UAC9B,CAAC,SAAS,aAAa;AAAA,QACzB;AAAA;AAAA,QAEA,QAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA,CAAC,yBAAyB,yBAAyB,uBAAuB;AAAA,UAC5E;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,yBAAyB,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,UACnF;AAAA,UACA,CAAC,qBAAqB,CAAC,yBAAyB,uBAAuB,CAAC;AAAA,UACxE,CAAC,QAAQ,uBAAuB;AAAA,UAChC,CAAC,cAAc,uBAAuB;AAAA,UACtC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,cAAc,eAAe;AAAA,UAC9B,CAAC,QAAQ,gBAAgB;AAAA,UACzB,CAAC,mBAAmB,CAAC,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,GAAG,eAAe,CAAC;AAAA,QAC7F;AAAA,QACA,YAAY;AAAA,UACV,CAAC,KAAK,uBAAuB;AAAA,UAC7B,CAAC,MAAM,gBAAgB;AAAA,UACvB,CAAC,cAAc,eAAe;AAAA,UAC9B,CAAC,SAAS,QAAQ;AAAA,UAClB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,UACtB,CAAC,SAAS,YAAY;AAAA,QACxB;AAAA,QACA,eAAe;AAAA,UACb,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,UACtB,CAAC,SAAS,YAAY;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
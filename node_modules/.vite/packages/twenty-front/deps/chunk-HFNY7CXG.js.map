{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Request/consts/mediaTypes.js"], "sourcesContent": ["const n = {\n  \"application/epub+zip\": { extension: \".epub\" },\n  \"application/gzip\": { extension: \".gz\" },\n  \"application/java-archive\": { extension: \".jar\" },\n  \"application/javascript\": { extension: \".js\", raw: !0 },\n  \"application/json\": { extension: \".json\", raw: !0, language: \"json\" },\n  \"application/ld+json\": { extension: \".jsonld\", raw: !0, language: \"json\" },\n  \"application/problem+json\": {\n    extension: \".json\",\n    raw: !0,\n    language: \"json\"\n  },\n  \"application/msword\": { extension: \".doc\" },\n  \"application/octet-stream\": { extension: \".bin\" },\n  \"application/ogg\": { extension: \".ogx\" },\n  \"application/pdf\": { extension: \".pdf\", preview: \"object\" },\n  \"application/rtf\": { extension: \".rtf\", raw: !0 },\n  \"application/vnd.amazon.ebook\": { extension: \".azw\" },\n  \"application/vnd.apple.installer+xml\": {\n    extension: \".mpkg\",\n    raw: !0,\n    language: \"xml\"\n  },\n  \"application/vnd.mozilla.xul+xml\": {\n    extension: \".xul\",\n    raw: !0,\n    language: \"xml\"\n  },\n  \"application/vnd.ms-excel\": { extension: \".xls\" },\n  \"application/vnd.ms-fontobject\": { extension: \".eot\" },\n  \"application/vnd.ms-powerpoint\": { extension: \".ppt\" },\n  \"application/vnd.oasis.opendocument.presentation\": { extension: \".odp\" },\n  \"application/vnd.oasis.opendocument.spreadsheet\": { extension: \".ods\" },\n  \"application/vnd.oasis.opendocument.text\": { extension: \".odt\" },\n  \"application/vnd.openxmlformats-officedocument.presentationml.presentation\": {\n    extension: \".pptx\"\n  },\n  \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": {\n    extension: \".xlsx\"\n  },\n  \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": {\n    extension: \".docx\"\n  },\n  \"application/vnd.rar\": { extension: \".rar\" },\n  \"application/vnd.visio\": { extension: \".vsd\" },\n  \"application/x-7z-compressed\": { extension: \".7z\" },\n  \"application/x-abiword\": { extension: \".abw\" },\n  \"application/x-bzip\": { extension: \".bz\" },\n  \"application/x-bzip2\": { extension: \".bz2\" },\n  \"application/x-cdf\": { extension: \".cda\" },\n  \"application/x-csh\": { extension: \".csh\" },\n  \"application/x-freearc\": { extension: \".arc\" },\n  \"application/x-httpd-php\": { extension: \".php\", raw: !0 },\n  \"application/x-sh\": { extension: \".sh\", raw: !0 },\n  \"application/x-tar\": { extension: \".tar\" },\n  \"application/xhtml+xml\": { extension: \".xhtml\", raw: !0, language: \"html\" },\n  \"application/xml\": { extension: \".xml\", raw: !0, language: \"xml\" },\n  \"application/yaml\": { extension: \".yaml\", raw: !0, language: \"yaml\" },\n  \"application/zip\": { extension: \".zip\" },\n  \"audio/aac\": { extension: \".aac\" },\n  \"audio/midi\": { extension: \".midi\" },\n  \"audio/mpeg\": { extension: \".mp3\", preview: \"audio\" },\n  \"audio/ogg\": { extension: \".oga\" },\n  \"audio/wav\": { extension: \".wav\" },\n  \"audio/webm\": { extension: \".weba\" },\n  \"font/otf\": { extension: \".otf\" },\n  \"font/ttf\": { extension: \".ttf\" },\n  \"font/woff\": { extension: \".woff\" },\n  \"font/woff2\": { extension: \".woff2\" },\n  \"image/apng\": { extension: \".apng\", preview: \"image\", alpha: !0 },\n  \"image/avif\": { extension: \".avif\", preview: \"image\" },\n  \"image/bmp\": { extension: \".bmp\", preview: \"image\" },\n  \"image/gif\": { extension: \".gif\", preview: \"image\", alpha: !0 },\n  \"image/jpeg\": { extension: \".jpg\", preview: \"image\" },\n  \"image/png\": { extension: \".png\", preview: \"image\", alpha: !0 },\n  \"image/svg+xml\": {\n    extension: \".svg\",\n    raw: !0,\n    language: \"xml\",\n    preview: \"image\",\n    alpha: !0\n  },\n  \"image/tiff\": { extension: \".tiff\" },\n  \"image/vnd.microsoft.icon\": { extension: \".ico\", preview: \"image\" },\n  \"image/webp\": { extension: \".webp\", preview: \"image\", alpha: !0 },\n  \"text/calendar\": { extension: \".ics\", raw: !0 },\n  \"text/css\": { extension: \".css\", raw: !0, language: \"css\" },\n  \"text/csv\": { extension: \".csv\", raw: !0 },\n  \"text/html\": {\n    extension: \".html\",\n    raw: !0,\n    language: \"html\",\n    preview: \"object\"\n  },\n  \"text/javascript\": { extension: \".js\", raw: !0 },\n  \"text/plain\": { extension: \".txt\", raw: !0 },\n  \"text/xml\": { extension: \".xml\", raw: !0, language: \"xml\" },\n  \"text/yaml\": { extension: \".yaml\", raw: !0, language: \"yaml\" },\n  \"video/3gpp\": { extension: \".3gp\" },\n  \"audio/3gpp\": { extension: \".3gp\" },\n  \"video/3gpp2\": { extension: \".3g2\" },\n  \"audio/3gpp2\": { extension: \".3g2\" },\n  \"video/mp2t\": { extension: \".ts\" },\n  \"video/mp4\": { extension: \".mp4\", preview: \"video\" },\n  \"video/mpeg\": { extension: \".mpeg\" },\n  \"video/ogg\": { extension: \".ogv\" },\n  \"video/webm\": { extension: \".webm\", preview: \"video\" },\n  \"video/x-msvideo\": { extension: \".avi\" }\n}, i = Object.entries(n).filter(([, e]) => e == null ? void 0 : e.raw).map(([e]) => e);\nexport {\n  n as mediaTypes,\n  i as textMediaTypes\n};\n"], "mappings": ";AAAA,IAAM,IAAI;AAAA,EACR,wBAAwB,EAAE,WAAW,QAAQ;AAAA,EAC7C,oBAAoB,EAAE,WAAW,MAAM;AAAA,EACvC,4BAA4B,EAAE,WAAW,OAAO;AAAA,EAChD,0BAA0B,EAAE,WAAW,OAAO,KAAK,KAAG;AAAA,EACtD,oBAAoB,EAAE,WAAW,SAAS,KAAK,MAAI,UAAU,OAAO;AAAA,EACpE,uBAAuB,EAAE,WAAW,WAAW,KAAK,MAAI,UAAU,OAAO;AAAA,EACzE,4BAA4B;AAAA,IAC1B,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,sBAAsB,EAAE,WAAW,OAAO;AAAA,EAC1C,4BAA4B,EAAE,WAAW,OAAO;AAAA,EAChD,mBAAmB,EAAE,WAAW,OAAO;AAAA,EACvC,mBAAmB,EAAE,WAAW,QAAQ,SAAS,SAAS;AAAA,EAC1D,mBAAmB,EAAE,WAAW,QAAQ,KAAK,KAAG;AAAA,EAChD,gCAAgC,EAAE,WAAW,OAAO;AAAA,EACpD,uCAAuC;AAAA,IACrC,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,mCAAmC;AAAA,IACjC,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,4BAA4B,EAAE,WAAW,OAAO;AAAA,EAChD,iCAAiC,EAAE,WAAW,OAAO;AAAA,EACrD,iCAAiC,EAAE,WAAW,OAAO;AAAA,EACrD,mDAAmD,EAAE,WAAW,OAAO;AAAA,EACvE,kDAAkD,EAAE,WAAW,OAAO;AAAA,EACtE,2CAA2C,EAAE,WAAW,OAAO;AAAA,EAC/D,6EAA6E;AAAA,IAC3E,WAAW;AAAA,EACb;AAAA,EACA,qEAAqE;AAAA,IACnE,WAAW;AAAA,EACb;AAAA,EACA,2EAA2E;AAAA,IACzE,WAAW;AAAA,EACb;AAAA,EACA,uBAAuB,EAAE,WAAW,OAAO;AAAA,EAC3C,yBAAyB,EAAE,WAAW,OAAO;AAAA,EAC7C,+BAA+B,EAAE,WAAW,MAAM;AAAA,EAClD,yBAAyB,EAAE,WAAW,OAAO;AAAA,EAC7C,sBAAsB,EAAE,WAAW,MAAM;AAAA,EACzC,uBAAuB,EAAE,WAAW,OAAO;AAAA,EAC3C,qBAAqB,EAAE,WAAW,OAAO;AAAA,EACzC,qBAAqB,EAAE,WAAW,OAAO;AAAA,EACzC,yBAAyB,EAAE,WAAW,OAAO;AAAA,EAC7C,2BAA2B,EAAE,WAAW,QAAQ,KAAK,KAAG;AAAA,EACxD,oBAAoB,EAAE,WAAW,OAAO,KAAK,KAAG;AAAA,EAChD,qBAAqB,EAAE,WAAW,OAAO;AAAA,EACzC,yBAAyB,EAAE,WAAW,UAAU,KAAK,MAAI,UAAU,OAAO;AAAA,EAC1E,mBAAmB,EAAE,WAAW,QAAQ,KAAK,MAAI,UAAU,MAAM;AAAA,EACjE,oBAAoB,EAAE,WAAW,SAAS,KAAK,MAAI,UAAU,OAAO;AAAA,EACpE,mBAAmB,EAAE,WAAW,OAAO;AAAA,EACvC,aAAa,EAAE,WAAW,OAAO;AAAA,EACjC,cAAc,EAAE,WAAW,QAAQ;AAAA,EACnC,cAAc,EAAE,WAAW,QAAQ,SAAS,QAAQ;AAAA,EACpD,aAAa,EAAE,WAAW,OAAO;AAAA,EACjC,aAAa,EAAE,WAAW,OAAO;AAAA,EACjC,cAAc,EAAE,WAAW,QAAQ;AAAA,EACnC,YAAY,EAAE,WAAW,OAAO;AAAA,EAChC,YAAY,EAAE,WAAW,OAAO;AAAA,EAChC,aAAa,EAAE,WAAW,QAAQ;AAAA,EAClC,cAAc,EAAE,WAAW,SAAS;AAAA,EACpC,cAAc,EAAE,WAAW,SAAS,SAAS,SAAS,OAAO,KAAG;AAAA,EAChE,cAAc,EAAE,WAAW,SAAS,SAAS,QAAQ;AAAA,EACrD,aAAa,EAAE,WAAW,QAAQ,SAAS,QAAQ;AAAA,EACnD,aAAa,EAAE,WAAW,QAAQ,SAAS,SAAS,OAAO,KAAG;AAAA,EAC9D,cAAc,EAAE,WAAW,QAAQ,SAAS,QAAQ;AAAA,EACpD,aAAa,EAAE,WAAW,QAAQ,SAAS,SAAS,OAAO,KAAG;AAAA,EAC9D,iBAAiB;AAAA,IACf,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,cAAc,EAAE,WAAW,QAAQ;AAAA,EACnC,4BAA4B,EAAE,WAAW,QAAQ,SAAS,QAAQ;AAAA,EAClE,cAAc,EAAE,WAAW,SAAS,SAAS,SAAS,OAAO,KAAG;AAAA,EAChE,iBAAiB,EAAE,WAAW,QAAQ,KAAK,KAAG;AAAA,EAC9C,YAAY,EAAE,WAAW,QAAQ,KAAK,MAAI,UAAU,MAAM;AAAA,EAC1D,YAAY,EAAE,WAAW,QAAQ,KAAK,KAAG;AAAA,EACzC,aAAa;AAAA,IACX,WAAW;AAAA,IACX,KAAK;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB,EAAE,WAAW,OAAO,KAAK,KAAG;AAAA,EAC/C,cAAc,EAAE,WAAW,QAAQ,KAAK,KAAG;AAAA,EAC3C,YAAY,EAAE,WAAW,QAAQ,KAAK,MAAI,UAAU,MAAM;AAAA,EAC1D,aAAa,EAAE,WAAW,SAAS,KAAK,MAAI,UAAU,OAAO;AAAA,EAC7D,cAAc,EAAE,WAAW,OAAO;AAAA,EAClC,cAAc,EAAE,WAAW,OAAO;AAAA,EAClC,eAAe,EAAE,WAAW,OAAO;AAAA,EACnC,eAAe,EAAE,WAAW,OAAO;AAAA,EACnC,cAAc,EAAE,WAAW,MAAM;AAAA,EACjC,aAAa,EAAE,WAAW,QAAQ,SAAS,QAAQ;AAAA,EACnD,cAAc,EAAE,WAAW,QAAQ;AAAA,EACnC,aAAa,EAAE,WAAW,OAAO;AAAA,EACjC,cAAc,EAAE,WAAW,SAAS,SAAS,QAAQ;AAAA,EACrD,mBAAmB,EAAE,WAAW,OAAO;AACzC;AA5GA,IA4GG,IAAI,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,KAAK,OAAO,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;", "names": []}
{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/mode-indent.js"], "sourcesContent": ["export default function indent(state, textAfter) {\n    var _a, _b;\n    const { levels, indentLevel } = state;\n    const level = !levels || levels.length === 0\n        ? indentLevel\n        : levels.at(-1) - (((_a = this.electricInput) === null || _a === void 0 ? void 0 : _a.test(textAfter)) ? 1 : 0);\n    return (level || 0) * (((_b = this.config) === null || _b === void 0 ? void 0 : _b.indentUnit) || 0);\n}\n//# sourceMappingURL=mode-indent.js.map"], "mappings": ";AAAe,SAAS,OAAO,OAAO,WAAW;AAC7C,MAAI,IAAI;AACR,QAAM,EAAE,QAAQ,YAAa,IAAG;AAChC,QAAM,QAAQ,CAAC,UAAU,OAAO,WAAW,IACrC,cACA,OAAO,GAAG,EAAE,OAAO,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,KAAK,IAAI;AACjH,UAAQ,SAAS,QAAQ,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AACtG;", "names": []}
{"version": 3, "sources": ["../../../../json-2-csv/lib/constants.js", "../../../../doc-path/lib/path.js", "../../../../deeks/lib/utils.js", "../../../../deeks/lib/types.js", "../../../../deeks/lib/deeks.js", "../../../../json-2-csv/lib/utils.js", "../../../../json-2-csv/lib/json2csv.js", "../../../../json-2-csv/lib/csv2json.js", "../../../../json-2-csv/lib/converter.js"], "sourcesContent": ["'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.excelBOM = exports.defaultCsv2JsonOptions = exports.defaultJson2CsvOptions = exports.errors = void 0;\nexports.errors = {\n    optionsRequired: 'Options were not passed and are required.',\n    json2csv: {\n        cannotCallOn: 'Cannot call json2csv on',\n        dataCheckFailure: 'Data provided was not an array of documents.',\n        notSameSchema: 'Not all documents have the same schema.'\n    },\n    csv2json: {\n        cannotCallOn: 'Cannot call csv2json on',\n        dataCheckFailure: 'CSV is not a string.'\n    }\n};\nexports.defaultJson2CsvOptions = {\n    arrayIndexesAsKeys: false,\n    checkSchemaDifferences: false,\n    delimiter: {\n        field: ',',\n        wrap: '\"',\n        eol: '\\n'\n    },\n    emptyFieldValue: undefined,\n    escapeHeaderNestedDots: true,\n    excelBOM: false,\n    excludeKeys: [],\n    expandNestedObjects: true,\n    expandArrayObjects: false,\n    prependHeader: true,\n    preventCsvInjection: false,\n    sortHeader: false,\n    trimFieldValues: false,\n    trimHeaderFields: false,\n    unwindArrays: false,\n    useDateIso8601Format: false,\n    useLocaleFormat: false,\n    wrapBooleans: false,\n};\nexports.defaultCsv2JsonOptions = {\n    delimiter: {\n        field: ',',\n        wrap: '\"',\n        eol: '\\n'\n    },\n    excelBOM: false,\n    preventCsvInjection: false,\n    trimFieldValues: false,\n    trimHeaderFields: false,\n};\nexports.excelBOM = '\\ufeff';\n", "/**\n * @license MIT\n * doc-path <https://github.com/mrodrig/doc-path>\n * Copyright (c) 2015-present, <PERSON>.\n */\n'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setPath = exports.evaluatePath = void 0;\n/**\n * Main function that evaluates the path in a particular object\n * @throws {Error} possible error if call stack size is exceeded\n */\nfunction evaluatePath(obj, kp) {\n    if (!obj) {\n        return null;\n    }\n    const { dotIndex, key, remaining } = state(kp);\n    const kpVal = typeof obj === 'object' && kp in obj ? obj[kp] : undefined;\n    const keyVal = typeof obj === 'object' && key in obj ? obj[key] : undefined;\n    if (dotIndex >= 0 && typeof obj === 'object' && !(kp in obj)) {\n        const { key: nextKey } = state(remaining);\n        const nextKeyAsInt = parseInt(nextKey);\n        // If there's an array at the current key in the object, then iterate over those items evaluating the remaining path\n        if (Array.isArray(keyVal) && isNaN(nextKeyAsInt)) {\n            return keyVal.map((doc) => evaluatePath(doc, remaining));\n        }\n        // Otherwise, we can just recur\n        return evaluatePath(keyVal, remaining);\n    }\n    else if (Array.isArray(obj)) {\n        const keyAsInt = parseInt(key);\n        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {\n            return keyVal;\n        }\n        // If this object is actually an array, then iterate over those items evaluating the path\n        return obj.map((doc) => evaluatePath(doc, kp));\n    }\n    else if (dotIndex >= 0 && kp !== key && typeof obj === 'object' && key in obj) {\n        // If there's a field with a non-nested dot, then recur into that sub-value\n        return evaluatePath(keyVal, remaining);\n    }\n    else if (dotIndex === -1 && typeof obj === 'object' && key in obj && !(kp in obj)) {\n        // If the field is here, but the key was escaped\n        return keyVal;\n    }\n    // Otherwise, we can just return value directly\n    return kpVal;\n}\nexports.evaluatePath = evaluatePath;\n/**\n * Main function that performs validation before passing off to _sp\n * @throws {Error} possible error if call stack size is exceeded\n */\nfunction setPath(obj, kp, v) {\n    if (!obj) {\n        throw new Error('No object was provided.');\n    }\n    else if (!kp) {\n        throw new Error('No keyPath was provided.');\n    }\n    return _sp(obj, kp, v);\n}\nexports.setPath = setPath;\n// Helper function that will set the value in the provided object/array.\nfunction _sp(obj, kp, v) {\n    const { dotIndex, key, remaining } = state(kp);\n    // If this is clearly a prototype pollution attempt, then refuse to modify the path\n    if (kp.startsWith('__proto__') || kp.startsWith('constructor') || kp.startsWith('prototype')) {\n        return obj;\n    }\n    if (dotIndex >= 0) {\n        const keyAsInt = parseInt(key);\n        // If there is a '.' in the key path, recur on the subdoc and ...\n        if (typeof obj === 'object' && obj !== null && !(key in obj) && Array.isArray(obj) && !isNaN(keyAsInt)) {\n            // If there's no value at obj[key] then populate an empty object\n            obj[key] = obj[key] ?? {};\n            // Continue iterating on the rest of the key path to set the appropriate value where intended and then return\n            _sp(obj[key], remaining, v);\n            return obj;\n        }\n        else if (typeof obj === 'object' && obj !== null && !(key in obj) && Array.isArray(obj)) {\n            // If this is an array and there are multiple levels of keys to iterate over, recur.\n            obj.forEach((doc) => _sp(doc, kp, v));\n            return obj;\n        }\n        else if (typeof obj === 'object' && obj !== null && !(key in obj) && !Array.isArray(obj)) {\n            const { key: nextKey } = state(remaining);\n            const nextKeyAsInt = parseInt(nextKey);\n            if (!isNaN(nextKeyAsInt)) {\n                // If the current key doesn't exist yet and the next key is a number (likely array index), populate an empty array\n                obj[key] = [];\n            }\n            else if (remaining === '') {\n                // If the remaining key is empty, then a `.` character appeared right at the end of the path and wasn't actually indicating a separate level\n                obj[kp] = v;\n                return obj;\n            }\n            else {\n                // If the current key doesn't exist yet, populate it\n                obj[key] = {};\n            }\n        }\n        _sp(obj[key], remaining, v);\n    }\n    else if (Array.isArray(obj)) {\n        const keyAsInt = parseInt(key);\n        // If the object is an array and this key is an int (likely array index), then set the value directly and return\n        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {\n            obj[key] = v;\n            return obj;\n        }\n        // If this \"obj\" is actually an array, then we can loop over each of the values and set the path\n        obj.forEach((doc) => _sp(doc, remaining, v));\n        return obj;\n    }\n    else {\n        // Otherwise, we can set the path directly\n        obj[key] = v;\n    }\n    return obj;\n}\n// Helper function that returns some information necessary to evaluate or set a path  based on the provided keyPath value\nfunction state(kp) {\n    const dotIndex = findFirstNonEscapedDotIndex(kp);\n    return {\n        dotIndex,\n        key: kp.slice(0, dotIndex >= 0 ? dotIndex : undefined).replace(/\\\\./g, '.'),\n        remaining: kp.slice(dotIndex + 1)\n    };\n}\nfunction findFirstNonEscapedDotIndex(kp) {\n    for (let i = 0; i < kp.length; i++) {\n        const previousChar = i > 0 ? kp[i - 1] : '', currentChar = kp[i];\n        if (currentChar === '.' && previousChar !== '\\\\')\n            return i;\n    }\n    return -1;\n}\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isDocumentToRecurOn = exports.flatten = exports.unique = void 0;\nfunction unique(array) {\n    return [...new Set(array)];\n}\nexports.unique = unique;\nfunction flatten(array) {\n    return [].concat(...array);\n}\nexports.flatten = flatten;\n/**\n * Returns whether this value is a document to recur on or not\n * @param val Any item whose type will be evaluated\n * @returns {boolean}\n */\nfunction isDocumentToRecurOn(val) {\n    return typeof val === 'object' && val !== null && !Array.isArray(val) && Object.keys(val).length;\n}\nexports.isDocumentToRecurOn = isDocumentToRecurOn;\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deepKeysFromList = exports.deepKeys = void 0;\nconst utils = __importStar(require(\"./utils\"));\n__exportStar(require(\"./types\"), exports);\n/**\n * Return the deep keys list for a single document\n * @param object\n * @param options\n * @returns {Array}\n */\nfunction deepKeys(object, options) {\n    const parsedOptions = mergeOptions(options);\n    if (typeof object === 'object' && object !== null) {\n        return generateDeepKeysList('', object, parsedOptions);\n    }\n    return [];\n}\nexports.deepKeys = deepKeys;\n/**\n * Return the deep keys list for all documents in the provided list\n * @param list\n * @param options\n * @returns Array[Array[String]]\n */\nfunction deepKeysFromList(list, options) {\n    const parsedOptions = mergeOptions(options);\n    return list.map((document) => {\n        if (typeof document === 'object' && document !== null) {\n            // if the data at the key is a document, then we retrieve the subHeading starting with an empty string heading and the doc\n            return deepKeys(document, parsedOptions);\n        }\n        return [];\n    });\n}\nexports.deepKeysFromList = deepKeysFromList;\nfunction generateDeepKeysList(heading, data, options) {\n    const keys = Object.keys(data).map((currentKey) => {\n        // If the given heading is empty, then we set the heading to be the subKey, otherwise set it as a nested heading w/ a dot\n        const keyName = buildKeyName(heading, escapeNestedDotsIfSpecified(currentKey, options));\n        // If we have another nested document, recur on the sub-document to retrieve the full key name\n        if (options.expandNestedObjects && utils.isDocumentToRecurOn(data[currentKey]) || (options.arrayIndexesAsKeys && Array.isArray(data[currentKey]) && data[currentKey].length)) {\n            return generateDeepKeysList(keyName, data[currentKey], options);\n        }\n        else if (options.expandArrayObjects && Array.isArray(data[currentKey])) {\n            // If we have a nested array that we need to recur on\n            return processArrayKeys(data[currentKey], keyName, options);\n        }\n        else if (options.ignoreEmptyArrays && Array.isArray(data[currentKey]) && !data[currentKey].length) {\n            return [];\n        }\n        // Otherwise return this key name since we don't have a sub document\n        return keyName;\n    });\n    return utils.flatten(keys);\n}\n/**\n * Helper function to handle the processing of arrays when the expandArrayObjects\n * option is specified.\n * @param subArray\n * @param currentKeyPath\n * @param options\n * @returns {*}\n */\nfunction processArrayKeys(subArray, currentKeyPath, options) {\n    let subArrayKeys = deepKeysFromList(subArray, options);\n    if (!subArray.length) {\n        return options.ignoreEmptyArraysWhenExpanding ? [] : [currentKeyPath];\n    }\n    else if (subArray.length && utils.flatten(subArrayKeys).length === 0) {\n        // Has items in the array, but no objects\n        return [currentKeyPath];\n    }\n    else {\n        subArrayKeys = subArrayKeys.map((schemaKeys) => {\n            if (Array.isArray(schemaKeys) && schemaKeys.length === 0) {\n                return [currentKeyPath];\n            }\n            return schemaKeys.map((subKey) => buildKeyName(currentKeyPath, escapeNestedDotsIfSpecified(subKey, options)));\n        });\n        return utils.unique(utils.flatten(subArrayKeys));\n    }\n}\nfunction escapeNestedDotsIfSpecified(key, options) {\n    if (options.escapeNestedDots) {\n        return key.replace(/\\./g, '\\\\.');\n    }\n    return key;\n}\n/**\n * Function used to generate the key path\n * @param upperKeyName String accumulated key path\n * @param currentKeyName String current key name\n * @returns String\n */\nfunction buildKeyName(upperKeyName, currentKeyName) {\n    if (upperKeyName) {\n        return upperKeyName + '.' + currentKeyName;\n    }\n    return currentKeyName;\n}\nfunction mergeOptions(options) {\n    return {\n        arrayIndexesAsKeys: false,\n        expandNestedObjects: true,\n        expandArrayObjects: false,\n        ignoreEmptyArraysWhenExpanding: false,\n        escapeNestedDots: false,\n        ignoreEmptyArrays: false,\n        ...(options ?? {})\n    };\n}\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isInvalid = exports.flatten = exports.unique = exports.arrayDifference = exports.isError = exports.isUndefined = exports.isNull = exports.isObject = exports.isString = exports.isNumber = exports.unwind = exports.getNCharacters = exports.removeEmptyFields = exports.isEmptyField = exports.computeSchemaDifferences = exports.isDateRepresentation = exports.isStringRepresentation = exports.deepCopy = exports.validate = exports.buildC2JOptions = exports.buildJ2COptions = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst constants_1 = require(\"./constants\");\nconst dateStringRegex = /\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}Z/, MAX_ARRAY_LENGTH = 100000;\n/**\n * Build the options to be passed to the appropriate function\n * If a user does not provide custom options, then we use our default\n * If options are provided, then we set each valid key that was passed\n */\nfunction buildJ2COptions(opts) {\n    return {\n        ...constants_1.defaultJson2CsvOptions,\n        ...opts,\n        delimiter: {\n            field: opts?.delimiter?.field ?? constants_1.defaultJson2CsvOptions.delimiter.field,\n            wrap: opts?.delimiter?.wrap || constants_1.defaultJson2CsvOptions.delimiter.wrap,\n            eol: opts?.delimiter?.eol || constants_1.defaultJson2CsvOptions.delimiter.eol,\n        },\n        fieldTitleMap: Object.create({}),\n    };\n}\nexports.buildJ2COptions = buildJ2COptions;\n/**\n * Build the options to be passed to the appropriate function\n * If a user does not provide custom options, then we use our default\n * If options are provided, then we set each valid key that was passed\n */\nfunction buildC2JOptions(opts) {\n    return {\n        ...constants_1.defaultCsv2JsonOptions,\n        ...opts,\n        delimiter: {\n            field: opts?.delimiter?.field ?? constants_1.defaultCsv2JsonOptions.delimiter.field,\n            wrap: opts?.delimiter?.wrap || constants_1.defaultCsv2JsonOptions.delimiter.wrap,\n            eol: opts?.delimiter?.eol || constants_1.defaultCsv2JsonOptions.delimiter.eol,\n        },\n    };\n}\nexports.buildC2JOptions = buildC2JOptions;\nfunction validate(data, validationFn, errorMessages) {\n    if (!data)\n        throw new Error(`${errorMessages.cannotCallOn} ${data}.`);\n    if (!validationFn(data))\n        throw new Error(errorMessages.dataCheckFailure);\n    return true;\n}\nexports.validate = validate;\n/**\n * Utility function to deep copy an object, used by the module tests\n */\nfunction deepCopy(obj) {\n    return JSON.parse(JSON.stringify(obj));\n}\nexports.deepCopy = deepCopy;\n/**\n * Helper function that determines whether the provided value is a representation\n *   of a string. Given the RFC4180 requirements, that means that the value is\n *   wrapped in value wrap delimiters (usually a quotation mark on each side).\n */\nfunction isStringRepresentation(fieldValue, options) {\n    const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];\n    // If the field starts and ends with a wrap delimiter\n    return firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap;\n}\nexports.isStringRepresentation = isStringRepresentation;\n/**\n * Helper function that determines whether the provided value is a representation\n *   of a date.\n */\nfunction isDateRepresentation(fieldValue) {\n    return dateStringRegex.test(fieldValue);\n}\nexports.isDateRepresentation = isDateRepresentation;\n/**\n * Helper function that determines the schema differences between two objects.\n */\nfunction computeSchemaDifferences(schemaA, schemaB) {\n    return arrayDifference(schemaA, schemaB)\n        .concat(arrayDifference(schemaB, schemaA));\n}\nexports.computeSchemaDifferences = computeSchemaDifferences;\n/**\n * Utility function to check if a field is considered empty so that the emptyFieldValue can be used instead\n */\nfunction isEmptyField(fieldValue) {\n    return isUndefined(fieldValue) || isNull(fieldValue) || fieldValue === '';\n}\nexports.isEmptyField = isEmptyField;\n/**\n * Helper function that removes empty field values from an array.\n */\nfunction removeEmptyFields(fields) {\n    return fields.filter((field) => !isEmptyField(field));\n}\nexports.removeEmptyFields = removeEmptyFields;\n/**\n * Helper function that retrieves the next n characters from the start index in\n *   the string including the character at the start index. This is used to\n *   check if are currently at an EOL value, since it could be multiple\n *   characters in length (eg. '\\r\\n')\n */\nfunction getNCharacters(str, start, n) {\n    return str.substring(start, start + n);\n}\nexports.getNCharacters = getNCharacters;\n/**\n * The following unwind functionality is a heavily modified version of @edwincen's\n * unwind extension for lodash. Since lodash is a large package to require in,\n * and all of the required functionality was already being imported, either\n * natively or with doc-path, I decided to rewrite the majority of the logic\n * so that an additional dependency would not be required. The original code\n * with the lodash dependency can be found here:\n *\n * https://github.com/edwincen/unwind/blob/master/index.js\n */\n/**\n * Core function that unwinds an item at the provided path\n */\nfunction unwindItem(accumulator, item, fieldPath) {\n    const valueToUnwind = (0, doc_path_1.evaluatePath)(item, fieldPath);\n    let cloned = deepCopy(item);\n    if (Array.isArray(valueToUnwind) && valueToUnwind.length) {\n        valueToUnwind.forEach((val) => {\n            cloned = deepCopy(item);\n            accumulator.push((0, doc_path_1.setPath)(cloned, fieldPath, val));\n        });\n    }\n    else if (Array.isArray(valueToUnwind) && valueToUnwind.length === 0) {\n        // Push an empty string so the value is empty since there are no values\n        (0, doc_path_1.setPath)(cloned, fieldPath, '');\n        accumulator.push(cloned);\n    }\n    else {\n        accumulator.push(cloned);\n    }\n}\n/**\n * Main unwind function which takes an array and a field to unwind.\n */\nfunction unwind(array, field) {\n    const result = [];\n    array.forEach((item) => {\n        unwindItem(result, item, field);\n    });\n    return result;\n}\nexports.unwind = unwind;\n/**\n * Checks whether value can be converted to a number\n */\nfunction isNumber(value) {\n    return !isNaN(Number(value));\n}\nexports.isNumber = isNumber;\n/*\n * Helper functions which were created to remove underscorejs from this package.\n */\nfunction isString(value) {\n    return typeof value === 'string';\n}\nexports.isString = isString;\nfunction isObject(value) {\n    return typeof value === 'object';\n}\nexports.isObject = isObject;\nfunction isNull(value) {\n    return value === null;\n}\nexports.isNull = isNull;\nfunction isUndefined(value) {\n    return typeof value === 'undefined';\n}\nexports.isUndefined = isUndefined;\nfunction isError(value) {\n    // TODO(mrodrig): test this possible change\n    // return value instanceof Error;\n    return Object.prototype.toString.call(value) === '[object Error]';\n}\nexports.isError = isError;\nfunction arrayDifference(a, b) {\n    return a.filter((x) => !b.includes(x));\n}\nexports.arrayDifference = arrayDifference;\nfunction unique(array) {\n    return [...new Set(array)];\n}\nexports.unique = unique;\nfunction flatten(array) {\n    // Node 11+ - use the native array flattening function\n    if (array.flat) {\n        return array.flat();\n    }\n    // #167 - allow browsers to flatten very long 200k+ element arrays\n    if (array.length > MAX_ARRAY_LENGTH) {\n        let safeArray = [];\n        for (let a = 0; a < array.length; a += MAX_ARRAY_LENGTH) {\n            safeArray = safeArray.concat(...array.slice(a, a + MAX_ARRAY_LENGTH));\n        }\n        return safeArray;\n    }\n    return array.reduce((accumulator, value) => accumulator.concat(value), []);\n}\nexports.flatten = flatten;\n/**\n * Used to help avoid incorrect values returned by JSON.parse when converting\n * CSV back to JSON, such as '39e1804' which JSON.parse converts to Infinity\n */\nfunction isInvalid(parsedJson) {\n    return parsedJson === Infinity ||\n        parsedJson === -Infinity;\n}\nexports.isInvalid = isInvalid;\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Json2Csv = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst deeks_1 = require(\"deeks\");\nconst constants_1 = require(\"./constants\");\nconst utils = __importStar(require(\"./utils\"));\nconst Json2Csv = function (options) {\n    const wrapDelimiterCheckRegex = new RegExp(options.delimiter.wrap, 'g'), crlfSearchRegex = /\\r?\\n|\\r/, customValueParser = options.parseValue && typeof options.parseValue === 'function' ? options.parseValue : null, expandingWithoutUnwinding = options.expandArrayObjects && !options.unwindArrays, deeksOptions = {\n        arrayIndexesAsKeys: options.arrayIndexesAsKeys,\n        expandNestedObjects: options.expandNestedObjects,\n        expandArrayObjects: expandingWithoutUnwinding,\n        ignoreEmptyArraysWhenExpanding: expandingWithoutUnwinding,\n        escapeNestedDots: true,\n    };\n    /** HEADER FIELD FUNCTIONS **/\n    /**\n     * Returns the list of data field names of all documents in the provided list\n     */\n    function getFieldNameList(data) {\n        // If keys weren't specified, then we'll use the list of keys generated by the deeks module\n        return (0, deeks_1.deepKeysFromList)(data, deeksOptions);\n    }\n    /**\n     * Processes the schemas by checking for schema differences, if so desired.\n     * If schema differences are not to be checked, then it resolves the unique\n     * list of field names.\n     */\n    function processSchemas(documentSchemas) {\n        // If the user wants to check for the same schema (regardless of schema ordering)\n        if (options.checkSchemaDifferences) {\n            return checkSchemaDifferences(documentSchemas);\n        }\n        else {\n            // Otherwise, we do not care if the schemas are different, so we should get the unique list of keys\n            const uniqueFieldNames = utils.unique(utils.flatten(documentSchemas));\n            return uniqueFieldNames;\n        }\n    }\n    /**\n     * This function performs the schema difference check, if the user specifies that it should be checked.\n     * If there are no field names, then there are no differences.\n     * Otherwise, we get the first schema and the remaining list of schemas\n     */\n    function checkSchemaDifferences(documentSchemas) {\n        // have multiple documents - ensure only one schema (regardless of field ordering)\n        const firstDocSchema = documentSchemas[0], restOfDocumentSchemas = documentSchemas.slice(1), schemaDifferences = computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas);\n        // If there are schema inconsistencies, throw a schema not the same error\n        if (schemaDifferences) {\n            throw new Error(constants_1.errors.json2csv.notSameSchema);\n        }\n        return firstDocSchema;\n    }\n    /**\n     * Computes the number of schema differences\n     */\n    function computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas) {\n        return restOfDocumentSchemas.reduce((schemaDifferences, documentSchema) => {\n            // If there is a difference between the schemas, increment the counter of schema inconsistencies\n            const numberOfDifferences = utils.computeSchemaDifferences(firstDocSchema, documentSchema).length;\n            return numberOfDifferences > 0\n                ? schemaDifferences + 1\n                : schemaDifferences;\n        }, 0);\n    }\n    /**\n     * If so specified, this filters the detected key paths to exclude any keys that have been specified\n     */\n    function filterExcludedKeys(keyPaths) {\n        if (options.excludeKeys) {\n            return keyPaths.filter((keyPath) => {\n                for (const excludedKey of options.excludeKeys) {\n                    // Only match if the excludedKey appears at the beginning of the string so we don't accidentally match a key farther down in a key path\n                    const regex = excludedKey instanceof RegExp ? excludedKey : new RegExp(`^${excludedKey}`);\n                    if (excludedKey === keyPath || keyPath.match(regex)) {\n                        return false; // Exclude the key\n                    }\n                }\n                return true; // Otherwise, include the key\n            });\n        }\n        return keyPaths;\n    }\n    /**\n     * If so specified, this sorts the header field names alphabetically\n     */\n    function sortHeaderFields(fieldNames) {\n        if (options.sortHeader && typeof options.sortHeader === 'function') {\n            return fieldNames.sort(options.sortHeader);\n        }\n        else if (options.sortHeader) {\n            return fieldNames.sort();\n        }\n        return fieldNames;\n    }\n    /**\n     * Trims the header fields, if the user desires them to be trimmed.\n     */\n    function trimHeaderFields(params) {\n        if (options.trimHeaderFields) {\n            params.headerFields = params.headerFields.map((field) => field.split('.')\n                .map((component) => component.trim())\n                .join('.'));\n        }\n        return params;\n    }\n    /**\n     * Wrap the headings, if desired by the user.\n     */\n    function wrapHeaderFields(params) {\n        // only perform this if we are actually prepending the header\n        if (options.prependHeader) {\n            params.headerFields = params.headerFields.map(function (headingKey) {\n                return wrapFieldValueIfNecessary(headingKey);\n            });\n        }\n        return params;\n    }\n    /**\n     * Generates the CSV header string by joining the headerFields by the field delimiter\n     */\n    function generateCsvHeader(params) {\n        // #185 - generate a keys list to avoid finding native Map() methods\n        const fieldTitleMapKeys = Object.keys(options.fieldTitleMap);\n        params.header = params.headerFields\n            .map(function (field) {\n            let headerKey = field;\n            // If a custom field title was provided for this field, use that\n            if (fieldTitleMapKeys.includes(field)) {\n                headerKey = options.fieldTitleMap[field];\n            }\n            else if (!options.escapeHeaderNestedDots) {\n                // Otherwise, if the user doesn't want nested dots in keys to be escaped, then unescape them\n                headerKey = headerKey.replace(/\\\\\\./g, '.');\n            }\n            return headerKey;\n        })\n            .join(options.delimiter.field);\n        return params;\n    }\n    function convertKeysToHeaderFields() {\n        if (!options.keys)\n            return [];\n        return options.keys.map((key) => {\n            if (typeof key === 'object' && 'field' in key) {\n                options.fieldTitleMap[key.field] = key.title ?? key.field;\n                return key.field;\n            }\n            return key;\n        });\n    }\n    function extractWildcardMatchKeys() {\n        if (!options.keys)\n            return [];\n        return options.keys.flatMap(item => {\n            if (typeof item === 'string') {\n                // Exclude plain strings that were passed in options.keys\n                return [];\n            }\n            else if (item?.wildcardMatch) {\n                // Return \"field\" value for objects with wildcardMatch: true\n                return item.field;\n            }\n            // Exclude other objects\n            return [];\n        });\n    }\n    /**\n     * Retrieve the headings for all documents and return it.\n     * This checks that all documents have the same schema.\n     */\n    function retrieveHeaderFields(data) {\n        const wildcardMatchKeys = extractWildcardMatchKeys();\n        const keyStrings = convertKeysToHeaderFields();\n        const fieldNames = getFieldNameList(data);\n        const processed = processSchemas(fieldNames);\n        if (options.keys) {\n            options.keys = keyStrings;\n            const matchedKeys = keyStrings.flatMap((userProvidedKey) => {\n                // If this is not a wildcard matched key, then just return and include it in the resulting key list\n                if (!wildcardMatchKeys.includes(userProvidedKey)) {\n                    return userProvidedKey;\n                }\n                // Otherwise, identify all detected keys that match with the provided wildcard key:\n                const matches = [];\n                const regex = new RegExp(`^${userProvidedKey}`);\n                for (const detectedKey of processed) {\n                    if (userProvidedKey === detectedKey || detectedKey.match(regex)) {\n                        matches.push(detectedKey);\n                    }\n                }\n                return matches;\n            });\n            if (!options.unwindArrays) {\n                const filtered = filterExcludedKeys(matchedKeys);\n                return sortHeaderFields(filtered);\n            }\n        }\n        const filtered = filterExcludedKeys(processed);\n        return sortHeaderFields(filtered);\n    }\n    /** RECORD FIELD FUNCTIONS **/\n    /**\n     * Unwinds objects in arrays within record objects if the user specifies the\n     * expandArrayObjects option. If not specified, this passes the params\n     * argument through to the next function in the promise chain.\n     *\n     * The `finalPass` parameter is used to trigger one last pass to ensure no more\n     * arrays need to be expanded\n     */\n    function unwindRecordsIfNecessary(params, finalPass = false) {\n        if (options.unwindArrays) {\n            const originalRecordsLength = params.records.length;\n            // Unwind each of the documents at the given headerField\n            params.headerFields.forEach((headerField) => {\n                params.records = utils.unwind(params.records, headerField);\n            });\n            const headerFields = retrieveHeaderFields(params.records);\n            params.headerFields = headerFields;\n            // If we were able to unwind more arrays, then try unwinding again...\n            if (originalRecordsLength !== params.records.length) {\n                return unwindRecordsIfNecessary(params);\n            }\n            // Otherwise, we didn't unwind any additional arrays, so continue...\n            // Run a final time in case the earlier unwinding exposed additional\n            // arrays to unwind...\n            if (!finalPass) {\n                return unwindRecordsIfNecessary(params, true);\n            }\n            // If keys were provided, set the headerFields back to the provided keys after unwinding:\n            if (options.keys) {\n                const userSelectedFields = convertKeysToHeaderFields();\n                params.headerFields = filterExcludedKeys(userSelectedFields);\n            }\n            return params;\n        }\n        return params;\n    }\n    /**\n     * Main function which handles the processing of a record, or document to be converted to CSV format\n     * This function specifies and performs the necessary operations in the necessary order\n     * in order to obtain the data and convert it to CSV form while maintaining RFC 4180 compliance.\n     * * Order of operations:\n     * - Get fields from provided key list (as array of actual values)\n     * - Convert the values to csv/string representation [possible option here for custom converters?]\n     * - Trim fields\n     * - Determine if they need to be wrapped (& wrap if necessary)\n     * - Combine values for each line (by joining by field delimiter)\n     */\n    function processRecords(params) {\n        params.recordString = params.records.map((record) => {\n            // Retrieve data for each of the headerFields from this record\n            const recordFieldData = retrieveRecordFieldData(record, params.headerFields), \n            // Process the data in this record and return the\n            processedRecordData = recordFieldData.map((fieldValue) => {\n                fieldValue = trimRecordFieldValue(fieldValue);\n                fieldValue = preventCsvInjection(fieldValue);\n                let stringified = customValueParser ? customValueParser(fieldValue, recordFieldValueToString) : recordFieldValueToString(fieldValue);\n                stringified = wrapFieldValueIfNecessary(stringified);\n                return stringified;\n            });\n            // Join the record data by the field delimiter\n            return generateCsvRowFromRecord(processedRecordData);\n        }).join(options.delimiter.eol);\n        return params;\n    }\n    /**\n     * Helper function intended to process *just* array values when the expandArrayObjects setting is set to true\n     */\n    function processRecordFieldDataForExpandedArrayObject(recordFieldValue) {\n        const filteredRecordFieldValue = utils.removeEmptyFields(recordFieldValue);\n        // If we have an array and it's either empty of full of empty values, then use an empty value representation\n        if (!recordFieldValue.length || !filteredRecordFieldValue.length) {\n            return options.emptyFieldValue || '';\n        }\n        else if (filteredRecordFieldValue.length === 1) {\n            // Otherwise, we have an array of actual values...\n            // Since we are expanding array objects, we will want to key in on values of objects.\n            return filteredRecordFieldValue[0]; // Extract the single value in the array\n        }\n        return recordFieldValue;\n    }\n    /**\n     * Gets all field values from a particular record for the given list of fields\n     */\n    function retrieveRecordFieldData(record, fields) {\n        const recordValues = [];\n        fields.forEach((field) => {\n            let recordFieldValue = (0, doc_path_1.evaluatePath)(record, field);\n            if (!utils.isUndefined(options.emptyFieldValue) && utils.isEmptyField(recordFieldValue)) {\n                recordFieldValue = options.emptyFieldValue;\n            }\n            else if (options.expandArrayObjects && Array.isArray(recordFieldValue)) {\n                recordFieldValue = processRecordFieldDataForExpandedArrayObject(recordFieldValue);\n            }\n            recordValues.push(recordFieldValue);\n        });\n        return recordValues;\n    }\n    /**\n     * Converts a record field value to its string representation\n     */\n    function recordFieldValueToString(fieldValue) {\n        const isDate = fieldValue instanceof Date; // store to avoid checking twice\n        if (fieldValue === null || Array.isArray(fieldValue) || typeof fieldValue === 'object' && !isDate) {\n            return JSON.stringify(fieldValue);\n        }\n        else if (typeof fieldValue === 'undefined') {\n            return 'undefined';\n        }\n        else if (isDate && options.useDateIso8601Format) {\n            return fieldValue.toISOString();\n        }\n        else {\n            return !options.useLocaleFormat ? fieldValue.toString() : fieldValue.toLocaleString();\n        }\n    }\n    /**\n     * Trims the record field value, if specified by the user's provided options\n     */\n    function trimRecordFieldValue(fieldValue) {\n        if (options.trimFieldValues) {\n            if (Array.isArray(fieldValue)) {\n                return fieldValue.map(trimRecordFieldValue);\n            }\n            else if (typeof fieldValue === 'string') {\n                return fieldValue.trim();\n            }\n            return fieldValue;\n        }\n        return fieldValue;\n    }\n    /**\n     * Prevent CSV injection on strings if specified by the user's provided options.\n     * Mitigation will be done by ensuring that the first character doesn't being with:\n     * Equals (=), Plus (+), Minus (-), At (@), Tab (0x09), Carriage return (0x0D).\n     * More info: https://owasp.org/www-community/attacks/CSV_Injection\n     */\n    function preventCsvInjection(fieldValue) {\n        if (options.preventCsvInjection) {\n            if (Array.isArray(fieldValue)) {\n                return fieldValue.map(preventCsvInjection);\n            }\n            else if (typeof fieldValue === 'string' && !utils.isNumber(fieldValue)) {\n                return fieldValue.replace(/^[=+\\-@\\t\\r]+/g, '');\n            }\n            return fieldValue;\n        }\n        return fieldValue;\n    }\n    /**\n     * Escapes quotation marks in the field value, if necessary, and appropriately\n     * wraps the record field value if it contains a comma (field delimiter),\n     * quotation mark (wrap delimiter), or a line break (CRLF)\n     */\n    function wrapFieldValueIfNecessary(fieldValue) {\n        const wrapDelimiter = options.delimiter.wrap;\n        // eg. includes quotation marks (default delimiter)\n        if (fieldValue.includes(options.delimiter.wrap)) {\n            // add an additional quotation mark before each quotation mark appearing in the field value\n            fieldValue = fieldValue.replace(wrapDelimiterCheckRegex, wrapDelimiter + wrapDelimiter);\n        }\n        // if the field contains a comma (field delimiter), quotation mark (wrap delimiter), line break, or CRLF\n        //   then enclose it in quotation marks (wrap delimiter)\n        if (fieldValue.includes(options.delimiter.field) ||\n            fieldValue.includes(options.delimiter.wrap) ||\n            fieldValue.match(crlfSearchRegex) ||\n            options.wrapBooleans && (fieldValue === 'true' || fieldValue === 'false')) {\n            // wrap the field's value in a wrap delimiter (quotation marks by default)\n            fieldValue = wrapDelimiter + fieldValue + wrapDelimiter;\n        }\n        return fieldValue;\n    }\n    /**\n     * Generates the CSV record string by joining the field values together by the field delimiter\n     */\n    function generateCsvRowFromRecord(recordFieldValues) {\n        return recordFieldValues.join(options.delimiter.field);\n    }\n    /** CSV COMPONENT COMBINER/FINAL PROCESSOR **/\n    /**\n     * Performs the final CSV construction by combining the fields in the appropriate\n     * order depending on the provided options values and sends the generated CSV\n     * back to the user\n     */\n    function generateCsvFromComponents(params) {\n        const header = params.header, records = params.recordString, \n        // If we are prepending the header, then add an EOL, otherwise just return the records\n        csv = (options.excelBOM ? constants_1.excelBOM : '') +\n            (options.prependHeader ? header + options.delimiter.eol : '') +\n            records;\n        return csv;\n    }\n    /** MAIN CONVERTER FUNCTION **/\n    /**\n     * Internally exported json2csv function\n     */\n    function convert(data) {\n        // Single document, not an array\n        if (utils.isObject(data) && !data.length) {\n            data = [data]; // Convert to an array of the given document\n        }\n        // Retrieve the heading and then generate the CSV with the keys that are identified\n        const headerFields = {\n            headerFields: retrieveHeaderFields(data),\n            records: data,\n            header: '',\n            recordString: '',\n        };\n        const unwinded = unwindRecordsIfNecessary(headerFields);\n        const processed = processRecords(unwinded);\n        const wrapped = wrapHeaderFields(processed);\n        const trimmed = trimHeaderFields(wrapped);\n        const generated = generateCsvHeader(trimmed);\n        return generateCsvFromComponents(generated);\n    }\n    return {\n        convert,\n    };\n};\nexports.Json2Csv = Json2Csv;\n", "'use strict';\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Csv2Json = void 0;\nconst doc_path_1 = require(\"doc-path\");\nconst constants_1 = require(\"./constants\");\nconst utils = __importStar(require(\"./utils\"));\nconst Csv2Json = function (options) {\n    const escapedWrapDelimiterRegex = new RegExp(options.delimiter.wrap + options.delimiter.wrap, 'g'), excelBOMRegex = new RegExp('^' + constants_1.excelBOM), valueParserFn = options.parseValue && typeof options.parseValue === 'function' ? options.parseValue : JSON.parse;\n    /**\n     * Trims the header key, if specified by the user via the provided options\n     */\n    function processHeaderKey(headerKey) {\n        headerKey = removeWrapDelimitersFromValue(headerKey);\n        if (options.trimHeaderFields) {\n            return headerKey.split('.')\n                .map((component) => component.trim())\n                .join('.');\n        }\n        return headerKey;\n    }\n    /**\n     * Generate the JSON heading from the CSV\n     */\n    function retrieveHeading(lines) {\n        let headerFields = [];\n        if (options.headerFields) {\n            headerFields = options.headerFields.map((headerField, index) => ({\n                value: processHeaderKey(headerField),\n                index\n            }));\n        }\n        else {\n            // Generate and return the heading keys\n            const headerRow = lines[0];\n            headerFields = headerRow.map((headerKey, index) => ({\n                value: processHeaderKey(headerKey),\n                index\n            }));\n            // If the user provided keys, filter the generated keys to just the user provided keys so we also have the key index\n            if (options.keys) {\n                const keys = options.keys; // TypeScript type checking work around to get it to recognize the option is not undefined\n                headerFields = headerFields.filter((headerKey) => keys.includes(headerKey.value));\n            }\n        }\n        return {\n            lines,\n            headerFields,\n            recordLines: [],\n        };\n    }\n    /**\n     * Removes the Excel BOM value, if specified by the options object\n     */\n    function stripExcelBOM(csv) {\n        if (options.excelBOM) {\n            return csv.replace(excelBOMRegex, '');\n        }\n        return csv;\n    }\n    /**\n     * Helper function that splits a line so that we can handle wrapped fields\n     */\n    function splitLines(csv) {\n        // Parse out the line...\n        const lines = [], lastCharacterIndex = csv.length - 1, eolDelimiterLength = options.delimiter.eol.length, stateVariables = {\n            insideWrapDelimiter: false,\n            parsingValue: true,\n            justParsedDoubleQuote: false,\n            startIndex: 0\n        };\n        let splitLine = [], character, charBefore, charAfter, nextNChar, index = 0;\n        // Loop through each character in the line to identify where to split the values\n        while (index < csv.length) {\n            // Current character\n            character = csv[index];\n            // Previous character\n            charBefore = index ? csv[index - 1] : '';\n            // Next character\n            charAfter = index < lastCharacterIndex ? csv[index + 1] : '';\n            // Next n characters, including the current character, where n = length(EOL delimiter)\n            // This allows for the checking of an EOL delimiter when if it is more than a single character (eg. '\\r\\n')\n            nextNChar = utils.getNCharacters(csv, index, eolDelimiterLength);\n            if ((nextNChar === options.delimiter.eol && !stateVariables.insideWrapDelimiter ||\n                index === lastCharacterIndex) && charBefore === options.delimiter.field) {\n                // If we reached an EOL delimiter or the end of the csv and the previous character is a field delimiter...\n                // If the start index is the current index (and since the previous character is a comma),\n                //   then the value being parsed is an empty value accordingly, add an empty string\n                if (nextNChar === options.delimiter.eol && stateVariables.startIndex === index) {\n                    splitLine.push('');\n                }\n                else if (character === options.delimiter.field) {\n                    // If we reached the end of the CSV, there's no new line, and the current character is a comma\n                    // then add an empty string for the current value\n                    splitLine.push('');\n                }\n                else {\n                    // Otherwise, there's a valid value, and the start index isn't the current index, grab the whole value\n                    splitLine.push(csv.substring(stateVariables.startIndex));\n                }\n                // Since the last character is a comma, there's still an additional implied field value trailing the comma.\n                //   Since this value is empty, we push an extra empty value\n                splitLine.push('');\n                // Finally, push the split line values into the lines array and clear the split line\n                lines.push(splitLine);\n                splitLine = [];\n                stateVariables.startIndex = index + eolDelimiterLength;\n                stateVariables.parsingValue = true;\n                stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;\n            }\n            else if (index === lastCharacterIndex && character === options.delimiter.field) {\n                // If we reach the end of the CSV and the current character is a field delimiter\n                // Parse the previously seen value and add it to the line\n                const parsedValue = csv.substring(stateVariables.startIndex, index);\n                splitLine.push(parsedValue);\n                // Then add an empty string to the line since the last character being a field delimiter indicates an empty field\n                splitLine.push('');\n                lines.push(splitLine);\n            }\n            else if (index === lastCharacterIndex || nextNChar === options.delimiter.eol &&\n                // if we aren't inside wrap delimiters or if we are but the character before was a wrap delimiter and we didn't just see two\n                (!stateVariables.insideWrapDelimiter ||\n                    stateVariables.insideWrapDelimiter && charBefore === options.delimiter.wrap && !stateVariables.justParsedDoubleQuote)) {\n                // Otherwise if we reached the end of the line or csv (and current character is not a field delimiter)\n                const toIndex = index !== lastCharacterIndex || charBefore === options.delimiter.wrap ? index : undefined;\n                // Retrieve the remaining value and add it to the split line list of values\n                splitLine.push(csv.substring(stateVariables.startIndex, toIndex));\n                // Finally, push the split line values into the lines array and clear the split line\n                lines.push(splitLine);\n                splitLine = [];\n                stateVariables.startIndex = index + eolDelimiterLength;\n                stateVariables.parsingValue = true;\n                stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;\n            }\n            else if (character === options.delimiter.wrap && charBefore === options.delimiter.field &&\n                !stateVariables.insideWrapDelimiter && !stateVariables.parsingValue) {\n                // If we reached a wrap delimiter after a comma and we aren't inside a wrap delimiter\n                stateVariables.startIndex = index;\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                // If the next character(s) are an EOL delimiter, then skip them so we don't parse what we've seen as another value\n                if (utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {\n                    index += options.delimiter.eol.length + 1; // Skip past EOL\n                }\n            }\n            else if ((charBefore !== options.delimiter.wrap || stateVariables.justParsedDoubleQuote && charBefore === options.delimiter.wrap) &&\n                character === options.delimiter.wrap && utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {\n                // If we reach a wrap which is not preceded by a wrap delim and the next character is an EOL delim (ie. *\"\\n)\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = false;\n                // Next iteration will substring, add the value to the line, and push the line onto the array of lines\n            }\n            else if (character === options.delimiter.wrap && (index === 0 || utils.getNCharacters(csv, index - eolDelimiterLength, eolDelimiterLength) === options.delimiter.eol && !stateVariables.insideWrapDelimiter)) {\n                // If the line starts with a wrap delimiter (ie. \"*)\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index;\n            }\n            else if (character === options.delimiter.wrap && charAfter === options.delimiter.field) {\n                // If we reached a wrap delimiter with a field delimiter after it (ie. *\",)\n                splitLine.push(csv.substring(stateVariables.startIndex, index + 1));\n                stateVariables.startIndex = index + 2; // next value starts after the field delimiter\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = false;\n            }\n            else if (character === options.delimiter.wrap && charBefore === options.delimiter.field &&\n                !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {\n                // If we reached a wrap delimiter with a field delimiter after it (ie. ,\"*)\n                splitLine.push(csv.substring(stateVariables.startIndex, index - 1));\n                stateVariables.insideWrapDelimiter = true;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index;\n            }\n            else if (character === options.delimiter.wrap && charAfter === options.delimiter.wrap && index !== stateVariables.startIndex) {\n                // If we run into an escaped quote (ie. \"\") skip past the second quote\n                index += 2;\n                stateVariables.justParsedDoubleQuote = true;\n                continue;\n            }\n            else if (character === options.delimiter.field && charBefore !== options.delimiter.wrap &&\n                charAfter !== options.delimiter.wrap && !stateVariables.insideWrapDelimiter &&\n                stateVariables.parsingValue) {\n                // If we reached a field delimiter and are not inside the wrap delimiters (ie. *,*)\n                splitLine.push(csv.substring(stateVariables.startIndex, index));\n                stateVariables.startIndex = index + 1;\n            }\n            else if (character === options.delimiter.field && charBefore === options.delimiter.wrap &&\n                charAfter !== options.delimiter.wrap && !stateVariables.parsingValue) {\n                // If we reached a field delimiter, the previous character was a wrap delimiter, and the\n                //   next character is not a wrap delimiter (ie. \",*)\n                stateVariables.insideWrapDelimiter = false;\n                stateVariables.parsingValue = true;\n                stateVariables.startIndex = index + 1;\n            }\n            // Otherwise increment to the next character\n            index++;\n            // Reset the double quote state variable\n            stateVariables.justParsedDoubleQuote = false;\n        }\n        return lines;\n    }\n    /**\n     * Retrieves the record lines from the split CSV lines and sets it on the params object\n     */\n    function retrieveRecordLines(params) {\n        if (options.headerFields) { // This option is passed for instances where the CSV has no header line\n            params.recordLines = params.lines;\n        }\n        else { // All lines except for the header line\n            params.recordLines = params.lines.splice(1);\n        }\n        return params;\n    }\n    /**\n     * Retrieves the value for the record from the line at the provided key.\n     */\n    function retrieveRecordValueFromLine(headerField, line) {\n        // If there is a value at the key's index, use it; otherwise, null\n        const value = line[headerField.index];\n        // Perform any necessary value conversions on the record value\n        return processRecordValue(value);\n    }\n    /**\n     * Processes the record's value by parsing the data to ensure the CSV is\n     * converted to the JSON that created it.\n     */\n    function processRecordValue(fieldValue) {\n        // If the value is an array representation, convert it\n        const parsedJson = parseValue(fieldValue);\n        // If parsedJson is anything aside from an error, then we want to use the parsed value\n        // This allows us to interpret values like 'null' --> null, 'false' --> false\n        if (!utils.isError(parsedJson) && !utils.isInvalid(parsedJson)) {\n            return parsedJson;\n        }\n        else if (fieldValue === 'undefined') {\n            return undefined;\n        }\n        return fieldValue;\n    }\n    /**\n     * Trims the record value, if specified by the user via the options object\n     */\n    function trimRecordValue(fieldValue) {\n        if (options.trimFieldValues && fieldValue !== null) {\n            return fieldValue.trim();\n        }\n        return fieldValue;\n    }\n    /**\n     * Create a JSON document with the given keys (designated by the CSV header)\n     *   and the values (from the given line)\n     * @returns {Object} created json document\n     */\n    function createDocument(headerFields, line) {\n        // Reduce the keys into a JSON document representing the given line\n        return headerFields.reduce((document, headerField) => {\n            // If there is a value at the key's index in the line, set the value; otherwise null\n            const value = retrieveRecordValueFromLine(headerField, line);\n            try {\n                // Otherwise add the key and value to the document\n                return (0, doc_path_1.setPath)(document, headerField.value, value);\n            }\n            catch (error) {\n                // Catch any errors where key paths are null or '' and continue\n                return document;\n            }\n        }, {});\n    }\n    /**\n     * Removes the outermost wrap delimiters from a value, if they are present\n     * Otherwise, the non-wrapped value is returned as is\n     */\n    function removeWrapDelimitersFromValue(fieldValue) {\n        const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];\n        // If the field starts and ends with a wrap delimiter\n        if (firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap) {\n            // Handle the case where the field is just a pair of wrap delimiters \n            return fieldValue.length <= 2 ? '' : fieldValue.substring(1, lastIndex);\n        }\n        return fieldValue;\n    }\n    /**\n     * Unescapes wrap delimiters by replacing duplicates with a single (eg. \"\" -> \")\n     * This is done in order to parse RFC 4180 compliant CSV back to JSON\n     */\n    function unescapeWrapDelimiterInField(fieldValue) {\n        return fieldValue.replace(escapedWrapDelimiterRegex, options.delimiter.wrap);\n    }\n    /**\n     * Main helper function to convert the CSV to the JSON document array\n     */\n    function transformRecordLines(params) {\n        // For each line, create the document and add it to the array of documents\n        return params.recordLines.reduce((generatedJsonObjects, line) => {\n            line = line.map((fieldValue) => {\n                // Perform the necessary operations on each line\n                fieldValue = removeWrapDelimitersFromValue(fieldValue);\n                fieldValue = unescapeWrapDelimiterInField(fieldValue);\n                fieldValue = trimRecordValue(fieldValue);\n                return fieldValue;\n            });\n            const generatedDocument = createDocument(params.headerFields, line);\n            return generatedJsonObjects.concat(generatedDocument);\n        }, []);\n    }\n    /**\n     * Attempts to parse the provided value. If it is not parsable, then an error is returned\n     */\n    function parseValue(value) {\n        try {\n            if (utils.isStringRepresentation(value, options) && !utils.isDateRepresentation(value)) {\n                return value;\n            }\n            const parsedJson = valueParserFn(value);\n            // If the parsed value is an array, then we also need to trim record values, if specified\n            if (Array.isArray(parsedJson)) {\n                return parsedJson.map(trimRecordValue);\n            }\n            return parsedJson;\n        }\n        catch (err) {\n            return err;\n        }\n    }\n    /**\n     * Internally exported csv2json function\n     */\n    function convert(data) {\n        // Split the CSV into lines using the specified EOL option\n        const stripped = stripExcelBOM(data);\n        const split = splitLines(stripped);\n        const heading = retrieveHeading(split); // Retrieve the headings from the CSV, unless the user specified the keys\n        const lines = retrieveRecordLines(heading); // Retrieve the record lines from the CSV\n        return transformRecordLines(lines); // Retrieve the JSON document array\n    }\n    return {\n        convert,\n    };\n};\nexports.Csv2Json = Csv2Json;\n", "'use strict';\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.csv2json = exports.json2csv = void 0;\nconst constants_1 = require(\"./constants\");\nconst json2csv_1 = require(\"./json2csv\");\nconst csv2json_1 = require(\"./csv2json\");\nconst utils_1 = require(\"./utils\");\nfunction json2csv(data, options) {\n    const builtOptions = (0, utils_1.buildJ2COptions)(options ?? {});\n    // Validate the parameters before calling the converter's convert function\n    (0, utils_1.validate)(data, utils_1.isObject, constants_1.errors.json2csv);\n    return (0, json2csv_1.Json2Csv)(builtOptions).convert(data);\n}\nexports.json2csv = json2csv;\nfunction csv2json(data, options) {\n    const builtOptions = (0, utils_1.buildC2JOptions)(options ?? {});\n    // Validate the parameters before calling the converter's convert function\n    (0, utils_1.validate)(data, utils_1.isString, constants_1.errors.csv2json);\n    return (0, csv2json_1.Csv2Json)(builtOptions).convert(data);\n}\nexports.csv2json = csv2json;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,yBAAyB,QAAQ,yBAAyB,QAAQ,SAAS;AACtG,YAAQ,SAAS;AAAA,MACb,iBAAiB;AAAA,MACjB,UAAU;AAAA,QACN,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,eAAe;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,QACN,cAAc;AAAA,QACd,kBAAkB;AAAA,MACtB;AAAA,IACJ;AACA,YAAQ,yBAAyB;AAAA,MAC7B,oBAAoB;AAAA,MACpB,wBAAwB;AAAA,MACxB,WAAW;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,KAAK;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,MACjB,wBAAwB;AAAA,MACxB,UAAU;AAAA,MACV,aAAa,CAAC;AAAA,MACd,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,cAAc;AAAA,IAClB;AACA,YAAQ,yBAAyB;AAAA,MAC7B,WAAW;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,KAAK;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACtB;AACA,YAAQ,WAAW;AAAA;AAAA;;;AClDnB;AAAA;AAAA;AAMA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU,QAAQ,eAAe;AAKzC,aAAS,aAAa,KAAK,IAAI;AAC3B,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AACA,YAAM,EAAE,UAAU,KAAK,UAAU,IAAI,MAAM,EAAE;AAC7C,YAAM,QAAQ,OAAO,QAAQ,YAAY,MAAM,MAAM,IAAI,EAAE,IAAI;AAC/D,YAAM,SAAS,OAAO,QAAQ,YAAY,OAAO,MAAM,IAAI,GAAG,IAAI;AAClE,UAAI,YAAY,KAAK,OAAO,QAAQ,YAAY,EAAE,MAAM,MAAM;AAC1D,cAAM,EAAE,KAAK,QAAQ,IAAI,MAAM,SAAS;AACxC,cAAM,eAAe,SAAS,OAAO;AAErC,YAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,YAAY,GAAG;AAC9C,iBAAO,OAAO,IAAI,CAAC,QAAQ,aAAa,KAAK,SAAS,CAAC;AAAA,QAC3D;AAEA,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC,WACS,MAAM,QAAQ,GAAG,GAAG;AACzB,cAAM,WAAW,SAAS,GAAG;AAC7B,YAAI,OAAO,OAAO,aAAa,MAAM,CAAC,MAAM,QAAQ,GAAG;AACnD,iBAAO;AAAA,QACX;AAEA,eAAO,IAAI,IAAI,CAAC,QAAQ,aAAa,KAAK,EAAE,CAAC;AAAA,MACjD,WACS,YAAY,KAAK,OAAO,OAAO,OAAO,QAAQ,YAAY,OAAO,KAAK;AAE3E,eAAO,aAAa,QAAQ,SAAS;AAAA,MACzC,WACS,aAAa,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,EAAE,MAAM,MAAM;AAE/E,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AACA,YAAQ,eAAe;AAKvB,aAAS,QAAQ,KAAK,IAAI,GAAG;AACzB,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C,WACS,CAAC,IAAI;AACV,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,aAAO,IAAI,KAAK,IAAI,CAAC;AAAA,IACzB;AACA,YAAQ,UAAU;AAElB,aAAS,IAAI,KAAK,IAAI,GAAG;AACrB,YAAM,EAAE,UAAU,KAAK,UAAU,IAAI,MAAM,EAAE;AAE7C,UAAI,GAAG,WAAW,WAAW,KAAK,GAAG,WAAW,aAAa,KAAK,GAAG,WAAW,WAAW,GAAG;AAC1F,eAAO;AAAA,MACX;AACA,UAAI,YAAY,GAAG;AACf,cAAM,WAAW,SAAS,GAAG;AAE7B,YAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,QAAQ,GAAG;AAEpG,cAAI,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC;AAExB,cAAI,IAAI,GAAG,GAAG,WAAW,CAAC;AAC1B,iBAAO;AAAA,QACX,WACS,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAM,QAAQ,GAAG,GAAG;AAErF,cAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,CAAC;AACpC,iBAAO;AAAA,QACX,WACS,OAAO,QAAQ,YAAY,QAAQ,QAAQ,EAAE,OAAO,QAAQ,CAAC,MAAM,QAAQ,GAAG,GAAG;AACtF,gBAAM,EAAE,KAAK,QAAQ,IAAI,MAAM,SAAS;AACxC,gBAAM,eAAe,SAAS,OAAO;AACrC,cAAI,CAAC,MAAM,YAAY,GAAG;AAEtB,gBAAI,GAAG,IAAI,CAAC;AAAA,UAChB,WACS,cAAc,IAAI;AAEvB,gBAAI,EAAE,IAAI;AACV,mBAAO;AAAA,UACX,OACK;AAED,gBAAI,GAAG,IAAI,CAAC;AAAA,UAChB;AAAA,QACJ;AACA,YAAI,IAAI,GAAG,GAAG,WAAW,CAAC;AAAA,MAC9B,WACS,MAAM,QAAQ,GAAG,GAAG;AACzB,cAAM,WAAW,SAAS,GAAG;AAE7B,YAAI,OAAO,OAAO,aAAa,MAAM,CAAC,MAAM,QAAQ,GAAG;AACnD,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACX;AAEA,YAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,WAAW,CAAC,CAAC;AAC3C,eAAO;AAAA,MACX,OACK;AAED,YAAI,GAAG,IAAI;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,IAAI;AACf,YAAM,WAAW,4BAA4B,EAAE;AAC/C,aAAO;AAAA,QACH;AAAA,QACA,KAAK,GAAG,MAAM,GAAG,YAAY,IAAI,WAAW,MAAS,EAAE,QAAQ,QAAQ,GAAG;AAAA,QAC1E,WAAW,GAAG,MAAM,WAAW,CAAC;AAAA,MACpC;AAAA,IACJ;AACA,aAAS,4BAA4B,IAAI;AACrC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,cAAM,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC;AAC/D,YAAI,gBAAgB,OAAO,iBAAiB;AACxC,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACzIA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,sBAAsB,QAAQ,UAAU,QAAQ,SAAS;AACjE,aAAS,OAAO,OAAO;AACnB,aAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,IAC7B;AACA,YAAQ,SAAS;AACjB,aAAS,QAAQ,OAAO;AACpB,aAAO,CAAC,EAAE,OAAO,GAAG,KAAK;AAAA,IAC7B;AACA,YAAQ,UAAU;AAMlB,aAAS,oBAAoB,KAAK;AAC9B,aAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE;AAAA,IAC9F;AACA,YAAQ,sBAAsB;AAAA;AAAA;;;ACnB9B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGA,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB,QAAQ,WAAW;AAC9C,QAAM,QAAQ,aAAa,eAAkB;AAC7C,iBAAa,iBAAoB,OAAO;AAOxC,aAAS,SAAS,QAAQ,SAAS;AAC/B,YAAM,gBAAgB,aAAa,OAAO;AAC1C,UAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,eAAO,qBAAqB,IAAI,QAAQ,aAAa;AAAA,MACzD;AACA,aAAO,CAAC;AAAA,IACZ;AACA,YAAQ,WAAW;AAOnB,aAAS,iBAAiB,MAAM,SAAS;AACrC,YAAM,gBAAgB,aAAa,OAAO;AAC1C,aAAO,KAAK,IAAI,CAAC,aAAa;AAC1B,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AAEnD,iBAAO,SAAS,UAAU,aAAa;AAAA,QAC3C;AACA,eAAO,CAAC;AAAA,MACZ,CAAC;AAAA,IACL;AACA,YAAQ,mBAAmB;AAC3B,aAAS,qBAAqB,SAAS,MAAM,SAAS;AAClD,YAAM,OAAO,OAAO,KAAK,IAAI,EAAE,IAAI,CAAC,eAAe;AAE/C,cAAM,UAAU,aAAa,SAAS,4BAA4B,YAAY,OAAO,CAAC;AAEtF,YAAI,QAAQ,uBAAuB,MAAM,oBAAoB,KAAK,UAAU,CAAC,KAAM,QAAQ,sBAAsB,MAAM,QAAQ,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,EAAE,QAAS;AAC1K,iBAAO,qBAAqB,SAAS,KAAK,UAAU,GAAG,OAAO;AAAA,QAClE,WACS,QAAQ,sBAAsB,MAAM,QAAQ,KAAK,UAAU,CAAC,GAAG;AAEpE,iBAAO,iBAAiB,KAAK,UAAU,GAAG,SAAS,OAAO;AAAA,QAC9D,WACS,QAAQ,qBAAqB,MAAM,QAAQ,KAAK,UAAU,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE,QAAQ;AAC/F,iBAAO,CAAC;AAAA,QACZ;AAEA,eAAO;AAAA,MACX,CAAC;AACD,aAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AASA,aAAS,iBAAiB,UAAU,gBAAgB,SAAS;AACzD,UAAI,eAAe,iBAAiB,UAAU,OAAO;AACrD,UAAI,CAAC,SAAS,QAAQ;AAClB,eAAO,QAAQ,iCAAiC,CAAC,IAAI,CAAC,cAAc;AAAA,MACxE,WACS,SAAS,UAAU,MAAM,QAAQ,YAAY,EAAE,WAAW,GAAG;AAElE,eAAO,CAAC,cAAc;AAAA,MAC1B,OACK;AACD,uBAAe,aAAa,IAAI,CAAC,eAAe;AAC5C,cAAI,MAAM,QAAQ,UAAU,KAAK,WAAW,WAAW,GAAG;AACtD,mBAAO,CAAC,cAAc;AAAA,UAC1B;AACA,iBAAO,WAAW,IAAI,CAAC,WAAW,aAAa,gBAAgB,4BAA4B,QAAQ,OAAO,CAAC,CAAC;AAAA,QAChH,CAAC;AACD,eAAO,MAAM,OAAO,MAAM,QAAQ,YAAY,CAAC;AAAA,MACnD;AAAA,IACJ;AACA,aAAS,4BAA4B,KAAK,SAAS;AAC/C,UAAI,QAAQ,kBAAkB;AAC1B,eAAO,IAAI,QAAQ,OAAO,KAAK;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAOA,aAAS,aAAa,cAAc,gBAAgB;AAChD,UAAI,cAAc;AACd,eAAO,eAAe,MAAM;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AACA,aAAS,aAAa,SAAS;AAC3B,aAAO;AAAA,QACH,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,GAAI,WAAW,CAAC;AAAA,MACpB;AAAA,IACJ;AAAA;AAAA;;;ACzIA,IAAAC,iBAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY,QAAQ,UAAU,QAAQ,SAAS,QAAQ,kBAAkB,QAAQ,UAAU,QAAQ,cAAc,QAAQ,SAAS,QAAQ,WAAW,QAAQ,WAAW,QAAQ,WAAW,QAAQ,SAAS,QAAQ,iBAAiB,QAAQ,oBAAoB,QAAQ,eAAe,QAAQ,2BAA2B,QAAQ,uBAAuB,QAAQ,yBAAyB,QAAQ,WAAW,QAAQ,WAAW,QAAQ,kBAAkB,QAAQ,kBAAkB;AAC7d,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,kBAAkB;AAAxB,QAAsE,mBAAmB;AAMzF,aAAS,gBAAgB,MAAM;AAX/B;AAYI,aAAO;AAAA,QACH,GAAG,YAAY;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,UACP,SAAO,kCAAM,cAAN,mBAAiB,UAAS,YAAY,uBAAuB,UAAU;AAAA,UAC9E,QAAM,kCAAM,cAAN,mBAAiB,SAAQ,YAAY,uBAAuB,UAAU;AAAA,UAC5E,OAAK,kCAAM,cAAN,mBAAiB,QAAO,YAAY,uBAAuB,UAAU;AAAA,QAC9E;AAAA,QACA,eAAe,uBAAO,OAAO,CAAC,CAAC;AAAA,MACnC;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAM1B,aAAS,gBAAgB,MAAM;AA7B/B;AA8BI,aAAO;AAAA,QACH,GAAG,YAAY;AAAA,QACf,GAAG;AAAA,QACH,WAAW;AAAA,UACP,SAAO,kCAAM,cAAN,mBAAiB,UAAS,YAAY,uBAAuB,UAAU;AAAA,UAC9E,QAAM,kCAAM,cAAN,mBAAiB,SAAQ,YAAY,uBAAuB,UAAU;AAAA,UAC5E,OAAK,kCAAM,cAAN,mBAAiB,QAAO,YAAY,uBAAuB,UAAU;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAC1B,aAAS,SAAS,MAAM,cAAc,eAAe;AACjD,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,GAAG,cAAc,YAAY,IAAI,IAAI,GAAG;AAC5D,UAAI,CAAC,aAAa,IAAI;AAClB,cAAM,IAAI,MAAM,cAAc,gBAAgB;AAClD,aAAO;AAAA,IACX;AACA,YAAQ,WAAW;AAInB,aAAS,SAAS,KAAK;AACnB,aAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,IACzC;AACA,YAAQ,WAAW;AAMnB,aAAS,uBAAuB,YAAY,SAAS;AACjD,YAAM,YAAY,WAAW,CAAC,GAAG,YAAY,WAAW,SAAS,GAAG,WAAW,WAAW,SAAS;AAEnG,aAAO,cAAc,QAAQ,UAAU,QAAQ,aAAa,QAAQ,UAAU;AAAA,IAClF;AACA,YAAQ,yBAAyB;AAKjC,aAAS,qBAAqB,YAAY;AACtC,aAAO,gBAAgB,KAAK,UAAU;AAAA,IAC1C;AACA,YAAQ,uBAAuB;AAI/B,aAAS,yBAAyB,SAAS,SAAS;AAChD,aAAO,gBAAgB,SAAS,OAAO,EAClC,OAAO,gBAAgB,SAAS,OAAO,CAAC;AAAA,IACjD;AACA,YAAQ,2BAA2B;AAInC,aAAS,aAAa,YAAY;AAC9B,aAAO,YAAY,UAAU,KAAK,OAAO,UAAU,KAAK,eAAe;AAAA,IAC3E;AACA,YAAQ,eAAe;AAIvB,aAAS,kBAAkB,QAAQ;AAC/B,aAAO,OAAO,OAAO,CAAC,UAAU,CAAC,aAAa,KAAK,CAAC;AAAA,IACxD;AACA,YAAQ,oBAAoB;AAO5B,aAAS,eAAe,KAAK,OAAO,GAAG;AACnC,aAAO,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,IACzC;AACA,YAAQ,iBAAiB;AAczB,aAAS,WAAW,aAAa,MAAM,WAAW;AAC9C,YAAM,iBAAiB,GAAG,WAAW,cAAc,MAAM,SAAS;AAClE,UAAI,SAAS,SAAS,IAAI;AAC1B,UAAI,MAAM,QAAQ,aAAa,KAAK,cAAc,QAAQ;AACtD,sBAAc,QAAQ,CAAC,QAAQ;AAC3B,mBAAS,SAAS,IAAI;AACtB,sBAAY,MAAM,GAAG,WAAW,SAAS,QAAQ,WAAW,GAAG,CAAC;AAAA,QACpE,CAAC;AAAA,MACL,WACS,MAAM,QAAQ,aAAa,KAAK,cAAc,WAAW,GAAG;AAEjE,SAAC,GAAG,WAAW,SAAS,QAAQ,WAAW,EAAE;AAC7C,oBAAY,KAAK,MAAM;AAAA,MAC3B,OACK;AACD,oBAAY,KAAK,MAAM;AAAA,MAC3B;AAAA,IACJ;AAIA,aAAS,OAAO,OAAO,OAAO;AAC1B,YAAM,SAAS,CAAC;AAChB,YAAM,QAAQ,CAAC,SAAS;AACpB,mBAAW,QAAQ,MAAM,KAAK;AAAA,MAClC,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,SAAS;AAIjB,aAAS,SAAS,OAAO;AACrB,aAAO,CAAC,MAAM,OAAO,KAAK,CAAC;AAAA,IAC/B;AACA,YAAQ,WAAW;AAInB,aAAS,SAAS,OAAO;AACrB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,WAAW;AACnB,aAAS,SAAS,OAAO;AACrB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,WAAW;AACnB,aAAS,OAAO,OAAO;AACnB,aAAO,UAAU;AAAA,IACrB;AACA,YAAQ,SAAS;AACjB,aAAS,YAAY,OAAO;AACxB,aAAO,OAAO,UAAU;AAAA,IAC5B;AACA,YAAQ,cAAc;AACtB,aAAS,QAAQ,OAAO;AAGpB,aAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAAA,IACrD;AACA,YAAQ,UAAU;AAClB,aAAS,gBAAgB,GAAG,GAAG;AAC3B,aAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,kBAAkB;AAC1B,aAAS,OAAO,OAAO;AACnB,aAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAAA,IAC7B;AACA,YAAQ,SAAS;AACjB,aAAS,QAAQ,OAAO;AAEpB,UAAI,MAAM,MAAM;AACZ,eAAO,MAAM,KAAK;AAAA,MACtB;AAEA,UAAI,MAAM,SAAS,kBAAkB;AACjC,YAAI,YAAY,CAAC;AACjB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,kBAAkB;AACrD,sBAAY,UAAU,OAAO,GAAG,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC;AAAA,QACxE;AACA,eAAO;AAAA,MACX;AACA,aAAO,MAAM,OAAO,CAAC,aAAa,UAAU,YAAY,OAAO,KAAK,GAAG,CAAC,CAAC;AAAA,IAC7E;AACA,YAAQ,UAAU;AAKlB,aAAS,UAAU,YAAY;AAC3B,aAAO,eAAe,YAClB,eAAe;AAAA,IACvB;AACA,YAAQ,YAAY;AAAA;AAAA;;;ACrNpB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAM,aAAa;AACnB,QAAM,UAAU;AAChB,QAAM,cAAc;AACpB,QAAM,QAAQ,aAAa,gBAAkB;AAC7C,QAAM,WAAW,SAAU,SAAS;AAChC,YAAM,0BAA0B,IAAI,OAAO,QAAQ,UAAU,MAAM,GAAG,GAAG,kBAAkB,YAAY,oBAAoB,QAAQ,cAAc,OAAO,QAAQ,eAAe,aAAa,QAAQ,aAAa,MAAM,4BAA4B,QAAQ,sBAAsB,CAAC,QAAQ,cAAc,eAAe;AAAA,QACnT,oBAAoB,QAAQ;AAAA,QAC5B,qBAAqB,QAAQ;AAAA,QAC7B,oBAAoB;AAAA,QACpB,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,MACtB;AAKA,eAAS,iBAAiB,MAAM;AAE5B,gBAAQ,GAAG,QAAQ,kBAAkB,MAAM,YAAY;AAAA,MAC3D;AAMA,eAAS,eAAe,iBAAiB;AAErC,YAAI,QAAQ,wBAAwB;AAChC,iBAAO,uBAAuB,eAAe;AAAA,QACjD,OACK;AAED,gBAAM,mBAAmB,MAAM,OAAO,MAAM,QAAQ,eAAe,CAAC;AACpE,iBAAO;AAAA,QACX;AAAA,MACJ;AAMA,eAAS,uBAAuB,iBAAiB;AAE7C,cAAM,iBAAiB,gBAAgB,CAAC,GAAG,wBAAwB,gBAAgB,MAAM,CAAC,GAAG,oBAAoB,iCAAiC,gBAAgB,qBAAqB;AAEvL,YAAI,mBAAmB;AACnB,gBAAM,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAA,QAC7D;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iCAAiC,gBAAgB,uBAAuB;AAC7E,eAAO,sBAAsB,OAAO,CAAC,mBAAmB,mBAAmB;AAEvE,gBAAM,sBAAsB,MAAM,yBAAyB,gBAAgB,cAAc,EAAE;AAC3F,iBAAO,sBAAsB,IACvB,oBAAoB,IACpB;AAAA,QACV,GAAG,CAAC;AAAA,MACR;AAIA,eAAS,mBAAmB,UAAU;AAClC,YAAI,QAAQ,aAAa;AACrB,iBAAO,SAAS,OAAO,CAAC,YAAY;AAChC,uBAAW,eAAe,QAAQ,aAAa;AAE3C,oBAAM,QAAQ,uBAAuB,SAAS,cAAc,IAAI,OAAO,IAAI,WAAW,EAAE;AACxF,kBAAI,gBAAgB,WAAW,QAAQ,MAAM,KAAK,GAAG;AACjD,uBAAO;AAAA,cACX;AAAA,YACJ;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,YAAY;AAClC,YAAI,QAAQ,cAAc,OAAO,QAAQ,eAAe,YAAY;AAChE,iBAAO,WAAW,KAAK,QAAQ,UAAU;AAAA,QAC7C,WACS,QAAQ,YAAY;AACzB,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,QAAQ;AAC9B,YAAI,QAAQ,kBAAkB;AAC1B,iBAAO,eAAe,OAAO,aAAa,IAAI,CAAC,UAAU,MAAM,MAAM,GAAG,EACnE,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC,EACnC,KAAK,GAAG,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AAIA,eAAS,iBAAiB,QAAQ;AAE9B,YAAI,QAAQ,eAAe;AACvB,iBAAO,eAAe,OAAO,aAAa,IAAI,SAAU,YAAY;AAChE,mBAAO,0BAA0B,UAAU;AAAA,UAC/C,CAAC;AAAA,QACL;AACA,eAAO;AAAA,MACX;AAIA,eAAS,kBAAkB,QAAQ;AAE/B,cAAM,oBAAoB,OAAO,KAAK,QAAQ,aAAa;AAC3D,eAAO,SAAS,OAAO,aAClB,IAAI,SAAU,OAAO;AACtB,cAAI,YAAY;AAEhB,cAAI,kBAAkB,SAAS,KAAK,GAAG;AACnC,wBAAY,QAAQ,cAAc,KAAK;AAAA,UAC3C,WACS,CAAC,QAAQ,wBAAwB;AAEtC,wBAAY,UAAU,QAAQ,SAAS,GAAG;AAAA,UAC9C;AACA,iBAAO;AAAA,QACX,CAAC,EACI,KAAK,QAAQ,UAAU,KAAK;AACjC,eAAO;AAAA,MACX;AACA,eAAS,4BAA4B;AACjC,YAAI,CAAC,QAAQ;AACT,iBAAO,CAAC;AACZ,eAAO,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,cAAI,OAAO,QAAQ,YAAY,WAAW,KAAK;AAC3C,oBAAQ,cAAc,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI;AACpD,mBAAO,IAAI;AAAA,UACf;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,eAAS,2BAA2B;AAChC,YAAI,CAAC,QAAQ;AACT,iBAAO,CAAC;AACZ,eAAO,QAAQ,KAAK,QAAQ,UAAQ;AAChC,cAAI,OAAO,SAAS,UAAU;AAE1B,mBAAO,CAAC;AAAA,UACZ,WACS,6BAAM,eAAe;AAE1B,mBAAO,KAAK;AAAA,UAChB;AAEA,iBAAO,CAAC;AAAA,QACZ,CAAC;AAAA,MACL;AAKA,eAAS,qBAAqB,MAAM;AAChC,cAAM,oBAAoB,yBAAyB;AACnD,cAAM,aAAa,0BAA0B;AAC7C,cAAM,aAAa,iBAAiB,IAAI;AACxC,cAAM,YAAY,eAAe,UAAU;AAC3C,YAAI,QAAQ,MAAM;AACd,kBAAQ,OAAO;AACf,gBAAM,cAAc,WAAW,QAAQ,CAAC,oBAAoB;AAExD,gBAAI,CAAC,kBAAkB,SAAS,eAAe,GAAG;AAC9C,qBAAO;AAAA,YACX;AAEA,kBAAM,UAAU,CAAC;AACjB,kBAAM,QAAQ,IAAI,OAAO,IAAI,eAAe,EAAE;AAC9C,uBAAW,eAAe,WAAW;AACjC,kBAAI,oBAAoB,eAAe,YAAY,MAAM,KAAK,GAAG;AAC7D,wBAAQ,KAAK,WAAW;AAAA,cAC5B;AAAA,YACJ;AACA,mBAAO;AAAA,UACX,CAAC;AACD,cAAI,CAAC,QAAQ,cAAc;AACvB,kBAAMC,YAAW,mBAAmB,WAAW;AAC/C,mBAAO,iBAAiBA,SAAQ;AAAA,UACpC;AAAA,QACJ;AACA,cAAM,WAAW,mBAAmB,SAAS;AAC7C,eAAO,iBAAiB,QAAQ;AAAA,MACpC;AAUA,eAAS,yBAAyB,QAAQ,YAAY,OAAO;AACzD,YAAI,QAAQ,cAAc;AACtB,gBAAM,wBAAwB,OAAO,QAAQ;AAE7C,iBAAO,aAAa,QAAQ,CAAC,gBAAgB;AACzC,mBAAO,UAAU,MAAM,OAAO,OAAO,SAAS,WAAW;AAAA,UAC7D,CAAC;AACD,gBAAM,eAAe,qBAAqB,OAAO,OAAO;AACxD,iBAAO,eAAe;AAEtB,cAAI,0BAA0B,OAAO,QAAQ,QAAQ;AACjD,mBAAO,yBAAyB,MAAM;AAAA,UAC1C;AAIA,cAAI,CAAC,WAAW;AACZ,mBAAO,yBAAyB,QAAQ,IAAI;AAAA,UAChD;AAEA,cAAI,QAAQ,MAAM;AACd,kBAAM,qBAAqB,0BAA0B;AACrD,mBAAO,eAAe,mBAAmB,kBAAkB;AAAA,UAC/D;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAYA,eAAS,eAAe,QAAQ;AAC5B,eAAO,eAAe,OAAO,QAAQ,IAAI,CAAC,WAAW;AAEjD,gBAAM,kBAAkB,wBAAwB,QAAQ,OAAO,YAAY,GAE3E,sBAAsB,gBAAgB,IAAI,CAAC,eAAe;AACtD,yBAAa,qBAAqB,UAAU;AAC5C,yBAAa,oBAAoB,UAAU;AAC3C,gBAAI,cAAc,oBAAoB,kBAAkB,YAAY,wBAAwB,IAAI,yBAAyB,UAAU;AACnI,0BAAc,0BAA0B,WAAW;AACnD,mBAAO;AAAA,UACX,CAAC;AAED,iBAAO,yBAAyB,mBAAmB;AAAA,QACvD,CAAC,EAAE,KAAK,QAAQ,UAAU,GAAG;AAC7B,eAAO;AAAA,MACX;AAIA,eAAS,6CAA6C,kBAAkB;AACpE,cAAM,2BAA2B,MAAM,kBAAkB,gBAAgB;AAEzE,YAAI,CAAC,iBAAiB,UAAU,CAAC,yBAAyB,QAAQ;AAC9D,iBAAO,QAAQ,mBAAmB;AAAA,QACtC,WACS,yBAAyB,WAAW,GAAG;AAG5C,iBAAO,yBAAyB,CAAC;AAAA,QACrC;AACA,eAAO;AAAA,MACX;AAIA,eAAS,wBAAwB,QAAQ,QAAQ;AAC7C,cAAM,eAAe,CAAC;AACtB,eAAO,QAAQ,CAAC,UAAU;AACtB,cAAI,oBAAoB,GAAG,WAAW,cAAc,QAAQ,KAAK;AACjE,cAAI,CAAC,MAAM,YAAY,QAAQ,eAAe,KAAK,MAAM,aAAa,gBAAgB,GAAG;AACrF,+BAAmB,QAAQ;AAAA,UAC/B,WACS,QAAQ,sBAAsB,MAAM,QAAQ,gBAAgB,GAAG;AACpE,+BAAmB,6CAA6C,gBAAgB;AAAA,UACpF;AACA,uBAAa,KAAK,gBAAgB;AAAA,QACtC,CAAC;AACD,eAAO;AAAA,MACX;AAIA,eAAS,yBAAyB,YAAY;AAC1C,cAAM,SAAS,sBAAsB;AACrC,YAAI,eAAe,QAAQ,MAAM,QAAQ,UAAU,KAAK,OAAO,eAAe,YAAY,CAAC,QAAQ;AAC/F,iBAAO,KAAK,UAAU,UAAU;AAAA,QACpC,WACS,OAAO,eAAe,aAAa;AACxC,iBAAO;AAAA,QACX,WACS,UAAU,QAAQ,sBAAsB;AAC7C,iBAAO,WAAW,YAAY;AAAA,QAClC,OACK;AACD,iBAAO,CAAC,QAAQ,kBAAkB,WAAW,SAAS,IAAI,WAAW,eAAe;AAAA,QACxF;AAAA,MACJ;AAIA,eAAS,qBAAqB,YAAY;AACtC,YAAI,QAAQ,iBAAiB;AACzB,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,oBAAoB;AAAA,UAC9C,WACS,OAAO,eAAe,UAAU;AACrC,mBAAO,WAAW,KAAK;AAAA,UAC3B;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAOA,eAAS,oBAAoB,YAAY;AACrC,YAAI,QAAQ,qBAAqB;AAC7B,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,mBAAmB;AAAA,UAC7C,WACS,OAAO,eAAe,YAAY,CAAC,MAAM,SAAS,UAAU,GAAG;AACpE,mBAAO,WAAW,QAAQ,kBAAkB,EAAE;AAAA,UAClD;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAMA,eAAS,0BAA0B,YAAY;AAC3C,cAAM,gBAAgB,QAAQ,UAAU;AAExC,YAAI,WAAW,SAAS,QAAQ,UAAU,IAAI,GAAG;AAE7C,uBAAa,WAAW,QAAQ,yBAAyB,gBAAgB,aAAa;AAAA,QAC1F;AAGA,YAAI,WAAW,SAAS,QAAQ,UAAU,KAAK,KAC3C,WAAW,SAAS,QAAQ,UAAU,IAAI,KAC1C,WAAW,MAAM,eAAe,KAChC,QAAQ,iBAAiB,eAAe,UAAU,eAAe,UAAU;AAE3E,uBAAa,gBAAgB,aAAa;AAAA,QAC9C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,yBAAyB,mBAAmB;AACjD,eAAO,kBAAkB,KAAK,QAAQ,UAAU,KAAK;AAAA,MACzD;AAOA,eAAS,0BAA0B,QAAQ;AACvC,cAAM,SAAS,OAAO,QAAQ,UAAU,OAAO,cAE/C,OAAO,QAAQ,WAAW,YAAY,WAAW,OAC5C,QAAQ,gBAAgB,SAAS,QAAQ,UAAU,MAAM,MAC1D;AACJ,eAAO;AAAA,MACX;AAKA,eAAS,QAAQ,MAAM;AAEnB,YAAI,MAAM,SAAS,IAAI,KAAK,CAAC,KAAK,QAAQ;AACtC,iBAAO,CAAC,IAAI;AAAA,QAChB;AAEA,cAAM,eAAe;AAAA,UACjB,cAAc,qBAAqB,IAAI;AAAA,UACvC,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AACA,cAAM,WAAW,yBAAyB,YAAY;AACtD,cAAM,YAAY,eAAe,QAAQ;AACzC,cAAM,UAAU,iBAAiB,SAAS;AAC1C,cAAM,UAAU,iBAAiB,OAAO;AACxC,cAAM,YAAY,kBAAkB,OAAO;AAC3C,eAAO,0BAA0B,SAAS;AAAA,MAC9C;AACA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,WAAW;AAAA;AAAA;;;AC3bnB;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AACnB,QAAM,aAAa;AACnB,QAAM,cAAc;AACpB,QAAM,QAAQ,aAAa,gBAAkB;AAC7C,QAAM,WAAW,SAAU,SAAS;AAChC,YAAM,4BAA4B,IAAI,OAAO,QAAQ,UAAU,OAAO,QAAQ,UAAU,MAAM,GAAG,GAAG,gBAAgB,IAAI,OAAO,MAAM,YAAY,QAAQ,GAAG,gBAAgB,QAAQ,cAAc,OAAO,QAAQ,eAAe,aAAa,QAAQ,aAAa,KAAK;AAIvQ,eAAS,iBAAiB,WAAW;AACjC,oBAAY,8BAA8B,SAAS;AACnD,YAAI,QAAQ,kBAAkB;AAC1B,iBAAO,UAAU,MAAM,GAAG,EACrB,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC,EACnC,KAAK,GAAG;AAAA,QACjB;AACA,eAAO;AAAA,MACX;AAIA,eAAS,gBAAgB,OAAO;AAC5B,YAAI,eAAe,CAAC;AACpB,YAAI,QAAQ,cAAc;AACtB,yBAAe,QAAQ,aAAa,IAAI,CAAC,aAAa,WAAW;AAAA,YAC7D,OAAO,iBAAiB,WAAW;AAAA,YACnC;AAAA,UACJ,EAAE;AAAA,QACN,OACK;AAED,gBAAM,YAAY,MAAM,CAAC;AACzB,yBAAe,UAAU,IAAI,CAAC,WAAW,WAAW;AAAA,YAChD,OAAO,iBAAiB,SAAS;AAAA,YACjC;AAAA,UACJ,EAAE;AAEF,cAAI,QAAQ,MAAM;AACd,kBAAM,OAAO,QAAQ;AACrB,2BAAe,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,UAAU,KAAK,CAAC;AAAA,UACpF;AAAA,QACJ;AACA,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA,aAAa,CAAC;AAAA,QAClB;AAAA,MACJ;AAIA,eAAS,cAAc,KAAK;AACxB,YAAI,QAAQ,UAAU;AAClB,iBAAO,IAAI,QAAQ,eAAe,EAAE;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AAIA,eAAS,WAAW,KAAK;AAErB,cAAM,QAAQ,CAAC,GAAG,qBAAqB,IAAI,SAAS,GAAG,qBAAqB,QAAQ,UAAU,IAAI,QAAQ,iBAAiB;AAAA,UACvH,qBAAqB;AAAA,UACrB,cAAc;AAAA,UACd,uBAAuB;AAAA,UACvB,YAAY;AAAA,QAChB;AACA,YAAI,YAAY,CAAC,GAAG,WAAW,YAAY,WAAW,WAAW,QAAQ;AAEzE,eAAO,QAAQ,IAAI,QAAQ;AAEvB,sBAAY,IAAI,KAAK;AAErB,uBAAa,QAAQ,IAAI,QAAQ,CAAC,IAAI;AAEtC,sBAAY,QAAQ,qBAAqB,IAAI,QAAQ,CAAC,IAAI;AAG1D,sBAAY,MAAM,eAAe,KAAK,OAAO,kBAAkB;AAC/D,eAAK,cAAc,QAAQ,UAAU,OAAO,CAAC,eAAe,uBACxD,UAAU,uBAAuB,eAAe,QAAQ,UAAU,OAAO;AAIzE,gBAAI,cAAc,QAAQ,UAAU,OAAO,eAAe,eAAe,OAAO;AAC5E,wBAAU,KAAK,EAAE;AAAA,YACrB,WACS,cAAc,QAAQ,UAAU,OAAO;AAG5C,wBAAU,KAAK,EAAE;AAAA,YACrB,OACK;AAED,wBAAU,KAAK,IAAI,UAAU,eAAe,UAAU,CAAC;AAAA,YAC3D;AAGA,sBAAU,KAAK,EAAE;AAEjB,kBAAM,KAAK,SAAS;AACpB,wBAAY,CAAC;AACb,2BAAe,aAAa,QAAQ;AACpC,2BAAe,eAAe;AAC9B,2BAAe,sBAAsB,cAAc,QAAQ,UAAU;AAAA,UACzE,WACS,UAAU,sBAAsB,cAAc,QAAQ,UAAU,OAAO;AAG5E,kBAAM,cAAc,IAAI,UAAU,eAAe,YAAY,KAAK;AAClE,sBAAU,KAAK,WAAW;AAE1B,sBAAU,KAAK,EAAE;AACjB,kBAAM,KAAK,SAAS;AAAA,UACxB,WACS,UAAU,sBAAsB,cAAc,QAAQ,UAAU;AAAA,WAEpE,CAAC,eAAe,uBACb,eAAe,uBAAuB,eAAe,QAAQ,UAAU,QAAQ,CAAC,eAAe,wBAAwB;AAE3H,kBAAM,UAAU,UAAU,sBAAsB,eAAe,QAAQ,UAAU,OAAO,QAAQ;AAEhG,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,OAAO,CAAC;AAEhE,kBAAM,KAAK,SAAS;AACpB,wBAAY,CAAC;AACb,2BAAe,aAAa,QAAQ;AACpC,2BAAe,eAAe;AAC9B,2BAAe,sBAAsB,cAAc,QAAQ,UAAU;AAAA,UACzE,WACS,cAAc,QAAQ,UAAU,QAAQ,eAAe,QAAQ,UAAU,SAC9E,CAAC,eAAe,uBAAuB,CAAC,eAAe,cAAc;AAErE,2BAAe,aAAa;AAC5B,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAE9B,gBAAI,MAAM,eAAe,KAAK,QAAQ,GAAG,kBAAkB,MAAM,QAAQ,UAAU,KAAK;AACpF,uBAAS,QAAQ,UAAU,IAAI,SAAS;AAAA,YAC5C;AAAA,UACJ,YACU,eAAe,QAAQ,UAAU,QAAQ,eAAe,yBAAyB,eAAe,QAAQ,UAAU,SACxH,cAAc,QAAQ,UAAU,QAAQ,MAAM,eAAe,KAAK,QAAQ,GAAG,kBAAkB,MAAM,QAAQ,UAAU,KAAK;AAE5H,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAAA,UAElC,WACS,cAAc,QAAQ,UAAU,SAAS,UAAU,KAAK,MAAM,eAAe,KAAK,QAAQ,oBAAoB,kBAAkB,MAAM,QAAQ,UAAU,OAAO,CAAC,eAAe,sBAAsB;AAE1M,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa;AAAA,UAChC,WACS,cAAc,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,OAAO;AAEpF,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,QAAQ,CAAC,CAAC;AAClE,2BAAe,aAAa,QAAQ;AACpC,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAAA,UAClC,WACS,cAAc,QAAQ,UAAU,QAAQ,eAAe,QAAQ,UAAU,SAC9E,CAAC,eAAe,uBAAuB,eAAe,cAAc;AAEpE,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,QAAQ,CAAC,CAAC;AAClE,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa;AAAA,UAChC,WACS,cAAc,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,QAAQ,UAAU,eAAe,YAAY;AAE1H,qBAAS;AACT,2BAAe,wBAAwB;AACvC;AAAA,UACJ,WACS,cAAc,QAAQ,UAAU,SAAS,eAAe,QAAQ,UAAU,QAC/E,cAAc,QAAQ,UAAU,QAAQ,CAAC,eAAe,uBACxD,eAAe,cAAc;AAE7B,sBAAU,KAAK,IAAI,UAAU,eAAe,YAAY,KAAK,CAAC;AAC9D,2BAAe,aAAa,QAAQ;AAAA,UACxC,WACS,cAAc,QAAQ,UAAU,SAAS,eAAe,QAAQ,UAAU,QAC/E,cAAc,QAAQ,UAAU,QAAQ,CAAC,eAAe,cAAc;AAGtE,2BAAe,sBAAsB;AACrC,2BAAe,eAAe;AAC9B,2BAAe,aAAa,QAAQ;AAAA,UACxC;AAEA;AAEA,yBAAe,wBAAwB;AAAA,QAC3C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,oBAAoB,QAAQ;AACjC,YAAI,QAAQ,cAAc;AACtB,iBAAO,cAAc,OAAO;AAAA,QAChC,OACK;AACD,iBAAO,cAAc,OAAO,MAAM,OAAO,CAAC;AAAA,QAC9C;AACA,eAAO;AAAA,MACX;AAIA,eAAS,4BAA4B,aAAa,MAAM;AAEpD,cAAM,QAAQ,KAAK,YAAY,KAAK;AAEpC,eAAO,mBAAmB,KAAK;AAAA,MACnC;AAKA,eAAS,mBAAmB,YAAY;AAEpC,cAAM,aAAa,WAAW,UAAU;AAGxC,YAAI,CAAC,MAAM,QAAQ,UAAU,KAAK,CAAC,MAAM,UAAU,UAAU,GAAG;AAC5D,iBAAO;AAAA,QACX,WACS,eAAe,aAAa;AACjC,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAIA,eAAS,gBAAgB,YAAY;AACjC,YAAI,QAAQ,mBAAmB,eAAe,MAAM;AAChD,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAMA,eAAS,eAAe,cAAc,MAAM;AAExC,eAAO,aAAa,OAAO,CAAC,UAAU,gBAAgB;AAElD,gBAAM,QAAQ,4BAA4B,aAAa,IAAI;AAC3D,cAAI;AAEA,oBAAQ,GAAG,WAAW,SAAS,UAAU,YAAY,OAAO,KAAK;AAAA,UACrE,SACO,OAAO;AAEV,mBAAO;AAAA,UACX;AAAA,QACJ,GAAG,CAAC,CAAC;AAAA,MACT;AAKA,eAAS,8BAA8B,YAAY;AAC/C,cAAM,YAAY,WAAW,CAAC,GAAG,YAAY,WAAW,SAAS,GAAG,WAAW,WAAW,SAAS;AAEnG,YAAI,cAAc,QAAQ,UAAU,QAAQ,aAAa,QAAQ,UAAU,MAAM;AAE7E,iBAAO,WAAW,UAAU,IAAI,KAAK,WAAW,UAAU,GAAG,SAAS;AAAA,QAC1E;AACA,eAAO;AAAA,MACX;AAKA,eAAS,6BAA6B,YAAY;AAC9C,eAAO,WAAW,QAAQ,2BAA2B,QAAQ,UAAU,IAAI;AAAA,MAC/E;AAIA,eAAS,qBAAqB,QAAQ;AAElC,eAAO,OAAO,YAAY,OAAO,CAAC,sBAAsB,SAAS;AAC7D,iBAAO,KAAK,IAAI,CAAC,eAAe;AAE5B,yBAAa,8BAA8B,UAAU;AACrD,yBAAa,6BAA6B,UAAU;AACpD,yBAAa,gBAAgB,UAAU;AACvC,mBAAO;AAAA,UACX,CAAC;AACD,gBAAM,oBAAoB,eAAe,OAAO,cAAc,IAAI;AAClE,iBAAO,qBAAqB,OAAO,iBAAiB;AAAA,QACxD,GAAG,CAAC,CAAC;AAAA,MACT;AAIA,eAAS,WAAW,OAAO;AACvB,YAAI;AACA,cAAI,MAAM,uBAAuB,OAAO,OAAO,KAAK,CAAC,MAAM,qBAAqB,KAAK,GAAG;AACpF,mBAAO;AAAA,UACX;AACA,gBAAM,aAAa,cAAc,KAAK;AAEtC,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,mBAAO,WAAW,IAAI,eAAe;AAAA,UACzC;AACA,iBAAO;AAAA,QACX,SACO,KAAK;AACR,iBAAO;AAAA,QACX;AAAA,MACJ;AAIA,eAAS,QAAQ,MAAM;AAEnB,cAAM,WAAW,cAAc,IAAI;AACnC,cAAM,QAAQ,WAAW,QAAQ;AACjC,cAAM,UAAU,gBAAgB,KAAK;AACrC,cAAM,QAAQ,oBAAoB,OAAO;AACzC,eAAO,qBAAqB,KAAK;AAAA,MACrC;AACA,aAAO;AAAA,QACH;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,WAAW;AAAA;AAAA;;;ACzWnB;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW,QAAQ,WAAW;AACtC,QAAM,cAAc;AACpB,QAAM,aAAa;AACnB,QAAM,aAAa;AACnB,QAAM,UAAU;AAChB,aAAS,SAAS,MAAM,SAAS;AAC7B,YAAM,gBAAgB,GAAG,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAE/D,OAAC,GAAG,QAAQ,UAAU,MAAM,QAAQ,UAAU,YAAY,OAAO,QAAQ;AACzE,cAAQ,GAAG,WAAW,UAAU,YAAY,EAAE,QAAQ,IAAI;AAAA,IAC9D;AACA,YAAQ,WAAW;AACnB,aAAS,SAAS,MAAM,SAAS;AAC7B,YAAM,gBAAgB,GAAG,QAAQ,iBAAiB,WAAW,CAAC,CAAC;AAE/D,OAAC,GAAG,QAAQ,UAAU,MAAM,QAAQ,UAAU,YAAY,OAAO,QAAQ;AACzE,cAAQ,GAAG,WAAW,UAAU,YAAY,EAAE,QAAQ,IAAI;AAAA,IAC9D;AACA,YAAQ,WAAW;AAAA;AAAA;", "names": ["exports", "require_utils", "filtered"]}
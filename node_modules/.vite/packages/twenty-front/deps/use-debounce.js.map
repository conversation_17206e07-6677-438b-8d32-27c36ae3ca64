{"version": 3, "sources": ["../../../../use-debounce/src/useDebouncedCallback.ts", "../../../../use-debounce/src/useDebounce.ts", "../../../../use-debounce/src/useThrottledCallback.ts"], "sourcesContent": ["import { useRef, useEffect, useMemo } from 'react';\n\nexport interface CallOptions {\n  /**\n   * Controls if the function should be invoked on the leading edge of the timeout.\n   */\n  leading?: boolean;\n  /**\n   * Controls if the function should be invoked on the trailing edge of the timeout.\n   */\n  trailing?: boolean;\n}\n\nexport interface Options extends CallOptions {\n  /**\n   * The maximum time the given function is allowed to be delayed before it's invoked.\n   */\n  maxWait?: number;\n  /**\n   * If the setting is set to true, all debouncing and timers will happen on the server side as well\n   */\n  debounceOnServer?: boolean;\n}\n\nexport interface ControlFunctions<ReturnT> {\n  /**\n   * Cancel pending function invocations\n   */\n  cancel: () => void;\n  /**\n   * Immediately invoke pending function invocations\n   */\n  flush: () => ReturnT | undefined;\n  /**\n   * Returns `true` if there are any pending function invocations\n   */\n  isPending: () => boolean;\n}\n\n/**\n * Subsequent calls to the debounced function return the result of the last func invocation.\n * Note, that if there are no previous invocations you will get undefined. You should check it in your code properly.\n */\nexport interface DebouncedState<T extends (...args: any) => ReturnType<T>>\n  extends ControlFunctions<ReturnType<T>> {\n  (...args: Parameters<T>): ReturnType<T> | undefined;\n}\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked, or until the next browser frame is drawn.\n *\n * The debounced function comes with a `cancel` method to cancel delayed `func`\n * invocations and a `flush` method to immediately invoke them.\n *\n * Provide `options` to indicate whether `func` should be invoked on the leading\n * and/or trailing edge of the `wait` timeout. The `func` is invoked with the\n * last arguments provided to the debounced function.\n *\n * Subsequent calls to the debounced function return the result of the last\n * `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * If `wait` is omitted in an environment with `requestAnimationFrame`, `func`\n * invocation will be deferred until the next frame is drawn (typically about\n * 16ms).\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `debounce` and `throttle`.\n *\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0]\n *  The number of milliseconds to delay; if omitted, `requestAnimationFrame` is\n *  used (if available, otherwise it will be setTimeout(...,0)).\n * @param {Object} [options={}] The options object.\n *  Controls if `func` should be invoked on the leading edge of the timeout.\n * @param {boolean} [options.leading=false]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {number} [options.maxWait]\n *  Controls if `func` should be invoked the trailing edge of the timeout.\n * @param {boolean} [options.trailing=true]\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * const resizeHandler = useDebouncedCallback(calculateLayout, 150);\n * window.addEventListener('resize', resizeHandler)\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * const clickHandler = useDebouncedCallback(sendMail, 300, {\n *   leading: true,\n *   trailing: false,\n * })\n * <button onClick={clickHandler}>click me</button>\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * const debounced = useDebouncedCallback(batchLog, 250, { 'maxWait': 1000 })\n * const source = new EventSource('/stream')\n * source.addEventListener('message', debounced)\n *\n * // Cancel the trailing debounced invocation.\n * window.addEventListener('popstate', debounced.cancel)\n *\n * // Check for pending invocations.\n * const status = debounced.pending() ? \"Pending...\" : \"Ready\"\n */\nexport default function useDebouncedCallback<\n  T extends (...args: any) => ReturnType<T>,\n>(func: T, wait?: number, options?: Options): DebouncedState<T> {\n  const lastCallTime = useRef(null);\n  const lastInvokeTime = useRef(0);\n  const timerId = useRef(null);\n  const lastArgs = useRef<unknown[]>([]);\n  const lastThis = useRef<unknown>();\n  const result = useRef<ReturnType<T>>();\n  const funcRef = useRef(func);\n  const mounted = useRef(true);\n  // Always keep the latest version of debounce callback, with no wait time.\n  funcRef.current = func;\n\n  const isClientSide = typeof window !== 'undefined';\n  // Bypass `requestAnimationFrame` by explicitly setting `wait=0`.\n  const useRAF = !wait && wait !== 0 && isClientSide;\n\n  if (typeof func !== 'function') {\n    throw new TypeError('Expected a function');\n  }\n\n  wait = +wait || 0;\n  options = options || {};\n\n  const leading = !!options.leading;\n  const trailing = 'trailing' in options ? !!options.trailing : true; // `true` by default\n  const maxing = 'maxWait' in options;\n  const debounceOnServer =\n    'debounceOnServer' in options ? !!options.debounceOnServer : false; // `false` by default\n  const maxWait = maxing ? Math.max(+options.maxWait || 0, wait) : null;\n\n  useEffect(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n\n  // You may have a question, why we have so many code under the useMemo definition.\n  //\n  // This was made as we want to escape from useCallback hell and\n  // not to initialize a number of functions each time useDebouncedCallback is called.\n  //\n  // It means that we have less garbage for our GC calls which improves performance.\n  // Also, it makes this library smaller.\n  //\n  // And the last reason, that the code without lots of useCallback with deps is easier to read.\n  // You have only one place for that.\n  const debounced = useMemo(() => {\n    const invokeFunc = (time: number) => {\n      const args = lastArgs.current;\n      const thisArg = lastThis.current;\n\n      lastArgs.current = lastThis.current = null;\n      lastInvokeTime.current = time;\n      return (result.current = funcRef.current.apply(thisArg, args));\n    };\n\n    const startTimer = (pendingFunc: () => void, wait: number) => {\n      if (useRAF) cancelAnimationFrame(timerId.current);\n      timerId.current = useRAF\n        ? requestAnimationFrame(pendingFunc)\n        : setTimeout(pendingFunc, wait);\n    };\n\n    const shouldInvoke = (time: number) => {\n      if (!mounted.current) return false;\n\n      const timeSinceLastCall = time - lastCallTime.current;\n      const timeSinceLastInvoke = time - lastInvokeTime.current;\n\n      // Either this is the first call, activity has stopped and we're at the\n      // trailing edge, the system time has gone backwards and we're treating\n      // it as the trailing edge, or we've hit the `maxWait` limit.\n      return (\n        !lastCallTime.current ||\n        timeSinceLastCall >= wait ||\n        timeSinceLastCall < 0 ||\n        (maxing && timeSinceLastInvoke >= maxWait)\n      );\n    };\n\n    const trailingEdge = (time: number) => {\n      timerId.current = null;\n\n      // Only invoke if we have `lastArgs` which means `func` has been\n      // debounced at least once.\n      if (trailing && lastArgs.current) {\n        return invokeFunc(time);\n      }\n      lastArgs.current = lastThis.current = null;\n      return result.current;\n    };\n\n    const timerExpired = () => {\n      const time = Date.now();\n      if (shouldInvoke(time)) {\n        return trailingEdge(time);\n      }\n      // https://github.com/xnimorz/use-debounce/issues/97\n      if (!mounted.current) {\n        return;\n      }\n      // Remaining wait calculation\n      const timeSinceLastCall = time - lastCallTime.current;\n      const timeSinceLastInvoke = time - lastInvokeTime.current;\n      const timeWaiting = wait - timeSinceLastCall;\n      const remainingWait = maxing\n        ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)\n        : timeWaiting;\n\n      // Restart the timer\n      startTimer(timerExpired, remainingWait);\n    };\n\n    const func: DebouncedState<T> = (...args: Parameters<T>): ReturnType<T> => {\n      if (!isClientSide && !debounceOnServer) {\n        return;\n      }\n      const time = Date.now();\n      const isInvoking = shouldInvoke(time);\n\n      lastArgs.current = args;\n      lastThis.current = this;\n      lastCallTime.current = time;\n\n      if (isInvoking) {\n        if (!timerId.current && mounted.current) {\n          // Reset any `maxWait` timer.\n          lastInvokeTime.current = lastCallTime.current;\n          // Start the timer for the trailing edge.\n          startTimer(timerExpired, wait);\n          // Invoke the leading edge.\n          return leading ? invokeFunc(lastCallTime.current) : result.current;\n        }\n        if (maxing) {\n          // Handle invocations in a tight loop.\n          startTimer(timerExpired, wait);\n          return invokeFunc(lastCallTime.current);\n        }\n      }\n      if (!timerId.current) {\n        startTimer(timerExpired, wait);\n      }\n      return result.current;\n    };\n\n    func.cancel = () => {\n      if (timerId.current) {\n        useRAF\n          ? cancelAnimationFrame(timerId.current)\n          : clearTimeout(timerId.current);\n      }\n      lastInvokeTime.current = 0;\n      lastArgs.current =\n        lastCallTime.current =\n        lastThis.current =\n        timerId.current =\n          null;\n    };\n\n    func.isPending = () => {\n      return !!timerId.current;\n    };\n\n    func.flush = () => {\n      return !timerId.current ? result.current : trailingEdge(Date.now());\n    };\n\n    return func;\n  }, [\n    leading,\n    maxing,\n    wait,\n    maxWait,\n    trailing,\n    useRAF,\n    isClientSide,\n    debounceOnServer,\n  ]);\n\n  return debounced;\n}\n", "import { useCallback, useRef, useReducer } from 'react';\nimport useDebouncedCallback, { DebouncedState } from './useDebouncedCallback';\n\nfunction valueEquality<T>(left: T, right: T): boolean {\n  return left === right;\n}\n\nfunction reducer<T>(_: T, action: T) {\n  return action;\n}\n\nexport default function useDebounce<T>(\n  value: T,\n  delay: number,\n  options?: {\n    maxWait?: number;\n    leading?: boolean;\n    trailing?: boolean;\n    equalityFn?: (left: T, right: T) => boolean;\n  }\n): [T, DebouncedState<(value: T) => void>] {\n  const eq = (options && options.equalityFn) || valueEquality;\n\n  const [state, dispatch] = useReducer(reducer, value);\n  const debounced = useDebouncedCallback(\n    useCallback((value: T) => dispatch(value), [dispatch]),\n    delay,\n    options\n  );\n  const previousValue = useRef(value);\n\n  if (!eq(previousValue.current, value)) {\n    debounced(value);\n    previousValue.current = value;\n  }\n\n  if (eq(state as T, value)) {\n    debounced.cancel();\n  }\n\n  return [state as T, debounced];\n}\n", "import useDebouncedCallback, {\n  CallOptions,\n  DebouncedState,\n} from './useDebouncedCallback';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds (or once per browser frame).\n *\n * The throttled function comes with a `cancel` method to cancel delayed `func`\n * invocations and a `flush` method to immediately invoke them.\n *\n * Provide `options` to indicate whether `func` should be invoked on the leading\n * and/or trailing edge of the `wait` timeout. The `func` is invoked with the\n * last arguments provided to the throttled function.\n *\n * Subsequent calls to the throttled function return the result of the last\n * `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * If `wait` is omitted in an environment with `requestAnimationFrame`, `func`\n * invocation will be deferred until the next frame is drawn (typically about\n * 16ms).\n *\n * See [<PERSON>'s article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `throttle` and `debounce`.\n *\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0]\n *  The number of milliseconds to throttle invocations to; if omitted,\n *  `requestAnimationFrame` is used (if available, otherwise it will be setTimeout(...,0)).\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * const scrollHandler = useThrottledCallback(updatePosition, 100)\n * window.addEventListener('scroll', scrollHandler)\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * const throttled = useThrottledCallback(renewToken, 300000, { 'trailing': false })\n * <button onClick={throttled}>click</button>\n *\n * // Cancel the trailing throttled invocation.\n * window.addEventListener('popstate', throttled.cancel);\n */\nexport default function useThrottledCallback<\n  T extends (...args: any) => ReturnType<T>,\n>(\n  func: T,\n  wait: number,\n  { leading = true, trailing = true }: CallOptions = {}\n): DebouncedState<T> {\n  return useDebouncedCallback(func, wait, {\n    maxWait: wait,\n    leading,\n    trailing,\n  });\n}\n"], "mappings": ";;;;;;;;;AAkHwB,SAAAA,EAEtBC,IAASC,IAAeC,IAAAA;AAAiB,MAAAC,KAAAC,MACnCC,SAAeC,aAAAA,QAAO,IAAA,GACtBC,SAAiBD,aAAAA,QAAO,CAAA,GACxBE,SAAUF,aAAAA,QAAO,IAAA,GACjBG,QAAWH,aAAAA,QAAkB,CAAA,CAAA,GAC7BI,QAAWJ,aAAAA,QAAAA,GACXK,QAASL,aAAAA,QAAAA,GACTM,QAAUN,aAAAA,QAAON,EAAAA,GACjBa,QAAUP,aAAAA,QAAAA,IAAO;AAEvBM,IAAQE,UAAUd;AAElB,MAAMe,IAAiC,eAAA,OAAXC,QAEtBC,IAAAA,CAAUhB,MAAiB,MAATA,MAAcc;AAEtC,MAAoB,cAAA,OAATf,GACT,OAAU,IAAAkB,UAAU,qBAAA;AAGtBjB,EAAAA,KAAAA,CAAQA,MAAQ;AAGhB,MAAMkB,IAAAA,CAAAA,EAFNjB,KAAUA,MAAW,CAAE,GAEGiB,SACpBC,IAAAA,EAAW,cAAclB,OAAAA,CAAAA,CAAYA,GAAQkB,UAC7CC,IAAS,aAAanB,IACtBoB,IACJ,sBAAsBpB,MAAAA,CAAAA,CAAYA,GAAQoB,kBACtCC,IAAUF,IAASG,KAAKC,IAAAA,CAAKvB,GAAQqB,WAAW,GAAGtB,EAAAA,IAAQ;AAEjEyB,mBAAAA,WAAU,WAAA;AAER,WADAb,EAAQC,UAAAA,MAAU,WAAA;AAEhBD,QAAQC,UAAAA;IACV;EACF,GAAG,CAAA,CAAA;AAYH,MAAMa,QAAYC,aAAAA,SAAQ,WAAA;AACxB,QAAMC,KAAa,SAACC,IAAAA;AAClB,UAAMC,KAAOtB,EAASK,SAChBkB,KAAUtB,EAASI;AAIzB,aAFAL,EAASK,UAAUJ,EAASI,UAAU,MACtCP,GAAeO,UAAUgB,IACjBnB,EAAOG,UAAUF,EAAQE,QAAQmB,MAAMD,IAASD,EAAAA;IAC1D,GAEMG,KAAa,SAACC,IAAyBlC,IAAAA;AACvCgB,WAAQmB,qBAAqB5B,GAAQM,OAAAA,GACzCN,GAAQM,UAAUG,IACdoB,sBAAsBF,EAAAA,IACtBG,WAAWH,IAAalC,EAAAA;IAC9B,GAEMsC,KAAe,SAACT,IAAAA;AACpB,UAAA,CAAKjB,EAAQC,QAAS,QAAA;AAEtB,UAAM0B,KAAoBV,KAAOzB,GAAaS;AAM9C,aAAA,CACGT,GAAaS,WACd0B,MAAqBvC,MACrBuC,KAAoB,KACnBnB,KATyBS,KAAOvB,GAAeO,WASdS;IAEtC,GAEMkB,KAAe,SAACX,IAAAA;AAKpB,aAJAtB,GAAQM,UAAU,MAIdM,KAAYX,EAASK,UAChBe,GAAWC,EAAAA,KAEpBrB,EAASK,UAAUJ,EAASI,UAAU,MAC/BH,EAAOG;IAChB,GAEM4B,KAAe,SAAfA,KAAAA;AACJ,UAAMZ,KAAOa,KAAKC,IAAAA;AAClB,UAAIL,GAAaT,EAAAA,EACf,QAAOW,GAAaX,EAAAA;AAGtB,UAAKjB,EAAQC,SAAb;AAIA,YAEM+B,KAAc5C,MAFM6B,KAAOzB,GAAaS,UAGxCgC,KAAgBzB,IAClBG,KAAKuB,IAAIF,IAAatB,KAHEO,KAAOvB,GAAeO,QAAAA,IAI9C+B;AAGJX,QAAAA,GAAWQ,IAAcI,EAAAA;MAVxB;IAWH,GAEM9C,KAA0B,WAAA;AAC9B,UAAKe,KAAiBO,GAAtB;AAGA,YAAMQ,KAAOa,KAAKC,IAAAA,GACZI,KAAaT,GAAaT,EAAAA;AAMhC,YAJArB,EAASK,UAAO,CAAA,EAAAmC,MAAAC,KAAAC,SAAAA,GAChBzC,EAASI,UAAUX,IACnBE,GAAaS,UAAUgB,IAEnBkB,IAAY;AACd,cAAA,CAAKxC,GAAQM,WAAWD,EAAQC,QAM9B,QAJAP,GAAeO,UAAUT,GAAaS,SAEtCoB,GAAWQ,IAAczC,EAAAA,GAElBkB,IAAUU,GAAWxB,GAAaS,OAAAA,IAAWH,EAAOG;AAE7D,cAAIO,EAGF,QADAa,GAAWQ,IAAczC,EAAAA,GAClB4B,GAAWxB,GAAaS,OAAAA;QAElC;AAID,eAHKN,GAAQM,WACXoB,GAAWQ,IAAczC,EAAAA,GAEpBU,EAAOG;MA1Bb;IA2BH;AAwBA,WAtBAd,GAAKoD,SAAS,WAAA;AACR5C,MAAAA,GAAQM,YACVG,IACImB,qBAAqB5B,GAAQM,OAAAA,IAC7BuC,aAAa7C,GAAQM,OAAAA,IAE3BP,GAAeO,UAAU,GACzBL,EAASK,UACPT,GAAaS,UACbJ,EAASI,UACTN,GAAQM,UACN;IACN,GAEAd,GAAKsD,YAAY,WAAA;AACf,aAAA,CAAA,CAAS9C,GAAQM;IACnB,GAEAd,GAAKuD,QAAQ,WAAA;AACX,aAAQ/C,GAAQM,UAA2B2B,GAAaE,KAAKC,IAAAA,CAAAA,IAAnCjC,EAAOG;IACnC,GAEOd;EACT,GAAG,CACDmB,GACAE,GACApB,IACAsB,GACAH,GACAH,GACAF,GACAO,CAAAA,CAAAA;AAGF,SAAOK;AACT;ACtSA,SAAS6B,EAAiBC,IAASC,IAAAA;AACjC,SAAOD,OAASC;AAClB;AAEA,SAASC,EAAWC,IAAMC,IAAAA;AACxB,SAAOA;AACT;AAEwB,SAAAC,EACtBC,IACAC,IACA9D,IAAAA;AAOA,MAAM+D,KAAM/D,MAAWA,GAAQgE,cAAeV,GAE9CW,QAA0BC,aAAAA,YAAWT,GAASI,EAAAA,GAAvCM,IAAKF,EAAEG,CAAAA,GAAAA,IAAQH,EAAAA,CAAAA,GAChBxC,IAAY5B,MAChBwE,aAAAA,aAAY,SAACR,IAAAA;AAAa,WAAAO,EAASP,EAAAA;EAAM,GAAE,CAACO,CAAAA,CAAAA,GAC5CN,IACA9D,EAAAA,GAEIsE,QAAgBlE,aAAAA,QAAOyD,EAAAA;AAW7B,SATKE,GAAGO,EAAc1D,SAASiD,EAAAA,MAC7BpC,EAAUoC,EAAAA,GACVS,EAAc1D,UAAUiD,KAGtBE,GAAGI,GAAYN,EAAAA,KACjBpC,EAAUyB,OAAAA,GAGL,CAACiB,GAAY1C,CAAAA;AACtB;ACgBc,SAAU8C,EAGtBzE,IACAC,IAAYyE,IAAAA;AACyC,MAAAC,KAAAA,WAAAD,KAAF,CAAE,IAAAA,IAAAE,KAAAD,GAAnDxD,SAAc0D,KAAAF,GAAEvD;AAElB,SAAOrB,EAAqBC,IAAMC,IAAM,EACtCsB,SAAStB,IACTkB,SAAAA,WAJOyD,MAAOA,IAKdxD,UAAAA,WALwByD,MAAOA,GAAAA,CAAAA;AAOnC;", "names": ["useDebouncedCallback", "func", "wait", "options", "_this", "this", "lastCallTime", "useRef", "lastInvokeTime", "timerId", "lastArgs", "lastThis", "result", "funcRef", "mounted", "current", "isClientSide", "window", "useRAF", "TypeError", "leading", "trailing", "maxing", "debounceOnServer", "max<PERSON><PERSON>", "Math", "max", "useEffect", "debounced", "useMemo", "invokeFunc", "time", "args", "thisArg", "apply", "startTimer", "pendingFunc", "cancelAnimationFrame", "requestAnimationFrame", "setTimeout", "shouldInvoke", "timeSinceLastCall", "trailingEdge", "timerExpired", "Date", "now", "timeWaiting", "remainingWait", "min", "isInvoking", "slice", "call", "arguments", "cancel", "clearTimeout", "isPending", "flush", "valueEquality", "left", "right", "reducer", "_", "action", "useDebounce", "value", "delay", "eq", "equalityFn", "_useReducer", "useReducer", "state", "dispatch", "useCallback", "previousValue", "useThrottledCallback", "_temp", "_ref", "_ref$leading", "_ref$trailing"]}
{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/Server/ServerVariablesSelect.vue.js", "../../../../@scalar/api-client/dist/components/Server/ServerVariablesTextbox.vue.js", "../../../../@scalar/api-client/dist/components/Server/ServerVariablesForm.vue.js"], "sourcesContent": ["import { defineComponent as v, computed as s, openBlock as r, createBlock as f, unref as a, withCtx as c, createVNode as i, createElementVNode as S, normalizeClass as V, createElementBlock as h, createCommentVNode as g, createTextVNode as C, toDisplayString as k } from \"vue\";\nimport { ScalarListbox as y, ScalarButton as B, ScalarIcon as N } from \"@scalar/components\";\nconst _ = {\n  key: 0,\n  class: \"sr-only\"\n}, x = /* @__PURE__ */ v({\n  __name: \"ServerVariablesSelect\",\n  props: {\n    enum: {},\n    value: {},\n    controls: {}\n  },\n  emits: [\"change\"],\n  setup(u, { emit: m }) {\n    const o = u, d = m, t = s(\n      () => o.enum.map((e) => ({ id: e, label: e }))\n    ), l = s({\n      get: () => t.value.find((e) => e.id === o.value),\n      set: (e) => d(\"change\", (e == null ? void 0 : e.id) ?? \"\")\n    });\n    return (e, n) => (r(), f(a(y), {\n      modelValue: l.value,\n      \"onUpdate:modelValue\": n[0] || (n[0] = (p) => l.value = p),\n      options: t.value\n    }, {\n      default: c(() => [\n        i(a(B), {\n          \"aria-controls\": e.controls,\n          class: \"h-8 w-full p-0 py-1.5 font-normal\",\n          variant: \"ghost\"\n        }, {\n          default: c(() => [\n            S(\"span\", {\n              class: V({ \"text-c-1\": e.value })\n            }, [\n              e.value ? (r(), h(\"span\", _, \" Selected: \")) : g(\"\", !0),\n              C(\" \" + k(e.value || \"Select value\"), 1)\n            ], 2),\n            i(a(N), {\n              class: \"ml-1\",\n              icon: \"ChevronDown\",\n              size: \"sm\"\n            })\n          ]),\n          _: 1\n        }, 8, [\"aria-controls\"])\n      ]),\n      _: 1\n    }, 8, [\"modelValue\", \"options\"]));\n  }\n});\nexport {\n  x as default\n};\n", "import { defineComponent as p, computed as c, withDirectives as u, openBlock as m, createElementBlock as i, mergeProps as d, vModelText as f } from \"vue\";\nconst g = /* @__PURE__ */ p({\n  __name: \"ServerVariablesTextbox\",\n  props: {\n    value: {},\n    controls: {}\n  },\n  emits: [\"change\"],\n  setup(l, { emit: r }) {\n    const a = l, n = r, t = c({\n      get: () => a.value,\n      set: (e) => n(\"change\", e)\n    });\n    return (e, o) => u((m(), i(\"input\", d({\n      \"onUpdate:modelValue\": o[0] || (o[0] = (s) => t.value = s)\n    }, e.controls ? { ...e.$attrs, \"aria-controls\": e.controls } : {}, {\n      autocomplete: \"off\",\n      class: \"text-c-1 w-full border-transparent px-1.5 -outline-offset-1 group-last/label:rounded-br-lg\",\n      placeholder: \"value\",\n      spellcheck: \"false\",\n      type: \"text\"\n    }), null, 16)), [\n      [f, t.value]\n    ]);\n  }\n});\nexport {\n  g as default\n};\n", "import { defineComponent as h, openBlock as o, createElementBlock as m, Fragment as k, renderList as y, createElementVNode as C, toDisplayString as V, createBlock as v, createCommentVNode as _ } from \"vue\";\nimport B from \"./ServerVariablesSelect.vue.js\";\nimport S from \"./ServerVariablesTextbox.vue.js\";\nconst $ = { class: \"mr-1.5 flex items-center py-1.5 pl-3 after:content-[':'] group-has-[input]/label:mr-0\" }, N = /* @__PURE__ */ h({\n  __name: \"ServerVariablesForm\",\n  props: {\n    variables: {},\n    values: {},\n    controls: {}\n  },\n  emits: [\"update:variable\"],\n  setup(f, { emit: g }) {\n    const n = f, d = g;\n    function s(e, r) {\n      d(\"update:variable\", e, r);\n    }\n    const i = (e) => {\n      var r, l, a;\n      return (((r = n.values) == null ? void 0 : r[e]) ?? ((a = (l = n.variables) == null ? void 0 : l[e]) == null ? void 0 : a.default) ?? \"\").toString();\n    };\n    return (e, r) => e.variables && Object.keys(e.variables ?? {}).length ? (o(!0), m(k, { key: 0 }, y(Object.keys(e.variables), (l) => {\n      var a, u, c, p, b;\n      return o(), m(\"label\", {\n        key: l,\n        class: \"group/label flex w-full\"\n      }, [\n        C(\"span\", $, V(l), 1),\n        (c = (u = (a = e.variables) == null ? void 0 : a[l]) == null ? void 0 : u.enum) != null && c.length ? (o(), v(B, {\n          key: 0,\n          controls: e.controls,\n          enum: ((b = (p = e.variables[l]) == null ? void 0 : p.enum) == null ? void 0 : b.map((t) => `${t}`)) ?? [],\n          label: l,\n          value: i(l),\n          onChange: (t) => s(l, t)\n        }, null, 8, [\"controls\", \"enum\", \"label\", \"value\", \"onChange\"])) : (o(), v(S, {\n          key: 1,\n          controls: e.controls,\n          value: i(l),\n          onChange: (t) => s(l, t)\n        }, null, 8, [\"controls\", \"value\", \"onChange\"]))\n      ]);\n    }), 128)) : _(\"\", !0);\n  }\n});\nexport {\n  N as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAI;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,CAAC;AAAA,IACP,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MACtB,MAAM,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,EAAE;AAAA,IAC/C,GAAG,IAAI,SAAE;AAAA,MACP,KAAK,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK;AAAA,MAC/C,KAAK,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,SAAS,EAAE,OAAO,EAAE;AAAA,IAC3D,CAAC;AACD,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,MAC7B,YAAY,EAAE;AAAA,MACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,MACxD,SAAS,EAAE;AAAA,IACb,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,iBAAiB,EAAE;AAAA,UACnB,OAAO;AAAA,UACP,SAAS;AAAA,QACX,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AAAA,YACf,gBAAE,QAAQ;AAAA,cACR,OAAO,eAAE,EAAE,YAAY,EAAE,MAAM,CAAC;AAAA,YAClC,GAAG;AAAA,cACD,EAAE,SAAS,UAAE,GAAG,mBAAE,QAAQ,GAAG,aAAa,KAAK,mBAAE,IAAI,IAAE;AAAA,cACvD,gBAAE,MAAM,gBAAE,EAAE,SAAS,cAAc,GAAG,CAAC;AAAA,YACzC,GAAG,CAAC;AAAA,YACJ,YAAE,MAAE,CAAC,GAAG;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,YACR,CAAC;AAAA,UACH,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,GAAG,CAAC,eAAe,CAAC;AAAA,MACzB,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,cAAc,SAAS,CAAC;AAAA,EACjC;AACF,CAAC;;;ACjDD,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI,SAAE;AAAA,MACxB,KAAK,MAAM,EAAE;AAAA,MACb,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC;AAAA,IAC3B,CAAC;AACD,WAAO,CAAC,GAAG,MAAM,gBAAG,UAAE,GAAG,mBAAE,SAAS,WAAE;AAAA,MACpC,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,IAC1D,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,QAAQ,iBAAiB,EAAE,SAAS,IAAI,CAAC,GAAG;AAAA,MACjE,cAAc;AAAA,MACd,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,MAAM;AAAA,IACR,CAAC,GAAG,MAAM,EAAE,IAAI;AAAA,MACd,CAAC,YAAG,EAAE,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AACF,CAAC;;;ACtBD,IAAMA,KAAI,EAAE,OAAO,wFAAwF;AAA3G,IAA8G,IAAoB,gBAAE;AAAA,EAClI,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,WAAW,CAAC;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,EACb;AAAA,EACA,OAAO,CAAC,iBAAiB;AAAA,EACzB,MAAM,GAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAIA;AACjB,aAAS,EAAE,GAAG,GAAG;AACf,QAAE,mBAAmB,GAAG,CAAC;AAAA,IAC3B;AACA,UAAM,IAAI,CAAC,MAAM;AACf,UAAI,GAAG,GAAG;AACV,gBAAU,IAAI,EAAE,WAAW,OAAO,SAAS,EAAE,CAAC,QAAQ,KAAK,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,CAAC,MAAM,OAAO,SAAS,EAAE,YAAY,IAAI,SAAS;AAAA,IACrJ;AACA,WAAO,CAAC,GAAG,MAAM,EAAE,aAAa,OAAO,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,UAAU,UAAE,IAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG,WAAE,OAAO,KAAK,EAAE,SAAS,GAAG,CAAC,MAAM;AAClI,UAAI,GAAG,GAAGC,IAAG,GAAG;AAChB,aAAO,UAAE,GAAG,mBAAE,SAAS;AAAA,QACrB,KAAK;AAAA,QACL,OAAO;AAAA,MACT,GAAG;AAAA,QACD,gBAAE,QAAQF,IAAG,gBAAE,CAAC,GAAG,CAAC;AAAA,SACnBE,MAAK,KAAK,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,QAAQA,GAAE,UAAU,UAAE,GAAG,YAAE,GAAG;AAAA,UAC/G,KAAK;AAAA,UACL,UAAU,EAAE;AAAA,UACZ,QAAQ,KAAK,IAAI,EAAE,UAAU,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,OAAO,SAAS,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;AAAA,UACzG,OAAO;AAAA,UACP,OAAO,EAAE,CAAC;AAAA,UACV,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAAA,QACzB,GAAG,MAAM,GAAG,CAAC,YAAY,QAAQ,SAAS,SAAS,UAAU,CAAC,MAAM,UAAE,GAAG,YAAE,GAAG;AAAA,UAC5E,KAAK;AAAA,UACL,UAAU,EAAE;AAAA,UACZ,OAAO,EAAE,CAAC;AAAA,UACV,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAAA,QACzB,GAAG,MAAM,GAAG,CAAC,YAAY,SAAS,UAAU,CAAC;AAAA,MAC/C,CAAC;AAAA,IACH,CAAC,GAAG,GAAG,KAAK,mBAAE,IAAI,IAAE;AAAA,EACtB;AACF,CAAC;", "names": ["$", "g", "c"]}
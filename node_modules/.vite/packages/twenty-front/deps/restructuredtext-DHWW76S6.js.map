{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/restructuredtext/restructuredtext.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rst\",\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  alphanumerics: /[A-Za-z0-9]/,\n  simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n  simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n  phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n  citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n  blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n  precedingChars: /(?:[ -:/'\"<([{])/,\n  followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n  punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n  tokenizer: {\n    root: [\n      //sections\n      [/^(@punctuation{3,}$){1,1}?/, \"keyword\"],\n      //line-blocks\n      //No rules on it\n      //bullet-lists\n      [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, \"keyword\"],\n      //literal-blocks\n      [/([ ]::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      [/(::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      { include: \"@tables\" },\n      { include: \"@explicitMarkupBlocks\" },\n      { include: \"@inlineMarkup\" }\n    ],\n    explicitMarkupBlocks: [\n      //citations\n      { include: \"@citations\" },\n      //footnotes\n      { include: \"@footnotes\" },\n      //directives\n      [\n        /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"keyword\", \"\", \"\"]\n      ],\n      //hyperlink-targets\n      [\n        /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"hyperlinks\" }, \"\", \"\", \"string.link\", \"\", \"\", \"string.link\"]\n      ],\n      //anonymous-hyperlinks\n      [\n        /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"\", \"\", \"string.link\"]\n      ],\n      [/^(__\\s+)(.+)/, [\"\", \"string.link\"]],\n      //substitution-definitions\n      [\n        /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"string.link\", \"\", \"keyword\", \"\"],\n        \"@rawBlocks\"\n      ],\n      [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, [\"\", \"string.link\", \"\"]],\n      //comments\n      [/^(\\.\\.)([ ].*)$/, [{ token: \"\", next: \"@comments\" }, \"comment\"]]\n    ],\n    inlineMarkup: [\n      { include: \"@citationsReference\" },\n      { include: \"@footnotesReference\" },\n      //hyperlink-references\n      [/(@simpleRefName)(_{1,2})/, [\"string.link\", \"\"]],\n      //embedded-uris-and-aliases\n      [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, [\"\", \"string.link\", \"\", \"string.link\", \"\", \"\", \"\"]],\n      //emphasis\n      [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\*[^*]+\\*/, \"emphasis\"],\n      //inline-literals\n      [/(``)((?:[^`]|\\`(?!`))+)(``)/, [\"\", \"keyword\", \"\"]],\n      [/(__\\s+)(.+)/, [\"\", \"keyword\"]],\n      //interpreted-text\n      [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, [\"\", \"keyword\", \"\", \"\", \"\"]],\n      [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, [\"\", \"\", \"\", \"keyword\", \"\"]],\n      [/(`)([^`]+)(`)/, \"\"],\n      //inline-internal-targets\n      [/(_`)(@phrase)(`)/, [\"\", \"string.link\", \"\"]]\n    ],\n    citations: [\n      [\n        /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    citationsReference: [[/(\\[)(@citationName)(\\]_)/, [\"\", \"string.link\", \"\"]]],\n    footnotes: [\n      [\n        /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    footnotesReference: [\n      [/(\\[)([0-9]+)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(#@simpleRefName?)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(\\*)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]]\n    ],\n    blankLineOfLiteralBlocks: [\n      [/^$/, \"\", \"@subsequentLinesOfLiteralBlocks\"],\n      [/^.*$/, \"\", \"@pop\"]\n    ],\n    subsequentLinesOfLiteralBlocks: [\n      [/(@blockLiteralStart+)(.*)/, [\"keyword\", \"\"]],\n      [/^(?!blockLiteralStart)/, \"\", \"@popall\"]\n    ],\n    subsequentLines: [\n      [/^[\\s]+.*/, \"\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    hyperlinks: [\n      [/^[\\s]+.*/, \"string.link\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    comments: [\n      [/^[\\s]+.*/, \"comment\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    tables: [\n      [/\\+-[+-]+/, \"keyword\"],\n      [/\\+=[+=]+/, \"keyword\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAwBA;AAjCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC7C;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,+BAA+B;AAAA,UACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,8BAA8B,SAAS;AAAA;AAAA;AAAA;AAAA,UAIxC,CAAC,oEAAoE,SAAS;AAAA;AAAA,UAE9E,CAAC,eAAe,WAAW,2BAA2B;AAAA,UACtD,CAAC,YAAY,WAAW,2BAA2B;AAAA,UACnD,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,wBAAwB;AAAA,UACnC,EAAE,SAAS,gBAAgB;AAAA,QAC7B;AAAA,QACA,sBAAsB;AAAA;AAAA,UAEpB,EAAE,SAAS,aAAa;AAAA;AAAA,UAExB,EAAE,SAAS,aAAa;AAAA;AAAA,UAExB;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,WAAW,IAAI,EAAE;AAAA,UAC5D;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,aAAa,GAAG,IAAI,IAAI,eAAe,IAAI,IAAI,aAAa;AAAA,UAClF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,IAAI,IAAI,aAAa;AAAA,UACpE;AAAA,UACA,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC;AAAA;AAAA,UAEpC;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,eAAe,IAAI,WAAW,EAAE;AAAA,YAC7E;AAAA,UACF;AAAA,UACA,CAAC,qCAAqC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA;AAAA,UAE7D,CAAC,mBAAmB,CAAC,EAAE,OAAO,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC;AAAA,QACnE;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,SAAS,sBAAsB;AAAA,UACjC,EAAE,SAAS,sBAAsB;AAAA;AAAA,UAEjC,CAAC,4BAA4B,CAAC,eAAe,EAAE,CAAC;AAAA;AAAA,UAEhD,CAAC,kCAAkC,CAAC,IAAI,eAAe,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;AAAA;AAAA,UAErF,CAAC,8BAA8B,QAAQ;AAAA,UACvC,CAAC,aAAa,UAAU;AAAA;AAAA,UAExB,CAAC,+BAA+B,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,UACnD,CAAC,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA;AAAA,UAE/B,CAAC,mDAAmD,CAAC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;AAAA,UAC/E,CAAC,mDAAmD,CAAC,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;AAAA,UAC/E,CAAC,iBAAiB,EAAE;AAAA;AAAA,UAEpB,CAAC,oBAAoB,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,QAC9C;AAAA,QACA,WAAW;AAAA,UACT;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,UACjE;AAAA,QACF;AAAA,QACA,oBAAoB,CAAC,CAAC,4BAA4B,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC;AAAA,QAC1E,WAAW;AAAA,UACT;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,EAAE;AAAA,UAC7D;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,UACjE;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,UACjE;AAAA,QACF;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,uBAAuB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,UACnD,CAAC,iCAAiC,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,UAC7D,CAAC,mBAAmB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,QACjD;AAAA,QACA,0BAA0B;AAAA,UACxB,CAAC,MAAM,IAAI,iCAAiC;AAAA,UAC5C,CAAC,QAAQ,IAAI,MAAM;AAAA,QACrB;AAAA,QACA,gCAAgC;AAAA,UAC9B,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC;AAAA,UAC7C,CAAC,0BAA0B,IAAI,SAAS;AAAA,QAC1C;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,YAAY,EAAE;AAAA,UACf,CAAC,WAAW,IAAI,MAAM;AAAA,QACxB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,YAAY,aAAa;AAAA,UAC1B,CAAC,WAAW,IAAI,MAAM;AAAA,QACxB;AAAA,QACA,UAAU;AAAA,UACR,CAAC,YAAY,SAAS;AAAA,UACtB,CAAC,WAAW,IAAI,MAAM;AAAA,QACxB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,YAAY,SAAS;AAAA,UACtB,CAAC,YAAY,SAAS;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../date-fns/esm/locale/ru/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/ru/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/ru/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/ru/_lib/localize/index.js", "../../../../date-fns/esm/locale/ru/_lib/match/index.js", "../../../../date-fns/esm/locale/ru/index.js"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  var rem10 = count % 10;\n  var rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'через ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' назад';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше секунды',\n      singularNominative: 'меньше {{count}} секунды',\n      singularGenitive: 'меньше {{count}} секунд',\n      pluralGenitive: 'меньше {{count}} секунд'\n    },\n    future: {\n      one: 'меньше, чем через секунду',\n      singularNominative: 'меньше, чем через {{count}} секунду',\n      singularGenitive: 'меньше, чем через {{count}} секунды',\n      pluralGenitive: 'меньше, чем через {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунды',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду назад',\n      singularGenitive: '{{count}} секунды назад',\n      pluralGenitive: '{{count}} секунд назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} секунду',\n      singularGenitive: 'через {{count}} секунды',\n      pluralGenitive: 'через {{count}} секунд'\n    }\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return 'через полминуты';\n      } else {\n        return 'полминуты назад';\n      }\n    }\n    return 'полминуты';\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше минуты',\n      singularNominative: 'меньше {{count}} минуты',\n      singularGenitive: 'меньше {{count}} минут',\n      pluralGenitive: 'меньше {{count}} минут'\n    },\n    future: {\n      one: 'меньше, чем через минуту',\n      singularNominative: 'меньше, чем через {{count}} минуту',\n      singularGenitive: 'меньше, чем через {{count}} минуты',\n      pluralGenitive: 'меньше, чем через {{count}} минут'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} минута',\n      singularGenitive: '{{count}} минуты',\n      pluralGenitive: '{{count}} минут'\n    },\n    past: {\n      singularNominative: '{{count}} минуту назад',\n      singularGenitive: '{{count}} минуты назад',\n      pluralGenitive: '{{count}} минут назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} минуту',\n      singularGenitive: 'через {{count}} минуты',\n      pluralGenitive: 'через {{count}} минут'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} часа',\n      singularGenitive: 'около {{count}} часов',\n      pluralGenitive: 'около {{count}} часов'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} час',\n      singularGenitive: 'приблизительно через {{count}} часа',\n      pluralGenitive: 'приблизительно через {{count}} часов'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} час',\n      singularGenitive: '{{count}} часа',\n      pluralGenitive: '{{count}} часов'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} дня',\n      pluralGenitive: '{{count}} дней'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} недели',\n      singularGenitive: 'около {{count}} недель',\n      pluralGenitive: 'около {{count}} недель'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} неделю',\n      singularGenitive: 'приблизительно через {{count}} недели',\n      pluralGenitive: 'приблизительно через {{count}} недель'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} неделя',\n      singularGenitive: '{{count}} недели',\n      pluralGenitive: '{{count}} недель'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} месяца',\n      singularGenitive: 'около {{count}} месяцев',\n      pluralGenitive: 'около {{count}} месяцев'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} месяц',\n      singularGenitive: 'приблизительно через {{count}} месяца',\n      pluralGenitive: 'приблизительно через {{count}} месяцев'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяца',\n      pluralGenitive: '{{count}} месяцев'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} года',\n      singularGenitive: 'около {{count}} лет',\n      pluralGenitive: 'около {{count}} лет'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} год',\n      singularGenitive: 'приблизительно через {{count}} года',\n      pluralGenitive: 'приблизительно через {{count}} лет'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} год',\n      singularGenitive: '{{count}} года',\n      pluralGenitive: '{{count}} лет'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'больше {{count}} года',\n      singularGenitive: 'больше {{count}} лет',\n      pluralGenitive: 'больше {{count}} лет'\n    },\n    future: {\n      singularNominative: 'больше, чем через {{count}} год',\n      singularGenitive: 'больше, чем через {{count}} года',\n      pluralGenitive: 'больше, чем через {{count}} лет'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'почти {{count}} год',\n      singularGenitive: 'почти {{count}} года',\n      pluralGenitive: 'почти {{count}} лет'\n    },\n    future: {\n      singularNominative: 'почти через {{count}} год',\n      singularGenitive: 'почти через {{count}} года',\n      pluralGenitive: 'почти через {{count}} лет'\n    }\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  return formatDistanceLocale[token](count, options);\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, d MMMM y 'г.'\",\n  long: \"d MMMM y 'г.'\",\n  medium: \"d MMM y 'г.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nexport default formatLong;", "import isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['воскресенье', 'понедельник', 'вторник', 'среду', 'четверг', 'пятницу', 'субботу'];\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в прошлое \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в прошлый \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в прошлую \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'во \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в следующее \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в следующий \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в следующую \" + weekday + \" в' p\";\n  }\n}\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера в' p\",\n  today: \"'сегодня в' p\",\n  tomorrow: \"'завтра в' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.э.', 'н.э.'],\n  abbreviated: ['до н. э.', 'н. э.'],\n  wide: ['до нашей эры', 'нашей эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'март', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']\n};\nvar formattingMonthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'мар.', 'апр.', 'мая', 'июн.', 'июл.', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря']\n};\nvar dayValues = {\n  narrow: ['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['вск', 'пнд', 'втр', 'срд', 'чтв', 'птн', 'суб'],\n  wide: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'вечер',\n    night: 'ночь'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'вечера',\n    night: 'ночи'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n  if (unit === 'date') {\n    suffix = '-е';\n  } else if (unit === 'week' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?э\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?э\\.?)/i,\n  wide: /^(до нашей эры|нашей эры|наша эра)/i\n};\nvar parseEraPatterns = {\n  any: [/^д/i, /^н/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,\n  wide: /^[1234](-?[ыои]?й?)? квартал/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[яфмаисонд]/i,\n  abbreviated: /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\\.?/i,\n  wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^я/i, /^ф/i, /^м/i, /^а/i, /^м/i, /^и/i, /^и/i, /^а/i, /^с/i, /^о/i, /^н/i, /^я/i],\n  any: [/^я/i, /^ф/i, /^мар/i, /^ап/i, /^ма[йя]/i, /^июн/i, /^июл/i, /^ав/i, /^с/i, /^о/i, /^н/i, /^д/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[впсч]/i,\n  short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\\.?/i,\n  abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,\n  wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i\n};\nvar parseDayPatterns = {\n  narrow: [/^в/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^в[ос]/i, /^п[он]/i, /^в/i, /^ср/i, /^ч/i, /^п[ят]/i, /^с[уб]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n  abbreviated: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n  wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^полн/i,\n    noon: /^полд/i,\n    morning: /^у/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Russian locale.\n * @language Russian\n * @iso-639-2 rus\n * <AUTHOR> [@kossnocorp]{@link https://github.com/kossnocorp}\n * <AUTHOR> [@leshakoss]{@link https://github.com/leshakoss}\n */\nvar locale = {\n  code: 'ru',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;;;;AAAA,SAAS,WAAW,QAAQ,OAAO;AAEjC,MAAI,OAAO,QAAQ,UAAa,UAAU,GAAG;AAC3C,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAGrB,MAAI,UAAU,KAAK,WAAW,IAAI;AAChC,WAAO,OAAO,mBAAmB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGrE,WAAW,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK;AACnE,WAAO,OAAO,iBAAiB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGnE,OAAO;AACL,WAAO,OAAO,eAAe,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACjE;AACF;AACA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,SAAU,OAAO,SAAS;AAC/B,QAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,OAAO,QAAQ,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,WAAW,WAAW,OAAO,SAAS,KAAK;AAAA,QACpD;AAAA,MACF,OAAO;AACL,YAAI,OAAO,MAAM;AACf,iBAAO,WAAW,OAAO,MAAM,KAAK;AAAA,QACtC,OAAO;AACL,iBAAO,WAAW,OAAO,SAAS,KAAK,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,WAAW,OAAO,SAAS,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,SAAS,YAAY,QAAQ,SAAS;AACjD,QAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,qBAAqB;AAAA,IAC1B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,SAAS,qBAAqB;AAAA,IAC5B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,YAAY,qBAAqB;AAAA,IAC/B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,IAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAClE,SAAO,qBAAqB,KAAK,EAAE,OAAO,OAAO;AACnD;AACA,IAAO,yBAAQ;;;ACjOf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,KAAK;AACP;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;AC7Bf,IAAI,qBAAqB,CAAC,eAAe,eAAe,WAAW,SAAS,WAAW,WAAW,SAAS;AAC3G,SAAS,UAAU,KAAK;AACtB,MAAI,UAAU,mBAAmB,GAAG;AACpC,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,IACnC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,IACnC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,EACrC;AACF;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,UAAU,mBAAmB,GAAG;AACpC,MAAI,QAAQ,GAAa;AACvB,WAAO,SAAS,UAAU;AAAA,EAC5B,OAAO;AACL,WAAO,QAAQ,UAAU;AAAA,EAC3B;AACF;AACA,SAAS,UAAU,KAAK;AACtB,MAAI,UAAU,mBAAmB,GAAG;AACpC,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,IACrC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,IACrC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,EACvC;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB,UAAU,SAAS,SAAS,MAAM,UAAU,SAAS;AACnD,QAAI,MAAM,KAAK,UAAU;AACzB,QAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,UAAU,GAAG;AAAA,IACtB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU,SAAS,SAAS,MAAM,UAAU,SAAS;AACnD,QAAI,MAAM,KAAK,UAAU;AACzB,QAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,UAAU,GAAG;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,MAAM,UAAU,SAAS;AAC3E,MAAI,SAAS,qBAAqB,KAAK;AACvC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;ACpEf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,WAAW,MAAM;AAAA,EAC1B,aAAa,CAAC,YAAY,OAAO;AAAA,EACjC,MAAM,CAAC,gBAAgB,WAAW;AACpC;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,WAAW,WAAW,WAAW,SAAS;AAAA,EACxD,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,SAAS,MAAM;AAAA,EAC7G,MAAM,CAAC,UAAU,WAAW,QAAQ,UAAU,OAAO,QAAQ,QAAQ,UAAU,YAAY,WAAW,UAAU,SAAS;AAC3H;AACA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,SAAS,MAAM;AAAA,EAC7G,MAAM,CAAC,UAAU,WAAW,SAAS,UAAU,OAAO,QAAQ,QAAQ,WAAW,YAAY,WAAW,UAAU,SAAS;AAC7H;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,eAAe,eAAe,WAAW,SAAS,WAAW,WAAW,SAAS;AAC1F;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa,SAAS;AAC/D,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACrE,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,aAAS;AAAA,EACX,WAAW,SAAS,UAAU,SAAS,YAAY,SAAS,UAAU;AACpE,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,SAAS;AAClB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAO,mBAAQ;;;ACpIf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,OAAO,OAAO,SAAS,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACvG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,WAAW,WAAW,OAAO,QAAQ,OAAO,WAAW,SAAS;AACxE;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;ACpFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["formatDistance", "formatRelative", "ordinalNumber", "valueCallback"]}
import {
  requireSearchcursor
} from "./chunk-UFFZJAP6.js";
import {
  getDefaultExportFromCjs
} from "./chunk-HV37R6KS.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@graphiql/react/dist/searchcursor.es.js
function _mergeNamespaces(n, m) {
  for (var i = 0; i < m.length; i++) {
    const e = m[i];
    if (typeof e !== "string" && !Array.isArray(e)) {
      for (const k in e) {
        if (k !== "default" && !(k in n)) {
          const d = Object.getOwnPropertyDescriptor(e, k);
          if (d) {
            Object.defineProperty(n, k, d.get ? d : {
              enumerable: true,
              get: () => e[k]
            });
          }
        }
      }
    }
  }
  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: "Module" }));
}
var searchcursorExports = requireSearchcursor();
var searchcursor = getDefaultExportFromCjs(searchcursorExports);
var searchcursor$1 = _mergeNamespaces({
  __proto__: null,
  default: searchcursor
}, [searchcursorExports]);
export {
  searchcursor$1 as s
};
//# sourceMappingURL=searchcursor.es-PWJC4EHR.js.map

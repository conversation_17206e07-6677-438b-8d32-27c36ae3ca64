import {
  _
} from "./chunk-LNIR43HZ.js";
import {
  createBlock,
  createTextVNode,
  defineComponent,
  openBlock,
  withCtx
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/CollectionSync.vue2.js
var a = defineComponent({
  __name: "CollectionSync",
  setup(i) {
    return (l, t) => (openBlock(), createBlock(_, null, {
      title: withCtx(() => t[0] || (t[0] = [
        createTextVNode("Sync")
      ])),
      _: 1
    }));
  }
});
export {
  a as default
};
//# sourceMappingURL=CollectionSync.vue-WIEGEPOR.js.map

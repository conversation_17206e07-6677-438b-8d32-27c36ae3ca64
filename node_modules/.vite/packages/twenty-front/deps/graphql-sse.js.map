{"version": 3, "sources": ["../../../../graphql-sse/lib/utils.mjs", "../../../../graphql-sse/lib/common.mjs", "../../../../graphql-sse/lib/handler.mjs", "../../../../graphql-sse/lib/parser.mjs", "../../../../graphql-sse/lib/client.mjs"], "sourcesContent": ["/**\n *\n * utils\n *\n */\n/** @private */\nexport function isObject(val) {\n    return typeof val === 'object' && val !== null;\n}\n", "/**\n *\n * common\n *\n */\nimport { isObject } from './utils.mjs';\n/**\n * Header key through which the event stream token is transmitted\n * when using the client in \"single connection mode\".\n *\n * Read more: https://github.com/enisdenjo/graphql-sse/blob/master/PROTOCOL.md#single-connection-mode\n *\n * @category Common\n */\nexport const TOKEN_HEADER_KEY = 'x-graphql-event-stream-token';\n/**\n * URL query parameter key through which the event stream token is transmitted\n * when using the client in \"single connection mode\".\n *\n * Read more: https://github.com/enisdenjo/graphql-sse/blob/master/PROTOCOL.md#single-connection-mode\n *\n * @category Common\n */\nexport const TOKEN_QUERY_KEY = 'token';\n/** @category Common */\nexport function validateStreamEvent(e) {\n    e = e;\n    if (e !== 'next' && e !== 'complete')\n        throw new Error(`Invalid stream event \"${e}\"`);\n    return e;\n}\n/** @category Common */\nexport function print(msg) {\n    let str = `event: ${msg.event}\\ndata:`;\n    if (msg.data) {\n        str += ' ';\n        str += JSON.stringify(msg.data);\n    }\n    str += '\\n\\n';\n    return str;\n}\n/** @category Common */\nexport function parseStreamData(e, data) {\n    if (data) {\n        try {\n            data = JSON.parse(data);\n        }\n        catch {\n            throw new Error('Invalid stream data');\n        }\n    }\n    if (e === 'next' && !data)\n        throw new Error('Stream data must be an object for \"next\" events');\n    return (data || null);\n}\n/**\n * Checks whether the provided value is an async iterable.\n *\n * @category Common\n */\nexport function isAsyncIterable(val) {\n    return typeof Object(val)[Symbol.asyncIterator] === 'function';\n}\n/**\n * Checks whether the provided value is an async generator.\n *\n * @category Common\n */\nexport function isAsyncGenerator(val) {\n    return (isObject(val) &&\n        typeof Object(val)[Symbol.asyncIterator] === 'function' &&\n        typeof val.return === 'function' &&\n        typeof val.throw === 'function' &&\n        typeof val.next === 'function');\n}\n", "/**\n *\n * handler\n *\n */\nimport { getOperationAST, parse, validate as graphqlValidate, execute as graphqlExecute, subscribe as graphqlSubscribe, } from 'graphql';\nimport { isObject } from './utils.mjs';\nimport { TOKEN_HEADER_KEY, TOKEN_QUERY_KEY, print, isAsyncGenerator, isAsyncIterable, } from './common.mjs';\n/**\n * Makes a Protocol compliant HTTP GraphQL server handler. The handler can\n * be used with your favourite server library.\n *\n * Read more about the Protocol in the PROTOCOL.md documentation file.\n *\n * @category Server\n */\nexport function createHandler(options) {\n    const { validate = graphqlValidate, execute = graphqlExecute, subscribe = graphqlSubscribe, schema, authenticate = function extractOrCreateStreamToken(req) {\n        var _a;\n        const headerToken = req.headers.get(TOKEN_HEADER_KEY);\n        if (headerToken)\n            return Array.isArray(headerToken) ? headerToken.join('') : headerToken;\n        const urlToken = new URL((_a = req.url) !== null && _a !== void 0 ? _a : '', 'http://localhost/').searchParams.get(TOKEN_QUERY_KEY);\n        if (urlToken)\n            return urlToken;\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }, onConnect, context, onSubscribe, onOperation, onNext, onComplete, } = options;\n    const streams = {};\n    function createStream(token) {\n        const ops = {};\n        let pinger;\n        const msgs = (() => {\n            const pending = [];\n            const deferred = {\n                done: false,\n                error: null,\n                resolve: () => {\n                    // noop\n                },\n            };\n            async function dispose() {\n                clearInterval(pinger);\n                // make room for another potential stream while this one is being disposed\n                if (typeof token === 'string')\n                    delete streams[token];\n                // complete all operations and flush messages queue before ending the stream\n                for (const op of Object.values(ops)) {\n                    if (isAsyncGenerator(op)) {\n                        await op.return(undefined);\n                    }\n                }\n            }\n            const iterator = (async function* iterator() {\n                for (;;) {\n                    if (!pending.length) {\n                        // only wait if there are no pending messages available\n                        await new Promise((resolve) => (deferred.resolve = resolve));\n                    }\n                    // first flush\n                    while (pending.length) {\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        yield pending.shift();\n                    }\n                    // then error\n                    if (deferred.error) {\n                        throw deferred.error;\n                    }\n                    // or complete\n                    if (deferred.done) {\n                        return;\n                    }\n                }\n            })();\n            iterator.throw = async (err) => {\n                if (!deferred.done) {\n                    deferred.done = true;\n                    deferred.error = err;\n                    deferred.resolve();\n                    await dispose();\n                }\n                return { done: true, value: undefined };\n            };\n            iterator.return = async () => {\n                if (!deferred.done) {\n                    deferred.done = true;\n                    deferred.resolve();\n                    await dispose();\n                }\n                return { done: true, value: undefined };\n            };\n            return {\n                next(msg) {\n                    pending.push(msg);\n                    deferred.resolve();\n                },\n                iterator,\n            };\n        })();\n        let subscribed = false;\n        return {\n            get open() {\n                return subscribed;\n            },\n            ops,\n            subscribe() {\n                subscribed = true;\n                // write an empty message because some browsers (like Firefox and Safari)\n                // dont accept the header flush\n                msgs.next(':\\n\\n');\n                // ping client every 12 seconds to keep the connection alive\n                pinger = setInterval(() => msgs.next(':\\n\\n'), 12000);\n                return msgs.iterator;\n            },\n            from(ctx, req, result, opId) {\n                (async () => {\n                    if (isAsyncIterable(result)) {\n                        /** multiple emitted results */\n                        for await (let part of result) {\n                            const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, req, part));\n                            if (maybeResult)\n                                part = maybeResult;\n                            msgs.next(print({\n                                event: 'next',\n                                data: opId\n                                    ? {\n                                        id: opId,\n                                        payload: part,\n                                    }\n                                    : part,\n                            }));\n                        }\n                    }\n                    else {\n                        /** single emitted result */\n                        const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, req, result));\n                        if (maybeResult)\n                            result = maybeResult;\n                        msgs.next(print({\n                            event: 'next',\n                            data: opId\n                                ? {\n                                    id: opId,\n                                    payload: result,\n                                }\n                                : result,\n                        }));\n                    }\n                    msgs.next(print({\n                        event: 'complete',\n                        data: opId ? { id: opId } : null,\n                    }));\n                    await (onComplete === null || onComplete === void 0 ? void 0 : onComplete(ctx, req));\n                    if (!opId) {\n                        // end on complete when no operation id is present\n                        // because distinct event streams are used for each operation\n                        await msgs.iterator.return();\n                    }\n                    else {\n                        delete ops[opId];\n                    }\n                })().catch(msgs.iterator.throw);\n            },\n        };\n    }\n    async function prepare(req, params) {\n        let args;\n        const onSubscribeResult = await (onSubscribe === null || onSubscribe === void 0 ? void 0 : onSubscribe(req, params));\n        if (isResponse(onSubscribeResult))\n            return onSubscribeResult;\n        else if (isExecutionResult(onSubscribeResult) ||\n            isAsyncIterable(onSubscribeResult))\n            return {\n                // even if the result is already available, use\n                // context because onNext and onComplete needs it\n                ctx: (typeof context === 'function'\n                    ? await context(req, params)\n                    : context),\n                perform() {\n                    return onSubscribeResult;\n                },\n            };\n        else if (onSubscribeResult)\n            args = onSubscribeResult;\n        else {\n            // you either provide a schema dynamically through\n            // `onSubscribe` or you set one up during the server setup\n            if (!schema)\n                throw new Error('The GraphQL schema is not provided');\n            const { operationName, variables } = params;\n            let query;\n            try {\n                query = parse(params.query);\n            }\n            catch (err) {\n                return [\n                    JSON.stringify({\n                        errors: [\n                            err instanceof Error\n                                ? {\n                                    message: err.message,\n                                    // TODO: stack might leak sensitive information\n                                    // stack: err.stack,\n                                }\n                                : err,\n                        ],\n                    }),\n                    {\n                        status: 400,\n                        statusText: 'Bad Request',\n                        headers: { 'content-type': 'application/json; charset=utf-8' },\n                    },\n                ];\n            }\n            const argsWithoutSchema = {\n                operationName,\n                document: query,\n                variableValues: variables,\n                contextValue: (typeof context === 'function'\n                    ? await context(req, params)\n                    : context),\n            };\n            args = {\n                ...argsWithoutSchema,\n                schema: typeof schema === 'function'\n                    ? await schema(req, argsWithoutSchema)\n                    : schema,\n            };\n        }\n        let operation;\n        try {\n            const ast = getOperationAST(args.document, args.operationName);\n            if (!ast)\n                throw null;\n            operation = ast.operation;\n        }\n        catch {\n            return [\n                JSON.stringify({\n                    errors: [{ message: 'Unable to detect operation AST' }],\n                }),\n                {\n                    status: 400,\n                    statusText: 'Bad Request',\n                    headers: { 'content-type': 'application/json; charset=utf-8' },\n                },\n            ];\n        }\n        // mutations cannot happen over GETs as per the spec\n        // Read more: https://github.com/graphql/graphql-over-http/blob/main/spec/GraphQLOverHTTP.md#get\n        if (operation === 'mutation' && req.method === 'GET') {\n            return [\n                JSON.stringify({\n                    errors: [{ message: 'Cannot perform mutations over GET' }],\n                }),\n                {\n                    status: 405,\n                    statusText: 'Method Not Allowed',\n                    headers: {\n                        allow: 'POST',\n                        'content-type': 'application/json; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        // we validate after injecting the context because the process of\n        // reporting the validation errors might need the supplied context value\n        const validationErrs = validate(args.schema, args.document);\n        if (validationErrs.length) {\n            if (req.headers.get('accept') === 'text/event-stream') {\n                // accept the request and emit the validation error in event streams,\n                // promoting graceful GraphQL error reporting\n                // Read more: https://www.w3.org/TR/eventsource/#processing-model\n                // Read more: https://github.com/graphql/graphql-over-http/blob/main/spec/GraphQLOverHTTP.md#document-validation\n                return {\n                    ctx: args.contextValue,\n                    perform() {\n                        return { errors: validationErrs };\n                    },\n                };\n            }\n            return [\n                JSON.stringify({ errors: validationErrs }),\n                {\n                    status: 400,\n                    statusText: 'Bad Request',\n                    headers: { 'content-type': 'application/json; charset=utf-8' },\n                },\n            ];\n        }\n        return {\n            ctx: args.contextValue,\n            async perform() {\n                const result = await (operation === 'subscription'\n                    ? subscribe(args)\n                    : execute(args));\n                const maybeResult = await (onOperation === null || onOperation === void 0 ? void 0 : onOperation(args.contextValue, req, args, result));\n                if (maybeResult)\n                    return maybeResult;\n                return result;\n            },\n        };\n    }\n    return async function handler(req) {\n        var _a, _b, _c;\n        const token = await authenticate(req);\n        if (isResponse(token))\n            return token;\n        // TODO: make accept detection more resilient\n        const accept = req.headers.get('accept') || '*/*';\n        const stream = typeof token === 'string' ? streams[token] : null;\n        if (accept === 'text/event-stream') {\n            const maybeResponse = await (onConnect === null || onConnect === void 0 ? void 0 : onConnect(req));\n            if (isResponse(maybeResponse))\n                return maybeResponse;\n            // if event stream is not registered, process it directly.\n            // this means that distinct connections are used for graphql operations\n            if (!stream) {\n                const paramsOrResponse = await parseReq(req);\n                if (isResponse(paramsOrResponse))\n                    return paramsOrResponse;\n                const params = paramsOrResponse;\n                const distinctStream = createStream(null);\n                // reserve space for the operation\n                distinctStream.ops[''] = null;\n                const prepared = await prepare(req, params);\n                if (isResponse(prepared))\n                    return prepared;\n                const result = await prepared.perform();\n                if (isAsyncIterable(result))\n                    distinctStream.ops[''] = result;\n                distinctStream.from(prepared.ctx, req, result, null);\n                return [\n                    distinctStream.subscribe(),\n                    {\n                        status: 200,\n                        statusText: 'OK',\n                        headers: {\n                            connection: 'keep-alive',\n                            'cache-control': 'no-cache',\n                            'content-encoding': 'none',\n                            'content-type': 'text/event-stream; charset=utf-8',\n                        },\n                    },\n                ];\n            }\n            // open stream cant exist, only one per token is allowed\n            if (stream.open) {\n                return [\n                    JSON.stringify({ errors: [{ message: 'Stream already open' }] }),\n                    {\n                        status: 409,\n                        statusText: 'Conflict',\n                        headers: {\n                            'content-type': 'application/json; charset=utf-8',\n                        },\n                    },\n                ];\n            }\n            return [\n                stream.subscribe(),\n                {\n                    status: 200,\n                    statusText: 'OK',\n                    headers: {\n                        connection: 'keep-alive',\n                        'cache-control': 'no-cache',\n                        'content-encoding': 'none',\n                        'content-type': 'text/event-stream; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        // if there us no token supplied, exclusively use the \"distinct connection mode\"\n        if (typeof token !== 'string') {\n            return [null, { status: 404, statusText: 'Not Found' }];\n        }\n        // method PUT prepares a stream for future incoming connections\n        if (req.method === 'PUT') {\n            if (!['*/*', 'text/plain'].includes(accept)) {\n                return [null, { status: 406, statusText: 'Not Acceptable' }];\n            }\n            // streams mustnt exist if putting new one\n            if (stream) {\n                return [\n                    JSON.stringify({\n                        errors: [{ message: 'Stream already registered' }],\n                    }),\n                    {\n                        status: 409,\n                        statusText: 'Conflict',\n                        headers: {\n                            'content-type': 'application/json; charset=utf-8',\n                        },\n                    },\n                ];\n            }\n            streams[token] = createStream(token);\n            return [\n                token,\n                {\n                    status: 201,\n                    statusText: 'Created',\n                    headers: {\n                        'content-type': 'text/plain; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        else if (req.method === 'DELETE') {\n            // method DELETE completes an existing operation streaming in streams\n            // streams must exist when completing operations\n            if (!stream) {\n                return [\n                    JSON.stringify({\n                        errors: [{ message: 'Stream not found' }],\n                    }),\n                    {\n                        status: 404,\n                        statusText: 'Not Found',\n                        headers: {\n                            'content-type': 'application/json; charset=utf-8',\n                        },\n                    },\n                ];\n            }\n            const opId = new URL((_a = req.url) !== null && _a !== void 0 ? _a : '', 'http://localhost/').searchParams.get('operationId');\n            if (!opId) {\n                return [\n                    JSON.stringify({\n                        errors: [{ message: 'Operation ID is missing' }],\n                    }),\n                    {\n                        status: 400,\n                        statusText: 'Bad Request',\n                        headers: {\n                            'content-type': 'application/json; charset=utf-8',\n                        },\n                    },\n                ];\n            }\n            const op = stream.ops[opId];\n            if (isAsyncGenerator(op))\n                op.return(undefined);\n            delete stream.ops[opId]; // deleting the operation means no further activity should take place\n            return [\n                null,\n                {\n                    status: 200,\n                    statusText: 'OK',\n                },\n            ];\n        }\n        else if (req.method !== 'GET' && req.method !== 'POST') {\n            // only POSTs and GETs are accepted at this point\n            return [\n                null,\n                {\n                    status: 405,\n                    statusText: 'Method Not Allowed',\n                    headers: {\n                        allow: 'GET, POST, PUT, DELETE',\n                    },\n                },\n            ];\n        }\n        else if (!stream) {\n            // for all other requests, streams must exist to attach the result onto\n            return [\n                JSON.stringify({\n                    errors: [{ message: 'Stream not found' }],\n                }),\n                {\n                    status: 404,\n                    statusText: 'Not Found',\n                    headers: {\n                        'content-type': 'application/json; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        if (!['*/*', 'application/*', 'application/json'].includes(accept)) {\n            return [\n                null,\n                {\n                    status: 406,\n                    statusText: 'Not Acceptable',\n                },\n            ];\n        }\n        const paramsOrResponse = await parseReq(req);\n        if (isResponse(paramsOrResponse))\n            return paramsOrResponse;\n        const params = paramsOrResponse;\n        const opId = String((_c = (_b = params.extensions) === null || _b === void 0 ? void 0 : _b.operationId) !== null && _c !== void 0 ? _c : '');\n        if (!opId) {\n            return [\n                JSON.stringify({\n                    errors: [{ message: 'Operation ID is missing' }],\n                }),\n                {\n                    status: 400,\n                    statusText: 'Bad Request',\n                    headers: {\n                        'content-type': 'application/json; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        if (opId in stream.ops) {\n            return [\n                JSON.stringify({\n                    errors: [{ message: 'Operation with ID already exists' }],\n                }),\n                {\n                    status: 409,\n                    statusText: 'Conflict',\n                    headers: {\n                        'content-type': 'application/json; charset=utf-8',\n                    },\n                },\n            ];\n        }\n        // reserve space for the operation through ID\n        stream.ops[opId] = null;\n        const prepared = await prepare(req, params);\n        if (isResponse(prepared))\n            return prepared;\n        // operation might have completed before prepared\n        if (!(opId in stream.ops)) {\n            return [\n                null,\n                {\n                    status: 204,\n                    statusText: 'No Content',\n                },\n            ];\n        }\n        const result = await prepared.perform();\n        // operation might have completed before performed\n        if (!(opId in stream.ops)) {\n            if (isAsyncGenerator(result))\n                result.return(undefined);\n            if (!(opId in stream.ops)) {\n                return [\n                    null,\n                    {\n                        status: 204,\n                        statusText: 'No Content',\n                    },\n                ];\n            }\n        }\n        if (isAsyncIterable(result))\n            stream.ops[opId] = result;\n        // streaming to an empty reservation is ok (will be flushed on connect)\n        stream.from(prepared.ctx, req, result, opId);\n        return [null, { status: 202, statusText: 'Accepted' }];\n    };\n}\nasync function parseReq(req) {\n    var _a, _b, _c;\n    const params = {};\n    try {\n        switch (true) {\n            case req.method === 'GET': {\n                try {\n                    const [, search] = req.url.split('?');\n                    const searchParams = new URLSearchParams(search);\n                    params.operationName = (_a = searchParams.get('operationName')) !== null && _a !== void 0 ? _a : undefined;\n                    params.query = (_b = searchParams.get('query')) !== null && _b !== void 0 ? _b : undefined;\n                    const variables = searchParams.get('variables');\n                    if (variables)\n                        params.variables = JSON.parse(variables);\n                    const extensions = searchParams.get('extensions');\n                    if (extensions)\n                        params.extensions = JSON.parse(extensions);\n                }\n                catch {\n                    throw new Error('Unparsable URL');\n                }\n                break;\n            }\n            case req.method === 'POST' &&\n                ((_c = req.headers.get('content-type')) === null || _c === void 0 ? void 0 : _c.includes('application/json')):\n                {\n                    if (!req.body) {\n                        throw new Error('Missing body');\n                    }\n                    const body = typeof req.body === 'function' ? await req.body() : req.body;\n                    const data = typeof body === 'string' ? JSON.parse(body) : body;\n                    if (!isObject(data)) {\n                        throw new Error('JSON body must be an object');\n                    }\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Any is ok because values will be checked below.\n                    params.operationName = data.operationName;\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Any is ok because values will be checked below.\n                    params.query = data.query;\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Any is ok because values will be checked below.\n                    params.variables = data.variables;\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Any is ok because values will be checked below.\n                    params.extensions = data.extensions;\n                    break;\n                }\n            default:\n                return [\n                    null,\n                    {\n                        status: 415,\n                        statusText: 'Unsupported Media Type',\n                    },\n                ];\n        }\n        if (params.query == null)\n            throw new Error('Missing query');\n        if (typeof params.query !== 'string')\n            throw new Error('Invalid query');\n        if (params.variables != null &&\n            (typeof params.variables !== 'object' || Array.isArray(params.variables))) {\n            throw new Error('Invalid variables');\n        }\n        if (params.extensions != null &&\n            (typeof params.extensions !== 'object' ||\n                Array.isArray(params.extensions))) {\n            throw new Error('Invalid extensions');\n        }\n        // request parameters are checked and now complete\n        return params;\n    }\n    catch (err) {\n        return [\n            JSON.stringify({\n                errors: [\n                    err instanceof Error\n                        ? {\n                            message: err.message,\n                            // TODO: stack might leak sensitive information\n                            // stack: err.stack,\n                        }\n                        : err,\n                ],\n            }),\n            {\n                status: 400,\n                statusText: 'Bad Request',\n                headers: { 'content-type': 'application/json; charset=utf-8' },\n            },\n        ];\n    }\n}\nfunction isResponse(val) {\n    // TODO: comprehensive check\n    return Array.isArray(val);\n}\nexport function isExecutionResult(val) {\n    return (isObject(val) &&\n        ('data' in val || ('data' in val && val.data == null && 'errors' in val)));\n}\n", "/**\n *\n * parser\n *\n */\nimport { validateStreamEvent, parseStreamData, } from './common.mjs';\nvar ControlChars;\n(function (ControlChars) {\n    ControlChars[ControlChars[\"NewLine\"] = 10] = \"NewLine\";\n    ControlChars[ControlChars[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    ControlChars[ControlChars[\"Space\"] = 32] = \"Space\";\n    ControlChars[ControlChars[\"Colon\"] = 58] = \"Colon\";\n})(ControlChars || (ControlChars = {}));\n/**\n * HTTP response chunk parser for graphql-sse's event stream messages.\n *\n * Reference: https://github.com/Azure/fetch-event-source/blob/main/src/parse.ts\n *\n * @private\n */\nexport function createParser() {\n    let buffer;\n    let position; // current read position\n    let fieldLength; // length of the `field` portion of the line\n    let discardTrailingNewline = false;\n    let message = { event: '', data: '' };\n    let pending = [];\n    const decoder = new TextDecoder();\n    return function parse(chunk) {\n        if (buffer === undefined) {\n            buffer = chunk;\n            position = 0;\n            fieldLength = -1;\n        }\n        else {\n            const concat = new Uint8Array(buffer.length + chunk.length);\n            concat.set(buffer);\n            concat.set(chunk, buffer.length);\n            buffer = concat;\n        }\n        const bufLength = buffer.length;\n        let lineStart = 0; // index where the current line starts\n        while (position < bufLength) {\n            if (discardTrailingNewline) {\n                if (buffer[position] === ControlChars.NewLine) {\n                    lineStart = ++position; // skip to next char\n                }\n                discardTrailingNewline = false;\n            }\n            // look forward until the end of line\n            let lineEnd = -1; // index of the \\r or \\n char\n            for (; position < bufLength && lineEnd === -1; ++position) {\n                switch (buffer[position]) {\n                    case ControlChars.Colon:\n                        if (fieldLength === -1) {\n                            // first colon in line\n                            fieldLength = position - lineStart;\n                        }\n                        break;\n                    // \\r case below should fallthrough to \\n:\n                    case ControlChars.CarriageReturn:\n                        discardTrailingNewline = true;\n                    // eslint-disable-next-line no-fallthrough\n                    case ControlChars.NewLine:\n                        lineEnd = position;\n                        break;\n                }\n            }\n            if (lineEnd === -1) {\n                // end of the buffer but the line hasn't ended\n                break;\n            }\n            else if (lineStart === lineEnd) {\n                // empty line denotes end of incoming message\n                if (message.event || message.data) {\n                    // NOT a server ping (\":\\n\\n\")\n                    if (!message.event)\n                        throw new Error('Missing message event');\n                    const event = validateStreamEvent(message.event);\n                    const data = parseStreamData(event, message.data);\n                    pending.push({\n                        event,\n                        data,\n                    });\n                    message = { event: '', data: '' };\n                }\n            }\n            else if (fieldLength > 0) {\n                // end of line indicates message\n                const line = buffer.subarray(lineStart, lineEnd);\n                // exclude comments and lines with no values\n                // line is of format \"<field>:<value>\" or \"<field>: <value>\"\n                // https://html.spec.whatwg.org/multipage/server-sent-events.html#event-stream-interpretation\n                const field = decoder.decode(line.subarray(0, fieldLength));\n                const valueOffset = fieldLength + (line[fieldLength + 1] === ControlChars.Space ? 2 : 1);\n                const value = decoder.decode(line.subarray(valueOffset));\n                switch (field) {\n                    case 'event':\n                        message.event = value;\n                        break;\n                    case 'data':\n                        // append the new value if the message has data\n                        message.data = message.data ? message.data + '\\n' + value : value;\n                        break;\n                }\n            }\n            // next line\n            lineStart = position;\n            fieldLength = -1;\n        }\n        if (lineStart === bufLength) {\n            // finished reading\n            buffer = undefined;\n            const messages = [...pending];\n            pending = [];\n            return messages;\n        }\n        else if (lineStart !== 0) {\n            // create a new view into buffer beginning at lineStart so we don't\n            // need to copy over the previous lines when we get the new chunk\n            buffer = buffer.subarray(lineStart);\n            position -= lineStart;\n        }\n    };\n}\n", "/**\n *\n * client\n *\n */\nimport { createParser } from './parser.mjs';\nimport { isObject } from './utils.mjs';\nimport { TOKEN_HEADER_KEY, } from './common.mjs';\n/** This file is the entry point for browsers, re-export common elements. */\nexport * from './common.mjs';\n/**\n * Creates a disposable GraphQL over SSE client to transmit\n * GraphQL operation results.\n *\n * If you have an HTTP/2 server, it is recommended to use the client\n * in \"distinct connections mode\" (`singleConnection = false`) which will\n * create a new SSE connection for each subscribe. This is the default.\n *\n * However, when dealing with HTTP/1 servers from a browser, consider using\n * the \"single connection mode\" (`singleConnection = true`) which will\n * use only one SSE connection.\n *\n * @category Client\n */\nexport function createClient(options) {\n    const { singleConnection = false, lazy = true, lazyCloseTimeout = 0, onNonLazyError = console.error, \n    /**\n     * Generates a v4 UUID to be used as the ID using `Math`\n     * as the random number generator. Supply your own generator\n     * in case you need more uniqueness.\n     *\n     * Reference: https://gist.github.com/jed/982883\n     */\n    generateID = function generateUUID() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }, retryAttempts = 5, retry = async function randomisedExponentialBackoff(retries) {\n        let retryDelay = 1000; // start with 1s delay\n        for (let i = 0; i < retries; i++) {\n            retryDelay *= 2;\n        }\n        await new Promise((resolve) => setTimeout(resolve, retryDelay +\n            // add random timeout from 300ms to 3s\n            Math.floor(Math.random() * (3000 - 300) + 300)));\n    }, credentials = 'same-origin', referrer, referrerPolicy, onMessage, on: clientOn, } = options;\n    const fetchFn = (options.fetchFn || fetch);\n    const AbortControllerImpl = (options.abortControllerImpl ||\n        AbortController);\n    // we dont use yet another AbortController here because of\n    // node's max EventEmitters listeners being only 10\n    const client = (() => {\n        let disposed = false;\n        const listeners = [];\n        return {\n            get disposed() {\n                return disposed;\n            },\n            onDispose(cb) {\n                if (disposed) {\n                    // empty the call stack and then call the cb\n                    setTimeout(() => cb(), 0);\n                    return () => {\n                        // noop\n                    };\n                }\n                listeners.push(cb);\n                return () => {\n                    listeners.splice(listeners.indexOf(cb), 1);\n                };\n            },\n            dispose() {\n                if (disposed)\n                    return;\n                disposed = true;\n                // we copy the listeners so that onDispose unlistens dont \"pull the rug under our feet\"\n                for (const listener of [...listeners]) {\n                    listener();\n                }\n            },\n        };\n    })();\n    let connCtrl, conn, locks = 0, retryingErr = null, retries = 0;\n    async function getOrConnect() {\n        try {\n            if (client.disposed)\n                throw new Error('Client has been disposed');\n            return await (conn !== null && conn !== void 0 ? conn : (conn = (async () => {\n                var _a, _b, _c;\n                if (retryingErr) {\n                    await retry(retries);\n                    // connection might've been aborted while waiting for retry\n                    if (connCtrl.signal.aborted)\n                        throw new Error('Connection aborted by the client');\n                    retries++;\n                }\n                (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connecting) === null || _a === void 0 ? void 0 : _a.call(clientOn, !!retryingErr);\n                // we must create a new controller here because lazy mode aborts currently active ones\n                connCtrl = new AbortControllerImpl();\n                const unlistenDispose = client.onDispose(() => connCtrl.abort());\n                connCtrl.signal.addEventListener('abort', () => {\n                    unlistenDispose();\n                    conn = undefined;\n                });\n                const url = typeof options.url === 'function'\n                    ? await options.url()\n                    : options.url;\n                if (connCtrl.signal.aborted)\n                    throw new Error('Connection aborted by the client');\n                const headers = typeof options.headers === 'function'\n                    ? await options.headers()\n                    : (_b = options.headers) !== null && _b !== void 0 ? _b : {};\n                if (connCtrl.signal.aborted)\n                    throw new Error('Connection aborted by the client');\n                let res;\n                try {\n                    res = await fetchFn(url, {\n                        signal: connCtrl.signal,\n                        method: 'PUT',\n                        credentials,\n                        referrer,\n                        referrerPolicy,\n                        headers,\n                    });\n                }\n                catch (err) {\n                    throw new NetworkError(err);\n                }\n                if (res.status !== 201)\n                    throw new NetworkError(res);\n                const token = await res.text();\n                headers[TOKEN_HEADER_KEY] = token;\n                const connected = await connect({\n                    signal: connCtrl.signal,\n                    headers,\n                    credentials,\n                    referrer,\n                    referrerPolicy,\n                    url,\n                    fetchFn,\n                    onMessage: (msg) => {\n                        var _a;\n                        (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.message) === null || _a === void 0 ? void 0 : _a.call(clientOn, msg);\n                        onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg); // @deprecated\n                    },\n                });\n                (_c = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connected) === null || _c === void 0 ? void 0 : _c.call(clientOn, !!retryingErr);\n                connected.waitForThrow().catch(() => (conn = undefined));\n                return connected;\n            })()));\n        }\n        catch (err) {\n            // whatever problem happens during connect means the connection was not established\n            conn = undefined;\n            throw err;\n        }\n    }\n    // non-lazy mode always holds one lock to persist the connection\n    if (singleConnection && !lazy) {\n        (async () => {\n            locks++;\n            for (;;) {\n                try {\n                    const { waitForThrow } = await getOrConnect();\n                    await waitForThrow();\n                }\n                catch (err) {\n                    if (client.disposed)\n                        return;\n                    // all non-network errors are worth reporting immediately\n                    if (!(err instanceof NetworkError))\n                        return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(err);\n                    // was a network error, get rid of the current connection to ensure retries\n                    conn = undefined;\n                    // retries are not allowed or we tried to many times, report error\n                    if (!retryAttempts || retries >= retryAttempts)\n                        return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(err);\n                    // try again\n                    retryingErr = err;\n                }\n            }\n        })();\n    }\n    function subscribe(request, sink, on) {\n        if (!singleConnection) {\n            // distinct connections mode\n            const control = new AbortControllerImpl();\n            const unlisten = client.onDispose(() => {\n                unlisten();\n                control.abort();\n            });\n            (async () => {\n                var _a, _b, _c, _d, _e;\n                let retryingErr = null, retries = 0;\n                for (;;) {\n                    try {\n                        if (retryingErr) {\n                            await retry(retries);\n                            // connection might've been aborted while waiting for retry\n                            if (control.signal.aborted)\n                                throw new Error('Connection aborted by the client');\n                            retries++;\n                        }\n                        (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connecting) === null || _a === void 0 ? void 0 : _a.call(clientOn, !!retryingErr);\n                        (_b = on === null || on === void 0 ? void 0 : on.connecting) === null || _b === void 0 ? void 0 : _b.call(on, !!retryingErr);\n                        const url = typeof options.url === 'function'\n                            ? await options.url()\n                            : options.url;\n                        if (control.signal.aborted)\n                            throw new Error('Connection aborted by the client');\n                        const headers = typeof options.headers === 'function'\n                            ? await options.headers()\n                            : (_c = options.headers) !== null && _c !== void 0 ? _c : {};\n                        if (control.signal.aborted)\n                            throw new Error('Connection aborted by the client');\n                        const { getResults } = await connect({\n                            signal: control.signal,\n                            headers: {\n                                ...headers,\n                                'content-type': 'application/json; charset=utf-8',\n                            },\n                            credentials,\n                            referrer,\n                            referrerPolicy,\n                            url,\n                            body: JSON.stringify(request),\n                            fetchFn,\n                            onMessage: (msg) => {\n                                var _a, _b;\n                                (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.message) === null || _a === void 0 ? void 0 : _a.call(clientOn, msg);\n                                (_b = on === null || on === void 0 ? void 0 : on.message) === null || _b === void 0 ? void 0 : _b.call(on, msg);\n                                onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg); // @deprecated\n                            },\n                        });\n                        (_d = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connected) === null || _d === void 0 ? void 0 : _d.call(clientOn, !!retryingErr);\n                        (_e = on === null || on === void 0 ? void 0 : on.connected) === null || _e === void 0 ? void 0 : _e.call(on, !!retryingErr);\n                        for await (const result of getResults()) {\n                            // only after receiving results are future connects not considered retries.\n                            // this is because a client might successfully connect, but the server\n                            // ends up terminating the connection afterwards before streaming anything.\n                            // of course, if the client completes the subscription, this loop will\n                            // break and therefore stop the stream (it wont reconnect)\n                            retryingErr = null;\n                            retries = 0;\n                            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                            sink.next(result);\n                        }\n                        return control.abort();\n                    }\n                    catch (err) {\n                        if (control.signal.aborted)\n                            return;\n                        // all non-network errors are worth reporting immediately\n                        if (!(err instanceof NetworkError))\n                            throw err;\n                        // retries are not allowed or we tried to many times, report error\n                        if (!retryAttempts || retries >= retryAttempts)\n                            throw err;\n                        // try again\n                        retryingErr = err;\n                    }\n                }\n            })()\n                .then(() => sink.complete())\n                .catch((err) => sink.error(err));\n            return () => control.abort();\n        }\n        // single connection mode\n        locks++;\n        const control = new AbortControllerImpl();\n        const unlisten = client.onDispose(() => {\n            unlisten();\n            control.abort();\n        });\n        (async () => {\n            const operationId = generateID();\n            request = {\n                ...request,\n                extensions: { ...request.extensions, operationId },\n            };\n            let complete = null;\n            for (;;) {\n                complete = null;\n                try {\n                    const { url, headers, getResults } = await getOrConnect();\n                    let res;\n                    try {\n                        res = await fetchFn(url, {\n                            signal: control.signal,\n                            method: 'POST',\n                            credentials,\n                            referrer,\n                            referrerPolicy,\n                            headers: {\n                                ...headers,\n                                'content-type': 'application/json; charset=utf-8',\n                            },\n                            body: JSON.stringify(request),\n                        });\n                    }\n                    catch (err) {\n                        throw new NetworkError(err);\n                    }\n                    if (res.status !== 202)\n                        throw new NetworkError(res);\n                    complete = async () => {\n                        let res;\n                        try {\n                            const control = new AbortControllerImpl();\n                            const unlisten = client.onDispose(() => {\n                                unlisten();\n                                control.abort();\n                            });\n                            res = await fetchFn(url + '?operationId=' + operationId, {\n                                signal: control.signal,\n                                method: 'DELETE',\n                                credentials,\n                                referrer,\n                                referrerPolicy,\n                                headers,\n                            });\n                        }\n                        catch (err) {\n                            throw new NetworkError(err);\n                        }\n                        if (res.status !== 200)\n                            throw new NetworkError(res);\n                    };\n                    for await (const result of getResults({\n                        signal: control.signal,\n                        operationId,\n                    })) {\n                        // only after receiving results are future connects not considered retries.\n                        // this is because a client might successfully connect, but the server\n                        // ends up terminating the connection afterwards before streaming anything.\n                        // of course, if the client completes the subscription, this loop will\n                        // break and therefore stop the stream (it wont reconnect)\n                        retryingErr = null;\n                        retries = 0;\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        sink.next(result);\n                    }\n                    complete = null; // completed by the server\n                    return control.abort();\n                }\n                catch (err) {\n                    if (control.signal.aborted)\n                        return await (complete === null || complete === void 0 ? void 0 : complete());\n                    // all non-network errors are worth reporting immediately\n                    if (!(err instanceof NetworkError)) {\n                        control.abort(); // TODO: tests for making sure the control's aborted\n                        throw err;\n                    }\n                    // was a network error, get rid of the current connection to ensure retries\n                    // but only if the client is running in lazy mode (otherwise the non-lazy lock will get rid of the connection)\n                    if (lazy) {\n                        conn = undefined;\n                    }\n                    // retries are not allowed or we tried to many times, report error\n                    if (!retryAttempts || retries >= retryAttempts) {\n                        control.abort(); // TODO: tests for making sure the control's aborted\n                        throw err;\n                    }\n                    // try again\n                    retryingErr = err;\n                }\n                finally {\n                    // release lock if subscription is aborted\n                    if (control.signal.aborted && --locks === 0) {\n                        if (isFinite(lazyCloseTimeout) && lazyCloseTimeout > 0) {\n                            // allow for the specified calmdown time and then close the\n                            // connection, only if no lock got created in the meantime and\n                            // if the connection is still open\n                            setTimeout(() => {\n                                if (!locks)\n                                    connCtrl.abort();\n                            }, lazyCloseTimeout);\n                        }\n                        else {\n                            // otherwise close immediately\n                            connCtrl.abort();\n                        }\n                    }\n                }\n            }\n        })()\n            .then(() => sink.complete())\n            .catch((err) => sink.error(err));\n        return () => control.abort();\n    }\n    return {\n        subscribe,\n        iterate(request, on) {\n            const pending = [];\n            const deferred = {\n                done: false,\n                error: null,\n                resolve: () => {\n                    // noop\n                },\n            };\n            const dispose = subscribe(request, {\n                next(val) {\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    pending.push(val);\n                    deferred.resolve();\n                },\n                error(err) {\n                    deferred.done = true;\n                    deferred.error = err;\n                    deferred.resolve();\n                },\n                complete() {\n                    deferred.done = true;\n                    deferred.resolve();\n                },\n            }, on);\n            const iterator = (async function* iterator() {\n                for (;;) {\n                    if (!pending.length) {\n                        // only wait if there are no pending messages available\n                        await new Promise((resolve) => (deferred.resolve = resolve));\n                    }\n                    // first flush\n                    while (pending.length) {\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        yield pending.shift();\n                    }\n                    // then error\n                    if (deferred.error) {\n                        throw deferred.error;\n                    }\n                    // or complete\n                    if (deferred.done) {\n                        return;\n                    }\n                }\n            })();\n            iterator.throw = async (err) => {\n                if (!deferred.done) {\n                    deferred.done = true;\n                    deferred.error = err;\n                    deferred.resolve();\n                }\n                return { done: true, value: undefined };\n            };\n            iterator.return = async () => {\n                dispose();\n                return { done: true, value: undefined };\n            };\n            return iterator;\n        },\n        dispose() {\n            client.dispose();\n        },\n    };\n}\n/**\n * A network error caused by the client or an unexpected response from the server.\n *\n * Network errors are considered retryable, all others error types will be reported\n * immediately.\n *\n * To avoid bundling DOM typings (because the client can run in Node env too),\n * you should supply the `Response` generic depending on your Fetch implementation.\n *\n * @category Client\n */\nexport class NetworkError extends Error {\n    constructor(msgOrErrOrResponse) {\n        let message, response;\n        if (isResponseLike(msgOrErrOrResponse)) {\n            response = msgOrErrOrResponse;\n            message =\n                'Server responded with ' +\n                    msgOrErrOrResponse.status +\n                    ': ' +\n                    msgOrErrOrResponse.statusText;\n        }\n        else if (msgOrErrOrResponse instanceof Error)\n            message = msgOrErrOrResponse.message;\n        else\n            message = String(msgOrErrOrResponse);\n        super(message);\n        this.name = this.constructor.name;\n        this.response = response;\n    }\n}\nfunction isResponseLike(val) {\n    return (isObject(val) &&\n        typeof val['ok'] === 'boolean' &&\n        typeof val['status'] === 'number' &&\n        typeof val['statusText'] === 'string');\n}\nasync function connect(options) {\n    const { signal, url, credentials, headers, body, referrer, referrerPolicy, fetchFn, onMessage, } = options;\n    const waiting = {};\n    const queue = {};\n    let res;\n    try {\n        res = await fetchFn(url, {\n            signal,\n            method: body ? 'POST' : 'GET',\n            credentials,\n            referrer,\n            referrerPolicy,\n            headers: {\n                ...headers,\n                accept: 'text/event-stream',\n            },\n            body,\n        });\n    }\n    catch (err) {\n        throw new NetworkError(err);\n    }\n    if (!res.ok)\n        throw new NetworkError(res);\n    if (!res.body)\n        throw new Error('Missing response body');\n    let error = null;\n    let waitingForThrow;\n    (async () => {\n        var _a;\n        try {\n            const parse = createParser();\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            for await (const chunk of toAsyncIterable(res.body)) {\n                if (typeof chunk === 'string')\n                    throw (error = new Error(`Unexpected string chunk \"${chunk}\"`)); // set error as fatal indicator\n                // read chunk and if messages are ready, yield them\n                let msgs;\n                try {\n                    msgs = parse(chunk);\n                }\n                catch (err) {\n                    throw (error = err); // set error as fatal indicator\n                }\n                if (!msgs)\n                    continue;\n                for (const msg of msgs) {\n                    try {\n                        onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg);\n                    }\n                    catch (err) {\n                        throw (error = err); // set error as fatal indicator\n                    }\n                    const operationId = msg.data && 'id' in msg.data\n                        ? msg.data.id // StreamDataForID\n                        : ''; // StreamData\n                    if (!(operationId in queue))\n                        queue[operationId] = [];\n                    switch (msg.event) {\n                        case 'next':\n                            if (operationId)\n                                queue[operationId].push(msg.data.payload);\n                            else\n                                queue[operationId].push(msg.data);\n                            break;\n                        case 'complete':\n                            queue[operationId].push('complete');\n                            break;\n                        default:\n                            throw (error = new Error(`Unexpected message event \"${msg.event}\"`)); // set error as fatal indicator\n                    }\n                    (_a = waiting[operationId]) === null || _a === void 0 ? void 0 : _a.proceed();\n                }\n            }\n            // some browsers (like Safari) closes the connection without errors even on abrupt server shutdowns,\n            // we therefore make sure that no stream is active and waiting for results (not completed)\n            if (Object.keys(waiting).length) {\n                throw new Error('Connection closed while having active streams');\n            }\n        }\n        catch (err) {\n            if (!error && Object.keys(waiting).length) {\n                // we assume the error is most likely a NetworkError because there are listeners waiting for events.\n                // additionally, the `error` is another indicator because we set it early if the error is considered fatal\n                error = new NetworkError(err);\n            }\n            else {\n                error = err;\n            }\n            waitingForThrow === null || waitingForThrow === void 0 ? void 0 : waitingForThrow(error);\n        }\n        finally {\n            Object.values(waiting).forEach(({ proceed }) => proceed());\n        }\n    })();\n    return {\n        url,\n        headers,\n        waitForThrow: () => new Promise((_, reject) => {\n            if (error)\n                return reject(error);\n            waitingForThrow = reject;\n        }),\n        async *getResults(options) {\n            var _a;\n            const { signal, operationId = '' } = options !== null && options !== void 0 ? options : {};\n            // operationId === '' ? StreamData : StreamDataForID\n            try {\n                for (;;) {\n                    while ((_a = queue[operationId]) === null || _a === void 0 ? void 0 : _a.length) {\n                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                        const result = queue[operationId].shift();\n                        if (result === 'complete')\n                            return;\n                        yield result;\n                    }\n                    if (error)\n                        throw error;\n                    if (signal === null || signal === void 0 ? void 0 : signal.aborted)\n                        throw new Error('Getting results aborted by the client');\n                    await new Promise((resolve) => {\n                        const proceed = () => {\n                            signal === null || signal === void 0 ? void 0 : signal.removeEventListener('abort', proceed);\n                            delete waiting[operationId];\n                            resolve();\n                        };\n                        signal === null || signal === void 0 ? void 0 : signal.addEventListener('abort', proceed);\n                        waiting[operationId] = { proceed };\n                    });\n                }\n            }\n            finally {\n                delete queue[operationId];\n            }\n        },\n    };\n}\n/** Isomorphic ReadableStream to AsyncIterator converter. */\nfunction toAsyncIterable(val) {\n    // node stream is already async iterable\n    if (typeof Object(val)[Symbol.asyncIterator] === 'function') {\n        val = val;\n        return val;\n    }\n    // convert web stream to async iterable\n    return (async function* () {\n        const reader = val.getReader();\n        let result;\n        do {\n            result = await reader.read();\n            if (result.value !== undefined)\n                yield result.value;\n        } while (!result.done);\n    })();\n}\n"], "mappings": ";;;;;;;;;;;AAMO,SAAS,SAAS,KAAK;AAC1B,SAAO,OAAO,QAAQ,YAAY,QAAQ;AAC9C;;;ACMO,IAAM,mBAAmB;AASzB,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,GAAG;AACnC,MAAI;AACJ,MAAI,MAAM,UAAU,MAAM;AACtB,UAAM,IAAI,MAAM,yBAAyB,CAAC,GAAG;AACjD,SAAO;AACX;AAEO,SAAS,MAAM,KAAK;AACvB,MAAI,MAAM,UAAU,IAAI,KAAK;AAAA;AAC7B,MAAI,IAAI,MAAM;AACV,WAAO;AACP,WAAO,KAAK,UAAU,IAAI,IAAI;AAAA,EAClC;AACA,SAAO;AACP,SAAO;AACX;AAEO,SAAS,gBAAgB,GAAG,MAAM;AACrC,MAAI,MAAM;AACN,QAAI;AACA,aAAO,KAAK,MAAM,IAAI;AAAA,IAC1B,QACM;AACF,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACzC;AAAA,EACJ;AACA,MAAI,MAAM,UAAU,CAAC;AACjB,UAAM,IAAI,MAAM,iDAAiD;AACrE,SAAQ,QAAQ;AACpB;AAMO,SAAS,gBAAgB,KAAK;AACjC,SAAO,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM;AACxD;AAMO,SAAS,iBAAiB,KAAK;AAClC,SAAQ,SAAS,GAAG,KAChB,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM,cAC7C,OAAO,IAAI,WAAW,cACtB,OAAO,IAAI,UAAU,cACrB,OAAO,IAAI,SAAS;AAC5B;;;ACrEA;AAWO,SAAS,cAAc,SAAS;AACnC,QAAM,EAAE,UAAAA,YAAW,UAAiB,SAAAC,WAAU,SAAgB,WAAAC,aAAY,WAAkB,QAAQ,eAAe,SAAS,2BAA2B,KAAK;AACxJ,QAAI;AACJ,UAAM,cAAc,IAAI,QAAQ,IAAI,gBAAgB;AACpD,QAAI;AACA,aAAO,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,EAAE,IAAI;AAC/D,UAAM,WAAW,IAAI,KAAK,KAAK,IAAI,SAAS,QAAQ,OAAO,SAAS,KAAK,IAAI,mBAAmB,EAAE,aAAa,IAAI,eAAe;AAClI,QAAI;AACA,aAAO;AACX,WAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AAClE,YAAM,IAAK,KAAK,OAAO,IAAI,KAAM,GAAG,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACnE,aAAO,EAAE,SAAS,EAAE;AAAA,IACxB,CAAC;AAAA,EACL,GAAG,WAAW,SAAS,aAAa,aAAa,QAAQ,WAAY,IAAI;AACzE,QAAM,UAAU,CAAC;AACjB,WAAS,aAAa,OAAO;AACzB,UAAM,MAAM,CAAC;AACb,QAAI;AACJ,UAAM,QAAQ,MAAM;AAChB,YAAM,UAAU,CAAC;AACjB,YAAM,WAAW;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,QAEf;AAAA,MACJ;AACA,qBAAe,UAAU;AACrB,sBAAc,MAAM;AAEpB,YAAI,OAAO,UAAU;AACjB,iBAAO,QAAQ,KAAK;AAExB,mBAAW,MAAM,OAAO,OAAO,GAAG,GAAG;AACjC,cAAI,iBAAiB,EAAE,GAAG;AACtB,kBAAM,GAAG,OAAO,MAAS;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,WAAY,gBAAgBC,YAAW;AACzC,mBAAS;AACL,cAAI,CAAC,QAAQ,QAAQ;AAEjB,kBAAM,IAAI,QAAQ,CAAC,YAAa,SAAS,UAAU,OAAQ;AAAA,UAC/D;AAEA,iBAAO,QAAQ,QAAQ;AAEnB,kBAAM,QAAQ,MAAM;AAAA,UACxB;AAEA,cAAI,SAAS,OAAO;AAChB,kBAAM,SAAS;AAAA,UACnB;AAEA,cAAI,SAAS,MAAM;AACf;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,EAAG;AACH,eAAS,QAAQ,OAAO,QAAQ;AAC5B,YAAI,CAAC,SAAS,MAAM;AAChB,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AACjB,gBAAM,QAAQ;AAAA,QAClB;AACA,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,eAAS,SAAS,YAAY;AAC1B,YAAI,CAAC,SAAS,MAAM;AAChB,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,gBAAM,QAAQ;AAAA,QAClB;AACA,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,aAAO;AAAA,QACH,KAAK,KAAK;AACN,kBAAQ,KAAK,GAAG;AAChB,mBAAS,QAAQ;AAAA,QACrB;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,GAAG;AACH,QAAI,aAAa;AACjB,WAAO;AAAA,MACH,IAAI,OAAO;AACP,eAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA,YAAY;AACR,qBAAa;AAGb,aAAK,KAAK,OAAO;AAEjB,iBAAS,YAAY,MAAM,KAAK,KAAK,OAAO,GAAG,IAAK;AACpD,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,KAAK,KAAK,KAAK,QAAQ,MAAM;AACzB,SAAC,YAAY;AACT,cAAI,gBAAgB,MAAM,GAAG;AAEzB,2BAAe,QAAQ,QAAQ;AAC3B,oBAAM,cAAc,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,KAAK,IAAI;AAChG,kBAAI;AACA,uBAAO;AACX,mBAAK,KAAK,MAAM;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM,OACA;AAAA,kBACE,IAAI;AAAA,kBACJ,SAAS;AAAA,gBACb,IACE;AAAA,cACV,CAAC,CAAC;AAAA,YACN;AAAA,UACJ,OACK;AAED,kBAAM,cAAc,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,KAAK,MAAM;AAClG,gBAAI;AACA,uBAAS;AACb,iBAAK,KAAK,MAAM;AAAA,cACZ,OAAO;AAAA,cACP,MAAM,OACA;AAAA,gBACE,IAAI;AAAA,gBACJ,SAAS;AAAA,cACb,IACE;AAAA,YACV,CAAC,CAAC;AAAA,UACN;AACA,eAAK,KAAK,MAAM;AAAA,YACZ,OAAO;AAAA,YACP,MAAM,OAAO,EAAE,IAAI,KAAK,IAAI;AAAA,UAChC,CAAC,CAAC;AACF,iBAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,KAAK,GAAG;AAClF,cAAI,CAAC,MAAM;AAGP,kBAAM,KAAK,SAAS,OAAO;AAAA,UAC/B,OACK;AACD,mBAAO,IAAI,IAAI;AAAA,UACnB;AAAA,QACJ,GAAG,EAAE,MAAM,KAAK,SAAS,KAAK;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACA,iBAAe,QAAQ,KAAK,QAAQ;AAChC,QAAI;AACJ,UAAM,oBAAoB,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,MAAM;AAClH,QAAI,WAAW,iBAAiB;AAC5B,aAAO;AAAA,aACF,kBAAkB,iBAAiB,KACxC,gBAAgB,iBAAiB;AACjC,aAAO;AAAA;AAAA;AAAA,QAGH,KAAM,OAAO,YAAY,aACnB,MAAM,QAAQ,KAAK,MAAM,IACzB;AAAA,QACN,UAAU;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,aACK;AACL,aAAO;AAAA,SACN;AAGD,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,oCAAoC;AACxD,YAAM,EAAE,eAAe,UAAU,IAAI;AACrC,UAAI;AACJ,UAAI;AACA,gBAAQ,MAAM,OAAO,KAAK;AAAA,MAC9B,SACO,KAAK;AACR,eAAO;AAAA,UACH,KAAK,UAAU;AAAA,YACX,QAAQ;AAAA,cACJ,eAAe,QACT;AAAA,gBACE,SAAS,IAAI;AAAA;AAAA;AAAA,cAGjB,IACE;AAAA,YACV;AAAA,UACJ,CAAC;AAAA,UACD;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS,EAAE,gBAAgB,kCAAkC;AAAA,UACjE;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,oBAAoB;AAAA,QACtB;AAAA,QACA,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,cAAe,OAAO,YAAY,aAC5B,MAAM,QAAQ,KAAK,MAAM,IACzB;AAAA,MACV;AACA,aAAO;AAAA,QACH,GAAG;AAAA,QACH,QAAQ,OAAO,WAAW,aACpB,MAAM,OAAO,KAAK,iBAAiB,IACnC;AAAA,MACV;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACA,YAAM,MAAM,gBAAgB,KAAK,UAAU,KAAK,aAAa;AAC7D,UAAI,CAAC;AACD,cAAM;AACV,kBAAY,IAAI;AAAA,IACpB,QACM;AACF,aAAO;AAAA,QACH,KAAK,UAAU;AAAA,UACX,QAAQ,CAAC,EAAE,SAAS,iCAAiC,CAAC;AAAA,QAC1D,CAAC;AAAA,QACD;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS,EAAE,gBAAgB,kCAAkC;AAAA,QACjE;AAAA,MACJ;AAAA,IACJ;AAGA,QAAI,cAAc,cAAc,IAAI,WAAW,OAAO;AAClD,aAAO;AAAA,QACH,KAAK,UAAU;AAAA,UACX,QAAQ,CAAC,EAAE,SAAS,oCAAoC,CAAC;AAAA,QAC7D,CAAC;AAAA,QACD;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,OAAO;AAAA,YACP,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,UAAM,iBAAiBH,UAAS,KAAK,QAAQ,KAAK,QAAQ;AAC1D,QAAI,eAAe,QAAQ;AACvB,UAAI,IAAI,QAAQ,IAAI,QAAQ,MAAM,qBAAqB;AAKnD,eAAO;AAAA,UACH,KAAK,KAAK;AAAA,UACV,UAAU;AACN,mBAAO,EAAE,QAAQ,eAAe;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,QACH,KAAK,UAAU,EAAE,QAAQ,eAAe,CAAC;AAAA,QACzC;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS,EAAE,gBAAgB,kCAAkC;AAAA,QACjE;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,MACH,KAAK,KAAK;AAAA,MACV,MAAM,UAAU;AACZ,cAAM,SAAS,OAAO,cAAc,iBAC9BE,WAAU,IAAI,IACdD,SAAQ,IAAI;AAClB,cAAM,cAAc,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,cAAc,KAAK,MAAM,MAAM;AACrI,YAAI;AACA,iBAAO;AACX,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,eAAe,QAAQ,KAAK;AAC/B,QAAI,IAAI,IAAI;AACZ,UAAM,QAAQ,MAAM,aAAa,GAAG;AACpC,QAAI,WAAW,KAAK;AAChB,aAAO;AAEX,UAAM,SAAS,IAAI,QAAQ,IAAI,QAAQ,KAAK;AAC5C,UAAM,SAAS,OAAO,UAAU,WAAW,QAAQ,KAAK,IAAI;AAC5D,QAAI,WAAW,qBAAqB;AAChC,YAAM,gBAAgB,OAAO,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AAChG,UAAI,WAAW,aAAa;AACxB,eAAO;AAGX,UAAI,CAAC,QAAQ;AACT,cAAMG,oBAAmB,MAAM,SAAS,GAAG;AAC3C,YAAI,WAAWA,iBAAgB;AAC3B,iBAAOA;AACX,cAAMC,UAASD;AACf,cAAM,iBAAiB,aAAa,IAAI;AAExC,uBAAe,IAAI,EAAE,IAAI;AACzB,cAAME,YAAW,MAAM,QAAQ,KAAKD,OAAM;AAC1C,YAAI,WAAWC,SAAQ;AACnB,iBAAOA;AACX,cAAMC,UAAS,MAAMD,UAAS,QAAQ;AACtC,YAAI,gBAAgBC,OAAM;AACtB,yBAAe,IAAI,EAAE,IAAIA;AAC7B,uBAAe,KAAKD,UAAS,KAAK,KAAKC,SAAQ,IAAI;AACnD,eAAO;AAAA,UACH,eAAe,UAAU;AAAA,UACzB;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS;AAAA,cACL,YAAY;AAAA,cACZ,iBAAiB;AAAA,cACjB,oBAAoB;AAAA,cACpB,gBAAgB;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,OAAO,MAAM;AACb,eAAO;AAAA,UACH,KAAK,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,sBAAsB,CAAC,EAAE,CAAC;AAAA,UAC/D;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS;AAAA,cACL,gBAAgB;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,QACH,OAAO,UAAU;AAAA,QACjB;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,oBAAoB;AAAA,YACpB,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,OAAO,UAAU,UAAU;AAC3B,aAAO,CAAC,MAAM,EAAE,QAAQ,KAAK,YAAY,YAAY,CAAC;AAAA,IAC1D;AAEA,QAAI,IAAI,WAAW,OAAO;AACtB,UAAI,CAAC,CAAC,OAAO,YAAY,EAAE,SAAS,MAAM,GAAG;AACzC,eAAO,CAAC,MAAM,EAAE,QAAQ,KAAK,YAAY,iBAAiB,CAAC;AAAA,MAC/D;AAEA,UAAI,QAAQ;AACR,eAAO;AAAA,UACH,KAAK,UAAU;AAAA,YACX,QAAQ,CAAC,EAAE,SAAS,4BAA4B,CAAC;AAAA,UACrD,CAAC;AAAA,UACD;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS;AAAA,cACL,gBAAgB;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,cAAQ,KAAK,IAAI,aAAa,KAAK;AACnC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,IAAI,WAAW,UAAU;AAG9B,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,UACH,KAAK,UAAU;AAAA,YACX,QAAQ,CAAC,EAAE,SAAS,mBAAmB,CAAC;AAAA,UAC5C,CAAC;AAAA,UACD;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS;AAAA,cACL,gBAAgB;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAMC,QAAO,IAAI,KAAK,KAAK,IAAI,SAAS,QAAQ,OAAO,SAAS,KAAK,IAAI,mBAAmB,EAAE,aAAa,IAAI,aAAa;AAC5H,UAAI,CAACA,OAAM;AACP,eAAO;AAAA,UACH,KAAK,UAAU;AAAA,YACX,QAAQ,CAAC,EAAE,SAAS,0BAA0B,CAAC;AAAA,UACnD,CAAC;AAAA,UACD;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SAAS;AAAA,cACL,gBAAgB;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,KAAK,OAAO,IAAIA,KAAI;AAC1B,UAAI,iBAAiB,EAAE;AACnB,WAAG,OAAO,MAAS;AACvB,aAAO,OAAO,IAAIA,KAAI;AACtB,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ,WACS,IAAI,WAAW,SAAS,IAAI,WAAW,QAAQ;AAEpD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,CAAC,QAAQ;AAEd,aAAO;AAAA,QACH,KAAK,UAAU;AAAA,UACX,QAAQ,CAAC,EAAE,SAAS,mBAAmB,CAAC;AAAA,QAC5C,CAAC;AAAA,QACD;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,CAAC,OAAO,iBAAiB,kBAAkB,EAAE,SAAS,MAAM,GAAG;AAChE,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,mBAAmB,MAAM,SAAS,GAAG;AAC3C,QAAI,WAAW,gBAAgB;AAC3B,aAAO;AACX,UAAM,SAAS;AACf,UAAM,OAAO,QAAQ,MAAM,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,QAAQ,OAAO,SAAS,KAAK,EAAE;AAC3I,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,QACH,KAAK,UAAU;AAAA,UACX,QAAQ,CAAC,EAAE,SAAS,0BAA0B,CAAC;AAAA,QACnD,CAAC;AAAA,QACD;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,QAAQ,OAAO,KAAK;AACpB,aAAO;AAAA,QACH,KAAK,UAAU;AAAA,UACX,QAAQ,CAAC,EAAE,SAAS,mCAAmC,CAAC;AAAA,QAC5D,CAAC;AAAA,QACD;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,YACL,gBAAgB;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,IAAI,IAAI,IAAI;AACnB,UAAM,WAAW,MAAM,QAAQ,KAAK,MAAM;AAC1C,QAAI,WAAW,QAAQ;AACnB,aAAO;AAEX,QAAI,EAAE,QAAQ,OAAO,MAAM;AACvB,aAAO;AAAA,QACH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,YAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,SAAS,MAAM,SAAS,QAAQ;AAEtC,QAAI,EAAE,QAAQ,OAAO,MAAM;AACvB,UAAI,iBAAiB,MAAM;AACvB,eAAO,OAAO,MAAS;AAC3B,UAAI,EAAE,QAAQ,OAAO,MAAM;AACvB,eAAO;AAAA,UACH;AAAA,UACA;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,gBAAgB,MAAM;AACtB,aAAO,IAAI,IAAI,IAAI;AAEvB,WAAO,KAAK,SAAS,KAAK,KAAK,QAAQ,IAAI;AAC3C,WAAO,CAAC,MAAM,EAAE,QAAQ,KAAK,YAAY,WAAW,CAAC;AAAA,EACzD;AACJ;AACA,eAAe,SAAS,KAAK;AACzB,MAAI,IAAI,IAAI;AACZ,QAAM,SAAS,CAAC;AAChB,MAAI;AACA,YAAQ,MAAM;AAAA,MACV,KAAK,IAAI,WAAW,OAAO;AACvB,YAAI;AACA,gBAAM,CAAC,EAAE,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG;AACpC,gBAAM,eAAe,IAAI,gBAAgB,MAAM;AAC/C,iBAAO,iBAAiB,KAAK,aAAa,IAAI,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK;AACjG,iBAAO,SAAS,KAAK,aAAa,IAAI,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK;AACjF,gBAAM,YAAY,aAAa,IAAI,WAAW;AAC9C,cAAI;AACA,mBAAO,YAAY,KAAK,MAAM,SAAS;AAC3C,gBAAM,aAAa,aAAa,IAAI,YAAY;AAChD,cAAI;AACA,mBAAO,aAAa,KAAK,MAAM,UAAU;AAAA,QACjD,QACM;AACF,gBAAM,IAAI,MAAM,gBAAgB;AAAA,QACpC;AACA;AAAA,MACJ;AAAA,MACA,MAAK,IAAI,WAAW,YACd,KAAK,IAAI,QAAQ,IAAI,cAAc,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,kBAAkB,KAC3G;AACI,YAAI,CAAC,IAAI,MAAM;AACX,gBAAM,IAAI,MAAM,cAAc;AAAA,QAClC;AACA,cAAM,OAAO,OAAO,IAAI,SAAS,aAAa,MAAM,IAAI,KAAK,IAAI,IAAI;AACrE,cAAM,OAAO,OAAO,SAAS,WAAW,KAAK,MAAM,IAAI,IAAI;AAC3D,YAAI,CAAC,SAAS,IAAI,GAAG;AACjB,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QACjD;AAEA,eAAO,gBAAgB,KAAK;AAE5B,eAAO,QAAQ,KAAK;AAEpB,eAAO,YAAY,KAAK;AAExB,eAAO,aAAa,KAAK;AACzB;AAAA,MACJ;AAAA,MACJ;AACI,eAAO;AAAA,UACH;AAAA,UACA;AAAA,YACI,QAAQ;AAAA,YACR,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,IACR;AACA,QAAI,OAAO,SAAS;AAChB,YAAM,IAAI,MAAM,eAAe;AACnC,QAAI,OAAO,OAAO,UAAU;AACxB,YAAM,IAAI,MAAM,eAAe;AACnC,QAAI,OAAO,aAAa,SACnB,OAAO,OAAO,cAAc,YAAY,MAAM,QAAQ,OAAO,SAAS,IAAI;AAC3E,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACvC;AACA,QAAI,OAAO,cAAc,SACpB,OAAO,OAAO,eAAe,YAC1B,MAAM,QAAQ,OAAO,UAAU,IAAI;AACvC,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACxC;AAEA,WAAO;AAAA,EACX,SACO,KAAK;AACR,WAAO;AAAA,MACH,KAAK,UAAU;AAAA,QACX,QAAQ;AAAA,UACJ,eAAe,QACT;AAAA,YACE,SAAS,IAAI;AAAA;AAAA;AAAA,UAGjB,IACE;AAAA,QACV;AAAA,MACJ,CAAC;AAAA,MACD;AAAA,QACI,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,SAAS,EAAE,gBAAgB,kCAAkC;AAAA,MACjE;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,KAAK;AAErB,SAAO,MAAM,QAAQ,GAAG;AAC5B;AACO,SAAS,kBAAkB,KAAK;AACnC,SAAQ,SAAS,GAAG,MACf,UAAU,OAAQ,UAAU,OAAO,IAAI,QAAQ,QAAQ,YAAY;AAC5E;;;AC7oBA,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAaA,cAAa,SAAS,IAAI,EAAE,IAAI;AAC7C,EAAAA,cAAaA,cAAa,gBAAgB,IAAI,EAAE,IAAI;AACpD,EAAAA,cAAaA,cAAa,OAAO,IAAI,EAAE,IAAI;AAC3C,EAAAA,cAAaA,cAAa,OAAO,IAAI,EAAE,IAAI;AAC/C,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAQ/B,SAAS,eAAe;AAC3B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,yBAAyB;AAC7B,MAAI,UAAU,EAAE,OAAO,IAAI,MAAM,GAAG;AACpC,MAAI,UAAU,CAAC;AACf,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,SAASC,OAAM,OAAO;AACzB,QAAI,WAAW,QAAW;AACtB,eAAS;AACT,iBAAW;AACX,oBAAc;AAAA,IAClB,OACK;AACD,YAAM,SAAS,IAAI,WAAW,OAAO,SAAS,MAAM,MAAM;AAC1D,aAAO,IAAI,MAAM;AACjB,aAAO,IAAI,OAAO,OAAO,MAAM;AAC/B,eAAS;AAAA,IACb;AACA,UAAM,YAAY,OAAO;AACzB,QAAI,YAAY;AAChB,WAAO,WAAW,WAAW;AACzB,UAAI,wBAAwB;AACxB,YAAI,OAAO,QAAQ,MAAM,aAAa,SAAS;AAC3C,sBAAY,EAAE;AAAA,QAClB;AACA,iCAAyB;AAAA,MAC7B;AAEA,UAAI,UAAU;AACd,aAAO,WAAW,aAAa,YAAY,IAAI,EAAE,UAAU;AACvD,gBAAQ,OAAO,QAAQ,GAAG;AAAA,UACtB,KAAK,aAAa;AACd,gBAAI,gBAAgB,IAAI;AAEpB,4BAAc,WAAW;AAAA,YAC7B;AACA;AAAA;AAAA,UAEJ,KAAK,aAAa;AACd,qCAAyB;AAAA;AAAA,UAE7B,KAAK,aAAa;AACd,sBAAU;AACV;AAAA,QACR;AAAA,MACJ;AACA,UAAI,YAAY,IAAI;AAEhB;AAAA,MACJ,WACS,cAAc,SAAS;AAE5B,YAAI,QAAQ,SAAS,QAAQ,MAAM;AAE/B,cAAI,CAAC,QAAQ;AACT,kBAAM,IAAI,MAAM,uBAAuB;AAC3C,gBAAM,QAAQ,oBAAoB,QAAQ,KAAK;AAC/C,gBAAM,OAAO,gBAAgB,OAAO,QAAQ,IAAI;AAChD,kBAAQ,KAAK;AAAA,YACT;AAAA,YACA;AAAA,UACJ,CAAC;AACD,oBAAU,EAAE,OAAO,IAAI,MAAM,GAAG;AAAA,QACpC;AAAA,MACJ,WACS,cAAc,GAAG;AAEtB,cAAM,OAAO,OAAO,SAAS,WAAW,OAAO;AAI/C,cAAM,QAAQ,QAAQ,OAAO,KAAK,SAAS,GAAG,WAAW,CAAC;AAC1D,cAAM,cAAc,eAAe,KAAK,cAAc,CAAC,MAAM,aAAa,QAAQ,IAAI;AACtF,cAAM,QAAQ,QAAQ,OAAO,KAAK,SAAS,WAAW,CAAC;AACvD,gBAAQ,OAAO;AAAA,UACX,KAAK;AACD,oBAAQ,QAAQ;AAChB;AAAA,UACJ,KAAK;AAED,oBAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,OAAO,QAAQ;AAC5D;AAAA,QACR;AAAA,MACJ;AAEA,kBAAY;AACZ,oBAAc;AAAA,IAClB;AACA,QAAI,cAAc,WAAW;AAEzB,eAAS;AACT,YAAM,WAAW,CAAC,GAAG,OAAO;AAC5B,gBAAU,CAAC;AACX,aAAO;AAAA,IACX,WACS,cAAc,GAAG;AAGtB,eAAS,OAAO,SAAS,SAAS;AAClC,kBAAY;AAAA,IAChB;AAAA,EACJ;AACJ;;;ACpGO,SAAS,aAAa,SAAS;AAClC,QAAM;AAAA,IAAE,mBAAmB;AAAA,IAAO,OAAO;AAAA,IAAM,mBAAmB;AAAA,IAAG,iBAAiB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQ9F,aAAa,SAAS,eAAe;AACjC,aAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AAClE,cAAM,IAAK,KAAK,OAAO,IAAI,KAAM,GAAG,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACnE,eAAO,EAAE,SAAS,EAAE;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,IAAG,gBAAgB;AAAA,IAAG,QAAQ,eAAe,6BAA6BC,UAAS;AAC/E,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAIA,UAAS,KAAK;AAC9B,sBAAc;AAAA,MAClB;AACA,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS;AAAA,MAE/C,KAAK,MAAM,KAAK,OAAO,KAAK,MAAO,OAAO,GAAG,CAAC,CAAC;AAAA,IACvD;AAAA,IAAG,cAAc;AAAA,IAAe;AAAA,IAAU;AAAA,IAAgB;AAAA,IAAW,IAAI;AAAA,EAAU,IAAI;AACvF,QAAM,UAAW,QAAQ,WAAW;AACpC,QAAM,sBAAuB,QAAQ,uBACjC;AAGJ,QAAM,SAAU,uBAAM;AAClB,QAAI,WAAW;AACf,UAAM,YAAY,CAAC;AACnB,WAAO;AAAA,MACH,IAAI,WAAW;AACX,eAAO;AAAA,MACX;AAAA,MACA,UAAU,IAAI;AACV,YAAI,UAAU;AAEV,qBAAW,MAAM,GAAG,GAAG,CAAC;AACxB,iBAAO,MAAM;AAAA,UAEb;AAAA,QACJ;AACA,kBAAU,KAAK,EAAE;AACjB,eAAO,MAAM;AACT,oBAAU,OAAO,UAAU,QAAQ,EAAE,GAAG,CAAC;AAAA,QAC7C;AAAA,MACJ;AAAA,MACA,UAAU;AACN,YAAI;AACA;AACJ,mBAAW;AAEX,mBAAW,YAAY,CAAC,GAAG,SAAS,GAAG;AACnC,mBAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG;AACH,MAAI,UAAU,MAAM,QAAQ,GAAG,cAAc,MAAM,UAAU;AAC7D,iBAAe,eAAe;AAC1B,QAAI;AACA,UAAI,OAAO;AACP,cAAM,IAAI,MAAM,0BAA0B;AAC9C,aAAO,OAAO,SAAS,QAAQ,SAAS,SAAS,OAAQ,QAAQ,YAAY;AACzE,YAAI,IAAI,IAAI;AACZ,YAAI,aAAa;AACb,gBAAM,MAAM,OAAO;AAEnB,cAAI,SAAS,OAAO;AAChB,kBAAM,IAAI,MAAM,kCAAkC;AACtD;AAAA,QACJ;AACA,SAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,CAAC,CAAC,WAAW;AAEnJ,mBAAW,IAAI,oBAAoB;AACnC,cAAM,kBAAkB,OAAO,UAAU,MAAM,SAAS,MAAM,CAAC;AAC/D,iBAAS,OAAO,iBAAiB,SAAS,MAAM;AAC5C,0BAAgB;AAChB,iBAAO;AAAA,QACX,CAAC;AACD,cAAM,MAAM,OAAO,QAAQ,QAAQ,aAC7B,MAAM,QAAQ,IAAI,IAClB,QAAQ;AACd,YAAI,SAAS,OAAO;AAChB,gBAAM,IAAI,MAAM,kCAAkC;AACtD,cAAM,UAAU,OAAO,QAAQ,YAAY,aACrC,MAAM,QAAQ,QAAQ,KACrB,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC/D,YAAI,SAAS,OAAO;AAChB,gBAAM,IAAI,MAAM,kCAAkC;AACtD,YAAI;AACJ,YAAI;AACA,gBAAM,MAAM,QAAQ,KAAK;AAAA,YACrB,QAAQ,SAAS;AAAA,YACjB,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL,SACO,KAAK;AACR,gBAAM,IAAI,aAAa,GAAG;AAAA,QAC9B;AACA,YAAI,IAAI,WAAW;AACf,gBAAM,IAAI,aAAa,GAAG;AAC9B,cAAM,QAAQ,MAAM,IAAI,KAAK;AAC7B,gBAAQ,gBAAgB,IAAI;AAC5B,cAAM,YAAY,MAAM,QAAQ;AAAA,UAC5B,QAAQ,SAAS;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,CAAC,QAAQ;AAChB,gBAAIC;AACJ,aAACA,MAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,UAAU,GAAG;AACtI,0BAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AAAA,UACvE;AAAA,QACJ,CAAC;AACD,SAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,CAAC,CAAC,WAAW;AAClJ,kBAAU,aAAa,EAAE,MAAM,MAAO,OAAO,MAAU;AACvD,eAAO;AAAA,MACX,GAAG;AAAA,IACP,SACO,KAAK;AAER,aAAO;AACP,YAAM;AAAA,IACV;AAAA,EACJ;AAEA,MAAI,oBAAoB,CAAC,MAAM;AAC3B,KAAC,YAAY;AACT;AACA,iBAAS;AACL,YAAI;AACA,gBAAM,EAAE,aAAa,IAAI,MAAM,aAAa;AAC5C,gBAAM,aAAa;AAAA,QACvB,SACO,KAAK;AACR,cAAI,OAAO;AACP;AAEJ,cAAI,EAAE,eAAe;AACjB,mBAAO,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,GAAG;AAE7F,iBAAO;AAEP,cAAI,CAAC,iBAAiB,WAAW;AAC7B,mBAAO,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,GAAG;AAE7F,wBAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ,GAAG;AAAA,EACP;AACA,WAASC,WAAU,SAAS,MAAM,IAAI;AAClC,QAAI,CAAC,kBAAkB;AAEnB,YAAMC,WAAU,IAAI,oBAAoB;AACxC,YAAMC,YAAW,OAAO,UAAU,MAAM;AACpC,QAAAA,UAAS;AACT,QAAAD,SAAQ,MAAM;AAAA,MAClB,CAAC;AACD,OAAC,YAAY;AACT,YAAI,IAAI,IAAI,IAAI,IAAI;AACpB,YAAIE,eAAc,MAAML,WAAU;AAClC,mBAAS;AACL,cAAI;AACA,gBAAIK,cAAa;AACb,oBAAM,MAAML,QAAO;AAEnB,kBAAIG,SAAQ,OAAO;AACf,sBAAM,IAAI,MAAM,kCAAkC;AACtD,cAAAH;AAAA,YACJ;AACA,aAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,CAAC,CAACK,YAAW;AACnJ,aAAC,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,CAAC,CAACA,YAAW;AAC3H,kBAAM,MAAM,OAAO,QAAQ,QAAQ,aAC7B,MAAM,QAAQ,IAAI,IAClB,QAAQ;AACd,gBAAIF,SAAQ,OAAO;AACf,oBAAM,IAAI,MAAM,kCAAkC;AACtD,kBAAM,UAAU,OAAO,QAAQ,YAAY,aACrC,MAAM,QAAQ,QAAQ,KACrB,KAAK,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC/D,gBAAIA,SAAQ,OAAO;AACf,oBAAM,IAAI,MAAM,kCAAkC;AACtD,kBAAM,EAAE,WAAW,IAAI,MAAM,QAAQ;AAAA,cACjC,QAAQA,SAAQ;AAAA,cAChB,SAAS;AAAA,gBACL,GAAG;AAAA,gBACH,gBAAgB;AAAA,cACpB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,MAAM,KAAK,UAAU,OAAO;AAAA,cAC5B;AAAA,cACA,WAAW,CAAC,QAAQ;AAChB,oBAAIF,KAAIK;AACR,iBAACL,MAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,UAAU,GAAG;AACtI,iBAACK,MAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAI,GAAG;AAC9G,8BAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AAAA,cACvE;AAAA,YACJ,CAAC;AACD,aAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,CAAC,CAACD,YAAW;AAClJ,aAAC,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,CAAC,CAACA,YAAW;AAC1H,6BAAiB,UAAU,WAAW,GAAG;AAMrC,cAAAA,eAAc;AACd,cAAAL,WAAU;AAEV,mBAAK,KAAK,MAAM;AAAA,YACpB;AACA,mBAAOG,SAAQ,MAAM;AAAA,UACzB,SACO,KAAK;AACR,gBAAIA,SAAQ,OAAO;AACf;AAEJ,gBAAI,EAAE,eAAe;AACjB,oBAAM;AAEV,gBAAI,CAAC,iBAAiBH,YAAW;AAC7B,oBAAM;AAEV,YAAAK,eAAc;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ,GAAG,EACE,KAAK,MAAM,KAAK,SAAS,CAAC,EAC1B,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,CAAC;AACnC,aAAO,MAAMF,SAAQ,MAAM;AAAA,IAC/B;AAEA;AACA,UAAM,UAAU,IAAI,oBAAoB;AACxC,UAAM,WAAW,OAAO,UAAU,MAAM;AACpC,eAAS;AACT,cAAQ,MAAM;AAAA,IAClB,CAAC;AACD,KAAC,YAAY;AACT,YAAM,cAAc,WAAW;AAC/B,gBAAU;AAAA,QACN,GAAG;AAAA,QACH,YAAY,EAAE,GAAG,QAAQ,YAAY,YAAY;AAAA,MACrD;AACA,UAAI,WAAW;AACf,iBAAS;AACL,mBAAW;AACX,YAAI;AACA,gBAAM,EAAE,KAAK,SAAS,WAAW,IAAI,MAAM,aAAa;AACxD,cAAI;AACJ,cAAI;AACA,kBAAM,MAAM,QAAQ,KAAK;AAAA,cACrB,QAAQ,QAAQ;AAAA,cAChB,QAAQ;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS;AAAA,gBACL,GAAG;AAAA,gBACH,gBAAgB;AAAA,cACpB;AAAA,cACA,MAAM,KAAK,UAAU,OAAO;AAAA,YAChC,CAAC;AAAA,UACL,SACO,KAAK;AACR,kBAAM,IAAI,aAAa,GAAG;AAAA,UAC9B;AACA,cAAI,IAAI,WAAW;AACf,kBAAM,IAAI,aAAa,GAAG;AAC9B,qBAAW,YAAY;AACnB,gBAAII;AACJ,gBAAI;AACA,oBAAMJ,WAAU,IAAI,oBAAoB;AACxC,oBAAMC,YAAW,OAAO,UAAU,MAAM;AACpC,gBAAAA,UAAS;AACT,gBAAAD,SAAQ,MAAM;AAAA,cAClB,CAAC;AACD,cAAAI,OAAM,MAAM,QAAQ,MAAM,kBAAkB,aAAa;AAAA,gBACrD,QAAQJ,SAAQ;AAAA,gBAChB,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ,CAAC;AAAA,YACL,SACO,KAAK;AACR,oBAAM,IAAI,aAAa,GAAG;AAAA,YAC9B;AACA,gBAAII,KAAI,WAAW;AACf,oBAAM,IAAI,aAAaA,IAAG;AAAA,UAClC;AACA,2BAAiB,UAAU,WAAW;AAAA,YAClC,QAAQ,QAAQ;AAAA,YAChB;AAAA,UACJ,CAAC,GAAG;AAMA,0BAAc;AACd,sBAAU;AAEV,iBAAK,KAAK,MAAM;AAAA,UACpB;AACA,qBAAW;AACX,iBAAO,QAAQ,MAAM;AAAA,QACzB,SACO,KAAK;AACR,cAAI,QAAQ,OAAO;AACf,mBAAO,OAAO,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAE/E,cAAI,EAAE,eAAe,eAAe;AAChC,oBAAQ,MAAM;AACd,kBAAM;AAAA,UACV;AAGA,cAAI,MAAM;AACN,mBAAO;AAAA,UACX;AAEA,cAAI,CAAC,iBAAiB,WAAW,eAAe;AAC5C,oBAAQ,MAAM;AACd,kBAAM;AAAA,UACV;AAEA,wBAAc;AAAA,QAClB,UACA;AAEI,cAAI,QAAQ,OAAO,WAAW,EAAE,UAAU,GAAG;AACzC,gBAAI,SAAS,gBAAgB,KAAK,mBAAmB,GAAG;AAIpD,yBAAW,MAAM;AACb,oBAAI,CAAC;AACD,2BAAS,MAAM;AAAA,cACvB,GAAG,gBAAgB;AAAA,YACvB,OACK;AAED,uBAAS,MAAM;AAAA,YACnB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,GAAG,EACE,KAAK,MAAM,KAAK,SAAS,CAAC,EAC1B,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,CAAC;AACnC,WAAO,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACA,SAAO;AAAA,IACH,WAAAL;AAAA,IACA,QAAQ,SAAS,IAAI;AACjB,YAAM,UAAU,CAAC;AACjB,YAAM,WAAW;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,QAEf;AAAA,MACJ;AACA,YAAM,UAAUA,WAAU,SAAS;AAAA,QAC/B,KAAK,KAAK;AAEN,kBAAQ,KAAK,GAAG;AAChB,mBAAS,QAAQ;AAAA,QACrB;AAAA,QACA,MAAM,KAAK;AACP,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAAA,QACrB;AAAA,QACA,WAAW;AACP,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AAAA,QACrB;AAAA,MACJ,GAAG,EAAE;AACL,YAAM,WAAY,gBAAgBM,YAAW;AACzC,mBAAS;AACL,cAAI,CAAC,QAAQ,QAAQ;AAEjB,kBAAM,IAAI,QAAQ,CAAC,YAAa,SAAS,UAAU,OAAQ;AAAA,UAC/D;AAEA,iBAAO,QAAQ,QAAQ;AAEnB,kBAAM,QAAQ,MAAM;AAAA,UACxB;AAEA,cAAI,SAAS,OAAO;AAChB,kBAAM,SAAS;AAAA,UACnB;AAEA,cAAI,SAAS,MAAM;AACf;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,EAAG;AACH,eAAS,QAAQ,OAAO,QAAQ;AAC5B,YAAI,CAAC,SAAS,MAAM;AAChB,mBAAS,OAAO;AAChB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAAA,QACrB;AACA,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,eAAS,SAAS,YAAY;AAC1B,gBAAQ;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,MAC1C;AACA,aAAO;AAAA,IACX;AAAA,IACA,UAAU;AACN,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ;AAYO,IAAM,eAAN,cAA2B,MAAM;AAAA,EACpC,YAAY,oBAAoB;AAC5B,QAAI,SAAS;AACb,QAAI,eAAe,kBAAkB,GAAG;AACpC,iBAAW;AACX,gBACI,2BACI,mBAAmB,SACnB,OACA,mBAAmB;AAAA,IAC/B,WACS,8BAA8B;AACnC,gBAAU,mBAAmB;AAAA;AAE7B,gBAAU,OAAO,kBAAkB;AACvC,UAAM,OAAO;AACb,SAAK,OAAO,KAAK,YAAY;AAC7B,SAAK,WAAW;AAAA,EACpB;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,SAAQ,SAAS,GAAG,KAChB,OAAO,IAAI,IAAI,MAAM,aACrB,OAAO,IAAI,QAAQ,MAAM,YACzB,OAAO,IAAI,YAAY,MAAM;AACrC;AACA,eAAe,QAAQ,SAAS;AAC5B,QAAM,EAAE,QAAQ,KAAK,aAAa,SAAS,MAAM,UAAU,gBAAgB,SAAS,UAAW,IAAI;AACnG,QAAM,UAAU,CAAC;AACjB,QAAM,QAAQ,CAAC;AACf,MAAI;AACJ,MAAI;AACA,UAAM,MAAM,QAAQ,KAAK;AAAA,MACrB;AAAA,MACA,QAAQ,OAAO,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACL,GAAG;AAAA,QACH,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,SACO,KAAK;AACR,UAAM,IAAI,aAAa,GAAG;AAAA,EAC9B;AACA,MAAI,CAAC,IAAI;AACL,UAAM,IAAI,aAAa,GAAG;AAC9B,MAAI,CAAC,IAAI;AACL,UAAM,IAAI,MAAM,uBAAuB;AAC3C,MAAI,QAAQ;AACZ,MAAI;AACJ,GAAC,YAAY;AACT,QAAI;AACJ,QAAI;AACA,YAAMC,SAAQ,aAAa;AAE3B,uBAAiB,SAAS,gBAAgB,IAAI,IAAI,GAAG;AACjD,YAAI,OAAO,UAAU;AACjB,gBAAO,QAAQ,IAAI,MAAM,4BAA4B,KAAK,GAAG;AAEjE,YAAI;AACJ,YAAI;AACA,iBAAOA,OAAM,KAAK;AAAA,QACtB,SACO,KAAK;AACR,gBAAO,QAAQ;AAAA,QACnB;AACA,YAAI,CAAC;AACD;AACJ,mBAAW,OAAO,MAAM;AACpB,cAAI;AACA,0BAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AAAA,UACvE,SACO,KAAK;AACR,kBAAO,QAAQ;AAAA,UACnB;AACA,gBAAM,cAAc,IAAI,QAAQ,QAAQ,IAAI,OACtC,IAAI,KAAK,KACT;AACN,cAAI,EAAE,eAAe;AACjB,kBAAM,WAAW,IAAI,CAAC;AAC1B,kBAAQ,IAAI,OAAO;AAAA,YACf,KAAK;AACD,kBAAI;AACA,sBAAM,WAAW,EAAE,KAAK,IAAI,KAAK,OAAO;AAAA;AAExC,sBAAM,WAAW,EAAE,KAAK,IAAI,IAAI;AACpC;AAAA,YACJ,KAAK;AACD,oBAAM,WAAW,EAAE,KAAK,UAAU;AAClC;AAAA,YACJ;AACI,oBAAO,QAAQ,IAAI,MAAM,6BAA6B,IAAI,KAAK,GAAG;AAAA,UAC1E;AACA,WAAC,KAAK,QAAQ,WAAW,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAAA,QAChF;AAAA,MACJ;AAGA,UAAI,OAAO,KAAK,OAAO,EAAE,QAAQ;AAC7B,cAAM,IAAI,MAAM,+CAA+C;AAAA,MACnE;AAAA,IACJ,SACO,KAAK;AACR,UAAI,CAAC,SAAS,OAAO,KAAK,OAAO,EAAE,QAAQ;AAGvC,gBAAQ,IAAI,aAAa,GAAG;AAAA,MAChC,OACK;AACD,gBAAQ;AAAA,MACZ;AACA,0BAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK;AAAA,IAC3F,UACA;AACI,aAAO,OAAO,OAAO,EAAE,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC;AAAA,IAC7D;AAAA,EACJ,GAAG;AACH,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,cAAc,MAAM,IAAI,QAAQ,CAAC,GAAG,WAAW;AAC3C,UAAI;AACA,eAAO,OAAO,KAAK;AACvB,wBAAkB;AAAA,IACtB,CAAC;AAAA,IACD,OAAO,WAAWC,UAAS;AACvB,UAAI;AACJ,YAAM,EAAE,QAAAC,SAAQ,cAAc,GAAG,IAAID,aAAY,QAAQA,aAAY,SAASA,WAAU,CAAC;AAEzF,UAAI;AACA,mBAAS;AACL,kBAAQ,KAAK,MAAM,WAAW,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAE7E,kBAAM,SAAS,MAAM,WAAW,EAAE,MAAM;AACxC,gBAAI,WAAW;AACX;AACJ,kBAAM;AAAA,UACV;AACA,cAAI;AACA,kBAAM;AACV,cAAIC,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO;AACvD,kBAAM,IAAI,MAAM,uCAAuC;AAC3D,gBAAM,IAAI,QAAQ,CAAC,YAAY;AAC3B,kBAAM,UAAU,MAAM;AAClB,cAAAA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,oBAAoB,SAAS,OAAO;AAC3F,qBAAO,QAAQ,WAAW;AAC1B,sBAAQ;AAAA,YACZ;AACA,YAAAA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,iBAAiB,SAAS,OAAO;AACxF,oBAAQ,WAAW,IAAI,EAAE,QAAQ;AAAA,UACrC,CAAC;AAAA,QACL;AAAA,MACJ,UACA;AACI,eAAO,MAAM,WAAW;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,gBAAgB,KAAK;AAE1B,MAAI,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM,YAAY;AACzD,UAAM;AACN,WAAO;AAAA,EACX;AAEA,SAAQ,mBAAmB;AACvB,UAAM,SAAS,IAAI,UAAU;AAC7B,QAAI;AACJ,OAAG;AACC,eAAS,MAAM,OAAO,KAAK;AAC3B,UAAI,OAAO,UAAU;AACjB,cAAM,OAAO;AAAA,IACrB,SAAS,CAAC,OAAO;AAAA,EACrB,EAAG;AACP;", "names": ["validate", "execute", "subscribe", "iterator", "paramsOrResponse", "params", "prepared", "result", "opId", "ControlChars", "parse", "retries", "_a", "subscribe", "control", "unlisten", "retryingErr", "_b", "res", "iterator", "parse", "options", "signal"]}
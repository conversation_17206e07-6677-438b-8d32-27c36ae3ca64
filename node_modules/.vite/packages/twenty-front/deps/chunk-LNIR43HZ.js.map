{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/ViewLayout/ViewLayoutSection.vue.js"], "sourcesContent": ["import { defineComponent as r, openBlock as t, createElementBlock as o, normalizeProps as s, guardReactiveProps as i, unref as c, renderSlot as l, createCommentVNode as m } from \"vue\";\nimport { useBindCx as u } from \"@scalar/components\";\nconst a = {\n  key: 0,\n  class: \"bg-b-1 z-1 sticky top-0 flex min-h-11 items-center border-b px-2.5 text-sm font-medium xl:rounded-none\"\n}, _ = /* @__PURE__ */ r({\n  __name: \"ViewLayoutSection\",\n  setup(d) {\n    const { cx: n } = u();\n    return (e, f) => (t(), o(\"section\", s(i(\n      c(n)(\"xl:custom-scroll bg-b-1 flex flex-1 flex-col xl:h-full xl:min-w-0\")\n    )), [\n      e.$slots.title ? (t(), o(\"div\", a, [\n        l(e.$slots, \"title\")\n      ])) : m(\"\", !0),\n      l(e.$slots, \"default\")\n    ], 16));\n  }\n});\nexport {\n  _ as default\n};\n"], "mappings": ";;;;;;;;;;;;;AAEA,IAAM,IAAI;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,UAAM,EAAE,IAAI,EAAE,IAAI,EAAE;AACpB,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,WAAW,eAAE;AAAA,MACpC,MAAE,CAAC,EAAE,mEAAmE;AAAA,IAC1E,CAAC,GAAG;AAAA,MACF,EAAE,OAAO,SAAS,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,QACjC,WAAE,EAAE,QAAQ,OAAO;AAAA,MACrB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACd,WAAE,EAAE,QAAQ,SAAS;AAAA,IACvB,GAAG,EAAE;AAAA,EACP;AACF,CAAC;", "names": []}
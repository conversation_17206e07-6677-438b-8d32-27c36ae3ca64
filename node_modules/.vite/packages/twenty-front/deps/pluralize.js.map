{"version": 3, "sources": ["../../../../pluralize/pluralize.js"], "sourcesContent": ["/* global define */\n\n(function (root, pluralize) {\n  /* istanbul ignore else */\n  if (typeof require === 'function' && typeof exports === 'object' && typeof module === 'object') {\n    // Node.\n    module.exports = pluralize();\n  } else if (typeof define === 'function' && define.amd) {\n    // AMD, registers as an anonymous module.\n    define(function () {\n      return pluralize();\n    });\n  } else {\n    // Browser global.\n    root.pluralize = pluralize();\n  }\n})(this, function () {\n  // Rule storage - pluralize and singularize need to be run sequentially,\n  // while other rules can be optimized using an object for instant lookups.\n  var pluralRules = [];\n  var singularRules = [];\n  var uncountables = {};\n  var irregularPlurals = {};\n  var irregularSingles = {};\n\n  /**\n   * Sanitize a pluralization rule to a usable regular expression.\n   *\n   * @param  {(RegExp|string)} rule\n   * @return {RegExp}\n   */\n  function sanitizeRule (rule) {\n    if (typeof rule === 'string') {\n      return new RegExp('^' + rule + '$', 'i');\n    }\n\n    return rule;\n  }\n\n  /**\n   * Pass in a word token to produce a function that can replicate the case on\n   * another word.\n   *\n   * @param  {string}   word\n   * @param  {string}   token\n   * @return {Function}\n   */\n  function restoreCase (word, token) {\n    // Tokens are an exact match.\n    if (word === token) return token;\n\n    // Lower cased words. E.g. \"hello\".\n    if (word === word.toLowerCase()) return token.toLowerCase();\n\n    // Upper cased words. E.g. \"WHISKY\".\n    if (word === word.toUpperCase()) return token.toUpperCase();\n\n    // Title cased words. E.g. \"Title\".\n    if (word[0] === word[0].toUpperCase()) {\n      return token.charAt(0).toUpperCase() + token.substr(1).toLowerCase();\n    }\n\n    // Lower cased words. E.g. \"test\".\n    return token.toLowerCase();\n  }\n\n  /**\n   * Interpolate a regexp string.\n   *\n   * @param  {string} str\n   * @param  {Array}  args\n   * @return {string}\n   */\n  function interpolate (str, args) {\n    return str.replace(/\\$(\\d{1,2})/g, function (match, index) {\n      return args[index] || '';\n    });\n  }\n\n  /**\n   * Replace a word using a rule.\n   *\n   * @param  {string} word\n   * @param  {Array}  rule\n   * @return {string}\n   */\n  function replace (word, rule) {\n    return word.replace(rule[0], function (match, index) {\n      var result = interpolate(rule[1], arguments);\n\n      if (match === '') {\n        return restoreCase(word[index - 1], result);\n      }\n\n      return restoreCase(match, result);\n    });\n  }\n\n  /**\n   * Sanitize a word by passing in the word and sanitization rules.\n   *\n   * @param  {string}   token\n   * @param  {string}   word\n   * @param  {Array}    rules\n   * @return {string}\n   */\n  function sanitizeWord (token, word, rules) {\n    // Empty string or doesn't need fixing.\n    if (!token.length || uncountables.hasOwnProperty(token)) {\n      return word;\n    }\n\n    var len = rules.length;\n\n    // Iterate over the sanitization rules and use the first one to match.\n    while (len--) {\n      var rule = rules[len];\n\n      if (rule[0].test(word)) return replace(word, rule);\n    }\n\n    return word;\n  }\n\n  /**\n   * Replace a word with the updated word.\n   *\n   * @param  {Object}   replaceMap\n   * @param  {Object}   keepMap\n   * @param  {Array}    rules\n   * @return {Function}\n   */\n  function replaceWord (replaceMap, keepMap, rules) {\n    return function (word) {\n      // Get the correct token and case restoration functions.\n      var token = word.toLowerCase();\n\n      // Check against the keep object map.\n      if (keepMap.hasOwnProperty(token)) {\n        return restoreCase(word, token);\n      }\n\n      // Check against the replacement map for a direct word replacement.\n      if (replaceMap.hasOwnProperty(token)) {\n        return restoreCase(word, replaceMap[token]);\n      }\n\n      // Run all the rules against the word.\n      return sanitizeWord(token, word, rules);\n    };\n  }\n\n  /**\n   * Check if a word is part of the map.\n   */\n  function checkWord (replaceMap, keepMap, rules, bool) {\n    return function (word) {\n      var token = word.toLowerCase();\n\n      if (keepMap.hasOwnProperty(token)) return true;\n      if (replaceMap.hasOwnProperty(token)) return false;\n\n      return sanitizeWord(token, token, rules) === token;\n    };\n  }\n\n  /**\n   * Pluralize or singularize a word based on the passed in count.\n   *\n   * @param  {string}  word      The word to pluralize\n   * @param  {number}  count     How many of the word exist\n   * @param  {boolean} inclusive Whether to prefix with the number (e.g. 3 ducks)\n   * @return {string}\n   */\n  function pluralize (word, count, inclusive) {\n    var pluralized = count === 1\n      ? pluralize.singular(word) : pluralize.plural(word);\n\n    return (inclusive ? count + ' ' : '') + pluralized;\n  }\n\n  /**\n   * Pluralize a word.\n   *\n   * @type {Function}\n   */\n  pluralize.plural = replaceWord(\n    irregularSingles, irregularPlurals, pluralRules\n  );\n\n  /**\n   * Check if a word is plural.\n   *\n   * @type {Function}\n   */\n  pluralize.isPlural = checkWord(\n    irregularSingles, irregularPlurals, pluralRules\n  );\n\n  /**\n   * Singularize a word.\n   *\n   * @type {Function}\n   */\n  pluralize.singular = replaceWord(\n    irregularPlurals, irregularSingles, singularRules\n  );\n\n  /**\n   * Check if a word is singular.\n   *\n   * @type {Function}\n   */\n  pluralize.isSingular = checkWord(\n    irregularPlurals, irregularSingles, singularRules\n  );\n\n  /**\n   * Add a pluralization rule to the collection.\n   *\n   * @param {(string|RegExp)} rule\n   * @param {string}          replacement\n   */\n  pluralize.addPluralRule = function (rule, replacement) {\n    pluralRules.push([sanitizeRule(rule), replacement]);\n  };\n\n  /**\n   * Add a singularization rule to the collection.\n   *\n   * @param {(string|RegExp)} rule\n   * @param {string}          replacement\n   */\n  pluralize.addSingularRule = function (rule, replacement) {\n    singularRules.push([sanitizeRule(rule), replacement]);\n  };\n\n  /**\n   * Add an uncountable word rule.\n   *\n   * @param {(string|RegExp)} word\n   */\n  pluralize.addUncountableRule = function (word) {\n    if (typeof word === 'string') {\n      uncountables[word.toLowerCase()] = true;\n      return;\n    }\n\n    // Set singular and plural references for the word.\n    pluralize.addPluralRule(word, '$0');\n    pluralize.addSingularRule(word, '$0');\n  };\n\n  /**\n   * Add an irregular word definition.\n   *\n   * @param {string} single\n   * @param {string} plural\n   */\n  pluralize.addIrregularRule = function (single, plural) {\n    plural = plural.toLowerCase();\n    single = single.toLowerCase();\n\n    irregularSingles[single] = plural;\n    irregularPlurals[plural] = single;\n  };\n\n  /**\n   * Irregular rules.\n   */\n  [\n    // Pronouns.\n    ['I', 'we'],\n    ['me', 'us'],\n    ['he', 'they'],\n    ['she', 'they'],\n    ['them', 'them'],\n    ['myself', 'ourselves'],\n    ['yourself', 'yourselves'],\n    ['itself', 'themselves'],\n    ['herself', 'themselves'],\n    ['himself', 'themselves'],\n    ['themself', 'themselves'],\n    ['is', 'are'],\n    ['was', 'were'],\n    ['has', 'have'],\n    ['this', 'these'],\n    ['that', 'those'],\n    // Words ending in with a consonant and `o`.\n    ['echo', 'echoes'],\n    ['dingo', 'dingoes'],\n    ['volcano', 'volcanoes'],\n    ['tornado', 'tornadoes'],\n    ['torpedo', 'torpedoes'],\n    // Ends with `us`.\n    ['genus', 'genera'],\n    ['viscus', 'viscera'],\n    // Ends with `ma`.\n    ['stigma', 'stigmata'],\n    ['stoma', 'stomata'],\n    ['dogma', 'dogmata'],\n    ['lemma', 'lemmata'],\n    ['schema', 'schemata'],\n    ['anathema', 'anathemata'],\n    // Other irregular rules.\n    ['ox', 'oxen'],\n    ['axe', 'axes'],\n    ['die', 'dice'],\n    ['yes', 'yeses'],\n    ['foot', 'feet'],\n    ['eave', 'eaves'],\n    ['goose', 'geese'],\n    ['tooth', 'teeth'],\n    ['quiz', 'quizzes'],\n    ['human', 'humans'],\n    ['proof', 'proofs'],\n    ['carve', 'carves'],\n    ['valve', 'valves'],\n    ['looey', 'looies'],\n    ['thief', 'thieves'],\n    ['groove', 'grooves'],\n    ['pickaxe', 'pickaxes'],\n    ['passerby', 'passersby']\n  ].forEach(function (rule) {\n    return pluralize.addIrregularRule(rule[0], rule[1]);\n  });\n\n  /**\n   * Pluralization rules.\n   */\n  [\n    [/s?$/i, 's'],\n    [/[^\\u0000-\\u007F]$/i, '$0'],\n    [/([^aeiou]ese)$/i, '$1'],\n    [/(ax|test)is$/i, '$1es'],\n    [/(alias|[^aou]us|t[lm]as|gas|ris)$/i, '$1es'],\n    [/(e[mn]u)s?$/i, '$1s'],\n    [/([^l]ias|[aeiou]las|[ejzr]as|[iu]am)$/i, '$1'],\n    [/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i, '$1i'],\n    [/(alumn|alg|vertebr)(?:a|ae)$/i, '$1ae'],\n    [/(seraph|cherub)(?:im)?$/i, '$1im'],\n    [/(her|at|gr)o$/i, '$1oes'],\n    [/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|automat|quor)(?:a|um)$/i, '$1a'],\n    [/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)(?:a|on)$/i, '$1a'],\n    [/sis$/i, 'ses'],\n    [/(?:(kni|wi|li)fe|(ar|l|ea|eo|oa|hoo)f)$/i, '$1$2ves'],\n    [/([^aeiouy]|qu)y$/i, '$1ies'],\n    [/([^ch][ieo][ln])ey$/i, '$1ies'],\n    [/(x|ch|ss|sh|zz)$/i, '$1es'],\n    [/(matr|cod|mur|sil|vert|ind|append)(?:ix|ex)$/i, '$1ices'],\n    [/\\b((?:tit)?m|l)(?:ice|ouse)$/i, '$1ice'],\n    [/(pe)(?:rson|ople)$/i, '$1ople'],\n    [/(child)(?:ren)?$/i, '$1ren'],\n    [/eaux$/i, '$0'],\n    [/m[ae]n$/i, 'men'],\n    ['thou', 'you']\n  ].forEach(function (rule) {\n    return pluralize.addPluralRule(rule[0], rule[1]);\n  });\n\n  /**\n   * Singularization rules.\n   */\n  [\n    [/s$/i, ''],\n    [/(ss)$/i, '$1'],\n    [/(wi|kni|(?:after|half|high|low|mid|non|night|[^\\w]|^)li)ves$/i, '$1fe'],\n    [/(ar|(?:wo|[ae])l|[eo][ao])ves$/i, '$1f'],\n    [/ies$/i, 'y'],\n    [/\\b([pl]|zomb|(?:neck|cross)?t|coll|faer|food|gen|goon|group|lass|talk|goal|cut)ies$/i, '$1ie'],\n    [/\\b(mon|smil)ies$/i, '$1ey'],\n    [/\\b((?:tit)?m|l)ice$/i, '$1ouse'],\n    [/(seraph|cherub)im$/i, '$1'],\n    [/(x|ch|ss|sh|zz|tto|go|cho|alias|[^aou]us|t[lm]as|gas|(?:her|at|gr)o|[aeiou]ris)(?:es)?$/i, '$1'],\n    [/(analy|diagno|parenthe|progno|synop|the|empha|cri|ne)(?:sis|ses)$/i, '$1sis'],\n    [/(movie|twelve|abuse|e[mn]u)s$/i, '$1'],\n    [/(test)(?:is|es)$/i, '$1is'],\n    [/(alumn|syllab|vir|radi|nucle|fung|cact|stimul|termin|bacill|foc|uter|loc|strat)(?:us|i)$/i, '$1us'],\n    [/(agend|addend|millenni|dat|extrem|bacteri|desiderat|strat|candelabr|errat|ov|symposi|curricul|quor)a$/i, '$1um'],\n    [/(apheli|hyperbat|periheli|asyndet|noumen|phenomen|criteri|organ|prolegomen|hedr|automat)a$/i, '$1on'],\n    [/(alumn|alg|vertebr)ae$/i, '$1a'],\n    [/(cod|mur|sil|vert|ind)ices$/i, '$1ex'],\n    [/(matr|append)ices$/i, '$1ix'],\n    [/(pe)(rson|ople)$/i, '$1rson'],\n    [/(child)ren$/i, '$1'],\n    [/(eau)x?$/i, '$1'],\n    [/men$/i, 'man']\n  ].forEach(function (rule) {\n    return pluralize.addSingularRule(rule[0], rule[1]);\n  });\n\n  /**\n   * Uncountable rules.\n   */\n  [\n    // Singular words with no plurals.\n    'adulthood',\n    'advice',\n    'agenda',\n    'aid',\n    'aircraft',\n    'alcohol',\n    'ammo',\n    'analytics',\n    'anime',\n    'athletics',\n    'audio',\n    'bison',\n    'blood',\n    'bream',\n    'buffalo',\n    'butter',\n    'carp',\n    'cash',\n    'chassis',\n    'chess',\n    'clothing',\n    'cod',\n    'commerce',\n    'cooperation',\n    'corps',\n    'debris',\n    'diabetes',\n    'digestion',\n    'elk',\n    'energy',\n    'equipment',\n    'excretion',\n    'expertise',\n    'firmware',\n    'flounder',\n    'fun',\n    'gallows',\n    'garbage',\n    'graffiti',\n    'hardware',\n    'headquarters',\n    'health',\n    'herpes',\n    'highjinks',\n    'homework',\n    'housework',\n    'information',\n    'jeans',\n    'justice',\n    'kudos',\n    'labour',\n    'literature',\n    'machinery',\n    'mackerel',\n    'mail',\n    'media',\n    'mews',\n    'moose',\n    'music',\n    'mud',\n    'manga',\n    'news',\n    'only',\n    'personnel',\n    'pike',\n    'plankton',\n    'pliers',\n    'police',\n    'pollution',\n    'premises',\n    'rain',\n    'research',\n    'rice',\n    'salmon',\n    'scissors',\n    'series',\n    'sewage',\n    'shambles',\n    'shrimp',\n    'software',\n    'species',\n    'staff',\n    'swine',\n    'tennis',\n    'traffic',\n    'transportation',\n    'trout',\n    'tuna',\n    'wealth',\n    'welfare',\n    'whiting',\n    'wildebeest',\n    'wildlife',\n    'you',\n    /pok[eé]mon$/i,\n    // Regexes.\n    /[^aeiou]ese$/i, // \"chinese\", \"japanese\"\n    /deer$/i, // \"deer\", \"reindeer\"\n    /fish$/i, // \"fish\", \"blowfish\", \"angelfish\"\n    /measles$/i,\n    /o[iu]s$/i, // \"carnivorous\"\n    /pox$/i, // \"chickpox\", \"smallpox\"\n    /sheep$/i\n  ].forEach(pluralize.addUncountableRule);\n\n  return pluralize;\n});\n"], "mappings": ";;;;;;AAAA;AAAA;AAEA,KAAC,SAAU,MAAM,WAAW;AAE1B,UAAI,OAAO,cAAY,cAAc,OAAO,YAAY,YAAY,OAAO,WAAW,UAAU;AAE9F,eAAO,UAAU,UAAU;AAAA,MAC7B,WAAW,OAAO,WAAW,cAAc,OAAO,KAAK;AAErD,eAAO,WAAY;AACjB,iBAAO,UAAU;AAAA,QACnB,CAAC;AAAA,MACH,OAAO;AAEL,aAAK,YAAY,UAAU;AAAA,MAC7B;AAAA,IACF,GAAG,SAAM,WAAY;AAGnB,UAAI,cAAc,CAAC;AACnB,UAAI,gBAAgB,CAAC;AACrB,UAAI,eAAe,CAAC;AACpB,UAAI,mBAAmB,CAAC;AACxB,UAAI,mBAAmB,CAAC;AAQxB,eAAS,aAAc,MAAM;AAC3B,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,IAAI,OAAO,MAAM,OAAO,KAAK,GAAG;AAAA,QACzC;AAEA,eAAO;AAAA,MACT;AAUA,eAAS,YAAa,MAAM,OAAO;AAEjC,YAAI,SAAS,MAAO,QAAO;AAG3B,YAAI,SAAS,KAAK,YAAY,EAAG,QAAO,MAAM,YAAY;AAG1D,YAAI,SAAS,KAAK,YAAY,EAAG,QAAO,MAAM,YAAY;AAG1D,YAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,YAAY,GAAG;AACrC,iBAAO,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,OAAO,CAAC,EAAE,YAAY;AAAA,QACrE;AAGA,eAAO,MAAM,YAAY;AAAA,MAC3B;AASA,eAAS,YAAa,KAAK,MAAM;AAC/B,eAAO,IAAI,QAAQ,gBAAgB,SAAU,OAAO,OAAO;AACzD,iBAAO,KAAK,KAAK,KAAK;AAAA,QACxB,CAAC;AAAA,MACH;AASA,eAAS,QAAS,MAAM,MAAM;AAC5B,eAAO,KAAK,QAAQ,KAAK,CAAC,GAAG,SAAU,OAAO,OAAO;AACnD,cAAI,SAAS,YAAY,KAAK,CAAC,GAAG,SAAS;AAE3C,cAAI,UAAU,IAAI;AAChB,mBAAO,YAAY,KAAK,QAAQ,CAAC,GAAG,MAAM;AAAA,UAC5C;AAEA,iBAAO,YAAY,OAAO,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AAUA,eAAS,aAAc,OAAO,MAAM,OAAO;AAEzC,YAAI,CAAC,MAAM,UAAU,aAAa,eAAe,KAAK,GAAG;AACvD,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,MAAM;AAGhB,eAAO,OAAO;AACZ,cAAI,OAAO,MAAM,GAAG;AAEpB,cAAI,KAAK,CAAC,EAAE,KAAK,IAAI,EAAG,QAAO,QAAQ,MAAM,IAAI;AAAA,QACnD;AAEA,eAAO;AAAA,MACT;AAUA,eAAS,YAAa,YAAY,SAAS,OAAO;AAChD,eAAO,SAAU,MAAM;AAErB,cAAI,QAAQ,KAAK,YAAY;AAG7B,cAAI,QAAQ,eAAe,KAAK,GAAG;AACjC,mBAAO,YAAY,MAAM,KAAK;AAAA,UAChC;AAGA,cAAI,WAAW,eAAe,KAAK,GAAG;AACpC,mBAAO,YAAY,MAAM,WAAW,KAAK,CAAC;AAAA,UAC5C;AAGA,iBAAO,aAAa,OAAO,MAAM,KAAK;AAAA,QACxC;AAAA,MACF;AAKA,eAAS,UAAW,YAAY,SAAS,OAAO,MAAM;AACpD,eAAO,SAAU,MAAM;AACrB,cAAI,QAAQ,KAAK,YAAY;AAE7B,cAAI,QAAQ,eAAe,KAAK,EAAG,QAAO;AAC1C,cAAI,WAAW,eAAe,KAAK,EAAG,QAAO;AAE7C,iBAAO,aAAa,OAAO,OAAO,KAAK,MAAM;AAAA,QAC/C;AAAA,MACF;AAUA,eAAS,UAAW,MAAM,OAAO,WAAW;AAC1C,YAAI,aAAa,UAAU,IACvB,UAAU,SAAS,IAAI,IAAI,UAAU,OAAO,IAAI;AAEpD,gBAAQ,YAAY,QAAQ,MAAM,MAAM;AAAA,MAC1C;AAOA,gBAAU,SAAS;AAAA,QACjB;AAAA,QAAkB;AAAA,QAAkB;AAAA,MACtC;AAOA,gBAAU,WAAW;AAAA,QACnB;AAAA,QAAkB;AAAA,QAAkB;AAAA,MACtC;AAOA,gBAAU,WAAW;AAAA,QACnB;AAAA,QAAkB;AAAA,QAAkB;AAAA,MACtC;AAOA,gBAAU,aAAa;AAAA,QACrB;AAAA,QAAkB;AAAA,QAAkB;AAAA,MACtC;AAQA,gBAAU,gBAAgB,SAAU,MAAM,aAAa;AACrD,oBAAY,KAAK,CAAC,aAAa,IAAI,GAAG,WAAW,CAAC;AAAA,MACpD;AAQA,gBAAU,kBAAkB,SAAU,MAAM,aAAa;AACvD,sBAAc,KAAK,CAAC,aAAa,IAAI,GAAG,WAAW,CAAC;AAAA,MACtD;AAOA,gBAAU,qBAAqB,SAAU,MAAM;AAC7C,YAAI,OAAO,SAAS,UAAU;AAC5B,uBAAa,KAAK,YAAY,CAAC,IAAI;AACnC;AAAA,QACF;AAGA,kBAAU,cAAc,MAAM,IAAI;AAClC,kBAAU,gBAAgB,MAAM,IAAI;AAAA,MACtC;AAQA,gBAAU,mBAAmB,SAAU,QAAQ,QAAQ;AACrD,iBAAS,OAAO,YAAY;AAC5B,iBAAS,OAAO,YAAY;AAE5B,yBAAiB,MAAM,IAAI;AAC3B,yBAAiB,MAAM,IAAI;AAAA,MAC7B;AAKA;AAAA;AAAA,QAEE,CAAC,KAAK,IAAI;AAAA,QACV,CAAC,MAAM,IAAI;AAAA,QACX,CAAC,MAAM,MAAM;AAAA,QACb,CAAC,OAAO,MAAM;AAAA,QACd,CAAC,QAAQ,MAAM;AAAA,QACf,CAAC,UAAU,WAAW;AAAA,QACtB,CAAC,YAAY,YAAY;AAAA,QACzB,CAAC,UAAU,YAAY;AAAA,QACvB,CAAC,WAAW,YAAY;AAAA,QACxB,CAAC,WAAW,YAAY;AAAA,QACxB,CAAC,YAAY,YAAY;AAAA,QACzB,CAAC,MAAM,KAAK;AAAA,QACZ,CAAC,OAAO,MAAM;AAAA,QACd,CAAC,OAAO,MAAM;AAAA,QACd,CAAC,QAAQ,OAAO;AAAA,QAChB,CAAC,QAAQ,OAAO;AAAA;AAAA,QAEhB,CAAC,QAAQ,QAAQ;AAAA,QACjB,CAAC,SAAS,SAAS;AAAA,QACnB,CAAC,WAAW,WAAW;AAAA,QACvB,CAAC,WAAW,WAAW;AAAA,QACvB,CAAC,WAAW,WAAW;AAAA;AAAA,QAEvB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,UAAU,SAAS;AAAA;AAAA,QAEpB,CAAC,UAAU,UAAU;AAAA,QACrB,CAAC,SAAS,SAAS;AAAA,QACnB,CAAC,SAAS,SAAS;AAAA,QACnB,CAAC,SAAS,SAAS;AAAA,QACnB,CAAC,UAAU,UAAU;AAAA,QACrB,CAAC,YAAY,YAAY;AAAA;AAAA,QAEzB,CAAC,MAAM,MAAM;AAAA,QACb,CAAC,OAAO,MAAM;AAAA,QACd,CAAC,OAAO,MAAM;AAAA,QACd,CAAC,OAAO,OAAO;AAAA,QACf,CAAC,QAAQ,MAAM;AAAA,QACf,CAAC,QAAQ,OAAO;AAAA,QAChB,CAAC,SAAS,OAAO;AAAA,QACjB,CAAC,SAAS,OAAO;AAAA,QACjB,CAAC,QAAQ,SAAS;AAAA,QAClB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,SAAS,QAAQ;AAAA,QAClB,CAAC,SAAS,SAAS;AAAA,QACnB,CAAC,UAAU,SAAS;AAAA,QACpB,CAAC,WAAW,UAAU;AAAA,QACtB,CAAC,YAAY,WAAW;AAAA,MAC1B,EAAE,QAAQ,SAAU,MAAM;AACxB,eAAO,UAAU,iBAAiB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MACpD,CAAC;AAKD;AAAA,QACE,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,sBAAsB,IAAI;AAAA,QAC3B,CAAC,mBAAmB,IAAI;AAAA,QACxB,CAAC,iBAAiB,MAAM;AAAA,QACxB,CAAC,sCAAsC,MAAM;AAAA,QAC7C,CAAC,gBAAgB,KAAK;AAAA,QACtB,CAAC,0CAA0C,IAAI;AAAA,QAC/C,CAAC,6FAA6F,KAAK;AAAA,QACnG,CAAC,iCAAiC,MAAM;AAAA,QACxC,CAAC,4BAA4B,MAAM;AAAA,QACnC,CAAC,kBAAkB,OAAO;AAAA,QAC1B,CAAC,yHAAyH,KAAK;AAAA,QAC/H,CAAC,sGAAsG,KAAK;AAAA,QAC5G,CAAC,SAAS,KAAK;AAAA,QACf,CAAC,4CAA4C,SAAS;AAAA,QACtD,CAAC,qBAAqB,OAAO;AAAA,QAC7B,CAAC,wBAAwB,OAAO;AAAA,QAChC,CAAC,qBAAqB,MAAM;AAAA,QAC5B,CAAC,iDAAiD,QAAQ;AAAA,QAC1D,CAAC,iCAAiC,OAAO;AAAA,QACzC,CAAC,uBAAuB,QAAQ;AAAA,QAChC,CAAC,qBAAqB,OAAO;AAAA,QAC7B,CAAC,UAAU,IAAI;AAAA,QACf,CAAC,YAAY,KAAK;AAAA,QAClB,CAAC,QAAQ,KAAK;AAAA,MAChB,EAAE,QAAQ,SAAU,MAAM;AACxB,eAAO,UAAU,cAAc,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MACjD,CAAC;AAKD;AAAA,QACE,CAAC,OAAO,EAAE;AAAA,QACV,CAAC,UAAU,IAAI;AAAA,QACf,CAAC,iEAAiE,MAAM;AAAA,QACxE,CAAC,mCAAmC,KAAK;AAAA,QACzC,CAAC,SAAS,GAAG;AAAA,QACb,CAAC,wFAAwF,MAAM;AAAA,QAC/F,CAAC,qBAAqB,MAAM;AAAA,QAC5B,CAAC,wBAAwB,QAAQ;AAAA,QACjC,CAAC,uBAAuB,IAAI;AAAA,QAC5B,CAAC,4FAA4F,IAAI;AAAA,QACjG,CAAC,sEAAsE,OAAO;AAAA,QAC9E,CAAC,kCAAkC,IAAI;AAAA,QACvC,CAAC,qBAAqB,MAAM;AAAA,QAC5B,CAAC,6FAA6F,MAAM;AAAA,QACpG,CAAC,0GAA0G,MAAM;AAAA,QACjH,CAAC,+FAA+F,MAAM;AAAA,QACtG,CAAC,2BAA2B,KAAK;AAAA,QACjC,CAAC,gCAAgC,MAAM;AAAA,QACvC,CAAC,uBAAuB,MAAM;AAAA,QAC9B,CAAC,qBAAqB,QAAQ;AAAA,QAC9B,CAAC,gBAAgB,IAAI;AAAA,QACrB,CAAC,aAAa,IAAI;AAAA,QAClB,CAAC,SAAS,KAAK;AAAA,MACjB,EAAE,QAAQ,SAAU,MAAM;AACxB,eAAO,UAAU,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MACnD,CAAC;AAKD;AAAA;AAAA,QAEE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,MACF,EAAE,QAAQ,UAAU,kBAAkB;AAEtC,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;", "names": []}
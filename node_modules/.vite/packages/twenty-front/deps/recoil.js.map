{"version": 3, "sources": ["../../../../recoil/es/index.js"], "sourcesContent": ["import react from 'react';\nimport reactDom from 'react-dom';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction err(message) {\n  const error = new Error(message); // In V8, Error objects keep the closure scope chain alive until the\n  // err.stack property is accessed.\n\n  if (error.stack === undefined) {\n    // IE sets the stack only if error is thrown\n    try {\n      throw error;\n    } catch (_) {} // eslint-disable-line fb-www/no-unused-catch-bindings, no-empty\n\n  }\n\n  return error;\n}\n\nvar err_1 = err;\n\n// @oss-only\n\n\nvar Recoil_err = err_1;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\n// Split declaration and implementation to allow this function to pretend to\n// check for actual instance of Promise instead of something with a `then`\n// method.\n// eslint-disable-next-line no-redeclare\nfunction isPromise(p) {\n  return !!p && typeof p.then === 'function';\n}\n\nvar Recoil_isPromise = isPromise;\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n\n  throw Recoil_err(message !== null && message !== void 0 ? message : 'Got unexpected null or undefined');\n}\n\nvar Recoil_nullthrows = nullthrows;\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nclass BaseLoadable {\n  getValue() {\n    throw Recoil_err('BaseLoadable');\n  }\n\n  toPromise() {\n    throw Recoil_err('BaseLoadable');\n  }\n\n  valueMaybe() {\n    throw Recoil_err('BaseLoadable');\n  }\n\n  valueOrThrow() {\n    // $FlowFixMe[prop-missing]\n    throw Recoil_err(`Loadable expected value, but in \"${this.state}\" state`);\n  }\n\n  promiseMaybe() {\n    throw Recoil_err('BaseLoadable');\n  }\n\n  promiseOrThrow() {\n    // $FlowFixMe[prop-missing]\n    throw Recoil_err(`Loadable expected promise, but in \"${this.state}\" state`);\n  }\n\n  errorMaybe() {\n    throw Recoil_err('BaseLoadable');\n  }\n\n  errorOrThrow() {\n    // $FlowFixMe[prop-missing]\n    throw Recoil_err(`Loadable expected error, but in \"${this.state}\" state`);\n  }\n\n  is(other) {\n    // $FlowFixMe[prop-missing]\n    return other.state === this.state && other.contents === this.contents;\n  }\n\n  map(_map) {\n    throw Recoil_err('BaseLoadable');\n  }\n\n}\n\nclass ValueLoadable extends BaseLoadable {\n  constructor(value) {\n    super();\n\n    _defineProperty(this, \"state\", 'hasValue');\n\n    _defineProperty(this, \"contents\", void 0);\n\n    this.contents = value;\n  }\n\n  getValue() {\n    return this.contents;\n  }\n\n  toPromise() {\n    return Promise.resolve(this.contents);\n  }\n\n  valueMaybe() {\n    return this.contents;\n  }\n\n  valueOrThrow() {\n    return this.contents;\n  }\n\n  promiseMaybe() {\n    return undefined;\n  }\n\n  errorMaybe() {\n    return undefined;\n  }\n\n  map(map) {\n    try {\n      const next = map(this.contents);\n      return Recoil_isPromise(next) ? loadableWithPromise(next) : isLoadable(next) ? next : loadableWithValue(next);\n    } catch (e) {\n      return Recoil_isPromise(e) ? // If we \"suspended\", then try again.\n      // errors and subsequent retries will be handled in 'loading' case\n      // $FlowFixMe[prop-missing]\n      loadableWithPromise(e.next(() => this.map(map))) : loadableWithError(e);\n    }\n  }\n\n}\n\nclass ErrorLoadable extends BaseLoadable {\n  constructor(error) {\n    super();\n\n    _defineProperty(this, \"state\", 'hasError');\n\n    _defineProperty(this, \"contents\", void 0);\n\n    this.contents = error;\n  }\n\n  getValue() {\n    throw this.contents;\n  }\n\n  toPromise() {\n    return Promise.reject(this.contents);\n  }\n\n  valueMaybe() {\n    return undefined;\n  }\n\n  promiseMaybe() {\n    return undefined;\n  }\n\n  errorMaybe() {\n    return this.contents;\n  }\n\n  errorOrThrow() {\n    return this.contents;\n  }\n\n  map(_map) {\n    // $FlowIssue[incompatible-return]\n    return this;\n  }\n\n}\n\nclass LoadingLoadable extends BaseLoadable {\n  constructor(promise) {\n    super();\n\n    _defineProperty(this, \"state\", 'loading');\n\n    _defineProperty(this, \"contents\", void 0);\n\n    this.contents = promise;\n  }\n\n  getValue() {\n    throw this.contents;\n  }\n\n  toPromise() {\n    return this.contents;\n  }\n\n  valueMaybe() {\n    return undefined;\n  }\n\n  promiseMaybe() {\n    return this.contents;\n  }\n\n  promiseOrThrow() {\n    return this.contents;\n  }\n\n  errorMaybe() {\n    return undefined;\n  }\n\n  map(map) {\n    return loadableWithPromise(this.contents.then(value => {\n      const next = map(value);\n\n      if (isLoadable(next)) {\n        const nextLoadable = next;\n\n        switch (nextLoadable.state) {\n          case 'hasValue':\n            return nextLoadable.contents;\n\n          case 'hasError':\n            throw nextLoadable.contents;\n\n          case 'loading':\n            return nextLoadable.contents;\n        }\n      } // $FlowIssue[incompatible-return]\n\n\n      return next;\n    }) // $FlowFixMe[incompatible-call]\n    .catch(e => {\n      if (Recoil_isPromise(e)) {\n        // we were \"suspended,\" try again\n        return e.then(() => this.map(map).contents);\n      }\n\n      throw e;\n    }));\n  }\n\n}\n\nfunction loadableWithValue(value) {\n  return Object.freeze(new ValueLoadable(value));\n}\n\nfunction loadableWithError(error) {\n  return Object.freeze(new ErrorLoadable(error));\n}\n\nfunction loadableWithPromise(promise) {\n  return Object.freeze(new LoadingLoadable(promise));\n}\n\nfunction loadableLoading() {\n  return Object.freeze(new LoadingLoadable(new Promise(() => {})));\n}\n\nfunction loadableAllArray(inputs) {\n  return inputs.every(i => i.state === 'hasValue') ? loadableWithValue(inputs.map(i => i.contents)) : inputs.some(i => i.state === 'hasError') ? loadableWithError(Recoil_nullthrows(inputs.find(i => i.state === 'hasError'), 'Invalid loadable passed to loadableAll').contents) : loadableWithPromise(Promise.all(inputs.map(i => i.contents)));\n}\n\nfunction loadableAll(inputs) {\n  const unwrapedInputs = Array.isArray(inputs) ? inputs : Object.getOwnPropertyNames(inputs).map(key => inputs[key]);\n  const normalizedInputs = unwrapedInputs.map(x => isLoadable(x) ? x : Recoil_isPromise(x) ? loadableWithPromise(x) : loadableWithValue(x));\n  const output = loadableAllArray(normalizedInputs);\n  return Array.isArray(inputs) ? // $FlowIssue[incompatible-return]\n  output : // Object.getOwnPropertyNames() has consistent key ordering with ES6\n  // $FlowIssue[incompatible-call]\n  output.map(outputs => Object.getOwnPropertyNames(inputs).reduce( // $FlowFixMe[invalid-computed-prop]\n  (out, key, idx) => ({ ...out,\n    [key]: outputs[idx]\n  }), {}));\n}\n\nfunction isLoadable(x) {\n  return x instanceof BaseLoadable;\n}\n\nconst LoadableStaticInterface = {\n  of: value => Recoil_isPromise(value) ? loadableWithPromise(value) : isLoadable(value) ? value : loadableWithValue(value),\n  error: error => loadableWithError(error),\n  // $FlowIssue[incompatible-return]\n  loading: () => loadableLoading(),\n  // $FlowIssue[unclear-type]\n  all: loadableAll,\n  isLoadable\n};\nvar Recoil_Loadable = {\n  loadableWithValue,\n  loadableWithError,\n  loadableWithPromise,\n  loadableLoading,\n  loadableAll,\n  isLoadable,\n  RecoilLoadable: LoadableStaticInterface\n};\n\nvar Recoil_Loadable_1 = Recoil_Loadable.loadableWithValue;\nvar Recoil_Loadable_2 = Recoil_Loadable.loadableWithError;\nvar Recoil_Loadable_3 = Recoil_Loadable.loadableWithPromise;\nvar Recoil_Loadable_4 = Recoil_Loadable.loadableLoading;\nvar Recoil_Loadable_5 = Recoil_Loadable.loadableAll;\nvar Recoil_Loadable_6 = Recoil_Loadable.isLoadable;\nvar Recoil_Loadable_7 = Recoil_Loadable.RecoilLoadable;\n\nvar Recoil_Loadable$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  loadableWithValue: Recoil_Loadable_1,\n  loadableWithError: Recoil_Loadable_2,\n  loadableWithPromise: Recoil_Loadable_3,\n  loadableLoading: Recoil_Loadable_4,\n  loadableAll: Recoil_Loadable_5,\n  isLoadable: Recoil_Loadable_6,\n  RecoilLoadable: Recoil_Loadable_7\n});\n\nconst env = {\n  RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED: true,\n  // Note: RECOIL_GKS_ENABLED settings will only be honored in OSS builds of Recoil\n  RECOIL_GKS_ENABLED: new Set(['recoil_hamt_2020', 'recoil_sync_external_store', 'recoil_suppress_rerender_in_callback', 'recoil_memory_managament_2020'])\n};\n\nfunction readProcessEnvBooleanFlag(name, set) {\n  var _process$env$name, _process$env$name$toL;\n\n  const sanitizedValue = (_process$env$name = process.env[name]) === null || _process$env$name === void 0 ? void 0 : (_process$env$name$toL = _process$env$name.toLowerCase()) === null || _process$env$name$toL === void 0 ? void 0 : _process$env$name$toL.trim();\n\n  if (sanitizedValue == null || sanitizedValue === '') {\n    return;\n  }\n\n  const allowedValues = ['true', 'false'];\n\n  if (!allowedValues.includes(sanitizedValue)) {\n    throw Recoil_err(`process.env.${name} value must be 'true', 'false', or empty: ${sanitizedValue}`);\n  }\n\n  set(sanitizedValue === 'true');\n}\n\nfunction readProcessEnvStringArrayFlag(name, set) {\n  var _process$env$name2;\n\n  const sanitizedValue = (_process$env$name2 = process.env[name]) === null || _process$env$name2 === void 0 ? void 0 : _process$env$name2.trim();\n\n  if (sanitizedValue == null || sanitizedValue === '') {\n    return;\n  }\n\n  set(sanitizedValue.split(/\\s*,\\s*|\\s+/));\n}\n/**\n * Allow NodeJS/NextJS/etc to set the initial state through process.env variable\n * Note:  we don't assume 'process' is available in all runtime environments\n *\n * @see https://github.com/facebookexperimental/Recoil/issues/733\n */\n\n\nfunction applyProcessEnvFlagOverrides() {\n  var _process;\n\n  // note: this check is needed in addition to the check below, runtime error will occur without it!\n  // eslint-disable-next-line fb-www/typeof-undefined\n  if (typeof process === 'undefined') {\n    return;\n  }\n\n  if (((_process = process) === null || _process === void 0 ? void 0 : _process.env) == null) {\n    return;\n  }\n\n  readProcessEnvBooleanFlag('RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED', value => {\n    env.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED = value;\n  });\n  readProcessEnvStringArrayFlag('RECOIL_GKS_ENABLED', value => {\n    value.forEach(gk => {\n      env.RECOIL_GKS_ENABLED.add(gk);\n    });\n  });\n}\n\napplyProcessEnvFlagOverrides();\nvar Recoil_RecoilEnv = env;\n\nfunction Recoil_gkx_OSS(gk) {\n  return Recoil_RecoilEnv.RECOIL_GKS_ENABLED.has(gk);\n}\n\nRecoil_gkx_OSS.setPass = gk => {\n  Recoil_RecoilEnv.RECOIL_GKS_ENABLED.add(gk);\n};\n\nRecoil_gkx_OSS.setFail = gk => {\n  Recoil_RecoilEnv.RECOIL_GKS_ENABLED.delete(gk);\n};\n\nRecoil_gkx_OSS.clear = () => {\n  Recoil_RecoilEnv.RECOIL_GKS_ENABLED.clear();\n};\n\nvar Recoil_gkx = Recoil_gkx_OSS; // @oss-only\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction recoverableViolation(message, _projectName, {\n  error\n} = {}) {\n  if (process.env.NODE_ENV !== \"production\") {\n    console.error(message, error);\n  }\n\n  return null;\n}\n\nvar recoverableViolation_1 = recoverableViolation;\n\n// @oss-only\n\n\nvar Recoil_recoverableViolation = recoverableViolation_1;\n\nvar _createMutableSource, _useMutableSource, _useSyncExternalStore;\n\n\n\n\n\n\n\nconst createMutableSource = // flowlint-next-line unclear-type:off\n(_createMutableSource = react.createMutableSource) !== null && _createMutableSource !== void 0 ? _createMutableSource : react.unstable_createMutableSource;\nconst useMutableSource = // flowlint-next-line unclear-type:off\n(_useMutableSource = react.useMutableSource) !== null && _useMutableSource !== void 0 ? _useMutableSource : react.unstable_useMutableSource; // https://github.com/reactwg/react-18/discussions/86\n\nconst useSyncExternalStore = // flowlint-next-line unclear-type:off\n(_useSyncExternalStore = react.useSyncExternalStore) !== null && _useSyncExternalStore !== void 0 ? _useSyncExternalStore : // flowlint-next-line unclear-type:off\nreact.unstable_useSyncExternalStore;\nlet ReactRendererVersionMismatchWarnOnce = false; // Check if the current renderer supports `useSyncExternalStore()`.\n// Since React goes through a proxy dispatcher and the current renderer can\n// change we can't simply check if `React.useSyncExternalStore()` is defined.\n\nfunction currentRendererSupportsUseSyncExternalStore() {\n  var _ReactCurrentDispatch;\n\n  // $FlowFixMe[incompatible-use]\n  const {\n    ReactCurrentDispatcher,\n    ReactCurrentOwner\n  } =\n  /* $FlowFixMe[prop-missing] This workaround was approved as a safer mechanism\n   * to detect if the current renderer supports useSyncExternalStore()\n   * https://fb.workplace.com/groups/reactjs/posts/9558682330846963/ */\n  react.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  const dispatcher = (_ReactCurrentDispatch = ReactCurrentDispatcher === null || ReactCurrentDispatcher === void 0 ? void 0 : ReactCurrentDispatcher.current) !== null && _ReactCurrentDispatch !== void 0 ? _ReactCurrentDispatch : ReactCurrentOwner.currentDispatcher;\n  const isUseSyncExternalStoreSupported = dispatcher.useSyncExternalStore != null;\n\n  if (useSyncExternalStore && !isUseSyncExternalStoreSupported && !ReactRendererVersionMismatchWarnOnce) {\n    ReactRendererVersionMismatchWarnOnce = true;\n    Recoil_recoverableViolation('A React renderer without React 18+ API support is being used with React 18+.');\n  }\n\n  return isUseSyncExternalStoreSupported;\n}\n\n/**\n * mode: The React API and approach to use for syncing state with React\n * early: Re-renders from Recoil updates occur:\n *    1) earlier\n *    2) in sync with React updates in the same batch\n *    3) before transaction observers instead of after.\n * concurrent: Is the current mode compatible with Concurrent Mode and useTransition()\n */\nfunction reactMode() {\n  // NOTE: This mode is currently broken with some Suspense cases\n  // see Recoil_selector-test.js\n  if (Recoil_gkx('recoil_transition_support')) {\n    return {\n      mode: 'TRANSITION_SUPPORT',\n      early: true,\n      concurrent: true\n    };\n  }\n\n  if (Recoil_gkx('recoil_sync_external_store') && useSyncExternalStore != null) {\n    return {\n      mode: 'SYNC_EXTERNAL_STORE',\n      early: true,\n      concurrent: false\n    };\n  }\n\n  if (Recoil_gkx('recoil_mutable_source') && useMutableSource != null && typeof window !== 'undefined' && !window.$disableRecoilValueMutableSource_TEMP_HACK_DO_NOT_USE) {\n    return Recoil_gkx('recoil_suppress_rerender_in_callback') ? {\n      mode: 'MUTABLE_SOURCE',\n      early: true,\n      concurrent: true\n    } : {\n      mode: 'MUTABLE_SOURCE',\n      early: false,\n      concurrent: false\n    };\n  }\n\n  return Recoil_gkx('recoil_suppress_rerender_in_callback') ? {\n    mode: 'LEGACY',\n    early: true,\n    concurrent: false\n  } : {\n    mode: 'LEGACY',\n    early: false,\n    concurrent: false\n  };\n} // TODO Need to figure out if there is a standard/open-source equivalent to see if hot module replacement is happening:\n\n\nfunction isFastRefreshEnabled() {\n  // @fb-only: const {isAcceptingUpdate} = require('__debug');\n  // @fb-only: return typeof isAcceptingUpdate === 'function' && isAcceptingUpdate();\n  return false; // @oss-only\n}\n\nvar Recoil_ReactMode = {\n  createMutableSource,\n  useMutableSource,\n  useSyncExternalStore,\n  currentRendererSupportsUseSyncExternalStore,\n  reactMode,\n  isFastRefreshEnabled\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\n// eslint-disable-next-line no-unused-vars\nclass AbstractRecoilValue {\n  constructor(newKey) {\n    _defineProperty(this, \"key\", void 0);\n\n    this.key = newKey;\n  }\n\n  toJSON() {\n    return {\n      key: this.key\n    };\n  }\n\n}\n\nclass RecoilState extends AbstractRecoilValue {}\n\nclass RecoilValueReadOnly extends AbstractRecoilValue {}\n\nfunction isRecoilValue(x) {\n  return x instanceof RecoilState || x instanceof RecoilValueReadOnly;\n}\n\nvar Recoil_RecoilValue = {\n  AbstractRecoilValue,\n  RecoilState,\n  RecoilValueReadOnly,\n  isRecoilValue\n};\n\nvar Recoil_RecoilValue_1 = Recoil_RecoilValue.AbstractRecoilValue;\nvar Recoil_RecoilValue_2 = Recoil_RecoilValue.RecoilState;\nvar Recoil_RecoilValue_3 = Recoil_RecoilValue.RecoilValueReadOnly;\nvar Recoil_RecoilValue_4 = Recoil_RecoilValue.isRecoilValue;\n\nvar Recoil_RecoilValue$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AbstractRecoilValue: Recoil_RecoilValue_1,\n  RecoilState: Recoil_RecoilValue_2,\n  RecoilValueReadOnly: Recoil_RecoilValue_3,\n  isRecoilValue: Recoil_RecoilValue_4\n});\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction sprintf(format, ...args) {\n  let index = 0;\n  return format.replace(/%s/g, () => String(args[index++]));\n}\n\nvar sprintf_1 = sprintf;\n\nfunction expectationViolation(format, ...args) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const message = sprintf_1.call(null, format, ...args);\n    const error = new Error(message);\n    error.name = 'Expectation Violation';\n    console.error(error);\n  }\n}\n\nvar expectationViolation_1 = expectationViolation;\n\n// @oss-only\n\n\nvar Recoil_expectationViolation = expectationViolation_1;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Creates a new iterable whose output is generated by passing the input\n * iterable's values through the mapper function.\n */\n\nfunction mapIterable(iterable, callback) {\n  // Use generator to create iterable/iterator\n  return function* () {\n    let index = 0;\n\n    for (const value of iterable) {\n      yield callback(value, index++);\n    }\n  }();\n}\n\nvar Recoil_mapIterable = mapIterable;\n\nconst {\n  isFastRefreshEnabled: isFastRefreshEnabled$1\n} = Recoil_ReactMode;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass DefaultValue {}\n\nconst DEFAULT_VALUE = new DefaultValue();\n// flowlint-next-line unclear-type:off\nconst nodes = new Map(); // flowlint-next-line unclear-type:off\n\nconst recoilValues = new Map();\n/* eslint-disable no-redeclare */\n\nfunction recoilValuesForKeys(keys) {\n  return Recoil_mapIterable(keys, key => Recoil_nullthrows(recoilValues.get(key)));\n}\n\nfunction checkForDuplicateAtomKey(key) {\n  if (nodes.has(key)) {\n    const message = `Duplicate atom key \"${key}\". This is a FATAL ERROR in\n      production. But it is safe to ignore this warning if it occurred because of\n      hot module replacement.`;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      // TODO Figure this out for open-source\n      if (!isFastRefreshEnabled$1()) {\n        Recoil_expectationViolation(message, 'recoil');\n      }\n    } else {\n      // @fb-only: recoverableViolation(message, 'recoil');\n      console.warn(message); // @oss-only\n    }\n  }\n}\n\nfunction registerNode(node) {\n  if (Recoil_RecoilEnv.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED) {\n    checkForDuplicateAtomKey(node.key);\n  }\n\n  nodes.set(node.key, node);\n  const recoilValue = node.set == null ? new Recoil_RecoilValue$1.RecoilValueReadOnly(node.key) : new Recoil_RecoilValue$1.RecoilState(node.key);\n  recoilValues.set(node.key, recoilValue);\n  return recoilValue;\n}\n/* eslint-enable no-redeclare */\n\n\nclass NodeMissingError extends Error {} // flowlint-next-line unclear-type:off\n\n\nfunction getNode(key) {\n  const node = nodes.get(key);\n\n  if (node == null) {\n    throw new NodeMissingError(`Missing definition for RecoilValue: \"${key}\"\"`);\n  }\n\n  return node;\n} // flowlint-next-line unclear-type:off\n\n\nfunction getNodeMaybe(key) {\n  return nodes.get(key);\n}\n\nconst configDeletionHandlers = new Map();\n\nfunction deleteNodeConfigIfPossible(key) {\n  var _node$shouldDeleteCon;\n\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  }\n\n  const node = nodes.get(key);\n\n  if (node !== null && node !== void 0 && (_node$shouldDeleteCon = node.shouldDeleteConfigOnRelease) !== null && _node$shouldDeleteCon !== void 0 && _node$shouldDeleteCon.call(node)) {\n    var _getConfigDeletionHan;\n\n    nodes.delete(key);\n    (_getConfigDeletionHan = getConfigDeletionHandler(key)) === null || _getConfigDeletionHan === void 0 ? void 0 : _getConfigDeletionHan();\n    configDeletionHandlers.delete(key);\n  }\n}\n\nfunction setConfigDeletionHandler(key, fn) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  }\n\n  if (fn === undefined) {\n    configDeletionHandlers.delete(key);\n  } else {\n    configDeletionHandlers.set(key, fn);\n  }\n}\n\nfunction getConfigDeletionHandler(key) {\n  return configDeletionHandlers.get(key);\n}\n\nvar Recoil_Node = {\n  nodes,\n  recoilValues,\n  registerNode,\n  getNode,\n  getNodeMaybe,\n  deleteNodeConfigIfPossible,\n  setConfigDeletionHandler,\n  getConfigDeletionHandler,\n  recoilValuesForKeys,\n  NodeMissingError,\n  DefaultValue,\n  DEFAULT_VALUE\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction enqueueExecution(s, f) {\n  f();\n}\n\nvar Recoil_Queue = {\n  enqueueExecution\n};\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar hamt_1 = createCommonjsModule(function (module) {\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\n/**\n    @fileOverview Hash Array Mapped Trie.\n\n    Code based on: https://github.com/exclipy/pdata\n*/\n\n\nvar hamt = {}; // export\n\n/* Configuration\n ******************************************************************************/\n\nvar SIZE = 5;\nvar BUCKET_SIZE = Math.pow(2, SIZE);\nvar MASK = BUCKET_SIZE - 1;\nvar MAX_INDEX_NODE = BUCKET_SIZE / 2;\nvar MIN_ARRAY_NODE = BUCKET_SIZE / 4;\n/*\n ******************************************************************************/\n\nvar nothing = {};\n\nvar constant = function constant(x) {\n  return function () {\n    return x;\n  };\n};\n/**\n    Get 32 bit hash of string.\n\n    Based on:\n    http://stackoverflow.com/questions/7616461/generate-a-hash-from-string-in-javascript-jquery\n*/\n\n\nvar hash = hamt.hash = function (str) {\n  var type = typeof str === 'undefined' ? 'undefined' : _typeof(str);\n  if (type === 'number') return str;\n  if (type !== 'string') str += '';\n  var hash = 0;\n\n  for (var i = 0, len = str.length; i < len; ++i) {\n    var c = str.charCodeAt(i);\n    hash = (hash << 5) - hash + c | 0;\n  }\n\n  return hash;\n};\n/* Bit Ops\n ******************************************************************************/\n\n/**\n    Hamming weight.\n\n    Taken from: http://jsperf.com/hamming-weight\n*/\n\n\nvar popcount = function popcount(x) {\n  x -= x >> 1 & 0x55555555;\n  x = (x & 0x33333333) + (x >> 2 & 0x33333333);\n  x = x + (x >> 4) & 0x0f0f0f0f;\n  x += x >> 8;\n  x += x >> 16;\n  return x & 0x7f;\n};\n\nvar hashFragment = function hashFragment(shift, h) {\n  return h >>> shift & MASK;\n};\n\nvar toBitmap = function toBitmap(x) {\n  return 1 << x;\n};\n\nvar fromBitmap = function fromBitmap(bitmap, bit) {\n  return popcount(bitmap & bit - 1);\n};\n/* Array Ops\n ******************************************************************************/\n\n/**\n    Set a value in an array.\n\n    @param mutate Should the input array be mutated?\n    @param at Index to change.\n    @param v New value\n    @param arr Array.\n*/\n\n\nvar arrayUpdate = function arrayUpdate(mutate, at, v, arr) {\n  var out = arr;\n\n  if (!mutate) {\n    var len = arr.length;\n    out = new Array(len);\n\n    for (var i = 0; i < len; ++i) {\n      out[i] = arr[i];\n    }\n  }\n\n  out[at] = v;\n  return out;\n};\n/**\n    Remove a value from an array.\n\n    @param mutate Should the input array be mutated?\n    @param at Index to remove.\n    @param arr Array.\n*/\n\n\nvar arraySpliceOut = function arraySpliceOut(mutate, at, arr) {\n  var newLen = arr.length - 1;\n  var i = 0;\n  var g = 0;\n  var out = arr;\n\n  if (mutate) {\n    i = g = at;\n  } else {\n    out = new Array(newLen);\n\n    while (i < at) {\n      out[g++] = arr[i++];\n    }\n  }\n\n  ++i;\n\n  while (i <= newLen) {\n    out[g++] = arr[i++];\n  }\n\n  if (mutate) {\n    out.length = newLen;\n  }\n\n  return out;\n};\n/**\n    Insert a value into an array.\n\n    @param mutate Should the input array be mutated?\n    @param at Index to insert at.\n    @param v Value to insert,\n    @param arr Array.\n*/\n\n\nvar arraySpliceIn = function arraySpliceIn(mutate, at, v, arr) {\n  var len = arr.length;\n\n  if (mutate) {\n    var _i = len;\n\n    while (_i >= at) {\n      arr[_i--] = arr[_i];\n    }\n\n    arr[at] = v;\n    return arr;\n  }\n\n  var i = 0,\n      g = 0;\n  var out = new Array(len + 1);\n\n  while (i < at) {\n    out[g++] = arr[i++];\n  }\n\n  out[at] = v;\n\n  while (i < len) {\n    out[++g] = arr[i++];\n  }\n\n  return out;\n};\n/* Node Structures\n ******************************************************************************/\n\n\nvar LEAF = 1;\nvar COLLISION = 2;\nvar INDEX = 3;\nvar ARRAY = 4;\n/**\n    Empty node.\n*/\n\nvar empty = {\n  __hamt_isEmpty: true\n};\n\nvar isEmptyNode = function isEmptyNode(x) {\n  return x === empty || x && x.__hamt_isEmpty;\n};\n/**\n    Leaf holding a value.\n\n    @member edit Edit of the node.\n    @member hash Hash of key.\n    @member key Key.\n    @member value Value stored.\n*/\n\n\nvar Leaf = function Leaf(edit, hash, key, value) {\n  return {\n    type: LEAF,\n    edit: edit,\n    hash: hash,\n    key: key,\n    value: value,\n    _modify: Leaf__modify\n  };\n};\n/**\n    Leaf holding multiple values with the same hash but different keys.\n\n    @member edit Edit of the node.\n    @member hash Hash of key.\n    @member children Array of collision children node.\n*/\n\n\nvar Collision = function Collision(edit, hash, children) {\n  return {\n    type: COLLISION,\n    edit: edit,\n    hash: hash,\n    children: children,\n    _modify: Collision__modify\n  };\n};\n/**\n    Internal node with a sparse set of children.\n\n    Uses a bitmap and array to pack children.\n\n  @member edit Edit of the node.\n    @member mask Bitmap that encode the positions of children in the array.\n    @member children Array of child nodes.\n*/\n\n\nvar IndexedNode = function IndexedNode(edit, mask, children) {\n  return {\n    type: INDEX,\n    edit: edit,\n    mask: mask,\n    children: children,\n    _modify: IndexedNode__modify\n  };\n};\n/**\n    Internal node with many children.\n\n    @member edit Edit of the node.\n    @member size Number of children.\n    @member children Array of child nodes.\n*/\n\n\nvar ArrayNode = function ArrayNode(edit, size, children) {\n  return {\n    type: ARRAY,\n    edit: edit,\n    size: size,\n    children: children,\n    _modify: ArrayNode__modify\n  };\n};\n/**\n    Is `node` a leaf node?\n*/\n\n\nvar isLeaf = function isLeaf(node) {\n  return node === empty || node.type === LEAF || node.type === COLLISION;\n};\n/* Internal node operations.\n ******************************************************************************/\n\n/**\n    Expand an indexed node into an array node.\n\n  @param edit Current edit.\n    @param frag Index of added child.\n    @param child Added child.\n    @param mask Index node mask before child added.\n    @param subNodes Index node children before child added.\n*/\n\n\nvar expand = function expand(edit, frag, child, bitmap, subNodes) {\n  var arr = [];\n  var bit = bitmap;\n  var count = 0;\n\n  for (var i = 0; bit; ++i) {\n    if (bit & 1) arr[i] = subNodes[count++];\n    bit >>>= 1;\n  }\n\n  arr[frag] = child;\n  return ArrayNode(edit, count + 1, arr);\n};\n/**\n    Collapse an array node into a indexed node.\n\n  @param edit Current edit.\n    @param count Number of elements in new array.\n    @param removed Index of removed element.\n    @param elements Array node children before remove.\n*/\n\n\nvar pack = function pack(edit, count, removed, elements) {\n  var children = new Array(count - 1);\n  var g = 0;\n  var bitmap = 0;\n\n  for (var i = 0, len = elements.length; i < len; ++i) {\n    if (i !== removed) {\n      var elem = elements[i];\n\n      if (elem && !isEmptyNode(elem)) {\n        children[g++] = elem;\n        bitmap |= 1 << i;\n      }\n    }\n  }\n\n  return IndexedNode(edit, bitmap, children);\n};\n/**\n    Merge two leaf nodes.\n\n    @param shift Current shift.\n    @param h1 Node 1 hash.\n    @param n1 Node 1.\n    @param h2 Node 2 hash.\n    @param n2 Node 2.\n*/\n\n\nvar mergeLeaves = function mergeLeaves(edit, shift, h1, n1, h2, n2) {\n  if (h1 === h2) return Collision(edit, h1, [n2, n1]);\n  var subH1 = hashFragment(shift, h1);\n  var subH2 = hashFragment(shift, h2);\n  return IndexedNode(edit, toBitmap(subH1) | toBitmap(subH2), subH1 === subH2 ? [mergeLeaves(edit, shift + SIZE, h1, n1, h2, n2)] : subH1 < subH2 ? [n1, n2] : [n2, n1]);\n};\n/**\n    Update an entry in a collision list.\n\n    @param mutate Should mutation be used?\n    @param edit Current edit.\n    @param keyEq Key compare function.\n    @param hash Hash of collision.\n    @param list Collision list.\n    @param f Update function.\n    @param k Key to update.\n    @param size Size ref.\n*/\n\n\nvar updateCollisionList = function updateCollisionList(mutate, edit, keyEq, h, list, f, k, size) {\n  var len = list.length;\n\n  for (var i = 0; i < len; ++i) {\n    var child = list[i];\n\n    if (keyEq(k, child.key)) {\n      var value = child.value;\n\n      var _newValue = f(value);\n\n      if (_newValue === value) return list;\n\n      if (_newValue === nothing) {\n        --size.value;\n        return arraySpliceOut(mutate, i, list);\n      }\n\n      return arrayUpdate(mutate, i, Leaf(edit, h, k, _newValue), list);\n    }\n  }\n\n  var newValue = f();\n  if (newValue === nothing) return list;\n  ++size.value;\n  return arrayUpdate(mutate, len, Leaf(edit, h, k, newValue), list);\n};\n\nvar canEditNode = function canEditNode(edit, node) {\n  return edit === node.edit;\n};\n/* Editing\n ******************************************************************************/\n\n\nvar Leaf__modify = function Leaf__modify(edit, keyEq, shift, f, h, k, size) {\n  if (keyEq(k, this.key)) {\n    var _v = f(this.value);\n\n    if (_v === this.value) return this;else if (_v === nothing) {\n      --size.value;\n      return empty;\n    }\n\n    if (canEditNode(edit, this)) {\n      this.value = _v;\n      return this;\n    }\n\n    return Leaf(edit, h, k, _v);\n  }\n\n  var v = f();\n  if (v === nothing) return this;\n  ++size.value;\n  return mergeLeaves(edit, shift, this.hash, this, h, Leaf(edit, h, k, v));\n};\n\nvar Collision__modify = function Collision__modify(edit, keyEq, shift, f, h, k, size) {\n  if (h === this.hash) {\n    var canEdit = canEditNode(edit, this);\n    var list = updateCollisionList(canEdit, edit, keyEq, this.hash, this.children, f, k, size);\n    if (list === this.children) return this;\n    return list.length > 1 ? Collision(edit, this.hash, list) : list[0]; // collapse single element collision list\n  }\n\n  var v = f();\n  if (v === nothing) return this;\n  ++size.value;\n  return mergeLeaves(edit, shift, this.hash, this, h, Leaf(edit, h, k, v));\n};\n\nvar IndexedNode__modify = function IndexedNode__modify(edit, keyEq, shift, f, h, k, size) {\n  var mask = this.mask;\n  var children = this.children;\n  var frag = hashFragment(shift, h);\n  var bit = toBitmap(frag);\n  var indx = fromBitmap(mask, bit);\n  var exists = mask & bit;\n  var current = exists ? children[indx] : empty;\n\n  var child = current._modify(edit, keyEq, shift + SIZE, f, h, k, size);\n\n  if (current === child) return this;\n  var canEdit = canEditNode(edit, this);\n  var bitmap = mask;\n  var newChildren = void 0;\n\n  if (exists && isEmptyNode(child)) {\n    // remove\n    bitmap &= ~bit;\n    if (!bitmap) return empty;\n    if (children.length <= 2 && isLeaf(children[indx ^ 1])) return children[indx ^ 1]; // collapse\n\n    newChildren = arraySpliceOut(canEdit, indx, children);\n  } else if (!exists && !isEmptyNode(child)) {\n    // add\n    if (children.length >= MAX_INDEX_NODE) return expand(edit, frag, child, mask, children);\n    bitmap |= bit;\n    newChildren = arraySpliceIn(canEdit, indx, child, children);\n  } else {\n    // modify\n    newChildren = arrayUpdate(canEdit, indx, child, children);\n  }\n\n  if (canEdit) {\n    this.mask = bitmap;\n    this.children = newChildren;\n    return this;\n  }\n\n  return IndexedNode(edit, bitmap, newChildren);\n};\n\nvar ArrayNode__modify = function ArrayNode__modify(edit, keyEq, shift, f, h, k, size) {\n  var count = this.size;\n  var children = this.children;\n  var frag = hashFragment(shift, h);\n  var child = children[frag];\n\n  var newChild = (child || empty)._modify(edit, keyEq, shift + SIZE, f, h, k, size);\n\n  if (child === newChild) return this;\n  var canEdit = canEditNode(edit, this);\n  var newChildren = void 0;\n\n  if (isEmptyNode(child) && !isEmptyNode(newChild)) {\n    // add\n    ++count;\n    newChildren = arrayUpdate(canEdit, frag, newChild, children);\n  } else if (!isEmptyNode(child) && isEmptyNode(newChild)) {\n    // remove\n    --count;\n    if (count <= MIN_ARRAY_NODE) return pack(edit, count, frag, children);\n    newChildren = arrayUpdate(canEdit, frag, empty, children);\n  } else {\n    // modify\n    newChildren = arrayUpdate(canEdit, frag, newChild, children);\n  }\n\n  if (canEdit) {\n    this.size = count;\n    this.children = newChildren;\n    return this;\n  }\n\n  return ArrayNode(edit, count, newChildren);\n};\n\nempty._modify = function (edit, keyEq, shift, f, h, k, size) {\n  var v = f();\n  if (v === nothing) return empty;\n  ++size.value;\n  return Leaf(edit, h, k, v);\n};\n/*\n ******************************************************************************/\n\n\nfunction Map(editable, edit, config, root, size) {\n  this._editable = editable;\n  this._edit = edit;\n  this._config = config;\n  this._root = root;\n  this._size = size;\n}\n\nMap.prototype.setTree = function (newRoot, newSize) {\n  if (this._editable) {\n    this._root = newRoot;\n    this._size = newSize;\n    return this;\n  }\n\n  return newRoot === this._root ? this : new Map(this._editable, this._edit, this._config, newRoot, newSize);\n};\n/* Queries\n ******************************************************************************/\n\n/**\n    Lookup the value for `key` in `map` using a custom `hash`.\n\n    Returns the value or `alt` if none.\n*/\n\n\nvar tryGetHash = hamt.tryGetHash = function (alt, hash, key, map) {\n  var node = map._root;\n  var shift = 0;\n  var keyEq = map._config.keyEq;\n\n  while (true) {\n    switch (node.type) {\n      case LEAF:\n        {\n          return keyEq(key, node.key) ? node.value : alt;\n        }\n\n      case COLLISION:\n        {\n          if (hash === node.hash) {\n            var children = node.children;\n\n            for (var i = 0, len = children.length; i < len; ++i) {\n              var child = children[i];\n              if (keyEq(key, child.key)) return child.value;\n            }\n          }\n\n          return alt;\n        }\n\n      case INDEX:\n        {\n          var frag = hashFragment(shift, hash);\n          var bit = toBitmap(frag);\n\n          if (node.mask & bit) {\n            node = node.children[fromBitmap(node.mask, bit)];\n            shift += SIZE;\n            break;\n          }\n\n          return alt;\n        }\n\n      case ARRAY:\n        {\n          node = node.children[hashFragment(shift, hash)];\n\n          if (node) {\n            shift += SIZE;\n            break;\n          }\n\n          return alt;\n        }\n\n      default:\n        return alt;\n    }\n  }\n};\n\nMap.prototype.tryGetHash = function (alt, hash, key) {\n  return tryGetHash(alt, hash, key, this);\n};\n/**\n    Lookup the value for `key` in `map` using internal hash function.\n\n    @see `tryGetHash`\n*/\n\n\nvar tryGet = hamt.tryGet = function (alt, key, map) {\n  return tryGetHash(alt, map._config.hash(key), key, map);\n};\n\nMap.prototype.tryGet = function (alt, key) {\n  return tryGet(alt, key, this);\n};\n/**\n    Lookup the value for `key` in `map` using a custom `hash`.\n\n    Returns the value or `undefined` if none.\n*/\n\n\nvar getHash = hamt.getHash = function (hash, key, map) {\n  return tryGetHash(undefined, hash, key, map);\n};\n\nMap.prototype.getHash = function (hash, key) {\n  return getHash(hash, key, this);\n};\n/**\n    Lookup the value for `key` in `map` using internal hash function.\n\n    @see `get`\n*/\n\n\nvar get = hamt.get = function (key, map) {\n  return tryGetHash(undefined, map._config.hash(key), key, map);\n};\n\nMap.prototype.get = function (key, alt) {\n  return tryGet(alt, key, this);\n};\n/**\n    Does an entry exist for `key` in `map`? Uses custom `hash`.\n*/\n\n\nvar hasHash = hamt.has = function (hash, key, map) {\n  return tryGetHash(nothing, hash, key, map) !== nothing;\n};\n\nMap.prototype.hasHash = function (hash, key) {\n  return hasHash(hash, key, this);\n};\n/**\n    Does an entry exist for `key` in `map`? Uses internal hash function.\n*/\n\n\nvar has = hamt.has = function (key, map) {\n  return hasHash(map._config.hash(key), key, map);\n};\n\nMap.prototype.has = function (key) {\n  return has(key, this);\n};\n\nvar defKeyCompare = function defKeyCompare(x, y) {\n  return x === y;\n};\n/**\n    Create an empty map.\n\n    @param config Configuration.\n*/\n\n\nhamt.make = function (config) {\n  return new Map(0, 0, {\n    keyEq: config && config.keyEq || defKeyCompare,\n    hash: config && config.hash || hash\n  }, empty, 0);\n};\n/**\n    Empty map.\n*/\n\n\nhamt.empty = hamt.make();\n/**\n    Does `map` contain any elements?\n*/\n\nvar isEmpty = hamt.isEmpty = function (map) {\n  return map && !!isEmptyNode(map._root);\n};\n\nMap.prototype.isEmpty = function () {\n  return isEmpty(this);\n};\n/* Updates\n ******************************************************************************/\n\n/**\n    Alter the value stored for `key` in `map` using function `f` using\n    custom hash.\n\n    `f` is invoked with the current value for `k` if it exists,\n    or no arguments if no such value exists. `modify` will always either\n    update or insert a value into the map.\n\n    Returns a map with the modified value. Does not alter `map`.\n*/\n\n\nvar modifyHash = hamt.modifyHash = function (f, hash, key, map) {\n  var size = {\n    value: map._size\n  };\n\n  var newRoot = map._root._modify(map._editable ? map._edit : NaN, map._config.keyEq, 0, f, hash, key, size);\n\n  return map.setTree(newRoot, size.value);\n};\n\nMap.prototype.modifyHash = function (hash, key, f) {\n  return modifyHash(f, hash, key, this);\n};\n/**\n    Alter the value stored for `key` in `map` using function `f` using\n    internal hash function.\n\n    @see `modifyHash`\n*/\n\n\nvar modify = hamt.modify = function (f, key, map) {\n  return modifyHash(f, map._config.hash(key), key, map);\n};\n\nMap.prototype.modify = function (key, f) {\n  return modify(f, key, this);\n};\n/**\n    Store `value` for `key` in `map` using custom `hash`.\n\n    Returns a map with the modified value. Does not alter `map`.\n*/\n\n\nvar setHash = hamt.setHash = function (hash, key, value, map) {\n  return modifyHash(constant(value), hash, key, map);\n};\n\nMap.prototype.setHash = function (hash, key, value) {\n  return setHash(hash, key, value, this);\n};\n/**\n    Store `value` for `key` in `map` using internal hash function.\n\n    @see `setHash`\n*/\n\n\nvar set = hamt.set = function (key, value, map) {\n  return setHash(map._config.hash(key), key, value, map);\n};\n\nMap.prototype.set = function (key, value) {\n  return set(key, value, this);\n};\n/**\n    Remove the entry for `key` in `map`.\n\n    Returns a map with the value removed. Does not alter `map`.\n*/\n\n\nvar del = constant(nothing);\n\nvar removeHash = hamt.removeHash = function (hash, key, map) {\n  return modifyHash(del, hash, key, map);\n};\n\nMap.prototype.removeHash = Map.prototype.deleteHash = function (hash, key) {\n  return removeHash(hash, key, this);\n};\n/**\n    Remove the entry for `key` in `map` using internal hash function.\n\n    @see `removeHash`\n*/\n\n\nvar remove = hamt.remove = function (key, map) {\n  return removeHash(map._config.hash(key), key, map);\n};\n\nMap.prototype.remove = Map.prototype.delete = function (key) {\n  return remove(key, this);\n};\n/* Mutation\n ******************************************************************************/\n\n/**\n    Mark `map` as mutable.\n */\n\n\nvar beginMutation = hamt.beginMutation = function (map) {\n  return new Map(map._editable + 1, map._edit + 1, map._config, map._root, map._size);\n};\n\nMap.prototype.beginMutation = function () {\n  return beginMutation(this);\n};\n/**\n    Mark `map` as immutable.\n */\n\n\nvar endMutation = hamt.endMutation = function (map) {\n  map._editable = map._editable && map._editable - 1;\n  return map;\n};\n\nMap.prototype.endMutation = function () {\n  return endMutation(this);\n};\n/**\n    Mutate `map` within the context of `f`.\n    @param f\n    @param map HAMT\n*/\n\n\nvar mutate = hamt.mutate = function (f, map) {\n  var transient = beginMutation(map);\n  f(transient);\n  return endMutation(transient);\n};\n\nMap.prototype.mutate = function (f) {\n  return mutate(f, this);\n};\n/* Traversal\n ******************************************************************************/\n\n/**\n    Apply a continuation.\n*/\n\n\nvar appk = function appk(k) {\n  return k && lazyVisitChildren(k[0], k[1], k[2], k[3], k[4]);\n};\n/**\n    Recursively visit all values stored in an array of nodes lazily.\n*/\n\n\nvar lazyVisitChildren = function lazyVisitChildren(len, children, i, f, k) {\n  while (i < len) {\n    var child = children[i++];\n    if (child && !isEmptyNode(child)) return lazyVisit(child, f, [len, children, i, f, k]);\n  }\n\n  return appk(k);\n};\n/**\n    Recursively visit all values stored in `node` lazily.\n*/\n\n\nvar lazyVisit = function lazyVisit(node, f, k) {\n  switch (node.type) {\n    case LEAF:\n      return {\n        value: f(node),\n        rest: k\n      };\n\n    case COLLISION:\n    case ARRAY:\n    case INDEX:\n      var children = node.children;\n      return lazyVisitChildren(children.length, children, 0, f, k);\n\n    default:\n      return appk(k);\n  }\n};\n\nvar DONE = {\n  done: true\n};\n/**\n    Javascript iterator over a map.\n*/\n\nfunction MapIterator(v) {\n  this.v = v;\n}\n\nMapIterator.prototype.next = function () {\n  if (!this.v) return DONE;\n  var v0 = this.v;\n  this.v = appk(v0.rest);\n  return v0;\n};\n\nMapIterator.prototype[Symbol.iterator] = function () {\n  return this;\n};\n/**\n    Lazily visit each value in map with function `f`.\n*/\n\n\nvar visit = function visit(map, f) {\n  return new MapIterator(lazyVisit(map._root, f));\n};\n/**\n    Get a Javascsript iterator of `map`.\n\n    Iterates over `[key, value]` arrays.\n*/\n\n\nvar buildPairs = function buildPairs(x) {\n  return [x.key, x.value];\n};\n\nvar entries = hamt.entries = function (map) {\n  return visit(map, buildPairs);\n};\n\nMap.prototype.entries = Map.prototype[Symbol.iterator] = function () {\n  return entries(this);\n};\n/**\n    Get array of all keys in `map`.\n\n    Order is not guaranteed.\n*/\n\n\nvar buildKeys = function buildKeys(x) {\n  return x.key;\n};\n\nvar keys = hamt.keys = function (map) {\n  return visit(map, buildKeys);\n};\n\nMap.prototype.keys = function () {\n  return keys(this);\n};\n/**\n    Get array of all values in `map`.\n\n    Order is not guaranteed, duplicates are preserved.\n*/\n\n\nvar buildValues = function buildValues(x) {\n  return x.value;\n};\n\nvar values = hamt.values = Map.prototype.values = function (map) {\n  return visit(map, buildValues);\n};\n\nMap.prototype.values = function () {\n  return values(this);\n};\n/* Fold\n ******************************************************************************/\n\n/**\n    Visit every entry in the map, aggregating data.\n\n    Order of nodes is not guaranteed.\n\n    @param f Function mapping accumulated value, value, and key to new value.\n    @param z Starting value.\n    @param m HAMT\n*/\n\n\nvar fold = hamt.fold = function (f, z, m) {\n  var root = m._root;\n  if (root.type === LEAF) return f(z, root.value, root.key);\n  var toVisit = [root.children];\n  var children = void 0;\n\n  while (children = toVisit.pop()) {\n    for (var i = 0, len = children.length; i < len;) {\n      var child = children[i++];\n\n      if (child && child.type) {\n        if (child.type === LEAF) z = f(z, child.value, child.key);else toVisit.push(child.children);\n      }\n    }\n  }\n\n  return z;\n};\n\nMap.prototype.fold = function (f, z) {\n  return fold(f, z, this);\n};\n/**\n    Visit every entry in the map, aggregating data.\n\n    Order of nodes is not guaranteed.\n\n    @param f Function invoked with value and key\n    @param map HAMT\n*/\n\n\nvar forEach = hamt.forEach = function (f, map) {\n  return fold(function (_, value, key) {\n    return f(value, key, map);\n  }, null, map);\n};\n\nMap.prototype.forEach = function (f) {\n  return forEach(f, this);\n};\n/* Aggregate\n ******************************************************************************/\n\n/**\n    Get the number of entries in `map`.\n*/\n\n\nvar count = hamt.count = function (map) {\n  return map._size;\n};\n\nMap.prototype.count = function () {\n  return count(this);\n};\n\nObject.defineProperty(Map.prototype, 'size', {\n  get: Map.prototype.count\n});\n/* Export\n ******************************************************************************/\n\nif ( module.exports) {\n  module.exports = hamt;\n} else {\n  undefined.hamt = hamt;\n}\n});\n\nclass BuiltInMap {\n  constructor(existing) {\n    _defineProperty(this, \"_map\", void 0);\n\n    this._map = new Map(existing === null || existing === void 0 ? void 0 : existing.entries());\n  }\n\n  keys() {\n    return this._map.keys();\n  }\n\n  entries() {\n    return this._map.entries();\n  }\n\n  get(k) {\n    return this._map.get(k);\n  }\n\n  has(k) {\n    return this._map.has(k);\n  }\n\n  set(k, v) {\n    this._map.set(k, v);\n\n    return this;\n  }\n\n  delete(k) {\n    this._map.delete(k);\n\n    return this;\n  }\n\n  clone() {\n    return persistentMap(this);\n  }\n\n  toMap() {\n    return new Map(this._map);\n  }\n\n}\n\nclass HashArrayMappedTrieMap {\n  // Because hamt.empty is not a function there is no way to introduce type\n  // parameters on it, so empty is typed as HAMTPlusMap<string, mixed>.\n  // $FlowIssue\n  constructor(existing) {\n    _defineProperty(this, \"_hamt\", hamt_1.empty.beginMutation());\n\n    if (existing instanceof HashArrayMappedTrieMap) {\n      const h = existing._hamt.endMutation();\n\n      existing._hamt = h.beginMutation();\n      this._hamt = h.beginMutation();\n    } else if (existing) {\n      for (const [k, v] of existing.entries()) {\n        this._hamt.set(k, v);\n      }\n    }\n  }\n\n  keys() {\n    return this._hamt.keys();\n  }\n\n  entries() {\n    return this._hamt.entries();\n  }\n\n  get(k) {\n    return this._hamt.get(k);\n  }\n\n  has(k) {\n    return this._hamt.has(k);\n  }\n\n  set(k, v) {\n    this._hamt.set(k, v);\n\n    return this;\n  }\n\n  delete(k) {\n    this._hamt.delete(k);\n\n    return this;\n  }\n\n  clone() {\n    return persistentMap(this);\n  }\n\n  toMap() {\n    return new Map(this._hamt);\n  }\n\n}\n\nfunction persistentMap(existing) {\n  if (Recoil_gkx('recoil_hamt_2020')) {\n    return new HashArrayMappedTrieMap(existing);\n  } else {\n    return new BuiltInMap(existing);\n  }\n}\n\nvar Recoil_PersistentMap = {\n  persistentMap\n};\n\nvar Recoil_PersistentMap_1 = Recoil_PersistentMap.persistentMap;\n\nvar Recoil_PersistentMap$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  persistentMap: Recoil_PersistentMap_1\n});\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Returns a set containing all of the values from the first set that are not\n * present in any of the subsequent sets.\n *\n * Note: this is written procedurally (i.e., without filterSet) for performant\n * use in tight loops.\n */\n\nfunction differenceSets(set, ...setsWithValuesToRemove) {\n  const ret = new Set();\n\n  FIRST: for (const value of set) {\n    for (const otherSet of setsWithValuesToRemove) {\n      if (otherSet.has(value)) {\n        continue FIRST;\n      }\n    }\n\n    ret.add(value);\n  }\n\n  return ret;\n}\n\nvar Recoil_differenceSets = differenceSets;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Returns a new Map object with the same keys as the original, but with the\n * values replaced with the output of the given callback function.\n */\n\nfunction mapMap(map, callback) {\n  const result = new Map();\n  map.forEach((value, key) => {\n    result.set(key, callback(value, key));\n  });\n  return result;\n}\n\nvar Recoil_mapMap = mapMap;\n\nfunction makeGraph() {\n  return {\n    nodeDeps: new Map(),\n    nodeToNodeSubscriptions: new Map()\n  };\n}\n\nfunction cloneGraph(graph) {\n  return {\n    nodeDeps: Recoil_mapMap(graph.nodeDeps, s => new Set(s)),\n    nodeToNodeSubscriptions: Recoil_mapMap(graph.nodeToNodeSubscriptions, s => new Set(s))\n  };\n} // Note that this overwrites the deps of existing nodes, rather than unioning\n// the new deps with the old deps.\n\n\nfunction mergeDepsIntoGraph(key, newDeps, graph, // If olderGraph is given then we will not overwrite changes made to the given\n// graph compared with olderGraph:\nolderGraph) {\n  const {\n    nodeDeps,\n    nodeToNodeSubscriptions\n  } = graph;\n  const oldDeps = nodeDeps.get(key);\n\n  if (oldDeps && olderGraph && oldDeps !== olderGraph.nodeDeps.get(key)) {\n    return;\n  } // Update nodeDeps:\n\n\n  nodeDeps.set(key, newDeps); // Add new deps to nodeToNodeSubscriptions:\n\n  const addedDeps = oldDeps == null ? newDeps : Recoil_differenceSets(newDeps, oldDeps);\n\n  for (const dep of addedDeps) {\n    if (!nodeToNodeSubscriptions.has(dep)) {\n      nodeToNodeSubscriptions.set(dep, new Set());\n    }\n\n    const existing = Recoil_nullthrows(nodeToNodeSubscriptions.get(dep));\n    existing.add(key);\n  } // Remove removed deps from nodeToNodeSubscriptions:\n\n\n  if (oldDeps) {\n    const removedDeps = Recoil_differenceSets(oldDeps, newDeps);\n\n    for (const dep of removedDeps) {\n      if (!nodeToNodeSubscriptions.has(dep)) {\n        return;\n      }\n\n      const existing = Recoil_nullthrows(nodeToNodeSubscriptions.get(dep));\n      existing.delete(key);\n\n      if (existing.size === 0) {\n        nodeToNodeSubscriptions.delete(dep);\n      }\n    }\n  }\n}\n\nfunction saveDepsToStore(key, deps, store, version) {\n  var _storeState$nextTree, _storeState$previousT, _storeState$previousT2, _storeState$previousT3;\n\n  const storeState = store.getState();\n\n  if (!(version === storeState.currentTree.version || version === ((_storeState$nextTree = storeState.nextTree) === null || _storeState$nextTree === void 0 ? void 0 : _storeState$nextTree.version) || version === ((_storeState$previousT = storeState.previousTree) === null || _storeState$previousT === void 0 ? void 0 : _storeState$previousT.version))) {\n    Recoil_recoverableViolation('Tried to save dependencies to a discarded tree');\n  } // Merge the dependencies discovered into the store's dependency map\n  // for the version that was read:\n\n\n  const graph = store.getGraph(version);\n  mergeDepsIntoGraph(key, deps, graph); // If this version is not the latest version, also write these dependencies\n  // into later versions if they don't already have their own:\n\n  if (version === ((_storeState$previousT2 = storeState.previousTree) === null || _storeState$previousT2 === void 0 ? void 0 : _storeState$previousT2.version)) {\n    const currentGraph = store.getGraph(storeState.currentTree.version);\n    mergeDepsIntoGraph(key, deps, currentGraph, graph);\n  }\n\n  if (version === ((_storeState$previousT3 = storeState.previousTree) === null || _storeState$previousT3 === void 0 ? void 0 : _storeState$previousT3.version) || version === storeState.currentTree.version) {\n    var _storeState$nextTree2;\n\n    const nextVersion = (_storeState$nextTree2 = storeState.nextTree) === null || _storeState$nextTree2 === void 0 ? void 0 : _storeState$nextTree2.version;\n\n    if (nextVersion !== undefined) {\n      const nextGraph = store.getGraph(nextVersion);\n      mergeDepsIntoGraph(key, deps, nextGraph, graph);\n    }\n  }\n}\n\nvar Recoil_Graph = {\n  cloneGraph,\n  graph: makeGraph,\n  saveDepsToStore\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nlet nextTreeStateVersion = 0;\n\nconst getNextTreeStateVersion = () => nextTreeStateVersion++;\n\nlet nextStoreID = 0;\n\nconst getNextStoreID = () => nextStoreID++;\n\nlet nextComponentID = 0;\n\nconst getNextComponentID = () => nextComponentID++;\n\nvar Recoil_Keys = {\n  getNextTreeStateVersion,\n  getNextStoreID,\n  getNextComponentID\n};\n\nconst {\n  persistentMap: persistentMap$1\n} = Recoil_PersistentMap$1;\n\nconst {\n  graph\n} = Recoil_Graph;\n\nconst {\n  getNextTreeStateVersion: getNextTreeStateVersion$1\n} = Recoil_Keys;\n\nfunction makeEmptyTreeState() {\n  const version = getNextTreeStateVersion$1();\n  return {\n    version,\n    stateID: version,\n    transactionMetadata: {},\n    dirtyAtoms: new Set(),\n    atomValues: persistentMap$1(),\n    nonvalidatedAtoms: persistentMap$1()\n  };\n}\n\nfunction makeEmptyStoreState() {\n  const currentTree = makeEmptyTreeState();\n  return {\n    currentTree,\n    nextTree: null,\n    previousTree: null,\n    commitDepth: 0,\n    knownAtoms: new Set(),\n    knownSelectors: new Set(),\n    transactionSubscriptions: new Map(),\n    nodeTransactionSubscriptions: new Map(),\n    nodeToComponentSubscriptions: new Map(),\n    queuedComponentCallbacks_DEPRECATED: [],\n    suspendedComponentResolvers: new Set(),\n    graphsByVersion: new Map().set(currentTree.version, graph()),\n    retention: {\n      referenceCounts: new Map(),\n      nodesRetainedByZone: new Map(),\n      retainablesToCheckForRelease: new Set()\n    },\n    nodeCleanupFunctions: new Map()\n  };\n}\n\nvar Recoil_State = {\n  makeEmptyTreeState,\n  makeEmptyStoreState,\n  getNextTreeStateVersion: getNextTreeStateVersion$1\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nclass RetentionZone {}\n\nfunction retentionZone() {\n  return new RetentionZone();\n}\n\nvar Recoil_RetentionZone = {\n  RetentionZone,\n  retentionZone\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Utilities for working with built-in Maps and Sets without mutating them.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction setByAddingToSet(set, v) {\n  const next = new Set(set);\n  next.add(v);\n  return next;\n}\n\nfunction setByDeletingFromSet(set, v) {\n  const next = new Set(set);\n  next.delete(v);\n  return next;\n}\n\nfunction mapBySettingInMap(map, k, v) {\n  const next = new Map(map);\n  next.set(k, v);\n  return next;\n}\n\nfunction mapByUpdatingInMap(map, k, updater) {\n  const next = new Map(map);\n  next.set(k, updater(next.get(k)));\n  return next;\n}\n\nfunction mapByDeletingFromMap(map, k) {\n  const next = new Map(map);\n  next.delete(k);\n  return next;\n}\n\nfunction mapByDeletingMultipleFromMap(map, ks) {\n  const next = new Map(map);\n  ks.forEach(k => next.delete(k));\n  return next;\n}\n\nvar Recoil_CopyOnWrite = {\n  setByAddingToSet,\n  setByDeletingFromSet,\n  mapBySettingInMap,\n  mapByUpdatingInMap,\n  mapByDeletingFromMap,\n  mapByDeletingMultipleFromMap\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Creates a new iterable whose output is generated by passing the input\n * iterable's values through the filter function.\n */\n\nfunction* filterIterable(iterable, predicate) {\n  // Use generator to create iterable/iterator\n  let index = 0;\n\n  for (const value of iterable) {\n    if (predicate(value, index++)) {\n      yield value;\n    }\n  }\n}\n\nvar Recoil_filterIterable = filterIterable;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Return a proxy object based on the provided base and factories objects.\n * The proxy will include all properties of the base object as-is.\n * The factories object contains callbacks to obtain the values of the properies\n * for its keys.\n *\n * This is useful for providing users an object where some properties may be\n * lazily computed only on first access.\n */\n// $FlowIssue[unclear-type]\n\nfunction lazyProxy(base, factories) {\n  const proxy = new Proxy(base, {\n    // Compute and cache lazy property if not already done.\n    get: (target, prop) => {\n      if (!(prop in target) && prop in factories) {\n        target[prop] = factories[prop]();\n      }\n\n      return target[prop];\n    },\n    // This method allows user to iterate keys as normal\n    ownKeys: target => {\n\n      return Object.keys(target);\n    }\n  }); // $FlowIssue[incompatible-return]\n\n  return proxy;\n}\n\nvar Recoil_lazyProxy = lazyProxy;\n\nconst {\n  getNode: getNode$1,\n  getNodeMaybe: getNodeMaybe$1,\n  recoilValuesForKeys: recoilValuesForKeys$1\n} = Recoil_Node;\n\nconst {\n  RetentionZone: RetentionZone$1\n} = Recoil_RetentionZone;\n\nconst {\n  setByAddingToSet: setByAddingToSet$1\n} = Recoil_CopyOnWrite;\n\n\n\n\n\n\n\n // flowlint-next-line unclear-type:off\n\n\nconst emptySet = Object.freeze(new Set());\n\nclass ReadOnlyRecoilValueError extends Error {}\n\nfunction initializeRetentionForNode(store, nodeKey, retainedBy) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return () => undefined;\n  }\n\n  const {\n    nodesRetainedByZone\n  } = store.getState().retention;\n\n  function addToZone(zone) {\n    let set = nodesRetainedByZone.get(zone);\n\n    if (!set) {\n      nodesRetainedByZone.set(zone, set = new Set());\n    }\n\n    set.add(nodeKey);\n  }\n\n  if (retainedBy instanceof RetentionZone$1) {\n    addToZone(retainedBy);\n  } else if (Array.isArray(retainedBy)) {\n    for (const zone of retainedBy) {\n      addToZone(zone);\n    }\n  }\n\n  return () => {\n    if (!Recoil_gkx('recoil_memory_managament_2020')) {\n      return;\n    }\n\n    const {\n      retention\n    } = store.getState();\n\n    function deleteFromZone(zone) {\n      const set = retention.nodesRetainedByZone.get(zone);\n      set === null || set === void 0 ? void 0 : set.delete(nodeKey);\n\n      if (set && set.size === 0) {\n        retention.nodesRetainedByZone.delete(zone);\n      }\n    }\n\n    if (retainedBy instanceof RetentionZone$1) {\n      deleteFromZone(retainedBy);\n    } else if (Array.isArray(retainedBy)) {\n      for (const zone of retainedBy) {\n        deleteFromZone(zone);\n      }\n    }\n  };\n}\n\nfunction initializeNodeIfNewToStore(store, treeState, key, trigger) {\n  const storeState = store.getState();\n\n  if (storeState.nodeCleanupFunctions.has(key)) {\n    return;\n  }\n\n  const node = getNode$1(key);\n  const retentionCleanup = initializeRetentionForNode(store, key, node.retainedBy);\n  const nodeCleanup = node.init(store, treeState, trigger);\n  storeState.nodeCleanupFunctions.set(key, () => {\n    nodeCleanup();\n    retentionCleanup();\n  });\n}\n\nfunction initializeNode(store, key, trigger) {\n  initializeNodeIfNewToStore(store, store.getState().currentTree, key, trigger);\n}\n\nfunction cleanUpNode(store, key) {\n  var _state$nodeCleanupFun;\n\n  const state = store.getState();\n  (_state$nodeCleanupFun = state.nodeCleanupFunctions.get(key)) === null || _state$nodeCleanupFun === void 0 ? void 0 : _state$nodeCleanupFun();\n  state.nodeCleanupFunctions.delete(key);\n} // Get the current value loadable of a node and update the state.\n// Update dependencies and subscriptions for selectors.\n// Update saved value validation for atoms.\n\n\nfunction getNodeLoadable(store, state, key) {\n  initializeNodeIfNewToStore(store, state, key, 'get');\n  return getNode$1(key).get(store, state);\n} // Peek at the current value loadable for a node without any evaluation or state change\n\n\nfunction peekNodeLoadable(store, state, key) {\n  return getNode$1(key).peek(store, state);\n} // Write value directly to state bypassing the Node interface as the node\n// definitions may not have been loaded yet when processing the initial snapshot.\n\n\nfunction setUnvalidatedAtomValue_DEPRECATED(state, key, newValue) {\n  var _node$invalidate;\n\n  const node = getNodeMaybe$1(key);\n  node === null || node === void 0 ? void 0 : (_node$invalidate = node.invalidate) === null || _node$invalidate === void 0 ? void 0 : _node$invalidate.call(node, state);\n  return { ...state,\n    atomValues: state.atomValues.clone().delete(key),\n    nonvalidatedAtoms: state.nonvalidatedAtoms.clone().set(key, newValue),\n    dirtyAtoms: setByAddingToSet$1(state.dirtyAtoms, key)\n  };\n} // Return the discovered dependencies and values to be written by setting\n// a node value. (Multiple values may be written due to selectors getting to\n// set upstreams; deps may be discovered because of reads in updater functions.)\n\n\nfunction setNodeValue(store, state, key, newValue) {\n  const node = getNode$1(key);\n\n  if (node.set == null) {\n    throw new ReadOnlyRecoilValueError(`Attempt to set read-only RecoilValue: ${key}`);\n  }\n\n  const set = node.set; // so flow doesn't lose the above refinement.\n\n  initializeNodeIfNewToStore(store, state, key, 'set');\n  return set(store, state, newValue);\n}\n\nfunction peekNodeInfo(store, state, key) {\n  const storeState = store.getState();\n  const graph = store.getGraph(state.version);\n  const type = getNode$1(key).nodeType;\n  return Recoil_lazyProxy({\n    type\n  }, {\n    // $FlowFixMe[underconstrained-implicit-instantiation]\n    loadable: () => peekNodeLoadable(store, state, key),\n    isActive: () => storeState.knownAtoms.has(key) || storeState.knownSelectors.has(key),\n    isSet: () => type === 'selector' ? false : state.atomValues.has(key),\n    isModified: () => state.dirtyAtoms.has(key),\n    // Report current dependencies.  If the node hasn't been evaluated, then\n    // dependencies may be missing based on the current state.\n    deps: () => {\n      var _graph$nodeDeps$get;\n\n      return recoilValuesForKeys$1((_graph$nodeDeps$get = graph.nodeDeps.get(key)) !== null && _graph$nodeDeps$get !== void 0 ? _graph$nodeDeps$get : []);\n    },\n    // Reports all \"current\" subscribers.  Evaluating other nodes or\n    // previous in-progress async evaluations may introduce new subscribers.\n    subscribers: () => {\n      var _storeState$nodeToCom, _storeState$nodeToCom2;\n\n      return {\n        nodes: recoilValuesForKeys$1(Recoil_filterIterable(getDownstreamNodes(store, state, new Set([key])), nodeKey => nodeKey !== key)),\n        components: Recoil_mapIterable((_storeState$nodeToCom = (_storeState$nodeToCom2 = storeState.nodeToComponentSubscriptions.get(key)) === null || _storeState$nodeToCom2 === void 0 ? void 0 : _storeState$nodeToCom2.values()) !== null && _storeState$nodeToCom !== void 0 ? _storeState$nodeToCom : [], ([name]) => ({\n          name\n        }))\n      };\n    }\n  });\n} // Find all of the recursively dependent nodes\n\n\nfunction getDownstreamNodes(store, state, keys) {\n  const visitedNodes = new Set();\n  const visitingNodes = Array.from(keys);\n  const graph = store.getGraph(state.version);\n\n  for (let key = visitingNodes.pop(); key; key = visitingNodes.pop()) {\n    var _graph$nodeToNodeSubs;\n\n    visitedNodes.add(key);\n    const subscribedNodes = (_graph$nodeToNodeSubs = graph.nodeToNodeSubscriptions.get(key)) !== null && _graph$nodeToNodeSubs !== void 0 ? _graph$nodeToNodeSubs : emptySet;\n\n    for (const downstreamNode of subscribedNodes) {\n      if (!visitedNodes.has(downstreamNode)) {\n        visitingNodes.push(downstreamNode);\n      }\n    }\n  }\n\n  return visitedNodes;\n}\n\nvar Recoil_FunctionalCore = {\n  getNodeLoadable,\n  peekNodeLoadable,\n  setNodeValue,\n  initializeNode,\n  cleanUpNode,\n  setUnvalidatedAtomValue_DEPRECATED,\n  peekNodeInfo,\n  getDownstreamNodes\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nlet _invalidateMemoizedSnapshot = null;\n\nfunction setInvalidateMemoizedSnapshot(invalidate) {\n  _invalidateMemoizedSnapshot = invalidate;\n}\n\nfunction invalidateMemoizedSnapshot() {\n  var _invalidateMemoizedSn;\n\n  (_invalidateMemoizedSn = _invalidateMemoizedSnapshot) === null || _invalidateMemoizedSn === void 0 ? void 0 : _invalidateMemoizedSn();\n}\n\nvar Recoil_SnapshotCache = {\n  setInvalidateMemoizedSnapshot,\n  invalidateMemoizedSnapshot\n};\n\nconst {\n  getDownstreamNodes: getDownstreamNodes$1,\n  getNodeLoadable: getNodeLoadable$1,\n  setNodeValue: setNodeValue$1\n} = Recoil_FunctionalCore;\n\nconst {\n  getNextComponentID: getNextComponentID$1\n} = Recoil_Keys;\n\nconst {\n  getNode: getNode$2,\n  getNodeMaybe: getNodeMaybe$2\n} = Recoil_Node;\n\nconst {\n  DefaultValue: DefaultValue$1\n} = Recoil_Node;\n\nconst {\n  reactMode: reactMode$1\n} = Recoil_ReactMode;\n\nconst {\n  AbstractRecoilValue: AbstractRecoilValue$1,\n  RecoilState: RecoilState$1,\n  RecoilValueReadOnly: RecoilValueReadOnly$1,\n  isRecoilValue: isRecoilValue$1\n} = Recoil_RecoilValue$1;\n\nconst {\n  invalidateMemoizedSnapshot: invalidateMemoizedSnapshot$1\n} = Recoil_SnapshotCache;\n\n\n\n\n\n\n\nfunction getRecoilValueAsLoadable(store, {\n  key\n}, treeState = store.getState().currentTree) {\n  var _storeState$nextTree, _storeState$previousT;\n\n  // Reading from an older tree can cause bugs because the dependencies that we\n  // discover during the read are lost.\n  const storeState = store.getState();\n\n  if (!(treeState.version === storeState.currentTree.version || treeState.version === ((_storeState$nextTree = storeState.nextTree) === null || _storeState$nextTree === void 0 ? void 0 : _storeState$nextTree.version) || treeState.version === ((_storeState$previousT = storeState.previousTree) === null || _storeState$previousT === void 0 ? void 0 : _storeState$previousT.version))) {\n    Recoil_recoverableViolation('Tried to read from a discarded tree');\n  }\n\n  const loadable = getNodeLoadable$1(store, treeState, key);\n\n  if (loadable.state === 'loading') {\n    loadable.contents.catch(() => {\n      /**\n       * HACK: intercept thrown error here to prevent an uncaught promise exception. Ideally this would happen closer to selector\n       * execution (perhaps introducing a new ERROR class to be resolved by async selectors that are in an error state)\n       */\n      return;\n    });\n  }\n\n  return loadable;\n}\n\nfunction applyAtomValueWrites(atomValues, writes) {\n  const result = atomValues.clone();\n  writes.forEach((v, k) => {\n    if (v.state === 'hasValue' && v.contents instanceof DefaultValue$1) {\n      result.delete(k);\n    } else {\n      result.set(k, v);\n    }\n  });\n  return result;\n}\n\nfunction valueFromValueOrUpdater(store, state, {\n  key\n}, valueOrUpdater) {\n  if (typeof valueOrUpdater === 'function') {\n    // Updater form: pass in the current value. Throw if the current value\n    // is unavailable (namely when updating an async selector that's\n    // pending or errored):\n    const current = getNodeLoadable$1(store, state, key);\n\n    if (current.state === 'loading') {\n      const msg = `Tried to set atom or selector \"${key}\" using an updater function while the current state is pending, this is not currently supported.`;\n      Recoil_recoverableViolation(msg);\n      throw Recoil_err(msg);\n    } else if (current.state === 'hasError') {\n      throw current.contents;\n    } // T itself may be a function, so our refinement is not sufficient:\n\n\n    return valueOrUpdater(current.contents); // flowlint-line unclear-type:off\n  } else {\n    return valueOrUpdater;\n  }\n}\n\nfunction applyAction(store, state, action) {\n  if (action.type === 'set') {\n    const {\n      recoilValue,\n      valueOrUpdater\n    } = action;\n    const newValue = valueFromValueOrUpdater(store, state, recoilValue, valueOrUpdater);\n    const writes = setNodeValue$1(store, state, recoilValue.key, newValue);\n\n    for (const [key, loadable] of writes.entries()) {\n      writeLoadableToTreeState(state, key, loadable);\n    }\n  } else if (action.type === 'setLoadable') {\n    const {\n      recoilValue: {\n        key\n      },\n      loadable\n    } = action;\n    writeLoadableToTreeState(state, key, loadable);\n  } else if (action.type === 'markModified') {\n    const {\n      recoilValue: {\n        key\n      }\n    } = action;\n    state.dirtyAtoms.add(key);\n  } else if (action.type === 'setUnvalidated') {\n    var _node$invalidate;\n\n    // Write value directly to state bypassing the Node interface as the node\n    // definitions may not have been loaded yet when processing the initial snapshot.\n    const {\n      recoilValue: {\n        key\n      },\n      unvalidatedValue\n    } = action;\n    const node = getNodeMaybe$2(key);\n    node === null || node === void 0 ? void 0 : (_node$invalidate = node.invalidate) === null || _node$invalidate === void 0 ? void 0 : _node$invalidate.call(node, state);\n    state.atomValues.delete(key);\n    state.nonvalidatedAtoms.set(key, unvalidatedValue);\n    state.dirtyAtoms.add(key);\n  } else {\n    Recoil_recoverableViolation(`Unknown action ${action.type}`);\n  }\n}\n\nfunction writeLoadableToTreeState(state, key, loadable) {\n  if (loadable.state === 'hasValue' && loadable.contents instanceof DefaultValue$1) {\n    state.atomValues.delete(key);\n  } else {\n    state.atomValues.set(key, loadable);\n  }\n\n  state.dirtyAtoms.add(key);\n  state.nonvalidatedAtoms.delete(key);\n}\n\nfunction applyActionsToStore(store, actions) {\n  store.replaceState(state => {\n    const newState = copyTreeState(state);\n\n    for (const action of actions) {\n      applyAction(store, newState, action);\n    }\n\n    invalidateDownstreams(store, newState);\n    invalidateMemoizedSnapshot$1();\n    return newState;\n  });\n}\n\nfunction queueOrPerformStateUpdate(store, action) {\n  if (batchStack.length) {\n    const actionsByStore = batchStack[batchStack.length - 1];\n    let actions = actionsByStore.get(store);\n\n    if (!actions) {\n      actionsByStore.set(store, actions = []);\n    }\n\n    actions.push(action);\n  } else {\n    applyActionsToStore(store, [action]);\n  }\n}\n\nconst batchStack = [];\n\nfunction batchStart() {\n  const actionsByStore = new Map();\n  batchStack.push(actionsByStore);\n  return () => {\n    for (const [store, actions] of actionsByStore) {\n      applyActionsToStore(store, actions);\n    }\n\n    const popped = batchStack.pop();\n\n    if (popped !== actionsByStore) {\n      Recoil_recoverableViolation('Incorrect order of batch popping');\n    }\n  };\n}\n\nfunction copyTreeState(state) {\n  return { ...state,\n    atomValues: state.atomValues.clone(),\n    nonvalidatedAtoms: state.nonvalidatedAtoms.clone(),\n    dirtyAtoms: new Set(state.dirtyAtoms)\n  };\n}\n\nfunction invalidateDownstreams(store, state) {\n  // Inform any nodes that were changed or downstream of changes so that they\n  // can clear out any caches as needed due to the update:\n  const downstreams = getDownstreamNodes$1(store, state, state.dirtyAtoms);\n\n  for (const key of downstreams) {\n    var _getNodeMaybe, _getNodeMaybe$invalid;\n\n    (_getNodeMaybe = getNodeMaybe$2(key)) === null || _getNodeMaybe === void 0 ? void 0 : (_getNodeMaybe$invalid = _getNodeMaybe.invalidate) === null || _getNodeMaybe$invalid === void 0 ? void 0 : _getNodeMaybe$invalid.call(_getNodeMaybe, state);\n  }\n}\n\nfunction setRecoilValue(store, recoilValue, valueOrUpdater) {\n  queueOrPerformStateUpdate(store, {\n    type: 'set',\n    recoilValue,\n    valueOrUpdater\n  });\n}\n\nfunction setRecoilValueLoadable(store, recoilValue, loadable) {\n  if (loadable instanceof DefaultValue$1) {\n    return setRecoilValue(store, recoilValue, loadable);\n  }\n\n  queueOrPerformStateUpdate(store, {\n    type: 'setLoadable',\n    recoilValue,\n    loadable: loadable\n  });\n}\n\nfunction markRecoilValueModified(store, recoilValue) {\n  queueOrPerformStateUpdate(store, {\n    type: 'markModified',\n    recoilValue\n  });\n}\n\nfunction setUnvalidatedRecoilValue(store, recoilValue, unvalidatedValue) {\n  queueOrPerformStateUpdate(store, {\n    type: 'setUnvalidated',\n    recoilValue,\n    unvalidatedValue\n  });\n}\n\nfunction subscribeToRecoilValue(store, {\n  key\n}, callback, componentDebugName = null) {\n  const subID = getNextComponentID$1();\n  const storeState = store.getState();\n\n  if (!storeState.nodeToComponentSubscriptions.has(key)) {\n    storeState.nodeToComponentSubscriptions.set(key, new Map());\n  }\n\n  Recoil_nullthrows(storeState.nodeToComponentSubscriptions.get(key)).set(subID, [componentDebugName !== null && componentDebugName !== void 0 ? componentDebugName : '<not captured>', callback]); // Handle the case that, during the same tick that we are subscribing, an atom\n  // has been updated by some effect handler. Otherwise we will miss the update.\n\n  const mode = reactMode$1();\n\n  if (mode.early && (mode.mode === 'LEGACY' || mode.mode === 'MUTABLE_SOURCE')) {\n    const nextTree = store.getState().nextTree;\n\n    if (nextTree && nextTree.dirtyAtoms.has(key)) {\n      callback(nextTree);\n    }\n  }\n\n  return {\n    release: () => {\n      const releaseStoreState = store.getState();\n      const subs = releaseStoreState.nodeToComponentSubscriptions.get(key);\n\n      if (subs === undefined || !subs.has(subID)) {\n        Recoil_recoverableViolation(`Subscription missing at release time for atom ${key}. This is a bug in Recoil.`);\n        return;\n      }\n\n      subs.delete(subID);\n\n      if (subs.size === 0) {\n        releaseStoreState.nodeToComponentSubscriptions.delete(key);\n      }\n    }\n  };\n}\n\nfunction refreshRecoilValue(store, recoilValue) {\n  var _node$clearCache;\n\n  const {\n    currentTree\n  } = store.getState();\n  const node = getNode$2(recoilValue.key);\n  (_node$clearCache = node.clearCache) === null || _node$clearCache === void 0 ? void 0 : _node$clearCache.call(node, store, currentTree);\n}\n\nvar Recoil_RecoilValueInterface = {\n  RecoilValueReadOnly: RecoilValueReadOnly$1,\n  AbstractRecoilValue: AbstractRecoilValue$1,\n  RecoilState: RecoilState$1,\n  getRecoilValueAsLoadable,\n  setRecoilValue,\n  setRecoilValueLoadable,\n  markRecoilValueModified,\n  setUnvalidatedRecoilValue,\n  subscribeToRecoilValue,\n  isRecoilValue: isRecoilValue$1,\n  applyAtomValueWrites,\n  // TODO Remove export when deprecating initialStoreState_DEPRECATED in RecoilRoot\n  batchStart,\n  writeLoadableToTreeState,\n  invalidateDownstreams,\n  copyTreeState,\n  refreshRecoilValue\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * The someSet() method tests whether some elements in the given Set pass the\n * test implemented by the provided function.\n */\n\nfunction someSet(set, callback, context) {\n  const iterator = set.entries();\n  let current = iterator.next();\n\n  while (!current.done) {\n    const entry = current.value;\n\n    if (callback.call(context, entry[1], entry[0], set)) {\n      return true;\n    }\n\n    current = iterator.next();\n  }\n\n  return false;\n}\n\nvar Recoil_someSet = someSet;\n\nconst {\n  cleanUpNode: cleanUpNode$1\n} = Recoil_FunctionalCore;\n\nconst {\n  deleteNodeConfigIfPossible: deleteNodeConfigIfPossible$1,\n  getNode: getNode$3\n} = Recoil_Node;\n\nconst {\n  RetentionZone: RetentionZone$2\n} = Recoil_RetentionZone;\n\n\n\n\n\n\n\n // Components that aren't mounted after suspending for this long will be assumed\n// to be discarded and their resources released.\n\n\nconst SUSPENSE_TIMEOUT_MS = 120000;\nconst emptySet$1 = new Set();\n\nfunction releaseRetainablesNowOnCurrentTree(store, retainables) {\n  const storeState = store.getState();\n  const treeState = storeState.currentTree;\n\n  if (storeState.nextTree) {\n    Recoil_recoverableViolation('releaseNodesNowOnCurrentTree should only be called at the end of a batch');\n    return; // leak memory rather than erase something that's about to be used.\n  }\n\n  const nodes = new Set();\n\n  for (const r of retainables) {\n    if (r instanceof RetentionZone$2) {\n      for (const n of nodesRetainedByZone(storeState, r)) {\n        nodes.add(n);\n      }\n    } else {\n      nodes.add(r);\n    }\n  }\n\n  const releasableNodes = findReleasableNodes(store, nodes);\n\n  for (const node of releasableNodes) {\n    releaseNode(store, treeState, node);\n  }\n}\n\nfunction findReleasableNodes(store, searchFromNodes) {\n  const storeState = store.getState();\n  const treeState = storeState.currentTree;\n  const graph = store.getGraph(treeState.version);\n  const releasableNodes = new Set(); // mutated to collect answer\n\n  const nonReleasableNodes = new Set();\n  findReleasableNodesInner(searchFromNodes);\n  return releasableNodes;\n\n  function findReleasableNodesInner(searchFromNodes) {\n    const releasableNodesFoundThisIteration = new Set();\n    const downstreams = getDownstreamNodesInTopologicalOrder(store, treeState, searchFromNodes, releasableNodes, // don't descend into these\n    nonReleasableNodes // don't descend into these\n    ); // Find which of the downstream nodes are releasable and which are not:\n\n    for (const node of downstreams) {\n      var _storeState$retention;\n\n      // Not releasable if configured to be retained forever:\n      if (getNode$3(node).retainedBy === 'recoilRoot') {\n        nonReleasableNodes.add(node);\n        continue;\n      } // Not releasable if retained directly by a component:\n\n\n      if (((_storeState$retention = storeState.retention.referenceCounts.get(node)) !== null && _storeState$retention !== void 0 ? _storeState$retention : 0) > 0) {\n        nonReleasableNodes.add(node);\n        continue;\n      } // Not releasable if retained by a zone:\n\n\n      if (zonesThatCouldRetainNode(node).some(z => storeState.retention.referenceCounts.get(z))) {\n        nonReleasableNodes.add(node);\n        continue;\n      } // Not releasable if it has a non-releasable child (which will already be in\n      // nonReleasableNodes because we are going in topological order):\n\n\n      const nodeChildren = graph.nodeToNodeSubscriptions.get(node);\n\n      if (nodeChildren && Recoil_someSet(nodeChildren, child => nonReleasableNodes.has(child))) {\n        nonReleasableNodes.add(node);\n        continue;\n      }\n\n      releasableNodes.add(node);\n      releasableNodesFoundThisIteration.add(node);\n    } // If we found any releasable nodes, we need to walk UP from those nodes to\n    // find whether their parents can now be released as well:\n\n\n    const parents = new Set();\n\n    for (const node of releasableNodesFoundThisIteration) {\n      for (const parent of (_graph$nodeDeps$get = graph.nodeDeps.get(node)) !== null && _graph$nodeDeps$get !== void 0 ? _graph$nodeDeps$get : emptySet$1) {\n        var _graph$nodeDeps$get;\n\n        if (!releasableNodes.has(parent)) {\n          parents.add(parent);\n        }\n      }\n    }\n\n    if (parents.size) {\n      findReleasableNodesInner(parents);\n    }\n  }\n} // Children before parents\n\n\nfunction getDownstreamNodesInTopologicalOrder(store, treeState, nodes, // Mutable set is destroyed in place\ndoNotDescendInto1, doNotDescendInto2) {\n  const graph = store.getGraph(treeState.version);\n  const answer = [];\n  const visited = new Set();\n\n  while (nodes.size > 0) {\n    visit(Recoil_nullthrows(nodes.values().next().value));\n  }\n\n  return answer;\n\n  function visit(node) {\n    if (doNotDescendInto1.has(node) || doNotDescendInto2.has(node)) {\n      nodes.delete(node);\n      return;\n    }\n\n    if (visited.has(node)) {\n      return;\n    }\n\n    const children = graph.nodeToNodeSubscriptions.get(node);\n\n    if (children) {\n      for (const child of children) {\n        visit(child);\n      }\n    }\n\n    visited.add(node);\n    nodes.delete(node);\n    answer.push(node);\n  }\n}\n\nfunction releaseNode(store, treeState, node) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  } // Atom effects, in-closure caches, etc.:\n\n\n  cleanUpNode$1(store, node); // Delete from store state:\n\n  const storeState = store.getState();\n  storeState.knownAtoms.delete(node);\n  storeState.knownSelectors.delete(node);\n  storeState.nodeTransactionSubscriptions.delete(node);\n  storeState.retention.referenceCounts.delete(node);\n  const zones = zonesThatCouldRetainNode(node);\n\n  for (const zone of zones) {\n    var _storeState$retention2;\n\n    (_storeState$retention2 = storeState.retention.nodesRetainedByZone.get(zone)) === null || _storeState$retention2 === void 0 ? void 0 : _storeState$retention2.delete(node);\n  } // Note that we DO NOT delete from nodeToComponentSubscriptions because this\n  // already happens when the last component that was retaining the node unmounts,\n  // and this could happen either before or after that.\n  // Delete from TreeState and dep graph:\n\n\n  treeState.atomValues.delete(node);\n  treeState.dirtyAtoms.delete(node);\n  treeState.nonvalidatedAtoms.delete(node);\n  const graph = storeState.graphsByVersion.get(treeState.version);\n\n  if (graph) {\n    const deps = graph.nodeDeps.get(node);\n\n    if (deps !== undefined) {\n      graph.nodeDeps.delete(node);\n\n      for (const dep of deps) {\n        var _graph$nodeToNodeSubs;\n\n        (_graph$nodeToNodeSubs = graph.nodeToNodeSubscriptions.get(dep)) === null || _graph$nodeToNodeSubs === void 0 ? void 0 : _graph$nodeToNodeSubs.delete(node);\n      }\n    } // No need to delete sub's deps as there should be no subs at this point.\n    // But an invariant would require deleting nodes in topological order.\n\n\n    graph.nodeToNodeSubscriptions.delete(node);\n  } // Node config (for family members only as their configs can be recreated, and\n  // only if they are not retained within any other Stores):\n\n\n  deleteNodeConfigIfPossible$1(node);\n}\n\nfunction nodesRetainedByZone(storeState, zone) {\n  var _storeState$retention3;\n\n  return (_storeState$retention3 = storeState.retention.nodesRetainedByZone.get(zone)) !== null && _storeState$retention3 !== void 0 ? _storeState$retention3 : emptySet$1;\n}\n\nfunction zonesThatCouldRetainNode(node) {\n  const retainedBy = getNode$3(node).retainedBy;\n\n  if (retainedBy === undefined || retainedBy === 'components' || retainedBy === 'recoilRoot') {\n    return [];\n  } else if (retainedBy instanceof RetentionZone$2) {\n    return [retainedBy];\n  } else {\n    return retainedBy; // it's an array of zones\n  }\n}\n\nfunction scheduleOrPerformPossibleReleaseOfRetainable(store, retainable) {\n  const state = store.getState();\n\n  if (state.nextTree) {\n    state.retention.retainablesToCheckForRelease.add(retainable);\n  } else {\n    releaseRetainablesNowOnCurrentTree(store, new Set([retainable]));\n  }\n}\n\nfunction updateRetainCount(store, retainable, delta) {\n  var _map$get;\n\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  }\n\n  const map = store.getState().retention.referenceCounts;\n  const newCount = ((_map$get = map.get(retainable)) !== null && _map$get !== void 0 ? _map$get : 0) + delta;\n\n  if (newCount === 0) {\n    updateRetainCountToZero(store, retainable);\n  } else {\n    map.set(retainable, newCount);\n  }\n}\n\nfunction updateRetainCountToZero(store, retainable) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  }\n\n  const map = store.getState().retention.referenceCounts;\n  map.delete(retainable);\n  scheduleOrPerformPossibleReleaseOfRetainable(store, retainable);\n}\n\nfunction releaseScheduledRetainablesNow(store) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  }\n\n  const state = store.getState();\n  releaseRetainablesNowOnCurrentTree(store, state.retention.retainablesToCheckForRelease);\n  state.retention.retainablesToCheckForRelease.clear();\n}\n\nfunction retainedByOptionWithDefault(r) {\n  // The default will change from 'recoilRoot' to 'components' in the future.\n  return r === undefined ? 'recoilRoot' : r;\n}\n\nvar Recoil_Retention = {\n  SUSPENSE_TIMEOUT_MS,\n  updateRetainCount,\n  updateRetainCountToZero,\n  releaseScheduledRetainablesNow,\n  retainedByOptionWithDefault\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * This is to export esstiential functions from react-dom\n * for our web build\n *\n * \n * @format\n * @oncall recoil\n */\nconst {\n  unstable_batchedUpdates\n} = reactDom;\n\nvar ReactBatchedUpdates = {\n  unstable_batchedUpdates\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * This is to export esstiential functions from react-dom\n * for our web build\n *\n * \n * @format\n * @oncall recoil\n */\n// @fb-only: const {unstable_batchedUpdates} = require('ReactDOMComet');\n// prettier-ignore\nconst {\n  unstable_batchedUpdates: unstable_batchedUpdates$1\n} = ReactBatchedUpdates; // @oss-only\n\n\nvar Recoil_ReactBatchedUpdates = {\n  unstable_batchedUpdates: unstable_batchedUpdates$1\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\nconst {\n  batchStart: batchStart$1\n} = Recoil_RecoilValueInterface;\n\nconst {\n  unstable_batchedUpdates: unstable_batchedUpdates$2\n} = Recoil_ReactBatchedUpdates; // flowlint-next-line unclear-type:off\n\n\n/*\n * During SSR, unstable_batchedUpdates may be undefined so this\n * falls back to a basic function that executes the batch\n */\nlet batcher = unstable_batchedUpdates$2 || (batchFn => batchFn());\n/**\n * Sets the provided batcher function as the batcher function used by Recoil.\n *\n * Set the batcher to a custom batcher for your renderer,\n * if you use a renderer other than React DOM or React Native.\n */\n\n\nconst setBatcher = newBatcher => {\n  batcher = newBatcher;\n};\n/**\n * Returns the current batcher function.\n */\n\n\nconst getBatcher = () => batcher;\n/**\n * Calls the current batcher function and passes the\n * provided callback function.\n */\n\n\nconst batchUpdates = callback => {\n  batcher(() => {\n    let batchEnd = () => undefined;\n\n    try {\n      batchEnd = batchStart$1();\n      callback();\n    } finally {\n      batchEnd();\n    }\n  });\n};\n\nvar Recoil_Batching = {\n  getBatcher,\n  setBatcher,\n  batchUpdates\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Combines multiple Iterables into a single Iterable.\n * Traverses the input Iterables in the order provided and maintains the order\n * of their elements.\n *\n * Example:\n * ```\n * const r = Array.from(concatIterables(['a', 'b'], ['c'], ['d', 'e', 'f']));\n * r == ['a', 'b', 'c', 'd', 'e', 'f'];\n * ```\n */\n\nfunction* concatIterables(iters) {\n  for (const iter of iters) {\n    for (const val of iter) {\n      yield val;\n    }\n  }\n}\n\nvar Recoil_concatIterables = concatIterables;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/* eslint-disable fb-www/typeof-undefined */\n\nconst isSSR = // $FlowFixMe(site=recoil) Window does not have a FlowType definition https://github.com/facebook/flow/issues/6709\ntypeof Window === 'undefined' || typeof window === 'undefined';\n/* eslint-enable fb-www/typeof-undefined */\n\nconst isWindow = value => !isSSR && ( // $FlowFixMe(site=recoil) Window does not have a FlowType definition https://github.com/facebook/flow/issues/6709\nvalue === window || value instanceof Window);\n\nconst isReactNative = typeof navigator !== 'undefined' && navigator.product === 'ReactNative'; // eslint-disable-line fb-www/typeof-undefined\n\nvar Recoil_Environment = {\n  isSSR,\n  isReactNative,\n  isWindow\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Caches a function's results based on the key returned by the passed\n * hashFunction.\n */\n\nfunction memoizeWithArgsHash(fn, hashFunction) {\n  let cache;\n  return (...args) => {\n    if (!cache) {\n      cache = {};\n    }\n\n    const key = hashFunction(...args);\n\n    if (!Object.hasOwnProperty.call(cache, key)) {\n      cache[key] = fn(...args);\n    }\n\n    return cache[key];\n  };\n}\n/**\n * Caches a function's results based on a comparison of the arguments.\n * Only caches the last return of the function.\n * Defaults to reference equality\n */\n\n\nfunction memoizeOneWithArgsHash(fn, hashFunction) {\n  let lastKey;\n  let lastResult; // breaking cache when arguments change\n\n  return (...args) => {\n    const key = hashFunction(...args);\n\n    if (lastKey === key) {\n      return lastResult;\n    }\n\n    lastKey = key;\n    lastResult = fn(...args);\n    return lastResult;\n  };\n}\n/**\n * Caches a function's results based on a comparison of the arguments.\n * Only caches the last return of the function.\n * Defaults to reference equality\n */\n\n\nfunction memoizeOneWithArgsHashAndInvalidation(fn, hashFunction) {\n  let lastKey;\n  let lastResult; // breaking cache when arguments change\n\n  const memoizedFn = (...args) => {\n    const key = hashFunction(...args);\n\n    if (lastKey === key) {\n      return lastResult;\n    }\n\n    lastKey = key;\n    lastResult = fn(...args);\n    return lastResult;\n  };\n\n  const invalidate = () => {\n    lastKey = null;\n  };\n\n  return [memoizedFn, invalidate];\n}\n\nvar Recoil_Memoize = {\n  memoizeWithArgsHash,\n  memoizeOneWithArgsHash,\n  memoizeOneWithArgsHashAndInvalidation\n};\n\nconst {\n  batchUpdates: batchUpdates$1\n} = Recoil_Batching;\n\nconst {\n  initializeNode: initializeNode$1,\n  peekNodeInfo: peekNodeInfo$1\n} = Recoil_FunctionalCore;\n\nconst {\n  graph: graph$1\n} = Recoil_Graph;\n\nconst {\n  getNextStoreID: getNextStoreID$1\n} = Recoil_Keys;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$1,\n  recoilValues: recoilValues$1,\n  recoilValuesForKeys: recoilValuesForKeys$2\n} = Recoil_Node;\n\nconst {\n  AbstractRecoilValue: AbstractRecoilValue$2,\n  getRecoilValueAsLoadable: getRecoilValueAsLoadable$1,\n  setRecoilValue: setRecoilValue$1,\n  setUnvalidatedRecoilValue: setUnvalidatedRecoilValue$1\n} = Recoil_RecoilValueInterface;\n\nconst {\n  updateRetainCount: updateRetainCount$1\n} = Recoil_Retention;\n\nconst {\n  setInvalidateMemoizedSnapshot: setInvalidateMemoizedSnapshot$1\n} = Recoil_SnapshotCache;\n\nconst {\n  getNextTreeStateVersion: getNextTreeStateVersion$2,\n  makeEmptyStoreState: makeEmptyStoreState$1\n} = Recoil_State;\n\n\n\nconst {\n  isSSR: isSSR$1\n} = Recoil_Environment;\n\n\n\n\n\n\n\n\n\nconst {\n  memoizeOneWithArgsHashAndInvalidation: memoizeOneWithArgsHashAndInvalidation$1\n} = Recoil_Memoize;\n\n\n\n // Opaque at this surface because it's part of the public API from here.\n\n\nconst retainWarning = `\nRecoil Snapshots only last for the duration of the callback they are provided to. To keep a Snapshot longer, do this:\n\n  const release = snapshot.retain();\n  try {\n    await doSomethingWithSnapshot(snapshot);\n  } finally {\n    release();\n  }\n\nThis is currently a DEV-only warning but will become a thrown exception in the next release of Recoil.\n`; // A \"Snapshot\" is \"read-only\" and captures a specific set of values of atoms.\n// However, the data-flow-graph and selector values may evolve as selector\n// evaluation functions are executed and async selectors resolve.\n\nclass Snapshot {\n  // eslint-disable-next-line fb-www/no-uninitialized-properties\n  constructor(storeState, parentStoreID) {\n    _defineProperty(this, \"_store\", void 0);\n\n    _defineProperty(this, \"_refCount\", 1);\n\n    _defineProperty(this, \"getLoadable\", recoilValue => {\n      this.checkRefCount_INTERNAL();\n      return getRecoilValueAsLoadable$1(this._store, recoilValue);\n    });\n\n    _defineProperty(this, \"getPromise\", recoilValue => {\n      this.checkRefCount_INTERNAL();\n      return this.getLoadable(recoilValue).toPromise();\n    });\n\n    _defineProperty(this, \"getNodes_UNSTABLE\", opt => {\n      this.checkRefCount_INTERNAL(); // TODO Deal with modified selectors\n\n      if ((opt === null || opt === void 0 ? void 0 : opt.isModified) === true) {\n        if ((opt === null || opt === void 0 ? void 0 : opt.isInitialized) === false) {\n          return [];\n        }\n\n        const state = this._store.getState().currentTree;\n\n        return recoilValuesForKeys$2(state.dirtyAtoms);\n      }\n\n      const knownAtoms = this._store.getState().knownAtoms;\n\n      const knownSelectors = this._store.getState().knownSelectors;\n\n      return (opt === null || opt === void 0 ? void 0 : opt.isInitialized) == null ? recoilValues$1.values() : opt.isInitialized === true ? recoilValuesForKeys$2(Recoil_concatIterables([knownAtoms, knownSelectors])) : Recoil_filterIterable(recoilValues$1.values(), ({\n        key\n      }) => !knownAtoms.has(key) && !knownSelectors.has(key));\n    });\n\n    _defineProperty(this, \"getInfo_UNSTABLE\", ({\n      key\n    }) => {\n      this.checkRefCount_INTERNAL();\n      return peekNodeInfo$1(this._store, this._store.getState().currentTree, key);\n    });\n\n    _defineProperty(this, \"map\", mapper => {\n      this.checkRefCount_INTERNAL();\n      const mutableSnapshot = new MutableSnapshot(this, batchUpdates$1);\n      mapper(mutableSnapshot); // if removing batchUpdates from `set` add it here\n\n      return mutableSnapshot;\n    });\n\n    _defineProperty(this, \"asyncMap\", async mapper => {\n      this.checkRefCount_INTERNAL();\n      const mutableSnapshot = new MutableSnapshot(this, batchUpdates$1);\n      mutableSnapshot.retain(); // Retain new snapshot during async mapper\n\n      await mapper(mutableSnapshot); // Continue to retain the new snapshot for the user, but auto-release it\n      // after the next tick, the same as a new synchronous snapshot.\n\n      mutableSnapshot.autoRelease_INTERNAL();\n      return mutableSnapshot;\n    });\n\n    this._store = {\n      storeID: getNextStoreID$1(),\n      parentStoreID,\n      getState: () => storeState,\n      replaceState: replacer => {\n        // no batching, so nextTree is never active\n        storeState.currentTree = replacer(storeState.currentTree);\n      },\n      getGraph: version => {\n        const graphs = storeState.graphsByVersion;\n\n        if (graphs.has(version)) {\n          return Recoil_nullthrows(graphs.get(version));\n        }\n\n        const newGraph = graph$1();\n        graphs.set(version, newGraph);\n        return newGraph;\n      },\n      subscribeToTransactions: () => ({\n        release: () => {}\n      }),\n      addTransactionMetadata: () => {\n        throw Recoil_err('Cannot subscribe to Snapshots');\n      }\n    }; // Initialize any nodes that are live in the parent store (primarily so that\n    // this snapshot gets counted towards the node's live stores count).\n    // TODO Optimize this when cloning snapshots for callbacks\n\n    for (const nodeKey of this._store.getState().knownAtoms) {\n      initializeNode$1(this._store, nodeKey, 'get');\n      updateRetainCount$1(this._store, nodeKey, 1);\n    }\n\n    this.autoRelease_INTERNAL();\n  }\n\n  retain() {\n    if (this._refCount <= 0) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw Recoil_err('Snapshot has already been released.');\n      } else {\n        Recoil_recoverableViolation('Attempt to retain() Snapshot that was already released.');\n      }\n    }\n\n    this._refCount++;\n    let released = false;\n    return () => {\n      if (!released) {\n        released = true;\n\n        this._release();\n      }\n    };\n  }\n  /**\n   * Release the snapshot on the next tick.  This means the snapshot is retained\n   * during the execution of the current function using it.\n   */\n\n\n  autoRelease_INTERNAL() {\n    if (!isSSR$1) {\n      // Use timeout of 10 to workaround Firefox issue: https://github.com/facebookexperimental/Recoil/issues/1936\n      window.setTimeout(() => this._release(), 10);\n    }\n  }\n\n  _release() {\n    this._refCount--;\n\n    if (this._refCount === 0) {\n      this._store.getState().nodeCleanupFunctions.forEach(cleanup => cleanup());\n\n      this._store.getState().nodeCleanupFunctions.clear();\n\n      if (!Recoil_gkx('recoil_memory_managament_2020')) {\n        return;\n      } // Temporarily nerfing this to allow us to find broken call sites without\n      // actually breaking anybody yet.\n      // for (const k of this._store.getState().knownAtoms) {\n      //   updateRetainCountToZero(this._store, k);\n      // }\n\n    } else if (this._refCount < 0) {\n      if (process.env.NODE_ENV !== \"production\") {\n        Recoil_recoverableViolation('Snapshot released an extra time.');\n      }\n    }\n  }\n\n  isRetained() {\n    return this._refCount > 0;\n  }\n\n  checkRefCount_INTERNAL() {\n    if (Recoil_gkx('recoil_memory_managament_2020') && this._refCount <= 0) {\n      if (process.env.NODE_ENV !== \"production\") {\n        Recoil_recoverableViolation(retainWarning);\n      } // What we will ship later:\n      // throw err(retainWarning);\n\n    }\n  }\n\n  getStore_INTERNAL() {\n    this.checkRefCount_INTERNAL();\n    return this._store;\n  }\n\n  getID() {\n    this.checkRefCount_INTERNAL();\n    return this._store.getState().currentTree.stateID;\n  }\n\n  getStoreID() {\n    this.checkRefCount_INTERNAL();\n    return this._store.storeID;\n  } // We want to allow the methods to be destructured and used as accessors\n\n  /* eslint-disable fb-www/extra-arrow-initializer */\n\n  /* eslint-enable fb-www/extra-arrow-initializer */\n\n\n}\n\nfunction cloneStoreState(store, treeState, bumpVersion = false) {\n  const storeState = store.getState();\n  const version = bumpVersion ? getNextTreeStateVersion$2() : treeState.version;\n  return {\n    // Always clone the TreeState to isolate stores from accidental mutations.\n    // For example, reading a selector from a cloned snapshot shouldn't cache\n    // in the original treestate which may cause the original to skip\n    // initialization of upstream atoms.\n    currentTree: {\n      // TODO snapshots shouldn't really have versions because a new version number\n      // is always assigned when the snapshot is gone to.\n      version: bumpVersion ? version : treeState.version,\n      stateID: bumpVersion ? version : treeState.stateID,\n      transactionMetadata: { ...treeState.transactionMetadata\n      },\n      dirtyAtoms: new Set(treeState.dirtyAtoms),\n      atomValues: treeState.atomValues.clone(),\n      nonvalidatedAtoms: treeState.nonvalidatedAtoms.clone()\n    },\n    commitDepth: 0,\n    nextTree: null,\n    previousTree: null,\n    knownAtoms: new Set(storeState.knownAtoms),\n    // FIXME here's a copy\n    knownSelectors: new Set(storeState.knownSelectors),\n    // FIXME here's a copy\n    transactionSubscriptions: new Map(),\n    nodeTransactionSubscriptions: new Map(),\n    nodeToComponentSubscriptions: new Map(),\n    queuedComponentCallbacks_DEPRECATED: [],\n    suspendedComponentResolvers: new Set(),\n    graphsByVersion: new Map().set(version, store.getGraph(treeState.version)),\n    retention: {\n      referenceCounts: new Map(),\n      nodesRetainedByZone: new Map(),\n      retainablesToCheckForRelease: new Set()\n    },\n    // FIXME here's a copy\n    // Create blank cleanup handlers for atoms so snapshots don't re-run\n    // atom effects.\n    nodeCleanupFunctions: new Map(Recoil_mapIterable(storeState.nodeCleanupFunctions.entries(), ([key]) => [key, () => {}]))\n  };\n} // Factory to build a fresh snapshot\n\n\nfunction freshSnapshot(initializeState) {\n  const snapshot = new Snapshot(makeEmptyStoreState$1());\n  return initializeState != null ? snapshot.map(initializeState) : snapshot;\n} // Factory to clone a snapshot state\n\n\nconst [memoizedCloneSnapshot, invalidateMemoizedSnapshot$2] = memoizeOneWithArgsHashAndInvalidation$1( // $FlowFixMe[missing-local-annot]\n(store, version) => {\n  var _storeState$nextTree;\n\n  const storeState = store.getState();\n  const treeState = version === 'latest' ? (_storeState$nextTree = storeState.nextTree) !== null && _storeState$nextTree !== void 0 ? _storeState$nextTree : storeState.currentTree : Recoil_nullthrows(storeState.previousTree);\n  return new Snapshot(cloneStoreState(store, treeState), store.storeID);\n}, (store, version) => {\n  var _store$getState$nextT, _store$getState$previ;\n\n  return String(version) + String(store.storeID) + String((_store$getState$nextT = store.getState().nextTree) === null || _store$getState$nextT === void 0 ? void 0 : _store$getState$nextT.version) + String(store.getState().currentTree.version) + String((_store$getState$previ = store.getState().previousTree) === null || _store$getState$previ === void 0 ? void 0 : _store$getState$previ.version);\n}); // Avoid circular dependencies\n\nsetInvalidateMemoizedSnapshot$1(invalidateMemoizedSnapshot$2);\n\nfunction cloneSnapshot(store, version = 'latest') {\n  const snapshot = memoizedCloneSnapshot(store, version);\n\n  if (!snapshot.isRetained()) {\n    invalidateMemoizedSnapshot$2();\n    return memoizedCloneSnapshot(store, version);\n  }\n\n  return snapshot;\n}\n\nclass MutableSnapshot extends Snapshot {\n  constructor(snapshot, batch) {\n    super(cloneStoreState(snapshot.getStore_INTERNAL(), snapshot.getStore_INTERNAL().getState().currentTree, true), snapshot.getStoreID());\n\n    _defineProperty(this, \"_batch\", void 0);\n\n    _defineProperty(this, \"set\", (recoilState, newValueOrUpdater) => {\n      this.checkRefCount_INTERNAL();\n      const store = this.getStore_INTERNAL(); // This batchUpdates ensures this `set` is applied immediately and you can\n      // read the written value after calling `set`. I would like to remove this\n      // behavior and only batch in `Snapshot.map`, but this would be a breaking\n      // change potentially.\n\n      this._batch(() => {\n        updateRetainCount$1(store, recoilState.key, 1);\n        setRecoilValue$1(this.getStore_INTERNAL(), recoilState, newValueOrUpdater);\n      });\n    });\n\n    _defineProperty(this, \"reset\", recoilState => {\n      this.checkRefCount_INTERNAL();\n      const store = this.getStore_INTERNAL(); // See note at `set` about batched updates.\n\n      this._batch(() => {\n        updateRetainCount$1(store, recoilState.key, 1);\n        setRecoilValue$1(this.getStore_INTERNAL(), recoilState, DEFAULT_VALUE$1);\n      });\n    });\n\n    _defineProperty(this, \"setUnvalidatedAtomValues_DEPRECATED\", values => {\n      this.checkRefCount_INTERNAL();\n      const store = this.getStore_INTERNAL(); // See note at `set` about batched updates.\n\n      batchUpdates$1(() => {\n        for (const [k, v] of values.entries()) {\n          updateRetainCount$1(store, k, 1);\n          setUnvalidatedRecoilValue$1(store, new AbstractRecoilValue$2(k), v);\n        }\n      });\n    });\n\n    this._batch = batch;\n  }\n\n}\n\nvar Recoil_Snapshot = {\n  Snapshot,\n  MutableSnapshot,\n  freshSnapshot,\n  cloneSnapshot\n};\n\nvar Recoil_Snapshot_1 = Recoil_Snapshot.Snapshot;\nvar Recoil_Snapshot_2 = Recoil_Snapshot.MutableSnapshot;\nvar Recoil_Snapshot_3 = Recoil_Snapshot.freshSnapshot;\nvar Recoil_Snapshot_4 = Recoil_Snapshot.cloneSnapshot;\n\nvar Recoil_Snapshot$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Snapshot: Recoil_Snapshot_1,\n  MutableSnapshot: Recoil_Snapshot_2,\n  freshSnapshot: Recoil_Snapshot_3,\n  cloneSnapshot: Recoil_Snapshot_4\n});\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction unionSets(...sets) {\n  const result = new Set();\n\n  for (const set of sets) {\n    for (const value of set) {\n      result.add(value);\n    }\n  }\n\n  return result;\n}\n\nvar Recoil_unionSets = unionSets;\n\nconst {\n  useRef\n} = react;\n/**\n * The same as `useRef()` except that if a function is specified then it will\n * call that function to get the value to initialize the reference with.\n * This is similar to how `useState()` behaves when given a function.  It allows\n * the user to avoid generating the initial value for subsequent renders.\n * The tradeoff is that to set the reference to a function itself you need to\n * nest it: useRefInitOnce(() => () => {...});\n */\n\n\nfunction useRefInitOnce(initialValue) {\n  // $FlowExpectedError[incompatible-call]\n  const ref = useRef(initialValue);\n\n  if (ref.current === initialValue && typeof initialValue === 'function') {\n    // $FlowExpectedError[incompatible-use]\n    ref.current = initialValue();\n  }\n\n  return ref;\n}\n\nvar Recoil_useRefInitOnce = useRefInitOnce;\n\n// @fb-only: const RecoilusagelogEvent = require('RecoilusagelogEvent');\n// @fb-only: const RecoilUsageLogFalcoEvent = require('RecoilUsageLogFalcoEvent');\n// @fb-only: const URI = require('URI');\n\n\nconst {\n  getNextTreeStateVersion: getNextTreeStateVersion$3,\n  makeEmptyStoreState: makeEmptyStoreState$2\n} = Recoil_State;\n\nconst {\n  cleanUpNode: cleanUpNode$2,\n  getDownstreamNodes: getDownstreamNodes$2,\n  initializeNode: initializeNode$2,\n  setNodeValue: setNodeValue$2,\n  setUnvalidatedAtomValue_DEPRECATED: setUnvalidatedAtomValue_DEPRECATED$1\n} = Recoil_FunctionalCore;\n\nconst {\n  graph: graph$2\n} = Recoil_Graph;\n\nconst {\n  cloneGraph: cloneGraph$1\n} = Recoil_Graph;\n\nconst {\n  getNextStoreID: getNextStoreID$2\n} = Recoil_Keys;\n\nconst {\n  createMutableSource: createMutableSource$1,\n  reactMode: reactMode$2\n} = Recoil_ReactMode;\n\nconst {\n  applyAtomValueWrites: applyAtomValueWrites$1\n} = Recoil_RecoilValueInterface;\n\nconst {\n  releaseScheduledRetainablesNow: releaseScheduledRetainablesNow$1\n} = Recoil_Retention;\n\nconst {\n  freshSnapshot: freshSnapshot$1\n} = Recoil_Snapshot$1;\n\n\n\nconst {\n  useCallback,\n  useContext,\n  useEffect,\n  useMemo,\n  useRef: useRef$1,\n  useState\n} = react;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction notInAContext() {\n  throw Recoil_err('This component must be used inside a <RecoilRoot> component.');\n}\n\nconst defaultStore = Object.freeze({\n  storeID: getNextStoreID$2(),\n  getState: notInAContext,\n  replaceState: notInAContext,\n  getGraph: notInAContext,\n  subscribeToTransactions: notInAContext,\n  addTransactionMetadata: notInAContext\n});\nlet stateReplacerIsBeingExecuted = false;\n\nfunction startNextTreeIfNeeded(store) {\n  if (stateReplacerIsBeingExecuted) {\n    throw Recoil_err('An atom update was triggered within the execution of a state updater function. State updater functions provided to Recoil must be pure functions.');\n  }\n\n  const storeState = store.getState();\n\n  if (storeState.nextTree === null) {\n    if (Recoil_gkx('recoil_memory_managament_2020') && Recoil_gkx('recoil_release_on_cascading_update_killswitch_2021')) {\n      // If this is a cascading update (that is, rendering due to one state change\n      // invokes a second state change), we won't have cleaned up retainables yet\n      // because this normally happens after notifying components. Do it before\n      // proceeding with the cascading update so that it remains predictable:\n      if (storeState.commitDepth > 0) {\n        releaseScheduledRetainablesNow$1(store);\n      }\n    }\n\n    const version = storeState.currentTree.version;\n    const nextVersion = getNextTreeStateVersion$3();\n    storeState.nextTree = { ...storeState.currentTree,\n      version: nextVersion,\n      stateID: nextVersion,\n      dirtyAtoms: new Set(),\n      transactionMetadata: {}\n    };\n    storeState.graphsByVersion.set(nextVersion, cloneGraph$1(Recoil_nullthrows(storeState.graphsByVersion.get(version))));\n  }\n}\n\nconst AppContext = react.createContext({\n  current: defaultStore\n});\n\nconst useStoreRef = () => useContext(AppContext); // $FlowExpectedError[incompatible-call]\n\n\nconst MutableSourceContext = react.createContext(null);\n\nfunction useRecoilMutableSource() {\n  const mutableSource = useContext(MutableSourceContext);\n\n  if (mutableSource == null) {\n    Recoil_expectationViolation('Attempted to use a Recoil hook outside of a <RecoilRoot>. ' + '<RecoilRoot> must be an ancestor of any component that uses ' + 'Recoil hooks.');\n  }\n\n  return mutableSource;\n}\n\nfunction notifyComponents(store, storeState, treeState) {\n  const dependentNodes = getDownstreamNodes$2(store, treeState, treeState.dirtyAtoms);\n\n  for (const key of dependentNodes) {\n    const comps = storeState.nodeToComponentSubscriptions.get(key);\n\n    if (comps) {\n      for (const [_subID, [_debugName, callback]] of comps) {\n        callback(treeState);\n      }\n    }\n  }\n}\n\nfunction sendEndOfBatchNotifications(store) {\n  const storeState = store.getState();\n  const treeState = storeState.currentTree; // Inform transaction subscribers of the transaction:\n\n  const dirtyAtoms = treeState.dirtyAtoms;\n\n  if (dirtyAtoms.size) {\n    // Execute Node-specific subscribers before global subscribers\n    for (const [key, subscriptions] of storeState.nodeTransactionSubscriptions) {\n      if (dirtyAtoms.has(key)) {\n        for (const [_, subscription] of subscriptions) {\n          subscription(store);\n        }\n      }\n    }\n\n    for (const [_, subscription] of storeState.transactionSubscriptions) {\n      subscription(store);\n    }\n\n    if (!reactMode$2().early || storeState.suspendedComponentResolvers.size > 0) {\n      // Notifying components is needed to wake from suspense, even when using\n      // early rendering.\n      notifyComponents(store, storeState, treeState); // Wake all suspended components so the right one(s) can try to re-render.\n      // We need to wake up components not just when some asynchronous selector\n      // resolved, but also when changing synchronous values because this may cause\n      // a selector to change from asynchronous to synchronous, in which case there\n      // would be no follow-up asynchronous resolution to wake us up.\n      // TODO OPTIMIZATION Only wake up related downstream components\n\n      storeState.suspendedComponentResolvers.forEach(cb => cb());\n      storeState.suspendedComponentResolvers.clear();\n    }\n  } // Special behavior ONLY invoked by useInterface.\n  // FIXME delete queuedComponentCallbacks_DEPRECATED when deleting useInterface.\n\n\n  storeState.queuedComponentCallbacks_DEPRECATED.forEach(cb => cb(treeState));\n  storeState.queuedComponentCallbacks_DEPRECATED.splice(0, storeState.queuedComponentCallbacks_DEPRECATED.length);\n}\n\nfunction endBatch(store) {\n  const storeState = store.getState();\n  storeState.commitDepth++;\n\n  try {\n    const {\n      nextTree\n    } = storeState; // Ignore commits that are not because of Recoil transactions -- namely,\n    // because something above RecoilRoot re-rendered:\n\n    if (nextTree == null) {\n      return;\n    } // nextTree is now committed -- note that copying and reset occurs when\n    // a transaction begins, in startNextTreeIfNeeded:\n\n\n    storeState.previousTree = storeState.currentTree;\n    storeState.currentTree = nextTree;\n    storeState.nextTree = null;\n    sendEndOfBatchNotifications(store);\n\n    if (storeState.previousTree != null) {\n      storeState.graphsByVersion.delete(storeState.previousTree.version);\n    } else {\n      Recoil_recoverableViolation('Ended batch with no previous state, which is unexpected', 'recoil');\n    }\n\n    storeState.previousTree = null;\n\n    if (Recoil_gkx('recoil_memory_managament_2020')) {\n      // Only release retainables if there were no writes during the end of the\n      // batch.  This avoids releasing something we might be about to use.\n      if (nextTree == null) {\n        releaseScheduledRetainablesNow$1(store);\n      }\n    }\n  } finally {\n    storeState.commitDepth--;\n  }\n}\n/*\n * The purpose of the Batcher is to observe when React batches end so that\n * Recoil state changes can be batched. Whenever Recoil state changes, we call\n * setState on the batcher. Then we wait for that change to be committed, which\n * signifies the end of the batch. That's when we respond to the Recoil change.\n */\n\n\nfunction Batcher({\n  setNotifyBatcherOfChange\n}) {\n  const storeRef = useStoreRef();\n  const [, setState] = useState([]); // $FlowFixMe[incompatible-call]\n\n  setNotifyBatcherOfChange(() => setState({}));\n  useEffect(() => {\n    // $FlowFixMe[incompatible-call]\n    setNotifyBatcherOfChange(() => setState({})); // If an asynchronous selector resolves after the Batcher is unmounted,\n    // notifyBatcherOfChange will still be called. An error gets thrown whenever\n    // setState is called after a component is already unmounted, so this sets\n    // notifyBatcherOfChange to be a no-op.\n\n    return () => {\n      setNotifyBatcherOfChange(() => {});\n    };\n  }, [setNotifyBatcherOfChange]);\n  useEffect(() => {\n    // enqueueExecution runs this function immediately; it is only used to\n    // manipulate the order of useEffects during tests, since React seems to\n    // call useEffect in an unpredictable order sometimes.\n    Recoil_Queue.enqueueExecution('Batcher', () => {\n      endBatch(storeRef.current);\n    });\n  });\n  return null;\n}\n\nif (process.env.NODE_ENV !== \"production\") {\n  if (typeof window !== 'undefined' && !window.$recoilDebugStates) {\n    window.$recoilDebugStates = [];\n  }\n} // When removing this deprecated function, remove stateBySettingRecoilValue\n// which will no longer be needed.\n\n\nfunction initialStoreState_DEPRECATED(store, initializeState) {\n  const initial = makeEmptyStoreState$2();\n  initializeState({\n    set: (atom, value) => {\n      const state = initial.currentTree;\n      const writes = setNodeValue$2(store, state, atom.key, value);\n      const writtenNodes = new Set(writes.keys());\n      const nonvalidatedAtoms = state.nonvalidatedAtoms.clone();\n\n      for (const n of writtenNodes) {\n        nonvalidatedAtoms.delete(n);\n      }\n\n      initial.currentTree = { ...state,\n        dirtyAtoms: Recoil_unionSets(state.dirtyAtoms, writtenNodes),\n        atomValues: applyAtomValueWrites$1(state.atomValues, writes),\n        // NB: PLEASE un-export applyAtomValueWrites when deleting this code\n        nonvalidatedAtoms\n      };\n    },\n    setUnvalidatedAtomValues: atomValues => {\n      // FIXME replace this with a mutative loop\n      atomValues.forEach((v, k) => {\n        initial.currentTree = setUnvalidatedAtomValue_DEPRECATED$1(initial.currentTree, k, v);\n      });\n    }\n  });\n  return initial;\n} // Initialize state snapshot for <RecoilRoot> for the initializeState prop.\n// Atom effect initialization takes precedence over this prop.\n// Any atom effects will be run before initialization, but then cleaned up,\n// they are then re-run when used as part of rendering.  These semantics are\n// compatible with React StrictMode where effects may be re-run multiple times\n// but state initialization only happens once the first time.\n\n\nfunction initialStoreState(initializeState) {\n  // Initialize a snapshot and get its store\n  const snapshot = freshSnapshot$1(initializeState);\n  const storeState = snapshot.getStore_INTERNAL().getState(); // Counteract the snapshot auto-release\n\n  snapshot.retain(); // Cleanup any effects run during initialization and clear the handlers so\n  // they will re-initialize if used during rendering.  This allows atom effect\n  // initialization to take precedence over initializeState and be compatible\n  // with StrictMode semantics.\n\n  storeState.nodeCleanupFunctions.forEach(cleanup => cleanup());\n  storeState.nodeCleanupFunctions.clear();\n  return storeState;\n}\n\nlet nextID = 0;\n\nfunction RecoilRoot_INTERNAL({\n  initializeState_DEPRECATED,\n  initializeState,\n  store_INTERNAL: storeProp,\n  // For use with React \"context bridging\"\n  children\n}) {\n  // prettier-ignore\n  // @fb-only: useEffect(() => {\n  // @fb-only: if (gkx('recoil_usage_logging')) {\n  // @fb-only: try {\n  // @fb-only: RecoilUsageLogFalcoEvent.log(() => ({\n  // @fb-only: type: RecoilusagelogEvent.RECOIL_ROOT_MOUNTED,\n  // @fb-only: path: URI.getRequestURI().getPath(),\n  // @fb-only: }));\n  // @fb-only: } catch {\n  // @fb-only: recoverableViolation(\n  // @fb-only: 'Error when logging Recoil Usage event',\n  // @fb-only: 'recoil',\n  // @fb-only: );\n  // @fb-only: }\n  // @fb-only: }\n  // @fb-only: }, []);\n  let storeStateRef; // eslint-disable-line prefer-const\n\n  const getGraph = version => {\n    const graphs = storeStateRef.current.graphsByVersion;\n\n    if (graphs.has(version)) {\n      return Recoil_nullthrows(graphs.get(version));\n    }\n\n    const newGraph = graph$2();\n    graphs.set(version, newGraph);\n    return newGraph;\n  };\n\n  const subscribeToTransactions = (callback, key) => {\n    if (key == null) {\n      // Global transaction subscriptions\n      const {\n        transactionSubscriptions\n      } = storeRef.current.getState();\n      const id = nextID++;\n      transactionSubscriptions.set(id, callback);\n      return {\n        release: () => {\n          transactionSubscriptions.delete(id);\n        }\n      };\n    } else {\n      // Node-specific transaction subscriptions:\n      const {\n        nodeTransactionSubscriptions\n      } = storeRef.current.getState();\n\n      if (!nodeTransactionSubscriptions.has(key)) {\n        nodeTransactionSubscriptions.set(key, new Map());\n      }\n\n      const id = nextID++;\n      Recoil_nullthrows(nodeTransactionSubscriptions.get(key)).set(id, callback);\n      return {\n        release: () => {\n          const subs = nodeTransactionSubscriptions.get(key);\n\n          if (subs) {\n            subs.delete(id);\n\n            if (subs.size === 0) {\n              nodeTransactionSubscriptions.delete(key);\n            }\n          }\n        }\n      };\n    }\n  };\n\n  const addTransactionMetadata = metadata => {\n    startNextTreeIfNeeded(storeRef.current);\n\n    for (const k of Object.keys(metadata)) {\n      Recoil_nullthrows(storeRef.current.getState().nextTree).transactionMetadata[k] = metadata[k];\n    }\n  };\n\n  const replaceState = replacer => {\n    startNextTreeIfNeeded(storeRef.current); // Use replacer to get the next state:\n\n    const nextTree = Recoil_nullthrows(storeStateRef.current.nextTree);\n    let replaced;\n\n    try {\n      stateReplacerIsBeingExecuted = true;\n      replaced = replacer(nextTree);\n    } finally {\n      stateReplacerIsBeingExecuted = false;\n    }\n\n    if (replaced === nextTree) {\n      return;\n    }\n\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof window !== 'undefined') {\n        window.$recoilDebugStates.push(replaced); // TODO this shouldn't happen here because it's not batched\n      }\n    } // Save changes to nextTree and schedule a React update:\n\n\n    storeStateRef.current.nextTree = replaced;\n\n    if (reactMode$2().early) {\n      notifyComponents(storeRef.current, storeStateRef.current, replaced);\n    }\n\n    Recoil_nullthrows(notifyBatcherOfChange.current)();\n  };\n\n  const notifyBatcherOfChange = useRef$1(null);\n  const setNotifyBatcherOfChange = useCallback(x => {\n    notifyBatcherOfChange.current = x;\n  }, [notifyBatcherOfChange]);\n  const storeRef = Recoil_useRefInitOnce(() => storeProp !== null && storeProp !== void 0 ? storeProp : {\n    storeID: getNextStoreID$2(),\n    getState: () => storeStateRef.current,\n    replaceState,\n    getGraph,\n    subscribeToTransactions,\n    addTransactionMetadata\n  });\n\n  if (storeProp != null) {\n    storeRef.current = storeProp;\n  }\n\n  storeStateRef = Recoil_useRefInitOnce(() => initializeState_DEPRECATED != null ? initialStoreState_DEPRECATED(storeRef.current, initializeState_DEPRECATED) : initializeState != null ? initialStoreState(initializeState) : makeEmptyStoreState$2());\n  const mutableSource = useMemo(() => createMutableSource$1 === null || createMutableSource$1 === void 0 ? void 0 : createMutableSource$1(storeStateRef, () => storeStateRef.current.currentTree.version), [storeStateRef]); // Cleanup when the <RecoilRoot> is unmounted\n\n  useEffect(() => {\n    // React is free to call effect cleanup handlers and effects at will, the\n    // deps array is only an optimization.  For example, React strict mode\n    // will execute each effect twice for testing.  Therefore, we need symmetry\n    // to re-initialize all known atoms after they were cleaned up.\n    const store = storeRef.current;\n\n    for (const atomKey of new Set(store.getState().knownAtoms)) {\n      initializeNode$2(store, atomKey, 'get');\n    }\n\n    return () => {\n      for (const atomKey of store.getState().knownAtoms) {\n        cleanUpNode$2(store, atomKey);\n      }\n    };\n  }, [storeRef]);\n  return /*#__PURE__*/react.createElement(AppContext.Provider, {\n    value: storeRef\n  }, /*#__PURE__*/react.createElement(MutableSourceContext.Provider, {\n    value: mutableSource\n  }, /*#__PURE__*/react.createElement(Batcher, {\n    setNotifyBatcherOfChange: setNotifyBatcherOfChange\n  }), children));\n}\n\nfunction RecoilRoot(props) {\n  const {\n    override,\n    ...propsExceptOverride\n  } = props;\n  const ancestorStoreRef = useStoreRef();\n\n  if (override === false && ancestorStoreRef.current !== defaultStore) {\n    // If ancestorStoreRef.current !== defaultStore, it means that this\n    // RecoilRoot is not nested within another.\n    return props.children;\n  }\n\n  return /*#__PURE__*/react.createElement(RecoilRoot_INTERNAL, propsExceptOverride);\n}\n\nfunction useRecoilStoreID() {\n  return useStoreRef().current.storeID;\n}\n\nvar Recoil_RecoilRoot = {\n  RecoilRoot,\n  useStoreRef,\n  useRecoilMutableSource,\n  useRecoilStoreID,\n  notifyComponents_FOR_TESTING: notifyComponents,\n  sendEndOfBatchNotifications_FOR_TESTING: sendEndOfBatchNotifications\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction shallowArrayEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0, l = a.length; i < l; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nvar Recoil_shallowArrayEqual = shallowArrayEqual;\n\nconst {\n  useEffect: useEffect$1,\n  useRef: useRef$2\n} = react;\n\nfunction usePrevious(value) {\n  const ref = useRef$2();\n  useEffect$1(() => {\n    // $FlowFixMe[incompatible-type]\n    ref.current = value;\n  });\n  return ref.current;\n}\n\nvar Recoil_usePrevious = usePrevious;\n\nconst {\n  useStoreRef: useStoreRef$1\n} = Recoil_RecoilRoot;\n\nconst {\n  SUSPENSE_TIMEOUT_MS: SUSPENSE_TIMEOUT_MS$1\n} = Recoil_Retention;\n\nconst {\n  updateRetainCount: updateRetainCount$2\n} = Recoil_Retention;\n\nconst {\n  RetentionZone: RetentionZone$3\n} = Recoil_RetentionZone;\n\nconst {\n  useEffect: useEffect$2,\n  useRef: useRef$3\n} = react;\n\nconst {\n  isSSR: isSSR$2\n} = Recoil_Environment;\n\n\n\n\n\n // I don't see a way to avoid the any type here because we want to accept readable\n// and writable values with any type parameter, but normally with writable ones\n// RecoilState<SomeT> is not a subtype of RecoilState<mixed>.\n\n\n// flowlint-line unclear-type:off\nfunction useRetain(toRetain) {\n  if (!Recoil_gkx('recoil_memory_managament_2020')) {\n    return;\n  } // eslint-disable-next-line fb-www/react-hooks\n\n\n  return useRetain_ACTUAL(toRetain);\n}\n\nfunction useRetain_ACTUAL(toRetain) {\n  const array = Array.isArray(toRetain) ? toRetain : [toRetain];\n  const retainables = array.map(a => a instanceof RetentionZone$3 ? a : a.key);\n  const storeRef = useStoreRef$1();\n  useEffect$2(() => {\n    if (!Recoil_gkx('recoil_memory_managament_2020')) {\n      return;\n    }\n\n    const store = storeRef.current;\n\n    if (timeoutID.current && !isSSR$2) {\n      // Already performed a temporary retain on render, simply cancel the release\n      // of that temporary retain.\n      window.clearTimeout(timeoutID.current);\n      timeoutID.current = null;\n    } else {\n      for (const r of retainables) {\n        updateRetainCount$2(store, r, 1);\n      }\n    }\n\n    return () => {\n      for (const r of retainables) {\n        updateRetainCount$2(store, r, -1);\n      }\n    }; // eslint-disable-next-line fb-www/react-hooks-deps\n  }, [storeRef, ...retainables]); // We want to retain if the component suspends. This is terrible but the Suspense\n  // API affords us no better option. If we suspend and never commit after some\n  // seconds, then release. The 'actual' retain/release in the effect above\n  // cancels this.\n\n  const timeoutID = useRef$3();\n  const previousRetainables = Recoil_usePrevious(retainables);\n\n  if (!isSSR$2 && (previousRetainables === undefined || !Recoil_shallowArrayEqual(previousRetainables, retainables))) {\n    const store = storeRef.current;\n\n    for (const r of retainables) {\n      updateRetainCount$2(store, r, 1);\n    }\n\n    if (previousRetainables) {\n      for (const r of previousRetainables) {\n        updateRetainCount$2(store, r, -1);\n      }\n    }\n\n    if (timeoutID.current) {\n      window.clearTimeout(timeoutID.current);\n    }\n\n    timeoutID.current = window.setTimeout(() => {\n      timeoutID.current = null;\n\n      for (const r of retainables) {\n        updateRetainCount$2(store, r, -1);\n      }\n    }, SUSPENSE_TIMEOUT_MS$1);\n  }\n}\n\nvar Recoil_useRetain = useRetain;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * THIS CODE HAS BEEN COMMENTED OUT INTENTIONALLY\n *\n * This technique of getting the component name is imperfect, since it both only\n * works in a non-minified code base, and more importantly introduces performance\n * problems since it relies in throwing errors which is an expensive operation.\n *\n * At some point we may want to reevaluate this technique hence why we have commented\n * this code out, rather than delete it all together.\n */\n// const {useRef} = require('react');\n// const gkx = require('recoil-shared/util/Recoil_gkx');\n// const stackTraceParser = require('recoil-shared/util/Recoil_stackTraceParser');\n\nfunction useComponentName() {\n  // const nameRef = useRef();\n  // if (__DEV__) {\n  //   if (gkx('recoil_infer_component_names')) {\n  //     if (nameRef.current === undefined) {\n  //       // There is no blessed way to determine the calling React component from\n  //       // within a hook. This hack uses the fact that hooks must start with 'use'\n  //       // and that hooks are either called by React Components or other hooks. It\n  //       // follows therefore, that to find the calling component, you simply need\n  //       // to look down the stack and find the first function which doesn't start\n  //       // with 'use'. We are only enabling this in dev for now, since once the\n  //       // codebase is minified, the naming assumptions no longer hold true.\n  //       // eslint-disable-next-line fb-www/no-new-error\n  //       const frames = stackTraceParser(new Error().stack);\n  //       for (const {methodName} of frames) {\n  //         // I observed cases where the frame was of the form 'Object.useXXX'\n  //         // hence why I'm searching for hooks following a word boundary\n  //         if (!methodName.match(/\\buse[^\\b]+$/)) {\n  //           return (nameRef.current = methodName);\n  //         }\n  //       }\n  //       nameRef.current = null;\n  //     }\n  //     return nameRef.current ?? '<unable to determine component name>';\n  //   }\n  // }\n  // @fb-only: return \"<component name only available when both in dev mode and when passing GK 'recoil_infer_component_names'>\";\n  return '<component name not available>'; // @oss-only\n}\n\nvar Recoil_useComponentName = useComponentName;\n\nconst {\n  batchUpdates: batchUpdates$2\n} = Recoil_Batching;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$2\n} = Recoil_Node;\n\nconst {\n  currentRendererSupportsUseSyncExternalStore: currentRendererSupportsUseSyncExternalStore$1,\n  reactMode: reactMode$3,\n  useMutableSource: useMutableSource$1,\n  useSyncExternalStore: useSyncExternalStore$1\n} = Recoil_ReactMode;\n\nconst {\n  useRecoilMutableSource: useRecoilMutableSource$1,\n  useStoreRef: useStoreRef$2\n} = Recoil_RecoilRoot;\n\nconst {\n  isRecoilValue: isRecoilValue$2\n} = Recoil_RecoilValue$1;\n\nconst {\n  AbstractRecoilValue: AbstractRecoilValue$3,\n  getRecoilValueAsLoadable: getRecoilValueAsLoadable$2,\n  setRecoilValue: setRecoilValue$2,\n  setUnvalidatedRecoilValue: setUnvalidatedRecoilValue$2,\n  subscribeToRecoilValue: subscribeToRecoilValue$1\n} = Recoil_RecoilValueInterface;\n\n\n\nconst {\n  useCallback: useCallback$1,\n  useEffect: useEffect$3,\n  useMemo: useMemo$1,\n  useRef: useRef$4,\n  useState: useState$1\n} = react;\n\nconst {\n  setByAddingToSet: setByAddingToSet$2\n} = Recoil_CopyOnWrite;\n\n\n\nconst {\n  isSSR: isSSR$3\n} = Recoil_Environment;\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction handleLoadable(loadable, recoilValue, storeRef) {\n  // We can't just throw the promise we are waiting on to Suspense.  If the\n  // upstream dependencies change it may produce a state in which the component\n  // can render, but it would still be suspended on a Promise that may never resolve.\n  if (loadable.state === 'hasValue') {\n    return loadable.contents;\n  } else if (loadable.state === 'loading') {\n    const promise = new Promise(resolve => {\n      const suspendedComponentResolvers = storeRef.current.getState().suspendedComponentResolvers;\n      suspendedComponentResolvers.add(resolve); // SSR should clear out the wake-up resolver if the Promise is resolved\n      // to avoid infinite loops.  (See https://github.com/facebookexperimental/Recoil/pull/2073)\n\n      if (isSSR$3 && Recoil_isPromise(loadable.contents)) {\n        loadable.contents.finally(() => {\n          suspendedComponentResolvers.delete(resolve);\n        });\n      }\n    }); // $FlowExpectedError Flow(prop-missing) for integrating with tools that inspect thrown promises @fb-only\n    // @fb-only: promise.displayName = `Recoil State: ${recoilValue.key}`;\n\n    throw promise;\n  } else if (loadable.state === 'hasError') {\n    throw loadable.contents;\n  } else {\n    throw Recoil_err(`Invalid value of loadable atom \"${recoilValue.key}\"`);\n  }\n}\n\nfunction validateRecoilValue(recoilValue, hookName // $FlowFixMe[missing-local-annot]\n) {\n  if (!isRecoilValue$2(recoilValue)) {\n    throw Recoil_err(`Invalid argument to ${hookName}: expected an atom or selector but got ${String(recoilValue)}`);\n  }\n}\n\n/**\n * Various things are broken with useRecoilInterface, particularly concurrent\n * mode, React strict mode, and memory management. They will not be fixed.\n * */\nfunction useRecoilInterface_DEPRECATED() {\n  const componentName = Recoil_useComponentName();\n  const storeRef = useStoreRef$2(); // eslint-disable-next-line fb-www/react-no-unused-state-hook\n\n  const [, forceUpdate] = useState$1([]);\n  const recoilValuesUsed = useRef$4(new Set());\n  recoilValuesUsed.current = new Set(); // Track the RecoilValues used just during this render\n\n  const previousSubscriptions = useRef$4(new Set());\n  const subscriptions = useRef$4(new Map());\n  const unsubscribeFrom = useCallback$1(key => {\n    const sub = subscriptions.current.get(key);\n\n    if (sub) {\n      sub.release();\n      subscriptions.current.delete(key);\n    }\n  }, [subscriptions]);\n  const updateState = useCallback$1((_state, key) => {\n    if (subscriptions.current.has(key)) {\n      forceUpdate([]);\n    }\n  }, []); // Effect to add/remove subscriptions as nodes are used\n\n  useEffect$3(() => {\n    const store = storeRef.current;\n    Recoil_differenceSets(recoilValuesUsed.current, previousSubscriptions.current).forEach(key => {\n      if (subscriptions.current.has(key)) {\n        Recoil_expectationViolation(`Double subscription to RecoilValue \"${key}\"`);\n        return;\n      }\n\n      const sub = subscribeToRecoilValue$1(store, new AbstractRecoilValue$3(key), state => updateState(state, key), componentName);\n      subscriptions.current.set(key, sub);\n      /**\n       * Since we're subscribing in an effect we need to update to the latest\n       * value of the atom since it may have changed since we rendered. We can\n       * go ahead and do that now, unless we're in the middle of a batch --\n       * in which case we should do it at the end of the batch, due to the\n       * following edge case: Suppose an atom is updated in another useEffect\n       * of this same component. Then the following sequence of events occur:\n       * 1. Atom is updated and subs fired (but we may not be subscribed\n       *    yet depending on order of effects, so we miss this) Updated value\n       *    is now in nextTree, but not currentTree.\n       * 2. This effect happens. We subscribe and update.\n       * 3. From the update we re-render and read currentTree, with old value.\n       * 4. Batcher's effect sets currentTree to nextTree.\n       * In this sequence we miss the update. To avoid that, add the update\n       * to queuedComponentCallback if a batch is in progress.\n       */\n      // FIXME delete queuedComponentCallbacks_DEPRECATED when deleting useInterface.\n\n      const state = store.getState();\n\n      if (state.nextTree) {\n        store.getState().queuedComponentCallbacks_DEPRECATED.push(() => {\n          updateState(store.getState(), key);\n        });\n      } else {\n        updateState(store.getState(), key);\n      }\n    });\n    Recoil_differenceSets(previousSubscriptions.current, recoilValuesUsed.current).forEach(key => {\n      unsubscribeFrom(key);\n    });\n    previousSubscriptions.current = recoilValuesUsed.current;\n  }); // Effect to unsubscribe from all when unmounting\n\n  useEffect$3(() => {\n    const currentSubscriptions = subscriptions.current; // Restore subscriptions that were cleared due to StrictMode running this effect twice\n\n    Recoil_differenceSets(recoilValuesUsed.current, new Set(currentSubscriptions.keys())).forEach(key => {\n      const sub = subscribeToRecoilValue$1(storeRef.current, new AbstractRecoilValue$3(key), state => updateState(state, key), componentName);\n      currentSubscriptions.set(key, sub);\n    });\n    return () => currentSubscriptions.forEach((_, key) => unsubscribeFrom(key));\n  }, [componentName, storeRef, unsubscribeFrom, updateState]);\n  return useMemo$1(() => {\n    // eslint-disable-next-line no-shadow\n    function useSetRecoilState(recoilState) {\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilState, 'useSetRecoilState');\n      }\n\n      return newValueOrUpdater => {\n        setRecoilValue$2(storeRef.current, recoilState, newValueOrUpdater);\n      };\n    } // eslint-disable-next-line no-shadow\n\n\n    function useResetRecoilState(recoilState) {\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilState, 'useResetRecoilState');\n      }\n\n      return () => setRecoilValue$2(storeRef.current, recoilState, DEFAULT_VALUE$2);\n    } // eslint-disable-next-line no-shadow\n\n\n    function useRecoilValueLoadable(recoilValue) {\n      var _storeState$nextTree;\n\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilValue, 'useRecoilValueLoadable');\n      }\n\n      if (!recoilValuesUsed.current.has(recoilValue.key)) {\n        recoilValuesUsed.current = setByAddingToSet$2(recoilValuesUsed.current, recoilValue.key);\n      } // TODO Restore optimization to memoize lookup\n\n\n      const storeState = storeRef.current.getState();\n      return getRecoilValueAsLoadable$2(storeRef.current, recoilValue, reactMode$3().early ? (_storeState$nextTree = storeState.nextTree) !== null && _storeState$nextTree !== void 0 ? _storeState$nextTree : storeState.currentTree : storeState.currentTree);\n    } // eslint-disable-next-line no-shadow\n\n\n    function useRecoilValue(recoilValue) {\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilValue, 'useRecoilValue');\n      }\n\n      const loadable = useRecoilValueLoadable(recoilValue);\n      return handleLoadable(loadable, recoilValue, storeRef);\n    } // eslint-disable-next-line no-shadow\n\n\n    function useRecoilState(recoilState) {\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilState, 'useRecoilState');\n      }\n\n      return [useRecoilValue(recoilState), useSetRecoilState(recoilState)];\n    } // eslint-disable-next-line no-shadow\n\n\n    function useRecoilStateLoadable(recoilState) {\n      if (process.env.NODE_ENV !== \"production\") {\n        validateRecoilValue(recoilState, 'useRecoilStateLoadable');\n      }\n\n      return [useRecoilValueLoadable(recoilState), useSetRecoilState(recoilState)];\n    }\n\n    return {\n      getRecoilValue: useRecoilValue,\n      getRecoilValueLoadable: useRecoilValueLoadable,\n      getRecoilState: useRecoilState,\n      getRecoilStateLoadable: useRecoilStateLoadable,\n      getSetRecoilState: useSetRecoilState,\n      getResetRecoilState: useResetRecoilState\n    };\n  }, [recoilValuesUsed, storeRef]);\n}\n\nconst recoilComponentGetRecoilValueCount_FOR_TESTING = {\n  current: 0\n};\n\nfunction useRecoilValueLoadable_SYNC_EXTERNAL_STORE(recoilValue) {\n  const storeRef = useStoreRef$2();\n  const componentName = Recoil_useComponentName();\n  const getSnapshot = useCallback$1(() => {\n    var _storeState$nextTree2;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      recoilComponentGetRecoilValueCount_FOR_TESTING.current++;\n    }\n\n    const store = storeRef.current;\n    const storeState = store.getState();\n    const treeState = reactMode$3().early ? (_storeState$nextTree2 = storeState.nextTree) !== null && _storeState$nextTree2 !== void 0 ? _storeState$nextTree2 : storeState.currentTree : storeState.currentTree;\n    const loadable = getRecoilValueAsLoadable$2(store, recoilValue, treeState);\n    return {\n      loadable,\n      key: recoilValue.key\n    };\n  }, [storeRef, recoilValue]); // Memoize the state to avoid unnecessary rerenders\n\n  const memoizePreviousSnapshot = useCallback$1(getState => {\n    let prevState;\n    return () => {\n      var _prevState, _prevState2;\n\n      const nextState = getState();\n\n      if ((_prevState = prevState) !== null && _prevState !== void 0 && _prevState.loadable.is(nextState.loadable) && ((_prevState2 = prevState) === null || _prevState2 === void 0 ? void 0 : _prevState2.key) === nextState.key) {\n        return prevState;\n      }\n\n      prevState = nextState;\n      return nextState;\n    };\n  }, []);\n  const getMemoizedSnapshot = useMemo$1(() => memoizePreviousSnapshot(getSnapshot), [getSnapshot, memoizePreviousSnapshot]);\n  const subscribe = useCallback$1(notify => {\n    const store = storeRef.current;\n    const subscription = subscribeToRecoilValue$1(store, recoilValue, notify, componentName);\n    return subscription.release;\n  }, [storeRef, recoilValue, componentName]);\n  return useSyncExternalStore$1(subscribe, getMemoizedSnapshot, // getSnapshot()\n  getMemoizedSnapshot // getServerSnapshot() for SSR support\n  ).loadable;\n}\n\nfunction useRecoilValueLoadable_MUTABLE_SOURCE(recoilValue) {\n  const storeRef = useStoreRef$2();\n  const getLoadable = useCallback$1(() => {\n    var _storeState$nextTree3;\n\n    const store = storeRef.current;\n    const storeState = store.getState();\n    const treeState = reactMode$3().early ? (_storeState$nextTree3 = storeState.nextTree) !== null && _storeState$nextTree3 !== void 0 ? _storeState$nextTree3 : storeState.currentTree : storeState.currentTree;\n    return getRecoilValueAsLoadable$2(store, recoilValue, treeState);\n  }, [storeRef, recoilValue]);\n  const getLoadableWithTesting = useCallback$1(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      recoilComponentGetRecoilValueCount_FOR_TESTING.current++;\n    }\n\n    return getLoadable();\n  }, [getLoadable]);\n  const componentName = Recoil_useComponentName();\n  const subscribe = useCallback$1((_storeState, notify) => {\n    const store = storeRef.current;\n    const subscription = subscribeToRecoilValue$1(store, recoilValue, () => {\n      if (!Recoil_gkx('recoil_suppress_rerender_in_callback')) {\n        return notify();\n      } // Only re-render if the value has changed.\n      // This will evaluate the atom/selector now as well as when the\n      // component renders, but that may help with prefetching.\n\n\n      const newLoadable = getLoadable();\n\n      if (!prevLoadableRef.current.is(newLoadable)) {\n        notify();\n      } // If the component is suspended then the effect setting prevLoadableRef\n      // will not run.  So, set the previous value here when its subscription\n      // is fired to wake it up.  We can't just rely on this, though, because\n      // this only executes when an atom/selector is dirty and the atom/selector\n      // passed to the hook can dynamically change.\n\n\n      prevLoadableRef.current = newLoadable;\n    }, componentName);\n    return subscription.release;\n  }, [storeRef, recoilValue, componentName, getLoadable]);\n  const source = useRecoilMutableSource$1();\n\n  if (source == null) {\n    throw Recoil_err('Recoil hooks must be used in components contained within a <RecoilRoot> component.');\n  }\n\n  const loadable = useMutableSource$1(source, getLoadableWithTesting, subscribe);\n  const prevLoadableRef = useRef$4(loadable);\n  useEffect$3(() => {\n    prevLoadableRef.current = loadable;\n  });\n  return loadable;\n}\n\nfunction useRecoilValueLoadable_TRANSITION_SUPPORT(recoilValue) {\n  const storeRef = useStoreRef$2();\n  const componentName = Recoil_useComponentName(); // Accessors to get the current state\n\n  const getLoadable = useCallback$1(() => {\n    var _storeState$nextTree4;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      recoilComponentGetRecoilValueCount_FOR_TESTING.current++;\n    }\n\n    const store = storeRef.current;\n    const storeState = store.getState();\n    const treeState = reactMode$3().early ? (_storeState$nextTree4 = storeState.nextTree) !== null && _storeState$nextTree4 !== void 0 ? _storeState$nextTree4 : storeState.currentTree : storeState.currentTree;\n    return getRecoilValueAsLoadable$2(store, recoilValue, treeState);\n  }, [storeRef, recoilValue]);\n  const getState = useCallback$1(() => ({\n    loadable: getLoadable(),\n    key: recoilValue.key\n  }), [getLoadable, recoilValue.key]); // Memoize state snapshots\n\n  const updateState = useCallback$1(prevState => {\n    const nextState = getState();\n    return prevState.loadable.is(nextState.loadable) && prevState.key === nextState.key ? prevState : nextState;\n  }, [getState]); // Subscribe to Recoil state changes\n\n  useEffect$3(() => {\n    const subscription = subscribeToRecoilValue$1(storeRef.current, recoilValue, _state => {\n      setState(updateState);\n    }, componentName); // Update state in case we are using a different key\n\n    setState(updateState);\n    return subscription.release;\n  }, [componentName, recoilValue, storeRef, updateState]); // Get the current state\n\n  const [state, setState] = useState$1(getState); // If we changed keys, then return the state for the new key.\n  // This is important in case the old key would cause the component to suspend.\n  // We don't have to set the new state here since the subscribing effect above\n  // will do that.\n\n  return state.key !== recoilValue.key ? getState().loadable : state.loadable;\n}\n\nfunction useRecoilValueLoadable_LEGACY(recoilValue) {\n  const storeRef = useStoreRef$2(); // eslint-disable-next-line fb-www/react-no-unused-state-hook\n\n  const [, forceUpdate] = useState$1([]);\n  const componentName = Recoil_useComponentName();\n  const getLoadable = useCallback$1(() => {\n    var _storeState$nextTree5;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      recoilComponentGetRecoilValueCount_FOR_TESTING.current++;\n    }\n\n    const store = storeRef.current;\n    const storeState = store.getState();\n    const treeState = reactMode$3().early ? (_storeState$nextTree5 = storeState.nextTree) !== null && _storeState$nextTree5 !== void 0 ? _storeState$nextTree5 : storeState.currentTree : storeState.currentTree;\n    return getRecoilValueAsLoadable$2(store, recoilValue, treeState);\n  }, [storeRef, recoilValue]);\n  const loadable = getLoadable();\n  const prevLoadableRef = useRef$4(loadable);\n  useEffect$3(() => {\n    prevLoadableRef.current = loadable;\n  });\n  useEffect$3(() => {\n    const store = storeRef.current;\n    const storeState = store.getState();\n    const subscription = subscribeToRecoilValue$1(store, recoilValue, _state => {\n      var _prevLoadableRef$curr;\n\n      if (!Recoil_gkx('recoil_suppress_rerender_in_callback')) {\n        return forceUpdate([]);\n      }\n\n      const newLoadable = getLoadable();\n\n      if (!((_prevLoadableRef$curr = prevLoadableRef.current) !== null && _prevLoadableRef$curr !== void 0 && _prevLoadableRef$curr.is(newLoadable))) {\n        // $FlowFixMe[incompatible-call]\n        forceUpdate(newLoadable);\n      }\n\n      prevLoadableRef.current = newLoadable;\n    }, componentName);\n    /**\n     * Since we're subscribing in an effect we need to update to the latest\n     * value of the atom since it may have changed since we rendered. We can\n     * go ahead and do that now, unless we're in the middle of a batch --\n     * in which case we should do it at the end of the batch, due to the\n     * following edge case: Suppose an atom is updated in another useEffect\n     * of this same component. Then the following sequence of events occur:\n     * 1. Atom is updated and subs fired (but we may not be subscribed\n     *    yet depending on order of effects, so we miss this) Updated value\n     *    is now in nextTree, but not currentTree.\n     * 2. This effect happens. We subscribe and update.\n     * 3. From the update we re-render and read currentTree, with old value.\n     * 4. Batcher's effect sets currentTree to nextTree.\n     * In this sequence we miss the update. To avoid that, add the update\n     * to queuedComponentCallback if a batch is in progress.\n     */\n\n    if (storeState.nextTree) {\n      store.getState().queuedComponentCallbacks_DEPRECATED.push(() => {\n        // $FlowFixMe[incompatible-type]\n        prevLoadableRef.current = null;\n        forceUpdate([]);\n      });\n    } else {\n      var _prevLoadableRef$curr2;\n\n      if (!Recoil_gkx('recoil_suppress_rerender_in_callback')) {\n        return forceUpdate([]);\n      }\n\n      const newLoadable = getLoadable();\n\n      if (!((_prevLoadableRef$curr2 = prevLoadableRef.current) !== null && _prevLoadableRef$curr2 !== void 0 && _prevLoadableRef$curr2.is(newLoadable))) {\n        // $FlowFixMe[incompatible-call]\n        forceUpdate(newLoadable);\n      }\n\n      prevLoadableRef.current = newLoadable;\n    }\n\n    return subscription.release;\n  }, [componentName, getLoadable, recoilValue, storeRef]);\n  return loadable;\n}\n/**\n  Like useRecoilValue(), but either returns the value if available or\n  just undefined if not available for any reason, such as pending or error.\n*/\n\n\nfunction useRecoilValueLoadable(recoilValue) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilValue, 'useRecoilValueLoadable');\n  }\n\n  if (Recoil_gkx('recoil_memory_managament_2020')) {\n    // eslint-disable-next-line fb-www/react-hooks\n    Recoil_useRetain(recoilValue);\n  }\n\n  return {\n    TRANSITION_SUPPORT: useRecoilValueLoadable_TRANSITION_SUPPORT,\n    // Recoil will attemp to detect if `useSyncExternalStore()` is supported with\n    // `reactMode()` before calling it.  However, sometimes the host React\n    // environment supports it but uses additional React renderers (such as with\n    // `react-three-fiber`) which do not.  While this is technically a user issue\n    // by using a renderer with React 18+ that doesn't fully support React 18 we\n    // don't want to break users if it can be avoided. As the current renderer can\n    // change at runtime, we need to dynamically check and fallback if necessary.\n    SYNC_EXTERNAL_STORE: currentRendererSupportsUseSyncExternalStore$1() ? useRecoilValueLoadable_SYNC_EXTERNAL_STORE : useRecoilValueLoadable_TRANSITION_SUPPORT,\n    MUTABLE_SOURCE: useRecoilValueLoadable_MUTABLE_SOURCE,\n    LEGACY: useRecoilValueLoadable_LEGACY\n  }[reactMode$3().mode](recoilValue);\n}\n/**\n  Returns the value represented by the RecoilValue.\n  If the value is pending, it will throw a Promise to suspend the component,\n  if the value is an error it will throw it for the nearest React error boundary.\n  This will also subscribe the component for any updates in the value.\n  */\n\n\nfunction useRecoilValue(recoilValue) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilValue, 'useRecoilValue');\n  }\n\n  const storeRef = useStoreRef$2();\n  const loadable = useRecoilValueLoadable(recoilValue);\n  return handleLoadable(loadable, recoilValue, storeRef);\n}\n/**\n  Returns a function that allows the value of a RecoilState to be updated, but does\n  not subscribe the component to changes to that RecoilState.\n*/\n\n\nfunction useSetRecoilState(recoilState) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilState, 'useSetRecoilState');\n  }\n\n  const storeRef = useStoreRef$2();\n  return useCallback$1(newValueOrUpdater => {\n    setRecoilValue$2(storeRef.current, recoilState, newValueOrUpdater);\n  }, [storeRef, recoilState]);\n}\n/**\n  Returns a function that will reset the value of a RecoilState to its default\n*/\n\n\nfunction useResetRecoilState(recoilState) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilState, 'useResetRecoilState');\n  }\n\n  const storeRef = useStoreRef$2();\n  return useCallback$1(() => {\n    setRecoilValue$2(storeRef.current, recoilState, DEFAULT_VALUE$2);\n  }, [storeRef, recoilState]);\n}\n/**\n  Equivalent to useState(). Allows the value of the RecoilState to be read and written.\n  Subsequent updates to the RecoilState will cause the component to re-render. If the\n  RecoilState is pending, this will suspend the component and initiate the\n  retrieval of the value. If evaluating the RecoilState resulted in an error, this will\n  throw the error so that the nearest React error boundary can catch it.\n*/\n\n\nfunction useRecoilState(recoilState) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilState, 'useRecoilState');\n  }\n\n  return [useRecoilValue(recoilState), useSetRecoilState(recoilState)];\n}\n/**\n  Like useRecoilState(), but does not cause Suspense or React error handling. Returns\n  an object that indicates whether the RecoilState is available, pending, or\n  unavailable due to an error.\n*/\n\n\nfunction useRecoilStateLoadable(recoilState) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilState, 'useRecoilStateLoadable');\n  }\n\n  return [useRecoilValueLoadable(recoilState), useSetRecoilState(recoilState)];\n}\n\nfunction useSetUnvalidatedAtomValues() {\n  const storeRef = useStoreRef$2();\n  return (values, transactionMetadata = {}) => {\n    batchUpdates$2(() => {\n      storeRef.current.addTransactionMetadata(transactionMetadata);\n      values.forEach((value, key) => setUnvalidatedRecoilValue$2(storeRef.current, new AbstractRecoilValue$3(key), value));\n    });\n  };\n}\n/**\n * Experimental variants of hooks with support for useTransition()\n */\n\n\nfunction useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE(recoilValue) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilValue, 'useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE');\n\n    if (!reactMode$3().early) {\n      Recoil_recoverableViolation('Attepmt to use a hook with UNSTABLE_TRANSITION_SUPPORT in a rendering mode incompatible with concurrent rendering.  Try enabling the recoil_sync_external_store or recoil_transition_support GKs.');\n    }\n  }\n\n  if (Recoil_gkx('recoil_memory_managament_2020')) {\n    // eslint-disable-next-line fb-www/react-hooks\n    Recoil_useRetain(recoilValue);\n  }\n\n  return useRecoilValueLoadable_TRANSITION_SUPPORT(recoilValue);\n}\n\nfunction useRecoilValue_TRANSITION_SUPPORT_UNSTABLE(recoilValue) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilValue, 'useRecoilValue_TRANSITION_SUPPORT_UNSTABLE');\n  }\n\n  const storeRef = useStoreRef$2();\n  const loadable = useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE(recoilValue);\n  return handleLoadable(loadable, recoilValue, storeRef);\n}\n\nfunction useRecoilState_TRANSITION_SUPPORT_UNSTABLE(recoilState) {\n  if (process.env.NODE_ENV !== \"production\") {\n    validateRecoilValue(recoilState, 'useRecoilState_TRANSITION_SUPPORT_UNSTABLE');\n  }\n\n  return [useRecoilValue_TRANSITION_SUPPORT_UNSTABLE(recoilState), useSetRecoilState(recoilState)];\n}\n\nvar Recoil_Hooks = {\n  recoilComponentGetRecoilValueCount_FOR_TESTING,\n  useRecoilInterface: useRecoilInterface_DEPRECATED,\n  useRecoilState,\n  useRecoilStateLoadable,\n  useRecoilValue,\n  useRecoilValueLoadable,\n  useResetRecoilState,\n  useSetRecoilState,\n  useSetUnvalidatedAtomValues,\n  useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE,\n  useRecoilValue_TRANSITION_SUPPORT_UNSTABLE,\n  useRecoilState_TRANSITION_SUPPORT_UNSTABLE\n};\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Returns a map containing all of the keys + values from the original map where\n * the given callback returned true.\n */\n\nfunction filterMap(map, callback) {\n  const result = new Map();\n\n  for (const [key, value] of map) {\n    if (callback(value, key)) {\n      result.set(key, value);\n    }\n  }\n\n  return result;\n}\n\nvar Recoil_filterMap = filterMap;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n/**\n * Returns a set containing all of the values from the original set where\n * the given callback returned true.\n */\n\nfunction filterSet(set, callback) {\n  const result = new Set();\n\n  for (const value of set) {\n    if (callback(value)) {\n      result.add(value);\n    }\n  }\n\n  return result;\n}\n\nvar Recoil_filterSet = filterSet;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction mergeMaps(...maps) {\n  const result = new Map();\n\n  for (let i = 0; i < maps.length; i++) {\n    const iterator = maps[i].keys();\n    let nextKey;\n\n    while (!(nextKey = iterator.next()).done) {\n      // $FlowIssue[incompatible-call] - map/iterator knows nothing about flow types\n      result.set(nextKey.value, maps[i].get(nextKey.value));\n    }\n  }\n\n  return result;\n}\n\nvar Recoil_mergeMaps = mergeMaps;\n\nconst {\n  batchUpdates: batchUpdates$3\n} = Recoil_Batching;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$3,\n  getNode: getNode$4,\n  nodes: nodes$1\n} = Recoil_Node;\n\nconst {\n  useStoreRef: useStoreRef$3\n} = Recoil_RecoilRoot;\n\nconst {\n  AbstractRecoilValue: AbstractRecoilValue$4,\n  setRecoilValueLoadable: setRecoilValueLoadable$1\n} = Recoil_RecoilValueInterface;\n\nconst {\n  SUSPENSE_TIMEOUT_MS: SUSPENSE_TIMEOUT_MS$2\n} = Recoil_Retention;\n\nconst {\n  cloneSnapshot: cloneSnapshot$1\n} = Recoil_Snapshot$1;\n\nconst {\n  useCallback: useCallback$2,\n  useEffect: useEffect$4,\n  useRef: useRef$5,\n  useState: useState$2\n} = react;\n\nconst {\n  isSSR: isSSR$4\n} = Recoil_Environment;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useTransactionSubscription(callback) {\n  const storeRef = useStoreRef$3();\n  useEffect$4(() => {\n    const sub = storeRef.current.subscribeToTransactions(callback);\n    return sub.release;\n  }, [callback, storeRef]);\n}\n\nfunction externallyVisibleAtomValuesInState(state) {\n  const atomValues = state.atomValues.toMap();\n  const persistedAtomContentsValues = Recoil_mapMap(Recoil_filterMap(atomValues, (v, k) => {\n    const node = getNode$4(k);\n    const persistence = node.persistence_UNSTABLE;\n    return persistence != null && persistence.type !== 'none' && v.state === 'hasValue';\n  }), v => v.contents); // Merge in nonvalidated atoms; we may not have defs for them but they will\n  // all have persistence on or they wouldn't be there in the first place.\n\n  return Recoil_mergeMaps(state.nonvalidatedAtoms.toMap(), persistedAtomContentsValues);\n}\n\n/**\n  Calls the given callback after any atoms have been modified and the consequent\n  component re-renders have been committed. This is intended for persisting\n  the values of the atoms to storage. The stored values can then be restored\n  using the useSetUnvalidatedAtomValues hook.\n\n  The callback receives the following info:\n\n  atomValues: The current value of every atom that is both persistable (persistence\n              type not set to 'none') and whose value is available (not in an\n              error or loading state).\n\n  previousAtomValues: The value of every persistable and available atom before\n               the transaction began.\n\n  atomInfo: A map containing the persistence settings for each atom. Every key\n            that exists in atomValues will also exist in atomInfo.\n\n  modifiedAtoms: The set of atoms that were written to during the transaction.\n\n  transactionMetadata: Arbitrary information that was added via the\n          useSetUnvalidatedAtomValues hook. Useful for ignoring the useSetUnvalidatedAtomValues\n          transaction, to avoid loops.\n*/\nfunction useTransactionObservation_DEPRECATED(callback) {\n  useTransactionSubscription(useCallback$2(store => {\n    let previousTree = store.getState().previousTree;\n    const currentTree = store.getState().currentTree;\n\n    if (!previousTree) {\n      Recoil_recoverableViolation('Transaction subscribers notified without a previous tree being present -- this is a bug in Recoil');\n      previousTree = store.getState().currentTree; // attempt to trundle on\n    }\n\n    const atomValues = externallyVisibleAtomValuesInState(currentTree);\n    const previousAtomValues = externallyVisibleAtomValuesInState(previousTree);\n    const atomInfo = Recoil_mapMap(nodes$1, node => {\n      var _node$persistence_UNS, _node$persistence_UNS2, _node$persistence_UNS3, _node$persistence_UNS4;\n\n      return {\n        persistence_UNSTABLE: {\n          type: (_node$persistence_UNS = (_node$persistence_UNS2 = node.persistence_UNSTABLE) === null || _node$persistence_UNS2 === void 0 ? void 0 : _node$persistence_UNS2.type) !== null && _node$persistence_UNS !== void 0 ? _node$persistence_UNS : 'none',\n          backButton: (_node$persistence_UNS3 = (_node$persistence_UNS4 = node.persistence_UNSTABLE) === null || _node$persistence_UNS4 === void 0 ? void 0 : _node$persistence_UNS4.backButton) !== null && _node$persistence_UNS3 !== void 0 ? _node$persistence_UNS3 : false\n        }\n      };\n    }); // Filter on existance in atomValues so that externally-visible rules\n    // are also applied to modified atoms (specifically exclude selectors):\n\n    const modifiedAtoms = Recoil_filterSet(currentTree.dirtyAtoms, k => atomValues.has(k) || previousAtomValues.has(k));\n    callback({\n      atomValues,\n      previousAtomValues,\n      atomInfo,\n      modifiedAtoms,\n      transactionMetadata: { ...currentTree.transactionMetadata\n      }\n    });\n  }, [callback]));\n}\n\nfunction useRecoilTransactionObserver(callback) {\n  useTransactionSubscription(useCallback$2(store => {\n    const snapshot = cloneSnapshot$1(store, 'latest');\n    const previousSnapshot = cloneSnapshot$1(store, 'previous');\n    callback({\n      snapshot,\n      previousSnapshot\n    });\n  }, [callback]));\n} // Return a snapshot of the current state and subscribe to all state changes\n\n\nfunction useRecoilSnapshot() {\n  const storeRef = useStoreRef$3();\n  const [snapshot, setSnapshot] = useState$2(() => cloneSnapshot$1(storeRef.current));\n  const previousSnapshot = Recoil_usePrevious(snapshot);\n  const timeoutID = useRef$5();\n  const releaseRef = useRef$5();\n  useTransactionSubscription(useCallback$2(store => setSnapshot(cloneSnapshot$1(store)), [])); // Retain snapshot for duration component is mounted\n\n  useEffect$4(() => {\n    const release = snapshot.retain(); // Release the retain from the rendering call\n\n    if (timeoutID.current && !isSSR$4) {\n      var _releaseRef$current;\n\n      window.clearTimeout(timeoutID.current);\n      timeoutID.current = null;\n      (_releaseRef$current = releaseRef.current) === null || _releaseRef$current === void 0 ? void 0 : _releaseRef$current.call(releaseRef);\n      releaseRef.current = null;\n    }\n\n    return () => {\n      // Defer the release.  If \"Fast Refresh\"\" is used then the component may\n      // re-render with the same state.  The previous cleanup will then run and\n      // then the new effect will run. We don't want the snapshot to be released\n      // by that cleanup before the new effect has a chance to retain it again.\n      // Use timeout of 10 to workaround Firefox issue: https://github.com/facebookexperimental/Recoil/issues/1936\n      window.setTimeout(release, 10);\n    };\n  }, [snapshot]); // Retain snapshot until above effect is run.\n  // Release after a threshold in case component is suspended.\n\n  if (previousSnapshot !== snapshot && !isSSR$4) {\n    // Release the previous snapshot\n    if (timeoutID.current) {\n      var _releaseRef$current2;\n\n      window.clearTimeout(timeoutID.current);\n      timeoutID.current = null;\n      (_releaseRef$current2 = releaseRef.current) === null || _releaseRef$current2 === void 0 ? void 0 : _releaseRef$current2.call(releaseRef);\n      releaseRef.current = null;\n    }\n\n    releaseRef.current = snapshot.retain();\n    timeoutID.current = window.setTimeout(() => {\n      var _releaseRef$current3;\n\n      timeoutID.current = null;\n      (_releaseRef$current3 = releaseRef.current) === null || _releaseRef$current3 === void 0 ? void 0 : _releaseRef$current3.call(releaseRef);\n      releaseRef.current = null;\n    }, SUSPENSE_TIMEOUT_MS$2);\n  }\n\n  return snapshot;\n}\n\nfunction gotoSnapshot(store, snapshot) {\n  var _storeState$nextTree;\n\n  const storeState = store.getState();\n  const prev = (_storeState$nextTree = storeState.nextTree) !== null && _storeState$nextTree !== void 0 ? _storeState$nextTree : storeState.currentTree;\n  const next = snapshot.getStore_INTERNAL().getState().currentTree;\n  batchUpdates$3(() => {\n    const keysToUpdate = new Set();\n\n    for (const keys of [prev.atomValues.keys(), next.atomValues.keys()]) {\n      for (const key of keys) {\n        var _prev$atomValues$get, _next$atomValues$get;\n\n        if (((_prev$atomValues$get = prev.atomValues.get(key)) === null || _prev$atomValues$get === void 0 ? void 0 : _prev$atomValues$get.contents) !== ((_next$atomValues$get = next.atomValues.get(key)) === null || _next$atomValues$get === void 0 ? void 0 : _next$atomValues$get.contents) && getNode$4(key).shouldRestoreFromSnapshots) {\n          keysToUpdate.add(key);\n        }\n      }\n    }\n\n    keysToUpdate.forEach(key => {\n      setRecoilValueLoadable$1(store, new AbstractRecoilValue$4(key), next.atomValues.has(key) ? Recoil_nullthrows(next.atomValues.get(key)) : DEFAULT_VALUE$3);\n    });\n    store.replaceState(state => ({ ...state,\n      stateID: snapshot.getID()\n    }));\n  });\n}\n\nfunction useGotoRecoilSnapshot() {\n  const storeRef = useStoreRef$3();\n  return useCallback$2(snapshot => gotoSnapshot(storeRef.current, snapshot), [storeRef]);\n}\n\nvar Recoil_SnapshotHooks = {\n  useRecoilSnapshot,\n  gotoSnapshot,\n  useGotoRecoilSnapshot,\n  useRecoilTransactionObserver,\n  useTransactionObservation_DEPRECATED,\n  useTransactionSubscription_DEPRECATED: useTransactionSubscription\n};\n\nconst {\n  peekNodeInfo: peekNodeInfo$2\n} = Recoil_FunctionalCore;\n\nconst {\n  useStoreRef: useStoreRef$4\n} = Recoil_RecoilRoot;\n\nfunction useGetRecoilValueInfo() {\n  const storeRef = useStoreRef$4(); // $FlowFixMe[incompatible-return]\n\n  return ({\n    key\n  }) => peekNodeInfo$2(storeRef.current, storeRef.current.getState().currentTree, key);\n}\n\nvar Recoil_useGetRecoilValueInfo = useGetRecoilValueInfo;\n\nconst {\n  reactMode: reactMode$4\n} = Recoil_ReactMode;\n\nconst {\n  RecoilRoot: RecoilRoot$1,\n  useStoreRef: useStoreRef$5\n} = Recoil_RecoilRoot;\n\n\n\nconst {\n  useMemo: useMemo$2\n} = react;\n\nfunction useRecoilBridgeAcrossReactRoots() {\n  // The test fails when using useMutableSource(), but only if act() is used\n  // for the nested root.  So, this may only be a testing environment issue.\n  if (reactMode$4().mode === 'MUTABLE_SOURCE') {\n    // eslint-disable-next-line fb-www/no-console\n    console.warn('Warning: There are known issues using useRecoilBridgeAcrossReactRoots() in recoil_mutable_source rendering mode.  Please consider upgrading to recoil_sync_external_store mode.');\n  }\n\n  const store = useStoreRef$5().current;\n  return useMemo$2(() => {\n    // eslint-disable-next-line no-shadow\n    function RecoilBridge({\n      children\n    }) {\n      return /*#__PURE__*/react.createElement(RecoilRoot$1, {\n        store_INTERNAL: store\n      }, children);\n    }\n\n    return RecoilBridge;\n  }, [store]);\n}\n\nvar Recoil_useRecoilBridgeAcrossReactRoots = useRecoilBridgeAcrossReactRoots;\n\nconst {\n  loadableWithValue: loadableWithValue$1\n} = Recoil_Loadable$1;\n\nconst {\n  initializeNode: initializeNode$3\n} = Recoil_FunctionalCore;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$4,\n  getNode: getNode$5\n} = Recoil_Node;\n\nconst {\n  copyTreeState: copyTreeState$1,\n  getRecoilValueAsLoadable: getRecoilValueAsLoadable$3,\n  invalidateDownstreams: invalidateDownstreams$1,\n  writeLoadableToTreeState: writeLoadableToTreeState$1\n} = Recoil_RecoilValueInterface;\n\n\n\nfunction isAtom(recoilValue) {\n  return getNode$5(recoilValue.key).nodeType === 'atom';\n}\n\nclass TransactionInterfaceImpl {\n  constructor(store, treeState) {\n    _defineProperty(this, \"_store\", void 0);\n\n    _defineProperty(this, \"_treeState\", void 0);\n\n    _defineProperty(this, \"_changes\", void 0);\n\n    _defineProperty(this, \"get\", recoilValue => {\n      if (this._changes.has(recoilValue.key)) {\n        // $FlowIssue[incompatible-return]\n        return this._changes.get(recoilValue.key);\n      }\n\n      if (!isAtom(recoilValue)) {\n        throw Recoil_err('Reading selectors within atomicUpdate is not supported');\n      }\n\n      const loadable = getRecoilValueAsLoadable$3(this._store, recoilValue, this._treeState);\n\n      if (loadable.state === 'hasValue') {\n        return loadable.contents;\n      } else if (loadable.state === 'hasError') {\n        throw loadable.contents;\n      } else {\n        throw Recoil_err(`Expected Recoil atom ${recoilValue.key} to have a value, but it is in a loading state.`);\n      }\n    });\n\n    _defineProperty(this, \"set\", (recoilState, valueOrUpdater) => {\n      if (!isAtom(recoilState)) {\n        throw Recoil_err('Setting selectors within atomicUpdate is not supported');\n      }\n\n      if (typeof valueOrUpdater === 'function') {\n        const current = this.get(recoilState);\n\n        this._changes.set(recoilState.key, valueOrUpdater(current)); // flowlint-line unclear-type:off\n\n      } else {\n        // Initialize atom and run effects if not initialized yet\n        initializeNode$3(this._store, recoilState.key, 'set');\n\n        this._changes.set(recoilState.key, valueOrUpdater);\n      }\n    });\n\n    _defineProperty(this, \"reset\", recoilState => {\n      this.set(recoilState, DEFAULT_VALUE$4);\n    });\n\n    this._store = store;\n    this._treeState = treeState;\n    this._changes = new Map();\n  } // Allow destructing\n  // eslint-disable-next-line fb-www/extra-arrow-initializer\n\n\n  newTreeState_INTERNAL() {\n    if (this._changes.size === 0) {\n      return this._treeState;\n    }\n\n    const newState = copyTreeState$1(this._treeState);\n\n    for (const [k, v] of this._changes) {\n      writeLoadableToTreeState$1(newState, k, loadableWithValue$1(v));\n    }\n\n    invalidateDownstreams$1(this._store, newState);\n    return newState;\n  }\n\n}\n\nfunction atomicUpdater(store) {\n  return fn => {\n    store.replaceState(treeState => {\n      const changeset = new TransactionInterfaceImpl(store, treeState);\n      fn(changeset);\n      return changeset.newTreeState_INTERNAL();\n    });\n  };\n}\n\nvar Recoil_AtomicUpdates = {\n  atomicUpdater\n};\n\nvar Recoil_AtomicUpdates_1 = Recoil_AtomicUpdates.atomicUpdater;\n\nvar Recoil_AtomicUpdates$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  atomicUpdater: Recoil_AtomicUpdates_1\n});\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction invariant(condition, message) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}\n\nvar invariant_1 = invariant;\n\n// @oss-only\n\n\nvar Recoil_invariant = invariant_1;\n\nconst {\n  atomicUpdater: atomicUpdater$1\n} = Recoil_AtomicUpdates$1;\n\nconst {\n  batchUpdates: batchUpdates$4\n} = Recoil_Batching;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$5\n} = Recoil_Node;\n\nconst {\n  useStoreRef: useStoreRef$6\n} = Recoil_RecoilRoot;\n\nconst {\n  refreshRecoilValue: refreshRecoilValue$1,\n  setRecoilValue: setRecoilValue$3\n} = Recoil_RecoilValueInterface;\n\nconst {\n  cloneSnapshot: cloneSnapshot$2\n} = Recoil_Snapshot$1;\n\nconst {\n  gotoSnapshot: gotoSnapshot$1\n} = Recoil_SnapshotHooks;\n\nconst {\n  useCallback: useCallback$3\n} = react;\n\n\n\n\n\n\n\n\n\nclass Sentinel {}\n\nconst SENTINEL = new Sentinel();\n\nfunction recoilCallback(store, fn, args, extraInterface) {\n  let ret = SENTINEL;\n  let releaseSnapshot;\n  batchUpdates$4(() => {\n    const errMsg = 'useRecoilCallback() expects a function that returns a function: ' + 'it accepts a function of the type (RecoilInterface) => (Args) => ReturnType ' + 'and returns a callback function (Args) => ReturnType, where RecoilInterface is ' + 'an object {snapshot, set, ...} and Args and ReturnType are the argument and return ' + 'types of the callback you want to create.  Please see the docs ' + 'at recoiljs.org for details.';\n\n    if (typeof fn !== 'function') {\n      throw Recoil_err(errMsg);\n    } // Clone the snapshot lazily to avoid overhead if the callback does not use it.\n    // Note that this means the snapshot may represent later state from when\n    // the callback was called if it first accesses the snapshot asynchronously.\n\n\n    const callbackInterface = Recoil_lazyProxy({ ...(extraInterface !== null && extraInterface !== void 0 ? extraInterface : {}),\n      // flowlint-line unclear-type:off\n      // $FlowFixMe[missing-local-annot]\n      set: (node, newValue) => setRecoilValue$3(store, node, newValue),\n      // $FlowFixMe[missing-local-annot]\n      reset: node => setRecoilValue$3(store, node, DEFAULT_VALUE$5),\n      // $FlowFixMe[missing-local-annot]\n      refresh: node => refreshRecoilValue$1(store, node),\n      gotoSnapshot: snapshot => gotoSnapshot$1(store, snapshot),\n      transact_UNSTABLE: transaction => atomicUpdater$1(store)(transaction)\n    }, {\n      snapshot: () => {\n        const snapshot = cloneSnapshot$2(store);\n        releaseSnapshot = snapshot.retain();\n        return snapshot;\n      }\n    });\n    const callback = fn(callbackInterface);\n\n    if (typeof callback !== 'function') {\n      throw Recoil_err(errMsg);\n    }\n\n    ret = callback(...args);\n  });\n  !!(ret instanceof Sentinel) ? process.env.NODE_ENV !== \"production\" ? Recoil_invariant(false, 'batchUpdates should return immediately') : Recoil_invariant(false) : void 0;\n\n  if (Recoil_isPromise(ret)) {\n    ret = ret.finally(() => {\n      var _releaseSnapshot;\n\n      (_releaseSnapshot = releaseSnapshot) === null || _releaseSnapshot === void 0 ? void 0 : _releaseSnapshot();\n    });\n  } else {\n    var _releaseSnapshot2;\n\n    (_releaseSnapshot2 = releaseSnapshot) === null || _releaseSnapshot2 === void 0 ? void 0 : _releaseSnapshot2();\n  }\n\n  return ret;\n}\n\nfunction useRecoilCallback(fn, deps) {\n  const storeRef = useStoreRef$6();\n  return useCallback$3( // $FlowIssue[incompatible-call]\n  (...args) => {\n    return recoilCallback(storeRef.current, fn, args);\n  }, deps != null ? [...deps, storeRef] : undefined // eslint-disable-line fb-www/react-hooks-deps\n  );\n}\n\nvar Recoil_useRecoilCallback = {\n  recoilCallback,\n  useRecoilCallback\n};\n\nconst {\n  useStoreRef: useStoreRef$7\n} = Recoil_RecoilRoot;\n\nconst {\n  refreshRecoilValue: refreshRecoilValue$2\n} = Recoil_RecoilValueInterface;\n\nconst {\n  useCallback: useCallback$4\n} = react;\n\nfunction useRecoilRefresher(recoilValue) {\n  const storeRef = useStoreRef$7();\n  return useCallback$4(() => {\n    const store = storeRef.current;\n    refreshRecoilValue$2(store, recoilValue);\n  }, [recoilValue, storeRef]);\n}\n\nvar Recoil_useRecoilRefresher = useRecoilRefresher;\n\nconst {\n  atomicUpdater: atomicUpdater$2\n} = Recoil_AtomicUpdates$1;\n\nconst {\n  useStoreRef: useStoreRef$8\n} = Recoil_RecoilRoot;\n\nconst {\n  useMemo: useMemo$3\n} = react;\n\nfunction useRecoilTransaction(fn, deps) {\n  const storeRef = useStoreRef$8();\n  return useMemo$3(() => (...args) => {\n    const atomicUpdate = atomicUpdater$2(storeRef.current);\n    atomicUpdate(transactionInterface => {\n      fn(transactionInterface)(...args);\n    });\n  }, deps != null ? [...deps, storeRef] : undefined // eslint-disable-line fb-www/react-hooks-deps\n  );\n}\n\nvar Recoil_useRecoilTransaction = useRecoilTransaction;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nclass WrappedValue {\n  constructor(value) {\n    _defineProperty(this, \"value\", void 0);\n\n    this.value = value;\n  }\n\n}\n\nvar Recoil_Wrapper = {\n  WrappedValue\n};\n\nvar Recoil_Wrapper_1 = Recoil_Wrapper.WrappedValue;\n\nvar Recoil_Wrapper$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  WrappedValue: Recoil_Wrapper_1\n});\n\nconst {\n  isFastRefreshEnabled: isFastRefreshEnabled$2\n} = Recoil_ReactMode;\n\n\n\nclass ChangedPathError extends Error {}\n\nclass TreeCache {\n  // $FlowIssue[unclear-type]\n  constructor(options) {\n    var _options$onHit, _options$onSet, _options$mapNodeValue;\n\n    _defineProperty(this, \"_name\", void 0);\n\n    _defineProperty(this, \"_numLeafs\", void 0);\n\n    _defineProperty(this, \"_root\", void 0);\n\n    _defineProperty(this, \"_onHit\", void 0);\n\n    _defineProperty(this, \"_onSet\", void 0);\n\n    _defineProperty(this, \"_mapNodeValue\", void 0);\n\n    this._name = options === null || options === void 0 ? void 0 : options.name;\n    this._numLeafs = 0;\n    this._root = null;\n    this._onHit = (_options$onHit = options === null || options === void 0 ? void 0 : options.onHit) !== null && _options$onHit !== void 0 ? _options$onHit : () => {};\n    this._onSet = (_options$onSet = options === null || options === void 0 ? void 0 : options.onSet) !== null && _options$onSet !== void 0 ? _options$onSet : () => {};\n    this._mapNodeValue = (_options$mapNodeValue = options === null || options === void 0 ? void 0 : options.mapNodeValue) !== null && _options$mapNodeValue !== void 0 ? _options$mapNodeValue : val => val;\n  }\n\n  size() {\n    return this._numLeafs;\n  } // $FlowIssue[unclear-type]\n\n\n  root() {\n    return this._root;\n  }\n\n  get(getNodeValue, handlers) {\n    var _this$getLeafNode;\n\n    return (_this$getLeafNode = this.getLeafNode(getNodeValue, handlers)) === null || _this$getLeafNode === void 0 ? void 0 : _this$getLeafNode.value;\n  }\n\n  getLeafNode(getNodeValue, handlers) {\n    if (this._root == null) {\n      return undefined;\n    } // Iterate down the tree based on the current node values until we hit a leaf\n    // $FlowIssue[unclear-type]\n\n\n    let node = this._root;\n\n    while (node) {\n      handlers === null || handlers === void 0 ? void 0 : handlers.onNodeVisit(node);\n\n      if (node.type === 'leaf') {\n        this._onHit(node);\n\n        return node;\n      }\n\n      const nodeValue = this._mapNodeValue(getNodeValue(node.nodeKey));\n\n      node = node.branches.get(nodeValue);\n    }\n\n    return undefined;\n  }\n\n  set(route, value, handlers) {\n    const addLeaf = () => {\n      var _node2, _node3, _this$_root2, _handlers$onNodeVisit2;\n\n      // First, setup the branch nodes for the route:\n      // Iterate down the tree to find or add branch nodes following the route\n      let node;\n      let branchKey;\n\n      for (const [nodeKey, nodeValue] of route) {\n        var _node, _handlers$onNodeVisit, _this$_root;\n\n        // If the previous root was a leaf, while we not have a get(), it means\n        // the selector has inconsistent values or implementation changed.\n        const root = this._root;\n\n        if ((root === null || root === void 0 ? void 0 : root.type) === 'leaf') {\n          throw this.invalidCacheError();\n        } // node now refers to the next node down in the tree\n\n\n        const parent = node; // $FlowFixMe[prop-missing]\n        // $FlowFixMe[incompatible-type]\n\n        node = parent ? parent.branches.get(branchKey) : root; // $FlowFixMe[prop-missing]\n        // $FlowFixMe[incompatible-type]\n\n        node = (_node = node) !== null && _node !== void 0 ? _node : {\n          type: 'branch',\n          nodeKey,\n          parent,\n          branches: new Map(),\n          branchKey\n        }; // If we found an existing node, confirm it has a consistent value\n\n        if (node.type !== 'branch' || node.nodeKey !== nodeKey) {\n          throw this.invalidCacheError();\n        } // Add the branch node to the tree\n\n\n        parent === null || parent === void 0 ? void 0 : parent.branches.set(branchKey, node);\n        handlers === null || handlers === void 0 ? void 0 : (_handlers$onNodeVisit = handlers.onNodeVisit) === null || _handlers$onNodeVisit === void 0 ? void 0 : _handlers$onNodeVisit.call(handlers, node); // Prepare for next iteration and install root if it is new.\n\n        branchKey = this._mapNodeValue(nodeValue);\n        this._root = (_this$_root = this._root) !== null && _this$_root !== void 0 ? _this$_root : node;\n      } // Second, setup the leaf node:\n      // If there is an existing leaf for this route confirm it is consistent\n\n\n      const oldLeaf = node ? (_node2 = node) === null || _node2 === void 0 ? void 0 : _node2.branches.get(branchKey) : this._root;\n\n      if (oldLeaf != null && (oldLeaf.type !== 'leaf' || oldLeaf.branchKey !== branchKey)) {\n        throw this.invalidCacheError();\n      } // Create a new or replacement leaf.\n\n\n      const leafNode = {\n        type: 'leaf',\n        value,\n        parent: node,\n        branchKey\n      }; // Install the leaf and call handlers\n\n      (_node3 = node) === null || _node3 === void 0 ? void 0 : _node3.branches.set(branchKey, leafNode);\n      this._root = (_this$_root2 = this._root) !== null && _this$_root2 !== void 0 ? _this$_root2 : leafNode;\n      this._numLeafs++;\n\n      this._onSet(leafNode);\n\n      handlers === null || handlers === void 0 ? void 0 : (_handlers$onNodeVisit2 = handlers.onNodeVisit) === null || _handlers$onNodeVisit2 === void 0 ? void 0 : _handlers$onNodeVisit2.call(handlers, leafNode);\n    };\n\n    try {\n      addLeaf();\n    } catch (error) {\n      // If the cache was stale or observed inconsistent values, such as with\n      // Fast Refresh, then clear it and rebuild with the new values.\n      if (error instanceof ChangedPathError) {\n        this.clear();\n        addLeaf();\n      } else {\n        throw error;\n      }\n    }\n  } // Returns true if leaf was actually deleted from the tree\n\n\n  delete(leaf) {\n    const root = this.root();\n\n    if (!root) {\n      return false;\n    }\n\n    if (leaf === root) {\n      this._root = null;\n      this._numLeafs = 0;\n      return true;\n    } // Iterate up from the leaf deleteing it from it's parent's branches.\n\n\n    let node = leaf.parent;\n    let branchKey = leaf.branchKey;\n\n    while (node) {\n      var _node4;\n\n      node.branches.delete(branchKey); // Stop iterating if we hit the root.\n\n      if (node === root) {\n        if (node.branches.size === 0) {\n          this._root = null;\n          this._numLeafs = 0;\n        } else {\n          this._numLeafs--;\n        }\n\n        return true;\n      } // Stop iterating if there are other branches since we don't need to\n      // remove any more nodes.\n\n\n      if (node.branches.size > 0) {\n        break;\n      } // Iterate up to our parent\n\n\n      branchKey = (_node4 = node) === null || _node4 === void 0 ? void 0 : _node4.branchKey;\n      node = node.parent;\n    } // Confirm that the leaf we are deleting is actually attached to our tree\n\n\n    for (; node !== root; node = node.parent) {\n      if (node == null) {\n        return false;\n      }\n    }\n\n    this._numLeafs--;\n    return true;\n  }\n\n  clear() {\n    this._numLeafs = 0;\n    this._root = null;\n  }\n\n  invalidCacheError() {\n    const CHANGED_PATH_ERROR_MESSAGE = isFastRefreshEnabled$2() ? 'Possible Fast Refresh module reload detected.  ' + 'This may also be caused by an selector returning inconsistent values. ' + 'Resetting cache.' : 'Invalid cache values.  This happens when selectors do not return ' + 'consistent values for the same input dependency values.  That may also ' + 'be caused when using Fast Refresh to change a selector implementation.  ' + 'Resetting cache.';\n    Recoil_recoverableViolation(CHANGED_PATH_ERROR_MESSAGE + (this._name != null ? ` - ${this._name}` : ''));\n    throw new ChangedPathError();\n  }\n\n}\n\nvar Recoil_TreeCache = {\n  TreeCache\n};\n\nvar Recoil_TreeCache_1 = Recoil_TreeCache.TreeCache;\n\nvar Recoil_TreeCache$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  TreeCache: Recoil_TreeCache_1\n});\n\nclass LRUCache {\n  constructor(options) {\n    var _options$mapKey;\n\n    _defineProperty(this, \"_maxSize\", void 0);\n\n    _defineProperty(this, \"_size\", void 0);\n\n    _defineProperty(this, \"_head\", void 0);\n\n    _defineProperty(this, \"_tail\", void 0);\n\n    _defineProperty(this, \"_map\", void 0);\n\n    _defineProperty(this, \"_keyMapper\", void 0);\n\n    this._maxSize = options.maxSize;\n    this._size = 0;\n    this._head = null;\n    this._tail = null;\n    this._map = new Map();\n    this._keyMapper = (_options$mapKey = options.mapKey) !== null && _options$mapKey !== void 0 ? _options$mapKey : v => v;\n  }\n\n  head() {\n    return this._head;\n  }\n\n  tail() {\n    return this._tail;\n  }\n\n  size() {\n    return this._size;\n  }\n\n  maxSize() {\n    return this._maxSize;\n  }\n\n  has(key) {\n    return this._map.has(this._keyMapper(key));\n  }\n\n  get(key) {\n    const mappedKey = this._keyMapper(key);\n\n    const node = this._map.get(mappedKey);\n\n    if (!node) {\n      return undefined;\n    }\n\n    this.set(key, node.value);\n    return node.value;\n  }\n\n  set(key, val) {\n    const mappedKey = this._keyMapper(key);\n\n    const existingNode = this._map.get(mappedKey);\n\n    if (existingNode) {\n      this.delete(key);\n    }\n\n    const head = this.head();\n    const node = {\n      key,\n      right: head,\n      left: null,\n      value: val\n    };\n\n    if (head) {\n      head.left = node;\n    } else {\n      this._tail = node;\n    }\n\n    this._map.set(mappedKey, node);\n\n    this._head = node;\n    this._size++;\n\n    this._maybeDeleteLRU();\n  }\n\n  _maybeDeleteLRU() {\n    if (this.size() > this.maxSize()) {\n      this.deleteLru();\n    }\n  }\n\n  deleteLru() {\n    const tail = this.tail();\n\n    if (tail) {\n      this.delete(tail.key);\n    }\n  }\n\n  delete(key) {\n    const mappedKey = this._keyMapper(key);\n\n    if (!this._size || !this._map.has(mappedKey)) {\n      return;\n    }\n\n    const node = Recoil_nullthrows(this._map.get(mappedKey));\n    const right = node.right;\n    const left = node.left;\n\n    if (right) {\n      right.left = node.left;\n    }\n\n    if (left) {\n      left.right = node.right;\n    }\n\n    if (node === this.head()) {\n      this._head = right;\n    }\n\n    if (node === this.tail()) {\n      this._tail = left;\n    }\n\n    this._map.delete(mappedKey);\n\n    this._size--;\n  }\n\n  clear() {\n    this._size = 0;\n    this._head = null;\n    this._tail = null;\n    this._map = new Map();\n  }\n\n}\n\nvar Recoil_LRUCache = {\n  LRUCache\n};\n\nvar Recoil_LRUCache_1 = Recoil_LRUCache.LRUCache;\n\nvar Recoil_LRUCache$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  LRUCache: Recoil_LRUCache_1\n});\n\nconst {\n  LRUCache: LRUCache$1\n} = Recoil_LRUCache$1;\n\nconst {\n  TreeCache: TreeCache$1\n} = Recoil_TreeCache$1;\n\nfunction treeCacheLRU({\n  name,\n  maxSize,\n  mapNodeValue = v => v\n}) {\n  const lruCache = new LRUCache$1({\n    maxSize\n  });\n  const cache = new TreeCache$1({\n    name,\n    mapNodeValue,\n    onHit: node => {\n      lruCache.set(node, true);\n    },\n    onSet: node => {\n      const lruNode = lruCache.tail();\n      lruCache.set(node, true);\n\n      if (lruNode && cache.size() > maxSize) {\n        // $FlowFixMe[incompatible-call]\n        cache.delete(lruNode.key);\n      }\n    }\n  });\n  return cache;\n}\n\nvar Recoil_treeCacheLRU = treeCacheLRU;\n\nconst TIME_WARNING_THRESHOLD_MS = 15;\n\nfunction stringify(x, opt, key) {\n  // A optimization to avoid the more expensive JSON.stringify() for simple strings\n  // This may lose protection for u2028 and u2029, though.\n  if (typeof x === 'string' && !x.includes('\"') && !x.includes('\\\\')) {\n    return `\"${x}\"`;\n  } // Handle primitive types\n\n\n  switch (typeof x) {\n    case 'undefined':\n      return '';\n    // JSON.stringify(undefined) returns undefined, but we always want to return a string\n\n    case 'boolean':\n      return x ? 'true' : 'false';\n\n    case 'number':\n    case 'symbol':\n      // case 'bigint': // BigInt is not supported in www\n      return String(x);\n\n    case 'string':\n      // Add surrounding quotes and escape internal quotes\n      return JSON.stringify(x);\n\n    case 'function':\n      if ((opt === null || opt === void 0 ? void 0 : opt.allowFunctions) !== true) {\n        throw Recoil_err('Attempt to serialize function in a Recoil cache key');\n      }\n\n      return `__FUNCTION(${x.name})__`;\n  }\n\n  if (x === null) {\n    return 'null';\n  } // Fallback case for unknown types\n\n\n  if (typeof x !== 'object') {\n    var _JSON$stringify;\n\n    return (_JSON$stringify = JSON.stringify(x)) !== null && _JSON$stringify !== void 0 ? _JSON$stringify : '';\n  } // Deal with all promises as equivalent for now.\n\n\n  if (Recoil_isPromise(x)) {\n    return '__PROMISE__';\n  } // Arrays handle recursive stringification\n\n\n  if (Array.isArray(x)) {\n    // $FlowFixMe[missing-local-annot]\n    return `[${x.map((v, i) => stringify(v, opt, i.toString()))}]`;\n  } // If an object defines a toJSON() method, then use that to override the\n  // serialization.  This matches the behavior of JSON.stringify().\n  // Pass the key for compatibility.\n  // Immutable.js collections define this method to allow us to serialize them.\n\n\n  if (typeof x.toJSON === 'function') {\n    // flowlint-next-line unclear-type: off\n    return stringify(x.toJSON(key), opt, key);\n  } // For built-in Maps, sort the keys in a stable order instead of the\n  // default insertion order.  Support non-string keys.\n\n\n  if (x instanceof Map) {\n    const obj = {};\n\n    for (const [k, v] of x) {\n      // Stringify will escape any nested quotes\n      obj[typeof k === 'string' ? k : stringify(k, opt)] = v;\n    }\n\n    return stringify(obj, opt, key);\n  } // For built-in Sets, sort the keys in a stable order instead of the\n  // default insertion order.\n\n\n  if (x instanceof Set) {\n    return stringify( // $FlowFixMe[missing-local-annot]\n    Array.from(x).sort((a, b) => stringify(a, opt).localeCompare(stringify(b, opt))), opt, key);\n  } // Anything else that is iterable serialize as an Array.\n\n\n  if (Symbol !== undefined && x[Symbol.iterator] != null && typeof x[Symbol.iterator] === 'function') {\n    // flowlint-next-line unclear-type: off\n    return stringify(Array.from(x), opt, key);\n  } // For all other Objects, sort the keys in a stable order.\n\n\n  return `{${Object.keys(x).filter(k => x[k] !== undefined).sort() // stringify the key to add quotes and escape any nested slashes or quotes.\n  .map(k => `${stringify(k, opt)}:${stringify(x[k], opt, k)}`).join(',')}}`;\n} // Utility similar to JSON.stringify() except:\n// * Serialize built-in Sets as an Array\n// * Serialize built-in Maps as an Object.  Supports non-string keys.\n// * Serialize other iterables as arrays\n// * Sort the keys of Objects and Maps to have a stable order based on string conversion.\n//    This overrides their default insertion order.\n// * Still uses toJSON() of any object to override serialization\n// * Support Symbols (though don't guarantee uniqueness)\n// * We could support BigInt, but Flow doesn't seem to like it.\n// See Recoil_stableStringify-test.js for examples\n\n\nfunction stableStringify(x, opt = {\n  allowFunctions: false\n}) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof window !== 'undefined') {\n      const startTime = window.performance ? window.performance.now() : 0;\n      const str = stringify(x, opt);\n      const endTime = window.performance ? window.performance.now() : 0;\n\n      if (endTime - startTime > TIME_WARNING_THRESHOLD_MS) {\n        /* eslint-disable fb-www/no-console */\n        console.groupCollapsed(`Recoil: Spent ${endTime - startTime}ms computing a cache key`);\n        console.warn(x, str);\n        console.groupEnd();\n        /* eslint-enable fb-www/no-console */\n      }\n\n      return str;\n    }\n  }\n\n  return stringify(x, opt);\n}\n\nvar Recoil_stableStringify = stableStringify;\n\nconst {\n  TreeCache: TreeCache$2\n} = Recoil_TreeCache$1;\n\n\n\n\n\n\n\n\n\nconst defaultPolicy = {\n  equality: 'reference',\n  eviction: 'keep-all',\n  maxSize: Infinity\n};\n\nfunction treeCacheFromPolicy({\n  equality = defaultPolicy.equality,\n  eviction = defaultPolicy.eviction,\n  maxSize = defaultPolicy.maxSize\n} = defaultPolicy, name) {\n  const valueMapper = getValueMapper(equality);\n  return getTreeCache(eviction, maxSize, valueMapper, name);\n}\n\nfunction getValueMapper(equality) {\n  switch (equality) {\n    case 'reference':\n      return val => val;\n\n    case 'value':\n      return val => Recoil_stableStringify(val);\n  }\n\n  throw Recoil_err(`Unrecognized equality policy ${equality}`);\n}\n\nfunction getTreeCache(eviction, maxSize, mapNodeValue, name) {\n  switch (eviction) {\n    case 'keep-all':\n      return new TreeCache$2({\n        name,\n        mapNodeValue\n      });\n\n    case 'lru':\n      return Recoil_treeCacheLRU({\n        name,\n        maxSize: Recoil_nullthrows(maxSize),\n        mapNodeValue\n      });\n\n    case 'most-recent':\n      return Recoil_treeCacheLRU({\n        name,\n        maxSize: 1,\n        mapNodeValue\n      });\n  }\n\n  throw Recoil_err(`Unrecognized eviction policy ${eviction}`);\n}\n\nvar Recoil_treeCacheFromPolicy = treeCacheFromPolicy;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction isNode(object) {\n  var _ownerDocument, _doc$defaultView;\n\n  if (typeof window === 'undefined') {\n    return false;\n  }\n\n  const doc = object != null ? (_ownerDocument = object.ownerDocument) !== null && _ownerDocument !== void 0 ? _ownerDocument : object : document;\n  const defaultView = (_doc$defaultView = doc.defaultView) !== null && _doc$defaultView !== void 0 ? _doc$defaultView : window;\n  return !!(object != null && (typeof defaultView.Node === 'function' ? object instanceof defaultView.Node : typeof object === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string'));\n}\n\nvar Recoil_isNode = isNode;\n\nconst {\n  isReactNative: isReactNative$1,\n  isWindow: isWindow$1\n} = Recoil_Environment;\n\n\n\n\n\nfunction shouldNotBeFrozen(value) {\n  // Primitives and functions:\n  if (value === null || typeof value !== 'object') {\n    return true;\n  } // React elements:\n\n\n  switch (typeof value.$$typeof) {\n    case 'symbol':\n      return true;\n\n    case 'number':\n      return true;\n  } // Immutable structures:\n\n\n  if (value['@@__IMMUTABLE_ITERABLE__@@'] != null || value['@@__IMMUTABLE_KEYED__@@'] != null || value['@@__IMMUTABLE_INDEXED__@@'] != null || value['@@__IMMUTABLE_ORDERED__@@'] != null || value['@@__IMMUTABLE_RECORD__@@'] != null) {\n    return true;\n  } // DOM nodes:\n\n\n  if (Recoil_isNode(value)) {\n    return true;\n  }\n\n  if (Recoil_isPromise(value)) {\n    return true;\n  }\n\n  if (value instanceof Error) {\n    return true;\n  }\n\n  if (ArrayBuffer.isView(value)) {\n    return true;\n  } // Some environments, just as Jest, don't work with the instanceof check\n\n\n  if (!isReactNative$1 && isWindow$1(value)) {\n    return true;\n  }\n\n  return false;\n} // Recursively freeze a value to enforce it is read-only.\n// This may also have minimal performance improvements for enumerating\n// objects (based on browser implementations, of course)\n\n\nfunction deepFreezeValue(value) {\n  if (typeof value !== 'object' || shouldNotBeFrozen(value)) {\n    return;\n  }\n\n  Object.freeze(value); // Make all properties read-only\n\n  for (const key in value) {\n    // $FlowIssue[method-unbinding] added when improving typing for this parameters\n    if (Object.prototype.hasOwnProperty.call(value, key)) {\n      const prop = value[key]; // Prevent infinite recurssion for circular references.\n\n      if (typeof prop === 'object' && prop != null && !Object.isFrozen(prop)) {\n        deepFreezeValue(prop);\n      }\n    }\n  }\n\n  Object.seal(value); // This also makes existing properties non-configurable.\n}\n\nvar Recoil_deepFreezeValue = deepFreezeValue;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * This is a stub for some integration into FB internal stuff\n *\n * \n * @format\n * @oncall recoil\n */\nfunction startPerfBlock(_id) {\n  return () => null;\n}\n\nvar Recoil_PerformanceTimings = {\n  startPerfBlock\n};\n\nconst {\n  isLoadable: isLoadable$1,\n  loadableWithError: loadableWithError$1,\n  loadableWithPromise: loadableWithPromise$1,\n  loadableWithValue: loadableWithValue$2\n} = Recoil_Loadable$1;\n\nconst {\n  WrappedValue: WrappedValue$1\n} = Recoil_Wrapper$1;\n\n\n\nconst {\n  getNodeLoadable: getNodeLoadable$2,\n  peekNodeLoadable: peekNodeLoadable$1,\n  setNodeValue: setNodeValue$3\n} = Recoil_FunctionalCore;\n\nconst {\n  saveDepsToStore: saveDepsToStore$1\n} = Recoil_Graph;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$6,\n  getConfigDeletionHandler: getConfigDeletionHandler$1,\n  getNode: getNode$6,\n  registerNode: registerNode$1\n} = Recoil_Node;\n\nconst {\n  isRecoilValue: isRecoilValue$3\n} = Recoil_RecoilValue$1;\n\nconst {\n  markRecoilValueModified: markRecoilValueModified$1\n} = Recoil_RecoilValueInterface;\n\nconst {\n  retainedByOptionWithDefault: retainedByOptionWithDefault$1\n} = Recoil_Retention;\n\nconst {\n  recoilCallback: recoilCallback$1\n} = Recoil_useRecoilCallback;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst {\n  startPerfBlock: startPerfBlock$1\n} = Recoil_PerformanceTimings;\n\n\n\nclass Canceled {}\n\nconst CANCELED = new Canceled();\n/**\n * An ExecutionID is an arbitrary ID that lets us distinguish executions from\n * each other. This is necessary as we need a way of solving this problem:\n * \"given 3 async executions, only update state for the 'latest' execution when\n * it finishes running regardless of when the other 2 finish\". ExecutionIDs\n * provide a convenient way of identifying executions so that we can track and\n * manage them over time.\n */\n\nconst dependencyStack = []; // for detecting circular dependencies.\n\nconst waitingStores = new Map();\n\nconst getNewExecutionID = (() => {\n  let executionID = 0;\n  return () => executionID++;\n})();\n/* eslint-disable no-redeclare */\n\n\nfunction selector(options) {\n  let recoilValue = null;\n  const {\n    key,\n    get,\n    cachePolicy_UNSTABLE: cachePolicy\n  } = options;\n  const set = options.set != null ? options.set : undefined; // flow\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof key !== 'string') {\n      throw Recoil_err('A key option with a unique string value must be provided when creating a selector.');\n    }\n\n    if (typeof get !== 'function') {\n      throw Recoil_err('Selectors must specify a get callback option to get the selector value.');\n    }\n  } // This is every discovered dependency across all executions\n\n\n  const discoveredDependencyNodeKeys = new Set();\n  const cache = Recoil_treeCacheFromPolicy(cachePolicy !== null && cachePolicy !== void 0 ? cachePolicy : {\n    equality: 'reference',\n    eviction: 'keep-all'\n  }, key);\n  const retainedBy = retainedByOptionWithDefault$1(options.retainedBy_UNSTABLE);\n  const executionInfoMap = new Map();\n  let liveStoresCount = 0;\n\n  function selectorIsLive() {\n    return !Recoil_gkx('recoil_memory_managament_2020') || liveStoresCount > 0;\n  }\n\n  function selectorInit(store) {\n    store.getState().knownSelectors.add(key);\n    liveStoresCount++;\n    return () => {\n      liveStoresCount--;\n    };\n  }\n\n  function selectorShouldDeleteConfigOnRelease() {\n    return getConfigDeletionHandler$1(key) !== undefined && !selectorIsLive();\n  }\n\n  function resolveAsync(store, state, executionID, loadable, depValues) {\n    setCache(state, loadable, depValues);\n    notifyStoresOfResolvedAsync(store, executionID);\n  }\n\n  function notifyStoresOfResolvedAsync(store, executionID) {\n    if (isLatestExecution(store, executionID)) {\n      clearExecutionInfo(store);\n    }\n\n    notifyWaitingStores(executionID, true);\n  }\n  /**\n   * Notify stores to pull the selector again if a new async dep was discovered.\n   * 1) Async selector adds a new dep but doesn't resolve yet.\n   *    Note that deps for an async selector are based on the state when the\n   *    evaluation started, in order to provide a consistent picture of state.\n   * 2) But, new value of dep based on the current state might cause the selector\n   *    to resolve or resolve differently.\n   * 3) Therefore, this notification will pull the selector based on the current\n   *    state for the components\n   */\n\n\n  function notifyStoresOfNewAsyncDep(store, executionID) {\n    if (isLatestExecution(store, executionID)) {\n      const executionInfo = Recoil_nullthrows(getExecutionInfo(store));\n      executionInfo.stateVersions.clear();\n      notifyWaitingStores(executionID, false);\n    }\n  }\n\n  function notifyWaitingStores(executionID, clearWaitlist) {\n    const stores = waitingStores.get(executionID);\n\n    if (stores != null) {\n      for (const waitingStore of stores) {\n        markRecoilValueModified$1(waitingStore, Recoil_nullthrows(recoilValue));\n      }\n\n      if (clearWaitlist) {\n        waitingStores.delete(executionID);\n      }\n    }\n  }\n\n  function markStoreWaitingForResolvedAsync(store, executionID) {\n    let stores = waitingStores.get(executionID);\n\n    if (stores == null) {\n      waitingStores.set(executionID, stores = new Set());\n    }\n\n    stores.add(store);\n  }\n  /**\n   * This function attaches a then() and a catch() to a promise that was\n   * returned from a selector's get() (either explicitly or implicitly by\n   * running a function that uses the \"async\" keyword). If a selector's get()\n   * returns a promise, we have two possibilities:\n   *\n   * 1. The promise will resolve, in which case it will have completely finished\n   *    executing without any remaining pending dependencies. No more retries\n   *    are needed and we can proceed with updating the cache and notifying\n   *    subscribers (if it is the latest execution, otherwise only the cache\n   *    will be updated and subscriptions will not be fired). This is the case\n   *    handled by the attached then() handler.\n   *\n   * 2. The promise will throw because it either has an error or it came across\n   *    an async dependency that has not yet resolved, in which case we will\n   *    call wrapDepdencyPromise(), whose responsibility is to handle dependency\n   *    promises. This case is handled by the attached catch() handler.\n   *\n   * Both branches will eventually resolve to the final result of the selector\n   * (or an error if a real error occurred).\n   *\n   * The execution will run to completion even if it is stale, and its value\n   * will be cached. But stale executions will not update global state or update\n   * executionInfo as that is the responsibility of the 'latest' execution.\n   *\n   * Note this function should not be passed a promise that was thrown--AKA a\n   * dependency promise. Dependency promises should be passed to\n   * wrapPendingDependencyPromise()).\n   */\n\n\n  function wrapResultPromise(store, promise, state, depValues, executionID, loadingDepsState) {\n    return promise.then(value => {\n      if (!selectorIsLive()) {\n        // The selector was released since the request began; ignore the response.\n        clearExecutionInfo(store);\n        throw CANCELED;\n      }\n\n      const loadable = loadableWithValue$2(value);\n      resolveAsync(store, state, executionID, loadable, depValues);\n      return value;\n    }).catch(errorOrPromise => {\n      if (!selectorIsLive()) {\n        // The selector was released since the request began; ignore the response.\n        clearExecutionInfo(store);\n        throw CANCELED;\n      }\n\n      if (Recoil_isPromise(errorOrPromise)) {\n        return wrapPendingDependencyPromise(store, errorOrPromise, state, depValues, executionID, loadingDepsState);\n      }\n\n      const loadable = loadableWithError$1(errorOrPromise);\n      resolveAsync(store, state, executionID, loadable, depValues);\n      throw errorOrPromise;\n    });\n  }\n  /**\n   * This function attaches a then() and a catch() to a promise that was\n   * thrown from a selector's get(). If a selector's get() throws a promise,\n   * we have two possibilities:\n   *\n   * 1. The promise will resolve, meaning one of our selector's dependencies is\n   *    now available and we should \"retry\" our get() by running it again. This\n   *    is the case handled by the attached then() handler.\n   *\n   * 2. The promise will throw because something went wrong with the dependency\n   *    promise (in other words a real error occurred). This case is handled by\n   *    the attached catch() handler. If the dependency promise throws, it is\n   *    _always_ a real error and not another dependency promise (any dependency\n   *    promises would have been handled upstream).\n   *\n   * The then() branch will eventually resolve to the final result of the\n   * selector (or an error if a real error occurs), and the catch() will always\n   * resolve to an error because the dependency promise is a promise that was\n   * wrapped upstream, meaning it will only resolve to its real value or to a\n   * real error.\n   *\n   * The execution will run to completion even if it is stale, and its value\n   * will be cached. But stale executions will not update global state or update\n   * executionInfo as that is the responsibility of the 'latest' execution.\n   *\n   * Note this function should not be passed a promise that was returned from\n   * get(). The intention is that this function is only passed promises that\n   * were thrown due to a pending dependency. Promises returned by get() should\n   * be passed to wrapResultPromise() instead.\n   */\n\n\n  function wrapPendingDependencyPromise(store, promise, state, existingDeps, executionID, loadingDepsState) {\n    return promise.then(resolvedDep => {\n      if (!selectorIsLive()) {\n        // The selector was released since the request began; ignore the response.\n        clearExecutionInfo(store);\n        throw CANCELED;\n      } // Check if we are handling a pending Recoil dependency or if the user\n      // threw their own Promise to \"suspend\" a selector evaluation.  We need\n      // to check that the loadingDepPromise actually matches the promise that\n      // we caught in case the selector happened to catch the promise we threw\n      // for a pending Recoil dependency from `getRecoilValue()` and threw\n      // their own promise instead.\n\n\n      if (loadingDepsState.loadingDepKey != null && loadingDepsState.loadingDepPromise === promise) {\n        /**\n         * Note for async atoms, this means we are changing the atom's value\n         * in the store for the given version. This should be alright because\n         * the version of state is now stale and a new version will have\n         * already been triggered by the atom being resolved (see this logic\n         * in Recoil_atom.js)\n         */\n        state.atomValues.set(loadingDepsState.loadingDepKey, loadableWithValue$2(resolvedDep));\n      } else {\n        /**\n         * If resolvedDepKey is not defined, the promise was a user-thrown\n         * promise. User-thrown promises are an advanced feature and they\n         * should be avoided in almost all cases. Using `loadable.map()` inside\n         * of selectors for loading loadables and then throwing that mapped\n         * loadable's promise is an example of a user-thrown promise.\n         *\n         * When we hit a user-thrown promise, we have to bail out of an optimization\n         * where we bypass calculating selector cache keys for selectors that\n         * have been previously seen for a given state (these selectors are saved in\n         * state.atomValues) to avoid stale state as we have no way of knowing\n         * what state changes happened (if any) in result to the promise resolving.\n         *\n         * Ideally we would only bail out selectors that are in the chain of\n         * dependencies for this selector, but there's currently no way to get\n         * a full list of a selector's downstream nodes because the state that\n         * is executing may be a discarded tree (so store.getGraph(state.version)\n         * will be empty), and the full dep tree may not be in the selector\n         * caches in the case where the selector's cache was cleared. To solve\n         * for this we would have to keep track of all running selector\n         * executions and their downstream deps. Because this only covers edge\n         * cases, that complexity might not be justifyable.\n         */\n        store.getState().knownSelectors.forEach(nodeKey => {\n          state.atomValues.delete(nodeKey);\n        });\n      }\n      /**\n       * Optimization: Now that the dependency has resolved, let's try hitting\n       * the cache in case the dep resolved to a value we have previously seen.\n       *\n       * TODO:\n       * Note this optimization is not perfect because it only prevents re-executions\n       * _after_ the point where an async dependency is found. Any code leading\n       * up to the async dependency may have run unnecessarily. The ideal case\n       * would be to wait for the async dependency to resolve first, check the\n       * cache, and prevent _any_ execution of the selector if the resulting\n       * value of the dependency leads to a path that is found in the cache.\n       * The ideal case is more difficult to implement as it would require that\n       * we capture and wait for the the async dependency right after checking\n       * the cache. The current approach takes advantage of the fact that running\n       * the selector already has a code path that lets us exit early when\n       * an async dep resolves.\n       */\n\n\n      const cachedLoadable = getLoadableFromCacheAndUpdateDeps(store, state);\n\n      if (cachedLoadable && cachedLoadable.state !== 'loading') {\n        /**\n         * This has to notify stores of a resolved async, even if there is no\n         * current pending execution for the following case:\n         * 1) A component renders with this pending loadable.\n         * 2) The upstream dependency resolves.\n         * 3) While processing some other selector it reads this one, such as\n         *    while traversing its dependencies.  At this point it gets the\n         *    new resolved value synchronously and clears the current\n         *    execution ID.  The component wasn't getting the value itself,\n         *    though, so it still has the pending loadable.\n         * 4) When this code executes the current execution id was cleared\n         *    and it wouldn't notify the component of the new value.\n         *\n         * I think this is only an issue with \"early\" rendering since the\n         * components got their value using the in-progress execution.\n         * We don't have a unit test for this case yet.  I'm not sure it is\n         * necessary with recoil_transition_support mode.\n         */\n        if (isLatestExecution(store, executionID) || getExecutionInfo(store) == null) {\n          notifyStoresOfResolvedAsync(store, executionID);\n        }\n\n        if (cachedLoadable.state === 'hasValue') {\n          return cachedLoadable.contents;\n        } else {\n          throw cachedLoadable.contents;\n        }\n      }\n      /**\n       * If this execution is stale, let's check to see if there is some in\n       * progress execution with a matching state. If we find a match, then\n       * we can take the value from that in-progress execution. Note this may\n       * sound like an edge case, but may be very common in cases where a\n       * loading dependency resolves from loading to having a value (thus\n       * possibly triggering a re-render), and React re-renders before the\n       * chained .then() functions run, thus starting a new execution as the\n       * dep has changed value. Without this check we will run the selector\n       * twice (once in the new execution and once again in this .then(), so\n       * this check is necessary to keep unnecessary re-executions to a\n       * minimum).\n       *\n       * Also note this code does not check across all executions that may be\n       * running. It only optimizes for the _latest_ execution per store as\n       * we currently do not maintain a list of all currently running executions.\n       * This means in some cases we may run selectors more than strictly\n       * necessary when there are multiple executions running for the same\n       * selector. This may be a valid tradeoff as checking for dep changes\n       * across all in-progress executions may take longer than just\n       * re-running the selector. This will be app-dependent, and maybe in the\n       * future we can make the behavior configurable. An ideal fix may be\n       * to extend the tree cache to support caching loading states.\n       */\n\n\n      if (!isLatestExecution(store, executionID)) {\n        const executionInfo = getInProgressExecutionInfo(store, state);\n\n        if (executionInfo != null) {\n          /**\n           * Returning promise here without wrapping as the wrapper logic was\n           * already done upstream when this promise was generated.\n           */\n          return executionInfo.loadingLoadable.contents;\n        }\n      } // Retry the selector evaluation now that the dependency has resolved\n\n\n      const [loadable, depValues] = evaluateSelectorGetter(store, state, executionID);\n\n      if (loadable.state !== 'loading') {\n        resolveAsync(store, state, executionID, loadable, depValues);\n      }\n\n      if (loadable.state === 'hasError') {\n        throw loadable.contents;\n      }\n\n      return loadable.contents;\n    }).catch(error => {\n      // The selector was released since the request began; ignore the response.\n      if (error instanceof Canceled) {\n        throw CANCELED;\n      }\n\n      if (!selectorIsLive()) {\n        clearExecutionInfo(store);\n        throw CANCELED;\n      }\n\n      const loadable = loadableWithError$1(error);\n      resolveAsync(store, state, executionID, loadable, existingDeps);\n      throw error;\n    });\n  }\n\n  function updateDeps(store, state, deps, executionID) {\n    var _store$getState, _store$getState$curre, _store$getState2, _store$getState2$next;\n\n    if (isLatestExecution(store, executionID) || state.version === ((_store$getState = store.getState()) === null || _store$getState === void 0 ? void 0 : (_store$getState$curre = _store$getState.currentTree) === null || _store$getState$curre === void 0 ? void 0 : _store$getState$curre.version) || state.version === ((_store$getState2 = store.getState()) === null || _store$getState2 === void 0 ? void 0 : (_store$getState2$next = _store$getState2.nextTree) === null || _store$getState2$next === void 0 ? void 0 : _store$getState2$next.version)) {\n      var _store$getState$nextT, _store$getState3, _store$getState3$next;\n\n      saveDepsToStore$1(key, deps, store, (_store$getState$nextT = (_store$getState3 = store.getState()) === null || _store$getState3 === void 0 ? void 0 : (_store$getState3$next = _store$getState3.nextTree) === null || _store$getState3$next === void 0 ? void 0 : _store$getState3$next.version) !== null && _store$getState$nextT !== void 0 ? _store$getState$nextT : store.getState().currentTree.version);\n    }\n\n    for (const nodeKey of deps) {\n      discoveredDependencyNodeKeys.add(nodeKey);\n    }\n  }\n\n  function evaluateSelectorGetter(store, state, executionID) {\n    const endPerfBlock = startPerfBlock$1(key); // TODO T63965866: use execution ID here\n\n    let duringSynchronousExecution = true;\n    let duringAsynchronousExecution = true;\n\n    const finishEvaluation = () => {\n      endPerfBlock();\n      duringAsynchronousExecution = false;\n    };\n\n    let result;\n    let resultIsError = false;\n    let loadable;\n    const loadingDepsState = {\n      loadingDepKey: null,\n      loadingDepPromise: null\n    };\n    /**\n     * Starting a fresh set of deps that we'll be using to update state. We're\n     * starting a new set versus adding it in existing state deps because\n     * the version of state that we update deps for may be a more recent version\n     * than the version the selector was called with. This is because the latest\n     * execution will update the deps of the current/latest version of state\n     * (This is safe to do because the fact that the selector is the latest\n     * execution means the deps we discover below are our best guess at the\n     * deps for the current/latest state in the store)\n     */\n\n    const depValues = new Map();\n\n    function getRecoilValue({\n      key: depKey\n    }) {\n      const depLoadable = getNodeLoadable$2(store, state, depKey);\n      depValues.set(depKey, depLoadable); // We need to update asynchronous dependencies as we go so the selector\n      // knows if it has to restart evaluation if one of them is updated before\n      // the asynchronous selector completely resolves.\n\n      if (!duringSynchronousExecution) {\n        updateDeps(store, state, new Set(depValues.keys()), executionID);\n        notifyStoresOfNewAsyncDep(store, executionID);\n      }\n\n      switch (depLoadable.state) {\n        case 'hasValue':\n          return depLoadable.contents;\n\n        case 'hasError':\n          throw depLoadable.contents;\n\n        case 'loading':\n          loadingDepsState.loadingDepKey = depKey;\n          loadingDepsState.loadingDepPromise = depLoadable.contents;\n          throw depLoadable.contents;\n      }\n\n      throw Recoil_err('Invalid Loadable state');\n    }\n\n    const getCallback = fn => {\n      return (...args) => {\n        if (duringAsynchronousExecution) {\n          throw Recoil_err('Callbacks from getCallback() should only be called asynchronously after the selector is evalutated.  It can be used for selectors to return objects with callbacks that can work with Recoil state without a subscription.');\n        }\n\n        !(recoilValue != null) ? process.env.NODE_ENV !== \"production\" ? Recoil_invariant(false, 'Recoil Value can never be null') : Recoil_invariant(false) : void 0;\n        return recoilCallback$1(store, fn, args, {\n          node: recoilValue\n        } // flowlint-line unclear-type:off\n        );\n      };\n    };\n\n    try {\n      result = get({\n        get: getRecoilValue,\n        getCallback\n      });\n      result = isRecoilValue$3(result) ? getRecoilValue(result) : result;\n\n      if (isLoadable$1(result)) {\n        if (result.state === 'hasError') {\n          resultIsError = true;\n        }\n\n        result = result.contents;\n      }\n\n      if (Recoil_isPromise(result)) {\n        result = wrapResultPromise(store, result, state, depValues, executionID, loadingDepsState).finally(finishEvaluation);\n      } else {\n        finishEvaluation();\n      }\n\n      result = result instanceof WrappedValue$1 ? result.value : result;\n    } catch (errorOrDepPromise) {\n      result = errorOrDepPromise;\n\n      if (Recoil_isPromise(result)) {\n        result = wrapPendingDependencyPromise(store, result, state, depValues, executionID, loadingDepsState).finally(finishEvaluation);\n      } else {\n        resultIsError = true;\n        finishEvaluation();\n      }\n    }\n\n    if (resultIsError) {\n      loadable = loadableWithError$1(result);\n    } else if (Recoil_isPromise(result)) {\n      loadable = loadableWithPromise$1(result);\n    } else {\n      loadable = loadableWithValue$2(result);\n    }\n\n    duringSynchronousExecution = false;\n    updateExecutionInfoDepValues(store, executionID, depValues);\n    updateDeps(store, state, new Set(depValues.keys()), executionID);\n    return [loadable, depValues];\n  }\n\n  function getLoadableFromCacheAndUpdateDeps(store, state) {\n    // First, look up in the state cache\n    // If it's here, then the deps in the store should already be valid.\n    let cachedLoadable = state.atomValues.get(key);\n\n    if (cachedLoadable != null) {\n      return cachedLoadable;\n    } // Second, look up in the selector cache and update the deps in the store\n\n\n    const depsAfterCacheLookup = new Set();\n\n    try {\n      cachedLoadable = cache.get(nodeKey => {\n        !(typeof nodeKey === 'string') ? process.env.NODE_ENV !== \"production\" ? Recoil_invariant(false, 'Cache nodeKey is type string') : Recoil_invariant(false) : void 0;\n        return getNodeLoadable$2(store, state, nodeKey).contents;\n      }, {\n        onNodeVisit: node => {\n          if (node.type === 'branch' && node.nodeKey !== key) {\n            depsAfterCacheLookup.add(node.nodeKey);\n          }\n        }\n      });\n    } catch (error) {\n      throw Recoil_err(`Problem with cache lookup for selector \"${key}\": ${error.message}`);\n    }\n\n    if (cachedLoadable) {\n      var _getExecutionInfo;\n\n      // Cache the results in the state to allow for cheaper lookup than\n      // iterating the tree cache of dependencies.\n      state.atomValues.set(key, cachedLoadable);\n      /**\n       * Ensure store contains correct dependencies if we hit the cache so that\n       * the store deps and cache are in sync for a given state. This is important\n       * because store deps are normally updated when new executions are created,\n       * but cache hits don't trigger new executions but they still _may_ signify\n       * a change in deps in the store if the store deps for this state are empty\n       * or stale.\n       */\n\n      updateDeps(store, state, depsAfterCacheLookup, (_getExecutionInfo = getExecutionInfo(store)) === null || _getExecutionInfo === void 0 ? void 0 : _getExecutionInfo.executionID);\n    }\n\n    return cachedLoadable;\n  }\n  /**\n   * Given a tree state, this function returns a Loadable of the current state.\n   *\n   * The selector's get() function will only be re-evaluated if _both_ of the\n   * following statements are true:\n   *\n   * 1. The current dep values from the given state produced a cache key that\n   *    was not found in the cache.\n   * 2. There is no currently running async execution OR there is an\n   *    async execution that is running, but after comparing the dep values in\n   *    the given state with the dep values that the execution has discovered so\n   *    far we find that at least one dep value has changed, in which case we\n   *    start a new execution (the previously running execution will continue to\n   *    run to completion, but only the new execution will be deemed the\n   *    'latest' execution, meaning it will be the only execution that will\n   *    update global state when it is finished. Any non-latest executions will\n   *    run to completion and update the selector cache but not global state).\n   */\n\n\n  function getSelectorLoadableAndUpdateDeps(store, state) {\n    // First, see if our current state is cached\n    const cachedVal = getLoadableFromCacheAndUpdateDeps(store, state);\n\n    if (cachedVal != null) {\n      clearExecutionInfo(store);\n      return cachedVal;\n    } // Second, check if there is already an ongoing execution based on the current state\n\n\n    const inProgressExecutionInfo = getInProgressExecutionInfo(store, state);\n\n    if (inProgressExecutionInfo != null) {\n      var _inProgressExecutionI;\n\n      if (((_inProgressExecutionI = inProgressExecutionInfo.loadingLoadable) === null || _inProgressExecutionI === void 0 ? void 0 : _inProgressExecutionI.state) === 'loading') {\n        markStoreWaitingForResolvedAsync(store, inProgressExecutionInfo.executionID);\n      } // FIXME: check after the fact to see if we made the right choice by waiting\n\n\n      return inProgressExecutionInfo.loadingLoadable;\n    } // Third, start a new evaluation of the selector\n\n\n    const newExecutionID = getNewExecutionID();\n    const [loadable, newDepValues] = evaluateSelectorGetter(store, state, newExecutionID);\n    /**\n     * Conditionally updates the cache with a given loadable.\n     *\n     * We only cache loadables that are not loading because our cache keys are\n     * based on dep values, which are in an unfinished state for loadables that\n     * have a 'loading' state (new deps may be discovered while the selector\n     * runs its async code). We never want to cache partial dependencies b/c it\n     * could lead to errors, such as prematurely returning the result based on a\n     * partial list of deps-- we need the full list of deps to ensure that we\n     * are returning the correct result from cache.\n     */\n\n    if (loadable.state === 'loading') {\n      setExecutionInfo(store, newExecutionID, loadable, newDepValues, state);\n      markStoreWaitingForResolvedAsync(store, newExecutionID);\n    } else {\n      clearExecutionInfo(store);\n      setCache(state, loadable, newDepValues);\n    }\n\n    return loadable;\n  }\n  /**\n   * Searches execution info across all stores to see if there is an in-progress\n   * execution whose dependency values match the values of the requesting store.\n   */\n\n\n  function getInProgressExecutionInfo(store, state) {\n    // Sort the pending executions so that our current store is checked first.\n    const pendingExecutions = Recoil_concatIterables([executionInfoMap.has(store) ? [Recoil_nullthrows(executionInfoMap.get(store))] : [], Recoil_mapIterable(Recoil_filterIterable(executionInfoMap, ([s]) => s !== store), ([, execInfo]) => execInfo)]);\n\n    function anyDepChanged(execDepValues) {\n      for (const [depKey, execLoadable] of execDepValues) {\n        if (!getNodeLoadable$2(store, state, depKey).is(execLoadable)) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n\n    for (const execInfo of pendingExecutions) {\n      if ( // If this execution was already checked to be valid with this version\n      // of state, then let's use it!\n      execInfo.stateVersions.get(state.version) || // If the deps for the execution match our current state, then it's valid\n      !anyDepChanged(execInfo.depValuesDiscoveredSoFarDuringAsyncWork)) {\n        execInfo.stateVersions.set(state.version, true);\n        return execInfo;\n      } else {\n        execInfo.stateVersions.set(state.version, false);\n      }\n    }\n\n    return undefined;\n  }\n\n  function getExecutionInfo(store) {\n    return executionInfoMap.get(store);\n  }\n  /**\n   * This function will update the selector's execution info when the selector\n   * has either finished running an execution or has started a new execution. If\n   * the given loadable is in a 'loading' state, the intention is that a new\n   * execution has started. Otherwise, the intention is that an execution has\n   * just finished.\n   */\n\n\n  function setExecutionInfo(store, newExecutionID, loadable, depValues, state) {\n    executionInfoMap.set(store, {\n      depValuesDiscoveredSoFarDuringAsyncWork: depValues,\n      executionID: newExecutionID,\n      loadingLoadable: loadable,\n      stateVersions: new Map([[state.version, true]])\n    });\n  }\n\n  function updateExecutionInfoDepValues(store, executionID, depValues) {\n    // We only need to bother updating the deps for the latest execution because\n    // that's all getInProgressExecutionInfo() will be looking for.\n    if (isLatestExecution(store, executionID)) {\n      const executionInfo = getExecutionInfo(store);\n\n      if (executionInfo != null) {\n        executionInfo.depValuesDiscoveredSoFarDuringAsyncWork = depValues;\n      }\n    }\n  }\n\n  function clearExecutionInfo(store) {\n    executionInfoMap.delete(store);\n  }\n\n  function isLatestExecution(store, executionID) {\n    var _getExecutionInfo2;\n\n    return executionID === ((_getExecutionInfo2 = getExecutionInfo(store)) === null || _getExecutionInfo2 === void 0 ? void 0 : _getExecutionInfo2.executionID);\n  }\n  /**\n   * FIXME: dep keys should take into account the state of the loadable to\n   * prevent the edge case where a loadable with an error and a loadable with\n   * an error as a value are treated as the same thing incorrectly. For example\n   * these two should be treated differently:\n   *\n   * selector({key: '', get: () => new Error('hi')});\n   * selector({key: '', get () => {throw new Error('hi')}});\n   *\n   * With current implementation they are treated the same\n   */\n\n\n  function depValuesToDepRoute(depValues) {\n    return Array.from(depValues.entries()).map(([depKey, valLoadable]) => [depKey, valLoadable.contents]);\n  }\n\n  function setCache(state, loadable, depValues) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (loadable.state !== 'loading' && Boolean(options.dangerouslyAllowMutability) === false) {\n        Recoil_deepFreezeValue(loadable.contents);\n      }\n    }\n\n    state.atomValues.set(key, loadable);\n\n    try {\n      cache.set(depValuesToDepRoute(depValues), loadable);\n    } catch (error) {\n      throw Recoil_err(`Problem with setting cache for selector \"${key}\": ${error.message}`);\n    }\n  }\n\n  function detectCircularDependencies(fn) {\n    if (dependencyStack.includes(key)) {\n      const message = `Recoil selector has circular dependencies: ${dependencyStack.slice(dependencyStack.indexOf(key)).join(' \\u2192 ')}`;\n      return loadableWithError$1(Recoil_err(message));\n    }\n\n    dependencyStack.push(key);\n\n    try {\n      return fn();\n    } finally {\n      dependencyStack.pop();\n    }\n  }\n\n  function selectorPeek(store, state) {\n    const cachedLoadable = state.atomValues.get(key);\n\n    if (cachedLoadable != null) {\n      return cachedLoadable;\n    }\n\n    return cache.get(nodeKey => {\n      var _peekNodeLoadable;\n\n      !(typeof nodeKey === 'string') ? process.env.NODE_ENV !== \"production\" ? Recoil_invariant(false, 'Cache nodeKey is type string') : Recoil_invariant(false) : void 0;\n      return (_peekNodeLoadable = peekNodeLoadable$1(store, state, nodeKey)) === null || _peekNodeLoadable === void 0 ? void 0 : _peekNodeLoadable.contents;\n    });\n  }\n\n  function selectorGet(store, state) {\n    return detectCircularDependencies(() => getSelectorLoadableAndUpdateDeps(store, state));\n  }\n\n  function invalidateSelector(state) {\n    state.atomValues.delete(key);\n  }\n\n  function clearSelectorCache(store, treeState) {\n    !(recoilValue != null) ? process.env.NODE_ENV !== \"production\" ? Recoil_invariant(false, 'Recoil Value can never be null') : Recoil_invariant(false) : void 0;\n\n    for (const nodeKey of discoveredDependencyNodeKeys) {\n      var _node$clearCache;\n\n      const node = getNode$6(nodeKey);\n      (_node$clearCache = node.clearCache) === null || _node$clearCache === void 0 ? void 0 : _node$clearCache.call(node, store, treeState);\n    }\n\n    discoveredDependencyNodeKeys.clear();\n    invalidateSelector(treeState);\n    cache.clear();\n    markRecoilValueModified$1(store, recoilValue);\n  }\n\n  if (set != null) {\n    /**\n     * ES5 strict mode prohibits defining non-top-level function declarations,\n     * so don't use function declaration syntax here\n     */\n    const selectorSet = (store, state, newValue) => {\n      let syncSelectorSetFinished = false;\n      const writes = new Map();\n\n      function getRecoilValue({\n        key: depKey\n      }) {\n        if (syncSelectorSetFinished) {\n          throw Recoil_err('Recoil: Async selector sets are not currently supported.');\n        }\n\n        const loadable = getNodeLoadable$2(store, state, depKey);\n\n        if (loadable.state === 'hasValue') {\n          return loadable.contents;\n        } else if (loadable.state === 'loading') {\n          const msg = `Getting value of asynchronous atom or selector \"${depKey}\" in a pending state while setting selector \"${key}\" is not yet supported.`;\n          Recoil_recoverableViolation(msg);\n          throw Recoil_err(msg);\n        } else {\n          throw loadable.contents;\n        }\n      }\n\n      function setRecoilState(recoilState, valueOrUpdater // $FlowFixMe[missing-local-annot]\n      ) {\n        if (syncSelectorSetFinished) {\n          const msg = 'Recoil: Async selector sets are not currently supported.';\n          Recoil_recoverableViolation(msg);\n          throw Recoil_err(msg);\n        }\n\n        const setValue = typeof valueOrUpdater === 'function' ? // cast to any because we can't restrict type S from being a function itself without losing support for opaque types\n        // flowlint-next-line unclear-type:off\n        valueOrUpdater(getRecoilValue(recoilState)) : valueOrUpdater;\n        const upstreamWrites = setNodeValue$3(store, state, recoilState.key, setValue);\n        upstreamWrites.forEach((v, k) => writes.set(k, v));\n      }\n\n      function resetRecoilState(recoilState) {\n        setRecoilState(recoilState, DEFAULT_VALUE$6);\n      }\n\n      const ret = set({\n        set: setRecoilState,\n        get: getRecoilValue,\n        reset: resetRecoilState\n      }, newValue); // set should be a void method, but if the user makes it `async`, then it\n      // will return a Promise, which we don't currently support.\n\n      if (ret !== undefined) {\n        throw Recoil_isPromise(ret) ? Recoil_err('Recoil: Async selector sets are not currently supported.') : Recoil_err('Recoil: selector set should be a void function.');\n      }\n\n      syncSelectorSetFinished = true;\n      return writes;\n    };\n\n    return recoilValue = registerNode$1({\n      key,\n      nodeType: 'selector',\n      peek: selectorPeek,\n      get: selectorGet,\n      set: selectorSet,\n      init: selectorInit,\n      invalidate: invalidateSelector,\n      clearCache: clearSelectorCache,\n      shouldDeleteConfigOnRelease: selectorShouldDeleteConfigOnRelease,\n      dangerouslyAllowMutability: options.dangerouslyAllowMutability,\n      shouldRestoreFromSnapshots: false,\n      retainedBy\n    });\n  } else {\n    return recoilValue = registerNode$1({\n      key,\n      nodeType: 'selector',\n      peek: selectorPeek,\n      get: selectorGet,\n      init: selectorInit,\n      invalidate: invalidateSelector,\n      clearCache: clearSelectorCache,\n      shouldDeleteConfigOnRelease: selectorShouldDeleteConfigOnRelease,\n      dangerouslyAllowMutability: options.dangerouslyAllowMutability,\n      shouldRestoreFromSnapshots: false,\n      retainedBy\n    });\n  }\n}\n/* eslint-enable no-redeclare */\n// $FlowIssue[incompatible-use]\n// $FlowFixMe[missing-local-annot]\n\n\nselector.value = value => new WrappedValue$1(value);\n\nvar Recoil_selector = selector;\n\n// @fb-only: import type {ScopeRules} from 'Recoil_ScopedAtom';\n// @fb-only: const {scopedAtom} = require('Recoil_ScopedAtom');\nconst {\n  isLoadable: isLoadable$2,\n  loadableWithError: loadableWithError$2,\n  loadableWithPromise: loadableWithPromise$2,\n  loadableWithValue: loadableWithValue$3\n} = Recoil_Loadable$1;\n\nconst {\n  WrappedValue: WrappedValue$2\n} = Recoil_Wrapper$1;\n\nconst {\n  peekNodeInfo: peekNodeInfo$3\n} = Recoil_FunctionalCore;\n\nconst {\n  DEFAULT_VALUE: DEFAULT_VALUE$7,\n  DefaultValue: DefaultValue$2,\n  getConfigDeletionHandler: getConfigDeletionHandler$2,\n  registerNode: registerNode$2,\n  setConfigDeletionHandler: setConfigDeletionHandler$1\n} = Recoil_Node;\n\nconst {\n  isRecoilValue: isRecoilValue$4\n} = Recoil_RecoilValue$1;\n\nconst {\n  getRecoilValueAsLoadable: getRecoilValueAsLoadable$4,\n  markRecoilValueModified: markRecoilValueModified$2,\n  setRecoilValue: setRecoilValue$4,\n  setRecoilValueLoadable: setRecoilValueLoadable$2\n} = Recoil_RecoilValueInterface;\n\nconst {\n  retainedByOptionWithDefault: retainedByOptionWithDefault$2\n} = Recoil_Retention;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst unwrap = x => x instanceof WrappedValue$2 ? x.value : x;\n\nfunction baseAtom(options) {\n  const {\n    key,\n    persistence_UNSTABLE: persistence\n  } = options;\n  const retainedBy = retainedByOptionWithDefault$2(options.retainedBy_UNSTABLE);\n  let liveStoresCount = 0;\n\n  function unwrapPromise(promise) {\n    return loadableWithPromise$2(promise.then(value => {\n      defaultLoadable = loadableWithValue$3(value);\n      return value;\n    }).catch(error => {\n      defaultLoadable = loadableWithError$2(error);\n      throw error;\n    }));\n  }\n\n  let defaultLoadable = Recoil_isPromise(options.default) ? unwrapPromise(options.default) : isLoadable$2(options.default) ? options.default.state === 'loading' ? unwrapPromise(options.default.contents) : options.default : // $FlowFixMe[incompatible-call]\n  loadableWithValue$3(unwrap(options.default));\n  maybeFreezeValueOrPromise(defaultLoadable.contents);\n  let cachedAnswerForUnvalidatedValue = undefined; // Cleanup handlers for this atom\n  // Rely on stable reference equality of the store to use it as a key per <RecoilRoot>\n\n  const cleanupEffectsByStore = new Map();\n\n  function maybeFreezeValueOrPromise(valueOrPromise) {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (options.dangerouslyAllowMutability !== true) {\n        if (Recoil_isPromise(valueOrPromise)) {\n          return valueOrPromise.then(value => {\n            Recoil_deepFreezeValue(value);\n            return value;\n          });\n        } else {\n          Recoil_deepFreezeValue(valueOrPromise);\n          return valueOrPromise;\n        }\n      }\n    }\n\n    return valueOrPromise;\n  }\n\n  function wrapPendingPromise(store, promise) {\n    const wrappedPromise = promise.then(value => {\n      var _store$getState$nextT, _state$atomValues$get;\n\n      const state = (_store$getState$nextT = store.getState().nextTree) !== null && _store$getState$nextT !== void 0 ? _store$getState$nextT : store.getState().currentTree;\n\n      if (((_state$atomValues$get = state.atomValues.get(key)) === null || _state$atomValues$get === void 0 ? void 0 : _state$atomValues$get.contents) === wrappedPromise) {\n        setRecoilValue$4(store, node, value);\n      }\n\n      return value;\n    }).catch(error => {\n      var _store$getState$nextT2, _state$atomValues$get2;\n\n      const state = (_store$getState$nextT2 = store.getState().nextTree) !== null && _store$getState$nextT2 !== void 0 ? _store$getState$nextT2 : store.getState().currentTree;\n\n      if (((_state$atomValues$get2 = state.atomValues.get(key)) === null || _state$atomValues$get2 === void 0 ? void 0 : _state$atomValues$get2.contents) === wrappedPromise) {\n        setRecoilValueLoadable$2(store, node, loadableWithError$2(error));\n      }\n\n      throw error;\n    });\n    return wrappedPromise;\n  }\n\n  function initAtom(store, initState, trigger) {\n    var _options$effects;\n\n    liveStoresCount++;\n\n    const cleanupAtom = () => {\n      var _cleanupEffectsByStor;\n\n      liveStoresCount--;\n      (_cleanupEffectsByStor = cleanupEffectsByStore.get(store)) === null || _cleanupEffectsByStor === void 0 ? void 0 : _cleanupEffectsByStor.forEach(cleanup => cleanup());\n      cleanupEffectsByStore.delete(store);\n    };\n\n    store.getState().knownAtoms.add(key); // Setup async defaults to notify subscribers when they resolve\n\n    if (defaultLoadable.state === 'loading') {\n      const notifyDefaultSubscribers = () => {\n        var _store$getState$nextT3;\n\n        const state = (_store$getState$nextT3 = store.getState().nextTree) !== null && _store$getState$nextT3 !== void 0 ? _store$getState$nextT3 : store.getState().currentTree;\n\n        if (!state.atomValues.has(key)) {\n          markRecoilValueModified$2(store, node);\n        }\n      };\n\n      defaultLoadable.contents.finally(notifyDefaultSubscribers);\n    } ///////////////////\n    // Run Atom Effects\n    ///////////////////\n\n\n    const effects = (_options$effects = options.effects) !== null && _options$effects !== void 0 ? _options$effects : options.effects_UNSTABLE;\n\n    if (effects != null) {\n      // This state is scoped by Store, since this is in the initAtom() closure\n      let initValue = DEFAULT_VALUE$7;\n      let isDuringInit = true;\n      let isInitError = false;\n      let pendingSetSelf = null;\n\n      function getLoadable(recoilValue) {\n        // Normally we can just get the current value of another atom.\n        // But for our own value we need to check if there is a pending\n        // initialized value or get the fallback default value.\n        if (isDuringInit && recoilValue.key === key) {\n          // Cast T to S\n          const retValue = initValue; // flowlint-line unclear-type:off\n\n          return retValue instanceof DefaultValue$2 ? peekAtom(store, initState) // flowlint-line unclear-type:off\n          : Recoil_isPromise(retValue) ? loadableWithPromise$2(retValue.then(v => v instanceof DefaultValue$2 ? // Cast T to S\n          defaultLoadable.toPromise() // flowlint-line unclear-type:off\n          : v)) : // $FlowFixMe[incompatible-call]\n          loadableWithValue$3(retValue);\n        }\n\n        return getRecoilValueAsLoadable$4(store, recoilValue);\n      }\n\n      function getPromise(recoilValue) {\n        return getLoadable(recoilValue).toPromise();\n      }\n\n      function getInfo_UNSTABLE(recoilValue) {\n        var _store$getState$nextT4;\n\n        const info = peekNodeInfo$3(store, (_store$getState$nextT4 = store.getState().nextTree) !== null && _store$getState$nextT4 !== void 0 ? _store$getState$nextT4 : store.getState().currentTree, recoilValue.key);\n        return isDuringInit && recoilValue.key === key && !(initValue instanceof DefaultValue$2) ? { ...info,\n          isSet: true,\n          loadable: getLoadable(recoilValue)\n        } : info;\n      }\n\n      const setSelf = effect => valueOrUpdater => {\n        if (isDuringInit) {\n          const currentLoadable = getLoadable(node);\n          const currentValue = currentLoadable.state === 'hasValue' ? currentLoadable.contents : DEFAULT_VALUE$7;\n          initValue = typeof valueOrUpdater === 'function' ? // cast to any because we can't restrict T from being a function without losing support for opaque types\n          valueOrUpdater(currentValue) // flowlint-line unclear-type:off\n          : valueOrUpdater;\n\n          if (Recoil_isPromise(initValue)) {\n            initValue = initValue.then(value => {\n              // Avoid calling onSet() when setSelf() initializes with a Promise\n              pendingSetSelf = {\n                effect,\n                value\n              };\n              return value;\n            });\n          }\n        } else {\n          if (Recoil_isPromise(valueOrUpdater)) {\n            throw Recoil_err('Setting atoms to async values is not implemented.');\n          }\n\n          if (typeof valueOrUpdater !== 'function') {\n            pendingSetSelf = {\n              effect,\n              value: unwrap(valueOrUpdater)\n            };\n          }\n\n          setRecoilValue$4(store, node, typeof valueOrUpdater === 'function' ? currentValue => {\n            const newValue = unwrap( // cast to any because we can't restrict T from being a function without losing support for opaque types\n            valueOrUpdater(currentValue) // flowlint-line unclear-type:off\n            ); // $FlowFixMe[incompatible-type]\n\n            pendingSetSelf = {\n              effect,\n              value: newValue\n            };\n            return newValue;\n          } : unwrap(valueOrUpdater));\n        }\n      };\n\n      const resetSelf = effect => () => setSelf(effect)(DEFAULT_VALUE$7);\n\n      const onSet = effect => handler => {\n        var _cleanupEffectsByStor2;\n\n        const {\n          release\n        } = store.subscribeToTransactions(currentStore => {\n          var _currentTree$atomValu;\n\n          // eslint-disable-next-line prefer-const\n          let {\n            currentTree,\n            previousTree\n          } = currentStore.getState();\n\n          if (!previousTree) {\n            Recoil_recoverableViolation('Transaction subscribers notified without a next tree being present -- this is a bug in Recoil');\n            previousTree = currentTree; // attempt to trundle on\n          }\n\n          const newLoadable = (_currentTree$atomValu = currentTree.atomValues.get(key)) !== null && _currentTree$atomValu !== void 0 ? _currentTree$atomValu : defaultLoadable;\n\n          if (newLoadable.state === 'hasValue') {\n            var _previousTree$atomVal, _pendingSetSelf, _pendingSetSelf2, _pendingSetSelf3;\n\n            const newValue = newLoadable.contents;\n            const oldLoadable = (_previousTree$atomVal = previousTree.atomValues.get(key)) !== null && _previousTree$atomVal !== void 0 ? _previousTree$atomVal : defaultLoadable;\n            const oldValue = oldLoadable.state === 'hasValue' ? oldLoadable.contents : DEFAULT_VALUE$7; // TODO This isn't actually valid, use as a placeholder for now.\n            // Ignore atom value changes that were set via setSelf() in the same effect.\n            // We will still properly call the handler if there was a subsequent\n            // set from something other than an atom effect which was batched\n            // with the `setSelf()` call.  However, we may incorrectly ignore\n            // the handler if the subsequent batched call happens to set the\n            // atom to the exact same value as the `setSelf()`.   But, in that\n            // case, it was kind of a noop, so the semantics are debatable..\n\n            if (((_pendingSetSelf = pendingSetSelf) === null || _pendingSetSelf === void 0 ? void 0 : _pendingSetSelf.effect) !== effect || ((_pendingSetSelf2 = pendingSetSelf) === null || _pendingSetSelf2 === void 0 ? void 0 : _pendingSetSelf2.value) !== newValue) {\n              handler(newValue, oldValue, !currentTree.atomValues.has(key));\n            } else if (((_pendingSetSelf3 = pendingSetSelf) === null || _pendingSetSelf3 === void 0 ? void 0 : _pendingSetSelf3.effect) === effect) {\n              pendingSetSelf = null;\n            }\n          }\n        }, key);\n        cleanupEffectsByStore.set(store, [...((_cleanupEffectsByStor2 = cleanupEffectsByStore.get(store)) !== null && _cleanupEffectsByStor2 !== void 0 ? _cleanupEffectsByStor2 : []), release]);\n      };\n\n      for (const effect of effects) {\n        try {\n          const cleanup = effect({\n            node,\n            storeID: store.storeID,\n            parentStoreID_UNSTABLE: store.parentStoreID,\n            trigger,\n            setSelf: setSelf(effect),\n            resetSelf: resetSelf(effect),\n            onSet: onSet(effect),\n            getPromise,\n            getLoadable,\n            getInfo_UNSTABLE\n          });\n\n          if (cleanup != null) {\n            var _cleanupEffectsByStor3;\n\n            cleanupEffectsByStore.set(store, [...((_cleanupEffectsByStor3 = cleanupEffectsByStore.get(store)) !== null && _cleanupEffectsByStor3 !== void 0 ? _cleanupEffectsByStor3 : []), cleanup]);\n          }\n        } catch (error) {\n          initValue = error;\n          isInitError = true;\n        }\n      }\n\n      isDuringInit = false; // Mutate initial state in place since we know there are no other subscribers\n      // since we are the ones initializing on first use.\n\n      if (!(initValue instanceof DefaultValue$2)) {\n        var _store$getState$nextT5;\n\n        const initLoadable = isInitError ? loadableWithError$2(initValue) : Recoil_isPromise(initValue) ? loadableWithPromise$2(wrapPendingPromise(store, initValue)) : loadableWithValue$3(unwrap(initValue));\n        maybeFreezeValueOrPromise(initLoadable.contents);\n        initState.atomValues.set(key, initLoadable); // If there is a pending transaction, then also mutate the next state tree.\n        // This could happen if the atom was first initialized in an action that\n        // also updated some other atom's state.\n\n        (_store$getState$nextT5 = store.getState().nextTree) === null || _store$getState$nextT5 === void 0 ? void 0 : _store$getState$nextT5.atomValues.set(key, initLoadable);\n      }\n    }\n\n    return cleanupAtom;\n  }\n\n  function peekAtom(_store, state) {\n    var _ref, _state$atomValues$get3;\n\n    return (_ref = (_state$atomValues$get3 = state.atomValues.get(key)) !== null && _state$atomValues$get3 !== void 0 ? _state$atomValues$get3 : cachedAnswerForUnvalidatedValue) !== null && _ref !== void 0 ? _ref : defaultLoadable;\n  }\n\n  function getAtom(_store, state) {\n    if (state.atomValues.has(key)) {\n      // Atom value is stored in state:\n      return Recoil_nullthrows(state.atomValues.get(key));\n    } else if (state.nonvalidatedAtoms.has(key)) {\n      // Atom value is stored but needs validation before use.\n      // We might have already validated it and have a cached validated value:\n      if (cachedAnswerForUnvalidatedValue != null) {\n        return cachedAnswerForUnvalidatedValue;\n      }\n\n      if (persistence == null) {\n        Recoil_expectationViolation(`Tried to restore a persisted value for atom ${key} but it has no persistence settings.`);\n        return defaultLoadable;\n      }\n\n      const nonvalidatedValue = state.nonvalidatedAtoms.get(key);\n      const validatorResult = persistence.validator(nonvalidatedValue, DEFAULT_VALUE$7);\n      const validatedValueLoadable = validatorResult instanceof DefaultValue$2 ? defaultLoadable : loadableWithValue$3(validatorResult);\n      cachedAnswerForUnvalidatedValue = validatedValueLoadable;\n      return cachedAnswerForUnvalidatedValue;\n    } else {\n      return defaultLoadable;\n    }\n  }\n\n  function invalidateAtom() {\n    cachedAnswerForUnvalidatedValue = undefined;\n  }\n\n  function setAtom(_store, state, newValue) {\n    // Bail out if we're being set to the existing value, or if we're being\n    // reset but have no stored value (validated or unvalidated) to reset from:\n    if (state.atomValues.has(key)) {\n      const existing = Recoil_nullthrows(state.atomValues.get(key));\n\n      if (existing.state === 'hasValue' && newValue === existing.contents) {\n        return new Map();\n      }\n    } else if (!state.nonvalidatedAtoms.has(key) && newValue instanceof DefaultValue$2) {\n      return new Map();\n    }\n\n    maybeFreezeValueOrPromise(newValue);\n    cachedAnswerForUnvalidatedValue = undefined; // can be released now if it was previously in use\n\n    return new Map().set(key, loadableWithValue$3(newValue));\n  }\n\n  function shouldDeleteConfigOnReleaseAtom() {\n    return getConfigDeletionHandler$2(key) !== undefined && liveStoresCount <= 0;\n  }\n\n  const node = registerNode$2({\n    key,\n    nodeType: 'atom',\n    peek: peekAtom,\n    get: getAtom,\n    set: setAtom,\n    init: initAtom,\n    invalidate: invalidateAtom,\n    shouldDeleteConfigOnRelease: shouldDeleteConfigOnReleaseAtom,\n    dangerouslyAllowMutability: options.dangerouslyAllowMutability,\n    persistence_UNSTABLE: options.persistence_UNSTABLE ? {\n      type: options.persistence_UNSTABLE.type,\n      backButton: options.persistence_UNSTABLE.backButton\n    } : undefined,\n    shouldRestoreFromSnapshots: true,\n    retainedBy\n  });\n  return node;\n} // prettier-ignore\n\n\nfunction atom(options) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options.key !== 'string') {\n      throw Recoil_err('A key option with a unique string value must be provided when creating an atom.');\n    }\n  }\n\n  const { // @fb-only: scopeRules_APPEND_ONLY_READ_THE_DOCS,\n    ...restOptions\n  } = options;\n  const optionsDefault = 'default' in options ? // $FlowIssue[incompatible-type] No way to refine in Flow that property is not defined\n  options.default : new Promise(() => {});\n\n  if (isRecoilValue$4(optionsDefault) // Continue to use atomWithFallback for promise defaults for scoped atoms\n  // for now, since scoped atoms don't support async defaults\n  // @fb-only: || (isPromise(optionsDefault) && scopeRules_APPEND_ONLY_READ_THE_DOCS)\n  // @fb-only: || (isLoadable(optionsDefault) && scopeRules_APPEND_ONLY_READ_THE_DOCS)\n  ) {\n    return atomWithFallback({ ...restOptions,\n      default: optionsDefault // @fb-only: scopeRules_APPEND_ONLY_READ_THE_DOCS,\n\n    }); // @fb-only: } else if (scopeRules_APPEND_ONLY_READ_THE_DOCS\n    // @fb-only: && !isPromise(optionsDefault)\n    // @fb-only: && !isLoadable(optionsDefault)\n    // @fb-only: ) {\n    // @fb-only: return scopedAtom<T>({\n    // @fb-only: ...restOptions,\n    // @fb-only: default: unwrap<T>(optionsDefault),\n    // @fb-only: scopeRules_APPEND_ONLY_READ_THE_DOCS,\n    // @fb-only: });\n  } else {\n    return baseAtom({ ...restOptions,\n      default: optionsDefault\n    });\n  }\n}\n\nfunction atomWithFallback(options) {\n  const base = atom({ ...options,\n    default: DEFAULT_VALUE$7,\n    persistence_UNSTABLE: options.persistence_UNSTABLE === undefined ? undefined : { ...options.persistence_UNSTABLE,\n      validator: storedValue => storedValue instanceof DefaultValue$2 ? storedValue : Recoil_nullthrows(options.persistence_UNSTABLE).validator(storedValue, DEFAULT_VALUE$7)\n    },\n    // TODO Hack for now.\n    effects: options.effects,\n    // flowlint-line unclear-type: off\n    effects_UNSTABLE: options.effects_UNSTABLE // flowlint-line unclear-type: off\n\n  }); // $FlowFixMe[incompatible-call]\n\n  const sel = Recoil_selector({\n    key: `${options.key}__withFallback`,\n    get: ({\n      get\n    }) => {\n      const baseValue = get(base);\n      return baseValue instanceof DefaultValue$2 ? options.default : baseValue;\n    },\n    // $FlowFixMe[incompatible-call]\n    set: ({\n      set\n    }, newValue) => set(base, newValue),\n    // This selector does not need to cache as it is a wrapper selector\n    // and the selector within the wrapper selector will have a cache\n    // option by default\n    cachePolicy_UNSTABLE: {\n      eviction: 'most-recent'\n    },\n    dangerouslyAllowMutability: options.dangerouslyAllowMutability\n  });\n  setConfigDeletionHandler$1(sel.key, getConfigDeletionHandler$2(options.key));\n  return sel;\n} // $FlowFixMe[missing-local-annot]\n\n\natom.value = value => new WrappedValue$2(value);\n\nvar Recoil_atom = atom;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n * @oncall recoil\n */\n\nclass MapCache {\n  constructor(options) {\n    var _options$mapKey;\n\n    _defineProperty(this, \"_map\", void 0);\n\n    _defineProperty(this, \"_keyMapper\", void 0);\n\n    this._map = new Map();\n    this._keyMapper = (_options$mapKey = options === null || options === void 0 ? void 0 : options.mapKey) !== null && _options$mapKey !== void 0 ? _options$mapKey : v => v;\n  }\n\n  size() {\n    return this._map.size;\n  }\n\n  has(key) {\n    return this._map.has(this._keyMapper(key));\n  }\n\n  get(key) {\n    return this._map.get(this._keyMapper(key));\n  }\n\n  set(key, val) {\n    this._map.set(this._keyMapper(key), val);\n  }\n\n  delete(key) {\n    this._map.delete(this._keyMapper(key));\n  }\n\n  clear() {\n    this._map.clear();\n  }\n\n}\n\nvar Recoil_MapCache = {\n  MapCache\n};\n\nvar Recoil_MapCache_1 = Recoil_MapCache.MapCache;\n\nvar Recoil_MapCache$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  MapCache: Recoil_MapCache_1\n});\n\nconst {\n  LRUCache: LRUCache$2\n} = Recoil_LRUCache$1;\n\nconst {\n  MapCache: MapCache$1\n} = Recoil_MapCache$1;\n\n\n\n\n\n\n\nconst defaultPolicy$1 = {\n  equality: 'reference',\n  eviction: 'none',\n  maxSize: Infinity\n};\n\nfunction cacheFromPolicy({\n  equality = defaultPolicy$1.equality,\n  eviction = defaultPolicy$1.eviction,\n  maxSize = defaultPolicy$1.maxSize\n} = defaultPolicy$1) {\n  const valueMapper = getValueMapper$1(equality);\n  const cache = getCache(eviction, maxSize, valueMapper);\n  return cache;\n}\n\nfunction getValueMapper$1(equality) {\n  switch (equality) {\n    case 'reference':\n      return val => val;\n\n    case 'value':\n      return val => Recoil_stableStringify(val);\n  }\n\n  throw Recoil_err(`Unrecognized equality policy ${equality}`);\n}\n\nfunction getCache(eviction, maxSize, mapKey) {\n  switch (eviction) {\n    case 'keep-all':\n      return new MapCache$1({\n        mapKey\n      });\n\n    case 'lru':\n      return new LRUCache$2({\n        mapKey,\n        maxSize: Recoil_nullthrows(maxSize)\n      });\n\n    case 'most-recent':\n      return new LRUCache$2({\n        mapKey,\n        maxSize: 1\n      });\n  }\n\n  throw Recoil_err(`Unrecognized eviction policy ${eviction}`);\n}\n\nvar Recoil_cacheFromPolicy = cacheFromPolicy;\n\n// @fb-only: import type {ScopeRules} from 'Recoil_ScopedAtom';\n\n\nconst {\n  setConfigDeletionHandler: setConfigDeletionHandler$2\n} = Recoil_Node;\n\n\n\n\n\n// Process scopeRules to handle any entries which are functions taking parameters\n// prettier-ignore\n// @fb-only: function mapScopeRules<P>(\n// @fb-only: scopeRules?: ParameterizedScopeRules<P>,\n// @fb-only: param: P,\n// @fb-only: ): ScopeRules | void {\n// @fb-only: return scopeRules?.map(rule =>\n// @fb-only: Array.isArray(rule)\n// @fb-only: ? rule.map(entry => (typeof entry === 'function' ? entry(param) : entry))\n// @fb-only: : rule,\n// @fb-only: );\n// @fb-only: }\n\n/*\nA function which returns an atom based on the input parameter.\n\nEach unique parameter returns a unique atom. E.g.,\n\n  const f = atomFamily(...);\n  f({a: 1}) => an atom\n  f({a: 2}) => a different atom\n\nThis allows components to persist local, private state using atoms.  Each\ninstance of the component may have a different key, which it uses as the\nparameter for a family of atoms; in this way, each component will have\nits own atom not shared by other instances.  These state keys may be composed\ninto children's state keys as well.\n*/\nfunction atomFamily(options) {\n  var _options$cachePolicyF, _options$cachePolicyF2;\n\n  const atomCache = Recoil_cacheFromPolicy({\n    equality: (_options$cachePolicyF = (_options$cachePolicyF2 = options.cachePolicyForParams_UNSTABLE) === null || _options$cachePolicyF2 === void 0 ? void 0 : _options$cachePolicyF2.equality) !== null && _options$cachePolicyF !== void 0 ? _options$cachePolicyF : 'value',\n    eviction: 'keep-all'\n  }); // Simple atomFamily implementation to cache individual atoms based\n  // on the parameter value equality.\n\n  return params => {\n    var _stableStringify, _options$effects;\n\n    const cachedAtom = atomCache.get(params);\n\n    if (cachedAtom != null) {\n      return cachedAtom;\n    }\n\n    const {\n      cachePolicyForParams_UNSTABLE,\n      ...atomOptions\n    } = options;\n    const optionsDefault = 'default' in options ? // $FlowIssue[incompatible-type] No way to refine in Flow that property is not defined\n    options.default : new Promise(() => {});\n    const newAtom = Recoil_atom({ ...atomOptions,\n      key: `${options.key}__${(_stableStringify = Recoil_stableStringify(params)) !== null && _stableStringify !== void 0 ? _stableStringify : 'void'}`,\n      default: typeof optionsDefault === 'function' ? // The default was parameterized\n      // Flow doesn't know that T isn't a function, so we need to case to any\n      // $FlowIssue[incompatible-use]\n      optionsDefault(params) : // Default may be a static value, promise, or RecoilValue\n      optionsDefault,\n      retainedBy_UNSTABLE: typeof options.retainedBy_UNSTABLE === 'function' ? options.retainedBy_UNSTABLE(params) : options.retainedBy_UNSTABLE,\n      effects: typeof options.effects === 'function' ? options.effects(params) : typeof options.effects_UNSTABLE === 'function' ? options.effects_UNSTABLE(params) : (_options$effects = options.effects) !== null && _options$effects !== void 0 ? _options$effects : options.effects_UNSTABLE // prettier-ignore\n      // @fb-only: scopeRules_APPEND_ONLY_READ_THE_DOCS: mapScopeRules(\n      // @fb-only: options.scopeRules_APPEND_ONLY_READ_THE_DOCS,\n      // @fb-only: params,\n      // @fb-only: ),\n\n    });\n    atomCache.set(params, newAtom);\n    setConfigDeletionHandler$2(newAtom.key, () => {\n      atomCache.delete(params);\n    });\n    return newAtom;\n  };\n}\n\nvar Recoil_atomFamily = atomFamily;\n\nconst {\n  setConfigDeletionHandler: setConfigDeletionHandler$3\n} = Recoil_Node;\n\n\n\n\n\n // Keep in mind the parameter needs to be serializable as a cahche key\n// using Recoil_stableStringify\n\n\n// Add a unique index to each selector in case the cache implementation allows\n// duplicate keys based on equivalent stringified parameters\nlet nextIndex = 0;\n/* eslint-disable no-redeclare */\n\n// Return a function that returns members of a family of selectors of the same type\n// E.g.,\n//\n// const s = selectorFamily(...);\n// s({a: 1}) => a selector\n// s({a: 2}) => a different selector\n//\n// By default, the selectors are distinguished by distinct values of the\n// parameter based on value equality, not reference equality.  This allows using\n// object literals or other equivalent objects at callsites to not create\n// duplicate cache entries.  This behavior may be overridden with the\n// cacheImplementationForParams option.\nfunction selectorFamily(options) {\n  var _options$cachePolicyF, _options$cachePolicyF2;\n\n  const selectorCache = Recoil_cacheFromPolicy({\n    equality: (_options$cachePolicyF = (_options$cachePolicyF2 = options.cachePolicyForParams_UNSTABLE) === null || _options$cachePolicyF2 === void 0 ? void 0 : _options$cachePolicyF2.equality) !== null && _options$cachePolicyF !== void 0 ? _options$cachePolicyF : 'value',\n    eviction: 'keep-all'\n  });\n  return params => {\n    var _stableStringify;\n\n    // Throw an error with selector key so that it is clear which\n    // selector is causing an error\n    let cachedSelector;\n\n    try {\n      cachedSelector = selectorCache.get(params);\n    } catch (error) {\n      throw Recoil_err(`Problem with cache lookup for selector ${options.key}: ${error.message}`);\n    }\n\n    if (cachedSelector != null) {\n      return cachedSelector;\n    }\n\n    const myKey = `${options.key}__selectorFamily/${(_stableStringify = Recoil_stableStringify(params, {\n      // It is possible to use functions in parameters if the user uses\n      // a cache with reference equality thanks to the incrementing index.\n      allowFunctions: true\n    })) !== null && _stableStringify !== void 0 ? _stableStringify : 'void'}/${nextIndex++}`; // Append index in case values serialize to the same key string\n\n    const myGet = callbacks => options.get(params)(callbacks);\n\n    const myCachePolicy = options.cachePolicy_UNSTABLE;\n    const retainedBy = typeof options.retainedBy_UNSTABLE === 'function' ? options.retainedBy_UNSTABLE(params) : options.retainedBy_UNSTABLE;\n    let newSelector;\n\n    if (options.set != null) {\n      const set = options.set;\n\n      const mySet = (callbacks, newValue) => set(params)(callbacks, newValue);\n\n      newSelector = Recoil_selector({\n        key: myKey,\n        get: myGet,\n        set: mySet,\n        cachePolicy_UNSTABLE: myCachePolicy,\n        dangerouslyAllowMutability: options.dangerouslyAllowMutability,\n        retainedBy_UNSTABLE: retainedBy\n      });\n    } else {\n      newSelector = Recoil_selector({\n        key: myKey,\n        get: myGet,\n        cachePolicy_UNSTABLE: myCachePolicy,\n        dangerouslyAllowMutability: options.dangerouslyAllowMutability,\n        retainedBy_UNSTABLE: retainedBy\n      });\n    }\n\n    selectorCache.set(params, newSelector);\n    setConfigDeletionHandler$3(newSelector.key, () => {\n      selectorCache.delete(params);\n    });\n    return newSelector;\n  };\n}\n/* eslint-enable no-redeclare */\n\n\nvar Recoil_selectorFamily = selectorFamily;\n\n// flowlint-next-line unclear-type:off\n\n\nconst constantSelector = Recoil_selectorFamily({\n  key: '__constant',\n  get: constant => () => constant,\n  cachePolicyForParams_UNSTABLE: {\n    equality: 'reference'\n  }\n}); // Function that returns a selector which always produces the\n// same constant value.  It may be called multiple times with the\n// same value, based on reference equality, and will provide the\n// same selector.\n\nfunction constSelector(constant) {\n  return constantSelector(constant);\n}\n\nvar Recoil_constSelector = constSelector;\n\n// flowlint-next-line unclear-type:off\n\n\nconst throwingSelector = Recoil_selectorFamily({\n  key: '__error',\n  get: message => () => {\n    throw Recoil_err(message);\n  },\n  // TODO Why?\n  cachePolicyForParams_UNSTABLE: {\n    equality: 'reference'\n  }\n}); // Function that returns a selector which always throws an error\n// with the provided message.\n\nfunction errorSelector(message) {\n  return throwingSelector(message);\n}\n\nvar Recoil_errorSelector = errorSelector;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Wraps another recoil value and prevents writing to it.\n *\n * \n * @format\n * @oncall recoil\n */\n\nfunction readOnlySelector(atom) {\n  // flowlint-next-line unclear-type: off\n  return atom;\n}\n\nvar Recoil_readOnlySelector = readOnlySelector;\n\nconst {\n  loadableWithError: loadableWithError$3,\n  loadableWithPromise: loadableWithPromise$3,\n  loadableWithValue: loadableWithValue$4\n} = Recoil_Loadable$1;\n\n\n\n\n\n /////////////////\n//  TRUTH TABLE\n/////////////////\n// Dependencies        waitForNone         waitForAny        waitForAll       waitForAllSettled\n//  [loading, loading]  [Promise, Promise]  Promise           Promise         Promise\n//  [value, loading]    [value, Promise]    [value, Promise]  Promise         Promise\n//  [value, value]      [value, value]      [value, value]    [value, value]  [value, value]\n//\n//  [error, loading]    [Error, Promise]    [Error, Promise]  Error           Promise\n//  [error, error]      [Error, Error]      [Error, Error]    Error           [error, error]\n//  [value, error]      [value, Error]      [value, Error]    Error           [value, error]\n// Issue parallel requests for all dependencies and return the current\n// status if they have results, have some error, or are still pending.\n\n\nfunction concurrentRequests(getRecoilValue, deps) {\n  const results = Array(deps.length).fill(undefined);\n  const exceptions = Array(deps.length).fill(undefined);\n\n  for (const [i, dep] of deps.entries()) {\n    try {\n      results[i] = getRecoilValue(dep);\n    } catch (e) {\n      // exceptions can either be Promises of pending results or real errors\n      exceptions[i] = e;\n    }\n  }\n\n  return [results, exceptions];\n}\n\nfunction isError(exp) {\n  return exp != null && !Recoil_isPromise(exp);\n}\n\nfunction unwrapDependencies(dependencies) {\n  return Array.isArray(dependencies) ? dependencies : Object.getOwnPropertyNames(dependencies).map(key => dependencies[key]);\n}\n\nfunction wrapResults(dependencies,\n/* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n * LTI update could not be added via codemod */\nresults) {\n  return Array.isArray(dependencies) ? results : // Object.getOwnPropertyNames() has consistent key ordering with ES6\n  Object.getOwnPropertyNames(dependencies).reduce((out, key, idx) => ({ ...out,\n    [key]: results[idx]\n  }), {});\n}\n\nfunction wrapLoadables(dependencies, results, exceptions) {\n  const output = exceptions.map((exception, idx) => exception == null ? loadableWithValue$4(results[idx]) : Recoil_isPromise(exception) ? loadableWithPromise$3(exception) : loadableWithError$3(exception));\n  return wrapResults(dependencies, output);\n}\n\nfunction combineAsyncResultsWithSyncResults(syncResults, asyncResults) {\n  return asyncResults.map((result, idx) =>\n  /**\n   * it's important we use === undefined as opposed to == null, because the\n   * resolved value of the async promise could be `null`, in which case we\n   * don't want to use syncResults[idx], which would be undefined. If async\n   * promise resolves to `undefined`, that's ok because `syncResults[idx]`\n   * will also be `undefined`. That's a little hacky, but it works.\n   */\n  result === undefined ? syncResults[idx] : result);\n} // Selector that requests all dependencies in parallel and immediately returns\n// current results without waiting.\n\n\nconst waitForNone = Recoil_selectorFamily({\n  key: '__waitForNone',\n  get: dependencies => ({\n    get\n  }) => {\n    // Issue requests for all dependencies in parallel.\n    const deps = unwrapDependencies(dependencies);\n    const [results, exceptions] = concurrentRequests(get, deps); // Always return the current status of the results; never block.\n\n    return wrapLoadables(dependencies, results, exceptions);\n  },\n  dangerouslyAllowMutability: true\n}); // Selector that requests all dependencies in parallel and waits for at least\n// one to be available before returning results.  It will only error if all\n// dependencies have errors.\n\nconst waitForAny = Recoil_selectorFamily({\n  key: '__waitForAny',\n  get: dependencies => ({\n    get\n  }) => {\n    // Issue requests for all dependencies in parallel.\n    // Exceptions can either be Promises of pending results or real errors\n    const deps = unwrapDependencies(dependencies);\n    const [results, exceptions] = concurrentRequests(get, deps); // If any results are available, value or error, return the current status\n\n    if (exceptions.some(exp => !Recoil_isPromise(exp))) {\n      return wrapLoadables(dependencies, results, exceptions);\n    } // Otherwise, return a promise that will resolve when the next result is\n    // available, whichever one happens to be next.  But, if all pending\n    // dependencies end up with errors, then reject the promise.\n\n\n    return new Promise(resolve => {\n      for (const [i, exp] of exceptions.entries()) {\n        if (Recoil_isPromise(exp)) {\n          exp.then(result => {\n            results[i] = result;\n            exceptions[i] = undefined;\n            resolve(wrapLoadables(dependencies, results, exceptions));\n          }).catch(error => {\n            exceptions[i] = error;\n            resolve(wrapLoadables(dependencies, results, exceptions));\n          });\n        }\n      }\n    });\n  },\n  dangerouslyAllowMutability: true\n}); // Selector that requests all dependencies in parallel and waits for all to be\n// available before returning a value.  It will error if any dependencies error.\n\nconst waitForAll = Recoil_selectorFamily({\n  key: '__waitForAll',\n  get: dependencies => ({\n    get\n  }) => {\n    // Issue requests for all dependencies in parallel.\n    // Exceptions can either be Promises of pending results or real errors\n    const deps = unwrapDependencies(dependencies);\n    const [results, exceptions] = concurrentRequests(get, deps); // If all results are available, return the results\n\n    if (exceptions.every(exp => exp == null)) {\n      return wrapResults(dependencies, results);\n    } // If we have any errors, throw the first error\n\n\n    const error = exceptions.find(isError);\n\n    if (error != null) {\n      throw error;\n    } // Otherwise, return a promise that will resolve when all results are available\n\n\n    return Promise.all(exceptions).then(exceptionResults => wrapResults(dependencies, combineAsyncResultsWithSyncResults(results, exceptionResults)));\n  },\n  dangerouslyAllowMutability: true\n});\nconst waitForAllSettled = Recoil_selectorFamily({\n  key: '__waitForAllSettled',\n  get: dependencies => ({\n    get\n  }) => {\n    // Issue requests for all dependencies in parallel.\n    // Exceptions can either be Promises of pending results or real errors\n    const deps = unwrapDependencies(dependencies);\n    const [results, exceptions] = concurrentRequests(get, deps); // If all results are available, return the results\n\n    if (exceptions.every(exp => !Recoil_isPromise(exp))) {\n      return wrapLoadables(dependencies, results, exceptions);\n    } // Wait for all results to settle\n\n\n    return Promise.all(exceptions.map((exp, i) => Recoil_isPromise(exp) ? exp.then(result => {\n      results[i] = result;\n      exceptions[i] = undefined;\n    }).catch(error => {\n      results[i] = undefined;\n      exceptions[i] = error;\n    }) : null)) // Then wrap them as loadables\n    .then(() => wrapLoadables(dependencies, results, exceptions));\n  },\n  dangerouslyAllowMutability: true\n});\nconst noWait = Recoil_selectorFamily({\n  key: '__noWait',\n  get: dependency => ({\n    get\n  }) => {\n    try {\n      return Recoil_selector.value(loadableWithValue$4(get(dependency)));\n    } catch (exception) {\n      return Recoil_selector.value(Recoil_isPromise(exception) ? loadableWithPromise$3(exception) : loadableWithError$3(exception));\n    }\n  },\n  dangerouslyAllowMutability: true\n});\nvar Recoil_WaitFor = {\n  waitForNone,\n  waitForAny,\n  waitForAll,\n  waitForAllSettled,\n  noWait\n};\n\nconst {\n  RecoilLoadable\n} = Recoil_Loadable$1;\n\nconst {\n  DefaultValue: DefaultValue$3\n} = Recoil_Node;\n\nconst {\n  RecoilRoot: RecoilRoot$2,\n  useRecoilStoreID: useRecoilStoreID$1\n} = Recoil_RecoilRoot;\n\nconst {\n  isRecoilValue: isRecoilValue$5\n} = Recoil_RecoilValue$1;\n\nconst {\n  retentionZone: retentionZone$1\n} = Recoil_RetentionZone;\n\nconst {\n  freshSnapshot: freshSnapshot$2\n} = Recoil_Snapshot$1;\n\nconst {\n  useRecoilState: useRecoilState$1,\n  useRecoilState_TRANSITION_SUPPORT_UNSTABLE: useRecoilState_TRANSITION_SUPPORT_UNSTABLE$1,\n  useRecoilStateLoadable: useRecoilStateLoadable$1,\n  useRecoilValue: useRecoilValue$1,\n  useRecoilValue_TRANSITION_SUPPORT_UNSTABLE: useRecoilValue_TRANSITION_SUPPORT_UNSTABLE$1,\n  useRecoilValueLoadable: useRecoilValueLoadable$1,\n  useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE: useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE$1,\n  useResetRecoilState: useResetRecoilState$1,\n  useSetRecoilState: useSetRecoilState$1\n} = Recoil_Hooks;\n\nconst {\n  useGotoRecoilSnapshot: useGotoRecoilSnapshot$1,\n  useRecoilSnapshot: useRecoilSnapshot$1,\n  useRecoilTransactionObserver: useRecoilTransactionObserver$1\n} = Recoil_SnapshotHooks;\n\n\n\n\n\nconst {\n  useRecoilCallback: useRecoilCallback$1\n} = Recoil_useRecoilCallback;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst {\n  noWait: noWait$1,\n  waitForAll: waitForAll$1,\n  waitForAllSettled: waitForAllSettled$1,\n  waitForAny: waitForAny$1,\n  waitForNone: waitForNone$1\n} = Recoil_WaitFor;\n\n\n\nvar Recoil_index = {\n  // Types\n  DefaultValue: DefaultValue$3,\n  isRecoilValue: isRecoilValue$5,\n  RecoilLoadable,\n  // Global Recoil environment settiongs\n  RecoilEnv: Recoil_RecoilEnv,\n  // Recoil Root\n  RecoilRoot: RecoilRoot$2,\n  useRecoilStoreID: useRecoilStoreID$1,\n  useRecoilBridgeAcrossReactRoots_UNSTABLE: Recoil_useRecoilBridgeAcrossReactRoots,\n  // Atoms/Selectors\n  atom: Recoil_atom,\n  selector: Recoil_selector,\n  // Convenience Atoms/Selectors\n  atomFamily: Recoil_atomFamily,\n  selectorFamily: Recoil_selectorFamily,\n  constSelector: Recoil_constSelector,\n  errorSelector: Recoil_errorSelector,\n  readOnlySelector: Recoil_readOnlySelector,\n  // Concurrency Helpers for Atoms/Selectors\n  noWait: noWait$1,\n  waitForNone: waitForNone$1,\n  waitForAny: waitForAny$1,\n  waitForAll: waitForAll$1,\n  waitForAllSettled: waitForAllSettled$1,\n  // Hooks for Atoms/Selectors\n  useRecoilValue: useRecoilValue$1,\n  useRecoilValueLoadable: useRecoilValueLoadable$1,\n  useRecoilState: useRecoilState$1,\n  useRecoilStateLoadable: useRecoilStateLoadable$1,\n  useSetRecoilState: useSetRecoilState$1,\n  useResetRecoilState: useResetRecoilState$1,\n  useGetRecoilValueInfo_UNSTABLE: Recoil_useGetRecoilValueInfo,\n  useRecoilRefresher_UNSTABLE: Recoil_useRecoilRefresher,\n  useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE: useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE$1,\n  useRecoilValue_TRANSITION_SUPPORT_UNSTABLE: useRecoilValue_TRANSITION_SUPPORT_UNSTABLE$1,\n  useRecoilState_TRANSITION_SUPPORT_UNSTABLE: useRecoilState_TRANSITION_SUPPORT_UNSTABLE$1,\n  // Hooks for complex operations\n  useRecoilCallback: useRecoilCallback$1,\n  useRecoilTransaction_UNSTABLE: Recoil_useRecoilTransaction,\n  // Snapshots\n  useGotoRecoilSnapshot: useGotoRecoilSnapshot$1,\n  useRecoilSnapshot: useRecoilSnapshot$1,\n  useRecoilTransactionObserver_UNSTABLE: useRecoilTransactionObserver$1,\n  snapshot_UNSTABLE: freshSnapshot$2,\n  // Memory Management\n  useRetain: Recoil_useRetain,\n  retentionZone: retentionZone$1\n};\nvar Recoil_index_1 = Recoil_index.DefaultValue;\nvar Recoil_index_2 = Recoil_index.isRecoilValue;\nvar Recoil_index_3 = Recoil_index.RecoilLoadable;\nvar Recoil_index_4 = Recoil_index.RecoilEnv;\nvar Recoil_index_5 = Recoil_index.RecoilRoot;\nvar Recoil_index_6 = Recoil_index.useRecoilStoreID;\nvar Recoil_index_7 = Recoil_index.useRecoilBridgeAcrossReactRoots_UNSTABLE;\nvar Recoil_index_8 = Recoil_index.atom;\nvar Recoil_index_9 = Recoil_index.selector;\nvar Recoil_index_10 = Recoil_index.atomFamily;\nvar Recoil_index_11 = Recoil_index.selectorFamily;\nvar Recoil_index_12 = Recoil_index.constSelector;\nvar Recoil_index_13 = Recoil_index.errorSelector;\nvar Recoil_index_14 = Recoil_index.readOnlySelector;\nvar Recoil_index_15 = Recoil_index.noWait;\nvar Recoil_index_16 = Recoil_index.waitForNone;\nvar Recoil_index_17 = Recoil_index.waitForAny;\nvar Recoil_index_18 = Recoil_index.waitForAll;\nvar Recoil_index_19 = Recoil_index.waitForAllSettled;\nvar Recoil_index_20 = Recoil_index.useRecoilValue;\nvar Recoil_index_21 = Recoil_index.useRecoilValueLoadable;\nvar Recoil_index_22 = Recoil_index.useRecoilState;\nvar Recoil_index_23 = Recoil_index.useRecoilStateLoadable;\nvar Recoil_index_24 = Recoil_index.useSetRecoilState;\nvar Recoil_index_25 = Recoil_index.useResetRecoilState;\nvar Recoil_index_26 = Recoil_index.useGetRecoilValueInfo_UNSTABLE;\nvar Recoil_index_27 = Recoil_index.useRecoilRefresher_UNSTABLE;\nvar Recoil_index_28 = Recoil_index.useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE;\nvar Recoil_index_29 = Recoil_index.useRecoilValue_TRANSITION_SUPPORT_UNSTABLE;\nvar Recoil_index_30 = Recoil_index.useRecoilState_TRANSITION_SUPPORT_UNSTABLE;\nvar Recoil_index_31 = Recoil_index.useRecoilCallback;\nvar Recoil_index_32 = Recoil_index.useRecoilTransaction_UNSTABLE;\nvar Recoil_index_33 = Recoil_index.useGotoRecoilSnapshot;\nvar Recoil_index_34 = Recoil_index.useRecoilSnapshot;\nvar Recoil_index_35 = Recoil_index.useRecoilTransactionObserver_UNSTABLE;\nvar Recoil_index_36 = Recoil_index.snapshot_UNSTABLE;\nvar Recoil_index_37 = Recoil_index.useRetain;\nvar Recoil_index_38 = Recoil_index.retentionZone;\n\nexport default Recoil_index;\nexport { Recoil_index_1 as DefaultValue, Recoil_index_4 as RecoilEnv, Recoil_index_3 as RecoilLoadable, Recoil_index_5 as RecoilRoot, Recoil_index_8 as atom, Recoil_index_10 as atomFamily, Recoil_index_12 as constSelector, Recoil_index_13 as errorSelector, Recoil_index_2 as isRecoilValue, Recoil_index_15 as noWait, Recoil_index_14 as readOnlySelector, Recoil_index_38 as retentionZone, Recoil_index_9 as selector, Recoil_index_11 as selectorFamily, Recoil_index_36 as snapshot_UNSTABLE, Recoil_index_26 as useGetRecoilValueInfo_UNSTABLE, Recoil_index_33 as useGotoRecoilSnapshot, Recoil_index_7 as useRecoilBridgeAcrossReactRoots_UNSTABLE, Recoil_index_31 as useRecoilCallback, Recoil_index_27 as useRecoilRefresher_UNSTABLE, Recoil_index_34 as useRecoilSnapshot, Recoil_index_22 as useRecoilState, Recoil_index_23 as useRecoilStateLoadable, Recoil_index_30 as useRecoilState_TRANSITION_SUPPORT_UNSTABLE, Recoil_index_6 as useRecoilStoreID, Recoil_index_35 as useRecoilTransactionObserver_UNSTABLE, Recoil_index_32 as useRecoilTransaction_UNSTABLE, Recoil_index_20 as useRecoilValue, Recoil_index_21 as useRecoilValueLoadable, Recoil_index_28 as useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE, Recoil_index_29 as useRecoilValue_TRANSITION_SUPPORT_UNSTABLE, Recoil_index_25 as useResetRecoilState, Recoil_index_37 as useRetain, Recoil_index_24 as useSetRecoilState, Recoil_index_18 as waitForAll, Recoil_index_19 as waitForAllSettled, Recoil_index_17 as waitForAny, Recoil_index_16 as waitForNone };\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAkB;AAClB,uBAAqB;AAarB,SAAS,IAAI,SAAS;AACpB,QAAM,QAAQ,IAAI,MAAM,OAAO;AAG/B,MAAI,MAAM,UAAU,QAAW;AAE7B,QAAI;AACF,YAAM;AAAA,IACR,SAAS,GAAG;AAAA,IAAC;AAAA,EAEf;AAEA,SAAO;AACT;AAEA,IAAI,QAAQ;AAKZ,IAAI,aAAa;AAiBjB,SAAS,UAAU,GAAG;AACpB,SAAO,CAAC,CAAC,KAAK,OAAO,EAAE,SAAS;AAClC;AAEA,IAAI,mBAAmB;AAEvB,SAAS,WAAW,GAAG,SAAS;AAC9B,MAAI,KAAK,MAAM;AACb,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,YAAY,QAAQ,YAAY,SAAS,UAAU,kCAAkC;AACxG;AAEA,IAAI,oBAAoB;AAExB,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,IAAM,eAAN,MAAmB;AAAA,EACjB,WAAW;AACT,UAAM,WAAW,cAAc;AAAA,EACjC;AAAA,EAEA,YAAY;AACV,UAAM,WAAW,cAAc;AAAA,EACjC;AAAA,EAEA,aAAa;AACX,UAAM,WAAW,cAAc;AAAA,EACjC;AAAA,EAEA,eAAe;AAEb,UAAM,WAAW,oCAAoC,KAAK,KAAK,SAAS;AAAA,EAC1E;AAAA,EAEA,eAAe;AACb,UAAM,WAAW,cAAc;AAAA,EACjC;AAAA,EAEA,iBAAiB;AAEf,UAAM,WAAW,sCAAsC,KAAK,KAAK,SAAS;AAAA,EAC5E;AAAA,EAEA,aAAa;AACX,UAAM,WAAW,cAAc;AAAA,EACjC;AAAA,EAEA,eAAe;AAEb,UAAM,WAAW,oCAAoC,KAAK,KAAK,SAAS;AAAA,EAC1E;AAAA,EAEA,GAAG,OAAO;AAER,WAAO,MAAM,UAAU,KAAK,SAAS,MAAM,aAAa,KAAK;AAAA,EAC/D;AAAA,EAEA,IAAI,MAAM;AACR,UAAM,WAAW,cAAc;AAAA,EACjC;AAEF;AAEA,IAAM,gBAAN,cAA4B,aAAa;AAAA,EACvC,YAAY,OAAO;AACjB,UAAM;AAEN,oBAAgB,MAAM,SAAS,UAAU;AAEzC,oBAAgB,MAAM,YAAY,MAAM;AAExC,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,YAAY;AACV,WAAO,QAAQ,QAAQ,KAAK,QAAQ;AAAA,EACtC;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,eAAe;AACb,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,KAAK;AACP,QAAI;AACF,YAAM,OAAO,IAAI,KAAK,QAAQ;AAC9B,aAAO,iBAAiB,IAAI,IAAI,oBAAoB,IAAI,IAAI,WAAW,IAAI,IAAI,OAAO,kBAAkB,IAAI;AAAA,IAC9G,SAAS,GAAG;AACV,aAAO,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA,QAGzB,oBAAoB,EAAE,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,UAAI,kBAAkB,CAAC;AAAA,IACxE;AAAA,EACF;AAEF;AAEA,IAAM,gBAAN,cAA4B,aAAa;AAAA,EACvC,YAAY,OAAO;AACjB,UAAM;AAEN,oBAAgB,MAAM,SAAS,UAAU;AAEzC,oBAAgB,MAAM,YAAY,MAAM;AAExC,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,WAAW;AACT,UAAM,KAAK;AAAA,EACb;AAAA,EAEA,YAAY;AACV,WAAO,QAAQ,OAAO,KAAK,QAAQ;AAAA,EACrC;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EAEA,eAAe;AACb,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,MAAM;AAER,WAAO;AAAA,EACT;AAEF;AAEA,IAAM,kBAAN,cAA8B,aAAa;AAAA,EACzC,YAAY,SAAS;AACnB,UAAM;AAEN,oBAAgB,MAAM,SAAS,SAAS;AAExC,oBAAgB,MAAM,YAAY,MAAM;AAExC,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,WAAW;AACT,UAAM,KAAK;AAAA,EACb;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EAEA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,oBAAoB,KAAK,SAAS,KAAK,WAAS;AACrD,YAAM,OAAO,IAAI,KAAK;AAEtB,UAAI,WAAW,IAAI,GAAG;AACpB,cAAM,eAAe;AAErB,gBAAQ,aAAa,OAAO;AAAA,UAC1B,KAAK;AACH,mBAAO,aAAa;AAAA,UAEtB,KAAK;AACH,kBAAM,aAAa;AAAA,UAErB,KAAK;AACH,mBAAO,aAAa;AAAA,QACxB;AAAA,MACF;AAGA,aAAO;AAAA,IACT,CAAC,EACA,MAAM,OAAK;AACV,UAAI,iBAAiB,CAAC,GAAG;AAEvB,eAAO,EAAE,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE,QAAQ;AAAA,MAC5C;AAEA,YAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAEF;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,OAAO,OAAO,IAAI,cAAc,KAAK,CAAC;AAC/C;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,OAAO,OAAO,IAAI,cAAc,KAAK,CAAC;AAC/C;AAEA,SAAS,oBAAoB,SAAS;AACpC,SAAO,OAAO,OAAO,IAAI,gBAAgB,OAAO,CAAC;AACnD;AAEA,SAAS,kBAAkB;AACzB,SAAO,OAAO,OAAO,IAAI,gBAAgB,IAAI,QAAQ,MAAM;AAAA,EAAC,CAAC,CAAC,CAAC;AACjE;AAEA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,OAAO,MAAM,OAAK,EAAE,UAAU,UAAU,IAAI,kBAAkB,OAAO,IAAI,OAAK,EAAE,QAAQ,CAAC,IAAI,OAAO,KAAK,OAAK,EAAE,UAAU,UAAU,IAAI,kBAAkB,kBAAkB,OAAO,KAAK,OAAK,EAAE,UAAU,UAAU,GAAG,wCAAwC,EAAE,QAAQ,IAAI,oBAAoB,QAAQ,IAAI,OAAO,IAAI,OAAK,EAAE,QAAQ,CAAC,CAAC;AACjV;AAEA,SAAS,YAAY,QAAQ;AAC3B,QAAM,iBAAiB,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,oBAAoB,MAAM,EAAE,IAAI,SAAO,OAAO,GAAG,CAAC;AACjH,QAAM,mBAAmB,eAAe,IAAI,OAAK,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,oBAAoB,CAAC,IAAI,kBAAkB,CAAC,CAAC;AACxI,QAAM,SAAS,iBAAiB,gBAAgB;AAChD,SAAO,MAAM,QAAQ,MAAM;AAAA;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA,IAEA,OAAO,IAAI,aAAW,OAAO,oBAAoB,MAAM,EAAE;AAAA;AAAA,MACzD,CAAC,KAAK,KAAK,SAAS;AAAA,QAAE,GAAG;AAAA,QACvB,CAAC,GAAG,GAAG,QAAQ,GAAG;AAAA,MACpB;AAAA,MAAI,CAAC;AAAA,IAAC,CAAC;AAAA;AACT;AAEA,SAAS,WAAW,GAAG;AACrB,SAAO,aAAa;AACtB;AAEA,IAAM,0BAA0B;AAAA,EAC9B,IAAI,WAAS,iBAAiB,KAAK,IAAI,oBAAoB,KAAK,IAAI,WAAW,KAAK,IAAI,QAAQ,kBAAkB,KAAK;AAAA,EACvH,OAAO,WAAS,kBAAkB,KAAK;AAAA;AAAA,EAEvC,SAAS,MAAM,gBAAgB;AAAA;AAAA,EAE/B,KAAK;AAAA,EACL;AACF;AACA,IAAI,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAClB;AAEA,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AAExC,IAAI,oBAAiC,OAAO,OAAO;AAAA,EACjD,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,gBAAgB;AAClB,CAAC;AAED,IAAM,MAAM;AAAA,EACV,4CAA4C;AAAA;AAAA,EAE5C,oBAAoB,oBAAI,IAAI,CAAC,oBAAoB,8BAA8B,wCAAwC,+BAA+B,CAAC;AACzJ;AAEA,SAAS,0BAA0B,MAAM,KAAK;AAC5C,MAAI,mBAAmB;AAEvB,QAAM,kBAAkB,oBAAoB,QAAQ,IAAI,IAAI,OAAO,QAAQ,sBAAsB,SAAS,UAAU,wBAAwB,kBAAkB,YAAY,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK;AAEhQ,MAAI,kBAAkB,QAAQ,mBAAmB,IAAI;AACnD;AAAA,EACF;AAEA,QAAM,gBAAgB,CAAC,QAAQ,OAAO;AAEtC,MAAI,CAAC,cAAc,SAAS,cAAc,GAAG;AAC3C,UAAM,WAAW,eAAe,IAAI,6CAA6C,cAAc,EAAE;AAAA,EACnG;AAEA,MAAI,mBAAmB,MAAM;AAC/B;AAEA,SAAS,8BAA8B,MAAM,KAAK;AAChD,MAAI;AAEJ,QAAM,kBAAkB,qBAAqB,QAAQ,IAAI,IAAI,OAAO,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,KAAK;AAE7I,MAAI,kBAAkB,QAAQ,mBAAmB,IAAI;AACnD;AAAA,EACF;AAEA,MAAI,eAAe,MAAM,aAAa,CAAC;AACzC;AASA,SAAS,+BAA+B;AACtC,MAAI;AAIJ,MAAI,OAAO,YAAY,aAAa;AAClC;AAAA,EACF;AAEA,QAAM,WAAW,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ,MAAM;AAC1F;AAAA,EACF;AAEA,4BAA0B,8CAA8C,WAAS;AAC/E,QAAI,6CAA6C;AAAA,EACnD,CAAC;AACD,gCAA8B,sBAAsB,WAAS;AAC3D,UAAM,QAAQ,QAAM;AAClB,UAAI,mBAAmB,IAAI,EAAE;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH;AAEA,6BAA6B;AAC7B,IAAI,mBAAmB;AAEvB,SAAS,eAAe,IAAI;AAC1B,SAAO,iBAAiB,mBAAmB,IAAI,EAAE;AACnD;AAEA,eAAe,UAAU,QAAM;AAC7B,mBAAiB,mBAAmB,IAAI,EAAE;AAC5C;AAEA,eAAe,UAAU,QAAM;AAC7B,mBAAiB,mBAAmB,OAAO,EAAE;AAC/C;AAEA,eAAe,QAAQ,MAAM;AAC3B,mBAAiB,mBAAmB,MAAM;AAC5C;AAEA,IAAI,aAAa;AAajB,SAAS,qBAAqB,SAAS,cAAc;AAAA,EACnD;AACF,IAAI,CAAC,GAAG;AACN,MAAI,MAAuC;AACzC,YAAQ,MAAM,SAAS,KAAK;AAAA,EAC9B;AAEA,SAAO;AACT;AAEA,IAAI,yBAAyB;AAK7B,IAAI,8BAA8B;AAElC,IAAI;AAAJ,IAA0B;AAA1B,IAA6C;AAQ7C,IAAM;AAAA;AAAA,GACL,uBAAuB,aAAAA,QAAM,yBAAyB,QAAQ,yBAAyB,SAAS,uBAAuB,aAAAA,QAAM;AAAA;AAC9H,IAAM;AAAA;AAAA,GACL,oBAAoB,aAAAA,QAAM,sBAAsB,QAAQ,sBAAsB,SAAS,oBAAoB,aAAAA,QAAM;AAAA;AAElH,IAAM;AAAA;AAAA,GACL,wBAAwB,aAAAA,QAAM,0BAA0B,QAAQ,0BAA0B,SAAS;AAAA;AAAA,IACpG,aAAAA,QAAM;AAAA;AAAA;AACN,IAAI,uCAAuC;AAI3C,SAAS,8CAA8C;AACrD,MAAI;AAGJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,IAIA,aAAAA,QAAM;AAAA;AACN,QAAM,cAAc,wBAAwB,2BAA2B,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,aAAa,QAAQ,0BAA0B,SAAS,wBAAwB,kBAAkB;AACrP,QAAM,kCAAkC,WAAW,wBAAwB;AAE3E,MAAI,wBAAwB,CAAC,mCAAmC,CAAC,sCAAsC;AACrG,2CAAuC;AACvC,gCAA4B,8EAA8E;AAAA,EAC5G;AAEA,SAAO;AACT;AAUA,SAAS,YAAY;AAGnB,MAAI,WAAW,2BAA2B,GAAG;AAC3C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,MAAI,WAAW,4BAA4B,KAAK,wBAAwB,MAAM;AAC5E,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,MAAI,WAAW,uBAAuB,KAAK,oBAAoB,QAAQ,OAAO,WAAW,eAAe,CAAC,OAAO,uDAAuD;AACrK,WAAO,WAAW,sCAAsC,IAAI;AAAA,MAC1D,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd,IAAI;AAAA,MACF,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,EACF;AAEA,SAAO,WAAW,sCAAsC,IAAI;AAAA,IAC1D,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAI;AAAA,IACF,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AAGA,SAAS,uBAAuB;AAG9B,SAAO;AACT;AAEA,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAcA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,QAAQ;AAClB,oBAAgB,MAAM,OAAO,MAAM;AAEnC,SAAK,MAAM;AAAA,EACb;AAAA,EAEA,SAAS;AACP,WAAO;AAAA,MACL,KAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAEF;AAEA,IAAM,cAAN,cAA0B,oBAAoB;AAAC;AAE/C,IAAM,sBAAN,cAAkC,oBAAoB;AAAC;AAEvD,SAAS,cAAc,GAAG;AACxB,SAAO,aAAa,eAAe,aAAa;AAClD;AAEA,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,uBAAuB,mBAAmB;AAC9C,IAAI,uBAAuB,mBAAmB;AAC9C,IAAI,uBAAuB,mBAAmB;AAC9C,IAAI,uBAAuB,mBAAmB;AAE9C,IAAI,uBAAoC,OAAO,OAAO;AAAA,EACpD,WAAW;AAAA,EACX,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,eAAe;AACjB,CAAC;AAaD,SAAS,QAAQ,WAAW,MAAM;AAChC,MAAI,QAAQ;AACZ,SAAO,OAAO,QAAQ,OAAO,MAAM,OAAO,KAAK,OAAO,CAAC,CAAC;AAC1D;AAEA,IAAI,YAAY;AAEhB,SAAS,qBAAqB,WAAW,MAAM;AAC7C,MAAI,MAAuC;AACzC,UAAM,UAAU,UAAU,KAAK,MAAM,QAAQ,GAAG,IAAI;AACpD,UAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,UAAM,OAAO;AACb,YAAQ,MAAM,KAAK;AAAA,EACrB;AACF;AAEA,IAAI,yBAAyB;AAK7B,IAAI,8BAA8B;AAiBlC,SAAS,YAAY,UAAU,UAAU;AAEvC,SAAO,aAAa;AAClB,QAAI,QAAQ;AAEZ,eAAW,SAAS,UAAU;AAC5B,YAAM,SAAS,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,EAAE;AACJ;AAEA,IAAI,qBAAqB;AAEzB,IAAM;AAAA,EACJ,sBAAsB;AACxB,IAAI;AAgBJ,IAAM,eAAN,MAAmB;AAAC;AAEpB,IAAM,gBAAgB,IAAI,aAAa;AAEvC,IAAM,QAAQ,oBAAI,IAAI;AAEtB,IAAM,eAAe,oBAAI,IAAI;AAG7B,SAAS,oBAAoB,MAAM;AACjC,SAAO,mBAAmB,MAAM,SAAO,kBAAkB,aAAa,IAAI,GAAG,CAAC,CAAC;AACjF;AAEA,SAAS,yBAAyB,KAAK;AACrC,MAAI,MAAM,IAAI,GAAG,GAAG;AAClB,UAAM,UAAU,uBAAuB,GAAG;AAAA;AAAA;AAI1C,QAAI,MAAuC;AAEzC,UAAI,CAAC,uBAAuB,GAAG;AAC7B,oCAA4B,SAAS,QAAQ;AAAA,MAC/C;AAAA,IACF,OAAO;AAEL,cAAQ,KAAK,OAAO;AAAA,IACtB;AAAA,EACF;AACF;AAEA,SAAS,aAAa,MAAM;AAC1B,MAAI,iBAAiB,4CAA4C;AAC/D,6BAAyB,KAAK,GAAG;AAAA,EACnC;AAEA,QAAM,IAAI,KAAK,KAAK,IAAI;AACxB,QAAM,cAAc,KAAK,OAAO,OAAO,IAAI,qBAAqB,oBAAoB,KAAK,GAAG,IAAI,IAAI,qBAAqB,YAAY,KAAK,GAAG;AAC7I,eAAa,IAAI,KAAK,KAAK,WAAW;AACtC,SAAO;AACT;AAIA,IAAM,mBAAN,cAA+B,MAAM;AAAC;AAGtC,SAAS,QAAQ,KAAK;AACpB,QAAM,OAAO,MAAM,IAAI,GAAG;AAE1B,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,iBAAiB,wCAAwC,GAAG,IAAI;AAAA,EAC5E;AAEA,SAAO;AACT;AAGA,SAAS,aAAa,KAAK;AACzB,SAAO,MAAM,IAAI,GAAG;AACtB;AAEA,IAAM,yBAAyB,oBAAI,IAAI;AAEvC,SAAS,2BAA2B,KAAK;AACvC,MAAI;AAEJ,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAEA,QAAM,OAAO,MAAM,IAAI,GAAG;AAE1B,MAAI,SAAS,QAAQ,SAAS,WAAW,wBAAwB,KAAK,iCAAiC,QAAQ,0BAA0B,UAAU,sBAAsB,KAAK,IAAI,GAAG;AACnL,QAAI;AAEJ,UAAM,OAAO,GAAG;AAChB,KAAC,wBAAwB,yBAAyB,GAAG,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACtI,2BAAuB,OAAO,GAAG;AAAA,EACnC;AACF;AAEA,SAAS,yBAAyB,KAAK,IAAI;AACzC,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAEA,MAAI,OAAO,QAAW;AACpB,2BAAuB,OAAO,GAAG;AAAA,EACnC,OAAO;AACL,2BAAuB,IAAI,KAAK,EAAE;AAAA,EACpC;AACF;AAEA,SAAS,yBAAyB,KAAK;AACrC,SAAO,uBAAuB,IAAI,GAAG;AACvC;AAEA,IAAI,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAaA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,IAAE;AACJ;AAEA,IAAI,eAAe;AAAA,EACjB;AACF;AAEA,SAAS,qBAAqB,IAAI,QAAQ;AACzC,SAAO,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACrE;AAEA,IAAI,SAAS,qBAAqB,SAAU,QAAQ;AAEpD,MAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AACjG,WAAO,OAAO;AAAA,EAChB,IAAI,SAAU,KAAK;AACjB,WAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,EAC3H;AAQA,MAAI,OAAO,CAAC;AAKZ,MAAI,OAAO;AACX,MAAI,cAAc,KAAK,IAAI,GAAG,IAAI;AAClC,MAAI,OAAO,cAAc;AACzB,MAAI,iBAAiB,cAAc;AACnC,MAAI,iBAAiB,cAAc;AAInC,MAAI,UAAU,CAAC;AAEf,MAAI,WAAW,SAASC,UAAS,GAAG;AAClC,WAAO,WAAY;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AASA,MAAI,OAAO,KAAK,OAAO,SAAU,KAAK;AACpC,QAAI,OAAO,OAAO,QAAQ,cAAc,cAAc,QAAQ,GAAG;AACjE,QAAI,SAAS,SAAU,QAAO;AAC9B,QAAI,SAAS,SAAU,QAAO;AAC9B,QAAIC,QAAO;AAEX,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC9C,UAAI,IAAI,IAAI,WAAW,CAAC;AACxB,MAAAA,SAAQA,SAAQ,KAAKA,QAAO,IAAI;AAAA,IAClC;AAEA,WAAOA;AAAA,EACT;AAWA,MAAI,WAAW,SAASC,UAAS,GAAG;AAClC,SAAK,KAAK,IAAI;AACd,SAAK,IAAI,cAAe,KAAK,IAAI;AACjC,QAAI,KAAK,KAAK,KAAK;AACnB,SAAK,KAAK;AACV,SAAK,KAAK;AACV,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,eAAe,SAASC,cAAa,OAAO,GAAG;AACjD,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,MAAI,WAAW,SAASC,UAAS,GAAG;AAClC,WAAO,KAAK;AAAA,EACd;AAEA,MAAI,aAAa,SAASC,YAAW,QAAQ,KAAK;AAChD,WAAO,SAAS,SAAS,MAAM,CAAC;AAAA,EAClC;AAcA,MAAI,cAAc,SAASC,aAAYC,SAAQ,IAAI,GAAG,KAAK;AACzD,QAAI,MAAM;AAEV,QAAI,CAACA,SAAQ;AACX,UAAI,MAAM,IAAI;AACd,YAAM,IAAI,MAAM,GAAG;AAEnB,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAI,CAAC,IAAI,IAAI,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,EAAE,IAAI;AACV,WAAO;AAAA,EACT;AAUA,MAAI,iBAAiB,SAASC,gBAAeD,SAAQ,IAAI,KAAK;AAC5D,QAAI,SAAS,IAAI,SAAS;AAC1B,QAAI,IAAI;AACR,QAAI,IAAI;AACR,QAAI,MAAM;AAEV,QAAIA,SAAQ;AACV,UAAI,IAAI;AAAA,IACV,OAAO;AACL,YAAM,IAAI,MAAM,MAAM;AAEtB,aAAO,IAAI,IAAI;AACb,YAAI,GAAG,IAAI,IAAI,GAAG;AAAA,MACpB;AAAA,IACF;AAEA,MAAE;AAEF,WAAO,KAAK,QAAQ;AAClB,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACpB;AAEA,QAAIA,SAAQ;AACV,UAAI,SAAS;AAAA,IACf;AAEA,WAAO;AAAA,EACT;AAWA,MAAI,gBAAgB,SAASE,eAAcF,SAAQ,IAAI,GAAG,KAAK;AAC7D,QAAI,MAAM,IAAI;AAEd,QAAIA,SAAQ;AACV,UAAI,KAAK;AAET,aAAO,MAAM,IAAI;AACf,YAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MACpB;AAEA,UAAI,EAAE,IAAI;AACV,aAAO;AAAA,IACT;AAEA,QAAI,IAAI,GACJ,IAAI;AACR,QAAI,MAAM,IAAI,MAAM,MAAM,CAAC;AAE3B,WAAO,IAAI,IAAI;AACb,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACpB;AAEA,QAAI,EAAE,IAAI;AAEV,WAAO,IAAI,KAAK;AACd,UAAI,EAAE,CAAC,IAAI,IAAI,GAAG;AAAA,IACpB;AAEA,WAAO;AAAA,EACT;AAKA,MAAI,OAAO;AACX,MAAI,YAAY;AAChB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AAKZ,MAAI,QAAQ;AAAA,IACV,gBAAgB;AAAA,EAClB;AAEA,MAAI,cAAc,SAASG,aAAY,GAAG;AACxC,WAAO,MAAM,SAAS,KAAK,EAAE;AAAA,EAC/B;AAWA,MAAI,OAAO,SAASC,MAAK,MAAMV,OAAM,KAAK,OAAO;AAC/C,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,MAAMA;AAAA,MACN;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAUA,MAAI,YAAY,SAASW,WAAU,MAAMX,OAAM,UAAU;AACvD,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,MAAMA;AAAA,MACN;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAYA,MAAI,cAAc,SAASY,aAAY,MAAM,MAAM,UAAU;AAC3D,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAUA,MAAI,YAAY,SAASC,WAAU,MAAM,MAAM,UAAU;AACvD,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAMA,MAAI,SAAS,SAASC,QAAO,MAAM;AACjC,WAAO,SAAS,SAAS,KAAK,SAAS,QAAQ,KAAK,SAAS;AAAA,EAC/D;AAeA,MAAI,SAAS,SAASC,QAAO,MAAM,MAAM,OAAO,QAAQ,UAAU;AAChE,QAAI,MAAM,CAAC;AACX,QAAI,MAAM;AACV,QAAIC,SAAQ;AAEZ,aAAS,IAAI,GAAG,KAAK,EAAE,GAAG;AACxB,UAAI,MAAM,EAAG,KAAI,CAAC,IAAI,SAASA,QAAO;AACtC,eAAS;AAAA,IACX;AAEA,QAAI,IAAI,IAAI;AACZ,WAAO,UAAU,MAAMA,SAAQ,GAAG,GAAG;AAAA,EACvC;AAWA,MAAI,OAAO,SAASC,MAAK,MAAMD,QAAO,SAAS,UAAU;AACvD,QAAI,WAAW,IAAI,MAAMA,SAAQ,CAAC;AAClC,QAAI,IAAI;AACR,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,EAAE,GAAG;AACnD,UAAI,MAAM,SAAS;AACjB,YAAI,OAAO,SAAS,CAAC;AAErB,YAAI,QAAQ,CAAC,YAAY,IAAI,GAAG;AAC9B,mBAAS,GAAG,IAAI;AAChB,oBAAU,KAAK;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,YAAY,MAAM,QAAQ,QAAQ;AAAA,EAC3C;AAYA,MAAI,cAAc,SAASE,aAAY,MAAM,OAAO,IAAI,IAAI,IAAI,IAAI;AAClE,QAAI,OAAO,GAAI,QAAO,UAAU,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClD,QAAI,QAAQ,aAAa,OAAO,EAAE;AAClC,QAAI,QAAQ,aAAa,OAAO,EAAE;AAClC,WAAO,YAAY,MAAM,SAAS,KAAK,IAAI,SAAS,KAAK,GAAG,UAAU,QAAQ,CAACA,aAAY,MAAM,QAAQ,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,QAAQ,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,EACvK;AAeA,MAAI,sBAAsB,SAASC,qBAAoBb,SAAQ,MAAM,OAAO,GAAG,MAAM,GAAG,GAAG,MAAM;AAC/F,QAAI,MAAM,KAAK;AAEf,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,UAAI,QAAQ,KAAK,CAAC;AAElB,UAAI,MAAM,GAAG,MAAM,GAAG,GAAG;AACvB,YAAI,QAAQ,MAAM;AAElB,YAAI,YAAY,EAAE,KAAK;AAEvB,YAAI,cAAc,MAAO,QAAO;AAEhC,YAAI,cAAc,SAAS;AACzB,YAAE,KAAK;AACP,iBAAO,eAAeA,SAAQ,GAAG,IAAI;AAAA,QACvC;AAEA,eAAO,YAAYA,SAAQ,GAAG,KAAK,MAAM,GAAG,GAAG,SAAS,GAAG,IAAI;AAAA,MACjE;AAAA,IACF;AAEA,QAAI,WAAW,EAAE;AACjB,QAAI,aAAa,QAAS,QAAO;AACjC,MAAE,KAAK;AACP,WAAO,YAAYA,SAAQ,KAAK,KAAK,MAAM,GAAG,GAAG,QAAQ,GAAG,IAAI;AAAA,EAClE;AAEA,MAAI,cAAc,SAASc,aAAY,MAAM,MAAM;AACjD,WAAO,SAAS,KAAK;AAAA,EACvB;AAKA,MAAI,eAAe,SAASC,cAAa,MAAM,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM;AAC1E,QAAI,MAAM,GAAG,KAAK,GAAG,GAAG;AACtB,UAAI,KAAK,EAAE,KAAK,KAAK;AAErB,UAAI,OAAO,KAAK,MAAO,QAAO;AAAA,eAAc,OAAO,SAAS;AAC1D,UAAE,KAAK;AACP,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,MAAM,IAAI,GAAG;AAC3B,aAAK,QAAQ;AACb,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,MAAM,GAAG,GAAG,EAAE;AAAA,IAC5B;AAEA,QAAI,IAAI,EAAE;AACV,QAAI,MAAM,QAAS,QAAO;AAC1B,MAAE,KAAK;AACP,WAAO,YAAY,MAAM,OAAO,KAAK,MAAM,MAAM,GAAG,KAAK,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,EACzE;AAEA,MAAI,oBAAoB,SAASC,mBAAkB,MAAM,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM;AACpF,QAAI,MAAM,KAAK,MAAM;AACnB,UAAI,UAAU,YAAY,MAAM,IAAI;AACpC,UAAI,OAAO,oBAAoB,SAAS,MAAM,OAAO,KAAK,MAAM,KAAK,UAAU,GAAG,GAAG,IAAI;AACzF,UAAI,SAAS,KAAK,SAAU,QAAO;AACnC,aAAO,KAAK,SAAS,IAAI,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC;AAAA,IACpE;AAEA,QAAI,IAAI,EAAE;AACV,QAAI,MAAM,QAAS,QAAO;AAC1B,MAAE,KAAK;AACP,WAAO,YAAY,MAAM,OAAO,KAAK,MAAM,MAAM,GAAG,KAAK,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,EACzE;AAEA,MAAI,sBAAsB,SAASC,qBAAoB,MAAM,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM;AACxF,QAAI,OAAO,KAAK;AAChB,QAAI,WAAW,KAAK;AACpB,QAAI,OAAO,aAAa,OAAO,CAAC;AAChC,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,OAAO,WAAW,MAAM,GAAG;AAC/B,QAAI,SAAS,OAAO;AACpB,QAAI,UAAU,SAAS,SAAS,IAAI,IAAI;AAExC,QAAI,QAAQ,QAAQ,QAAQ,MAAM,OAAO,QAAQ,MAAM,GAAG,GAAG,GAAG,IAAI;AAEpE,QAAI,YAAY,MAAO,QAAO;AAC9B,QAAI,UAAU,YAAY,MAAM,IAAI;AACpC,QAAI,SAAS;AACb,QAAI,cAAc;AAElB,QAAI,UAAU,YAAY,KAAK,GAAG;AAEhC,gBAAU,CAAC;AACX,UAAI,CAAC,OAAQ,QAAO;AACpB,UAAI,SAAS,UAAU,KAAK,OAAO,SAAS,OAAO,CAAC,CAAC,EAAG,QAAO,SAAS,OAAO,CAAC;AAEhF,oBAAc,eAAe,SAAS,MAAM,QAAQ;AAAA,IACtD,WAAW,CAAC,UAAU,CAAC,YAAY,KAAK,GAAG;AAEzC,UAAI,SAAS,UAAU,eAAgB,QAAO,OAAO,MAAM,MAAM,OAAO,MAAM,QAAQ;AACtF,gBAAU;AACV,oBAAc,cAAc,SAAS,MAAM,OAAO,QAAQ;AAAA,IAC5D,OAAO;AAEL,oBAAc,YAAY,SAAS,MAAM,OAAO,QAAQ;AAAA,IAC1D;AAEA,QAAI,SAAS;AACX,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,MAAM,QAAQ,WAAW;AAAA,EAC9C;AAEA,MAAI,oBAAoB,SAASC,mBAAkB,MAAM,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM;AACpF,QAAIR,SAAQ,KAAK;AACjB,QAAI,WAAW,KAAK;AACpB,QAAI,OAAO,aAAa,OAAO,CAAC;AAChC,QAAI,QAAQ,SAAS,IAAI;AAEzB,QAAI,YAAY,SAAS,OAAO,QAAQ,MAAM,OAAO,QAAQ,MAAM,GAAG,GAAG,GAAG,IAAI;AAEhF,QAAI,UAAU,SAAU,QAAO;AAC/B,QAAI,UAAU,YAAY,MAAM,IAAI;AACpC,QAAI,cAAc;AAElB,QAAI,YAAY,KAAK,KAAK,CAAC,YAAY,QAAQ,GAAG;AAEhD,QAAEA;AACF,oBAAc,YAAY,SAAS,MAAM,UAAU,QAAQ;AAAA,IAC7D,WAAW,CAAC,YAAY,KAAK,KAAK,YAAY,QAAQ,GAAG;AAEvD,QAAEA;AACF,UAAIA,UAAS,eAAgB,QAAO,KAAK,MAAMA,QAAO,MAAM,QAAQ;AACpE,oBAAc,YAAY,SAAS,MAAM,OAAO,QAAQ;AAAA,IAC1D,OAAO;AAEL,oBAAc,YAAY,SAAS,MAAM,UAAU,QAAQ;AAAA,IAC7D;AAEA,QAAI,SAAS;AACX,WAAK,OAAOA;AACZ,WAAK,WAAW;AAChB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,MAAMA,QAAO,WAAW;AAAA,EAC3C;AAEA,QAAM,UAAU,SAAU,MAAM,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM;AAC3D,QAAI,IAAI,EAAE;AACV,QAAI,MAAM,QAAS,QAAO;AAC1B,MAAE,KAAK;AACP,WAAO,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,EAC3B;AAKA,WAASS,KAAI,UAAU,MAAM,QAAQ,MAAM,MAAM;AAC/C,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAEA,EAAAA,KAAI,UAAU,UAAU,SAAU,SAAS,SAAS;AAClD,QAAI,KAAK,WAAW;AAClB,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,KAAK,QAAQ,OAAO,IAAIA,KAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS,SAAS,OAAO;AAAA,EAC3G;AAWA,MAAI,aAAa,KAAK,aAAa,SAAU,KAAKzB,OAAM,KAAK,KAAK;AAChE,QAAI,OAAO,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI,QAAQ,IAAI,QAAQ;AAExB,WAAO,MAAM;AACX,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK,MACH;AACE,iBAAO,MAAM,KAAK,KAAK,GAAG,IAAI,KAAK,QAAQ;AAAA,QAC7C;AAAA,QAEF,KAAK,WACH;AACE,cAAIA,UAAS,KAAK,MAAM;AACtB,gBAAI,WAAW,KAAK;AAEpB,qBAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,EAAE,GAAG;AACnD,kBAAI,QAAQ,SAAS,CAAC;AACtB,kBAAI,MAAM,KAAK,MAAM,GAAG,EAAG,QAAO,MAAM;AAAA,YAC1C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEF,KAAK,OACH;AACE,cAAI,OAAO,aAAa,OAAOA,KAAI;AACnC,cAAI,MAAM,SAAS,IAAI;AAEvB,cAAI,KAAK,OAAO,KAAK;AACnB,mBAAO,KAAK,SAAS,WAAW,KAAK,MAAM,GAAG,CAAC;AAC/C,qBAAS;AACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEF,KAAK,OACH;AACE,iBAAO,KAAK,SAAS,aAAa,OAAOA,KAAI,CAAC;AAE9C,cAAI,MAAM;AACR,qBAAS;AACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEF;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAEA,EAAAyB,KAAI,UAAU,aAAa,SAAU,KAAKzB,OAAM,KAAK;AACnD,WAAO,WAAW,KAAKA,OAAM,KAAK,IAAI;AAAA,EACxC;AAQA,MAAI,SAAS,KAAK,SAAS,SAAU,KAAK,KAAK,KAAK;AAClD,WAAO,WAAW,KAAK,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG;AAAA,EACxD;AAEA,EAAAyB,KAAI,UAAU,SAAS,SAAU,KAAK,KAAK;AACzC,WAAO,OAAO,KAAK,KAAK,IAAI;AAAA,EAC9B;AAQA,MAAI,UAAU,KAAK,UAAU,SAAUzB,OAAM,KAAK,KAAK;AACrD,WAAO,WAAW,QAAWA,OAAM,KAAK,GAAG;AAAA,EAC7C;AAEA,EAAAyB,KAAI,UAAU,UAAU,SAAUzB,OAAM,KAAK;AAC3C,WAAO,QAAQA,OAAM,KAAK,IAAI;AAAA,EAChC;AAQA,MAAI,MAAM,KAAK,MAAM,SAAU,KAAK,KAAK;AACvC,WAAO,WAAW,QAAW,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG;AAAA,EAC9D;AAEA,EAAAyB,KAAI,UAAU,MAAM,SAAU,KAAK,KAAK;AACtC,WAAO,OAAO,KAAK,KAAK,IAAI;AAAA,EAC9B;AAMA,MAAI,UAAU,KAAK,MAAM,SAAUzB,OAAM,KAAK,KAAK;AACjD,WAAO,WAAW,SAASA,OAAM,KAAK,GAAG,MAAM;AAAA,EACjD;AAEA,EAAAyB,KAAI,UAAU,UAAU,SAAUzB,OAAM,KAAK;AAC3C,WAAO,QAAQA,OAAM,KAAK,IAAI;AAAA,EAChC;AAMA,MAAI,MAAM,KAAK,MAAM,SAAU,KAAK,KAAK;AACvC,WAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG;AAAA,EAChD;AAEA,EAAAyB,KAAI,UAAU,MAAM,SAAU,KAAK;AACjC,WAAO,IAAI,KAAK,IAAI;AAAA,EACtB;AAEA,MAAI,gBAAgB,SAASC,eAAc,GAAG,GAAG;AAC/C,WAAO,MAAM;AAAA,EACf;AAQA,OAAK,OAAO,SAAU,QAAQ;AAC5B,WAAO,IAAID,KAAI,GAAG,GAAG;AAAA,MACnB,OAAO,UAAU,OAAO,SAAS;AAAA,MACjC,MAAM,UAAU,OAAO,QAAQ;AAAA,IACjC,GAAG,OAAO,CAAC;AAAA,EACb;AAMA,OAAK,QAAQ,KAAK,KAAK;AAKvB,MAAI,UAAU,KAAK,UAAU,SAAU,KAAK;AAC1C,WAAO,OAAO,CAAC,CAAC,YAAY,IAAI,KAAK;AAAA,EACvC;AAEA,EAAAA,KAAI,UAAU,UAAU,WAAY;AAClC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAgBA,MAAI,aAAa,KAAK,aAAa,SAAU,GAAGzB,OAAM,KAAK,KAAK;AAC9D,QAAI,OAAO;AAAA,MACT,OAAO,IAAI;AAAA,IACb;AAEA,QAAI,UAAU,IAAI,MAAM,QAAQ,IAAI,YAAY,IAAI,QAAQ,KAAK,IAAI,QAAQ,OAAO,GAAG,GAAGA,OAAM,KAAK,IAAI;AAEzG,WAAO,IAAI,QAAQ,SAAS,KAAK,KAAK;AAAA,EACxC;AAEA,EAAAyB,KAAI,UAAU,aAAa,SAAUzB,OAAM,KAAK,GAAG;AACjD,WAAO,WAAW,GAAGA,OAAM,KAAK,IAAI;AAAA,EACtC;AASA,MAAI,SAAS,KAAK,SAAS,SAAU,GAAG,KAAK,KAAK;AAChD,WAAO,WAAW,GAAG,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG;AAAA,EACtD;AAEA,EAAAyB,KAAI,UAAU,SAAS,SAAU,KAAK,GAAG;AACvC,WAAO,OAAO,GAAG,KAAK,IAAI;AAAA,EAC5B;AAQA,MAAI,UAAU,KAAK,UAAU,SAAUzB,OAAM,KAAK,OAAO,KAAK;AAC5D,WAAO,WAAW,SAAS,KAAK,GAAGA,OAAM,KAAK,GAAG;AAAA,EACnD;AAEA,EAAAyB,KAAI,UAAU,UAAU,SAAUzB,OAAM,KAAK,OAAO;AAClD,WAAO,QAAQA,OAAM,KAAK,OAAO,IAAI;AAAA,EACvC;AAQA,MAAI,MAAM,KAAK,MAAM,SAAU,KAAK,OAAO,KAAK;AAC9C,WAAO,QAAQ,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,OAAO,GAAG;AAAA,EACvD;AAEA,EAAAyB,KAAI,UAAU,MAAM,SAAU,KAAK,OAAO;AACxC,WAAO,IAAI,KAAK,OAAO,IAAI;AAAA,EAC7B;AAQA,MAAI,MAAM,SAAS,OAAO;AAE1B,MAAI,aAAa,KAAK,aAAa,SAAUzB,OAAM,KAAK,KAAK;AAC3D,WAAO,WAAW,KAAKA,OAAM,KAAK,GAAG;AAAA,EACvC;AAEA,EAAAyB,KAAI,UAAU,aAAaA,KAAI,UAAU,aAAa,SAAUzB,OAAM,KAAK;AACzE,WAAO,WAAWA,OAAM,KAAK,IAAI;AAAA,EACnC;AAQA,MAAI,SAAS,KAAK,SAAS,SAAU,KAAK,KAAK;AAC7C,WAAO,WAAW,IAAI,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG;AAAA,EACnD;AAEA,EAAAyB,KAAI,UAAU,SAASA,KAAI,UAAU,SAAS,SAAU,KAAK;AAC3D,WAAO,OAAO,KAAK,IAAI;AAAA,EACzB;AASA,MAAI,gBAAgB,KAAK,gBAAgB,SAAU,KAAK;AACtD,WAAO,IAAIA,KAAI,IAAI,YAAY,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAS,IAAI,OAAO,IAAI,KAAK;AAAA,EACpF;AAEA,EAAAA,KAAI,UAAU,gBAAgB,WAAY;AACxC,WAAO,cAAc,IAAI;AAAA,EAC3B;AAMA,MAAI,cAAc,KAAK,cAAc,SAAU,KAAK;AAClD,QAAI,YAAY,IAAI,aAAa,IAAI,YAAY;AACjD,WAAO;AAAA,EACT;AAEA,EAAAA,KAAI,UAAU,cAAc,WAAY;AACtC,WAAO,YAAY,IAAI;AAAA,EACzB;AAQA,MAAI,SAAS,KAAK,SAAS,SAAU,GAAG,KAAK;AAC3C,QAAI,YAAY,cAAc,GAAG;AACjC,MAAE,SAAS;AACX,WAAO,YAAY,SAAS;AAAA,EAC9B;AAEA,EAAAA,KAAI,UAAU,SAAS,SAAU,GAAG;AAClC,WAAO,OAAO,GAAG,IAAI;AAAA,EACvB;AASA,MAAI,OAAO,SAASE,MAAK,GAAG;AAC1B,WAAO,KAAK,kBAAkB,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAC5D;AAMA,MAAI,oBAAoB,SAASC,mBAAkB,KAAK,UAAU,GAAG,GAAG,GAAG;AACzE,WAAO,IAAI,KAAK;AACd,UAAI,QAAQ,SAAS,GAAG;AACxB,UAAI,SAAS,CAAC,YAAY,KAAK,EAAG,QAAO,UAAU,OAAO,GAAG,CAAC,KAAK,UAAU,GAAG,GAAG,CAAC,CAAC;AAAA,IACvF;AAEA,WAAO,KAAK,CAAC;AAAA,EACf;AAMA,MAAI,YAAY,SAASC,WAAU,MAAM,GAAG,GAAG;AAC7C,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,eAAO;AAAA,UACL,OAAO,EAAE,IAAI;AAAA,UACb,MAAM;AAAA,QACR;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,WAAW,KAAK;AACpB,eAAO,kBAAkB,SAAS,QAAQ,UAAU,GAAG,GAAG,CAAC;AAAA,MAE7D;AACE,eAAO,KAAK,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,MAAI,OAAO;AAAA,IACT,MAAM;AAAA,EACR;AAKA,WAAS,YAAY,GAAG;AACtB,SAAK,IAAI;AAAA,EACX;AAEA,cAAY,UAAU,OAAO,WAAY;AACvC,QAAI,CAAC,KAAK,EAAG,QAAO;AACpB,QAAI,KAAK,KAAK;AACd,SAAK,IAAI,KAAK,GAAG,IAAI;AACrB,WAAO;AAAA,EACT;AAEA,cAAY,UAAU,OAAO,QAAQ,IAAI,WAAY;AACnD,WAAO;AAAA,EACT;AAMA,MAAI,QAAQ,SAASC,OAAM,KAAK,GAAG;AACjC,WAAO,IAAI,YAAY,UAAU,IAAI,OAAO,CAAC,CAAC;AAAA,EAChD;AAQA,MAAI,aAAa,SAASC,YAAW,GAAG;AACtC,WAAO,CAAC,EAAE,KAAK,EAAE,KAAK;AAAA,EACxB;AAEA,MAAI,UAAU,KAAK,UAAU,SAAU,KAAK;AAC1C,WAAO,MAAM,KAAK,UAAU;AAAA,EAC9B;AAEA,EAAAN,KAAI,UAAU,UAAUA,KAAI,UAAU,OAAO,QAAQ,IAAI,WAAY;AACnE,WAAO,QAAQ,IAAI;AAAA,EACrB;AAQA,MAAI,YAAY,SAASO,WAAU,GAAG;AACpC,WAAO,EAAE;AAAA,EACX;AAEA,MAAI,OAAO,KAAK,OAAO,SAAU,KAAK;AACpC,WAAO,MAAM,KAAK,SAAS;AAAA,EAC7B;AAEA,EAAAP,KAAI,UAAU,OAAO,WAAY;AAC/B,WAAO,KAAK,IAAI;AAAA,EAClB;AAQA,MAAI,cAAc,SAASQ,aAAY,GAAG;AACxC,WAAO,EAAE;AAAA,EACX;AAEA,MAAI,SAAS,KAAK,SAASR,KAAI,UAAU,SAAS,SAAU,KAAK;AAC/D,WAAO,MAAM,KAAK,WAAW;AAAA,EAC/B;AAEA,EAAAA,KAAI,UAAU,SAAS,WAAY;AACjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAeA,MAAI,OAAO,KAAK,OAAO,SAAU,GAAG,GAAG,GAAG;AACxC,QAAI,OAAO,EAAE;AACb,QAAI,KAAK,SAAS,KAAM,QAAO,EAAE,GAAG,KAAK,OAAO,KAAK,GAAG;AACxD,QAAI,UAAU,CAAC,KAAK,QAAQ;AAC5B,QAAI,WAAW;AAEf,WAAO,WAAW,QAAQ,IAAI,GAAG;AAC/B,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,OAAM;AAC/C,YAAI,QAAQ,SAAS,GAAG;AAExB,YAAI,SAAS,MAAM,MAAM;AACvB,cAAI,MAAM,SAAS,KAAM,KAAI,EAAE,GAAG,MAAM,OAAO,MAAM,GAAG;AAAA,cAAO,SAAQ,KAAK,MAAM,QAAQ;AAAA,QAC5F;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,EAAAA,KAAI,UAAU,OAAO,SAAU,GAAG,GAAG;AACnC,WAAO,KAAK,GAAG,GAAG,IAAI;AAAA,EACxB;AAWA,MAAI,UAAU,KAAK,UAAU,SAAU,GAAG,KAAK;AAC7C,WAAO,KAAK,SAAU,GAAG,OAAO,KAAK;AACnC,aAAO,EAAE,OAAO,KAAK,GAAG;AAAA,IAC1B,GAAG,MAAM,GAAG;AAAA,EACd;AAEA,EAAAA,KAAI,UAAU,UAAU,SAAU,GAAG;AACnC,WAAO,QAAQ,GAAG,IAAI;AAAA,EACxB;AASA,MAAI,QAAQ,KAAK,QAAQ,SAAU,KAAK;AACtC,WAAO,IAAI;AAAA,EACb;AAEA,EAAAA,KAAI,UAAU,QAAQ,WAAY;AAChC,WAAO,MAAM,IAAI;AAAA,EACnB;AAEA,SAAO,eAAeA,KAAI,WAAW,QAAQ;AAAA,IAC3C,KAAKA,KAAI,UAAU;AAAA,EACrB,CAAC;AAID,MAAK,OAAO,SAAS;AACnB,WAAO,UAAU;AAAA,EACnB,OAAO;AACL,aAAU,OAAO;AAAA,EACnB;AACA,CAAC;AAED,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,UAAU;AACpB,oBAAgB,MAAM,QAAQ,MAAM;AAEpC,SAAK,OAAO,IAAI,IAAI,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ,CAAC;AAAA,EAC5F;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,KAAK,QAAQ;AAAA,EAC3B;AAAA,EAEA,IAAI,GAAG;AACL,WAAO,KAAK,KAAK,IAAI,CAAC;AAAA,EACxB;AAAA,EAEA,IAAI,GAAG;AACL,WAAO,KAAK,KAAK,IAAI,CAAC;AAAA,EACxB;AAAA,EAEA,IAAI,GAAG,GAAG;AACR,SAAK,KAAK,IAAI,GAAG,CAAC;AAElB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,GAAG;AACR,SAAK,KAAK,OAAO,CAAC;AAElB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AACN,WAAO,cAAc,IAAI;AAAA,EAC3B;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,IAAI,KAAK,IAAI;AAAA,EAC1B;AAEF;AAEA,IAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA;AAAA;AAAA,EAI3B,YAAY,UAAU;AACpB,oBAAgB,MAAM,SAAS,OAAO,MAAM,cAAc,CAAC;AAE3D,QAAI,oBAAoB,yBAAwB;AAC9C,YAAM,IAAI,SAAS,MAAM,YAAY;AAErC,eAAS,QAAQ,EAAE,cAAc;AACjC,WAAK,QAAQ,EAAE,cAAc;AAAA,IAC/B,WAAW,UAAU;AACnB,iBAAW,CAAC,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG;AACvC,aAAK,MAAM,IAAI,GAAG,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EAEA,IAAI,GAAG;AACL,WAAO,KAAK,MAAM,IAAI,CAAC;AAAA,EACzB;AAAA,EAEA,IAAI,GAAG;AACL,WAAO,KAAK,MAAM,IAAI,CAAC;AAAA,EACzB;AAAA,EAEA,IAAI,GAAG,GAAG;AACR,SAAK,MAAM,IAAI,GAAG,CAAC;AAEnB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,GAAG;AACR,SAAK,MAAM,OAAO,CAAC;AAEnB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AACN,WAAO,cAAc,IAAI;AAAA,EAC3B;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,IAAI,KAAK,KAAK;AAAA,EAC3B;AAEF;AAEA,SAAS,cAAc,UAAU;AAC/B,MAAI,WAAW,kBAAkB,GAAG;AAClC,WAAO,IAAI,uBAAuB,QAAQ;AAAA,EAC5C,OAAO;AACL,WAAO,IAAI,WAAW,QAAQ;AAAA,EAChC;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB;AACF;AAEA,IAAI,yBAAyB,qBAAqB;AAElD,IAAI,yBAAsC,OAAO,OAAO;AAAA,EACtD,WAAW;AAAA,EACX,eAAe;AACjB,CAAC;AAoBD,SAAS,eAAe,QAAQ,wBAAwB;AACtD,QAAM,MAAM,oBAAI,IAAI;AAEpB,QAAO,YAAW,SAAS,KAAK;AAC9B,eAAW,YAAY,wBAAwB;AAC7C,UAAI,SAAS,IAAI,KAAK,GAAG;AACvB,iBAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,IAAI,KAAK;AAAA,EACf;AAEA,SAAO;AACT;AAEA,IAAI,wBAAwB;AAiB5B,SAAS,OAAO,KAAK,UAAU;AAC7B,QAAM,SAAS,oBAAI,IAAI;AACvB,MAAI,QAAQ,CAAC,OAAO,QAAQ;AAC1B,WAAO,IAAI,KAAK,SAAS,OAAO,GAAG,CAAC;AAAA,EACtC,CAAC;AACD,SAAO;AACT;AAEA,IAAI,gBAAgB;AAEpB,SAAS,YAAY;AACnB,SAAO;AAAA,IACL,UAAU,oBAAI,IAAI;AAAA,IAClB,yBAAyB,oBAAI,IAAI;AAAA,EACnC;AACF;AAEA,SAAS,WAAWS,QAAO;AACzB,SAAO;AAAA,IACL,UAAU,cAAcA,OAAM,UAAU,OAAK,IAAI,IAAI,CAAC,CAAC;AAAA,IACvD,yBAAyB,cAAcA,OAAM,yBAAyB,OAAK,IAAI,IAAI,CAAC,CAAC;AAAA,EACvF;AACF;AAIA,SAAS,mBAAmB,KAAK,SAASA,QAE1C,YAAY;AACV,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAIA;AACJ,QAAM,UAAU,SAAS,IAAI,GAAG;AAEhC,MAAI,WAAW,cAAc,YAAY,WAAW,SAAS,IAAI,GAAG,GAAG;AACrE;AAAA,EACF;AAGA,WAAS,IAAI,KAAK,OAAO;AAEzB,QAAM,YAAY,WAAW,OAAO,UAAU,sBAAsB,SAAS,OAAO;AAEpF,aAAW,OAAO,WAAW;AAC3B,QAAI,CAAC,wBAAwB,IAAI,GAAG,GAAG;AACrC,8BAAwB,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,IAC5C;AAEA,UAAM,WAAW,kBAAkB,wBAAwB,IAAI,GAAG,CAAC;AACnE,aAAS,IAAI,GAAG;AAAA,EAClB;AAGA,MAAI,SAAS;AACX,UAAM,cAAc,sBAAsB,SAAS,OAAO;AAE1D,eAAW,OAAO,aAAa;AAC7B,UAAI,CAAC,wBAAwB,IAAI,GAAG,GAAG;AACrC;AAAA,MACF;AAEA,YAAM,WAAW,kBAAkB,wBAAwB,IAAI,GAAG,CAAC;AACnE,eAAS,OAAO,GAAG;AAEnB,UAAI,SAAS,SAAS,GAAG;AACvB,gCAAwB,OAAO,GAAG;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,KAAK,MAAM,OAAO,SAAS;AAClD,MAAI,sBAAsB,uBAAuB,wBAAwB;AAEzE,QAAM,aAAa,MAAM,SAAS;AAElC,MAAI,EAAE,YAAY,WAAW,YAAY,WAAW,cAAc,uBAAuB,WAAW,cAAc,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,YAAY,cAAc,wBAAwB,WAAW,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,WAAW;AAC5V,gCAA4B,gDAAgD;AAAA,EAC9E;AAIA,QAAMA,SAAQ,MAAM,SAAS,OAAO;AACpC,qBAAmB,KAAK,MAAMA,MAAK;AAGnC,MAAI,cAAc,yBAAyB,WAAW,kBAAkB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,UAAU;AAC5J,UAAM,eAAe,MAAM,SAAS,WAAW,YAAY,OAAO;AAClE,uBAAmB,KAAK,MAAM,cAAcA,MAAK;AAAA,EACnD;AAEA,MAAI,cAAc,yBAAyB,WAAW,kBAAkB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,YAAY,YAAY,WAAW,YAAY,SAAS;AAC1M,QAAI;AAEJ,UAAM,eAAe,wBAAwB,WAAW,cAAc,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAEhJ,QAAI,gBAAgB,QAAW;AAC7B,YAAM,YAAY,MAAM,SAAS,WAAW;AAC5C,yBAAmB,KAAK,MAAM,WAAWA,MAAK;AAAA,IAChD;AAAA,EACF;AACF;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA,OAAO;AAAA,EACP;AACF;AAaA,IAAI,uBAAuB;AAE3B,IAAM,0BAA0B,MAAM;AAEtC,IAAI,cAAc;AAElB,IAAM,iBAAiB,MAAM;AAE7B,IAAI,kBAAkB;AAEtB,IAAM,qBAAqB,MAAM;AAEjC,IAAI,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ;AACF,IAAI;AAEJ,IAAM;AAAA,EACJ,yBAAyB;AAC3B,IAAI;AAEJ,SAAS,qBAAqB;AAC5B,QAAM,UAAU,0BAA0B;AAC1C,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,IACT,qBAAqB,CAAC;AAAA,IACtB,YAAY,oBAAI,IAAI;AAAA,IACpB,YAAY,gBAAgB;AAAA,IAC5B,mBAAmB,gBAAgB;AAAA,EACrC;AACF;AAEA,SAAS,sBAAsB;AAC7B,QAAM,cAAc,mBAAmB;AACvC,SAAO;AAAA,IACL;AAAA,IACA,UAAU;AAAA,IACV,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY,oBAAI,IAAI;AAAA,IACpB,gBAAgB,oBAAI,IAAI;AAAA,IACxB,0BAA0B,oBAAI,IAAI;AAAA,IAClC,8BAA8B,oBAAI,IAAI;AAAA,IACtC,8BAA8B,oBAAI,IAAI;AAAA,IACtC,qCAAqC,CAAC;AAAA,IACtC,6BAA6B,oBAAI,IAAI;AAAA,IACrC,kBAAiB,oBAAI,IAAI,GAAE,IAAI,YAAY,SAAS,MAAM,CAAC;AAAA,IAC3D,WAAW;AAAA,MACT,iBAAiB,oBAAI,IAAI;AAAA,MACzB,qBAAqB,oBAAI,IAAI;AAAA,MAC7B,8BAA8B,oBAAI,IAAI;AAAA,IACxC;AAAA,IACA,sBAAsB,oBAAI,IAAI;AAAA,EAChC;AACF;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA,yBAAyB;AAC3B;AAaA,IAAM,gBAAN,MAAoB;AAAC;AAErB,SAAS,gBAAgB;AACvB,SAAO,IAAI,cAAc;AAC3B;AAEA,IAAI,uBAAuB;AAAA,EACzB;AAAA,EACA;AACF;AAeA,SAAS,iBAAiB,KAAK,GAAG;AAChC,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,OAAK,IAAI,CAAC;AACV,SAAO;AACT;AAEA,SAAS,qBAAqB,KAAK,GAAG;AACpC,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,OAAK,OAAO,CAAC;AACb,SAAO;AACT;AAEA,SAAS,kBAAkB,KAAK,GAAG,GAAG;AACpC,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,OAAK,IAAI,GAAG,CAAC;AACb,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAK,GAAG,SAAS;AAC3C,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,OAAK,IAAI,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC;AAChC,SAAO;AACT;AAEA,SAAS,qBAAqB,KAAK,GAAG;AACpC,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,OAAK,OAAO,CAAC;AACb,SAAO;AACT;AAEA,SAAS,6BAA6B,KAAK,IAAI;AAC7C,QAAM,OAAO,IAAI,IAAI,GAAG;AACxB,KAAG,QAAQ,OAAK,KAAK,OAAO,CAAC,CAAC;AAC9B,SAAO;AACT;AAEA,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAiBA,UAAU,eAAe,UAAU,WAAW;AAE5C,MAAI,QAAQ;AAEZ,aAAW,SAAS,UAAU;AAC5B,QAAI,UAAU,OAAO,OAAO,GAAG;AAC7B,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB;AAuB5B,SAAS,UAAU,MAAM,WAAW;AAClC,QAAM,QAAQ,IAAI,MAAM,MAAM;AAAA;AAAA,IAE5B,KAAK,CAAC,QAAQ,SAAS;AACrB,UAAI,EAAE,QAAQ,WAAW,QAAQ,WAAW;AAC1C,eAAO,IAAI,IAAI,UAAU,IAAI,EAAE;AAAA,MACjC;AAEA,aAAO,OAAO,IAAI;AAAA,IACpB;AAAA;AAAA,IAEA,SAAS,YAAU;AAEjB,aAAO,OAAO,KAAK,MAAM;AAAA,IAC3B;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,IAAI,mBAAmB;AAEvB,IAAM;AAAA,EACJ,SAAS;AAAA,EACT,cAAc;AAAA,EACd,qBAAqB;AACvB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,kBAAkB;AACpB,IAAI;AAWJ,IAAM,WAAW,OAAO,OAAO,oBAAI,IAAI,CAAC;AAExC,IAAM,2BAAN,cAAuC,MAAM;AAAC;AAE9C,SAAS,2BAA2B,OAAO,SAAS,YAAY;AAC9D,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD,WAAO,MAAM;AAAA,EACf;AAEA,QAAM;AAAA,IACJ,qBAAAC;AAAA,EACF,IAAI,MAAM,SAAS,EAAE;AAErB,WAAS,UAAU,MAAM;AACvB,QAAI,MAAMA,qBAAoB,IAAI,IAAI;AAEtC,QAAI,CAAC,KAAK;AACR,MAAAA,qBAAoB,IAAI,MAAM,MAAM,oBAAI,IAAI,CAAC;AAAA,IAC/C;AAEA,QAAI,IAAI,OAAO;AAAA,EACjB;AAEA,MAAI,sBAAsB,iBAAiB;AACzC,cAAU,UAAU;AAAA,EACtB,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,eAAW,QAAQ,YAAY;AAC7B,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,MAAM;AACX,QAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,IACF;AAEA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,MAAM,SAAS;AAEnB,aAAS,eAAe,MAAM;AAC5B,YAAM,MAAM,UAAU,oBAAoB,IAAI,IAAI;AAClD,cAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,OAAO,OAAO;AAE5D,UAAI,OAAO,IAAI,SAAS,GAAG;AACzB,kBAAU,oBAAoB,OAAO,IAAI;AAAA,MAC3C;AAAA,IACF;AAEA,QAAI,sBAAsB,iBAAiB;AACzC,qBAAe,UAAU;AAAA,IAC3B,WAAW,MAAM,QAAQ,UAAU,GAAG;AACpC,iBAAW,QAAQ,YAAY;AAC7B,uBAAe,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,2BAA2B,OAAO,WAAW,KAAK,SAAS;AAClE,QAAM,aAAa,MAAM,SAAS;AAElC,MAAI,WAAW,qBAAqB,IAAI,GAAG,GAAG;AAC5C;AAAA,EACF;AAEA,QAAM,OAAO,UAAU,GAAG;AAC1B,QAAM,mBAAmB,2BAA2B,OAAO,KAAK,KAAK,UAAU;AAC/E,QAAM,cAAc,KAAK,KAAK,OAAO,WAAW,OAAO;AACvD,aAAW,qBAAqB,IAAI,KAAK,MAAM;AAC7C,gBAAY;AACZ,qBAAiB;AAAA,EACnB,CAAC;AACH;AAEA,SAAS,eAAe,OAAO,KAAK,SAAS;AAC3C,6BAA2B,OAAO,MAAM,SAAS,EAAE,aAAa,KAAK,OAAO;AAC9E;AAEA,SAAS,YAAY,OAAO,KAAK;AAC/B,MAAI;AAEJ,QAAM,QAAQ,MAAM,SAAS;AAC7B,GAAC,wBAAwB,MAAM,qBAAqB,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAC5I,QAAM,qBAAqB,OAAO,GAAG;AACvC;AAKA,SAAS,gBAAgB,OAAO,OAAO,KAAK;AAC1C,6BAA2B,OAAO,OAAO,KAAK,KAAK;AACnD,SAAO,UAAU,GAAG,EAAE,IAAI,OAAO,KAAK;AACxC;AAGA,SAAS,iBAAiB,OAAO,OAAO,KAAK;AAC3C,SAAO,UAAU,GAAG,EAAE,KAAK,OAAO,KAAK;AACzC;AAIA,SAAS,mCAAmC,OAAO,KAAK,UAAU;AAChE,MAAI;AAEJ,QAAM,OAAO,eAAe,GAAG;AAC/B,WAAS,QAAQ,SAAS,SAAS,UAAU,mBAAmB,KAAK,gBAAgB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,MAAM,KAAK;AACrK,SAAO;AAAA,IAAE,GAAG;AAAA,IACV,YAAY,MAAM,WAAW,MAAM,EAAE,OAAO,GAAG;AAAA,IAC/C,mBAAmB,MAAM,kBAAkB,MAAM,EAAE,IAAI,KAAK,QAAQ;AAAA,IACpE,YAAY,mBAAmB,MAAM,YAAY,GAAG;AAAA,EACtD;AACF;AAKA,SAAS,aAAa,OAAO,OAAO,KAAK,UAAU;AACjD,QAAM,OAAO,UAAU,GAAG;AAE1B,MAAI,KAAK,OAAO,MAAM;AACpB,UAAM,IAAI,yBAAyB,yCAAyC,GAAG,EAAE;AAAA,EACnF;AAEA,QAAM,MAAM,KAAK;AAEjB,6BAA2B,OAAO,OAAO,KAAK,KAAK;AACnD,SAAO,IAAI,OAAO,OAAO,QAAQ;AACnC;AAEA,SAAS,aAAa,OAAO,OAAO,KAAK;AACvC,QAAM,aAAa,MAAM,SAAS;AAClC,QAAMD,SAAQ,MAAM,SAAS,MAAM,OAAO;AAC1C,QAAM,OAAO,UAAU,GAAG,EAAE;AAC5B,SAAO,iBAAiB;AAAA,IACtB;AAAA,EACF,GAAG;AAAA;AAAA,IAED,UAAU,MAAM,iBAAiB,OAAO,OAAO,GAAG;AAAA,IAClD,UAAU,MAAM,WAAW,WAAW,IAAI,GAAG,KAAK,WAAW,eAAe,IAAI,GAAG;AAAA,IACnF,OAAO,MAAM,SAAS,aAAa,QAAQ,MAAM,WAAW,IAAI,GAAG;AAAA,IACnE,YAAY,MAAM,MAAM,WAAW,IAAI,GAAG;AAAA;AAAA;AAAA,IAG1C,MAAM,MAAM;AACV,UAAI;AAEJ,aAAO,uBAAuB,sBAAsBA,OAAM,SAAS,IAAI,GAAG,OAAO,QAAQ,wBAAwB,SAAS,sBAAsB,CAAC,CAAC;AAAA,IACpJ;AAAA;AAAA;AAAA,IAGA,aAAa,MAAM;AACjB,UAAI,uBAAuB;AAE3B,aAAO;AAAA,QACL,OAAO,sBAAsB,sBAAsB,mBAAmB,OAAO,OAAO,oBAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,aAAW,YAAY,GAAG,CAAC;AAAA,QAChI,YAAY,oBAAoB,yBAAyB,yBAAyB,WAAW,6BAA6B,IAAI,GAAG,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,OAAO,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO;AAAA,UACpT;AAAA,QACF,EAAE;AAAA,MACJ;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAGA,SAAS,mBAAmB,OAAO,OAAO,MAAM;AAC9C,QAAM,eAAe,oBAAI,IAAI;AAC7B,QAAM,gBAAgB,MAAM,KAAK,IAAI;AACrC,QAAMA,SAAQ,MAAM,SAAS,MAAM,OAAO;AAE1C,WAAS,MAAM,cAAc,IAAI,GAAG,KAAK,MAAM,cAAc,IAAI,GAAG;AAClE,QAAI;AAEJ,iBAAa,IAAI,GAAG;AACpB,UAAM,mBAAmB,wBAAwBA,OAAM,wBAAwB,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AAEhK,eAAW,kBAAkB,iBAAiB;AAC5C,UAAI,CAAC,aAAa,IAAI,cAAc,GAAG;AACrC,sBAAc,KAAK,cAAc;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,wBAAwB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAaA,IAAI,8BAA8B;AAElC,SAAS,8BAA8B,YAAY;AACjD,gCAA8B;AAChC;AAEA,SAAS,6BAA6B;AACpC,MAAI;AAEJ,GAAC,wBAAwB,iCAAiC,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACtI;AAEA,IAAI,uBAAuB;AAAA,EACzB;AAAA,EACA;AACF;AAEA,IAAM;AAAA,EACJ,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,oBAAoB;AACtB,IAAI;AAEJ,IAAM;AAAA,EACJ,SAAS;AAAA,EACT,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,WAAW;AACb,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,4BAA4B;AAC9B,IAAI;AAQJ,SAAS,yBAAyB,OAAO;AAAA,EACvC;AACF,GAAG,YAAY,MAAM,SAAS,EAAE,aAAa;AAC3C,MAAI,sBAAsB;AAI1B,QAAM,aAAa,MAAM,SAAS;AAElC,MAAI,EAAE,UAAU,YAAY,WAAW,YAAY,WAAW,UAAU,cAAc,uBAAuB,WAAW,cAAc,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,YAAY,UAAU,cAAc,wBAAwB,WAAW,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,WAAW;AAC1X,gCAA4B,qCAAqC;AAAA,EACnE;AAEA,QAAM,WAAW,kBAAkB,OAAO,WAAW,GAAG;AAExD,MAAI,SAAS,UAAU,WAAW;AAChC,aAAS,SAAS,MAAM,MAAM;AAK5B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,YAAY,QAAQ;AAChD,QAAM,SAAS,WAAW,MAAM;AAChC,SAAO,QAAQ,CAAC,GAAG,MAAM;AACvB,QAAI,EAAE,UAAU,cAAc,EAAE,oBAAoB,gBAAgB;AAClE,aAAO,OAAO,CAAC;AAAA,IACjB,OAAO;AACL,aAAO,IAAI,GAAG,CAAC;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,wBAAwB,OAAO,OAAO;AAAA,EAC7C;AACF,GAAG,gBAAgB;AACjB,MAAI,OAAO,mBAAmB,YAAY;AAIxC,UAAM,UAAU,kBAAkB,OAAO,OAAO,GAAG;AAEnD,QAAI,QAAQ,UAAU,WAAW;AAC/B,YAAM,MAAM,kCAAkC,GAAG;AACjD,kCAA4B,GAAG;AAC/B,YAAM,WAAW,GAAG;AAAA,IACtB,WAAW,QAAQ,UAAU,YAAY;AACvC,YAAM,QAAQ;AAAA,IAChB;AAGA,WAAO,eAAe,QAAQ,QAAQ;AAAA,EACxC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO,OAAO,QAAQ;AACzC,MAAI,OAAO,SAAS,OAAO;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,wBAAwB,OAAO,OAAO,aAAa,cAAc;AAClF,UAAM,SAAS,eAAe,OAAO,OAAO,YAAY,KAAK,QAAQ;AAErE,eAAW,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,GAAG;AAC9C,+BAAyB,OAAO,KAAK,QAAQ;AAAA,IAC/C;AAAA,EACF,WAAW,OAAO,SAAS,eAAe;AACxC,UAAM;AAAA,MACJ,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,6BAAyB,OAAO,KAAK,QAAQ;AAAA,EAC/C,WAAW,OAAO,SAAS,gBAAgB;AACzC,UAAM;AAAA,MACJ,aAAa;AAAA,QACX;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,IAAI,GAAG;AAAA,EAC1B,WAAW,OAAO,SAAS,kBAAkB;AAC3C,QAAI;AAIJ,UAAM;AAAA,MACJ,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,eAAe,GAAG;AAC/B,aAAS,QAAQ,SAAS,SAAS,UAAU,mBAAmB,KAAK,gBAAgB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,MAAM,KAAK;AACrK,UAAM,WAAW,OAAO,GAAG;AAC3B,UAAM,kBAAkB,IAAI,KAAK,gBAAgB;AACjD,UAAM,WAAW,IAAI,GAAG;AAAA,EAC1B,OAAO;AACL,gCAA4B,kBAAkB,OAAO,IAAI,EAAE;AAAA,EAC7D;AACF;AAEA,SAAS,yBAAyB,OAAO,KAAK,UAAU;AACtD,MAAI,SAAS,UAAU,cAAc,SAAS,oBAAoB,gBAAgB;AAChF,UAAM,WAAW,OAAO,GAAG;AAAA,EAC7B,OAAO;AACL,UAAM,WAAW,IAAI,KAAK,QAAQ;AAAA,EACpC;AAEA,QAAM,WAAW,IAAI,GAAG;AACxB,QAAM,kBAAkB,OAAO,GAAG;AACpC;AAEA,SAAS,oBAAoB,OAAO,SAAS;AAC3C,QAAM,aAAa,WAAS;AAC1B,UAAM,WAAW,cAAc,KAAK;AAEpC,eAAW,UAAU,SAAS;AAC5B,kBAAY,OAAO,UAAU,MAAM;AAAA,IACrC;AAEA,0BAAsB,OAAO,QAAQ;AACrC,iCAA6B;AAC7B,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,0BAA0B,OAAO,QAAQ;AAChD,MAAI,WAAW,QAAQ;AACrB,UAAM,iBAAiB,WAAW,WAAW,SAAS,CAAC;AACvD,QAAI,UAAU,eAAe,IAAI,KAAK;AAEtC,QAAI,CAAC,SAAS;AACZ,qBAAe,IAAI,OAAO,UAAU,CAAC,CAAC;AAAA,IACxC;AAEA,YAAQ,KAAK,MAAM;AAAA,EACrB,OAAO;AACL,wBAAoB,OAAO,CAAC,MAAM,CAAC;AAAA,EACrC;AACF;AAEA,IAAM,aAAa,CAAC;AAEpB,SAAS,aAAa;AACpB,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,aAAW,KAAK,cAAc;AAC9B,SAAO,MAAM;AACX,eAAW,CAAC,OAAO,OAAO,KAAK,gBAAgB;AAC7C,0BAAoB,OAAO,OAAO;AAAA,IACpC;AAEA,UAAM,SAAS,WAAW,IAAI;AAE9B,QAAI,WAAW,gBAAgB;AAC7B,kCAA4B,kCAAkC;AAAA,IAChE;AAAA,EACF;AACF;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO;AAAA,IAAE,GAAG;AAAA,IACV,YAAY,MAAM,WAAW,MAAM;AAAA,IACnC,mBAAmB,MAAM,kBAAkB,MAAM;AAAA,IACjD,YAAY,IAAI,IAAI,MAAM,UAAU;AAAA,EACtC;AACF;AAEA,SAAS,sBAAsB,OAAO,OAAO;AAG3C,QAAM,cAAc,qBAAqB,OAAO,OAAO,MAAM,UAAU;AAEvE,aAAW,OAAO,aAAa;AAC7B,QAAI,eAAe;AAEnB,KAAC,gBAAgB,eAAe,GAAG,OAAO,QAAQ,kBAAkB,SAAS,UAAU,wBAAwB,cAAc,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,eAAe,KAAK;AAAA,EAClP;AACF;AAEA,SAAS,eAAe,OAAO,aAAa,gBAAgB;AAC1D,4BAA0B,OAAO;AAAA,IAC/B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,uBAAuB,OAAO,aAAa,UAAU;AAC5D,MAAI,oBAAoB,gBAAgB;AACtC,WAAO,eAAe,OAAO,aAAa,QAAQ;AAAA,EACpD;AAEA,4BAA0B,OAAO;AAAA,IAC/B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,wBAAwB,OAAO,aAAa;AACnD,4BAA0B,OAAO;AAAA,IAC/B,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACH;AAEA,SAAS,0BAA0B,OAAO,aAAa,kBAAkB;AACvE,4BAA0B,OAAO;AAAA,IAC/B,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,uBAAuB,OAAO;AAAA,EACrC;AACF,GAAG,UAAU,qBAAqB,MAAM;AACtC,QAAM,QAAQ,qBAAqB;AACnC,QAAM,aAAa,MAAM,SAAS;AAElC,MAAI,CAAC,WAAW,6BAA6B,IAAI,GAAG,GAAG;AACrD,eAAW,6BAA6B,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,EAC5D;AAEA,oBAAkB,WAAW,6BAA6B,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,uBAAuB,QAAQ,uBAAuB,SAAS,qBAAqB,kBAAkB,QAAQ,CAAC;AAG/L,QAAM,OAAO,YAAY;AAEzB,MAAI,KAAK,UAAU,KAAK,SAAS,YAAY,KAAK,SAAS,mBAAmB;AAC5E,UAAM,WAAW,MAAM,SAAS,EAAE;AAElC,QAAI,YAAY,SAAS,WAAW,IAAI,GAAG,GAAG;AAC5C,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,SAAS,MAAM;AACb,YAAM,oBAAoB,MAAM,SAAS;AACzC,YAAM,OAAO,kBAAkB,6BAA6B,IAAI,GAAG;AAEnE,UAAI,SAAS,UAAa,CAAC,KAAK,IAAI,KAAK,GAAG;AAC1C,oCAA4B,iDAAiD,GAAG,4BAA4B;AAC5G;AAAA,MACF;AAEA,WAAK,OAAO,KAAK;AAEjB,UAAI,KAAK,SAAS,GAAG;AACnB,0BAAkB,6BAA6B,OAAO,GAAG;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,OAAO,aAAa;AAC9C,MAAI;AAEJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,MAAM,SAAS;AACnB,QAAM,OAAO,UAAU,YAAY,GAAG;AACtC,GAAC,mBAAmB,KAAK,gBAAgB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,MAAM,OAAO,WAAW;AACxI;AAEA,IAAI,8BAA8B;AAAA,EAChC,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAiBA,SAAS,QAAQ,KAAK,UAAU,SAAS;AACvC,QAAM,WAAW,IAAI,QAAQ;AAC7B,MAAI,UAAU,SAAS,KAAK;AAE5B,SAAO,CAAC,QAAQ,MAAM;AACpB,UAAM,QAAQ,QAAQ;AAEtB,QAAI,SAAS,KAAK,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG;AACnD,aAAO;AAAA,IACT;AAEA,cAAU,SAAS,KAAK;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAI,iBAAiB;AAErB,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,4BAA4B;AAAA,EAC5B,SAAS;AACX,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAYJ,IAAM,sBAAsB;AAC5B,IAAM,aAAa,oBAAI,IAAI;AAE3B,SAAS,mCAAmC,OAAO,aAAa;AAC9D,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,YAAY,WAAW;AAE7B,MAAI,WAAW,UAAU;AACvB,gCAA4B,0EAA0E;AACtG;AAAA,EACF;AAEA,QAAME,SAAQ,oBAAI,IAAI;AAEtB,aAAW,KAAK,aAAa;AAC3B,QAAI,aAAa,iBAAiB;AAChC,iBAAW,KAAK,oBAAoB,YAAY,CAAC,GAAG;AAClD,QAAAA,OAAM,IAAI,CAAC;AAAA,MACb;AAAA,IACF,OAAO;AACL,MAAAA,OAAM,IAAI,CAAC;AAAA,IACb;AAAA,EACF;AAEA,QAAM,kBAAkB,oBAAoB,OAAOA,MAAK;AAExD,aAAW,QAAQ,iBAAiB;AAClC,gBAAY,OAAO,WAAW,IAAI;AAAA,EACpC;AACF;AAEA,SAAS,oBAAoB,OAAO,iBAAiB;AACnD,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,YAAY,WAAW;AAC7B,QAAMF,SAAQ,MAAM,SAAS,UAAU,OAAO;AAC9C,QAAM,kBAAkB,oBAAI,IAAI;AAEhC,QAAM,qBAAqB,oBAAI,IAAI;AACnC,2BAAyB,eAAe;AACxC,SAAO;AAEP,WAAS,yBAAyBG,kBAAiB;AACjD,UAAM,oCAAoC,oBAAI,IAAI;AAClD,UAAM,cAAc;AAAA,MAAqC;AAAA,MAAO;AAAA,MAAWA;AAAA,MAAiB;AAAA;AAAA,MAC5F;AAAA;AAAA,IACA;AAEA,eAAW,QAAQ,aAAa;AAC9B,UAAI;AAGJ,UAAI,UAAU,IAAI,EAAE,eAAe,cAAc;AAC/C,2BAAmB,IAAI,IAAI;AAC3B;AAAA,MACF;AAGA,YAAM,wBAAwB,WAAW,UAAU,gBAAgB,IAAI,IAAI,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB,KAAK,GAAG;AAC3J,2BAAmB,IAAI,IAAI;AAC3B;AAAA,MACF;AAGA,UAAI,yBAAyB,IAAI,EAAE,KAAK,OAAK,WAAW,UAAU,gBAAgB,IAAI,CAAC,CAAC,GAAG;AACzF,2BAAmB,IAAI,IAAI;AAC3B;AAAA,MACF;AAIA,YAAM,eAAeH,OAAM,wBAAwB,IAAI,IAAI;AAE3D,UAAI,gBAAgB,eAAe,cAAc,WAAS,mBAAmB,IAAI,KAAK,CAAC,GAAG;AACxF,2BAAmB,IAAI,IAAI;AAC3B;AAAA,MACF;AAEA,sBAAgB,IAAI,IAAI;AACxB,wCAAkC,IAAI,IAAI;AAAA,IAC5C;AAIA,UAAM,UAAU,oBAAI,IAAI;AAExB,eAAW,QAAQ,mCAAmC;AACpD,iBAAW,WAAW,sBAAsBA,OAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,wBAAwB,SAAS,sBAAsB,YAAY;AACnJ,YAAI;AAEJ,YAAI,CAAC,gBAAgB,IAAI,MAAM,GAAG;AAChC,kBAAQ,IAAI,MAAM;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ,MAAM;AAChB,+BAAyB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;AAGA,SAAS,qCAAqC,OAAO,WAAWE,QAChE,mBAAmB,mBAAmB;AACpC,QAAMF,SAAQ,MAAM,SAAS,UAAU,OAAO;AAC9C,QAAM,SAAS,CAAC;AAChB,QAAM,UAAU,oBAAI,IAAI;AAExB,SAAOE,OAAM,OAAO,GAAG;AACrB,UAAM,kBAAkBA,OAAM,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;AAAA,EACtD;AAEA,SAAO;AAEP,WAAS,MAAM,MAAM;AACnB,QAAI,kBAAkB,IAAI,IAAI,KAAK,kBAAkB,IAAI,IAAI,GAAG;AAC9D,MAAAA,OAAM,OAAO,IAAI;AACjB;AAAA,IACF;AAEA,QAAI,QAAQ,IAAI,IAAI,GAAG;AACrB;AAAA,IACF;AAEA,UAAM,WAAWF,OAAM,wBAAwB,IAAI,IAAI;AAEvD,QAAI,UAAU;AACZ,iBAAW,SAAS,UAAU;AAC5B,cAAM,KAAK;AAAA,MACb;AAAA,IACF;AAEA,YAAQ,IAAI,IAAI;AAChB,IAAAE,OAAM,OAAO,IAAI;AACjB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AAEA,SAAS,YAAY,OAAO,WAAW,MAAM;AAC3C,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAGA,gBAAc,OAAO,IAAI;AAEzB,QAAM,aAAa,MAAM,SAAS;AAClC,aAAW,WAAW,OAAO,IAAI;AACjC,aAAW,eAAe,OAAO,IAAI;AACrC,aAAW,6BAA6B,OAAO,IAAI;AACnD,aAAW,UAAU,gBAAgB,OAAO,IAAI;AAChD,QAAM,QAAQ,yBAAyB,IAAI;AAE3C,aAAW,QAAQ,OAAO;AACxB,QAAI;AAEJ,KAAC,yBAAyB,WAAW,UAAU,oBAAoB,IAAI,IAAI,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,OAAO,IAAI;AAAA,EAC3K;AAMA,YAAU,WAAW,OAAO,IAAI;AAChC,YAAU,WAAW,OAAO,IAAI;AAChC,YAAU,kBAAkB,OAAO,IAAI;AACvC,QAAMF,SAAQ,WAAW,gBAAgB,IAAI,UAAU,OAAO;AAE9D,MAAIA,QAAO;AACT,UAAM,OAAOA,OAAM,SAAS,IAAI,IAAI;AAEpC,QAAI,SAAS,QAAW;AACtB,MAAAA,OAAM,SAAS,OAAO,IAAI;AAE1B,iBAAW,OAAO,MAAM;AACtB,YAAI;AAEJ,SAAC,wBAAwBA,OAAM,wBAAwB,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,OAAO,IAAI;AAAA,MAC5J;AAAA,IACF;AAIA,IAAAA,OAAM,wBAAwB,OAAO,IAAI;AAAA,EAC3C;AAIA,+BAA6B,IAAI;AACnC;AAEA,SAAS,oBAAoB,YAAY,MAAM;AAC7C,MAAI;AAEJ,UAAQ,yBAAyB,WAAW,UAAU,oBAAoB,IAAI,IAAI,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB;AAChK;AAEA,SAAS,yBAAyB,MAAM;AACtC,QAAM,aAAa,UAAU,IAAI,EAAE;AAEnC,MAAI,eAAe,UAAa,eAAe,gBAAgB,eAAe,cAAc;AAC1F,WAAO,CAAC;AAAA,EACV,WAAW,sBAAsB,iBAAiB;AAChD,WAAO,CAAC,UAAU;AAAA,EACpB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,6CAA6C,OAAO,YAAY;AACvE,QAAM,QAAQ,MAAM,SAAS;AAE7B,MAAI,MAAM,UAAU;AAClB,UAAM,UAAU,6BAA6B,IAAI,UAAU;AAAA,EAC7D,OAAO;AACL,uCAAmC,OAAO,oBAAI,IAAI,CAAC,UAAU,CAAC,CAAC;AAAA,EACjE;AACF;AAEA,SAAS,kBAAkB,OAAO,YAAY,OAAO;AACnD,MAAI;AAEJ,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAEA,QAAM,MAAM,MAAM,SAAS,EAAE,UAAU;AACvC,QAAM,aAAa,WAAW,IAAI,IAAI,UAAU,OAAO,QAAQ,aAAa,SAAS,WAAW,KAAK;AAErG,MAAI,aAAa,GAAG;AAClB,4BAAwB,OAAO,UAAU;AAAA,EAC3C,OAAO;AACL,QAAI,IAAI,YAAY,QAAQ;AAAA,EAC9B;AACF;AAEA,SAAS,wBAAwB,OAAO,YAAY;AAClD,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAEA,QAAM,MAAM,MAAM,SAAS,EAAE,UAAU;AACvC,MAAI,OAAO,UAAU;AACrB,+CAA6C,OAAO,UAAU;AAChE;AAEA,SAAS,+BAA+B,OAAO;AAC7C,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAEA,QAAM,QAAQ,MAAM,SAAS;AAC7B,qCAAmC,OAAO,MAAM,UAAU,4BAA4B;AACtF,QAAM,UAAU,6BAA6B,MAAM;AACrD;AAEA,SAAS,4BAA4B,GAAG;AAEtC,SAAO,MAAM,SAAY,eAAe;AAC1C;AAEA,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAeA,IAAM;AAAA,EACJ;AACF,IAAI,iBAAAI;AAEJ,IAAI,sBAAsB;AAAA,EACxB;AACF;AAiBA,IAAM;AAAA,EACJ,yBAAyB;AAC3B,IAAI;AAGJ,IAAI,6BAA6B;AAAA,EAC/B,yBAAyB;AAC3B;AAYA,IAAM;AAAA,EACJ,YAAY;AACd,IAAI;AAEJ,IAAM;AAAA,EACJ,yBAAyB;AAC3B,IAAI;AAOJ,IAAI,UAAU,8BAA8B,aAAW,QAAQ;AAS/D,IAAM,aAAa,gBAAc;AAC/B,YAAU;AACZ;AAMA,IAAM,aAAa,MAAM;AAOzB,IAAM,eAAe,cAAY;AAC/B,UAAQ,MAAM;AACZ,QAAI,WAAW,MAAM;AAErB,QAAI;AACF,iBAAW,aAAa;AACxB,eAAS;AAAA,IACX,UAAE;AACA,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,IAAI,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF;AAwBA,UAAU,gBAAgB,OAAO;AAC/B,aAAW,QAAQ,OAAO;AACxB,eAAW,OAAO,MAAM;AACtB,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,IAAI,yBAAyB;AAc7B,IAAM;AAAA;AAAA,EACN,OAAO,WAAW,eAAe,OAAO,WAAW;AAAA;AAGnD,IAAM,WAAW,WAAS,CAAC;AAAA,CAC3B,UAAU,UAAU,iBAAiB;AAErC,IAAM,gBAAgB,OAAO,cAAc,eAAe,UAAU,YAAY;AAEhF,IAAI,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AACF;AAiBA,SAAS,oBAAoB,IAAI,cAAc;AAC7C,MAAI;AACJ,SAAO,IAAI,SAAS;AAClB,QAAI,CAAC,OAAO;AACV,cAAQ,CAAC;AAAA,IACX;AAEA,UAAM,MAAM,aAAa,GAAG,IAAI;AAEhC,QAAI,CAAC,OAAO,eAAe,KAAK,OAAO,GAAG,GAAG;AAC3C,YAAM,GAAG,IAAI,GAAG,GAAG,IAAI;AAAA,IACzB;AAEA,WAAO,MAAM,GAAG;AAAA,EAClB;AACF;AAQA,SAAS,uBAAuB,IAAI,cAAc;AAChD,MAAI;AACJ,MAAI;AAEJ,SAAO,IAAI,SAAS;AAClB,UAAM,MAAM,aAAa,GAAG,IAAI;AAEhC,QAAI,YAAY,KAAK;AACnB,aAAO;AAAA,IACT;AAEA,cAAU;AACV,iBAAa,GAAG,GAAG,IAAI;AACvB,WAAO;AAAA,EACT;AACF;AAQA,SAAS,sCAAsC,IAAI,cAAc;AAC/D,MAAI;AACJ,MAAI;AAEJ,QAAM,aAAa,IAAI,SAAS;AAC9B,UAAM,MAAM,aAAa,GAAG,IAAI;AAEhC,QAAI,YAAY,KAAK;AACnB,aAAO;AAAA,IACT;AAEA,cAAU;AACV,iBAAa,GAAG,GAAG,IAAI;AACvB,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,MAAM;AACvB,cAAU;AAAA,EACZ;AAEA,SAAO,CAAC,YAAY,UAAU;AAChC;AAEA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAAA,EAChB,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAClB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,qBAAqB;AACvB,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,2BAA2B;AAC7B,IAAI;AAEJ,IAAM;AAAA,EACJ,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,+BAA+B;AACjC,IAAI;AAEJ,IAAM;AAAA,EACJ,yBAAyB;AAAA,EACzB,qBAAqB;AACvB,IAAI;AAIJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAUJ,IAAM;AAAA,EACJ,uCAAuC;AACzC,IAAI;AAOJ,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAetB,IAAM,WAAN,MAAe;AAAA;AAAA,EAEb,YAAY,YAAY,eAAe;AACrC,oBAAgB,MAAM,UAAU,MAAM;AAEtC,oBAAgB,MAAM,aAAa,CAAC;AAEpC,oBAAgB,MAAM,eAAe,iBAAe;AAClD,WAAK,uBAAuB;AAC5B,aAAO,2BAA2B,KAAK,QAAQ,WAAW;AAAA,IAC5D,CAAC;AAED,oBAAgB,MAAM,cAAc,iBAAe;AACjD,WAAK,uBAAuB;AAC5B,aAAO,KAAK,YAAY,WAAW,EAAE,UAAU;AAAA,IACjD,CAAC;AAED,oBAAgB,MAAM,qBAAqB,SAAO;AAChD,WAAK,uBAAuB;AAE5B,WAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,gBAAgB,MAAM;AACvE,aAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,mBAAmB,OAAO;AAC3E,iBAAO,CAAC;AAAA,QACV;AAEA,cAAM,QAAQ,KAAK,OAAO,SAAS,EAAE;AAErC,eAAO,sBAAsB,MAAM,UAAU;AAAA,MAC/C;AAEA,YAAM,aAAa,KAAK,OAAO,SAAS,EAAE;AAE1C,YAAM,iBAAiB,KAAK,OAAO,SAAS,EAAE;AAE9C,cAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,kBAAkB,OAAO,eAAe,OAAO,IAAI,IAAI,kBAAkB,OAAO,sBAAsB,uBAAuB,CAAC,YAAY,cAAc,CAAC,CAAC,IAAI,sBAAsB,eAAe,OAAO,GAAG,CAAC;AAAA,QAClQ;AAAA,MACF,MAAM,CAAC,WAAW,IAAI,GAAG,KAAK,CAAC,eAAe,IAAI,GAAG,CAAC;AAAA,IACxD,CAAC;AAED,oBAAgB,MAAM,oBAAoB,CAAC;AAAA,MACzC;AAAA,IACF,MAAM;AACJ,WAAK,uBAAuB;AAC5B,aAAO,eAAe,KAAK,QAAQ,KAAK,OAAO,SAAS,EAAE,aAAa,GAAG;AAAA,IAC5E,CAAC;AAED,oBAAgB,MAAM,OAAO,YAAU;AACrC,WAAK,uBAAuB;AAC5B,YAAM,kBAAkB,IAAI,gBAAgB,MAAM,cAAc;AAChE,aAAO,eAAe;AAEtB,aAAO;AAAA,IACT,CAAC;AAED,oBAAgB,MAAM,YAAY,OAAM,WAAU;AAChD,WAAK,uBAAuB;AAC5B,YAAM,kBAAkB,IAAI,gBAAgB,MAAM,cAAc;AAChE,sBAAgB,OAAO;AAEvB,YAAM,OAAO,eAAe;AAG5B,sBAAgB,qBAAqB;AACrC,aAAO;AAAA,IACT,CAAC;AAED,SAAK,SAAS;AAAA,MACZ,SAAS,iBAAiB;AAAA,MAC1B;AAAA,MACA,UAAU,MAAM;AAAA,MAChB,cAAc,cAAY;AAExB,mBAAW,cAAc,SAAS,WAAW,WAAW;AAAA,MAC1D;AAAA,MACA,UAAU,aAAW;AACnB,cAAM,SAAS,WAAW;AAE1B,YAAI,OAAO,IAAI,OAAO,GAAG;AACvB,iBAAO,kBAAkB,OAAO,IAAI,OAAO,CAAC;AAAA,QAC9C;AAEA,cAAM,WAAW,QAAQ;AACzB,eAAO,IAAI,SAAS,QAAQ;AAC5B,eAAO;AAAA,MACT;AAAA,MACA,yBAAyB,OAAO;AAAA,QAC9B,SAAS,MAAM;AAAA,QAAC;AAAA,MAClB;AAAA,MACA,wBAAwB,MAAM;AAC5B,cAAM,WAAW,+BAA+B;AAAA,MAClD;AAAA,IACF;AAIA,eAAW,WAAW,KAAK,OAAO,SAAS,EAAE,YAAY;AACvD,uBAAiB,KAAK,QAAQ,SAAS,KAAK;AAC5C,0BAAoB,KAAK,QAAQ,SAAS,CAAC;AAAA,IAC7C;AAEA,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EAEA,SAAS;AACP,QAAI,KAAK,aAAa,GAAG;AACvB,UAAI,MAAuC;AACzC,cAAM,WAAW,qCAAqC;AAAA,MACxD,OAAO;AACL,oCAA4B,yDAAyD;AAAA,MACvF;AAAA,IACF;AAEA,SAAK;AACL,QAAI,WAAW;AACf,WAAO,MAAM;AACX,UAAI,CAAC,UAAU;AACb,mBAAW;AAEX,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB;AACrB,QAAI,CAAC,SAAS;AAEZ,aAAO,WAAW,MAAM,KAAK,SAAS,GAAG,EAAE;AAAA,IAC7C;AAAA,EACF;AAAA,EAEA,WAAW;AACT,SAAK;AAEL,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,OAAO,SAAS,EAAE,qBAAqB,QAAQ,aAAW,QAAQ,CAAC;AAExE,WAAK,OAAO,SAAS,EAAE,qBAAqB,MAAM;AAElD,UAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,MACF;AAAA,IAMF,WAAW,KAAK,YAAY,GAAG;AAC7B,UAAI,MAAuC;AACzC,oCAA4B,kCAAkC;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EAEA,yBAAyB;AACvB,QAAI,WAAW,+BAA+B,KAAK,KAAK,aAAa,GAAG;AACtE,UAAI,MAAuC;AACzC,oCAA4B,aAAa;AAAA,MAC3C;AAAA,IAGF;AAAA,EACF;AAAA,EAEA,oBAAoB;AAClB,SAAK,uBAAuB;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,QAAQ;AACN,SAAK,uBAAuB;AAC5B,WAAO,KAAK,OAAO,SAAS,EAAE,YAAY;AAAA,EAC5C;AAAA,EAEA,aAAa;AACX,SAAK,uBAAuB;AAC5B,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAOF;AAEA,SAAS,gBAAgB,OAAO,WAAW,cAAc,OAAO;AAC9D,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,UAAU,cAAc,0BAA0B,IAAI,UAAU;AACtE,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKL,aAAa;AAAA;AAAA;AAAA,MAGX,SAAS,cAAc,UAAU,UAAU;AAAA,MAC3C,SAAS,cAAc,UAAU,UAAU;AAAA,MAC3C,qBAAqB;AAAA,QAAE,GAAG,UAAU;AAAA,MACpC;AAAA,MACA,YAAY,IAAI,IAAI,UAAU,UAAU;AAAA,MACxC,YAAY,UAAU,WAAW,MAAM;AAAA,MACvC,mBAAmB,UAAU,kBAAkB,MAAM;AAAA,IACvD;AAAA,IACA,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,YAAY,IAAI,IAAI,WAAW,UAAU;AAAA;AAAA,IAEzC,gBAAgB,IAAI,IAAI,WAAW,cAAc;AAAA;AAAA,IAEjD,0BAA0B,oBAAI,IAAI;AAAA,IAClC,8BAA8B,oBAAI,IAAI;AAAA,IACtC,8BAA8B,oBAAI,IAAI;AAAA,IACtC,qCAAqC,CAAC;AAAA,IACtC,6BAA6B,oBAAI,IAAI;AAAA,IACrC,kBAAiB,oBAAI,IAAI,GAAE,IAAI,SAAS,MAAM,SAAS,UAAU,OAAO,CAAC;AAAA,IACzE,WAAW;AAAA,MACT,iBAAiB,oBAAI,IAAI;AAAA,MACzB,qBAAqB,oBAAI,IAAI;AAAA,MAC7B,8BAA8B,oBAAI,IAAI;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA,IAIA,sBAAsB,IAAI,IAAI,mBAAmB,WAAW,qBAAqB,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM;AAAA,IAAC,CAAC,CAAC,CAAC;AAAA,EACzH;AACF;AAGA,SAAS,cAAc,iBAAiB;AACtC,QAAM,WAAW,IAAI,SAAS,sBAAsB,CAAC;AACrD,SAAO,mBAAmB,OAAO,SAAS,IAAI,eAAe,IAAI;AACnE;AAGA,IAAM,CAAC,uBAAuB,4BAA4B,IAAI;AAAA;AAAA,EAC9D,CAAC,OAAO,YAAY;AAClB,QAAI;AAEJ,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,YAAY,YAAY,YAAY,uBAAuB,WAAW,cAAc,QAAQ,yBAAyB,SAAS,uBAAuB,WAAW,cAAc,kBAAkB,WAAW,YAAY;AAC7N,WAAO,IAAI,SAAS,gBAAgB,OAAO,SAAS,GAAG,MAAM,OAAO;AAAA,EACtE;AAAA,EAAG,CAAC,OAAO,YAAY;AACrB,QAAI,uBAAuB;AAE3B,WAAO,OAAO,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,QAAQ,wBAAwB,MAAM,SAAS,EAAE,cAAc,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,OAAO,IAAI,OAAO,MAAM,SAAS,EAAE,YAAY,OAAO,IAAI,QAAQ,wBAAwB,MAAM,SAAS,EAAE,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,OAAO;AAAA,EAC1Y;AAAC;AAED,gCAAgC,4BAA4B;AAE5D,SAAS,cAAc,OAAO,UAAU,UAAU;AAChD,QAAM,WAAW,sBAAsB,OAAO,OAAO;AAErD,MAAI,CAAC,SAAS,WAAW,GAAG;AAC1B,iCAA6B;AAC7B,WAAO,sBAAsB,OAAO,OAAO;AAAA,EAC7C;AAEA,SAAO;AACT;AAEA,IAAM,kBAAN,cAA8B,SAAS;AAAA,EACrC,YAAY,UAAU,OAAO;AAC3B,UAAM,gBAAgB,SAAS,kBAAkB,GAAG,SAAS,kBAAkB,EAAE,SAAS,EAAE,aAAa,IAAI,GAAG,SAAS,WAAW,CAAC;AAErI,oBAAgB,MAAM,UAAU,MAAM;AAEtC,oBAAgB,MAAM,OAAO,CAAC,aAAa,sBAAsB;AAC/D,WAAK,uBAAuB;AAC5B,YAAM,QAAQ,KAAK,kBAAkB;AAKrC,WAAK,OAAO,MAAM;AAChB,4BAAoB,OAAO,YAAY,KAAK,CAAC;AAC7C,yBAAiB,KAAK,kBAAkB,GAAG,aAAa,iBAAiB;AAAA,MAC3E,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,MAAM,SAAS,iBAAe;AAC5C,WAAK,uBAAuB;AAC5B,YAAM,QAAQ,KAAK,kBAAkB;AAErC,WAAK,OAAO,MAAM;AAChB,4BAAoB,OAAO,YAAY,KAAK,CAAC;AAC7C,yBAAiB,KAAK,kBAAkB,GAAG,aAAa,eAAe;AAAA,MACzE,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,MAAM,uCAAuC,YAAU;AACrE,WAAK,uBAAuB;AAC5B,YAAM,QAAQ,KAAK,kBAAkB;AAErC,qBAAe,MAAM;AACnB,mBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,GAAG;AACrC,8BAAoB,OAAO,GAAG,CAAC;AAC/B,sCAA4B,OAAO,IAAI,sBAAsB,CAAC,GAAG,CAAC;AAAA,QACpE;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,SAAK,SAAS;AAAA,EAChB;AAEF;AAEA,IAAI,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AACxC,IAAI,oBAAoB,gBAAgB;AAExC,IAAI,oBAAiC,OAAO,OAAO;AAAA,EACjD,WAAW;AAAA,EACX,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,eAAe;AACjB,CAAC;AAaD,SAAS,aAAa,MAAM;AAC1B,QAAM,SAAS,oBAAI,IAAI;AAEvB,aAAW,OAAO,MAAM;AACtB,eAAW,SAAS,KAAK;AACvB,aAAO,IAAI,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB;AAEvB,IAAM;AAAA,EACJ;AACF,IAAI,aAAAxC;AAWJ,SAAS,eAAe,cAAc;AAEpC,QAAM,MAAM,OAAO,YAAY;AAE/B,MAAI,IAAI,YAAY,gBAAgB,OAAO,iBAAiB,YAAY;AAEtE,QAAI,UAAU,aAAa;AAAA,EAC7B;AAEA,SAAO;AACT;AAEA,IAAI,wBAAwB;AAO5B,IAAM;AAAA,EACJ,yBAAyB;AAAA,EACzB,qBAAqB;AACvB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,oCAAoC;AACtC,IAAI;AAEJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAEJ,IAAM;AAAA,EACJ,YAAY;AACd,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAClB,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AAAA,EACrB,WAAW;AACb,IAAI;AAEJ,IAAM;AAAA,EACJ,sBAAsB;AACxB,IAAI;AAEJ,IAAM;AAAA,EACJ,gCAAgC;AAClC,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAIJ,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AACF,IAAI,aAAAA;AAgBJ,SAAS,gBAAgB;AACvB,QAAM,WAAW,8DAA8D;AACjF;AAEA,IAAM,eAAe,OAAO,OAAO;AAAA,EACjC,SAAS,iBAAiB;AAAA,EAC1B,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AAAA,EACV,yBAAyB;AAAA,EACzB,wBAAwB;AAC1B,CAAC;AACD,IAAI,+BAA+B;AAEnC,SAAS,sBAAsB,OAAO;AACpC,MAAI,8BAA8B;AAChC,UAAM,WAAW,mJAAmJ;AAAA,EACtK;AAEA,QAAM,aAAa,MAAM,SAAS;AAElC,MAAI,WAAW,aAAa,MAAM;AAChC,QAAI,WAAW,+BAA+B,KAAK,WAAW,oDAAoD,GAAG;AAKnH,UAAI,WAAW,cAAc,GAAG;AAC9B,yCAAiC,KAAK;AAAA,MACxC;AAAA,IACF;AAEA,UAAM,UAAU,WAAW,YAAY;AACvC,UAAM,cAAc,0BAA0B;AAC9C,eAAW,WAAW;AAAA,MAAE,GAAG,WAAW;AAAA,MACpC,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY,oBAAI,IAAI;AAAA,MACpB,qBAAqB,CAAC;AAAA,IACxB;AACA,eAAW,gBAAgB,IAAI,aAAa,aAAa,kBAAkB,WAAW,gBAAgB,IAAI,OAAO,CAAC,CAAC,CAAC;AAAA,EACtH;AACF;AAEA,IAAM,aAAa,aAAAA,QAAM,cAAc;AAAA,EACrC,SAAS;AACX,CAAC;AAED,IAAM,cAAc,MAAM,WAAW,UAAU;AAG/C,IAAM,uBAAuB,aAAAA,QAAM,cAAc,IAAI;AAErD,SAAS,yBAAyB;AAChC,QAAM,gBAAgB,WAAW,oBAAoB;AAErD,MAAI,iBAAiB,MAAM;AACzB,gCAA4B,qIAA+I;AAAA,EAC7K;AAEA,SAAO;AACT;AAEA,SAAS,iBAAiB,OAAO,YAAY,WAAW;AACtD,QAAM,iBAAiB,qBAAqB,OAAO,WAAW,UAAU,UAAU;AAElF,aAAW,OAAO,gBAAgB;AAChC,UAAM,QAAQ,WAAW,6BAA6B,IAAI,GAAG;AAE7D,QAAI,OAAO;AACT,iBAAW,CAAC,QAAQ,CAAC,YAAY,QAAQ,CAAC,KAAK,OAAO;AACpD,iBAAS,SAAS;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,4BAA4B,OAAO;AAC1C,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,YAAY,WAAW;AAE7B,QAAM,aAAa,UAAU;AAE7B,MAAI,WAAW,MAAM;AAEnB,eAAW,CAAC,KAAK,aAAa,KAAK,WAAW,8BAA8B;AAC1E,UAAI,WAAW,IAAI,GAAG,GAAG;AACvB,mBAAW,CAAC,GAAG,YAAY,KAAK,eAAe;AAC7C,uBAAa,KAAK;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,eAAW,CAAC,GAAG,YAAY,KAAK,WAAW,0BAA0B;AACnE,mBAAa,KAAK;AAAA,IACpB;AAEA,QAAI,CAAC,YAAY,EAAE,SAAS,WAAW,4BAA4B,OAAO,GAAG;AAG3E,uBAAiB,OAAO,YAAY,SAAS;AAO7C,iBAAW,4BAA4B,QAAQ,QAAM,GAAG,CAAC;AACzD,iBAAW,4BAA4B,MAAM;AAAA,IAC/C;AAAA,EACF;AAIA,aAAW,oCAAoC,QAAQ,QAAM,GAAG,SAAS,CAAC;AAC1E,aAAW,oCAAoC,OAAO,GAAG,WAAW,oCAAoC,MAAM;AAChH;AAEA,SAAS,SAAS,OAAO;AACvB,QAAM,aAAa,MAAM,SAAS;AAClC,aAAW;AAEX,MAAI;AACF,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAGJ,QAAI,YAAY,MAAM;AACpB;AAAA,IACF;AAIA,eAAW,eAAe,WAAW;AACrC,eAAW,cAAc;AACzB,eAAW,WAAW;AACtB,gCAA4B,KAAK;AAEjC,QAAI,WAAW,gBAAgB,MAAM;AACnC,iBAAW,gBAAgB,OAAO,WAAW,aAAa,OAAO;AAAA,IACnE,OAAO;AACL,kCAA4B,2DAA2D,QAAQ;AAAA,IACjG;AAEA,eAAW,eAAe;AAE1B,QAAI,WAAW,+BAA+B,GAAG;AAG/C,UAAI,YAAY,MAAM;AACpB,yCAAiC,KAAK;AAAA,MACxC;AAAA,IACF;AAAA,EACF,UAAE;AACA,eAAW;AAAA,EACb;AACF;AASA,SAAS,QAAQ;AAAA,EACf;AACF,GAAG;AACD,QAAM,WAAW,YAAY;AAC7B,QAAM,CAAC,EAAE,QAAQ,IAAI,SAAS,CAAC,CAAC;AAEhC,2BAAyB,MAAM,SAAS,CAAC,CAAC,CAAC;AAC3C,YAAU,MAAM;AAEd,6BAAyB,MAAM,SAAS,CAAC,CAAC,CAAC;AAK3C,WAAO,MAAM;AACX,+BAAyB,MAAM;AAAA,MAAC,CAAC;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,wBAAwB,CAAC;AAC7B,YAAU,MAAM;AAId,iBAAa,iBAAiB,WAAW,MAAM;AAC7C,eAAS,SAAS,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,IAAI,MAAuC;AACzC,MAAI,OAAO,WAAW,eAAe,CAAC,OAAO,oBAAoB;AAC/D,WAAO,qBAAqB,CAAC;AAAA,EAC/B;AACF;AAIA,SAAS,6BAA6B,OAAO,iBAAiB;AAC5D,QAAM,UAAU,sBAAsB;AACtC,kBAAgB;AAAA,IACd,KAAK,CAACyC,OAAM,UAAU;AACpB,YAAM,QAAQ,QAAQ;AACtB,YAAM,SAAS,eAAe,OAAO,OAAOA,MAAK,KAAK,KAAK;AAC3D,YAAM,eAAe,IAAI,IAAI,OAAO,KAAK,CAAC;AAC1C,YAAM,oBAAoB,MAAM,kBAAkB,MAAM;AAExD,iBAAW,KAAK,cAAc;AAC5B,0BAAkB,OAAO,CAAC;AAAA,MAC5B;AAEA,cAAQ,cAAc;AAAA,QAAE,GAAG;AAAA,QACzB,YAAY,iBAAiB,MAAM,YAAY,YAAY;AAAA,QAC3D,YAAY,uBAAuB,MAAM,YAAY,MAAM;AAAA;AAAA,QAE3D;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B,gBAAc;AAEtC,iBAAW,QAAQ,CAAC,GAAG,MAAM;AAC3B,gBAAQ,cAAc,qCAAqC,QAAQ,aAAa,GAAG,CAAC;AAAA,MACtF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAQA,SAAS,kBAAkB,iBAAiB;AAE1C,QAAM,WAAW,gBAAgB,eAAe;AAChD,QAAM,aAAa,SAAS,kBAAkB,EAAE,SAAS;AAEzD,WAAS,OAAO;AAKhB,aAAW,qBAAqB,QAAQ,aAAW,QAAQ,CAAC;AAC5D,aAAW,qBAAqB,MAAM;AACtC,SAAO;AACT;AAEA,IAAI,SAAS;AAEb,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB;AACF,GAAG;AAiBD,MAAI;AAEJ,QAAM,WAAW,aAAW;AAC1B,UAAM,SAAS,cAAc,QAAQ;AAErC,QAAI,OAAO,IAAI,OAAO,GAAG;AACvB,aAAO,kBAAkB,OAAO,IAAI,OAAO,CAAC;AAAA,IAC9C;AAEA,UAAM,WAAW,QAAQ;AACzB,WAAO,IAAI,SAAS,QAAQ;AAC5B,WAAO;AAAA,EACT;AAEA,QAAM,0BAA0B,CAAC,UAAU,QAAQ;AACjD,QAAI,OAAO,MAAM;AAEf,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,SAAS,QAAQ,SAAS;AAC9B,YAAM,KAAK;AACX,+BAAyB,IAAI,IAAI,QAAQ;AACzC,aAAO;AAAA,QACL,SAAS,MAAM;AACb,mCAAyB,OAAO,EAAE;AAAA,QACpC;AAAA,MACF;AAAA,IACF,OAAO;AAEL,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,SAAS,QAAQ,SAAS;AAE9B,UAAI,CAAC,6BAA6B,IAAI,GAAG,GAAG;AAC1C,qCAA6B,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,MACjD;AAEA,YAAM,KAAK;AACX,wBAAkB,6BAA6B,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,QAAQ;AACzE,aAAO;AAAA,QACL,SAAS,MAAM;AACb,gBAAM,OAAO,6BAA6B,IAAI,GAAG;AAEjD,cAAI,MAAM;AACR,iBAAK,OAAO,EAAE;AAEd,gBAAI,KAAK,SAAS,GAAG;AACnB,2CAA6B,OAAO,GAAG;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,yBAAyB,cAAY;AACzC,0BAAsB,SAAS,OAAO;AAEtC,eAAW,KAAK,OAAO,KAAK,QAAQ,GAAG;AACrC,wBAAkB,SAAS,QAAQ,SAAS,EAAE,QAAQ,EAAE,oBAAoB,CAAC,IAAI,SAAS,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,QAAM,eAAe,cAAY;AAC/B,0BAAsB,SAAS,OAAO;AAEtC,UAAM,WAAW,kBAAkB,cAAc,QAAQ,QAAQ;AACjE,QAAI;AAEJ,QAAI;AACF,qCAA+B;AAC/B,iBAAW,SAAS,QAAQ;AAAA,IAC9B,UAAE;AACA,qCAA+B;AAAA,IACjC;AAEA,QAAI,aAAa,UAAU;AACzB;AAAA,IACF;AAEA,QAAI,MAAuC;AACzC,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO,mBAAmB,KAAK,QAAQ;AAAA,MACzC;AAAA,IACF;AAGA,kBAAc,QAAQ,WAAW;AAEjC,QAAI,YAAY,EAAE,OAAO;AACvB,uBAAiB,SAAS,SAAS,cAAc,SAAS,QAAQ;AAAA,IACpE;AAEA,sBAAkB,sBAAsB,OAAO,EAAE;AAAA,EACnD;AAEA,QAAM,wBAAwB,SAAS,IAAI;AAC3C,QAAM,2BAA2B,YAAY,OAAK;AAChD,0BAAsB,UAAU;AAAA,EAClC,GAAG,CAAC,qBAAqB,CAAC;AAC1B,QAAM,WAAW,sBAAsB,MAAM,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,IACpG,SAAS,iBAAiB;AAAA,IAC1B,UAAU,MAAM,cAAc;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,MAAI,aAAa,MAAM;AACrB,aAAS,UAAU;AAAA,EACrB;AAEA,kBAAgB,sBAAsB,MAAM,8BAA8B,OAAO,6BAA6B,SAAS,SAAS,0BAA0B,IAAI,mBAAmB,OAAO,kBAAkB,eAAe,IAAI,sBAAsB,CAAC;AACpP,QAAM,gBAAgB,QAAQ,MAAM,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,eAAe,MAAM,cAAc,QAAQ,YAAY,OAAO,GAAG,CAAC,aAAa,CAAC;AAExN,YAAU,MAAM;AAKd,UAAM,QAAQ,SAAS;AAEvB,eAAW,WAAW,IAAI,IAAI,MAAM,SAAS,EAAE,UAAU,GAAG;AAC1D,uBAAiB,OAAO,SAAS,KAAK;AAAA,IACxC;AAEA,WAAO,MAAM;AACX,iBAAW,WAAW,MAAM,SAAS,EAAE,YAAY;AACjD,sBAAc,OAAO,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,SAAoB,aAAAzC,QAAM,cAAc,WAAW,UAAU;AAAA,IAC3D,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,qBAAqB,UAAU;AAAA,IACjE,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,SAAS;AAAA,IAC3C;AAAA,EACF,CAAC,GAAG,QAAQ,CAAC;AACf;AAEA,SAAS,WAAW,OAAO;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,mBAAmB,YAAY;AAErC,MAAI,aAAa,SAAS,iBAAiB,YAAY,cAAc;AAGnE,WAAO,MAAM;AAAA,EACf;AAEA,SAAoB,aAAAA,QAAM,cAAc,qBAAqB,mBAAmB;AAClF;AAEA,SAAS,mBAAmB;AAC1B,SAAO,YAAY,EAAE,QAAQ;AAC/B;AAEA,IAAI,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,8BAA8B;AAAA,EAC9B,yCAAyC;AAC3C;AAaA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,MAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,KAAK;AACxC,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,2BAA2B;AAE/B,IAAM;AAAA,EACJ,WAAW;AAAA,EACX,QAAQ;AACV,IAAI,aAAAA;AAEJ,SAAS,YAAY,OAAO;AAC1B,QAAM,MAAM,SAAS;AACrB,cAAY,MAAM;AAEhB,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO,IAAI;AACb;AAEA,IAAI,qBAAqB;AAEzB,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AACvB,IAAI;AAEJ,IAAM;AAAA,EACJ,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,WAAW;AAAA,EACX,QAAQ;AACV,IAAI,aAAAA;AAEJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAYJ,SAAS,UAAU,UAAU;AAC3B,MAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,EACF;AAGA,SAAO,iBAAiB,QAAQ;AAClC;AAEA,SAAS,iBAAiB,UAAU;AAClC,QAAM,QAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAC5D,QAAM,cAAc,MAAM,IAAI,OAAK,aAAa,kBAAkB,IAAI,EAAE,GAAG;AAC3E,QAAM,WAAW,cAAc;AAC/B,cAAY,MAAM;AAChB,QAAI,CAAC,WAAW,+BAA+B,GAAG;AAChD;AAAA,IACF;AAEA,UAAM,QAAQ,SAAS;AAEvB,QAAI,UAAU,WAAW,CAAC,SAAS;AAGjC,aAAO,aAAa,UAAU,OAAO;AACrC,gBAAU,UAAU;AAAA,IACtB,OAAO;AACL,iBAAW,KAAK,aAAa;AAC3B,4BAAoB,OAAO,GAAG,CAAC;AAAA,MACjC;AAAA,IACF;AAEA,WAAO,MAAM;AACX,iBAAW,KAAK,aAAa;AAC3B,4BAAoB,OAAO,GAAG,EAAE;AAAA,MAClC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,GAAG,WAAW,CAAC;AAK7B,QAAM,YAAY,SAAS;AAC3B,QAAM,sBAAsB,mBAAmB,WAAW;AAE1D,MAAI,CAAC,YAAY,wBAAwB,UAAa,CAAC,yBAAyB,qBAAqB,WAAW,IAAI;AAClH,UAAM,QAAQ,SAAS;AAEvB,eAAW,KAAK,aAAa;AAC3B,0BAAoB,OAAO,GAAG,CAAC;AAAA,IACjC;AAEA,QAAI,qBAAqB;AACvB,iBAAW,KAAK,qBAAqB;AACnC,4BAAoB,OAAO,GAAG,EAAE;AAAA,MAClC;AAAA,IACF;AAEA,QAAI,UAAU,SAAS;AACrB,aAAO,aAAa,UAAU,OAAO;AAAA,IACvC;AAEA,cAAU,UAAU,OAAO,WAAW,MAAM;AAC1C,gBAAU,UAAU;AAEpB,iBAAW,KAAK,aAAa;AAC3B,4BAAoB,OAAO,GAAG,EAAE;AAAA,MAClC;AAAA,IACF,GAAG,qBAAqB;AAAA,EAC1B;AACF;AAEA,IAAI,mBAAmB;AA0BvB,SAAS,mBAAmB;AA2B1B,SAAO;AACT;AAEA,IAAI,0BAA0B;AAE9B,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,6CAA6C;AAAA,EAC7C,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,sBAAsB;AACxB,IAAI;AAEJ,IAAM;AAAA,EACJ,wBAAwB;AAAA,EACxB,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,2BAA2B;AAAA,EAC3B,wBAAwB;AAC1B,IAAI;AAIJ,IAAM;AAAA,EACJ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AACZ,IAAI,aAAAA;AAEJ,IAAM;AAAA,EACJ,kBAAkB;AACpB,IAAI;AAIJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAcJ,SAAS,eAAe,UAAU,aAAa,UAAU;AAIvD,MAAI,SAAS,UAAU,YAAY;AACjC,WAAO,SAAS;AAAA,EAClB,WAAW,SAAS,UAAU,WAAW;AACvC,UAAM,UAAU,IAAI,QAAQ,aAAW;AACrC,YAAM,8BAA8B,SAAS,QAAQ,SAAS,EAAE;AAChE,kCAA4B,IAAI,OAAO;AAGvC,UAAI,WAAW,iBAAiB,SAAS,QAAQ,GAAG;AAClD,iBAAS,SAAS,QAAQ,MAAM;AAC9B,sCAA4B,OAAO,OAAO;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAGD,UAAM;AAAA,EACR,WAAW,SAAS,UAAU,YAAY;AACxC,UAAM,SAAS;AAAA,EACjB,OAAO;AACL,UAAM,WAAW,mCAAmC,YAAY,GAAG,GAAG;AAAA,EACxE;AACF;AAEA,SAAS,oBAAoB,aAAa,UACxC;AACA,MAAI,CAAC,gBAAgB,WAAW,GAAG;AACjC,UAAM,WAAW,uBAAuB,QAAQ,0CAA0C,OAAO,WAAW,CAAC,EAAE;AAAA,EACjH;AACF;AAMA,SAAS,gCAAgC;AACvC,QAAM,gBAAgB,wBAAwB;AAC9C,QAAM,WAAW,cAAc;AAE/B,QAAM,CAAC,EAAE,WAAW,IAAI,WAAW,CAAC,CAAC;AACrC,QAAM,mBAAmB,SAAS,oBAAI,IAAI,CAAC;AAC3C,mBAAiB,UAAU,oBAAI,IAAI;AAEnC,QAAM,wBAAwB,SAAS,oBAAI,IAAI,CAAC;AAChD,QAAM,gBAAgB,SAAS,oBAAI,IAAI,CAAC;AACxC,QAAM,kBAAkB,cAAc,SAAO;AAC3C,UAAM,MAAM,cAAc,QAAQ,IAAI,GAAG;AAEzC,QAAI,KAAK;AACP,UAAI,QAAQ;AACZ,oBAAc,QAAQ,OAAO,GAAG;AAAA,IAClC;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,cAAc,cAAc,CAAC,QAAQ,QAAQ;AACjD,QAAI,cAAc,QAAQ,IAAI,GAAG,GAAG;AAClC,kBAAY,CAAC,CAAC;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,cAAY,MAAM;AAChB,UAAM,QAAQ,SAAS;AACvB,0BAAsB,iBAAiB,SAAS,sBAAsB,OAAO,EAAE,QAAQ,SAAO;AAC5F,UAAI,cAAc,QAAQ,IAAI,GAAG,GAAG;AAClC,oCAA4B,uCAAuC,GAAG,GAAG;AACzE;AAAA,MACF;AAEA,YAAM,MAAM,yBAAyB,OAAO,IAAI,sBAAsB,GAAG,GAAG,CAAA0C,WAAS,YAAYA,QAAO,GAAG,GAAG,aAAa;AAC3H,oBAAc,QAAQ,IAAI,KAAK,GAAG;AAmBlC,YAAM,QAAQ,MAAM,SAAS;AAE7B,UAAI,MAAM,UAAU;AAClB,cAAM,SAAS,EAAE,oCAAoC,KAAK,MAAM;AAC9D,sBAAY,MAAM,SAAS,GAAG,GAAG;AAAA,QACnC,CAAC;AAAA,MACH,OAAO;AACL,oBAAY,MAAM,SAAS,GAAG,GAAG;AAAA,MACnC;AAAA,IACF,CAAC;AACD,0BAAsB,sBAAsB,SAAS,iBAAiB,OAAO,EAAE,QAAQ,SAAO;AAC5F,sBAAgB,GAAG;AAAA,IACrB,CAAC;AACD,0BAAsB,UAAU,iBAAiB;AAAA,EACnD,CAAC;AAED,cAAY,MAAM;AAChB,UAAM,uBAAuB,cAAc;AAE3C,0BAAsB,iBAAiB,SAAS,IAAI,IAAI,qBAAqB,KAAK,CAAC,CAAC,EAAE,QAAQ,SAAO;AACnG,YAAM,MAAM,yBAAyB,SAAS,SAAS,IAAI,sBAAsB,GAAG,GAAG,WAAS,YAAY,OAAO,GAAG,GAAG,aAAa;AACtI,2BAAqB,IAAI,KAAK,GAAG;AAAA,IACnC,CAAC;AACD,WAAO,MAAM,qBAAqB,QAAQ,CAAC,GAAG,QAAQ,gBAAgB,GAAG,CAAC;AAAA,EAC5E,GAAG,CAAC,eAAe,UAAU,iBAAiB,WAAW,CAAC;AAC1D,SAAO,UAAU,MAAM;AAErB,aAASC,mBAAkB,aAAa;AACtC,UAAI,MAAuC;AACzC,4BAAoB,aAAa,mBAAmB;AAAA,MACtD;AAEA,aAAO,uBAAqB;AAC1B,yBAAiB,SAAS,SAAS,aAAa,iBAAiB;AAAA,MACnE;AAAA,IACF;AAGA,aAASC,qBAAoB,aAAa;AACxC,UAAI,MAAuC;AACzC,4BAAoB,aAAa,qBAAqB;AAAA,MACxD;AAEA,aAAO,MAAM,iBAAiB,SAAS,SAAS,aAAa,eAAe;AAAA,IAC9E;AAGA,aAASC,wBAAuB,aAAa;AAC3C,UAAI;AAEJ,UAAI,MAAuC;AACzC,4BAAoB,aAAa,wBAAwB;AAAA,MAC3D;AAEA,UAAI,CAAC,iBAAiB,QAAQ,IAAI,YAAY,GAAG,GAAG;AAClD,yBAAiB,UAAU,mBAAmB,iBAAiB,SAAS,YAAY,GAAG;AAAA,MACzF;AAGA,YAAM,aAAa,SAAS,QAAQ,SAAS;AAC7C,aAAO,2BAA2B,SAAS,SAAS,aAAa,YAAY,EAAE,SAAS,uBAAuB,WAAW,cAAc,QAAQ,yBAAyB,SAAS,uBAAuB,WAAW,cAAc,WAAW,WAAW;AAAA,IAC1P;AAGA,aAASC,gBAAe,aAAa;AACnC,UAAI,MAAuC;AACzC,4BAAoB,aAAa,gBAAgB;AAAA,MACnD;AAEA,YAAM,WAAWD,wBAAuB,WAAW;AACnD,aAAO,eAAe,UAAU,aAAa,QAAQ;AAAA,IACvD;AAGA,aAASE,gBAAe,aAAa;AACnC,UAAI,MAAuC;AACzC,4BAAoB,aAAa,gBAAgB;AAAA,MACnD;AAEA,aAAO,CAACD,gBAAe,WAAW,GAAGH,mBAAkB,WAAW,CAAC;AAAA,IACrE;AAGA,aAASK,wBAAuB,aAAa;AAC3C,UAAI,MAAuC;AACzC,4BAAoB,aAAa,wBAAwB;AAAA,MAC3D;AAEA,aAAO,CAACH,wBAAuB,WAAW,GAAGF,mBAAkB,WAAW,CAAC;AAAA,IAC7E;AAEA,WAAO;AAAA,MACL,gBAAgBG;AAAA,MAChB,wBAAwBD;AAAA,MACxB,gBAAgBE;AAAA,MAChB,wBAAwBC;AAAA,MACxB,mBAAmBL;AAAA,MACnB,qBAAqBC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AACjC;AAEA,IAAM,iDAAiD;AAAA,EACrD,SAAS;AACX;AAEA,SAAS,2CAA2C,aAAa;AAC/D,QAAM,WAAW,cAAc;AAC/B,QAAM,gBAAgB,wBAAwB;AAC9C,QAAM,cAAc,cAAc,MAAM;AACtC,QAAI;AAEJ,QAAI,MAAuC;AACzC,qDAA+C;AAAA,IACjD;AAEA,UAAM,QAAQ,SAAS;AACvB,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,YAAY,YAAY,EAAE,SAAS,wBAAwB,WAAW,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB,WAAW,cAAc,WAAW;AACjM,UAAM,WAAW,2BAA2B,OAAO,aAAa,SAAS;AACzE,WAAO;AAAA,MACL;AAAA,MACA,KAAK,YAAY;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,CAAC;AAE1B,QAAM,0BAA0B,cAAc,cAAY;AACxD,QAAI;AACJ,WAAO,MAAM;AACX,UAAI,YAAY;AAEhB,YAAM,YAAY,SAAS;AAE3B,WAAK,aAAa,eAAe,QAAQ,eAAe,UAAU,WAAW,SAAS,GAAG,UAAU,QAAQ,OAAO,cAAc,eAAe,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS,UAAU,KAAK;AAC3N,eAAO;AAAA,MACT;AAEA,kBAAY;AACZ,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,sBAAsB,UAAU,MAAM,wBAAwB,WAAW,GAAG,CAAC,aAAa,uBAAuB,CAAC;AACxH,QAAM,YAAY,cAAc,YAAU;AACxC,UAAM,QAAQ,SAAS;AACvB,UAAM,eAAe,yBAAyB,OAAO,aAAa,QAAQ,aAAa;AACvF,WAAO,aAAa;AAAA,EACtB,GAAG,CAAC,UAAU,aAAa,aAAa,CAAC;AACzC,SAAO;AAAA,IAAuB;AAAA,IAAW;AAAA;AAAA,IACzC;AAAA;AAAA,EACA,EAAE;AACJ;AAEA,SAAS,sCAAsC,aAAa;AAC1D,QAAM,WAAW,cAAc;AAC/B,QAAM,cAAc,cAAc,MAAM;AACtC,QAAI;AAEJ,UAAM,QAAQ,SAAS;AACvB,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,YAAY,YAAY,EAAE,SAAS,wBAAwB,WAAW,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB,WAAW,cAAc,WAAW;AACjM,WAAO,2BAA2B,OAAO,aAAa,SAAS;AAAA,EACjE,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,QAAM,yBAAyB,cAAc,MAAM;AACjD,QAAI,MAAuC;AACzC,qDAA+C;AAAA,IACjD;AAEA,WAAO,YAAY;AAAA,EACrB,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,gBAAgB,wBAAwB;AAC9C,QAAM,YAAY,cAAc,CAAC,aAAa,WAAW;AACvD,UAAM,QAAQ,SAAS;AACvB,UAAM,eAAe,yBAAyB,OAAO,aAAa,MAAM;AACtE,UAAI,CAAC,WAAW,sCAAsC,GAAG;AACvD,eAAO,OAAO;AAAA,MAChB;AAKA,YAAM,cAAc,YAAY;AAEhC,UAAI,CAAC,gBAAgB,QAAQ,GAAG,WAAW,GAAG;AAC5C,eAAO;AAAA,MACT;AAOA,sBAAgB,UAAU;AAAA,IAC5B,GAAG,aAAa;AAChB,WAAO,aAAa;AAAA,EACtB,GAAG,CAAC,UAAU,aAAa,eAAe,WAAW,CAAC;AACtD,QAAM,SAAS,yBAAyB;AAExC,MAAI,UAAU,MAAM;AAClB,UAAM,WAAW,oFAAoF;AAAA,EACvG;AAEA,QAAM,WAAW,mBAAmB,QAAQ,wBAAwB,SAAS;AAC7E,QAAM,kBAAkB,SAAS,QAAQ;AACzC,cAAY,MAAM;AAChB,oBAAgB,UAAU;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;AAEA,SAAS,0CAA0C,aAAa;AAC9D,QAAM,WAAW,cAAc;AAC/B,QAAM,gBAAgB,wBAAwB;AAE9C,QAAM,cAAc,cAAc,MAAM;AACtC,QAAI;AAEJ,QAAI,MAAuC;AACzC,qDAA+C;AAAA,IACjD;AAEA,UAAM,QAAQ,SAAS;AACvB,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,YAAY,YAAY,EAAE,SAAS,wBAAwB,WAAW,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB,WAAW,cAAc,WAAW;AACjM,WAAO,2BAA2B,OAAO,aAAa,SAAS;AAAA,EACjE,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,QAAM,WAAW,cAAc,OAAO;AAAA,IACpC,UAAU,YAAY;AAAA,IACtB,KAAK,YAAY;AAAA,EACnB,IAAI,CAAC,aAAa,YAAY,GAAG,CAAC;AAElC,QAAM,cAAc,cAAc,eAAa;AAC7C,UAAM,YAAY,SAAS;AAC3B,WAAO,UAAU,SAAS,GAAG,UAAU,QAAQ,KAAK,UAAU,QAAQ,UAAU,MAAM,YAAY;AAAA,EACpG,GAAG,CAAC,QAAQ,CAAC;AAEb,cAAY,MAAM;AAChB,UAAM,eAAe,yBAAyB,SAAS,SAAS,aAAa,YAAU;AACrF,eAAS,WAAW;AAAA,IACtB,GAAG,aAAa;AAEhB,aAAS,WAAW;AACpB,WAAO,aAAa;AAAA,EACtB,GAAG,CAAC,eAAe,aAAa,UAAU,WAAW,CAAC;AAEtD,QAAM,CAAC,OAAO,QAAQ,IAAI,WAAW,QAAQ;AAK7C,SAAO,MAAM,QAAQ,YAAY,MAAM,SAAS,EAAE,WAAW,MAAM;AACrE;AAEA,SAAS,8BAA8B,aAAa;AAClD,QAAM,WAAW,cAAc;AAE/B,QAAM,CAAC,EAAE,WAAW,IAAI,WAAW,CAAC,CAAC;AACrC,QAAM,gBAAgB,wBAAwB;AAC9C,QAAM,cAAc,cAAc,MAAM;AACtC,QAAI;AAEJ,QAAI,MAAuC;AACzC,qDAA+C;AAAA,IACjD;AAEA,UAAM,QAAQ,SAAS;AACvB,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,YAAY,YAAY,EAAE,SAAS,wBAAwB,WAAW,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB,WAAW,cAAc,WAAW;AACjM,WAAO,2BAA2B,OAAO,aAAa,SAAS;AAAA,EACjE,GAAG,CAAC,UAAU,WAAW,CAAC;AAC1B,QAAM,WAAW,YAAY;AAC7B,QAAM,kBAAkB,SAAS,QAAQ;AACzC,cAAY,MAAM;AAChB,oBAAgB,UAAU;AAAA,EAC5B,CAAC;AACD,cAAY,MAAM;AAChB,UAAM,QAAQ,SAAS;AACvB,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,eAAe,yBAAyB,OAAO,aAAa,YAAU;AAC1E,UAAI;AAEJ,UAAI,CAAC,WAAW,sCAAsC,GAAG;AACvD,eAAO,YAAY,CAAC,CAAC;AAAA,MACvB;AAEA,YAAM,cAAc,YAAY;AAEhC,UAAI,GAAG,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,UAAU,sBAAsB,GAAG,WAAW,IAAI;AAE9I,oBAAY,WAAW;AAAA,MACzB;AAEA,sBAAgB,UAAU;AAAA,IAC5B,GAAG,aAAa;AAkBhB,QAAI,WAAW,UAAU;AACvB,YAAM,SAAS,EAAE,oCAAoC,KAAK,MAAM;AAE9D,wBAAgB,UAAU;AAC1B,oBAAY,CAAC,CAAC;AAAA,MAChB,CAAC;AAAA,IACH,OAAO;AACL,UAAI;AAEJ,UAAI,CAAC,WAAW,sCAAsC,GAAG;AACvD,eAAO,YAAY,CAAC,CAAC;AAAA,MACvB;AAEA,YAAM,cAAc,YAAY;AAEhC,UAAI,GAAG,yBAAyB,gBAAgB,aAAa,QAAQ,2BAA2B,UAAU,uBAAuB,GAAG,WAAW,IAAI;AAEjJ,oBAAY,WAAW;AAAA,MACzB;AAEA,sBAAgB,UAAU;AAAA,IAC5B;AAEA,WAAO,aAAa;AAAA,EACtB,GAAG,CAAC,eAAe,aAAa,aAAa,QAAQ,CAAC;AACtD,SAAO;AACT;AAOA,SAAS,uBAAuB,aAAa;AAC3C,MAAI,MAAuC;AACzC,wBAAoB,aAAa,wBAAwB;AAAA,EAC3D;AAEA,MAAI,WAAW,+BAA+B,GAAG;AAE/C,qBAAiB,WAAW;AAAA,EAC9B;AAEA,SAAO;AAAA,IACL,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQpB,qBAAqB,8CAA8C,IAAI,6CAA6C;AAAA,IACpH,gBAAgB;AAAA,IAChB,QAAQ;AAAA,EACV,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW;AACnC;AASA,SAAS,eAAe,aAAa;AACnC,MAAI,MAAuC;AACzC,wBAAoB,aAAa,gBAAgB;AAAA,EACnD;AAEA,QAAM,WAAW,cAAc;AAC/B,QAAM,WAAW,uBAAuB,WAAW;AACnD,SAAO,eAAe,UAAU,aAAa,QAAQ;AACvD;AAOA,SAAS,kBAAkB,aAAa;AACtC,MAAI,MAAuC;AACzC,wBAAoB,aAAa,mBAAmB;AAAA,EACtD;AAEA,QAAM,WAAW,cAAc;AAC/B,SAAO,cAAc,uBAAqB;AACxC,qBAAiB,SAAS,SAAS,aAAa,iBAAiB;AAAA,EACnE,GAAG,CAAC,UAAU,WAAW,CAAC;AAC5B;AAMA,SAAS,oBAAoB,aAAa;AACxC,MAAI,MAAuC;AACzC,wBAAoB,aAAa,qBAAqB;AAAA,EACxD;AAEA,QAAM,WAAW,cAAc;AAC/B,SAAO,cAAc,MAAM;AACzB,qBAAiB,SAAS,SAAS,aAAa,eAAe;AAAA,EACjE,GAAG,CAAC,UAAU,WAAW,CAAC;AAC5B;AAUA,SAAS,eAAe,aAAa;AACnC,MAAI,MAAuC;AACzC,wBAAoB,aAAa,gBAAgB;AAAA,EACnD;AAEA,SAAO,CAAC,eAAe,WAAW,GAAG,kBAAkB,WAAW,CAAC;AACrE;AAQA,SAAS,uBAAuB,aAAa;AAC3C,MAAI,MAAuC;AACzC,wBAAoB,aAAa,wBAAwB;AAAA,EAC3D;AAEA,SAAO,CAAC,uBAAuB,WAAW,GAAG,kBAAkB,WAAW,CAAC;AAC7E;AAEA,SAAS,8BAA8B;AACrC,QAAM,WAAW,cAAc;AAC/B,SAAO,CAAC,QAAQ,sBAAsB,CAAC,MAAM;AAC3C,mBAAe,MAAM;AACnB,eAAS,QAAQ,uBAAuB,mBAAmB;AAC3D,aAAO,QAAQ,CAAC,OAAO,QAAQ,4BAA4B,SAAS,SAAS,IAAI,sBAAsB,GAAG,GAAG,KAAK,CAAC;AAAA,IACrH,CAAC;AAAA,EACH;AACF;AAMA,SAAS,mDAAmD,aAAa;AACvE,MAAI,MAAuC;AACzC,wBAAoB,aAAa,oDAAoD;AAErF,QAAI,CAAC,YAAY,EAAE,OAAO;AACxB,kCAA4B,mMAAmM;AAAA,IACjO;AAAA,EACF;AAEA,MAAI,WAAW,+BAA+B,GAAG;AAE/C,qBAAiB,WAAW;AAAA,EAC9B;AAEA,SAAO,0CAA0C,WAAW;AAC9D;AAEA,SAAS,2CAA2C,aAAa;AAC/D,MAAI,MAAuC;AACzC,wBAAoB,aAAa,4CAA4C;AAAA,EAC/E;AAEA,QAAM,WAAW,cAAc;AAC/B,QAAM,WAAW,mDAAmD,WAAW;AAC/E,SAAO,eAAe,UAAU,aAAa,QAAQ;AACvD;AAEA,SAAS,2CAA2C,aAAa;AAC/D,MAAI,MAAuC;AACzC,wBAAoB,aAAa,4CAA4C;AAAA,EAC/E;AAEA,SAAO,CAAC,2CAA2C,WAAW,GAAG,kBAAkB,WAAW,CAAC;AACjG;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAiBA,SAAS,UAAU,KAAK,UAAU;AAChC,QAAM,SAAS,oBAAI,IAAI;AAEvB,aAAW,CAAC,KAAK,KAAK,KAAK,KAAK;AAC9B,QAAI,SAAS,OAAO,GAAG,GAAG;AACxB,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB;AAiBvB,SAAS,UAAU,KAAK,UAAU;AAChC,QAAM,SAAS,oBAAI,IAAI;AAEvB,aAAW,SAAS,KAAK;AACvB,QAAI,SAAS,KAAK,GAAG;AACnB,aAAO,IAAI,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB;AAavB,SAAS,aAAa,MAAM;AAC1B,QAAM,SAAS,oBAAI,IAAI;AAEvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,WAAW,KAAK,CAAC,EAAE,KAAK;AAC9B,QAAI;AAEJ,WAAO,EAAE,UAAU,SAAS,KAAK,GAAG,MAAM;AAExC,aAAO,IAAI,QAAQ,OAAO,KAAK,CAAC,EAAE,IAAI,QAAQ,KAAK,CAAC;AAAA,IACtD;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,mBAAmB;AAEvB,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,SAAS;AAAA,EACT,OAAO;AACT,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AAAA,EACrB,wBAAwB;AAC1B,IAAI;AAEJ,IAAM;AAAA,EACJ,qBAAqB;AACvB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AAAA,EACb,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AACZ,IAAI,aAAA5C;AAEJ,IAAM;AAAA,EACJ,OAAO;AACT,IAAI;AAgBJ,SAAS,2BAA2B,UAAU;AAC5C,QAAM,WAAW,cAAc;AAC/B,cAAY,MAAM;AAChB,UAAM,MAAM,SAAS,QAAQ,wBAAwB,QAAQ;AAC7D,WAAO,IAAI;AAAA,EACb,GAAG,CAAC,UAAU,QAAQ,CAAC;AACzB;AAEA,SAAS,mCAAmC,OAAO;AACjD,QAAM,aAAa,MAAM,WAAW,MAAM;AAC1C,QAAM,8BAA8B,cAAc,iBAAiB,YAAY,CAAC,GAAG,MAAM;AACvF,UAAM,OAAO,UAAU,CAAC;AACxB,UAAM,cAAc,KAAK;AACzB,WAAO,eAAe,QAAQ,YAAY,SAAS,UAAU,EAAE,UAAU;AAAA,EAC3E,CAAC,GAAG,OAAK,EAAE,QAAQ;AAGnB,SAAO,iBAAiB,MAAM,kBAAkB,MAAM,GAAG,2BAA2B;AACtF;AA0BA,SAAS,qCAAqC,UAAU;AACtD,6BAA2B,cAAc,WAAS;AAChD,QAAI,eAAe,MAAM,SAAS,EAAE;AACpC,UAAM,cAAc,MAAM,SAAS,EAAE;AAErC,QAAI,CAAC,cAAc;AACjB,kCAA4B,mGAAmG;AAC/H,qBAAe,MAAM,SAAS,EAAE;AAAA,IAClC;AAEA,UAAM,aAAa,mCAAmC,WAAW;AACjE,UAAM,qBAAqB,mCAAmC,YAAY;AAC1E,UAAM,WAAW,cAAc,SAAS,UAAQ;AAC9C,UAAI,uBAAuB,wBAAwB,wBAAwB;AAE3E,aAAO;AAAA,QACL,sBAAsB;AAAA,UACpB,OAAO,yBAAyB,yBAAyB,KAAK,0BAA0B,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,UAAU,QAAQ,0BAA0B,SAAS,wBAAwB;AAAA,UACjP,aAAa,0BAA0B,yBAAyB,KAAK,0BAA0B,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,gBAAgB,QAAQ,2BAA2B,SAAS,yBAAyB;AAAA,QAClQ;AAAA,MACF;AAAA,IACF,CAAC;AAGD,UAAM,gBAAgB,iBAAiB,YAAY,YAAY,OAAK,WAAW,IAAI,CAAC,KAAK,mBAAmB,IAAI,CAAC,CAAC;AAClH,aAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB;AAAA,QAAE,GAAG,YAAY;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChB;AAEA,SAAS,6BAA6B,UAAU;AAC9C,6BAA2B,cAAc,WAAS;AAChD,UAAM,WAAW,gBAAgB,OAAO,QAAQ;AAChD,UAAM,mBAAmB,gBAAgB,OAAO,UAAU;AAC1D,aAAS;AAAA,MACP;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChB;AAGA,SAAS,oBAAoB;AAC3B,QAAM,WAAW,cAAc;AAC/B,QAAM,CAAC,UAAU,WAAW,IAAI,WAAW,MAAM,gBAAgB,SAAS,OAAO,CAAC;AAClF,QAAM,mBAAmB,mBAAmB,QAAQ;AACpD,QAAM,YAAY,SAAS;AAC3B,QAAM,aAAa,SAAS;AAC5B,6BAA2B,cAAc,WAAS,YAAY,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAE1F,cAAY,MAAM;AAChB,UAAM,UAAU,SAAS,OAAO;AAEhC,QAAI,UAAU,WAAW,CAAC,SAAS;AACjC,UAAI;AAEJ,aAAO,aAAa,UAAU,OAAO;AACrC,gBAAU,UAAU;AACpB,OAAC,sBAAsB,WAAW,aAAa,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,KAAK,UAAU;AACpI,iBAAW,UAAU;AAAA,IACvB;AAEA,WAAO,MAAM;AAMX,aAAO,WAAW,SAAS,EAAE;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AAGb,MAAI,qBAAqB,YAAY,CAAC,SAAS;AAE7C,QAAI,UAAU,SAAS;AACrB,UAAI;AAEJ,aAAO,aAAa,UAAU,OAAO;AACrC,gBAAU,UAAU;AACpB,OAAC,uBAAuB,WAAW,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,UAAU;AACvI,iBAAW,UAAU;AAAA,IACvB;AAEA,eAAW,UAAU,SAAS,OAAO;AACrC,cAAU,UAAU,OAAO,WAAW,MAAM;AAC1C,UAAI;AAEJ,gBAAU,UAAU;AACpB,OAAC,uBAAuB,WAAW,aAAa,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,UAAU;AACvI,iBAAW,UAAU;AAAA,IACvB,GAAG,qBAAqB;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,aAAa,OAAO,UAAU;AACrC,MAAI;AAEJ,QAAM,aAAa,MAAM,SAAS;AAClC,QAAM,QAAQ,uBAAuB,WAAW,cAAc,QAAQ,yBAAyB,SAAS,uBAAuB,WAAW;AAC1I,QAAM,OAAO,SAAS,kBAAkB,EAAE,SAAS,EAAE;AACrD,iBAAe,MAAM;AACnB,UAAM,eAAe,oBAAI,IAAI;AAE7B,eAAW,QAAQ,CAAC,KAAK,WAAW,KAAK,GAAG,KAAK,WAAW,KAAK,CAAC,GAAG;AACnE,iBAAW,OAAO,MAAM;AACtB,YAAI,sBAAsB;AAE1B,cAAM,uBAAuB,KAAK,WAAW,IAAI,GAAG,OAAO,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,gBAAgB,uBAAuB,KAAK,WAAW,IAAI,GAAG,OAAO,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,aAAa,UAAU,GAAG,EAAE,4BAA4B;AACtU,uBAAa,IAAI,GAAG;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAEA,iBAAa,QAAQ,SAAO;AAC1B,+BAAyB,OAAO,IAAI,sBAAsB,GAAG,GAAG,KAAK,WAAW,IAAI,GAAG,IAAI,kBAAkB,KAAK,WAAW,IAAI,GAAG,CAAC,IAAI,eAAe;AAAA,IAC1J,CAAC;AACD,UAAM,aAAa,YAAU;AAAA,MAAE,GAAG;AAAA,MAChC,SAAS,SAAS,MAAM;AAAA,IAC1B,EAAE;AAAA,EACJ,CAAC;AACH;AAEA,SAAS,wBAAwB;AAC/B,QAAM,WAAW,cAAc;AAC/B,SAAO,cAAc,cAAY,aAAa,SAAS,SAAS,QAAQ,GAAG,CAAC,QAAQ,CAAC;AACvF;AAEA,IAAI,uBAAuB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uCAAuC;AACzC;AAEA,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,SAAS,wBAAwB;AAC/B,QAAM,WAAW,cAAc;AAE/B,SAAO,CAAC;AAAA,IACN;AAAA,EACF,MAAM,eAAe,SAAS,SAAS,SAAS,QAAQ,SAAS,EAAE,aAAa,GAAG;AACrF;AAEA,IAAI,+BAA+B;AAEnC,IAAM;AAAA,EACJ,WAAW;AACb,IAAI;AAEJ,IAAM;AAAA,EACJ,YAAY;AAAA,EACZ,aAAa;AACf,IAAI;AAIJ,IAAM;AAAA,EACJ,SAAS;AACX,IAAI,aAAAA;AAEJ,SAAS,kCAAkC;AAGzC,MAAI,YAAY,EAAE,SAAS,kBAAkB;AAE3C,YAAQ,KAAK,iLAAiL;AAAA,EAChM;AAEA,QAAM,QAAQ,cAAc,EAAE;AAC9B,SAAO,UAAU,MAAM;AAErB,aAAS,aAAa;AAAA,MACpB;AAAA,IACF,GAAG;AACD,aAAoB,aAAAA,QAAM,cAAc,cAAc;AAAA,QACpD,gBAAgB;AAAA,MAClB,GAAG,QAAQ;AAAA,IACb;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACZ;AAEA,IAAI,yCAAyC;AAE7C,IAAM;AAAA,EACJ,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAClB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,SAAS;AACX,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAC5B,IAAI;AAIJ,SAAS,OAAO,aAAa;AAC3B,SAAO,UAAU,YAAY,GAAG,EAAE,aAAa;AACjD;AAEA,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,OAAO,WAAW;AAC5B,oBAAgB,MAAM,UAAU,MAAM;AAEtC,oBAAgB,MAAM,cAAc,MAAM;AAE1C,oBAAgB,MAAM,YAAY,MAAM;AAExC,oBAAgB,MAAM,OAAO,iBAAe;AAC1C,UAAI,KAAK,SAAS,IAAI,YAAY,GAAG,GAAG;AAEtC,eAAO,KAAK,SAAS,IAAI,YAAY,GAAG;AAAA,MAC1C;AAEA,UAAI,CAAC,OAAO,WAAW,GAAG;AACxB,cAAM,WAAW,wDAAwD;AAAA,MAC3E;AAEA,YAAM,WAAW,2BAA2B,KAAK,QAAQ,aAAa,KAAK,UAAU;AAErF,UAAI,SAAS,UAAU,YAAY;AACjC,eAAO,SAAS;AAAA,MAClB,WAAW,SAAS,UAAU,YAAY;AACxC,cAAM,SAAS;AAAA,MACjB,OAAO;AACL,cAAM,WAAW,wBAAwB,YAAY,GAAG,iDAAiD;AAAA,MAC3G;AAAA,IACF,CAAC;AAED,oBAAgB,MAAM,OAAO,CAAC,aAAa,mBAAmB;AAC5D,UAAI,CAAC,OAAO,WAAW,GAAG;AACxB,cAAM,WAAW,wDAAwD;AAAA,MAC3E;AAEA,UAAI,OAAO,mBAAmB,YAAY;AACxC,cAAM,UAAU,KAAK,IAAI,WAAW;AAEpC,aAAK,SAAS,IAAI,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,MAE5D,OAAO;AAEL,yBAAiB,KAAK,QAAQ,YAAY,KAAK,KAAK;AAEpD,aAAK,SAAS,IAAI,YAAY,KAAK,cAAc;AAAA,MACnD;AAAA,IACF,CAAC;AAED,oBAAgB,MAAM,SAAS,iBAAe;AAC5C,WAAK,IAAI,aAAa,eAAe;AAAA,IACvC,CAAC;AAED,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW,oBAAI,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,QAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,WAAW,gBAAgB,KAAK,UAAU;AAEhD,eAAW,CAAC,GAAG,CAAC,KAAK,KAAK,UAAU;AAClC,iCAA2B,UAAU,GAAG,oBAAoB,CAAC,CAAC;AAAA,IAChE;AAEA,4BAAwB,KAAK,QAAQ,QAAQ;AAC7C,WAAO;AAAA,EACT;AAEF;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO,QAAM;AACX,UAAM,aAAa,eAAa;AAC9B,YAAM,YAAY,IAAI,yBAAyB,OAAO,SAAS;AAC/D,SAAG,SAAS;AACZ,aAAO,UAAU,sBAAsB;AAAA,IACzC,CAAC;AAAA,EACH;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB;AACF;AAEA,IAAI,yBAAyB,qBAAqB;AAElD,IAAI,yBAAsC,OAAO,OAAO;AAAA,EACtD,WAAW;AAAA,EACX,eAAe;AACjB,CAAC;AAaD,SAAS,UAAU,WAAW,SAAS;AACrC,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AAEA,IAAI,cAAc;AAKlB,IAAI,mBAAmB;AAEvB,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,oBAAoB;AAAA,EACpB,gBAAgB;AAClB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI,aAAAA;AAUJ,IAAM,WAAN,MAAe;AAAC;AAEhB,IAAM,WAAW,IAAI,SAAS;AAE9B,SAAS,eAAe,OAAO,IAAI,MAAM,gBAAgB;AACvD,MAAI,MAAM;AACV,MAAI;AACJ,iBAAe,MAAM;AACnB,UAAM,SAAS;AAEf,QAAI,OAAO,OAAO,YAAY;AAC5B,YAAM,WAAW,MAAM;AAAA,IACzB;AAKA,UAAM,oBAAoB,iBAAiB;AAAA,MAAE,GAAI,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB,CAAC;AAAA;AAAA;AAAA,MAGxH,KAAK,CAAC,MAAM,aAAa,iBAAiB,OAAO,MAAM,QAAQ;AAAA;AAAA,MAE/D,OAAO,UAAQ,iBAAiB,OAAO,MAAM,eAAe;AAAA;AAAA,MAE5D,SAAS,UAAQ,qBAAqB,OAAO,IAAI;AAAA,MACjD,cAAc,cAAY,eAAe,OAAO,QAAQ;AAAA,MACxD,mBAAmB,iBAAe,gBAAgB,KAAK,EAAE,WAAW;AAAA,IACtE,GAAG;AAAA,MACD,UAAU,MAAM;AACd,cAAM,WAAW,gBAAgB,KAAK;AACtC,0BAAkB,SAAS,OAAO;AAClC,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,UAAM,WAAW,GAAG,iBAAiB;AAErC,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,WAAW,MAAM;AAAA,IACzB;AAEA,UAAM,SAAS,GAAG,IAAI;AAAA,EACxB,CAAC;AACD,GAAC,EAAE,eAAe,YAAY,OAAwC,iBAAiB,OAAO,wCAAwC,IAAI,iBAAiB,KAAK,IAAI;AAEpK,MAAI,iBAAiB,GAAG,GAAG;AACzB,UAAM,IAAI,QAAQ,MAAM;AACtB,UAAI;AAEJ,OAAC,mBAAmB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAAA,IAC3G,CAAC;AAAA,EACH,OAAO;AACL,QAAI;AAEJ,KAAC,oBAAoB,qBAAqB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AAAA,EAC9G;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,IAAI,MAAM;AACnC,QAAM,WAAW,cAAc;AAC/B,SAAO;AAAA;AAAA,IACP,IAAI,SAAS;AACX,aAAO,eAAe,SAAS,SAAS,IAAI,IAAI;AAAA,IAClD;AAAA,IAAG,QAAQ,OAAO,CAAC,GAAG,MAAM,QAAQ,IAAI;AAAA;AAAA,EACxC;AACF;AAEA,IAAI,2BAA2B;AAAA,EAC7B;AAAA,EACA;AACF;AAEA,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,oBAAoB;AACtB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI,aAAAA;AAEJ,SAAS,mBAAmB,aAAa;AACvC,QAAM,WAAW,cAAc;AAC/B,SAAO,cAAc,MAAM;AACzB,UAAM,QAAQ,SAAS;AACvB,yBAAqB,OAAO,WAAW;AAAA,EACzC,GAAG,CAAC,aAAa,QAAQ,CAAC;AAC5B;AAEA,IAAI,4BAA4B;AAEhC,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,aAAa;AACf,IAAI;AAEJ,IAAM;AAAA,EACJ,SAAS;AACX,IAAI,aAAAA;AAEJ,SAAS,qBAAqB,IAAI,MAAM;AACtC,QAAM,WAAW,cAAc;AAC/B,SAAO;AAAA,IAAU,MAAM,IAAI,SAAS;AAClC,YAAM,eAAe,gBAAgB,SAAS,OAAO;AACrD,mBAAa,0BAAwB;AACnC,WAAG,oBAAoB,EAAE,GAAG,IAAI;AAAA,MAClC,CAAC;AAAA,IACH;AAAA,IAAG,QAAQ,OAAO,CAAC,GAAG,MAAM,QAAQ,IAAI;AAAA;AAAA,EACxC;AACF;AAEA,IAAI,8BAA8B;AAalC,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,OAAO;AACjB,oBAAgB,MAAM,SAAS,MAAM;AAErC,SAAK,QAAQ;AAAA,EACf;AAEF;AAEA,IAAI,iBAAiB;AAAA,EACnB;AACF;AAEA,IAAI,mBAAmB,eAAe;AAEtC,IAAI,mBAAgC,OAAO,OAAO;AAAA,EAChD,WAAW;AAAA,EACX,cAAc;AAChB,CAAC;AAED,IAAM;AAAA,EACJ,sBAAsB;AACxB,IAAI;AAIJ,IAAM,mBAAN,cAA+B,MAAM;AAAC;AAEtC,IAAM,YAAN,MAAgB;AAAA;AAAA,EAEd,YAAY,SAAS;AACnB,QAAI,gBAAgB,gBAAgB;AAEpC,oBAAgB,MAAM,SAAS,MAAM;AAErC,oBAAgB,MAAM,aAAa,MAAM;AAEzC,oBAAgB,MAAM,SAAS,MAAM;AAErC,oBAAgB,MAAM,UAAU,MAAM;AAEtC,oBAAgB,MAAM,UAAU,MAAM;AAEtC,oBAAgB,MAAM,iBAAiB,MAAM;AAE7C,SAAK,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACvE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,UAAU,iBAAiB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,QAAQ,mBAAmB,SAAS,iBAAiB,MAAM;AAAA,IAAC;AACjK,SAAK,UAAU,iBAAiB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,QAAQ,mBAAmB,SAAS,iBAAiB,MAAM;AAAA,IAAC;AACjK,SAAK,iBAAiB,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB,QAAQ,0BAA0B,SAAS,wBAAwB,SAAO;AAAA,EACtM;AAAA,EAEA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,cAAc,UAAU;AAC1B,QAAI;AAEJ,YAAQ,oBAAoB,KAAK,YAAY,cAAc,QAAQ,OAAO,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AAAA,EAC9I;AAAA,EAEA,YAAY,cAAc,UAAU;AAClC,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO;AAAA,IACT;AAIA,QAAI,OAAO,KAAK;AAEhB,WAAO,MAAM;AACX,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,IAAI;AAE7E,UAAI,KAAK,SAAS,QAAQ;AACxB,aAAK,OAAO,IAAI;AAEhB,eAAO;AAAA,MACT;AAEA,YAAM,YAAY,KAAK,cAAc,aAAa,KAAK,OAAO,CAAC;AAE/D,aAAO,KAAK,SAAS,IAAI,SAAS;AAAA,IACpC;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,OAAO,OAAO,UAAU;AAC1B,UAAM,UAAU,MAAM;AACpB,UAAI,QAAQ,QAAQ,cAAc;AAIlC,UAAI;AACJ,UAAI;AAEJ,iBAAW,CAAC,SAAS,SAAS,KAAK,OAAO;AACxC,YAAI,OAAO,uBAAuB;AAIlC,cAAM,OAAO,KAAK;AAElB,aAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,QAAQ;AACtE,gBAAM,KAAK,kBAAkB;AAAA,QAC/B;AAGA,cAAM,SAAS;AAGf,eAAO,SAAS,OAAO,SAAS,IAAI,SAAS,IAAI;AAGjD,gBAAQ,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ;AAAA,UAC3D,MAAM;AAAA,UACN;AAAA,UACA;AAAA,UACA,UAAU,oBAAI,IAAI;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,KAAK,SAAS,YAAY,KAAK,YAAY,SAAS;AACtD,gBAAM,KAAK,kBAAkB;AAAA,QAC/B;AAGA,mBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,IAAI,WAAW,IAAI;AACnF,qBAAa,QAAQ,aAAa,SAAS,UAAU,wBAAwB,SAAS,iBAAiB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,KAAK,UAAU,IAAI;AAEpM,oBAAY,KAAK,cAAc,SAAS;AACxC,aAAK,SAAS,cAAc,KAAK,WAAW,QAAQ,gBAAgB,SAAS,cAAc;AAAA,MAC7F;AAIA,YAAM,UAAU,QAAQ,SAAS,UAAU,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,IAAI,SAAS,IAAI,KAAK;AAEtH,UAAI,WAAW,SAAS,QAAQ,SAAS,UAAU,QAAQ,cAAc,YAAY;AACnF,cAAM,KAAK,kBAAkB;AAAA,MAC/B;AAGA,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,MACF;AAEA,OAAC,SAAS,UAAU,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,IAAI,WAAW,QAAQ;AAChG,WAAK,SAAS,eAAe,KAAK,WAAW,QAAQ,iBAAiB,SAAS,eAAe;AAC9F,WAAK;AAEL,WAAK,OAAO,QAAQ;AAEpB,mBAAa,QAAQ,aAAa,SAAS,UAAU,yBAAyB,SAAS,iBAAiB,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,KAAK,UAAU,QAAQ;AAAA,IAC7M;AAEA,QAAI;AACF,cAAQ;AAAA,IACV,SAAS,OAAO;AAGd,UAAI,iBAAiB,kBAAkB;AACrC,aAAK,MAAM;AACX,gBAAQ;AAAA,MACV,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,OAAO,MAAM;AACX,UAAM,OAAO,KAAK,KAAK;AAEvB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM;AACjB,WAAK,QAAQ;AACb,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,KAAK;AAChB,QAAI,YAAY,KAAK;AAErB,WAAO,MAAM;AACX,UAAI;AAEJ,WAAK,SAAS,OAAO,SAAS;AAE9B,UAAI,SAAS,MAAM;AACjB,YAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,eAAK,QAAQ;AACb,eAAK,YAAY;AAAA,QACnB,OAAO;AACL,eAAK;AAAA,QACP;AAEA,eAAO;AAAA,MACT;AAIA,UAAI,KAAK,SAAS,OAAO,GAAG;AAC1B;AAAA,MACF;AAGA,mBAAa,SAAS,UAAU,QAAQ,WAAW,SAAS,SAAS,OAAO;AAC5E,aAAO,KAAK;AAAA,IACd;AAGA,WAAO,SAAS,MAAM,OAAO,KAAK,QAAQ;AACxC,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAK;AACL,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AACN,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,oBAAoB;AAClB,UAAM,6BAA6B,uBAAuB,IAAI,0IAAoJ;AAClN,gCAA4B,8BAA8B,KAAK,SAAS,OAAO,MAAM,KAAK,KAAK,KAAK,GAAG;AACvG,UAAM,IAAI,iBAAiB;AAAA,EAC7B;AAEF;AAEA,IAAI,mBAAmB;AAAA,EACrB;AACF;AAEA,IAAI,qBAAqB,iBAAiB;AAE1C,IAAI,qBAAkC,OAAO,OAAO;AAAA,EAClD,WAAW;AAAA,EACX,WAAW;AACb,CAAC;AAED,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,SAAS;AACnB,QAAI;AAEJ,oBAAgB,MAAM,YAAY,MAAM;AAExC,oBAAgB,MAAM,SAAS,MAAM;AAErC,oBAAgB,MAAM,SAAS,MAAM;AAErC,oBAAgB,MAAM,SAAS,MAAM;AAErC,oBAAgB,MAAM,QAAQ,MAAM;AAEpC,oBAAgB,MAAM,cAAc,MAAM;AAE1C,SAAK,WAAW,QAAQ;AACxB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO,oBAAI,IAAI;AACpB,SAAK,cAAc,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,OAAK;AAAA,EACvH;AAAA,EAEA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK,KAAK,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,EAC3C;AAAA,EAEA,IAAI,KAAK;AACP,UAAM,YAAY,KAAK,WAAW,GAAG;AAErC,UAAM,OAAO,KAAK,KAAK,IAAI,SAAS;AAEpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,SAAK,IAAI,KAAK,KAAK,KAAK;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,KAAK,KAAK;AACZ,UAAM,YAAY,KAAK,WAAW,GAAG;AAErC,UAAM,eAAe,KAAK,KAAK,IAAI,SAAS;AAE5C,QAAI,cAAc;AAChB,WAAK,OAAO,GAAG;AAAA,IACjB;AAEA,UAAM,OAAO,KAAK,KAAK;AACvB,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAEA,QAAI,MAAM;AACR,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAEA,SAAK,KAAK,IAAI,WAAW,IAAI;AAE7B,SAAK,QAAQ;AACb,SAAK;AAEL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,GAAG;AAChC,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,YAAY;AACV,UAAM,OAAO,KAAK,KAAK;AAEvB,QAAI,MAAM;AACR,WAAK,OAAO,KAAK,GAAG;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,OAAO,KAAK;AACV,UAAM,YAAY,KAAK,WAAW,GAAG;AAErC,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5C;AAAA,IACF;AAEA,UAAM,OAAO,kBAAkB,KAAK,KAAK,IAAI,SAAS,CAAC;AACvD,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,KAAK;AAElB,QAAI,OAAO;AACT,YAAM,OAAO,KAAK;AAAA,IACpB;AAEA,QAAI,MAAM;AACR,WAAK,QAAQ,KAAK;AAAA,IACpB;AAEA,QAAI,SAAS,KAAK,KAAK,GAAG;AACxB,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,SAAS,KAAK,KAAK,GAAG;AACxB,WAAK,QAAQ;AAAA,IACf;AAEA,SAAK,KAAK,OAAO,SAAS;AAE1B,SAAK;AAAA,EACP;AAAA,EAEA,QAAQ;AACN,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO,oBAAI,IAAI;AAAA,EACtB;AAEF;AAEA,IAAI,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAI,oBAAoB,gBAAgB;AAExC,IAAI,oBAAiC,OAAO,OAAO;AAAA,EACjD,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AAED,IAAM;AAAA,EACJ,UAAU;AACZ,IAAI;AAEJ,IAAM;AAAA,EACJ,WAAW;AACb,IAAI;AAEJ,SAAS,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA,eAAe,OAAK;AACtB,GAAG;AACD,QAAM,WAAW,IAAI,WAAW;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,IAAI,YAAY;AAAA,IAC5B;AAAA,IACA;AAAA,IACA,OAAO,UAAQ;AACb,eAAS,IAAI,MAAM,IAAI;AAAA,IACzB;AAAA,IACA,OAAO,UAAQ;AACb,YAAM,UAAU,SAAS,KAAK;AAC9B,eAAS,IAAI,MAAM,IAAI;AAEvB,UAAI,WAAW,MAAM,KAAK,IAAI,SAAS;AAErC,cAAM,OAAO,QAAQ,GAAG;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAI,sBAAsB;AAE1B,IAAM,4BAA4B;AAElC,SAAS,UAAU,GAAG,KAAK,KAAK;AAG9B,MAAI,OAAO,MAAM,YAAY,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,SAAS,IAAI,GAAG;AAClE,WAAO,IAAI,CAAC;AAAA,EACd;AAGA,UAAQ,OAAO,GAAG;AAAA,IAChB,KAAK;AACH,aAAO;AAAA;AAAA,IAGT,KAAK;AACH,aAAO,IAAI,SAAS;AAAA,IAEtB,KAAK;AAAA,IACL,KAAK;AAEH,aAAO,OAAO,CAAC;AAAA,IAEjB,KAAK;AAEH,aAAO,KAAK,UAAU,CAAC;AAAA,IAEzB,KAAK;AACH,WAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,MAAM;AAC3E,cAAM,WAAW,qDAAqD;AAAA,MACxE;AAEA,aAAO,cAAc,EAAE,IAAI;AAAA,EAC/B;AAEA,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,UAAU;AACzB,QAAI;AAEJ,YAAQ,kBAAkB,KAAK,UAAU,CAAC,OAAO,QAAQ,oBAAoB,SAAS,kBAAkB;AAAA,EAC1G;AAGA,MAAI,iBAAiB,CAAC,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,QAAQ,CAAC,GAAG;AAEpB,WAAO,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,UAAU,GAAG,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AAAA,EAC7D;AAMA,MAAI,OAAO,EAAE,WAAW,YAAY;AAElC,WAAO,UAAU,EAAE,OAAO,GAAG,GAAG,KAAK,GAAG;AAAA,EAC1C;AAIA,MAAI,aAAa,KAAK;AACpB,UAAM,MAAM,CAAC;AAEb,eAAW,CAAC,GAAG,CAAC,KAAK,GAAG;AAEtB,UAAI,OAAO,MAAM,WAAW,IAAI,UAAU,GAAG,GAAG,CAAC,IAAI;AAAA,IACvD;AAEA,WAAO,UAAU,KAAK,KAAK,GAAG;AAAA,EAChC;AAIA,MAAI,aAAa,KAAK;AACpB,WAAO;AAAA;AAAA,MACP,MAAM,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,GAAG,GAAG,EAAE,cAAc,UAAU,GAAG,GAAG,CAAC,CAAC;AAAA,MAAG;AAAA,MAAK;AAAA,IAAG;AAAA,EAC5F;AAGA,MAAI,WAAW,UAAa,EAAE,OAAO,QAAQ,KAAK,QAAQ,OAAO,EAAE,OAAO,QAAQ,MAAM,YAAY;AAElG,WAAO,UAAU,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;AAAA,EAC1C;AAGA,SAAO,IAAI,OAAO,KAAK,CAAC,EAAE,OAAO,OAAK,EAAE,CAAC,MAAM,MAAS,EAAE,KAAK,EAC9D,IAAI,OAAK,GAAG,UAAU,GAAG,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC;AACxE;AAYA,SAAS,gBAAgB,GAAG,MAAM;AAAA,EAChC,gBAAgB;AAClB,GAAG;AACD,MAAI,MAAuC;AACzC,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,YAAY,OAAO,cAAc,OAAO,YAAY,IAAI,IAAI;AAClE,YAAM,MAAM,UAAU,GAAG,GAAG;AAC5B,YAAM,UAAU,OAAO,cAAc,OAAO,YAAY,IAAI,IAAI;AAEhE,UAAI,UAAU,YAAY,2BAA2B;AAEnD,gBAAQ,eAAe,iBAAiB,UAAU,SAAS,0BAA0B;AACrF,gBAAQ,KAAK,GAAG,GAAG;AACnB,gBAAQ,SAAS;AAAA,MAEnB;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,UAAU,GAAG,GAAG;AACzB;AAEA,IAAI,yBAAyB;AAE7B,IAAM;AAAA,EACJ,WAAW;AACb,IAAI;AAUJ,IAAM,gBAAgB;AAAA,EACpB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AACX;AAEA,SAAS,oBAAoB;AAAA,EAC3B,WAAW,cAAc;AAAA,EACzB,WAAW,cAAc;AAAA,EACzB,UAAU,cAAc;AAC1B,IAAI,eAAe,MAAM;AACvB,QAAM,cAAc,eAAe,QAAQ;AAC3C,SAAO,aAAa,UAAU,SAAS,aAAa,IAAI;AAC1D;AAEA,SAAS,eAAe,UAAU;AAChC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAO;AAAA,IAEhB,KAAK;AACH,aAAO,SAAO,uBAAuB,GAAG;AAAA,EAC5C;AAEA,QAAM,WAAW,gCAAgC,QAAQ,EAAE;AAC7D;AAEA,SAAS,aAAa,UAAU,SAAS,cAAc,MAAM;AAC3D,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,IAAI,YAAY;AAAA,QACrB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,oBAAoB;AAAA,QACzB;AAAA,QACA,SAAS,kBAAkB,OAAO;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,oBAAoB;AAAA,QACzB;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,gCAAgC,QAAQ,EAAE;AAC7D;AAEA,IAAI,6BAA6B;AAajC,SAAS,OAAO,QAAQ;AACtB,MAAI,gBAAgB;AAEpB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAEA,QAAM,MAAM,UAAU,QAAQ,iBAAiB,OAAO,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB,SAAS;AACvI,QAAM,eAAe,mBAAmB,IAAI,iBAAiB,QAAQ,qBAAqB,SAAS,mBAAmB;AACtH,SAAO,CAAC,EAAE,UAAU,SAAS,OAAO,YAAY,SAAS,aAAa,kBAAkB,YAAY,OAAO,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa,YAAY,OAAO,OAAO,aAAa;AAC7M;AAEA,IAAI,gBAAgB;AAEpB,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,UAAU;AACZ,IAAI;AAMJ,SAAS,kBAAkB,OAAO;AAEhC,MAAI,UAAU,QAAQ,OAAO,UAAU,UAAU;AAC/C,WAAO;AAAA,EACT;AAGA,UAAQ,OAAO,MAAM,UAAU;AAAA,IAC7B,KAAK;AACH,aAAO;AAAA,IAET,KAAK;AACH,aAAO;AAAA,EACX;AAGA,MAAI,MAAM,4BAA4B,KAAK,QAAQ,MAAM,yBAAyB,KAAK,QAAQ,MAAM,2BAA2B,KAAK,QAAQ,MAAM,2BAA2B,KAAK,QAAQ,MAAM,0BAA0B,KAAK,MAAM;AACpO,WAAO;AAAA,EACT;AAGA,MAAI,cAAc,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,KAAK,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,OAAO,KAAK,GAAG;AAC7B,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,mBAAmB,WAAW,KAAK,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,OAAO,UAAU,YAAY,kBAAkB,KAAK,GAAG;AACzD;AAAA,EACF;AAEA,SAAO,OAAO,KAAK;AAEnB,aAAW,OAAO,OAAO;AAEvB,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACpD,YAAM,OAAO,MAAM,GAAG;AAEtB,UAAI,OAAO,SAAS,YAAY,QAAQ,QAAQ,CAAC,OAAO,SAAS,IAAI,GAAG;AACtE,wBAAgB,IAAI;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK,KAAK;AACnB;AAEA,IAAI,yBAAyB;AAc7B,SAAS,eAAe,KAAK;AAC3B,SAAO,MAAM;AACf;AAEA,IAAI,4BAA4B;AAAA,EAC9B;AACF;AAEA,IAAM;AAAA,EACJ,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAIJ,IAAM;AAAA,EACJ,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,iBAAiB;AACnB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,SAAS;AAAA,EACT,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,yBAAyB;AAC3B,IAAI;AAEJ,IAAM;AAAA,EACJ,6BAA6B;AAC/B,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAClB,IAAI;AAoBJ,IAAM;AAAA,EACJ,gBAAgB;AAClB,IAAI;AAIJ,IAAM,WAAN,MAAe;AAAC;AAEhB,IAAM,WAAW,IAAI,SAAS;AAU9B,IAAM,kBAAkB,CAAC;AAEzB,IAAM,gBAAgB,oBAAI,IAAI;AAE9B,IAAM,oBAAqB,uBAAM;AAC/B,MAAI,cAAc;AAClB,SAAO,MAAM;AACf,GAAG;AAIH,SAAS,SAAS,SAAS;AACzB,MAAI,cAAc;AAClB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,sBAAsB;AAAA,EACxB,IAAI;AACJ,QAAM,MAAM,QAAQ,OAAO,OAAO,QAAQ,MAAM;AAEhD,MAAI,MAAuC;AACzC,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,WAAW,oFAAoF;AAAA,IACvG;AAEA,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,WAAW,yEAAyE;AAAA,IAC5F;AAAA,EACF;AAGA,QAAM,+BAA+B,oBAAI,IAAI;AAC7C,QAAM,QAAQ,2BAA2B,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,IACtG,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,GAAG;AACN,QAAM,aAAa,8BAA8B,QAAQ,mBAAmB;AAC5E,QAAM,mBAAmB,oBAAI,IAAI;AACjC,MAAI,kBAAkB;AAEtB,WAAS,iBAAiB;AACxB,WAAO,CAAC,WAAW,+BAA+B,KAAK,kBAAkB;AAAA,EAC3E;AAEA,WAAS,aAAa,OAAO;AAC3B,UAAM,SAAS,EAAE,eAAe,IAAI,GAAG;AACvC;AACA,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AAEA,WAAS,sCAAsC;AAC7C,WAAO,2BAA2B,GAAG,MAAM,UAAa,CAAC,eAAe;AAAA,EAC1E;AAEA,WAAS,aAAa,OAAO,OAAO,aAAa,UAAU,WAAW;AACpE,aAAS,OAAO,UAAU,SAAS;AACnC,gCAA4B,OAAO,WAAW;AAAA,EAChD;AAEA,WAAS,4BAA4B,OAAO,aAAa;AACvD,QAAI,kBAAkB,OAAO,WAAW,GAAG;AACzC,yBAAmB,KAAK;AAAA,IAC1B;AAEA,wBAAoB,aAAa,IAAI;AAAA,EACvC;AAaA,WAAS,0BAA0B,OAAO,aAAa;AACrD,QAAI,kBAAkB,OAAO,WAAW,GAAG;AACzC,YAAM,gBAAgB,kBAAkB,iBAAiB,KAAK,CAAC;AAC/D,oBAAc,cAAc,MAAM;AAClC,0BAAoB,aAAa,KAAK;AAAA,IACxC;AAAA,EACF;AAEA,WAAS,oBAAoB,aAAa,eAAe;AACvD,UAAM,SAAS,cAAc,IAAI,WAAW;AAE5C,QAAI,UAAU,MAAM;AAClB,iBAAW,gBAAgB,QAAQ;AACjC,kCAA0B,cAAc,kBAAkB,WAAW,CAAC;AAAA,MACxE;AAEA,UAAI,eAAe;AACjB,sBAAc,OAAO,WAAW;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,iCAAiC,OAAO,aAAa;AAC5D,QAAI,SAAS,cAAc,IAAI,WAAW;AAE1C,QAAI,UAAU,MAAM;AAClB,oBAAc,IAAI,aAAa,SAAS,oBAAI,IAAI,CAAC;AAAA,IACnD;AAEA,WAAO,IAAI,KAAK;AAAA,EAClB;AAgCA,WAAS,kBAAkB,OAAO,SAAS,OAAO,WAAW,aAAa,kBAAkB;AAC1F,WAAO,QAAQ,KAAK,WAAS;AAC3B,UAAI,CAAC,eAAe,GAAG;AAErB,2BAAmB,KAAK;AACxB,cAAM;AAAA,MACR;AAEA,YAAM,WAAW,oBAAoB,KAAK;AAC1C,mBAAa,OAAO,OAAO,aAAa,UAAU,SAAS;AAC3D,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,oBAAkB;AACzB,UAAI,CAAC,eAAe,GAAG;AAErB,2BAAmB,KAAK;AACxB,cAAM;AAAA,MACR;AAEA,UAAI,iBAAiB,cAAc,GAAG;AACpC,eAAO,6BAA6B,OAAO,gBAAgB,OAAO,WAAW,aAAa,gBAAgB;AAAA,MAC5G;AAEA,YAAM,WAAW,oBAAoB,cAAc;AACnD,mBAAa,OAAO,OAAO,aAAa,UAAU,SAAS;AAC3D,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAiCA,WAAS,6BAA6B,OAAO,SAAS,OAAO,cAAc,aAAa,kBAAkB;AACxG,WAAO,QAAQ,KAAK,iBAAe;AACjC,UAAI,CAAC,eAAe,GAAG;AAErB,2BAAmB,KAAK;AACxB,cAAM;AAAA,MACR;AAQA,UAAI,iBAAiB,iBAAiB,QAAQ,iBAAiB,sBAAsB,SAAS;AAQ5F,cAAM,WAAW,IAAI,iBAAiB,eAAe,oBAAoB,WAAW,CAAC;AAAA,MACvF,OAAO;AAwBL,cAAM,SAAS,EAAE,eAAe,QAAQ,aAAW;AACjD,gBAAM,WAAW,OAAO,OAAO;AAAA,QACjC,CAAC;AAAA,MACH;AAoBA,YAAM,iBAAiB,kCAAkC,OAAO,KAAK;AAErE,UAAI,kBAAkB,eAAe,UAAU,WAAW;AAmBxD,YAAI,kBAAkB,OAAO,WAAW,KAAK,iBAAiB,KAAK,KAAK,MAAM;AAC5E,sCAA4B,OAAO,WAAW;AAAA,QAChD;AAEA,YAAI,eAAe,UAAU,YAAY;AACvC,iBAAO,eAAe;AAAA,QACxB,OAAO;AACL,gBAAM,eAAe;AAAA,QACvB;AAAA,MACF;AA2BA,UAAI,CAAC,kBAAkB,OAAO,WAAW,GAAG;AAC1C,cAAM,gBAAgB,2BAA2B,OAAO,KAAK;AAE7D,YAAI,iBAAiB,MAAM;AAKzB,iBAAO,cAAc,gBAAgB;AAAA,QACvC;AAAA,MACF;AAGA,YAAM,CAAC,UAAU,SAAS,IAAI,uBAAuB,OAAO,OAAO,WAAW;AAE9E,UAAI,SAAS,UAAU,WAAW;AAChC,qBAAa,OAAO,OAAO,aAAa,UAAU,SAAS;AAAA,MAC7D;AAEA,UAAI,SAAS,UAAU,YAAY;AACjC,cAAM,SAAS;AAAA,MACjB;AAEA,aAAO,SAAS;AAAA,IAClB,CAAC,EAAE,MAAM,WAAS;AAEhB,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AAEA,UAAI,CAAC,eAAe,GAAG;AACrB,2BAAmB,KAAK;AACxB,cAAM;AAAA,MACR;AAEA,YAAM,WAAW,oBAAoB,KAAK;AAC1C,mBAAa,OAAO,OAAO,aAAa,UAAU,YAAY;AAC9D,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAEA,WAAS,WAAW,OAAO,OAAO,MAAM,aAAa;AACnD,QAAI,iBAAiB,uBAAuB,kBAAkB;AAE9D,QAAI,kBAAkB,OAAO,WAAW,KAAK,MAAM,cAAc,kBAAkB,MAAM,SAAS,OAAO,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,iBAAiB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,YAAY,MAAM,cAAc,mBAAmB,MAAM,SAAS,OAAO,QAAQ,qBAAqB,SAAS,UAAU,wBAAwB,iBAAiB,cAAc,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,UAAU;AAC7hB,UAAI,uBAAuB,kBAAkB;AAE7C,wBAAkB,KAAK,MAAM,QAAQ,yBAAyB,mBAAmB,MAAM,SAAS,OAAO,QAAQ,qBAAqB,SAAS,UAAU,wBAAwB,iBAAiB,cAAc,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,aAAa,QAAQ,0BAA0B,SAAS,wBAAwB,MAAM,SAAS,EAAE,YAAY,OAAO;AAAA,IAC9Y;AAEA,eAAW,WAAW,MAAM;AAC1B,mCAA6B,IAAI,OAAO;AAAA,IAC1C;AAAA,EACF;AAEA,WAAS,uBAAuB,OAAO,OAAO,aAAa;AACzD,UAAM,eAAe,iBAAiB,GAAG;AAEzC,QAAI,6BAA6B;AACjC,QAAI,8BAA8B;AAElC,UAAM,mBAAmB,MAAM;AAC7B,mBAAa;AACb,oCAA8B;AAAA,IAChC;AAEA,QAAI;AACJ,QAAI,gBAAgB;AACpB,QAAI;AACJ,UAAM,mBAAmB;AAAA,MACvB,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAYA,UAAM,YAAY,oBAAI,IAAI;AAE1B,aAAS,eAAe;AAAA,MACtB,KAAK;AAAA,IACP,GAAG;AACD,YAAM,cAAc,kBAAkB,OAAO,OAAO,MAAM;AAC1D,gBAAU,IAAI,QAAQ,WAAW;AAIjC,UAAI,CAAC,4BAA4B;AAC/B,mBAAW,OAAO,OAAO,IAAI,IAAI,UAAU,KAAK,CAAC,GAAG,WAAW;AAC/D,kCAA0B,OAAO,WAAW;AAAA,MAC9C;AAEA,cAAQ,YAAY,OAAO;AAAA,QACzB,KAAK;AACH,iBAAO,YAAY;AAAA,QAErB,KAAK;AACH,gBAAM,YAAY;AAAA,QAEpB,KAAK;AACH,2BAAiB,gBAAgB;AACjC,2BAAiB,oBAAoB,YAAY;AACjD,gBAAM,YAAY;AAAA,MACtB;AAEA,YAAM,WAAW,wBAAwB;AAAA,IAC3C;AAEA,UAAM,cAAc,QAAM;AACxB,aAAO,IAAI,SAAS;AAClB,YAAI,6BAA6B;AAC/B,gBAAM,WAAW,4NAA4N;AAAA,QAC/O;AAEA,UAAE,eAAe,QAAQ,OAAwC,iBAAiB,OAAO,gCAAgC,IAAI,iBAAiB,KAAK,IAAI;AACvJ,eAAO;AAAA,UAAiB;AAAA,UAAO;AAAA,UAAI;AAAA,UAAM;AAAA,YACvC,MAAM;AAAA,UACR;AAAA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI;AACF,eAAS,IAAI;AAAA,QACX,KAAK;AAAA,QACL;AAAA,MACF,CAAC;AACD,eAAS,gBAAgB,MAAM,IAAI,eAAe,MAAM,IAAI;AAE5D,UAAI,aAAa,MAAM,GAAG;AACxB,YAAI,OAAO,UAAU,YAAY;AAC/B,0BAAgB;AAAA,QAClB;AAEA,iBAAS,OAAO;AAAA,MAClB;AAEA,UAAI,iBAAiB,MAAM,GAAG;AAC5B,iBAAS,kBAAkB,OAAO,QAAQ,OAAO,WAAW,aAAa,gBAAgB,EAAE,QAAQ,gBAAgB;AAAA,MACrH,OAAO;AACL,yBAAiB;AAAA,MACnB;AAEA,eAAS,kBAAkB,iBAAiB,OAAO,QAAQ;AAAA,IAC7D,SAAS,mBAAmB;AAC1B,eAAS;AAET,UAAI,iBAAiB,MAAM,GAAG;AAC5B,iBAAS,6BAA6B,OAAO,QAAQ,OAAO,WAAW,aAAa,gBAAgB,EAAE,QAAQ,gBAAgB;AAAA,MAChI,OAAO;AACL,wBAAgB;AAChB,yBAAiB;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,iBAAW,oBAAoB,MAAM;AAAA,IACvC,WAAW,iBAAiB,MAAM,GAAG;AACnC,iBAAW,sBAAsB,MAAM;AAAA,IACzC,OAAO;AACL,iBAAW,oBAAoB,MAAM;AAAA,IACvC;AAEA,iCAA6B;AAC7B,iCAA6B,OAAO,aAAa,SAAS;AAC1D,eAAW,OAAO,OAAO,IAAI,IAAI,UAAU,KAAK,CAAC,GAAG,WAAW;AAC/D,WAAO,CAAC,UAAU,SAAS;AAAA,EAC7B;AAEA,WAAS,kCAAkC,OAAO,OAAO;AAGvD,QAAI,iBAAiB,MAAM,WAAW,IAAI,GAAG;AAE7C,QAAI,kBAAkB,MAAM;AAC1B,aAAO;AAAA,IACT;AAGA,UAAM,uBAAuB,oBAAI,IAAI;AAErC,QAAI;AACF,uBAAiB,MAAM,IAAI,aAAW;AACpC,UAAE,OAAO,YAAY,YAAY,OAAwC,iBAAiB,OAAO,8BAA8B,IAAI,iBAAiB,KAAK,IAAI;AAC7J,eAAO,kBAAkB,OAAO,OAAO,OAAO,EAAE;AAAA,MAClD,GAAG;AAAA,QACD,aAAa,UAAQ;AACnB,cAAI,KAAK,SAAS,YAAY,KAAK,YAAY,KAAK;AAClD,iCAAqB,IAAI,KAAK,OAAO;AAAA,UACvC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAO;AACd,YAAM,WAAW,2CAA2C,GAAG,MAAM,MAAM,OAAO,EAAE;AAAA,IACtF;AAEA,QAAI,gBAAgB;AAClB,UAAI;AAIJ,YAAM,WAAW,IAAI,KAAK,cAAc;AAUxC,iBAAW,OAAO,OAAO,uBAAuB,oBAAoB,iBAAiB,KAAK,OAAO,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,WAAW;AAAA,IAChL;AAEA,WAAO;AAAA,EACT;AAqBA,WAAS,iCAAiC,OAAO,OAAO;AAEtD,UAAM,YAAY,kCAAkC,OAAO,KAAK;AAEhE,QAAI,aAAa,MAAM;AACrB,yBAAmB,KAAK;AACxB,aAAO;AAAA,IACT;AAGA,UAAM,0BAA0B,2BAA2B,OAAO,KAAK;AAEvE,QAAI,2BAA2B,MAAM;AACnC,UAAI;AAEJ,YAAM,wBAAwB,wBAAwB,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,WAAW,WAAW;AACzK,yCAAiC,OAAO,wBAAwB,WAAW;AAAA,MAC7E;AAGA,aAAO,wBAAwB;AAAA,IACjC;AAGA,UAAM,iBAAiB,kBAAkB;AACzC,UAAM,CAAC,UAAU,YAAY,IAAI,uBAAuB,OAAO,OAAO,cAAc;AAapF,QAAI,SAAS,UAAU,WAAW;AAChC,uBAAiB,OAAO,gBAAgB,UAAU,cAAc,KAAK;AACrE,uCAAiC,OAAO,cAAc;AAAA,IACxD,OAAO;AACL,yBAAmB,KAAK;AACxB,eAAS,OAAO,UAAU,YAAY;AAAA,IACxC;AAEA,WAAO;AAAA,EACT;AAOA,WAAS,2BAA2B,OAAO,OAAO;AAEhD,UAAM,oBAAoB,uBAAuB,CAAC,iBAAiB,IAAI,KAAK,IAAI,CAAC,kBAAkB,iBAAiB,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,mBAAmB,sBAAsB,kBAAkB,CAAC,CAAC,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAErP,aAAS,cAAc,eAAe;AACpC,iBAAW,CAAC,QAAQ,YAAY,KAAK,eAAe;AAClD,YAAI,CAAC,kBAAkB,OAAO,OAAO,MAAM,EAAE,GAAG,YAAY,GAAG;AAC7D,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,eAAW,YAAY,mBAAmB;AACxC;AAAA;AAAA;AAAA,QAEA,SAAS,cAAc,IAAI,MAAM,OAAO;AAAA,QACxC,CAAC,cAAc,SAAS,uCAAuC;AAAA,QAAG;AAChE,iBAAS,cAAc,IAAI,MAAM,SAAS,IAAI;AAC9C,eAAO;AAAA,MACT,OAAO;AACL,iBAAS,cAAc,IAAI,MAAM,SAAS,KAAK;AAAA,MACjD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB,OAAO;AAC/B,WAAO,iBAAiB,IAAI,KAAK;AAAA,EACnC;AAUA,WAAS,iBAAiB,OAAO,gBAAgB,UAAU,WAAW,OAAO;AAC3E,qBAAiB,IAAI,OAAO;AAAA,MAC1B,yCAAyC;AAAA,MACzC,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,eAAe,oBAAI,IAAI,CAAC,CAAC,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AAEA,WAAS,6BAA6B,OAAO,aAAa,WAAW;AAGnE,QAAI,kBAAkB,OAAO,WAAW,GAAG;AACzC,YAAM,gBAAgB,iBAAiB,KAAK;AAE5C,UAAI,iBAAiB,MAAM;AACzB,sBAAc,0CAA0C;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAEA,WAAS,mBAAmB,OAAO;AACjC,qBAAiB,OAAO,KAAK;AAAA,EAC/B;AAEA,WAAS,kBAAkB,OAAO,aAAa;AAC7C,QAAI;AAEJ,WAAO,kBAAkB,qBAAqB,iBAAiB,KAAK,OAAO,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB;AAAA,EACjJ;AAcA,WAAS,oBAAoB,WAAW;AACtC,WAAO,MAAM,KAAK,UAAU,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,QAAQ,WAAW,MAAM,CAAC,QAAQ,YAAY,QAAQ,CAAC;AAAA,EACtG;AAEA,WAAS,SAAS,OAAO,UAAU,WAAW;AAC5C,QAAI,MAAuC;AACzC,UAAI,SAAS,UAAU,aAAa,QAAQ,QAAQ,0BAA0B,MAAM,OAAO;AACzF,+BAAuB,SAAS,QAAQ;AAAA,MAC1C;AAAA,IACF;AAEA,UAAM,WAAW,IAAI,KAAK,QAAQ;AAElC,QAAI;AACF,YAAM,IAAI,oBAAoB,SAAS,GAAG,QAAQ;AAAA,IACpD,SAAS,OAAO;AACd,YAAM,WAAW,4CAA4C,GAAG,MAAM,MAAM,OAAO,EAAE;AAAA,IACvF;AAAA,EACF;AAEA,WAAS,2BAA2B,IAAI;AACtC,QAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,YAAM,UAAU,8CAA8C,gBAAgB,MAAM,gBAAgB,QAAQ,GAAG,CAAC,EAAE,KAAK,KAAU,CAAC;AAClI,aAAO,oBAAoB,WAAW,OAAO,CAAC;AAAA,IAChD;AAEA,oBAAgB,KAAK,GAAG;AAExB,QAAI;AACF,aAAO,GAAG;AAAA,IACZ,UAAE;AACA,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF;AAEA,WAAS,aAAa,OAAO,OAAO;AAClC,UAAM,iBAAiB,MAAM,WAAW,IAAI,GAAG;AAE/C,QAAI,kBAAkB,MAAM;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI,aAAW;AAC1B,UAAI;AAEJ,QAAE,OAAO,YAAY,YAAY,OAAwC,iBAAiB,OAAO,8BAA8B,IAAI,iBAAiB,KAAK,IAAI;AAC7J,cAAQ,oBAAoB,mBAAmB,OAAO,OAAO,OAAO,OAAO,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AAAA,IAC/I,CAAC;AAAA,EACH;AAEA,WAAS,YAAY,OAAO,OAAO;AACjC,WAAO,2BAA2B,MAAM,iCAAiC,OAAO,KAAK,CAAC;AAAA,EACxF;AAEA,WAAS,mBAAmB,OAAO;AACjC,UAAM,WAAW,OAAO,GAAG;AAAA,EAC7B;AAEA,WAAS,mBAAmB,OAAO,WAAW;AAC5C,MAAE,eAAe,QAAQ,OAAwC,iBAAiB,OAAO,gCAAgC,IAAI,iBAAiB,KAAK,IAAI;AAEvJ,eAAW,WAAW,8BAA8B;AAClD,UAAI;AAEJ,YAAM,OAAO,UAAU,OAAO;AAC9B,OAAC,mBAAmB,KAAK,gBAAgB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,MAAM,OAAO,SAAS;AAAA,IACtI;AAEA,iCAA6B,MAAM;AACnC,uBAAmB,SAAS;AAC5B,UAAM,MAAM;AACZ,8BAA0B,OAAO,WAAW;AAAA,EAC9C;AAEA,MAAI,OAAO,MAAM;AAKf,UAAM,cAAc,CAAC,OAAO,OAAO,aAAa;AAC9C,UAAI,0BAA0B;AAC9B,YAAM,SAAS,oBAAI,IAAI;AAEvB,eAAS,eAAe;AAAA,QACtB,KAAK;AAAA,MACP,GAAG;AACD,YAAI,yBAAyB;AAC3B,gBAAM,WAAW,0DAA0D;AAAA,QAC7E;AAEA,cAAM,WAAW,kBAAkB,OAAO,OAAO,MAAM;AAEvD,YAAI,SAAS,UAAU,YAAY;AACjC,iBAAO,SAAS;AAAA,QAClB,WAAW,SAAS,UAAU,WAAW;AACvC,gBAAM,MAAM,mDAAmD,MAAM,gDAAgD,GAAG;AACxH,sCAA4B,GAAG;AAC/B,gBAAM,WAAW,GAAG;AAAA,QACtB,OAAO;AACL,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF;AAEA,eAAS,eAAe,aAAa,gBACnC;AACA,YAAI,yBAAyB;AAC3B,gBAAM,MAAM;AACZ,sCAA4B,GAAG;AAC/B,gBAAM,WAAW,GAAG;AAAA,QACtB;AAEA,cAAM,WAAW,OAAO,mBAAmB;AAAA;AAAA;AAAA,UAE3C,eAAe,eAAe,WAAW,CAAC;AAAA,YAAI;AAC9C,cAAM,iBAAiB,eAAe,OAAO,OAAO,YAAY,KAAK,QAAQ;AAC7E,uBAAe,QAAQ,CAAC,GAAG,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC;AAAA,MACnD;AAEA,eAAS,iBAAiB,aAAa;AACrC,uBAAe,aAAa,eAAe;AAAA,MAC7C;AAEA,YAAM,MAAM,IAAI;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACT,GAAG,QAAQ;AAGX,UAAI,QAAQ,QAAW;AACrB,cAAM,iBAAiB,GAAG,IAAI,WAAW,0DAA0D,IAAI,WAAW,iDAAiD;AAAA,MACrK;AAEA,gCAA0B;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,cAAc,eAAe;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,6BAA6B;AAAA,MAC7B,4BAA4B,QAAQ;AAAA,MACpC,4BAA4B;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,WAAO,cAAc,eAAe;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,6BAA6B;AAAA,MAC7B,4BAA4B,QAAQ;AAAA,MACpC,4BAA4B;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAMA,SAAS,QAAQ,WAAS,IAAI,eAAe,KAAK;AAElD,IAAI,kBAAkB;AAItB,IAAM;AAAA,EACJ,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,0BAA0B;AAC5B,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,wBAAwB;AAC1B,IAAI;AAEJ,IAAM;AAAA,EACJ,6BAA6B;AAC/B,IAAI;AAgBJ,IAAM,SAAS,OAAK,aAAa,iBAAiB,EAAE,QAAQ;AAE5D,SAAS,SAAS,SAAS;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,sBAAsB;AAAA,EACxB,IAAI;AACJ,QAAM,aAAa,8BAA8B,QAAQ,mBAAmB;AAC5E,MAAI,kBAAkB;AAEtB,WAAS,cAAc,SAAS;AAC9B,WAAO,sBAAsB,QAAQ,KAAK,WAAS;AACjD,wBAAkB,oBAAoB,KAAK;AAC3C,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,WAAS;AAChB,wBAAkB,oBAAoB,KAAK;AAC3C,YAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAEA,MAAI,kBAAkB,iBAAiB,QAAQ,OAAO,IAAI,cAAc,QAAQ,OAAO,IAAI,aAAa,QAAQ,OAAO,IAAI,QAAQ,QAAQ,UAAU,YAAY,cAAc,QAAQ,QAAQ,QAAQ,IAAI,QAAQ;AAAA;AAAA,IACnN,oBAAoB,OAAO,QAAQ,OAAO,CAAC;AAAA;AAC3C,4BAA0B,gBAAgB,QAAQ;AAClD,MAAI,kCAAkC;AAGtC,QAAM,wBAAwB,oBAAI,IAAI;AAEtC,WAAS,0BAA0B,gBAAgB;AACjD,QAAI,MAAuC;AACzC,UAAI,QAAQ,+BAA+B,MAAM;AAC/C,YAAI,iBAAiB,cAAc,GAAG;AACpC,iBAAO,eAAe,KAAK,WAAS;AAClC,mCAAuB,KAAK;AAC5B,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,OAAO;AACL,iCAAuB,cAAc;AACrC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,OAAO,SAAS;AAC1C,UAAM,iBAAiB,QAAQ,KAAK,WAAS;AAC3C,UAAI,uBAAuB;AAE3B,YAAM,SAAS,wBAAwB,MAAM,SAAS,EAAE,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB,MAAM,SAAS,EAAE;AAE1J,YAAM,wBAAwB,MAAM,WAAW,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,cAAc,gBAAgB;AACnK,yBAAiB,OAAO,MAAM,KAAK;AAAA,MACrC;AAEA,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,WAAS;AAChB,UAAI,wBAAwB;AAE5B,YAAM,SAAS,yBAAyB,MAAM,SAAS,EAAE,cAAc,QAAQ,2BAA2B,SAAS,yBAAyB,MAAM,SAAS,EAAE;AAE7J,YAAM,yBAAyB,MAAM,WAAW,IAAI,GAAG,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,cAAc,gBAAgB;AACtK,iCAAyB,OAAO,MAAM,oBAAoB,KAAK,CAAC;AAAA,MAClE;AAEA,YAAM;AAAA,IACR,CAAC;AACD,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,OAAO,WAAW,SAAS;AAC3C,QAAI;AAEJ;AAEA,UAAM,cAAc,MAAM;AACxB,UAAI;AAEJ;AACA,OAAC,wBAAwB,sBAAsB,IAAI,KAAK,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,QAAQ,aAAW,QAAQ,CAAC;AACrK,4BAAsB,OAAO,KAAK;AAAA,IACpC;AAEA,UAAM,SAAS,EAAE,WAAW,IAAI,GAAG;AAEnC,QAAI,gBAAgB,UAAU,WAAW;AACvC,YAAM,2BAA2B,MAAM;AACrC,YAAI;AAEJ,cAAM,SAAS,yBAAyB,MAAM,SAAS,EAAE,cAAc,QAAQ,2BAA2B,SAAS,yBAAyB,MAAM,SAAS,EAAE;AAE7J,YAAI,CAAC,MAAM,WAAW,IAAI,GAAG,GAAG;AAC9B,oCAA0B,OAAO,IAAI;AAAA,QACvC;AAAA,MACF;AAEA,sBAAgB,SAAS,QAAQ,wBAAwB;AAAA,IAC3D;AAKA,UAAM,WAAW,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB,QAAQ;AAE1H,QAAI,WAAW,MAAM;AAOnB,UAAS,cAAT,SAAqB,aAAa;AAIhC,YAAI,gBAAgB,YAAY,QAAQ,KAAK;AAE3C,gBAAM,WAAW;AAEjB,iBAAO,oBAAoB,iBAAiB,SAAS,OAAO,SAAS,IACnE,iBAAiB,QAAQ,IAAI,sBAAsB,SAAS,KAAK,OAAK,aAAa;AAAA;AAAA,YACrF,gBAAgB,UAAU;AAAA,cACxB,CAAC,CAAC;AAAA;AAAA,YACJ,oBAAoB,QAAQ;AAAA;AAAA,QAC9B;AAEA,eAAO,2BAA2B,OAAO,WAAW;AAAA,MACtD,GAES,aAAT,SAAoB,aAAa;AAC/B,eAAO,YAAY,WAAW,EAAE,UAAU;AAAA,MAC5C,GAES,mBAAT,SAA0B,aAAa;AACrC,YAAI;AAEJ,cAAM,OAAO,eAAe,QAAQ,yBAAyB,MAAM,SAAS,EAAE,cAAc,QAAQ,2BAA2B,SAAS,yBAAyB,MAAM,SAAS,EAAE,aAAa,YAAY,GAAG;AAC9M,eAAO,gBAAgB,YAAY,QAAQ,OAAO,EAAE,qBAAqB,kBAAkB;AAAA,UAAE,GAAG;AAAA,UAC9F,OAAO;AAAA,UACP,UAAU,YAAY,WAAW;AAAA,QACnC,IAAI;AAAA,MACN;AAnCA,UAAI,YAAY;AAChB,UAAI,eAAe;AACnB,UAAI,cAAc;AAClB,UAAI,iBAAiB;AAkCrB,YAAM,UAAU,YAAU,oBAAkB;AAC1C,YAAI,cAAc;AAChB,gBAAM,kBAAkB,YAAY,IAAI;AACxC,gBAAM,eAAe,gBAAgB,UAAU,aAAa,gBAAgB,WAAW;AACvF,sBAAY,OAAO,mBAAmB;AAAA;AAAA,YACtC,eAAe,YAAY;AAAA,cACzB;AAEF,cAAI,iBAAiB,SAAS,GAAG;AAC/B,wBAAY,UAAU,KAAK,WAAS;AAElC,+BAAiB;AAAA,gBACf;AAAA,gBACA;AAAA,cACF;AACA,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,cAAI,iBAAiB,cAAc,GAAG;AACpC,kBAAM,WAAW,mDAAmD;AAAA,UACtE;AAEA,cAAI,OAAO,mBAAmB,YAAY;AACxC,6BAAiB;AAAA,cACf;AAAA,cACA,OAAO,OAAO,cAAc;AAAA,YAC9B;AAAA,UACF;AAEA,2BAAiB,OAAO,MAAM,OAAO,mBAAmB,aAAa,kBAAgB;AACnF,kBAAM,WAAW;AAAA;AAAA,cACjB,eAAe,YAAY;AAAA;AAAA,YAC3B;AAEA,6BAAiB;AAAA,cACf;AAAA,cACA,OAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT,IAAI,OAAO,cAAc,CAAC;AAAA,QAC5B;AAAA,MACF;AAEA,YAAM,YAAY,YAAU,MAAM,QAAQ,MAAM,EAAE,eAAe;AAEjE,YAAM,QAAQ,YAAU,aAAW;AACjC,YAAI;AAEJ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,MAAM,wBAAwB,kBAAgB;AAChD,cAAI;AAGJ,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI,aAAa,SAAS;AAE1B,cAAI,CAAC,cAAc;AACjB,wCAA4B,+FAA+F;AAC3H,2BAAe;AAAA,UACjB;AAEA,gBAAM,eAAe,wBAAwB,YAAY,WAAW,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AAErJ,cAAI,YAAY,UAAU,YAAY;AACpC,gBAAI,uBAAuB,iBAAiB,kBAAkB;AAE9D,kBAAM,WAAW,YAAY;AAC7B,kBAAM,eAAe,wBAAwB,aAAa,WAAW,IAAI,GAAG,OAAO,QAAQ,0BAA0B,SAAS,wBAAwB;AACtJ,kBAAM,WAAW,YAAY,UAAU,aAAa,YAAY,WAAW;AAS3E,kBAAM,kBAAkB,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,YAAY,YAAY,mBAAmB,oBAAoB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,UAAU;AAC5P,sBAAQ,UAAU,UAAU,CAAC,YAAY,WAAW,IAAI,GAAG,CAAC;AAAA,YAC9D,aAAa,mBAAmB,oBAAoB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,YAAY,QAAQ;AACtI,+BAAiB;AAAA,YACnB;AAAA,UACF;AAAA,QACF,GAAG,GAAG;AACN,8BAAsB,IAAI,OAAO,CAAC,IAAK,yBAAyB,sBAAsB,IAAI,KAAK,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB,CAAC,GAAI,OAAO,CAAC;AAAA,MAC1L;AAEA,iBAAW,UAAU,SAAS;AAC5B,YAAI;AACF,gBAAM,UAAU,OAAO;AAAA,YACrB;AAAA,YACA,SAAS,MAAM;AAAA,YACf,wBAAwB,MAAM;AAAA,YAC9B;AAAA,YACA,SAAS,QAAQ,MAAM;AAAA,YACvB,WAAW,UAAU,MAAM;AAAA,YAC3B,OAAO,MAAM,MAAM;AAAA,YACnB;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAED,cAAI,WAAW,MAAM;AACnB,gBAAI;AAEJ,kCAAsB,IAAI,OAAO,CAAC,IAAK,yBAAyB,sBAAsB,IAAI,KAAK,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB,CAAC,GAAI,OAAO,CAAC;AAAA,UAC1L;AAAA,QACF,SAAS,OAAO;AACd,sBAAY;AACZ,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,qBAAe;AAGf,UAAI,EAAE,qBAAqB,iBAAiB;AAC1C,YAAI;AAEJ,cAAM,eAAe,cAAc,oBAAoB,SAAS,IAAI,iBAAiB,SAAS,IAAI,sBAAsB,mBAAmB,OAAO,SAAS,CAAC,IAAI,oBAAoB,OAAO,SAAS,CAAC;AACrM,kCAA0B,aAAa,QAAQ;AAC/C,kBAAU,WAAW,IAAI,KAAK,YAAY;AAI1C,SAAC,yBAAyB,MAAM,SAAS,EAAE,cAAc,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,WAAW,IAAI,KAAK,YAAY;AAAA,MACvK;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,QAAQ,OAAO;AAC/B,QAAI,MAAM;AAEV,YAAQ,QAAQ,yBAAyB,MAAM,WAAW,IAAI,GAAG,OAAO,QAAQ,2BAA2B,SAAS,yBAAyB,qCAAqC,QAAQ,SAAS,SAAS,OAAO;AAAA,EACrN;AAEA,WAAS,QAAQ,QAAQ,OAAO;AAC9B,QAAI,MAAM,WAAW,IAAI,GAAG,GAAG;AAE7B,aAAO,kBAAkB,MAAM,WAAW,IAAI,GAAG,CAAC;AAAA,IACpD,WAAW,MAAM,kBAAkB,IAAI,GAAG,GAAG;AAG3C,UAAI,mCAAmC,MAAM;AAC3C,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,MAAM;AACvB,oCAA4B,+CAA+C,GAAG,sCAAsC;AACpH,eAAO;AAAA,MACT;AAEA,YAAM,oBAAoB,MAAM,kBAAkB,IAAI,GAAG;AACzD,YAAM,kBAAkB,YAAY,UAAU,mBAAmB,eAAe;AAChF,YAAM,yBAAyB,2BAA2B,iBAAiB,kBAAkB,oBAAoB,eAAe;AAChI,wCAAkC;AAClC,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,iBAAiB;AACxB,sCAAkC;AAAA,EACpC;AAEA,WAAS,QAAQ,QAAQ,OAAO,UAAU;AAGxC,QAAI,MAAM,WAAW,IAAI,GAAG,GAAG;AAC7B,YAAM,WAAW,kBAAkB,MAAM,WAAW,IAAI,GAAG,CAAC;AAE5D,UAAI,SAAS,UAAU,cAAc,aAAa,SAAS,UAAU;AACnE,eAAO,oBAAI,IAAI;AAAA,MACjB;AAAA,IACF,WAAW,CAAC,MAAM,kBAAkB,IAAI,GAAG,KAAK,oBAAoB,gBAAgB;AAClF,aAAO,oBAAI,IAAI;AAAA,IACjB;AAEA,8BAA0B,QAAQ;AAClC,sCAAkC;AAElC,YAAO,oBAAI,IAAI,GAAE,IAAI,KAAK,oBAAoB,QAAQ,CAAC;AAAA,EACzD;AAEA,WAAS,kCAAkC;AACzC,WAAO,2BAA2B,GAAG,MAAM,UAAa,mBAAmB;AAAA,EAC7E;AAEA,QAAM,OAAO,eAAe;AAAA,IAC1B;AAAA,IACA,UAAU;AAAA,IACV,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,6BAA6B;AAAA,IAC7B,4BAA4B,QAAQ;AAAA,IACpC,sBAAsB,QAAQ,uBAAuB;AAAA,MACnD,MAAM,QAAQ,qBAAqB;AAAA,MACnC,YAAY,QAAQ,qBAAqB;AAAA,IAC3C,IAAI;AAAA,IACJ,4BAA4B;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,SAAS,KAAK,SAAS;AACrB,MAAI,MAAuC;AACzC,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACnC,YAAM,WAAW,iFAAiF;AAAA,IACpG;AAAA,EACF;AAEA,QAAM;AAAA;AAAA,IACJ,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,iBAAiB,aAAa;AAAA;AAAA,IACpC,QAAQ;AAAA,MAAU,IAAI,QAAQ,MAAM;AAAA,EAAC,CAAC;AAEtC,MAAI,gBAAgB,cAAc,GAIhC;AACA,WAAO,iBAAiB;AAAA,MAAE,GAAG;AAAA,MAC3B,SAAS;AAAA;AAAA,IAEX,CAAC;AAAA,EASH,OAAO;AACL,WAAO,SAAS;AAAA,MAAE,GAAG;AAAA,MACnB,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACF;AAEA,SAAS,iBAAiB,SAAS;AACjC,QAAM,OAAO,KAAK;AAAA,IAAE,GAAG;AAAA,IACrB,SAAS;AAAA,IACT,sBAAsB,QAAQ,yBAAyB,SAAY,SAAY;AAAA,MAAE,GAAG,QAAQ;AAAA,MAC1F,WAAW,iBAAe,uBAAuB,iBAAiB,cAAc,kBAAkB,QAAQ,oBAAoB,EAAE,UAAU,aAAa,eAAe;AAAA,IACxK;AAAA;AAAA,IAEA,SAAS,QAAQ;AAAA;AAAA,IAEjB,kBAAkB,QAAQ;AAAA;AAAA,EAE5B,CAAC;AAED,QAAM,MAAM,gBAAgB;AAAA,IAC1B,KAAK,GAAG,QAAQ,GAAG;AAAA,IACnB,KAAK,CAAC;AAAA,MACJ;AAAA,IACF,MAAM;AACJ,YAAM,YAAY,IAAI,IAAI;AAC1B,aAAO,qBAAqB,iBAAiB,QAAQ,UAAU;AAAA,IACjE;AAAA;AAAA,IAEA,KAAK,CAAC;AAAA,MACJ;AAAA,IACF,GAAG,aAAa,IAAI,MAAM,QAAQ;AAAA;AAAA;AAAA;AAAA,IAIlC,sBAAsB;AAAA,MACpB,UAAU;AAAA,IACZ;AAAA,IACA,4BAA4B,QAAQ;AAAA,EACtC,CAAC;AACD,6BAA2B,IAAI,KAAK,2BAA2B,QAAQ,GAAG,CAAC;AAC3E,SAAO;AACT;AAGA,KAAK,QAAQ,WAAS,IAAI,eAAe,KAAK;AAE9C,IAAI,cAAc;AAalB,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,SAAS;AACnB,QAAI;AAEJ,oBAAgB,MAAM,QAAQ,MAAM;AAEpC,oBAAgB,MAAM,cAAc,MAAM;AAE1C,SAAK,OAAO,oBAAI,IAAI;AACpB,SAAK,cAAc,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,OAAK;AAAA,EACzK;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK,KAAK,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,EAC3C;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK,KAAK,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,EAC3C;AAAA,EAEA,IAAI,KAAK,KAAK;AACZ,SAAK,KAAK,IAAI,KAAK,WAAW,GAAG,GAAG,GAAG;AAAA,EACzC;AAAA,EAEA,OAAO,KAAK;AACV,SAAK,KAAK,OAAO,KAAK,WAAW,GAAG,CAAC;AAAA,EACvC;AAAA,EAEA,QAAQ;AACN,SAAK,KAAK,MAAM;AAAA,EAClB;AAEF;AAEA,IAAI,kBAAkB;AAAA,EACpB;AACF;AAEA,IAAI,oBAAoB,gBAAgB;AAExC,IAAI,oBAAiC,OAAO,OAAO;AAAA,EACjD,WAAW;AAAA,EACX,UAAU;AACZ,CAAC;AAED,IAAM;AAAA,EACJ,UAAU;AACZ,IAAI;AAEJ,IAAM;AAAA,EACJ,UAAU;AACZ,IAAI;AAQJ,IAAM,kBAAkB;AAAA,EACtB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AACX;AAEA,SAAS,gBAAgB;AAAA,EACvB,WAAW,gBAAgB;AAAA,EAC3B,WAAW,gBAAgB;AAAA,EAC3B,UAAU,gBAAgB;AAC5B,IAAI,iBAAiB;AACnB,QAAM,cAAc,iBAAiB,QAAQ;AAC7C,QAAM,QAAQ,SAAS,UAAU,SAAS,WAAW;AACrD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU;AAClC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAO;AAAA,IAEhB,KAAK;AACH,aAAO,SAAO,uBAAuB,GAAG;AAAA,EAC5C;AAEA,QAAM,WAAW,gCAAgC,QAAQ,EAAE;AAC7D;AAEA,SAAS,SAAS,UAAU,SAAS,QAAQ;AAC3C,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,IAAI,WAAW;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,IAAI,WAAW;AAAA,QACpB;AAAA,QACA,SAAS,kBAAkB,OAAO;AAAA,MACpC,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,IAAI,WAAW;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,gCAAgC,QAAQ,EAAE;AAC7D;AAEA,IAAI,yBAAyB;AAK7B,IAAM;AAAA,EACJ,0BAA0B;AAC5B,IAAI;AAkCJ,SAAS,WAAW,SAAS;AAC3B,MAAI,uBAAuB;AAE3B,QAAM,YAAY,uBAAuB;AAAA,IACvC,WAAW,yBAAyB,yBAAyB,QAAQ,mCAAmC,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB;AAAA,IACrQ,UAAU;AAAA,EACZ,CAAC;AAGD,SAAO,YAAU;AACf,QAAI,kBAAkB;AAEtB,UAAM,aAAa,UAAU,IAAI,MAAM;AAEvC,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,UAAM;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,iBAAiB,aAAa;AAAA;AAAA,MACpC,QAAQ;AAAA,QAAU,IAAI,QAAQ,MAAM;AAAA,IAAC,CAAC;AACtC,UAAM,UAAU,YAAY;AAAA,MAAE,GAAG;AAAA,MAC/B,KAAK,GAAG,QAAQ,GAAG,MAAM,mBAAmB,uBAAuB,MAAM,OAAO,QAAQ,qBAAqB,SAAS,mBAAmB,MAAM;AAAA,MAC/I,SAAS,OAAO,mBAAmB;AAAA;AAAA;AAAA;AAAA,QAGnC,eAAe,MAAM;AAAA;AAAA;AAAA,QACrB;AAAA;AAAA,MACA,qBAAqB,OAAO,QAAQ,wBAAwB,aAAa,QAAQ,oBAAoB,MAAM,IAAI,QAAQ;AAAA,MACvH,SAAS,OAAO,QAAQ,YAAY,aAAa,QAAQ,QAAQ,MAAM,IAAI,OAAO,QAAQ,qBAAqB,aAAa,QAAQ,iBAAiB,MAAM,KAAK,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAM3Q,CAAC;AACD,cAAU,IAAI,QAAQ,OAAO;AAC7B,+BAA2B,QAAQ,KAAK,MAAM;AAC5C,gBAAU,OAAO,MAAM;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAI,oBAAoB;AAExB,IAAM;AAAA,EACJ,0BAA0B;AAC5B,IAAI;AAYJ,IAAI,YAAY;AAehB,SAAS,eAAe,SAAS;AAC/B,MAAI,uBAAuB;AAE3B,QAAM,gBAAgB,uBAAuB;AAAA,IAC3C,WAAW,yBAAyB,yBAAyB,QAAQ,mCAAmC,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,cAAc,QAAQ,0BAA0B,SAAS,wBAAwB;AAAA,IACrQ,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,YAAU;AACf,QAAI;AAIJ,QAAI;AAEJ,QAAI;AACF,uBAAiB,cAAc,IAAI,MAAM;AAAA,IAC3C,SAAS,OAAO;AACd,YAAM,WAAW,0CAA0C,QAAQ,GAAG,KAAK,MAAM,OAAO,EAAE;AAAA,IAC5F;AAEA,QAAI,kBAAkB,MAAM;AAC1B,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,GAAG,QAAQ,GAAG,qBAAqB,mBAAmB,uBAAuB,QAAQ;AAAA;AAAA;AAAA,MAGjG,gBAAgB;AAAA,IAClB,CAAC,OAAO,QAAQ,qBAAqB,SAAS,mBAAmB,MAAM,IAAI,WAAW;AAEtF,UAAM,QAAQ,eAAa,QAAQ,IAAI,MAAM,EAAE,SAAS;AAExD,UAAM,gBAAgB,QAAQ;AAC9B,UAAM,aAAa,OAAO,QAAQ,wBAAwB,aAAa,QAAQ,oBAAoB,MAAM,IAAI,QAAQ;AACrH,QAAI;AAEJ,QAAI,QAAQ,OAAO,MAAM;AACvB,YAAM,MAAM,QAAQ;AAEpB,YAAM,QAAQ,CAAC,WAAW,aAAa,IAAI,MAAM,EAAE,WAAW,QAAQ;AAEtE,oBAAc,gBAAgB;AAAA,QAC5B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,sBAAsB;AAAA,QACtB,4BAA4B,QAAQ;AAAA,QACpC,qBAAqB;AAAA,MACvB,CAAC;AAAA,IACH,OAAO;AACL,oBAAc,gBAAgB;AAAA,QAC5B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,sBAAsB;AAAA,QACtB,4BAA4B,QAAQ;AAAA,QACpC,qBAAqB;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,kBAAc,IAAI,QAAQ,WAAW;AACrC,+BAA2B,YAAY,KAAK,MAAM;AAChD,oBAAc,OAAO,MAAM;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAIA,IAAI,wBAAwB;AAK5B,IAAM,mBAAmB,sBAAsB;AAAA,EAC7C,KAAK;AAAA,EACL,KAAK,cAAY,MAAM;AAAA,EACvB,+BAA+B;AAAA,IAC7B,UAAU;AAAA,EACZ;AACF,CAAC;AAKD,SAAS,cAAc,UAAU;AAC/B,SAAO,iBAAiB,QAAQ;AAClC;AAEA,IAAI,uBAAuB;AAK3B,IAAM,mBAAmB,sBAAsB;AAAA,EAC7C,KAAK;AAAA,EACL,KAAK,aAAW,MAAM;AACpB,UAAM,WAAW,OAAO;AAAA,EAC1B;AAAA;AAAA,EAEA,+BAA+B;AAAA,IAC7B,UAAU;AAAA,EACZ;AACF,CAAC;AAGD,SAAS,cAAc,SAAS;AAC9B,SAAO,iBAAiB,OAAO;AACjC;AAEA,IAAI,uBAAuB;AAe3B,SAAS,iBAAiByC,OAAM;AAE9B,SAAOA;AACT;AAEA,IAAI,0BAA0B;AAE9B,IAAM;AAAA,EACJ,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB,IAAI;AAqBJ,SAAS,mBAAmB,gBAAgB,MAAM;AAChD,QAAM,UAAU,MAAM,KAAK,MAAM,EAAE,KAAK,MAAS;AACjD,QAAM,aAAa,MAAM,KAAK,MAAM,EAAE,KAAK,MAAS;AAEpD,aAAW,CAAC,GAAG,GAAG,KAAK,KAAK,QAAQ,GAAG;AACrC,QAAI;AACF,cAAQ,CAAC,IAAI,eAAe,GAAG;AAAA,IACjC,SAAS,GAAG;AAEV,iBAAW,CAAC,IAAI;AAAA,IAClB;AAAA,EACF;AAEA,SAAO,CAAC,SAAS,UAAU;AAC7B;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,OAAO,QAAQ,CAAC,iBAAiB,GAAG;AAC7C;AAEA,SAAS,mBAAmB,cAAc;AACxC,SAAO,MAAM,QAAQ,YAAY,IAAI,eAAe,OAAO,oBAAoB,YAAY,EAAE,IAAI,SAAO,aAAa,GAAG,CAAC;AAC3H;AAEA,SAAS,YAAY,cAGrB,SAAS;AACP,SAAO,MAAM,QAAQ,YAAY,IAAI;AAAA;AAAA,IACrC,OAAO,oBAAoB,YAAY,EAAE,OAAO,CAAC,KAAK,KAAK,SAAS;AAAA,MAAE,GAAG;AAAA,MACvE,CAAC,GAAG,GAAG,QAAQ,GAAG;AAAA,IACpB,IAAI,CAAC,CAAC;AAAA;AACR;AAEA,SAAS,cAAc,cAAc,SAAS,YAAY;AACxD,QAAM,SAAS,WAAW,IAAI,CAAC,WAAW,QAAQ,aAAa,OAAO,oBAAoB,QAAQ,GAAG,CAAC,IAAI,iBAAiB,SAAS,IAAI,sBAAsB,SAAS,IAAI,oBAAoB,SAAS,CAAC;AACzM,SAAO,YAAY,cAAc,MAAM;AACzC;AAEA,SAAS,mCAAmC,aAAa,cAAc;AACrE,SAAO,aAAa,IAAI,CAAC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQjC,WAAW,SAAY,YAAY,GAAG,IAAI;AAAA,GAAM;AAClD;AAIA,IAAM,cAAc,sBAAsB;AAAA,EACxC,KAAK;AAAA,EACL,KAAK,kBAAgB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM;AAEJ,UAAM,OAAO,mBAAmB,YAAY;AAC5C,UAAM,CAAC,SAAS,UAAU,IAAI,mBAAmB,KAAK,IAAI;AAE1D,WAAO,cAAc,cAAc,SAAS,UAAU;AAAA,EACxD;AAAA,EACA,4BAA4B;AAC9B,CAAC;AAID,IAAM,aAAa,sBAAsB;AAAA,EACvC,KAAK;AAAA,EACL,KAAK,kBAAgB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM;AAGJ,UAAM,OAAO,mBAAmB,YAAY;AAC5C,UAAM,CAAC,SAAS,UAAU,IAAI,mBAAmB,KAAK,IAAI;AAE1D,QAAI,WAAW,KAAK,SAAO,CAAC,iBAAiB,GAAG,CAAC,GAAG;AAClD,aAAO,cAAc,cAAc,SAAS,UAAU;AAAA,IACxD;AAKA,WAAO,IAAI,QAAQ,aAAW;AAC5B,iBAAW,CAAC,GAAG,GAAG,KAAK,WAAW,QAAQ,GAAG;AAC3C,YAAI,iBAAiB,GAAG,GAAG;AACzB,cAAI,KAAK,YAAU;AACjB,oBAAQ,CAAC,IAAI;AACb,uBAAW,CAAC,IAAI;AAChB,oBAAQ,cAAc,cAAc,SAAS,UAAU,CAAC;AAAA,UAC1D,CAAC,EAAE,MAAM,WAAS;AAChB,uBAAW,CAAC,IAAI;AAChB,oBAAQ,cAAc,cAAc,SAAS,UAAU,CAAC;AAAA,UAC1D,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B;AAC9B,CAAC;AAGD,IAAM,aAAa,sBAAsB;AAAA,EACvC,KAAK;AAAA,EACL,KAAK,kBAAgB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM;AAGJ,UAAM,OAAO,mBAAmB,YAAY;AAC5C,UAAM,CAAC,SAAS,UAAU,IAAI,mBAAmB,KAAK,IAAI;AAE1D,QAAI,WAAW,MAAM,SAAO,OAAO,IAAI,GAAG;AACxC,aAAO,YAAY,cAAc,OAAO;AAAA,IAC1C;AAGA,UAAM,QAAQ,WAAW,KAAK,OAAO;AAErC,QAAI,SAAS,MAAM;AACjB,YAAM;AAAA,IACR;AAGA,WAAO,QAAQ,IAAI,UAAU,EAAE,KAAK,sBAAoB,YAAY,cAAc,mCAAmC,SAAS,gBAAgB,CAAC,CAAC;AAAA,EAClJ;AAAA,EACA,4BAA4B;AAC9B,CAAC;AACD,IAAM,oBAAoB,sBAAsB;AAAA,EAC9C,KAAK;AAAA,EACL,KAAK,kBAAgB,CAAC;AAAA,IACpB;AAAA,EACF,MAAM;AAGJ,UAAM,OAAO,mBAAmB,YAAY;AAC5C,UAAM,CAAC,SAAS,UAAU,IAAI,mBAAmB,KAAK,IAAI;AAE1D,QAAI,WAAW,MAAM,SAAO,CAAC,iBAAiB,GAAG,CAAC,GAAG;AACnD,aAAO,cAAc,cAAc,SAAS,UAAU;AAAA,IACxD;AAGA,WAAO,QAAQ,IAAI,WAAW,IAAI,CAAC,KAAK,MAAM,iBAAiB,GAAG,IAAI,IAAI,KAAK,YAAU;AACvF,cAAQ,CAAC,IAAI;AACb,iBAAW,CAAC,IAAI;AAAA,IAClB,CAAC,EAAE,MAAM,WAAS;AAChB,cAAQ,CAAC,IAAI;AACb,iBAAW,CAAC,IAAI;AAAA,IAClB,CAAC,IAAI,IAAI,CAAC,EACT,KAAK,MAAM,cAAc,cAAc,SAAS,UAAU,CAAC;AAAA,EAC9D;AAAA,EACA,4BAA4B;AAC9B,CAAC;AACD,IAAM,SAAS,sBAAsB;AAAA,EACnC,KAAK;AAAA,EACL,KAAK,gBAAc,CAAC;AAAA,IAClB;AAAA,EACF,MAAM;AACJ,QAAI;AACF,aAAO,gBAAgB,MAAM,oBAAoB,IAAI,UAAU,CAAC,CAAC;AAAA,IACnE,SAAS,WAAW;AAClB,aAAO,gBAAgB,MAAM,iBAAiB,SAAS,IAAI,sBAAsB,SAAS,IAAI,oBAAoB,SAAS,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,4BAA4B;AAC9B,CAAC;AACD,IAAI,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM;AAAA,EACJ;AACF,IAAI;AAEJ,IAAM;AAAA,EACJ,cAAc;AAChB,IAAI;AAEJ,IAAM;AAAA,EACJ,YAAY;AAAA,EACZ,kBAAkB;AACpB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,eAAe;AACjB,IAAI;AAEJ,IAAM;AAAA,EACJ,gBAAgB;AAAA,EAChB,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oDAAoD;AAAA,EACpD,qBAAqB;AAAA,EACrB,mBAAmB;AACrB,IAAI;AAEJ,IAAM;AAAA,EACJ,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,8BAA8B;AAChC,IAAI;AAMJ,IAAM;AAAA,EACJ,mBAAmB;AACrB,IAAI;AAsBJ,IAAM;AAAA,EACJ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,aAAa;AACf,IAAI;AAIJ,IAAI,eAAe;AAAA;AAAA,EAEjB,cAAc;AAAA,EACd,eAAe;AAAA,EACf;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,0CAA0C;AAAA;AAAA,EAE1C,MAAM;AAAA,EACN,UAAU;AAAA;AAAA,EAEV,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,kBAAkB;AAAA;AAAA,EAElB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,mBAAmB;AAAA;AAAA,EAEnB,gBAAgB;AAAA,EAChB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,wBAAwB;AAAA,EACxB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA;AAAA,EAE5C,mBAAmB;AAAA,EACnB,+BAA+B;AAAA;AAAA,EAE/B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,uCAAuC;AAAA,EACvC,mBAAmB;AAAA;AAAA,EAEnB,WAAW;AAAA,EACX,eAAe;AACjB;AACA,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,iBAAiB,aAAa;AAClC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AACnC,IAAI,kBAAkB,aAAa;AAEnC,IAAO,aAAQ;", "names": ["react", "constant", "hash", "popcount", "hashFragment", "toBitmap", "fromBitmap", "arrayUpdate", "mutate", "arraySpliceOut", "arraySpliceIn", "isEmptyNode", "Leaf", "Collision", "IndexedNode", "ArrayNode", "<PERSON><PERSON><PERSON><PERSON>", "expand", "count", "pack", "mergeLeaves", "updateCollisionList", "canEditNode", "Leaf__modify", "Collision__modify", "IndexedNode__modify", "ArrayNode__modify", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appk", "lazyVisitChildren", "lazyVisit", "visit", "buildPairs", "buildKeys", "buildValues", "graph", "nodesRetainedByZone", "nodes", "searchFromNodes", "reactDom", "atom", "state", "useSetRecoilState", "useResetRecoilState", "useRecoilValueLoadable", "useRecoilValue", "useRecoilState", "useRecoilStateLoadable"]}
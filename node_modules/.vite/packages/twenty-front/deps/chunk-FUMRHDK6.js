import {
  s
} from "./chunk-K3FRCNXE.js";
import {
  createElementBlock,
  openBlock,
  renderSlot
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/api-client/dist/components/ViewLayout/ViewLayoutContent.vue.js
var c = {};
var s2 = { class: "*:border-t-1/2 xl:*:border-l-1/2 custom-scroll flex flex-col pr-0 first:*:border-t-0 xl:flex-row xl:*:border-t-0 xl:first:*:border-l-0" };
function n(r, f) {
  return openBlock(), createElementBlock("div", s2, [
    renderSlot(r.$slots, "default")
  ]);
}
var x = s(c, [["render", n]]);

export {
  x
};
//# sourceMappingURL=chunk-FUMRHDK6.js.map

{"version": 3, "sources": ["../../../../mdast-util-to-markdown/lib/configure.js", "../../../../mdast-util-to-markdown/lib/handle/blockquote.js", "../../../../mdast-util-to-markdown/lib/util/pattern-in-scope.js", "../../../../mdast-util-to-markdown/lib/handle/break.js", "../../../../longest-streak/index.js", "../../../../mdast-util-to-markdown/lib/util/format-code-as-indented.js", "../../../../mdast-util-to-markdown/lib/util/check-fence.js", "../../../../mdast-util-to-markdown/lib/handle/code.js", "../../../../mdast-util-to-markdown/lib/util/check-quote.js", "../../../../mdast-util-to-markdown/lib/handle/definition.js", "../../../../mdast-util-to-markdown/lib/util/check-emphasis.js", "../../../../mdast-util-to-markdown/lib/util/encode-character-reference.js", "../../../../mdast-util-to-markdown/lib/util/encode-info.js", "../../../../mdast-util-to-markdown/lib/handle/emphasis.js", "../../../../mdast-util-to-markdown/node_modules/mdast-util-to-string/lib/index.js", "../../../../mdast-util-to-markdown/lib/util/format-heading-as-setext.js", "../../../../mdast-util-to-markdown/lib/handle/heading.js", "../../../../mdast-util-to-markdown/lib/handle/html.js", "../../../../mdast-util-to-markdown/lib/handle/image.js", "../../../../mdast-util-to-markdown/lib/handle/image-reference.js", "../../../../mdast-util-to-markdown/lib/handle/inline-code.js", "../../../../mdast-util-to-markdown/lib/util/format-link-as-autolink.js", "../../../../mdast-util-to-markdown/lib/handle/link.js", "../../../../mdast-util-to-markdown/lib/handle/link-reference.js", "../../../../mdast-util-to-markdown/lib/util/check-bullet.js", "../../../../mdast-util-to-markdown/lib/util/check-bullet-other.js", "../../../../mdast-util-to-markdown/lib/util/check-bullet-ordered.js", "../../../../mdast-util-to-markdown/lib/util/check-rule.js", "../../../../mdast-util-to-markdown/lib/handle/list.js", "../../../../mdast-util-to-markdown/lib/util/check-list-item-indent.js", "../../../../mdast-util-to-markdown/lib/handle/list-item.js", "../../../../mdast-util-to-markdown/lib/handle/paragraph.js", "../../../../mdast-util-to-markdown/lib/handle/root.js", "../../../../mdast-util-to-markdown/lib/util/check-strong.js", "../../../../mdast-util-to-markdown/lib/handle/strong.js", "../../../../mdast-util-to-markdown/lib/handle/text.js", "../../../../mdast-util-to-markdown/lib/util/check-rule-repetition.js", "../../../../mdast-util-to-markdown/lib/handle/thematic-break.js", "../../../../mdast-util-to-markdown/lib/handle/index.js", "../../../../mdast-util-to-markdown/lib/join.js", "../../../../mdast-util-to-markdown/lib/unsafe.js", "../../../../mdast-util-to-markdown/node_modules/micromark-util-symbol/lib/codes.js", "../../../../mdast-util-to-markdown/node_modules/micromark-util-symbol/lib/constants.js", "../../../../mdast-util-to-markdown/node_modules/micromark-util-symbol/lib/values.js", "../../../../mdast-util-to-markdown/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js", "../../../../mdast-util-to-markdown/node_modules/micromark-util-decode-string/dev/index.js", "../../../../mdast-util-to-markdown/lib/util/association.js", "../../../../mdast-util-to-markdown/lib/util/compile-pattern.js", "../../../../mdast-util-to-markdown/lib/util/container-phrasing.js", "../../../../mdast-util-to-markdown/lib/util/container-flow.js", "../../../../mdast-util-to-markdown/lib/util/indent-lines.js", "../../../../mdast-util-to-markdown/lib/util/safe.js", "../../../../mdast-util-to-markdown/lib/util/track.js", "../../../../mdast-util-to-markdown/lib/index.js"], "sourcesContent": ["/**\n * @import {Options, State} from './types.js'\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nexport function configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'extensions': {\n          // Empty.\n          break\n        }\n\n        /* c8 ignore next 4 */\n        case 'unsafe': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'join': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'handlers': {\n          map(base[key], extension[key])\n          break\n        }\n\n        default: {\n          // @ts-expect-error: matches.\n          base.options[key] = extension[key]\n        }\n      }\n    }\n  }\n\n  return base\n}\n\n/**\n * @template T\n * @param {Array<T>} left\n * @param {Array<T> | null | undefined} right\n */\nfunction list(left, right) {\n  if (right) {\n    left.push(...right)\n  }\n}\n\n/**\n * @template T\n * @param {Record<string, T>} left\n * @param {Record<string, T> | null | undefined} right\n */\nfunction map(left, right) {\n  if (right) {\n    Object.assign(left, right)\n  }\n}\n", "/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n", "/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nexport function patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n", "/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\nimport {patternInScope} from '../util/pattern-in-scope.js'\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      patternInScope(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n", "/**\n * Get the count of the longest repeating streak of `substring` in `value`.\n *\n * @param {string} value\n *   Content to search in.\n * @param {string} substring\n *   Substring to look for, typically one character.\n * @returns {number}\n *   Count of most frequent adjacent `substring`s in `value`.\n */\nexport function longestStreak(value, substring) {\n  const source = String(value)\n  let index = source.indexOf(substring)\n  let expected = index\n  let count = 0\n  let max = 0\n\n  if (typeof substring !== 'string') {\n    throw new TypeError('Expected substring')\n  }\n\n  while (index !== -1) {\n    if (index === expected) {\n      if (++count > max) {\n        max = count\n      }\n    } else {\n      count = 1\n    }\n\n    expected = index + substring.length\n    index = source.indexOf(substring, expected)\n  }\n\n  return max\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nexport function checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\nimport {longestStreak} from 'longest-streak'\nimport {formatCodeAsIndented} from '../util/format-code-as-indented.js'\nimport {checkFence} from '../util/check-fence.js'\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function code(node, _, state, info) {\n  const marker = checkFence(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if (formatCodeAsIndented(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max(longestStreak(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nexport function checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function definition(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nexport function checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nexport function encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n", "/**\n * @import {EncodeSides} from '../types.js'\n */\n\nimport {classifyCharacter} from 'micromark-util-classify-character'\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nexport function encodeInfo(outside, inside, marker) {\n  const outsideKind = classifyCharacter(outside)\n  const insideKind = classifyCharacter(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\nimport {checkEmphasis} from '../util/check-emphasis.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function emphasis(node, _, state, info) {\n  const marker = checkEmphasis(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n", "/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\nimport {EXIT, visit} from 'unist-util-visit'\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  visit(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      toString(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {formatHeadingAsSetext} from '../util/format-heading-as-setext.js'\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if (formatHeadingAsSetext(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = encodeCharacterReference(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n", "/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nexport function html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function image(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nexport function inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatLinkAsAutolink(node, state) {\n  const raw = toString(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\nimport {formatLinkAsAutolink} from '../util/format-link-as-autolink.js'\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function link(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if (formatLinkAsAutolink(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return formatLinkAsAutolink(node, state) ? '<' : '['\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\nimport {checkBullet} from './check-bullet.js'\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBulletOther(state) {\n  const bullet = checkBullet(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nexport function checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nexport function checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkBulletOther} from '../util/check-bullet-other.js'\nimport {checkBulletOrdered} from '../util/check-bullet-ordered.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? checkBulletOrdered(state) : checkBullet(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : checkBulletOther(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (checkRule(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nexport function checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n", "/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkListItemIndent} from '../util/check-list-item-indent.js'\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function listItem(node, parent, state, info) {\n  const listItemIndent = checkListItemIndent(state)\n  let bullet = state.bulletCurrent || checkBullet(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\nimport {phrasing} from 'mdast-util-phrasing'\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return phrasing(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nexport function checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\nimport {checkStrong} from '../util/check-strong.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function strong(node, _, state, info) {\n  const marker = checkStrong(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nexport function checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\nimport {checkRuleRepetition} from '../util/check-rule-repetition.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nexport function thematicBreak(_, _1, state) {\n  const value = (\n    checkRule(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat(checkRuleRepetition(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {definition} from './definition.js'\nimport {emphasis} from './emphasis.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {image} from './image.js'\nimport {imageReference} from './image-reference.js'\nimport {inlineCode} from './inline-code.js'\nimport {link} from './link.js'\nimport {linkReference} from './link-reference.js'\nimport {list} from './list.js'\nimport {listItem} from './list-item.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default (CommonMark) handlers.\n */\nexport const handle = {\n  blockquote,\n  break: hardBreak,\n  code,\n  definition,\n  emphasis,\n  hardBreak,\n  heading,\n  html,\n  image,\n  imageReference,\n  inlineCode,\n  link,\n  linkReference,\n  list,\n  listItem,\n  paragraph,\n  root,\n  strong,\n  text,\n  thematicBreak\n}\n", "/**\n * @import {Join} from 'mdast-util-to-markdown'\n */\n\nimport {formatCodeAsIndented} from './util/format-code-as-indented.js'\nimport {formatHeadingAsSetext} from './util/format-heading-as-setext.js'\n\n/** @type {Array<Join>} */\nexport const join = [joinDefaults]\n\n/** @type {Join} */\nfunction joinDefaults(left, right, parent, state) {\n  // Indented code after list or another indented code.\n  if (\n    right.type === 'code' &&\n    formatCodeAsIndented(right, state) &&\n    (left.type === 'list' ||\n      (left.type === right.type && formatCodeAsIndented(left, state)))\n  ) {\n    return false\n  }\n\n  // Join children of a list or an item.\n  // In which case, `parent` has a `spread` field.\n  if ('spread' in parent && typeof parent.spread === 'boolean') {\n    if (\n      left.type === 'paragraph' &&\n      // Two paragraphs.\n      (left.type === right.type ||\n        right.type === 'definition' ||\n        // Paragraph followed by a setext heading.\n        (right.type === 'heading' && formatHeadingAsSetext(right, state)))\n    ) {\n      return\n    }\n\n    return parent.spread ? 1 : 0\n  }\n}\n", "/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain things like attention (emphasis, strong), images, or links.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * @type {Array<ConstructName>}\n */\nconst fullPhrasingSpans = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\n/** @type {Array<Unsafe>} */\nexport const unsafe = [\n  {character: '\\t', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: '\\t', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: '\\t',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  {\n    character: '\\r',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {\n    character: '\\n',\n    inConstruct: [\n      'codeFencedLangGraveAccent',\n      'codeFencedLangTilde',\n      'codeFencedMetaGraveAccent',\n      'codeFencedMetaTilde',\n      'destinationLiteral',\n      'headingAtx'\n    ]\n  },\n  {character: ' ', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {character: ' ', before: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  {\n    character: ' ',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedLangTilde']\n  },\n  // An exclamation mark can start an image, if it is followed by a link or\n  // a link reference.\n  {\n    character: '!',\n    after: '\\\\[',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A quote can break out of a title.\n  {character: '\"', inConstruct: 'titleQuote'},\n  // A number sign could start an ATX heading if it starts a line.\n  {atBreak: true, character: '#'},\n  {character: '#', inConstruct: 'headingAtx', after: '(?:[\\r\\n]|$)'},\n  // Dollar sign and percentage are not used in markdown.\n  // An ampersand could start a character reference.\n  {character: '&', after: '[#A-Za-z]', inConstruct: 'phrasing'},\n  // An apostrophe can break out of a title.\n  {character: \"'\", inConstruct: 'titleApostrophe'},\n  // A left paren could break out of a destination raw.\n  {character: '(', inConstruct: 'destinationRaw'},\n  // A left paren followed by `]` could make something into a link or image.\n  {\n    before: '\\\\]',\n    character: '(',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  // A right paren could start a list item or break out of a destination\n  // raw.\n  {atBreak: true, before: '\\\\d+', character: ')'},\n  {character: ')', inConstruct: 'destinationRaw'},\n  // An asterisk can start thematic breaks, list items, emphasis, strong.\n  {atBreak: true, character: '*', after: '(?:[ \\t\\r\\n*])'},\n  {character: '*', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A plus sign could start a list item.\n  {atBreak: true, character: '+', after: '(?:[ \\t\\r\\n])'},\n  // A dash can start thematic breaks, list items, and setext heading\n  // underlines.\n  {atBreak: true, character: '-', after: '(?:[ \\t\\r\\n-])'},\n  // A dot could start a list item.\n  {atBreak: true, before: '\\\\d+', character: '.', after: '(?:[ \\t\\r\\n]|$)'},\n  // Slash, colon, and semicolon are not used in markdown for constructs.\n  // A less than can start html (flow or text) or an autolink.\n  // HTML could start with an exclamation mark (declaration, cdata, comment),\n  // slash (closing tag), question mark (instruction), or a letter (tag).\n  // An autolink also starts with a letter.\n  // Finally, it could break out of a destination literal.\n  {atBreak: true, character: '<', after: '[!/?A-Za-z]'},\n  {\n    character: '<',\n    after: '[!/?A-Za-z]',\n    inConstruct: 'phrasing',\n    notInConstruct: fullPhrasingSpans\n  },\n  {character: '<', inConstruct: 'destinationLiteral'},\n  // An equals to can start setext heading underlines.\n  {atBreak: true, character: '='},\n  // A greater than can start block quotes and it can break out of a\n  // destination literal.\n  {atBreak: true, character: '>'},\n  {character: '>', inConstruct: 'destinationLiteral'},\n  // Question mark and at sign are not used in markdown for constructs.\n  // A left bracket can start definitions, references, labels,\n  {atBreak: true, character: '['},\n  {character: '[', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  {character: '[', inConstruct: ['label', 'reference']},\n  // A backslash can start an escape (when followed by punctuation) or a\n  // hard break (when followed by an eol).\n  // Note: typical escapes are handled in `safe`!\n  {character: '\\\\', after: '[\\\\r\\\\n]', inConstruct: 'phrasing'},\n  // A right bracket can exit labels.\n  {character: ']', inConstruct: ['label', 'reference']},\n  // Caret is not used in markdown for constructs.\n  // An underscore can start emphasis, strong, or a thematic break.\n  {atBreak: true, character: '_'},\n  {character: '_', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // A grave accent can start code (fenced or text), or it can break out of\n  // a grave accent code fence.\n  {atBreak: true, character: '`'},\n  {\n    character: '`',\n    inConstruct: ['codeFencedLangGraveAccent', 'codeFencedMetaGraveAccent']\n  },\n  {character: '`', inConstruct: 'phrasing', notInConstruct: fullPhrasingSpans},\n  // Left brace, vertical bar, right brace are not used in markdown for\n  // constructs.\n  // A tilde can start code (fenced).\n  {atBreak: true, character: '~'}\n]\n", "/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n", "/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n", "/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n", "import {codes, values} from 'micromark-util-symbol'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55_295 && code < 57_344) ||\n    // Noncharacters.\n    (code > 64_975 && code < 65_008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65_535) === 65_535 ||\n    (code & 65_535) === 65_534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1_114_111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCodePoint(code)\n}\n", "import {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX\n    return decodeNumericCharacterReference(\n      $2.slice(hex ? 2 : 1),\n      hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal\n    )\n  }\n\n  return decodeNamedCharacterReference($2) || $0\n}\n", "/**\n * @import {AssociationId} from '../types.js'\n */\n\nimport {decodeString} from 'micromark-util-decode-string'\n\n/**\n * Get an identifier from an association to match it to others.\n *\n * Associations are nodes that match to something else through an ID:\n * <https://github.com/syntax-tree/mdast#association>.\n *\n * The `label` of an association is the string value: character escapes and\n * references work, and casing is intact.\n * The `identifier` is used to match one association to another:\n * controversially, character escapes and references don’t work in this\n * matching: `&copy;` does not match `©`, and `\\+` does not match `+`.\n *\n * But casing is ignored (and whitespace) is trimmed and collapsed: ` A\\nb`\n * matches `a b`.\n * So, we do prefer the label when figuring out how we’re going to serialize:\n * it has whitespace, casing, and we can ignore most useless character\n * escapes and all character references.\n *\n * @type {AssociationId}\n */\nexport function association(node) {\n  if (node.label || !node.identifier) {\n    return node.label || ''\n  }\n\n  return decodeString(node.identifier)\n}\n", "/**\n * @import {CompilePattern} from '../types.js'\n */\n\n/**\n * @type {CompilePattern}\n */\nexport function compilePattern(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n", "/**\n * @import {Handle, Info, State} from 'mdast-util-to-markdown'\n * @import {PhrasingParents} from '../types.js'\n */\n\nimport {encodeCharacterReference} from './encode-character-reference.js'\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nexport function containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n  /** @type {string | undefined} */\n  let encodeAfter\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    let value = state.handle(child, parent, state, {\n      ...tracker.current(),\n      after,\n      before\n    })\n\n    // If we had to encode the first character after the previous node and it’s\n    // still the same character,\n    // encode it.\n    if (encodeAfter && encodeAfter === value.slice(0, 1)) {\n      value =\n        encodeCharacterReference(encodeAfter.charCodeAt(0)) + value.slice(1)\n    }\n\n    const encodingInfo = state.attentionEncodeSurroundingInfo\n    state.attentionEncodeSurroundingInfo = undefined\n    encodeAfter = undefined\n\n    // If we have to encode the first character before the current node and\n    // it’s still the same character,\n    // encode it.\n    if (encodingInfo) {\n      if (\n        results.length > 0 &&\n        encodingInfo.before &&\n        before === results[results.length - 1].slice(-1)\n      ) {\n        results[results.length - 1] =\n          results[results.length - 1].slice(0, -1) +\n          encodeCharacterReference(before.charCodeAt(0))\n      }\n\n      if (encodingInfo.after) encodeAfter = after\n    }\n\n    tracker.move(value)\n    results.push(value)\n    before = value.slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {FlowChildren, FlowParents, TrackFields} from '../types.js'\n */\n\n/**\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nexport function containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  const tracker = state.createTracker(info)\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          before: '\\n',\n          after: '\\n',\n          ...tracker.current()\n        })\n      )\n    )\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(\n        tracker.move(between(child, children[index + 1], parent, state))\n      )\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {FlowChildren} left\n * @param {FlowChildren} right\n * @param {FlowParents} parent\n * @param {State} state\n * @returns {string}\n */\nfunction between(left, right, parent, state) {\n  let index = state.join.length\n\n  while (index--) {\n    const result = state.join[index](left, right, parent, state)\n\n    if (result === true || result === 1) {\n      break\n    }\n\n    if (typeof result === 'number') {\n      return '\\n'.repeat(1 + result)\n    }\n\n    if (result === false) {\n      return '\\n\\n<!---->\\n\\n'\n    }\n  }\n\n  return '\\n\\n'\n}\n", "/**\n * @import {IndentLines} from '../types.js'\n */\n\nconst eol = /\\r?\\n|\\r/g\n\n/**\n * @type {IndentLines}\n */\nexport function indentLines(value, map) {\n  /** @type {Array<string>} */\n  const result = []\n  let start = 0\n  let line = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = eol.exec(value))) {\n    one(value.slice(start, match.index))\n    result.push(match[0])\n    start = match.index + match[0].length\n    line++\n  }\n\n  one(value.slice(start))\n\n  return result.join('')\n\n  /**\n   * @param {string} value\n   */\n  function one(value) {\n    result.push(map(value, line, !value))\n  }\n}\n", "/**\n * @import {SafeConfig, State} from 'mdast-util-to-markdown'\n */\n\nimport {encodeCharacterReference} from './encode-character-reference.js'\nimport {patternInScope} from './pattern-in-scope.js'\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {string | null | undefined} input\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nexport function safe(state, input, config) {\n  const value = (config.before || '') + (input || '') + (config.after || '')\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {Record<number, {before: boolean, after: boolean}>} */\n  const infos = {}\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n\n    if (!patternInScope(state.stack, pattern)) {\n      continue\n    }\n\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    while ((match = expression.exec(value))) {\n      const before = 'before' in pattern || Boolean(pattern.atBreak)\n      const after = 'after' in pattern\n      const position = match.index + (before ? match[1].length : 0)\n\n      if (positions.includes(position)) {\n        if (infos[position].before && !before) {\n          infos[position].before = false\n        }\n\n        if (infos[position].after && !after) {\n          infos[position].after = false\n        }\n      } else {\n        positions.push(position)\n        infos[position] = {before, after}\n      }\n    }\n  }\n\n  positions.sort(numerical)\n\n  let start = config.before ? config.before.length : 0\n  const end = value.length - (config.after ? config.after.length : 0)\n  index = -1\n\n  while (++index < positions.length) {\n    const position = positions[index]\n\n    // Character before or after matched:\n    if (position < start || position >= end) {\n      continue\n    }\n\n    // If this character is supposed to be escaped because it has a condition on\n    // the next character, and the next character is definitly being escaped,\n    // then skip this escape.\n    if (\n      (position + 1 < end &&\n        positions[index + 1] === position + 1 &&\n        infos[position].after &&\n        !infos[position + 1].before &&\n        !infos[position + 1].after) ||\n      (positions[index - 1] === position - 1 &&\n        infos[position].before &&\n        !infos[position - 1].before &&\n        !infos[position - 1].after)\n    ) {\n      continue\n    }\n\n    if (start !== position) {\n      // If we have to use a character reference, an ampersand would be more\n      // correct, but as backslashes only care about punctuation, either will\n      // do the trick\n      result.push(escapeBackslashes(value.slice(start, position), '\\\\'))\n    }\n\n    start = position\n\n    if (\n      /[!-/:-@[-`{-~]/.test(value.charAt(position)) &&\n      (!config.encode || !config.encode.includes(value.charAt(position)))\n    ) {\n      // Character escape.\n      result.push('\\\\')\n    } else {\n      // Character reference.\n      result.push(encodeCharacterReference(value.charCodeAt(position)))\n      start++\n    }\n  }\n\n  result.push(escapeBackslashes(value.slice(start, end), config.after))\n\n  return result.join('')\n}\n\n/**\n * @param {number} a\n * @param {number} b\n * @returns {number}\n */\nfunction numerical(a, b) {\n  return a - b\n}\n\n/**\n * @param {string} value\n * @param {string} after\n * @returns {string}\n */\nfunction escapeBackslashes(value, after) {\n  const expression = /\\\\(?=[!-/:-@[-`{-~])/g\n  /** @type {Array<number>} */\n  const positions = []\n  /** @type {Array<string>} */\n  const results = []\n  const whole = value + after\n  let index = -1\n  let start = 0\n  /** @type {RegExpExecArray | null} */\n  let match\n\n  while ((match = expression.exec(whole))) {\n    positions.push(match.index)\n  }\n\n  while (++index < positions.length) {\n    if (start !== positions[index]) {\n      results.push(value.slice(start, positions[index]))\n    }\n\n    results.push('\\\\')\n    start = positions[index]\n  }\n\n  results.push(value.slice(start))\n\n  return results.join('')\n}\n", "/**\n * @import {CreateTracker, TrackCurrent, TrackMove, TrackShift} from '../types.js'\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nexport function track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n", "/**\n * @import {Info, Join, Options, SafeConfig, State} from 'mdast-util-to-markdown'\n * @import {Nodes} from 'mdast'\n * @import {Enter, FlowParents, PhrasingParents, TrackFields} from './types.js'\n */\n\nimport {zwitch} from 'zwitch'\nimport {configure} from './configure.js'\nimport {handle as handlers} from './handle/index.js'\nimport {join} from './join.js'\nimport {unsafe} from './unsafe.js'\nimport {association} from './util/association.js'\nimport {compilePattern} from './util/compile-pattern.js'\nimport {containerPhrasing} from './util/container-phrasing.js'\nimport {containerFlow} from './util/container-flow.js'\nimport {indentLines} from './util/indent-lines.js'\nimport {safe} from './util/safe.js'\nimport {track} from './util/track.js'\n\n/**\n * Turn an mdast syntax tree into markdown.\n *\n * @param {Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized markdown representing `tree`.\n */\nexport function toMarkdown(tree, options) {\n  const settings = options || {}\n  /** @type {State} */\n  const state = {\n    associationId: association,\n    containerPhrasing: containerPhrasingBound,\n    containerFlow: containerFlowBound,\n    createTracker: track,\n    compilePattern,\n    enter,\n    // @ts-expect-error: GFM / frontmatter are typed in `mdast` but not defined\n    // here.\n    handlers: {...handlers},\n    // @ts-expect-error: add `handle` in a second.\n    handle: undefined,\n    indentLines,\n    indexStack: [],\n    join: [...join],\n    options: {},\n    safe: safeBound,\n    stack: [],\n    unsafe: [...unsafe]\n  }\n\n  configure(state, settings)\n\n  if (state.options.tightDefinitions) {\n    state.join.push(joinDefinition)\n  }\n\n  state.handle = zwitch('type', {\n    invalid,\n    unknown,\n    handlers: state.handlers\n  })\n\n  let result = state.handle(tree, undefined, state, {\n    before: '\\n',\n    after: '\\n',\n    now: {line: 1, column: 1},\n    lineShift: 0\n  })\n\n  if (\n    result &&\n    result.charCodeAt(result.length - 1) !== 10 &&\n    result.charCodeAt(result.length - 1) !== 13\n  ) {\n    result += '\\n'\n  }\n\n  return result\n\n  /** @type {Enter} */\n  function enter(name) {\n    state.stack.push(name)\n    return exit\n\n    /**\n     * @returns {undefined}\n     */\n    function exit() {\n      state.stack.pop()\n    }\n  }\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * @param {unknown} value\n * @returns {never}\n */\nfunction unknown(value) {\n  // Always a node.\n  const node = /** @type {Nodes} */ (value)\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/** @type {Join} */\nfunction joinDefinition(left, right) {\n  // No blank line between adjacent definitions.\n  if (left.type === 'definition' && left.type === right.type) {\n    return 0\n  }\n}\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {PhrasingParents} parent\n *   Parent of flow nodes.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasingBound(parent, info) {\n  return containerPhrasing(parent, this, info)\n}\n\n/**\n * Serialize the children of a parent that contains flow children.\n *\n * These children will typically be joined by blank lines.\n * What they are joined by exactly is defined by `Join` functions.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {FlowParents} parent\n *   Parent of flow nodes.\n * @param {TrackFields} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlowBound(parent, info) {\n  return containerFlow(parent, this, info)\n}\n\n/**\n * Make a string safe for embedding in markdown constructs.\n *\n * In markdown, almost all punctuation characters can, in certain cases,\n * result in something.\n * Whether they do is highly subjective to where they happen and in what\n * they happen.\n *\n * To solve this, `mdast-util-to-markdown` tracks:\n *\n * * Characters before and after something;\n * * What “constructs” we are in.\n *\n * This information is then used by this function to escape or encode\n * special characters.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} value\n *   Raw value to make safe.\n * @param {SafeConfig} config\n *   Configuration.\n * @returns {string}\n *   Serialized markdown safe for embedding.\n */\nfunction safeBound(value, config) {\n  return safe(this, value, config)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,IAAM,MAAM,CAAC,EAAE;AAOR,SAAS,UAAU,MAAM,WAAW;AACzC,MAAI,QAAQ;AAEZ,MAAI;AAGJ,MAAI,UAAU,YAAY;AACxB,WAAO,EAAE,QAAQ,UAAU,WAAW,QAAQ;AAC5C,gBAAU,MAAM,UAAU,WAAW,KAAK,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,OAAK,OAAO,WAAW;AACrB,QAAI,IAAI,KAAK,WAAW,GAAG,GAAG;AAC5B,cAAQ,KAAK;AAAA,QACX,KAAK,cAAc;AAEjB;AAAA,QACF;AAAA;AAAA,QAGA,KAAK,UAAU;AACb,eAAK,KAAK,GAAG,GAAG,UAAU,GAAG,CAAC;AAC9B;AAAA,QACF;AAAA,QAEA,KAAK,QAAQ;AACX,eAAK,KAAK,GAAG,GAAG,UAAU,GAAG,CAAC;AAC9B;AAAA,QACF;AAAA,QAEA,KAAK,YAAY;AACf,cAAI,KAAK,GAAG,GAAG,UAAU,GAAG,CAAC;AAC7B;AAAA,QACF;AAAA,QAEA,SAAS;AAEP,eAAK,QAAQ,GAAG,IAAI,UAAU,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,KAAK,MAAM,OAAO;AACzB,MAAI,OAAO;AACT,SAAK,KAAK,GAAG,KAAK;AAAA,EACpB;AACF;AAOA,SAAS,IAAI,MAAM,OAAO;AACxB,MAAI,OAAO;AACT,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AACF;;;AClEO,SAAS,WAAWA,OAAM,GAAG,OAAO,MAAM;AAC/C,QAAM,OAAO,MAAM,MAAM,YAAY;AACrC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAQ,KAAK,IAAI;AACjB,UAAQ,MAAM,CAAC;AACf,QAAM,QAAQ,MAAM;AAAA,IAClB,MAAM,cAAcA,OAAM,QAAQ,QAAQ,CAAC;AAAA,IAC3CC;AAAA,EACF;AACA,OAAK;AACL,SAAO;AACT;AAGA,SAASA,KAAI,MAAM,GAAG,OAAO;AAC3B,SAAO,OAAO,QAAQ,KAAK,OAAO;AACpC;;;ACnBO,SAAS,eAAe,OAAO,SAAS;AAC7C,SACE,YAAY,OAAO,QAAQ,aAAa,IAAI,KAC5C,CAAC,YAAY,OAAO,QAAQ,gBAAgB,KAAK;AAErD;AAQA,SAAS,YAAY,OAAOC,OAAM,MAAM;AACtC,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAAA,QAAO,CAACA,KAAI;AAAA,EACd;AAEA,MAAI,CAACA,SAAQA,MAAK,WAAW,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,MAAK,QAAQ;AAC5B,QAAI,MAAM,SAASA,MAAK,KAAK,CAAC,GAAG;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AC1BO,SAAS,UAAU,GAAG,IAAI,OAAO,MAAM;AAC5C,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AAGpC,QACE,MAAM,OAAO,KAAK,EAAE,cAAc,QAClC,eAAe,MAAM,OAAO,MAAM,OAAO,KAAK,CAAC,GAC/C;AACA,aAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF;AAEA,SAAO;AACT;;;ACnBO,SAAS,cAAc,OAAO,WAAW;AAC9C,QAAM,SAAS,OAAO,KAAK;AAC3B,MAAI,QAAQ,OAAO,QAAQ,SAAS;AACpC,MAAI,WAAW;AACf,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,OAAO,cAAc,UAAU;AACjC,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,SAAO,UAAU,IAAI;AACnB,QAAI,UAAU,UAAU;AACtB,UAAI,EAAE,QAAQ,KAAK;AACjB,cAAM;AAAA,MACR;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAEA,eAAW,QAAQ,UAAU;AAC7B,YAAQ,OAAO,QAAQ,WAAW,QAAQ;AAAA,EAC5C;AAEA,SAAO;AACT;;;ACzBO,SAAS,qBAAqBC,OAAM,OAAO;AAChD,SAAO;AAAA,IACL,MAAM,QAAQ,WAAW,SACvBA,MAAK;AAAA,IAEL,CAACA,MAAK;AAAA,IAEN,WAAW,KAAKA,MAAK,KAAK;AAAA,IAE1B,CAAC,0CAA0C,KAAKA,MAAK,KAAK;AAAA,EAC9D;AACF;;;ACbO,SAAS,WAAW,OAAO;AAChC,QAAM,SAAS,MAAM,QAAQ,SAAS;AAEtC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,iCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACJO,SAAS,KAAKC,OAAM,GAAG,OAAO,MAAM;AACzC,QAAM,SAAS,WAAW,KAAK;AAC/B,QAAM,MAAMA,MAAK,SAAS;AAC1B,QAAM,SAAS,WAAW,MAAM,gBAAgB;AAEhD,MAAI,qBAAqBA,OAAM,KAAK,GAAG;AACrC,UAAMC,QAAO,MAAM,MAAM,cAAc;AACvC,UAAMC,SAAQ,MAAM,YAAY,KAAKC,IAAG;AACxC,IAAAF,MAAK;AACL,WAAOC;AAAA,EACT;AAEA,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,WAAW,OAAO,OAAO,KAAK,IAAI,cAAc,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC;AAC1E,QAAM,OAAO,MAAM,MAAM,YAAY;AACrC,MAAI,QAAQ,QAAQ,KAAK,QAAQ;AAEjC,MAAIF,MAAK,MAAM;AACb,UAAM,UAAU,MAAM,MAAM,iBAAiB,MAAM,EAAE;AACrD,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,MAAM;AAAA,QACpB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ,CAAC,GAAG;AAAA,QACZ,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,YAAQ;AAAA,EACV;AAEA,MAAIA,MAAK,QAAQA,MAAK,MAAM;AAC1B,UAAM,UAAU,MAAM,MAAM,iBAAiB,MAAM,EAAE;AACrD,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,MAAM;AAAA,QACpB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ,CAAC,GAAG;AAAA,QACZ,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,IAAI;AAE1B,MAAI,KAAK;AACP,aAAS,QAAQ,KAAK,MAAM,IAAI;AAAA,EAClC;AAEA,WAAS,QAAQ,KAAK,QAAQ;AAC9B,OAAK;AACL,SAAO;AACT;AAGA,SAASG,KAAI,MAAM,GAAG,OAAO;AAC3B,UAAQ,QAAQ,KAAK,UAAU;AACjC;;;AClEO,SAAS,WAAW,OAAO;AAChC,QAAM,SAAS,MAAM,QAAQ,SAAS;AAEtC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACNO,SAAS,WAAWC,OAAM,GAAG,OAAO,MAAM;AAC/C,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAM,OAAO,MAAM,MAAM,YAAY;AACrC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,WAAS,QAAQ;AAAA,IACf,MAAM,KAAK,MAAM,cAAcA,KAAI,GAAG;AAAA,MACpC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,KAAK,KAAK;AAE3B,UAAQ;AAER;AAAA;AAAA,IAEE,CAACA,MAAK;AAAA,IAEN,eAAe,KAAKA,MAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAOA,MAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAIA,MAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,OAAK;AAEL,SAAO;AACT;;;ACnEO,SAAS,cAAc,OAAO;AACnC,QAAM,SAAS,MAAM,QAAQ,YAAY;AAEzC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,qCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACZO,SAAS,yBAAyBC,OAAM;AAC7C,SAAO,QAAQA,MAAK,SAAS,EAAE,EAAE,YAAY,IAAI;AACnD;;;AC8BO,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAClD,QAAM,cAAc,kBAAkB,OAAO;AAC7C,QAAM,aAAa,kBAAkB,MAAM;AAG3C,MAAI,gBAAgB,QAAW;AAC7B,WAAO,eAAe;AAAA;AAAA;AAAA;AAAA,MAIlB,WAAW,MACT,EAAC,QAAQ,MAAM,SAAS,KAAI,IAC5B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,QAChC,eAAe;AAAA;AAAA,MAEb,EAAC,QAAQ,MAAM,SAAS,KAAI;AAAA;AAAA;AAAA,MAE5B,EAAC,QAAQ,OAAO,SAAS,KAAI;AAAA;AAAA,EACrC;AAGA,MAAI,gBAAgB,GAAG;AACrB,WAAO,eAAe;AAAA;AAAA,MAElB,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,QAC9B,eAAe;AAAA;AAAA,MAEb,EAAC,QAAQ,MAAM,SAAS,KAAI;AAAA;AAAA;AAAA,MAE5B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA;AAAA,EACtC;AAGA,SAAO,eAAe;AAAA;AAAA,IAElB,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,MAC9B,eAAe;AAAA;AAAA,IAEb,EAAC,QAAQ,MAAM,SAAS,MAAK;AAAA;AAAA;AAAA,IAE7B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA;AACtC;;;ACxEA,SAAS,OAAO;AAST,SAAS,SAASC,OAAM,GAAG,OAAO,MAAM;AAC7C,QAAM,SAAS,cAAc,KAAK;AAClC,QAAM,OAAO,MAAM,MAAM,UAAU;AACnC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,SAAS,QAAQ,KAAK,MAAM;AAElC,MAAIC,WAAU,QAAQ;AAAA,IACpB,MAAM,kBAAkBD,OAAM;AAAA,MAC5B,OAAO;AAAA,MACP;AAAA,MACA,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,cAAcC,SAAQ,WAAW,CAAC;AACxC,QAAM,OAAO;AAAA,IACX,KAAK,OAAO,WAAW,KAAK,OAAO,SAAS,CAAC;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AAEA,MAAI,KAAK,QAAQ;AACf,IAAAA,WAAU,yBAAyB,WAAW,IAAIA,SAAQ,MAAM,CAAC;AAAA,EACnE;AAEA,QAAM,cAAcA,SAAQ,WAAWA,SAAQ,SAAS,CAAC;AACzD,QAAM,QAAQ,WAAW,KAAK,MAAM,WAAW,CAAC,GAAG,aAAa,MAAM;AAEtE,MAAI,MAAM,QAAQ;AAChB,IAAAA,WAAUA,SAAQ,MAAM,GAAG,EAAE,IAAI,yBAAyB,WAAW;AAAA,EACvE;AAEA,QAAM,QAAQ,QAAQ,KAAK,MAAM;AAEjC,OAAK;AAEL,QAAM,iCAAiC;AAAA,IACrC,OAAO,MAAM;AAAA,IACb,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,SAASA,WAAU;AAC5B;AAQA,SAAS,aAAa,GAAG,IAAI,OAAO;AAClC,SAAO,MAAM,QAAQ,YAAY;AACnC;;;ACxDA,IAAM,eAAe,CAAC;AAef,SAAS,SAAS,OAAO,SAAS;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,kBACJ,OAAO,SAAS,oBAAoB,YAChC,SAAS,kBACT;AACN,QAAM,cACJ,OAAO,SAAS,gBAAgB,YAAY,SAAS,cAAc;AAErE,SAAO,IAAI,OAAO,iBAAiB,WAAW;AAChD;AAcA,SAAS,IAAI,OAAO,iBAAiB,aAAa;AAChD,MAAI,KAAK,KAAK,GAAG;AACf,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,SAAS,UAAU,CAAC,cAAc,KAAK,MAAM;AAAA,IAC5D;AAEA,QAAI,mBAAmB,SAAS,SAAS,MAAM,KAAK;AAClD,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,cAAc,OAAO;AACvB,aAAO,IAAI,MAAM,UAAU,iBAAiB,WAAW;AAAA,IACzD;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,IAAI,OAAO,iBAAiB,WAAW;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAAS,IAAIC,SAAQ,iBAAiB,aAAa;AAEjD,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,QAAO,QAAQ;AAC9B,WAAO,KAAK,IAAI,IAAIA,QAAO,KAAK,GAAG,iBAAiB,WAAW;AAAA,EACjE;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAUA,SAAS,KAAK,OAAO;AACnB,SAAO,QAAQ,SAAS,OAAO,UAAU,QAAQ;AACnD;;;AC9FO,SAAS,sBAAsBC,OAAM,OAAO;AACjD,MAAI,mBAAmB;AAIvB,QAAMA,OAAM,SAAUA,OAAM;AAC1B,QACG,WAAWA,SAAQ,WAAW,KAAKA,MAAK,KAAK,KAC9CA,MAAK,SAAS,SACd;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,SAAO;AAAA,KACJ,CAACA,MAAK,SAASA,MAAK,QAAQ,MAC3B,SAASA,KAAI,MACZ,MAAM,QAAQ,UAAU;AAAA,EAC7B;AACF;;;AClBO,SAAS,QAAQC,OAAM,GAAG,OAAO,MAAM;AAC5C,QAAM,OAAO,KAAK,IAAI,KAAK,IAAI,GAAGA,MAAK,SAAS,CAAC,GAAG,CAAC;AACrD,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,MAAI,sBAAsBA,OAAM,KAAK,GAAG;AACtC,UAAMC,QAAO,MAAM,MAAM,eAAe;AACxC,UAAMC,WAAU,MAAM,MAAM,UAAU;AACtC,UAAMC,SAAQ,MAAM,kBAAkBH,OAAM;AAAA,MAC1C,GAAG,QAAQ,QAAQ;AAAA,MACnB,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AACD,IAAAE,SAAQ;AACR,IAAAD,MAAK;AAEL,WACEE,SACA,QACC,SAAS,IAAI,MAAM,KAAK;AAAA;AAAA,MAEvBA,OAAM;AAAA;AAAA,OAGH,KAAK,IAAIA,OAAM,YAAY,IAAI,GAAGA,OAAM,YAAY,IAAI,CAAC,IAAI;AAAA,IAClE;AAAA,EAEJ;AAEA,QAAM,WAAW,IAAI,OAAO,IAAI;AAChC,QAAM,OAAO,MAAM,MAAM,YAAY;AACrC,QAAM,UAAU,MAAM,MAAM,UAAU;AAMtC,UAAQ,KAAK,WAAW,GAAG;AAE3B,MAAI,QAAQ,MAAM,kBAAkBH,OAAM;AAAA,IACxC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AAED,MAAI,SAAS,KAAK,KAAK,GAAG;AAExB,YAAQ,yBAAyB,MAAM,WAAW,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,EACvE;AAEA,UAAQ,QAAQ,WAAW,MAAM,QAAQ;AAEzC,MAAI,MAAM,QAAQ,UAAU;AAC1B,aAAS,MAAM;AAAA,EACjB;AAEA,UAAQ;AACR,OAAK;AAEL,SAAO;AACT;;;ACtEA,KAAK,OAAO;AAML,SAAS,KAAKI,OAAM;AACzB,SAAOA,MAAK,SAAS;AACvB;AAKA,SAAS,WAAW;AAClB,SAAO;AACT;;;ACZA,MAAM,OAAO;AASN,SAAS,MAAMC,OAAM,GAAG,OAAO,MAAM;AAC1C,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAM,OAAO,MAAM,MAAM,OAAO;AAChC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,WAAS,QAAQ;AAAA,IACf,MAAM,KAAKA,MAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,EACxE;AACA,WAAS,QAAQ,KAAK,IAAI;AAE1B,UAAQ;AAER;AAAA;AAAA,IAEG,CAACA,MAAK,OAAOA,MAAK;AAAA,IAEnB,eAAe,KAAKA,MAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAOA,MAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAIA,MAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,GAAG;AACzB,OAAK;AAEL,SAAO;AACT;AAKA,SAAS,YAAY;AACnB,SAAO;AACT;;;AC5EA,eAAe,OAAO;AASf,SAAS,eAAeC,OAAM,GAAG,OAAO,MAAM;AACnD,QAAM,OAAOA,MAAK;AAClB,QAAM,OAAO,MAAM,MAAM,gBAAgB;AACzC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,QAAM,MAAM,MAAM,KAAKA,MAAK,KAAK;AAAA,IAC/B,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,WAAS,QAAQ,KAAK,MAAM,IAAI;AAEhC,UAAQ;AAER,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAC;AACf,YAAU,MAAM,MAAM,WAAW;AAKjC,QAAM,YAAY,MAAM,KAAK,MAAM,cAAcA,KAAI,GAAG;AAAA,IACtD,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,UAAQ;AACR,QAAM,QAAQ;AACd,OAAK;AAEL,MAAI,SAAS,UAAU,CAAC,OAAO,QAAQ,WAAW;AAChD,aAAS,QAAQ,KAAK,YAAY,GAAG;AAAA,EACvC,WAAW,SAAS,YAAY;AAE9B,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC3B,OAAO;AACL,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B;AAEA,SAAO;AACT;AAKA,SAAS,qBAAqB;AAC5B,SAAO;AACT;;;ACzDA,WAAW,OAAO;AAQX,SAAS,WAAWC,OAAM,GAAG,OAAO;AACzC,MAAI,QAAQA,MAAK,SAAS;AAC1B,MAAI,WAAW;AACf,MAAI,QAAQ;AAKZ,SAAO,IAAI,OAAO,aAAa,WAAW,UAAU,EAAE,KAAK,KAAK,GAAG;AACjE,gBAAY;AAAA,EACd;AAIA,MACE,WAAW,KAAK,KAAK,MACnB,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAM,QAAQ,KAAK,KAAK,IACzE;AACA,YAAQ,MAAM,QAAQ;AAAA,EACxB;AASA,SAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AACpC,UAAM,UAAU,MAAM,OAAO,KAAK;AAClC,UAAM,aAAa,MAAM,eAAe,OAAO;AAE/C,QAAI;AAKJ,QAAI,CAAC,QAAQ,QAAS;AAEtB,WAAQ,QAAQ,WAAW,KAAK,KAAK,GAAI;AACvC,UAAI,WAAW,MAAM;AAGrB,UACE,MAAM,WAAW,QAAQ,MAAM,MAC/B,MAAM,WAAW,WAAW,CAAC,MAAM,IACnC;AACA;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM,GAAG,QAAQ,IAAI,MAAM,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,SAAO,WAAW,QAAQ;AAC5B;AAKA,SAAS,iBAAiB;AACxB,SAAO;AACT;;;AC/DO,SAAS,qBAAqBC,OAAM,OAAO;AAChD,QAAM,MAAM,SAASA,KAAI;AAEzB,SAAO;AAAA,IACL,CAAC,MAAM,QAAQ;AAAA,IAEbA,MAAK;AAAA,IAEL,CAACA,MAAK;AAAA,IAENA,MAAK,YACLA,MAAK,SAAS,WAAW,KACzBA,MAAK,SAAS,CAAC,EAAE,SAAS;AAAA,KAEzB,QAAQA,MAAK,OAAO,YAAY,QAAQA,MAAK;AAAA,IAE9C,oBAAoB,KAAKA,MAAK,GAAG;AAAA;AAAA,IAGjC,CAAC,iBAAiB,KAAKA,MAAK,GAAG;AAAA,EACnC;AACF;;;ACxBA,KAAK,OAAO;AASL,SAAS,KAAKC,OAAM,GAAG,OAAO,MAAM;AACzC,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,MAAI;AAEJ,MAAI;AAEJ,MAAI,qBAAqBA,OAAM,KAAK,GAAG;AAErC,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,CAAC;AACf,WAAO,MAAM,MAAM,UAAU;AAC7B,QAAIC,SAAQ,QAAQ,KAAK,GAAG;AAC5B,IAAAA,UAAS,QAAQ;AAAA,MACf,MAAM,kBAAkBD,OAAM;AAAA,QAC5B,QAAQC;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,IAAAA,UAAS,QAAQ,KAAK,GAAG;AACzB,SAAK;AACL,UAAM,QAAQ;AACd,WAAOA;AAAA,EACT;AAEA,SAAO,MAAM,MAAM,MAAM;AACzB,YAAU,MAAM,MAAM,OAAO;AAC7B,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,WAAS,QAAQ;AAAA,IACf,MAAM,kBAAkBD,OAAM;AAAA,MAC5B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,KAAK,IAAI;AAC1B,UAAQ;AAER;AAAA;AAAA,IAEG,CAACA,MAAK,OAAOA,MAAK;AAAA,IAEnB,eAAe,KAAKA,MAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAOA,MAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAIA,MAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAKA,MAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,GAAG;AAEzB,OAAK;AACL,SAAO;AACT;AAQA,SAAS,SAASA,OAAM,GAAG,OAAO;AAChC,SAAO,qBAAqBA,OAAM,KAAK,IAAI,MAAM;AACnD;;;AC5GA,cAAc,OAAO;AASd,SAAS,cAAcE,OAAM,GAAG,OAAO,MAAM;AAClD,QAAM,OAAOA,MAAK;AAClB,QAAM,OAAO,MAAM,MAAM,eAAe;AACxC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,QAAMC,QAAO,MAAM,kBAAkBD,OAAM;AAAA,IACzC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,WAAS,QAAQ,KAAKC,QAAO,IAAI;AAEjC,UAAQ;AAER,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAC;AACf,YAAU,MAAM,MAAM,WAAW;AAKjC,QAAM,YAAY,MAAM,KAAK,MAAM,cAAcD,KAAI,GAAG;AAAA,IACtD,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,UAAQ;AACR,QAAM,QAAQ;AACd,OAAK;AAEL,MAAI,SAAS,UAAU,CAACC,SAAQA,UAAS,WAAW;AAClD,aAAS,QAAQ,KAAK,YAAY,GAAG;AAAA,EACvC,WAAW,SAAS,YAAY;AAE9B,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC3B,OAAO;AACL,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B;AAEA,SAAO;AACT;AAKA,SAAS,oBAAoB;AAC3B,SAAO;AACT;;;ACtDO,SAAS,YAAY,OAAO;AACjC,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,MAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;AACtD,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACVO,SAAS,iBAAiB,OAAO;AACtC,QAAM,SAAS,YAAY,KAAK;AAChC,QAAM,cAAc,MAAM,QAAQ;AAElC,MAAI,CAAC,aAAa;AAChB,WAAO,WAAW,MAAM,MAAM;AAAA,EAChC;AAEA,MAAI,gBAAgB,OAAO,gBAAgB,OAAO,gBAAgB,KAAK;AACrE,UAAM,IAAI;AAAA,MACR,kCACE,cACA;AAAA,IACJ;AAAA,EACF;AAEA,MAAI,gBAAgB,QAAQ;AAC1B,UAAM,IAAI;AAAA,MACR,yBACE,SACA,4BACA,cACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;AC7BO,SAAS,mBAAmB,OAAO;AACxC,QAAM,SAAS,MAAM,QAAQ,iBAAiB;AAE9C,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACZO,SAAS,UAAU,OAAO;AAC/B,QAAM,SAAS,MAAM,QAAQ,QAAQ;AAErC,MAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;AACtD,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACHO,SAASC,MAAKC,OAAM,QAAQ,OAAO,MAAM;AAC9C,QAAM,OAAO,MAAM,MAAM,MAAM;AAC/B,QAAM,gBAAgB,MAAM;AAE5B,MAAI,SAASA,MAAK,UAAU,mBAAmB,KAAK,IAAI,YAAY,KAAK;AAEzE,QAAM,cAAcA,MAAK,UACrB,WAAW,MACT,MACA,MACF,iBAAiB,KAAK;AAC1B,MAAI,qBACF,UAAU,MAAM,iBAAiB,WAAW,MAAM,iBAAiB;AAErE,MAAI,CAACA,MAAK,SAAS;AACjB,UAAM,gBAAgBA,MAAK,WAAWA,MAAK,SAAS,CAAC,IAAI;AAUzD;AAAA;AAAA,OAEG,WAAW,OAAO,WAAW;AAAA,MAE9B,kBACC,CAAC,cAAc,YAAY,CAAC,cAAc,SAAS,CAAC;AAAA,MAErD,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,UACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,cACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,UACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM;AAAA,MAExC,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM,KAClD,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM,KAClD,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM;AAAA,MAClD;AACA,2BAAqB;AAAA,IACvB;AAUA,QAAI,UAAU,KAAK,MAAM,UAAU,eAAe;AAChD,UAAI,QAAQ;AAEZ,aAAO,EAAE,QAAQA,MAAK,SAAS,QAAQ;AACrC,cAAM,OAAOA,MAAK,SAAS,KAAK;AAEhC,YACE,QACA,KAAK,SAAS,cACd,KAAK,YACL,KAAK,SAAS,CAAC,KACf,KAAK,SAAS,CAAC,EAAE,SAAS,iBAC1B;AACA,+BAAqB;AACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,oBAAoB;AACtB,aAAS;AAAA,EACX;AAEA,QAAM,gBAAgB;AACtB,QAAM,QAAQ,MAAM,cAAcA,OAAM,IAAI;AAC5C,QAAM,iBAAiB;AACvB,QAAM,gBAAgB;AACtB,OAAK;AACL,SAAO;AACT;;;AC3FO,SAAS,oBAAoB,OAAO;AACzC,QAAM,QAAQ,MAAM,QAAQ,kBAAkB;AAE9C,MAAI,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS;AAC3D,UAAM,IAAI;AAAA,MACR,kCACE,QACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACLO,SAAS,SAASC,OAAM,QAAQ,OAAO,MAAM;AAClD,QAAM,iBAAiB,oBAAoB,KAAK;AAChD,MAAI,SAAS,MAAM,iBAAiB,YAAY,KAAK;AAGrD,MAAI,UAAU,OAAO,SAAS,UAAU,OAAO,SAAS;AACtD,cACG,OAAO,OAAO,UAAU,YAAY,OAAO,QAAQ,KAChD,OAAO,QACP,MACH,MAAM,QAAQ,wBAAwB,QACnC,IACA,OAAO,SAAS,QAAQA,KAAI,KAChC;AAAA,EACJ;AAEA,MAAI,OAAO,OAAO,SAAS;AAE3B,MACE,mBAAmB,SAClB,mBAAmB,YAChB,UAAU,OAAO,SAAS,UAAU,OAAO,UAAWA,MAAK,SAC/D;AACA,WAAO,KAAK,KAAK,OAAO,CAAC,IAAI;AAAA,EAC/B;AAEA,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAQ,KAAK,SAAS,IAAI,OAAO,OAAO,OAAO,MAAM,CAAC;AACtD,UAAQ,MAAM,IAAI;AAClB,QAAM,OAAO,MAAM,MAAM,UAAU;AACnC,QAAM,QAAQ,MAAM;AAAA,IAClB,MAAM,cAAcA,OAAM,QAAQ,QAAQ,CAAC;AAAA,IAC3CC;AAAA,EACF;AACA,OAAK;AAEL,SAAO;AAGP,WAASA,KAAI,MAAM,OAAO,OAAO;AAC/B,QAAI,OAAO;AACT,cAAQ,QAAQ,KAAK,IAAI,OAAO,IAAI,KAAK;AAAA,IAC3C;AAEA,YAAQ,QAAQ,SAAS,SAAS,IAAI,OAAO,OAAO,OAAO,MAAM,KAAK;AAAA,EACxE;AACF;;;ACjDO,SAAS,UAAUC,OAAM,GAAG,OAAO,MAAM;AAC9C,QAAM,OAAO,MAAM,MAAM,WAAW;AACpC,QAAM,UAAU,MAAM,MAAM,UAAU;AACtC,QAAM,QAAQ,MAAM,kBAAkBA,OAAM,IAAI;AAChD,UAAQ;AACR,OAAK;AACL,SAAO;AACT;;;ACLO,SAAS,KAAKC,OAAM,GAAG,OAAO,MAAM;AAEzC,QAAM,cAAcA,MAAK,SAAS,KAAK,SAAU,GAAG;AAClD,WAAO,SAAS,CAAC;AAAA,EACnB,CAAC;AAED,QAAM,YAAY,cAAc,MAAM,oBAAoB,MAAM;AAChE,SAAO,UAAU,KAAK,OAAOA,OAAM,IAAI;AACzC;;;ACdO,SAAS,YAAY,OAAO;AACjC,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,mCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACXA,OAAO,OAAO;AASP,SAAS,OAAOC,OAAM,GAAG,OAAO,MAAM;AAC3C,QAAM,SAAS,YAAY,KAAK;AAChC,QAAM,OAAO,MAAM,MAAM,QAAQ;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,SAAS,QAAQ,KAAK,SAAS,MAAM;AAE3C,MAAIC,WAAU,QAAQ;AAAA,IACpB,MAAM,kBAAkBD,OAAM;AAAA,MAC5B,OAAO;AAAA,MACP;AAAA,MACA,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,cAAcC,SAAQ,WAAW,CAAC;AACxC,QAAM,OAAO;AAAA,IACX,KAAK,OAAO,WAAW,KAAK,OAAO,SAAS,CAAC;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AAEA,MAAI,KAAK,QAAQ;AACf,IAAAA,WAAU,yBAAyB,WAAW,IAAIA,SAAQ,MAAM,CAAC;AAAA,EACnE;AAEA,QAAM,cAAcA,SAAQ,WAAWA,SAAQ,SAAS,CAAC;AACzD,QAAM,QAAQ,WAAW,KAAK,MAAM,WAAW,CAAC,GAAG,aAAa,MAAM;AAEtE,MAAI,MAAM,QAAQ;AAChB,IAAAA,WAAUA,SAAQ,MAAM,GAAG,EAAE,IAAI,yBAAyB,WAAW;AAAA,EACvE;AAEA,QAAM,QAAQ,QAAQ,KAAK,SAAS,MAAM;AAE1C,OAAK;AAEL,QAAM,iCAAiC;AAAA,IACrC,OAAO,MAAM;AAAA,IACb,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,SAASA,WAAU;AAC5B;AAQA,SAAS,WAAW,GAAG,IAAI,OAAO;AAChC,SAAO,MAAM,QAAQ,UAAU;AACjC;;;ACxDO,SAAS,KAAKC,OAAM,GAAG,OAAO,MAAM;AACzC,SAAO,MAAM,KAAKA,MAAK,OAAO,IAAI;AACpC;;;ACNO,SAAS,oBAAoB,OAAO;AACzC,QAAM,aAAa,MAAM,QAAQ,kBAAkB;AAEnD,MAAI,aAAa,GAAG;AAClB,UAAM,IAAI;AAAA,MACR,6CACE,aACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACNO,SAAS,cAAc,GAAG,IAAI,OAAO;AAC1C,QAAM,SACJ,UAAU,KAAK,KAAK,MAAM,QAAQ,aAAa,MAAM,KACrD,OAAO,oBAAoB,KAAK,CAAC;AAEnC,SAAO,MAAM,QAAQ,aAAa,MAAM,MAAM,GAAG,EAAE,IAAI;AACzD;;;ACGO,IAAM,SAAS;AAAA,EACpB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACpCO,IAAM,OAAO,CAAC,YAAY;AAGjC,SAAS,aAAa,MAAM,OAAO,QAAQ,OAAO;AAEhD,MACE,MAAM,SAAS,UACf,qBAAqB,OAAO,KAAK,MAChC,KAAK,SAAS,UACZ,KAAK,SAAS,MAAM,QAAQ,qBAAqB,MAAM,KAAK,IAC/D;AACA,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,UAAU,OAAO,OAAO,WAAW,WAAW;AAC5D,QACE,KAAK,SAAS;AAAA,KAEb,KAAK,SAAS,MAAM,QACnB,MAAM,SAAS;AAAA,IAEd,MAAM,SAAS,aAAa,sBAAsB,OAAO,KAAK,IACjE;AACA;AAAA,IACF;AAEA,WAAO,OAAO,SAAS,IAAI;AAAA,EAC7B;AACF;;;AC1BA,IAAM,oBAAoB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,IAAM,SAAS;AAAA,EACpB,EAAC,WAAW,KAAM,OAAO,YAAY,aAAa,WAAU;AAAA,EAC5D,EAAC,WAAW,KAAM,QAAQ,YAAY,aAAa,WAAU;AAAA,EAC7D;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,6BAA6B,qBAAqB;AAAA,EAClE;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,EAAC,WAAW,KAAK,OAAO,YAAY,aAAa,WAAU;AAAA,EAC3D,EAAC,WAAW,KAAK,QAAQ,YAAY,aAAa,WAAU;AAAA,EAC5D;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,6BAA6B,qBAAqB;AAAA,EAClE;AAAA;AAAA;AAAA,EAGA;AAAA,IACE,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AAAA;AAAA,EAEA,EAAC,WAAW,KAAK,aAAa,aAAY;AAAA;AAAA,EAE1C,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA,EAC9B,EAAC,WAAW,KAAK,aAAa,cAAc,OAAO,eAAc;AAAA;AAAA;AAAA,EAGjE,EAAC,WAAW,KAAK,OAAO,aAAa,aAAa,WAAU;AAAA;AAAA,EAE5D,EAAC,WAAW,KAAK,aAAa,kBAAiB;AAAA;AAAA,EAE/C,EAAC,WAAW,KAAK,aAAa,iBAAgB;AAAA;AAAA,EAE9C;AAAA,IACE,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AAAA;AAAA;AAAA,EAGA,EAAC,SAAS,MAAM,QAAQ,QAAQ,WAAW,IAAG;AAAA,EAC9C,EAAC,WAAW,KAAK,aAAa,iBAAgB;AAAA;AAAA,EAE9C,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,gBAAgB;AAAA,EACvD,EAAC,WAAW,KAAK,aAAa,YAAY,gBAAgB,kBAAiB;AAAA;AAAA,EAE3E,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,eAAe;AAAA;AAAA;AAAA,EAGtD,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,gBAAgB;AAAA;AAAA,EAEvD,EAAC,SAAS,MAAM,QAAQ,QAAQ,WAAW,KAAK,OAAO,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxE,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,cAAa;AAAA,EACpD;AAAA,IACE,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AAAA,EACA,EAAC,WAAW,KAAK,aAAa,qBAAoB;AAAA;AAAA,EAElD,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA;AAAA;AAAA,EAG9B,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA,EAC9B,EAAC,WAAW,KAAK,aAAa,qBAAoB;AAAA;AAAA;AAAA,EAGlD,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA,EAC9B,EAAC,WAAW,KAAK,aAAa,YAAY,gBAAgB,kBAAiB;AAAA,EAC3E,EAAC,WAAW,KAAK,aAAa,CAAC,SAAS,WAAW,EAAC;AAAA;AAAA;AAAA;AAAA,EAIpD,EAAC,WAAW,MAAM,OAAO,YAAY,aAAa,WAAU;AAAA;AAAA,EAE5D,EAAC,WAAW,KAAK,aAAa,CAAC,SAAS,WAAW,EAAC;AAAA;AAAA;AAAA,EAGpD,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA,EAC9B,EAAC,WAAW,KAAK,aAAa,YAAY,gBAAgB,kBAAiB;AAAA;AAAA;AAAA,EAG3E,EAAC,SAAS,MAAM,WAAW,IAAG;AAAA,EAC9B;AAAA,IACE,WAAW;AAAA,IACX,aAAa,CAAC,6BAA6B,2BAA2B;AAAA,EACxE;AAAA,EACA,EAAC,WAAW,KAAK,aAAa,YAAY,gBAAgB,kBAAiB;AAAA;AAAA;AAAA;AAAA,EAI3E,EAAC,SAAS,MAAM,WAAW,IAAG;AAChC;;;AC/HO,IAAM;AAAA;AAAA,EAA8B;AAAA,IACzC,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA;AAAA,IACjB,eAAe;AAAA;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,iBAAiB;AAAA;AAAA,IACjB,kBAAkB;AAAA;AAAA,IAClB,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,KAAK;AAAA;AAAA,IACL,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,mBAAmB;AAAA;AAAA,IACnB,WAAW;AAAA;AAAA,IACX,oBAAoB;AAAA;AAAA,IACpB,OAAO;AAAA;AAAA,IACP,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,OAAO;AAAA;AAAA,IACP,KAAK;AAAA;AAAA,IAEL,iBAAiB;AAAA;AAAA,IAEjB,sBAAsB;AAAA;AAAA,EACxB;AAAA;;;ACrJO,IAAM;AAAA;AAAA,EAAkC;AAAA,IAC7C,oBAAoB;AAAA;AAAA,IACpB,qBAAqB;AAAA;AAAA,IACrB,+BAA+B;AAAA;AAAA,IAC/B,uBAAuB;AAAA;AAAA,IACvB,uBAAuB;AAAA;AAAA,IACvB,oBAAoB;AAAA;AAAA,IACpB,2BAA2B;AAAA;AAAA,IAC3B,0BAA0B;AAAA;AAAA,IAC1B,kCAAkC;AAAA;AAAA,IAClC,sCAAsC;AAAA;AAAA,IACtC,gCAAgC;AAAA;AAAA,IAChC,2BAA2B;AAAA;AAAA,IAC3B,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA;AAAA,IACxB,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,iBAAiB;AAAA;AAAA,IACjB,iBAAiB;AAAA;AAAA,IACjB,gBAAgB;AAAA;AAAA,IAChB,SAAS;AAAA;AAAA,IACT,mCAAmC;AAAA;AAAA,IACnC,sBAAsB;AAAA;AAAA,IACtB,sBAAsB;AAAA;AAAA,IACtB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,SAAS;AAAA;AAAA,IACT,6BAA6B;AAAA;AAAA,IAC7B,oBAAoB;AAAA;AAAA,EACtB;AAAA;;;ACnCO,IAAM;AAAA;AAAA,EAA+B;AAAA,IAC1C,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA;;;AC5FO,SAAS,gCAAgC,OAAO,MAAM;AAC3D,QAAMC,QAAO,OAAO,SAAS,OAAO,IAAI;AAExC;AAAA;AAAA,IAEEA,QAAO,MAAM,MACbA,UAAS,MAAM,MACdA,QAAO,MAAM,MAAMA,QAAO,MAAM;AAAA,IAEhCA,QAAO,MAAM,SAASA,QAAO;AAAA,IAE7BA,QAAO,SAAUA,QAAO;AAAA,IAExBA,QAAO,SAAUA,QAAO;AAAA,KAExBA,QAAO,WAAY,UACnBA,QAAO,WAAY;AAAA;AAAA,IAGpBA,QAAO;AAAA,IACP;AACA,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO,OAAO,cAAcA,KAAI;AAClC;;;ACrCA,IAAM,6BACJ;AAcK,SAAS,aAAa,OAAO;AAClC,SAAO,MAAM,QAAQ,4BAA4B,MAAM;AACzD;AAYA,SAAS,OAAO,IAAI,IAAI,IAAI;AAC1B,MAAI,IAAI;AAEN,WAAO;AAAA,EACT;AAGA,QAAM,OAAO,GAAG,WAAW,CAAC;AAE5B,MAAI,SAAS,MAAM,YAAY;AAC7B,UAAMC,QAAO,GAAG,WAAW,CAAC;AAC5B,UAAM,MAAMA,UAAS,MAAM,cAAcA,UAAS,MAAM;AACxD,WAAO;AAAA,MACL,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,MACpB,MAAM,UAAU,yBAAyB,UAAU;AAAA,IACrD;AAAA,EACF;AAEA,SAAO,8BAA8B,EAAE,KAAK;AAC9C;;;AC1BO,SAAS,YAAYC,OAAM;AAChC,MAAIA,MAAK,SAAS,CAACA,MAAK,YAAY;AAClC,WAAOA,MAAK,SAAS;AAAA,EACvB;AAEA,SAAO,aAAaA,MAAK,UAAU;AACrC;;;ACzBO,SAAS,eAAe,SAAS;AACtC,MAAI,CAAC,QAAQ,WAAW;AACtB,UAAM,UACH,QAAQ,UAAU,oBAAoB,OACtC,QAAQ,SAAS,QAAQ,QAAQ,SAAS,MAAM;AAEnD,YAAQ,YAAY,IAAI;AAAA,OACrB,SAAS,MAAM,SAAS,MAAM,OAC5B,sBAAsB,KAAK,QAAQ,SAAS,IAAI,OAAO,MACxD,QAAQ,aACP,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,SAAO,QAAQ;AACjB;;;ACFO,SAAS,kBAAkB,QAAQ,OAAO,MAAM;AACrD,QAAM,aAAa,MAAM;AACzB,QAAM,WAAW,OAAO,YAAY,CAAC;AAErC,QAAM,UAAU,CAAC;AACjB,MAAI,QAAQ;AACZ,MAAI,SAAS,KAAK;AAElB,MAAI;AAEJ,aAAW,KAAK,EAAE;AAClB,MAAI,UAAU,MAAM,cAAc,IAAI;AAEtC,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,UAAM,QAAQ,SAAS,KAAK;AAE5B,QAAI;AAEJ,eAAW,WAAW,SAAS,CAAC,IAAI;AAEpC,QAAI,QAAQ,IAAI,SAAS,QAAQ;AAG/B,UAAIC,UAAS,MAAM,OAAO,SAAS,SAAS,QAAQ,CAAC,EAAE,IAAI;AAG3D,UAAIA,WAAUA,QAAO,KAAM,CAAAA,UAASA,QAAO;AAC3C,cAAQA,UACJA,QAAO,SAAS,QAAQ,CAAC,GAAG,QAAQ,OAAO;AAAA,QACzC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC,EAAE,OAAO,CAAC,IACX;AAAA,IACN,OAAO;AACL,cAAQ,KAAK;AAAA,IACf;AAQA,QACE,QAAQ,SAAS,MAChB,WAAW,QAAQ,WAAW,SAC/B,MAAM,SAAS,QACf;AACA,cAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAAA,QACxD;AAAA,QACA;AAAA,MACF;AACA,eAAS;AAGT,gBAAU,MAAM,cAAc,IAAI;AAClC,cAAQ,KAAK,QAAQ,KAAK,EAAE,CAAC;AAAA,IAC/B;AAEA,QAAI,QAAQ,MAAM,OAAO,OAAO,QAAQ,OAAO;AAAA,MAC7C,GAAG,QAAQ,QAAQ;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC;AAKD,QAAI,eAAe,gBAAgB,MAAM,MAAM,GAAG,CAAC,GAAG;AACpD,cACE,yBAAyB,YAAY,WAAW,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,IACvE;AAEA,UAAM,eAAe,MAAM;AAC3B,UAAM,iCAAiC;AACvC,kBAAc;AAKd,QAAI,cAAc;AAChB,UACE,QAAQ,SAAS,KACjB,aAAa,UACb,WAAW,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,EAAE,GAC/C;AACA,gBAAQ,QAAQ,SAAS,CAAC,IACxB,QAAQ,QAAQ,SAAS,CAAC,EAAE,MAAM,GAAG,EAAE,IACvC,yBAAyB,OAAO,WAAW,CAAC,CAAC;AAAA,MACjD;AAEA,UAAI,aAAa,MAAO,eAAc;AAAA,IACxC;AAEA,YAAQ,KAAK,KAAK;AAClB,YAAQ,KAAK,KAAK;AAClB,aAAS,MAAM,MAAM,EAAE;AAAA,EACzB;AAEA,aAAW,IAAI;AAEf,SAAO,QAAQ,KAAK,EAAE;AACxB;;;AC7GO,SAAS,cAAc,QAAQ,OAAO,MAAM;AACjD,QAAM,aAAa,MAAM;AACzB,QAAM,WAAW,OAAO,YAAY,CAAC;AACrC,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,QAAM,UAAU,CAAC;AACjB,MAAI,QAAQ;AAEZ,aAAW,KAAK,EAAE;AAElB,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,UAAM,QAAQ,SAAS,KAAK;AAE5B,eAAW,WAAW,SAAS,CAAC,IAAI;AAEpC,YAAQ;AAAA,MACN,QAAQ;AAAA,QACN,MAAM,OAAO,OAAO,QAAQ,OAAO;AAAA,UACjC,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,GAAG,QAAQ,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,QAAQ;AACzB,YAAM,iBAAiB;AAAA,IACzB;AAEA,QAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B,cAAQ;AAAA,QACN,QAAQ,KAAK,QAAQ,OAAO,SAAS,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAEA,aAAW,IAAI;AAEf,SAAO,QAAQ,KAAK,EAAE;AACxB;AASA,SAAS,QAAQ,MAAM,OAAO,QAAQ,OAAO;AAC3C,MAAI,QAAQ,MAAM,KAAK;AAEvB,SAAO,SAAS;AACd,UAAM,SAAS,MAAM,KAAK,KAAK,EAAE,MAAM,OAAO,QAAQ,KAAK;AAE3D,QAAI,WAAW,QAAQ,WAAW,GAAG;AACnC;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,UAAU;AAC9B,aAAO,KAAK,OAAO,IAAI,MAAM;AAAA,IAC/B;AAEA,QAAI,WAAW,OAAO;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AC/EA,IAAM,MAAM;AAKL,SAAS,YAAY,OAAOC,MAAK;AAEtC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAQ,QAAQ,IAAI,KAAK,KAAK,GAAI;AAChC,IAAAC,KAAI,MAAM,MAAM,OAAO,MAAM,KAAK,CAAC;AACnC,WAAO,KAAK,MAAM,CAAC,CAAC;AACpB,YAAQ,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC/B;AAAA,EACF;AAEA,EAAAA,KAAI,MAAM,MAAM,KAAK,CAAC;AAEtB,SAAO,OAAO,KAAK,EAAE;AAKrB,WAASA,KAAIC,QAAO;AAClB,WAAO,KAAKF,KAAIE,QAAO,MAAM,CAACA,MAAK,CAAC;AAAA,EACtC;AACF;;;ACFO,SAAS,KAAK,OAAO,OAAO,QAAQ;AACzC,QAAM,SAAS,OAAO,UAAU,OAAO,SAAS,OAAO,OAAO,SAAS;AAEvE,QAAM,YAAY,CAAC;AAEnB,QAAM,SAAS,CAAC;AAEhB,QAAM,QAAQ,CAAC;AACf,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AACpC,UAAM,UAAU,MAAM,OAAO,KAAK;AAElC,QAAI,CAAC,eAAe,MAAM,OAAO,OAAO,GAAG;AACzC;AAAA,IACF;AAEA,UAAM,aAAa,MAAM,eAAe,OAAO;AAE/C,QAAI;AAEJ,WAAQ,QAAQ,WAAW,KAAK,KAAK,GAAI;AACvC,YAAM,SAAS,YAAY,WAAW,QAAQ,QAAQ,OAAO;AAC7D,YAAM,QAAQ,WAAW;AACzB,YAAM,WAAW,MAAM,SAAS,SAAS,MAAM,CAAC,EAAE,SAAS;AAE3D,UAAI,UAAU,SAAS,QAAQ,GAAG;AAChC,YAAI,MAAM,QAAQ,EAAE,UAAU,CAAC,QAAQ;AACrC,gBAAM,QAAQ,EAAE,SAAS;AAAA,QAC3B;AAEA,YAAI,MAAM,QAAQ,EAAE,SAAS,CAAC,OAAO;AACnC,gBAAM,QAAQ,EAAE,QAAQ;AAAA,QAC1B;AAAA,MACF,OAAO;AACL,kBAAU,KAAK,QAAQ;AACvB,cAAM,QAAQ,IAAI,EAAC,QAAQ,MAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAEA,YAAU,KAAK,SAAS;AAExB,MAAI,QAAQ,OAAO,SAAS,OAAO,OAAO,SAAS;AACnD,QAAM,MAAM,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,SAAS;AACjE,UAAQ;AAER,SAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,UAAM,WAAW,UAAU,KAAK;AAGhC,QAAI,WAAW,SAAS,YAAY,KAAK;AACvC;AAAA,IACF;AAKA,QACG,WAAW,IAAI,OACd,UAAU,QAAQ,CAAC,MAAM,WAAW,KACpC,MAAM,QAAQ,EAAE,SAChB,CAAC,MAAM,WAAW,CAAC,EAAE,UACrB,CAAC,MAAM,WAAW,CAAC,EAAE,SACtB,UAAU,QAAQ,CAAC,MAAM,WAAW,KACnC,MAAM,QAAQ,EAAE,UAChB,CAAC,MAAM,WAAW,CAAC,EAAE,UACrB,CAAC,MAAM,WAAW,CAAC,EAAE,OACvB;AACA;AAAA,IACF;AAEA,QAAI,UAAU,UAAU;AAItB,aAAO,KAAK,kBAAkB,MAAM,MAAM,OAAO,QAAQ,GAAG,IAAI,CAAC;AAAA,IACnE;AAEA,YAAQ;AAER,QACE,iBAAiB,KAAK,MAAM,OAAO,QAAQ,CAAC,MAC3C,CAAC,OAAO,UAAU,CAAC,OAAO,OAAO,SAAS,MAAM,OAAO,QAAQ,CAAC,IACjE;AAEA,aAAO,KAAK,IAAI;AAAA,IAClB,OAAO;AAEL,aAAO,KAAK,yBAAyB,MAAM,WAAW,QAAQ,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK,kBAAkB,MAAM,MAAM,OAAO,GAAG,GAAG,OAAO,KAAK,CAAC;AAEpE,SAAO,OAAO,KAAK,EAAE;AACvB;AAOA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI;AACb;AAOA,SAAS,kBAAkB,OAAO,OAAO;AACvC,QAAM,aAAa;AAEnB,QAAM,YAAY,CAAC;AAEnB,QAAM,UAAU,CAAC;AACjB,QAAM,QAAQ,QAAQ;AACtB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AAEZ,MAAI;AAEJ,SAAQ,QAAQ,WAAW,KAAK,KAAK,GAAI;AACvC,cAAU,KAAK,MAAM,KAAK;AAAA,EAC5B;AAEA,SAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,QAAI,UAAU,UAAU,KAAK,GAAG;AAC9B,cAAQ,KAAK,MAAM,MAAM,OAAO,UAAU,KAAK,CAAC,CAAC;AAAA,IACnD;AAEA,YAAQ,KAAK,IAAI;AACjB,YAAQ,UAAU,KAAK;AAAA,EACzB;AAEA,UAAQ,KAAK,MAAM,MAAM,KAAK,CAAC;AAE/B,SAAO,QAAQ,KAAK,EAAE;AACxB;;;ACpKO,SAAS,MAAM,QAAQ;AAI5B,QAAM,UAAU,UAAU,CAAC;AAC3B,QAAM,MAAM,QAAQ,OAAO,CAAC;AAC5B,MAAI,YAAY,QAAQ,aAAa;AACrC,MAAI,OAAO,IAAI,QAAQ;AACvB,MAAI,SAAS,IAAI,UAAU;AAE3B,SAAO,EAAC,MAAM,SAAS,MAAK;AAO5B,WAAS,UAAU;AACjB,WAAO,EAAC,KAAK,EAAC,MAAM,OAAM,GAAG,UAAS;AAAA,EACxC;AAOA,WAAS,MAAM,OAAO;AACpB,iBAAa;AAAA,EACf;AAOA,WAAS,KAAK,OAAO;AAEnB,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,MAAM,MAAM,WAAW;AACtC,UAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,YAAQ,OAAO,SAAS;AACxB,aACE,OAAO,WAAW,IAAI,SAAS,KAAK,SAAS,IAAI,KAAK,SAAS;AACjE,WAAO;AAAA,EACT;AACF;;;ACzBO,SAAS,WAAW,MAAM,SAAS;AACxC,QAAM,WAAW,WAAW,CAAC;AAE7B,QAAM,QAAQ;AAAA,IACZ,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,eAAe;AAAA,IACf;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,UAAU,EAAC,GAAG,OAAQ;AAAA;AAAA,IAEtB,QAAQ;AAAA,IACR;AAAA,IACA,YAAY,CAAC;AAAA,IACb,MAAM,CAAC,GAAG,IAAI;AAAA,IACd,SAAS,CAAC;AAAA,IACV,MAAM;AAAA,IACN,OAAO,CAAC;AAAA,IACR,QAAQ,CAAC,GAAG,MAAM;AAAA,EACpB;AAEA,YAAU,OAAO,QAAQ;AAEzB,MAAI,MAAM,QAAQ,kBAAkB;AAClC,UAAM,KAAK,KAAK,cAAc;AAAA,EAChC;AAEA,QAAM,SAAS,OAAO,QAAQ;AAAA,IAC5B;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,EAClB,CAAC;AAED,MAAI,SAAS,MAAM,OAAO,MAAM,QAAW,OAAO;AAAA,IAChD,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,KAAK,EAAC,MAAM,GAAG,QAAQ,EAAC;AAAA,IACxB,WAAW;AAAA,EACb,CAAC;AAED,MACE,UACA,OAAO,WAAW,OAAO,SAAS,CAAC,MAAM,MACzC,OAAO,WAAW,OAAO,SAAS,CAAC,MAAM,IACzC;AACA,cAAU;AAAA,EACZ;AAEA,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,UAAM,MAAM,KAAK,IAAI;AACrB,WAAO;AAKP,aAAS,OAAO;AACd,YAAM,MAAM,IAAI;AAAA,IAClB;AAAA,EACF;AACF;AAMA,SAAS,QAAQ,OAAO;AACtB,QAAM,IAAI,MAAM,0BAA0B,QAAQ,kBAAkB;AACtE;AAMA,SAAS,QAAQ,OAAO;AAEtB,QAAMC;AAAA;AAAA,IAA6B;AAAA;AACnC,QAAM,IAAI,MAAM,iCAAiCA,MAAK,OAAO,GAAG;AAClE;AAGA,SAAS,eAAe,MAAM,OAAO;AAEnC,MAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,MAAM,MAAM;AAC1D,WAAO;AAAA,EACT;AACF;AAgBA,SAAS,uBAAuB,QAAQ,MAAM;AAC5C,SAAO,kBAAkB,QAAQ,MAAM,IAAI;AAC7C;AAiBA,SAAS,mBAAmB,QAAQ,MAAM;AACxC,SAAO,cAAc,QAAQ,MAAM,IAAI;AACzC;AA2BA,SAAS,UAAU,OAAO,QAAQ;AAChC,SAAO,KAAK,MAAM,OAAO,MAAM;AACjC;", "names": ["node", "map", "list", "node", "node", "exit", "value", "map", "node", "code", "node", "between", "values", "node", "node", "exit", "subexit", "value", "node", "node", "node", "node", "node", "node", "value", "node", "text", "list", "node", "node", "map", "node", "node", "node", "between", "node", "list", "code", "head", "node", "handle", "map", "one", "value", "node"]}
import {
  r
} from "./chunk-N6JVQRIS.js";
import {
  x
} from "./chunk-FUMRHDK6.js";
import {
  n
} from "./chunk-HFNY7CXG.js";
import {
  i
} from "./chunk-HUQ3BRN5.js";
import {
  b
} from "./chunk-WPCBYP5E.js";
import {
  u
} from "./chunk-XV35K7AU.js";
import {
  P,
  d,
  e,
  f,
  i as i3,
  y
} from "./chunk-B5JRZI7Q.js";
import "./chunk-D7JDSFLT.js";
import "./chunk-ERAQ5RRJ.js";
import {
  m as m2
} from "./chunk-SGBOHCGH.js";
import "./chunk-VJPZRQQL.js";
import {
  f as f2,
  g,
  i as i2
} from "./chunk-RBNUC7A3.js";
import {
  useCodeMirror
} from "./chunk-GXEHGJ3I.js";
import {
  s
} from "./chunk-FXOF7VZK.js";
import {
  useRoute,
  useRouter
} from "./chunk-EFYDGGJO.js";
import {
  F,
  formatJsonOrYamlString,
  httpStatusCodes,
  je,
  prettyPrintJson,
  s as s2
} from "./chunk-K3FRCNXE.js";
import {
  _
} from "./chunk-LNIR43HZ.js";
import {
  $,
  B3 as B,
  Fragment,
  S,
  Transition,
  c,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  isRef,
  m,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  openBlock,
  p,
  ref,
  renderList,
  renderSlot,
  toDisplayString,
  toRef,
  unref,
  useId,
  watch,
  withCtx,
  withModifiers
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-O3EO7ESF.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-WESWXL2S.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-2S2EUIOI.js";
import {
  __commonJS,
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/shell-quote/quote.js
var require_quote = __commonJS({
  "node_modules/shell-quote/quote.js"(exports, module) {
    "use strict";
    module.exports = function quote(xs) {
      return xs.map(function(s7) {
        if (s7 && typeof s7 === "object") {
          return s7.op.replace(/(.)/g, "\\$1");
        }
        if (/["\s]/.test(s7) && !/'/.test(s7)) {
          return "'" + s7.replace(/(['\\])/g, "\\$1") + "'";
        }
        if (/["'\s]/.test(s7)) {
          return '"' + s7.replace(/(["\\$`!])/g, "\\$1") + '"';
        }
        return String(s7).replace(/([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g, "$1\\$2");
      }).join(" ");
    };
  }
});

// node_modules/shell-quote/parse.js
var require_parse = __commonJS({
  "node_modules/shell-quote/parse.js"(exports, module) {
    "use strict";
    var CONTROL = "(?:" + [
      "\\|\\|",
      "\\&\\&",
      ";;",
      "\\|\\&",
      "\\<\\(",
      "\\<\\<\\<",
      ">>",
      ">\\&",
      "<\\&",
      "[&;()|<>]"
    ].join("|") + ")";
    var controlRE = new RegExp("^" + CONTROL + "$");
    var META = "|&;()<> \\t";
    var SINGLE_QUOTE = '"((\\\\"|[^"])*?)"';
    var DOUBLE_QUOTE = "'((\\\\'|[^'])*?)'";
    var hash = /^#$/;
    var SQ = "'";
    var DQ = '"';
    var DS = "$";
    var TOKEN = "";
    var mult = 4294967296;
    for (i6 = 0; i6 < 4; i6++) {
      TOKEN += (mult * Math.random()).toString(16);
    }
    var i6;
    var startsWithToken = new RegExp("^" + TOKEN);
    function matchAll(s7, r4) {
      var origIndex = r4.lastIndex;
      var matches = [];
      var matchObj;
      while (matchObj = r4.exec(s7)) {
        matches.push(matchObj);
        if (r4.lastIndex === matchObj.index) {
          r4.lastIndex += 1;
        }
      }
      r4.lastIndex = origIndex;
      return matches;
    }
    function getVar(env, pre, key) {
      var r4 = typeof env === "function" ? env(key) : env[key];
      if (typeof r4 === "undefined" && key != "") {
        r4 = "";
      } else if (typeof r4 === "undefined") {
        r4 = "$";
      }
      if (typeof r4 === "object") {
        return pre + TOKEN + JSON.stringify(r4) + TOKEN;
      }
      return pre + r4;
    }
    function parseInternal(string, env, opts) {
      if (!opts) {
        opts = {};
      }
      var BS = opts.escape || "\\";
      var BAREWORD = "(\\" + BS + `['"` + META + `]|[^\\s'"` + META + "])+";
      var chunker = new RegExp([
        "(" + CONTROL + ")",
        // control chars
        "(" + BAREWORD + "|" + SINGLE_QUOTE + "|" + DOUBLE_QUOTE + ")+"
      ].join("|"), "g");
      var matches = matchAll(string, chunker);
      if (matches.length === 0) {
        return [];
      }
      if (!env) {
        env = {};
      }
      var commented = false;
      return matches.map(function(match) {
        var s7 = match[0];
        if (!s7 || commented) {
          return void 0;
        }
        if (controlRE.test(s7)) {
          return { op: s7 };
        }
        var quote = false;
        var esc = false;
        var out = "";
        var isGlob = false;
        var i7;
        function parseEnvVar() {
          i7 += 1;
          var varend;
          var varname;
          var char = s7.charAt(i7);
          if (char === "{") {
            i7 += 1;
            if (s7.charAt(i7) === "}") {
              throw new Error("Bad substitution: " + s7.slice(i7 - 2, i7 + 1));
            }
            varend = s7.indexOf("}", i7);
            if (varend < 0) {
              throw new Error("Bad substitution: " + s7.slice(i7));
            }
            varname = s7.slice(i7, varend);
            i7 = varend;
          } else if (/[*@#?$!_-]/.test(char)) {
            varname = char;
            i7 += 1;
          } else {
            var slicedFromI = s7.slice(i7);
            varend = slicedFromI.match(/[^\w\d_]/);
            if (!varend) {
              varname = slicedFromI;
              i7 = s7.length;
            } else {
              varname = slicedFromI.slice(0, varend.index);
              i7 += varend.index - 1;
            }
          }
          return getVar(env, "", varname);
        }
        for (i7 = 0; i7 < s7.length; i7++) {
          var c4 = s7.charAt(i7);
          isGlob = isGlob || !quote && (c4 === "*" || c4 === "?");
          if (esc) {
            out += c4;
            esc = false;
          } else if (quote) {
            if (c4 === quote) {
              quote = false;
            } else if (quote == SQ) {
              out += c4;
            } else {
              if (c4 === BS) {
                i7 += 1;
                c4 = s7.charAt(i7);
                if (c4 === DQ || c4 === BS || c4 === DS) {
                  out += c4;
                } else {
                  out += BS + c4;
                }
              } else if (c4 === DS) {
                out += parseEnvVar();
              } else {
                out += c4;
              }
            }
          } else if (c4 === DQ || c4 === SQ) {
            quote = c4;
          } else if (controlRE.test(c4)) {
            return { op: s7 };
          } else if (hash.test(c4)) {
            commented = true;
            var commentObj = { comment: string.slice(match.index + i7 + 1) };
            if (out.length) {
              return [out, commentObj];
            }
            return [commentObj];
          } else if (c4 === BS) {
            esc = true;
          } else if (c4 === DS) {
            out += parseEnvVar();
          } else {
            out += c4;
          }
        }
        if (isGlob) {
          return { op: "glob", pattern: out };
        }
        return out;
      }).reduce(function(prev, arg) {
        return typeof arg === "undefined" ? prev : prev.concat(arg);
      }, []);
    }
    module.exports = function parse(s7, env, opts) {
      var mapped = parseInternal(s7, env, opts);
      if (typeof env !== "function") {
        return mapped;
      }
      return mapped.reduce(function(acc, s8) {
        if (typeof s8 === "object") {
          return acc.concat(s8);
        }
        var xs = s8.split(RegExp("(" + TOKEN + ".*?" + TOKEN + ")", "g"));
        if (xs.length === 1) {
          return acc.concat(xs[0]);
        }
        return acc.concat(xs.filter(Boolean).map(function(x7) {
          if (startsWithToken.test(x7)) {
            return JSON.parse(x7.split(TOKEN)[1]);
          }
          return x7;
        }));
      }, []);
    };
  }
});

// node_modules/shell-quote/index.js
var require_shell_quote = __commonJS({
  "node_modules/shell-quote/index.js"(exports) {
    "use strict";
    exports.quote = require_quote();
    exports.parse = require_parse();
  }
});

// node_modules/@scalar/api-client/dist/libs/parse-curl.js
var import_shell_quote = __toESM(require_shell_quote(), 1);
function b2(i6) {
  const e4 = (0, import_shell_quote.parse)(i6).map((o) => typeof o == "object" && "op" in o && o.op === "glob" ? o.pattern.trim() : typeof o == "string" ? o.trim() : o).filter((o) => o !== ""), a2 = { url: "" }, t = e4[Symbol.iterator]();
  let r4 = t.next().value;
  for (; r4; )
    r4 === "-X" || r4 === "--request" ? h(t, a2) : r4 === "--url" ? s3(t, a2) : r4 === "-H" || r4 === "--header" ? p2(t, a2) : r4 === "--data" || r4 === "-d" || r4 === "--data-raw" || r4 === "--data-urlencode" || r4 === "--data-binary" || r4 === "--data-ascii" ? u2(t, a2, i6) : typeof r4 == "string" && !a2.url && (r4.startsWith("http") || r4.startsWith("www.")) ? s3([r4][Symbol.iterator](), a2) : r4 === "-P" ? d2(t, a2) : typeof r4 == "string" && r4.toLowerCase().includes("content-type") ? f3(r4, a2) : r4 === "-u" || r4 === "--user" ? m3(t, a2) : (r4 === "-b" || r4 === "--cookie") && l(t, a2), r4 = t.next().value;
  return a2;
}
function h(i6, e4) {
  e4.method = i6.next().value.toLowerCase();
}
function s3(i6, e4) {
  const a2 = new URL(i6.next().value.replace(/['"]/g, ""));
  e4.servers = [a2.origin], e4.path = a2.pathname !== "/" ? a2.pathname : "", e4.url = e4.servers[0] + e4.path;
  const t = n2(a2.search);
  e4.queryParameters = e4.queryParameters ? [...e4.queryParameters, ...t] : t;
}
function p2(i6, e4) {
  const a2 = i6.next().value.split(/:(.*)/);
  e4.headers = e4.headers || {}, a2[1] !== void 0 ? e4.headers[a2[0].trim()] = a2[1].trim() : e4.headers[a2[0].trim()] = "";
}
function d2(i6, e4) {
  const a2 = i6.next().value.replace(/['"]/g, "").split("=");
  e4.pathVariables = e4.pathVariables || {}, a2[1] !== void 0 ? e4.pathVariables[a2[0].trim()] = a2[1].trim() : e4.pathVariables[a2[0].trim()] = "";
}
function n2(i6) {
  const e4 = [];
  return new URL(i6, "http://example.com").searchParams.forEach((t, r4) => {
    e4.push({ key: r4, value: t });
  }), e4;
}
function f3(i6, e4) {
  const a2 = i6.replace(/['"]/g, "").split(/:(.+)/);
  e4.headers = e4.headers || {}, a2[0] && (a2[1] !== void 0 ? e4.headers[a2[0].trim()] = a2[1].trim() : e4.headers[a2[0].trim()] = "");
}
function m3(i6, e4) {
  const a2 = i6.next().value;
  try {
    const t = btoa(a2);
    e4.headers = e4.headers || {}, e4.headers.Authorization = `Basic ${t}`;
  } catch (t) {
    console.warn("Could not base64 encode these HTTP basic auth credentials:", a2, t);
  }
}
function l(i6, e4) {
  const a2 = i6.next().value;
  e4.headers = e4.headers || {}, e4.headers.Cookie ? e4.headers.Cookie += `; ${a2}` : e4.headers.Cookie = a2.replace(/;$/, "");
}
function u2(i6, e4, a2) {
  const t = i6.next().value;
  if (typeof t == "string" && (t.startsWith("@") ? e4.body = "" : e4.body = t, !e4.url || a2.includes("-G"))) {
    const r4 = n2(`?${e4.body}`);
    e4.queryParameters = e4.queryParameters ? [...e4.queryParameters, ...r4] : r4;
  }
}

// node_modules/@scalar/api-client/dist/libs/importers/curl.js
function l2(o) {
  try {
    return JSON.parse(o);
  } catch {
    const s7 = {};
    return o.split("&").forEach((a2) => {
      const [r4, e4] = a2.split("=");
      r4 && e4 && (s7[decodeURIComponent(r4)] = decodeURIComponent(e4));
    }), s7;
  }
}
function C(o) {
  const s7 = b2(o), { method: a2 = "get", url: r4, body: e4 = "", headers: p7 = {}, servers: i6, queryParameters: m4 = [] } = s7, y3 = new URL(r4).pathname, d6 = e4 != null && e4.includes("=") && !e4.startsWith("{") ? "application/x-www-form-urlencoded" : p7["Content-Type"] || "", c4 = e4 ? l2(e4) : {}, u3 = [
    ...Array.isArray(m4) ? m4.map(({ key: n3, value: t }) => ({
      name: n3,
      in: "query",
      schema: { type: typeof t, examples: [t] }
    })) : [],
    ...Object.entries(p7 || {}).map(([n3, t]) => ({
      name: n3,
      in: "header",
      schema: { type: typeof t },
      example: t
    }))
  ];
  return {
    method: a2,
    url: r4,
    path: y3,
    headers: p7,
    servers: i6 ?? [],
    ...Object.keys(c4).length > 0 && {
      requestBody: {
        content: {
          [d6]: {
            schema: {
              type: "object",
              properties: Object.fromEntries(
                Object.entries(c4).map(([n3, t]) => [n3, { type: typeof t }])
              )
            },
            example: c4
          }
        }
      }
    },
    parameters: u3
  };
}

// node_modules/@scalar/api-client/dist/views/Request/RequestSubpageHeader.vue2.js
var B2 = { class: "lg:min-h-client-header t-app__top-container border-b-1/2 flex w-full flex-wrap items-center justify-center p-2 pt-2 lg:p-1 lg:pt-1" };
var S2 = { class: "mb-2 flex w-1/2 flex-row items-center gap-1 lg:mb-0 lg:flex-1 lg:px-1" };
var $2 = { class: "mb-2 flex w-1/2 flex-row items-center justify-end gap-1 lg:mb-0 lg:flex-1 lg:px-2.5" };
var O = defineComponent({
  __name: "RequestSubpageHeader",
  props: {
    collection: {},
    operation: {},
    server: {},
    environment: {},
    envVariables: {},
    workspace: {}
  },
  emits: ["hideModal", "importCurl"],
  setup(x7) {
    const { hideClientButton: u3, showSidebar: d6, integration: c4 } = je(), { isSidebarOpen: f5 } = m2(), { layout: n3 } = s(), { currentRoute: b4 } = useRouter();
    return (e4, t) => (openBlock(), createElementBlock("div", B2, [
      createBaseVNode("div", S2, [
        unref(d6) ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(["size-8", { hidden: unref(n3) === "modal" && !unref(f5) }])
        }, null, 2)) : createCommentVNode("", true)
      ]),
      createVNode(e, {
        collection: e4.collection,
        envVariables: e4.envVariables,
        environment: e4.environment,
        operation: e4.operation,
        server: e4.server,
        workspace: e4.workspace,
        onImportCurl: t[0] || (t[0] = (a2) => e4.$emit("importCurl", a2))
      }, null, 8, ["collection", "envVariables", "environment", "operation", "server", "workspace"]),
      createBaseVNode("div", $2, [
        unref(n3) === "modal" && e4.collection.documentUrl && !unref(u3) ? (openBlock(), createBlock(unref(i3), {
          key: 0,
          buttonSource: "modal",
          class: "!w-fit lg:-mr-1",
          integration: unref(c4) ?? e4.collection.integration ?? null,
          source: unref(b4).query.source === "gitbook" ? "gitbook" : "api-reference",
          url: e4.collection.documentUrl
        }, null, 8, ["integration", "source", "url"])) : createCommentVNode("", true),
        unref(n3) === "modal" ? (openBlock(), createElementBlock("button", {
          key: 1,
          class: "app-exit-button gitbook-hidden zoomed:static zoomed:p-1 fixed right-2 top-2 rounded-full p-2",
          type: "button",
          onClick: t[1] || (t[1] = (a2) => e4.$emit("hideModal"))
        }, [
          createVNode(unref(c), {
            icon: "Close",
            size: "lg",
            thickness: "2"
          }),
          t[3] || (t[3] = createBaseVNode("span", { class: "sr-only" }, "Close Client", -1))
        ])) : createCommentVNode("", true),
        unref(n3) === "modal" ? (openBlock(), createElementBlock("button", {
          key: 2,
          class: "text-c-1 hover:bg-b-2 active:text-c-1 gitbook-show -mr-1.5 rounded p-2",
          type: "button",
          onClick: t[2] || (t[2] = (a2) => e4.$emit("hideModal"))
        }, [
          createVNode(unref(c), {
            icon: "Close",
            size: "md",
            thickness: "1.75"
          }),
          t[4] || (t[4] = createBaseVNode("span", { class: "sr-only" }, "Close Client", -1))
        ])) : createCommentVNode("", true)
      ])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSubpageHeader.vue.js
var d3 = s2(O, [["__scopeId", "data-v-ad3dcf8d"]]);

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyDownload.vue.js
var x2 = ["download", "href"];
var k = defineComponent({
  __name: "ResponseBodyDownload",
  props: {
    href: {},
    type: {},
    filename: {}
  },
  setup(t) {
    const n3 = t, r4 = computed(() => {
      var e4;
      const o = ((e4 = n[n3.type ?? ""]) == null ? void 0 : e4.extension) ?? ".unknown";
      return n3.filename ? n3.filename : `response${o}`;
    });
    return (o, e4) => (openBlock(), createElementBlock("a", {
      class: "text-c-3 text-xxs hover:bg-b-3 no-underlin flex items-center gap-1 rounded px-1.5 py-0.5",
      download: `${r4.value}`,
      href: o.href,
      onClick: e4[0] || (e4[0] = withModifiers(() => {
      }, ["stop"]))
    }, [
      createVNode(unref(c), {
        icon: "Download",
        size: "xs"
      }),
      e4[1] || (e4[1] = createBaseVNode("span", null, [
        createBaseVNode("span", null, "Download"),
        createBaseVNode("span", { class: "sr-only" }, "Response Body")
      ], -1))
    ], 8, x2));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyInfo.vue.js
var r2 = {};
var d4 = { class: "flex justify-center px-2 py-3" };
var f4 = { class: "text-c-3 p-2 text-sm" };
function l3(e4, _2) {
  return openBlock(), createElementBlock("div", d4, [
    createBaseVNode("div", f4, [
      renderSlot(e4.$slots, "default")
    ])
  ]);
}
var p3 = s2(r2, [["render", l3]]);

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyPreview.vue2.js
var c3 = ["src"];
var k2 = ["src", "type"];
var w = ["src", "type"];
var B3 = ["data", "type"];
var $3 = defineComponent({
  __name: "ResponseBodyPreview",
  props: {
    src: {},
    type: {},
    mode: {},
    alpha: { type: Boolean, default: false }
  },
  setup(u3) {
    const r4 = ref(false);
    return watch(
      () => u3.src,
      () => r4.value = false
    ), (e4, o) => !r4.value && e4.src ? (openBlock(), createElementBlock("div", {
      key: 0,
      class: normalizeClass(["flex justify-center overflow-auto rounded-b", { "bg-preview p-2": e4.alpha }])
    }, [
      e4.mode === "image" ? (openBlock(), createElementBlock("img", {
        key: 0,
        class: normalizeClass(["h-full max-w-full", { rounded: e4.alpha }]),
        src: e4.src,
        onError: o[0] || (o[0] = (t) => r4.value = true)
      }, null, 42, c3)) : e4.mode === "video" ? (openBlock(), createElementBlock("video", {
        key: 1,
        autoplay: "",
        controls: "",
        width: "100%",
        onError: o[1] || (o[1] = (t) => r4.value = true)
      }, [
        createBaseVNode("source", {
          src: e4.src,
          type: e4.type
        }, null, 8, k2)
      ], 32)) : e4.mode === "audio" ? (openBlock(), createElementBlock("audio", {
        key: 2,
        class: "my-12",
        controls: "",
        onError: o[2] || (o[2] = (t) => r4.value = true)
      }, [
        createBaseVNode("source", {
          src: e4.src,
          type: e4.type
        }, null, 8, w)
      ], 32)) : (openBlock(), createElementBlock("object", {
        key: 3,
        class: "aspect-[4/3] w-full",
        data: e4.src,
        type: e4.type,
        onError: o[3] || (o[3] = (t) => r4.value = true)
      }, null, 40, B3))
    ], 2)) : (openBlock(), createBlock(p3, { key: 1 }, {
      default: withCtx(() => o[4] || (o[4] = [
        createTextVNode("Preview unavailable")
      ])),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyPreview.vue.js
var s4 = s2($3, [["__scopeId", "data-v-0956ad2d"]]);

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyRaw.vue2.js
var k3 = { class: "body-raw grid min-h-0 overflow-hidden p-px outline-none has-[:focus-visible]:outline" };
var w2 = {
  key: 0,
  class: "scalar-code-copy"
};
var x3 = {
  class: "body-raw-scroller custom-scroll relative overflow-x-auto overscroll-contain",
  tabindex: "0"
};
var E = defineComponent({
  __name: "ResponseBodyRaw",
  props: {
    content: {},
    language: {}
  },
  setup(d6) {
    const r4 = d6, n3 = ref(null), { copyToClipboard: u3 } = p(), { codeMirror: p7 } = useCodeMirror({
      codeMirrorRef: n3,
      readOnly: true,
      lineNumbers: true,
      content: toRef(() => prettyPrintJson(r4.content)),
      language: toRef(() => r4.language),
      forceFoldGutter: true
    }), s7 = () => {
      var t;
      return ((t = p7.value) == null ? void 0 : t.state.doc.toString()) || "";
    };
    return (t, o) => (openBlock(), createElementBlock("div", k3, [
      s7() ? (openBlock(), createElementBlock("div", w2, [
        createBaseVNode("button", {
          class: "copy-button",
          type: "button",
          onClick: o[0] || (o[0] = (R) => unref(u3)(s7()))
        }, [
          o[1] || (o[1] = createBaseVNode("span", { class: "sr-only" }, "Copy content", -1)),
          createVNode(unref(c), {
            icon: "Clipboard",
            size: "md"
          })
        ])
      ])) : createCommentVNode("", true),
      createBaseVNode("div", x3, [
        createBaseVNode("div", {
          ref_key: "codeMirrorRef",
          ref: n3
        }, null, 512)
      ])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyRaw.vue.js
var s5 = s2(E, [["__scopeId", "data-v-96fbecd5"]]);

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyToggle.vue.js
var a = { class: "text-c-3 text-xxs -my-1 flex justify-center gap-0.5 rounded p-0.5" };
var i4 = defineComponent({
  __name: "ResponseBodyToggle",
  props: {
    modelValue: { type: Boolean }
  },
  emits: ["update:modelValue"],
  setup(p7) {
    return (e4, t) => (openBlock(), createElementBlock("div", a, [
      createBaseVNode("button", {
        class: normalizeClass(["hover:bg-b-3 rounded px-1", { "bg-b-3 text-c-1 cursor-default": e4.modelValue }]),
        type: "button",
        onClick: t[0] || (t[0] = withModifiers((s7) => e4.$emit("update:modelValue", true), ["stop"]))
      }, " Preview ", 2),
      createBaseVNode("button", {
        class: normalizeClass(["hover:bg-b-3 rounded px-1", { "bg-b-3 text-c-1 cursor-default": !e4.modelValue }]),
        type: "button",
        onClick: t[1] || (t[1] = withModifiers((s7) => e4.$emit("update:modelValue", false), ["stop"]))
      }, " Raw ", 2)
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBody.vue2.js
var q = {
  key: 0,
  class: "bg-b-1 flex max-h-[calc(100%-32px)] flex-col overflow-hidden"
};
var z = { class: "flex items-center justify-between border-b px-3 py-1.5" };
var A = { class: "text-xxs font-code leading-3" };
var X = defineComponent({
  __name: "ResponseBody",
  props: {
    title: {},
    data: {},
    headers: {}
  },
  setup(x7) {
    const c4 = x7, r4 = ref(true), p7 = computed(
      () => {
        var t;
        return !!((t = e4.value) != null && t.raw && e4.value.preview);
      }
    ), k5 = computed(() => r4.value || !p7.value), B6 = computed(() => !r4.value || !p7.value), { mimeType: i6, attachmentFilename: R, dataUrl: l5 } = y({
      data: c4.data,
      headers: c4.headers
    }), e4 = computed(() => n[i6.value.essence]);
    return (t, m4) => (openBlock(), createBlock(P, { class: "max-h-content overflow-y-hidden" }, createSlots({
      title: withCtx(() => [
        createTextVNode(toDisplayString(t.title), 1)
      ]),
      default: withCtx(() => {
        var f5, v, y3, w3;
        return [
          t.data ? (openBlock(), createElementBlock("div", q, [
            createBaseVNode("div", z, [
              createBaseVNode("span", A, toDisplayString(unref(i6).essence), 1),
              p7.value ? (openBlock(), createBlock(i4, {
                key: 0,
                modelValue: r4.value,
                "onUpdate:modelValue": m4[0] || (m4[0] = (V) => r4.value = V)
              }, null, 8, ["modelValue"])) : createCommentVNode("", true)
            ]),
            (f5 = e4.value) != null && f5.raw && B6.value ? (openBlock(), createBlock(s5, {
              key: unref(l5),
              content: t.data,
              language: e4.value.language
            }, null, 8, ["content", "language"])) : createCommentVNode("", true),
            (v = e4.value) != null && v.preview && k5.value ? (openBlock(), createBlock(s4, {
              key: unref(l5),
              alpha: e4.value.alpha,
              mode: e4.value.preview,
              src: unref(l5),
              type: unref(i6).essence
            }, null, 8, ["alpha", "mode", "src", "type"])) : createCommentVNode("", true),
            !((y3 = e4.value) != null && y3.raw) && !((w3 = e4.value) != null && w3.preview) ? (openBlock(), createBlock(p3, { key: 2 }, {
              default: withCtx(() => m4[1] || (m4[1] = [
                createTextVNode(" Binary file ")
              ])),
              _: 1
            })) : createCommentVNode("", true)
          ])) : createCommentVNode("", true)
        ];
      }),
      _: 2
    }, [
      t.data && unref(l5) ? {
        name: "actions",
        fn: withCtx(() => [
          createVNode(k, {
            filename: unref(R),
            href: unref(l5),
            type: unref(i6).essence
          }, null, 8, ["filename", "href", "type"])
        ]),
        key: "0"
      } : void 0
    ]), 1024));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBody.vue.js
var e2 = s2(X, [["__scopeId", "data-v-9bbb0b3d"]]);

// node_modules/@scalar/api-client/dist/assets/computer.ascii.js
var B4 = `                         .,,uod8B8bou,,.
                ..,uod8BBBBBBBBBBBBBBBBRPFT?l!i:.
           ||||||||||||||!?TFPRBBBBBBBBBBBBBBB8m=,
           ||||   '""^^!!||||||||||TFPRBBBVT!:...!
           ||||            '""^^!!|||||?!:.......!
           ||||                     ||||.........!
           ||||                     ||||.........!
           ||||                     ||||.........!
           ||||                     ||||.........!
           ||||                     ||||.........!
           ||||                     ||||.........!
           ||||,                    ||||.........\`
           |||||!!-._               ||||.......;.
           ':!|||||||||!!-._        ||||.....bBBBBWdou,.
         bBBBBB86foi!|||||||!!-..:|||!..bBBBBBBBBBBBBBBY!
         ::!?TFPRBBBBBB86foi!||||||||!!bBBBBBBBBBBBBBBY..!
         :::::::::!?TFPRBBBBBB86ftiaabBBBBBBBBBBBBBBY....!
         :::;\`"^!:;::::::!?TFPRBBBBBBBBBBBBBBBBBBBY......!
         ;::::::...''^::::::::::!?TFPRBBBBBBBBBBY........!
     .ob86foi;::::::::::::::::::::::::!?TFPRBY..........\`
    .b888888888886foi;:::::::::::::::::::::::..........\`
 .b888888888888888888886foi;::::::::::::::::..........
.b888888888888888888888888888886foi;:::::::::......\`
!Tf998888888888888888888888888888888886foi;:::....\`
  '"^!|Tf9988888888888888888888888888888888!::..\`
       '"^!|Tf998888888888888888888888889!! '\`
             '"^!|Tf9988888888888888888!!\`            iBBbo.
                  '"^!|Tf998888888889!\`             WBBBBbo.
                        '"^!|Tf9989!\`              YBBBP^'
                              '"^!\`               \`
`;

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseEmpty.vue2.js
var P2 = { class: "col-1 flex-center relative gap-6 p-2 capitalize" };
var T = {
  key: 0,
  class: "scalar-version-number"
};
var G = { class: "text-c-3 right-4 mt-auto flex w-full flex-col items-end gap-2 text-sm" };
var Z = defineComponent({
  __name: "ResponseEmpty",
  props: {
    numWorkspaceRequests: {}
  },
  setup(H) {
    const { events: r4, requestMutators: R } = je(), w3 = useRoute(), h2 = useRouter(), { layout: a2 } = s(), { activeWorkspace: C2, activeCollection: f5, activeRequest: p7 } = F(), k5 = () => {
      var n3, v, g2, x7, b4;
      if (!((n3 = f5.value) != null && n3.uid)) return;
      const s7 = (g2 = (v = p7.value) == null ? void 0 : v.tags) != null && g2.length ? { tags: p7.value.tags[0] ? [p7.value.tags[0]] : [] } : {}, e4 = R.add(
        s7,
        (x7 = f5.value) == null ? void 0 : x7.uid
      );
      e4 && (h2.push({
        name: "request",
        params: {
          workspace: (b4 = C2.value) == null ? void 0 : b4.uid,
          request: e4.uid
        }
      }), nextTick(() => {
        r4.hotKeys.emit({
          focusAddressBar: new KeyboardEvent("keydown", { key: "l" })
        });
      }));
    }, y3 = (s7) => {
      s7 != null && s7.createNew && w3.name === "request" && k5();
    }, N3 = "2.2.62";
    return onMounted(() => r4.hotKeys.on(y3)), onBeforeUnmount(() => r4.hotKeys.off(y3)), (s7, e4) => (openBlock(), createElementBlock("div", P2, [
      createBaseVNode("div", {
        class: normalizeClass(["flex h-[calc(100%_-_50px)] flex-col items-center justify-center", {
          "hidden opacity-0": s7.numWorkspaceRequests <= 1 && unref(a2) !== "modal"
        }])
      }, [
        unref(a2) !== "modal" ? (openBlock(), createElementBlock("div", T, [
          createTextVNode(" Scalar App V" + toDisplayString(unref(N3)) + " Beta ", 1),
          e4[3] || (e4[3] = createBaseVNode("div", { class: "mt-2" }, [
            createBaseVNode("a", {
              href: "https://github.com/scalar/scalar/issues/2669",
              target: "_blank"
            }, " Roadmap ")
          ], -1))
        ])) : createCommentVNode("", true),
        e4[4] || (e4[4] = createBaseVNode("a", {
          class: "gitbook-show scalar-version-number",
          href: "https://www.scalar.com",
          target: "_blank"
        }, " Powered By Scalar.com ", -1)),
        createVNode(i, {
          art: unref(B4),
          class: "text-c-3"
        }, null, 8, ["art"])
      ], 2),
      unref(a2) !== "modal" ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass(["hidden h-[calc(100%_-_50px)] items-center justify-center pb-5", {
          "!flex opacity-100": s7.numWorkspaceRequests == 1
        }])
      }, [
        createVNode(r)
      ], 2)) : createCommentVNode("", true),
      createBaseVNode("div", G, [
        unref(a2) !== "modal" ? (openBlock(), createElementBlock("button", {
          key: 0,
          class: "flex items-center gap-1.5",
          type: "button",
          onClick: e4[0] || (e4[0] = (n3) => unref(r4).commandPalette.emit())
        }, [
          e4[5] || (e4[5] = createTextVNode(" Get Started ")),
          createVNode(b, { hotkey: "k" })
        ])) : createCommentVNode("", true),
        unref(a2) === "desktop" ? (openBlock(), createElementBlock("button", {
          key: 1,
          class: "flex items-center gap-1.5",
          type: "button",
          onClick: e4[1] || (e4[1] = (n3) => k5())
        }, [
          e4[6] || (e4[6] = createTextVNode(" New Request ")),
          createVNode(b, { hotkey: "N" })
        ])) : createCommentVNode("", true),
        createBaseVNode("button", {
          class: "flex items-center gap-1.5",
          type: "button",
          onClick: e4[2] || (e4[2] = (n3) => unref(r4).executeRequest.emit())
        }, [
          e4[7] || (e4[7] = createTextVNode(" Send Request ")),
          createVNode(b, { hotkey: "↵" })
        ])
      ])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseEmpty.vue.js
var r3 = s2(Z, [["__scopeId", "data-v-fb583e5d"]]);

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseLoadingOverlay.vue2.js
var L = {
  key: 0,
  class: "bg-b-1 absolute inset-0 flex flex-col items-center justify-center gap-6"
};
var y2 = defineComponent({
  __name: "ResponseLoadingOverlay",
  setup(S3) {
    const { events: n3 } = je(), t = S(), a2 = ref();
    return n3.requestStatus.on((s7) => {
      s7 === "start" ? a2.value = setTimeout(() => t.startLoading(), 1e3) : (clearTimeout(a2.value), a2.value = void 0, t.stopLoading());
    }), (s7, o) => (openBlock(), createBlock(Transition, null, {
      default: withCtx(() => [
        unref(t).isLoading ? (openBlock(), createElementBlock("div", L, [
          createVNode(unref(m), {
            class: "text-c-3",
            loadingState: unref(t),
            size: "3xl"
          }, null, 8, ["loadingState"]),
          createVNode(unref($), {
            variant: "ghost",
            onClick: o[0] || (o[0] = (C2) => unref(n3).cancelRequest.emit())
          }, {
            default: withCtx(() => o[1] || (o[1] = [
              createTextVNode(" Cancel ")
            ])),
            _: 1
          })
        ])) : createCommentVNode("", true)
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseLoadingOverlay.vue.js
var p4 = s2(y2, [["__scopeId", "data-v-35587f03"]]);

// node_modules/pretty-bytes/index.js
var BYTE_UNITS = [
  "B",
  "kB",
  "MB",
  "GB",
  "TB",
  "PB",
  "EB",
  "ZB",
  "YB"
];
var BIBYTE_UNITS = [
  "B",
  "KiB",
  "MiB",
  "GiB",
  "TiB",
  "PiB",
  "EiB",
  "ZiB",
  "YiB"
];
var BIT_UNITS = [
  "b",
  "kbit",
  "Mbit",
  "Gbit",
  "Tbit",
  "Pbit",
  "Ebit",
  "Zbit",
  "Ybit"
];
var BIBIT_UNITS = [
  "b",
  "kibit",
  "Mibit",
  "Gibit",
  "Tibit",
  "Pibit",
  "Eibit",
  "Zibit",
  "Yibit"
];
var toLocaleString = (number, locale, options) => {
  let result = number;
  if (typeof locale === "string" || Array.isArray(locale)) {
    result = number.toLocaleString(locale, options);
  } else if (locale === true || options !== void 0) {
    result = number.toLocaleString(void 0, options);
  }
  return result;
};
function prettyBytes(number, options) {
  if (!Number.isFinite(number)) {
    throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);
  }
  options = {
    bits: false,
    binary: false,
    space: true,
    ...options
  };
  const UNITS = options.bits ? options.binary ? BIBIT_UNITS : BIT_UNITS : options.binary ? BIBYTE_UNITS : BYTE_UNITS;
  const separator = options.space ? " " : "";
  if (options.signed && number === 0) {
    return ` 0${separator}${UNITS[0]}`;
  }
  const isNegative = number < 0;
  const prefix = isNegative ? "-" : options.signed ? "+" : "";
  if (isNegative) {
    number = -number;
  }
  let localeOptions;
  if (options.minimumFractionDigits !== void 0) {
    localeOptions = { minimumFractionDigits: options.minimumFractionDigits };
  }
  if (options.maximumFractionDigits !== void 0) {
    localeOptions = { maximumFractionDigits: options.maximumFractionDigits, ...localeOptions };
  }
  if (number < 1) {
    const numberString2 = toLocaleString(number, options.locale, localeOptions);
    return prefix + numberString2 + separator + UNITS[0];
  }
  const exponent = Math.min(Math.floor(options.binary ? Math.log(number) / Math.log(1024) : Math.log10(number) / 3), UNITS.length - 1);
  number /= (options.binary ? 1024 : 1e3) ** exponent;
  if (!localeOptions) {
    number = number.toPrecision(3);
  }
  const numberString = toLocaleString(Number(number), options.locale, localeOptions);
  const unit = UNITS[exponent];
  return prefix + numberString + separator + unit;
}

// node_modules/parse-ms/index.js
function parseMilliseconds(milliseconds) {
  if (typeof milliseconds !== "number") {
    throw new TypeError("Expected a number");
  }
  const roundTowardsZero = milliseconds > 0 ? Math.floor : Math.ceil;
  return {
    days: roundTowardsZero(milliseconds / 864e5),
    hours: roundTowardsZero(milliseconds / 36e5) % 24,
    minutes: roundTowardsZero(milliseconds / 6e4) % 60,
    seconds: roundTowardsZero(milliseconds / 1e3) % 60,
    milliseconds: roundTowardsZero(milliseconds) % 1e3,
    microseconds: roundTowardsZero(milliseconds * 1e3) % 1e3,
    nanoseconds: roundTowardsZero(milliseconds * 1e6) % 1e3
  };
}

// node_modules/pretty-ms/index.js
var pluralize = (word, count) => count === 1 ? word : `${word}s`;
var SECOND_ROUNDING_EPSILON = 1e-7;
function prettyMilliseconds(milliseconds, options = {}) {
  if (!Number.isFinite(milliseconds)) {
    throw new TypeError("Expected a finite number");
  }
  if (options.colonNotation) {
    options.compact = false;
    options.formatSubMilliseconds = false;
    options.separateMilliseconds = false;
    options.verbose = false;
  }
  if (options.compact) {
    options.secondsDecimalDigits = 0;
    options.millisecondsDecimalDigits = 0;
  }
  const result = [];
  const floorDecimals = (value, decimalDigits) => {
    const flooredInterimValue = Math.floor(value * 10 ** decimalDigits + SECOND_ROUNDING_EPSILON);
    const flooredValue = Math.round(flooredInterimValue) / 10 ** decimalDigits;
    return flooredValue.toFixed(decimalDigits);
  };
  const add = (value, long, short, valueString) => {
    if ((result.length === 0 || !options.colonNotation) && value === 0 && !(options.colonNotation && short === "m")) {
      return;
    }
    valueString = (valueString || value || "0").toString();
    let prefix;
    let suffix;
    if (options.colonNotation) {
      prefix = result.length > 0 ? ":" : "";
      suffix = "";
      const wholeDigits = valueString.includes(".") ? valueString.split(".")[0].length : valueString.length;
      const minLength = result.length > 0 ? 2 : 1;
      valueString = "0".repeat(Math.max(0, minLength - wholeDigits)) + valueString;
    } else {
      prefix = "";
      suffix = options.verbose ? " " + pluralize(long, value) : short;
    }
    result.push(prefix + valueString + suffix);
  };
  const parsed = parseMilliseconds(milliseconds);
  add(Math.trunc(parsed.days / 365), "year", "y");
  add(parsed.days % 365, "day", "d");
  add(parsed.hours, "hour", "h");
  add(parsed.minutes, "minute", "m");
  if (options.separateMilliseconds || options.formatSubMilliseconds || !options.colonNotation && milliseconds < 1e3) {
    add(parsed.seconds, "second", "s");
    if (options.formatSubMilliseconds) {
      add(parsed.milliseconds, "millisecond", "ms");
      add(parsed.microseconds, "microsecond", "µs");
      add(parsed.nanoseconds, "nanosecond", "ns");
    } else {
      const millisecondsAndBelow = parsed.milliseconds + parsed.microseconds / 1e3 + parsed.nanoseconds / 1e6;
      const millisecondsDecimalDigits = typeof options.millisecondsDecimalDigits === "number" ? options.millisecondsDecimalDigits : 0;
      const roundedMiliseconds = millisecondsAndBelow >= 1 ? Math.round(millisecondsAndBelow) : Math.ceil(millisecondsAndBelow);
      const millisecondsString = millisecondsDecimalDigits ? millisecondsAndBelow.toFixed(millisecondsDecimalDigits) : roundedMiliseconds;
      add(
        Number.parseFloat(millisecondsString),
        "millisecond",
        "ms",
        millisecondsString
      );
    }
  } else {
    const seconds = milliseconds / 1e3 % 60;
    const secondsDecimalDigits = typeof options.secondsDecimalDigits === "number" ? options.secondsDecimalDigits : 1;
    const secondsFixed = floorDecimals(seconds, secondsDecimalDigits);
    const secondsString = options.keepDecimalsOnWholeSeconds ? secondsFixed : secondsFixed.replace(/\.0+$/, "");
    add(Number.parseFloat(secondsString), "second", "s", secondsString);
  }
  if (result.length === 0) {
    return "0" + (options.verbose ? " milliseconds" : "ms");
  }
  if (options.compact) {
    return result[0];
  }
  if (typeof options.unitCount === "number") {
    const separator = options.colonNotation ? "" : " ";
    return result.slice(0, Math.max(options.unitCount, 1)).join(separator);
  }
  return options.colonNotation ? result.join("") : result.join(" ");
}

// node_modules/@scalar/api-client/dist/components/HelpfulLink.vue.js
var l4 = ["href"];
var p5 = defineComponent({
  __name: "HelpfulLink",
  props: {
    href: {}
  },
  setup(s7) {
    return (e4, f5) => (openBlock(), createElementBlock("a", {
      class: "decoration-c-3 cursor-help underline underline-offset-2",
      href: e4.href,
      rel: "noopener noreferrer",
      target: "_blank"
    }, [
      renderSlot(e4.$slots, "default")
    ], 8, l4));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseMetaInformation.vue.js
var x4 = { class: "text-c-1 flex gap-1.5" };
var z2 = { key: 0 };
var D = { key: 0 };
var E2 = { key: 1 };
var $4 = defineComponent({
  __name: "ResponseMetaInformation",
  props: {
    response: {}
  },
  setup(g2) {
    const C2 = g2, { events: S3 } = je(), l5 = ref(), u3 = ref(0);
    S3.requestStatus.on((e4) => {
      e4 === "start" ? l5.value = setInterval(() => u3.value += 1e3, 1e3) : (clearInterval(l5.value), l5.value = void 0, u3.value = 0);
    });
    const i6 = (e4) => {
      var c4, d6;
      const t = parseInt(
        ((c4 = e4.headers) == null ? void 0 : c4["Content-Length"]) || ((d6 = e4.headers) == null ? void 0 : d6["content-length"]) || "0",
        10
      );
      return t ? prettyBytes(t) : void 0;
    }, s7 = computed(() => {
      const e4 = C2.response.status;
      if (e4)
        return httpStatusCodes[e4] ?? void 0;
    });
    return (e4, t) => (openBlock(), createElementBlock("div", x4, [
      l5.value && u3.value ? (openBlock(), createElementBlock("span", z2, toDisplayString(unref(prettyMilliseconds)(u3.value)), 1)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        createBaseVNode("span", null, [
          t[0] || (t[0] = createBaseVNode("span", { class: "sr-only" }, "Response Information, Duration:", -1)),
          createTextVNode(" " + toDisplayString(unref(prettyMilliseconds)(e4.response.duration)), 1)
        ]),
        i6(e4.response) ? (openBlock(), createElementBlock("span", D, [
          t[1] || (t[1] = createBaseVNode("span", { class: "sr-only" }, ", Size:", -1)),
          createTextVNode(" " + toDisplayString(i6(e4.response)), 1)
        ])) : createCommentVNode("", true),
        s7.value ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          t[2] || (t[2] = createBaseVNode("span", { class: "sr-only" }, ", Status:", -1)),
          s7.value.url ? (openBlock(), createBlock(p5, {
            key: 0,
            class: "flex items-center gap-1.5",
            href: s7.value.url
          }, {
            default: withCtx(() => [
              createTextVNode(toDisplayString(e4.response.status) + " " + toDisplayString(s7.value.name) + " ", 1),
              createBaseVNode("span", {
                class: "block h-1.5 w-1.5 rounded-full",
                style: normalizeStyle({ backgroundColor: s7.value.color })
              }, null, 4)
            ]),
            _: 1
          }, 8, ["href"])) : (openBlock(), createElementBlock("span", E2, [
            createTextVNode(toDisplayString(e4.response.status) + " " + toDisplayString(s7.value.name) + " ", 1),
            createBaseVNode("span", {
              class: "block h-1.5 w-1.5 rounded-full",
              style: normalizeStyle({ backgroundColor: s7.value.color })
            }, null, 4)
          ]))
        ], 64)) : createCommentVNode("", true)
      ], 64))
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseBodyVirtual.vue.js
var N = defineComponent({
  __name: "ResponseBodyVirtual",
  props: {
    content: {},
    data: {},
    headers: {}
  },
  setup(s7) {
    const o = s7, l5 = computed(() => formatJsonOrYamlString(o.content)), { mimeType: i6, attachmentFilename: m4, dataUrl: n3 } = y({
      data: o.data,
      headers: o.headers
    });
    return (k5, t) => (openBlock(), createBlock(P, { class: "!max-h-100% response-body-virtual overflow-x-auto" }, createSlots({
      title: withCtx(() => [
        t[0] || (t[0] = createTextVNode("Body"))
      ]),
      default: withCtx(() => [
        t[1] || (t[1] = createBaseVNode("div", { class: "font-code text-xxs border-1/2 rounded-t border-b-0 px-2.5 py-1.5" }, " This response body is massive! Syntax highlighting won’t work here. ", -1)),
        createVNode(unref(B), {
          containerClass: "custom-scroll scalar-code-block border-1/2 rounded-b flex flex-1 max-h-screen",
          contentClass: "language-plaintext whitespace-pre font-code text-base",
          lineHeight: 20,
          text: l5.value
        }, null, 8, ["text"])
      ]),
      _: 2
    }, [
      unref(n3) ? {
        name: "actions",
        fn: withCtx(() => [
          createVNode(k, {
            filename: unref(m4),
            href: unref(n3),
            type: unref(i6).essence
          }, null, 8, ["filename", "href", "type"])
        ]),
        key: "0"
      } : void 0
    ]), 1024));
  }
});

// node_modules/@scalar/api-client/dist/components/DataTable/DataTableText.vue.js
var i5 = { class: "flex-1 whitespace-nowrap px-2 py-1.5" };
var x5 = defineComponent({
  __name: "DataTableText",
  props: {
    text: {}
  },
  setup(_2) {
    return (e4, f5) => (openBlock(), createBlock(f2, { class: "relative flex" }, {
      default: withCtx(() => [
        createBaseVNode("span", i5, [
          renderSlot(e4.$slots, "default", {}, () => [
            createTextVNode(toDisplayString(e4.text), 1)
          ])
        ])
      ]),
      _: 3
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseCookies.vue.js
var d5 = {
  key: 1,
  class: "text-c-3 border-t-1/2 bg-b-1 flex min-h-[65px] items-center justify-center px-4 text-sm"
};
var B5 = defineComponent({
  __name: "ResponseCookies",
  props: {
    cookies: {}
  },
  setup(x7) {
    return (s7, l5) => (openBlock(), createBlock(P, { defaultOpen: false }, {
      title: withCtx(() => l5[0] || (l5[0] = [
        createTextVNode("Cookies")
      ])),
      default: withCtx(() => [
        (openBlock(), createElementBlock(Fragment, { key: 0 }, [
          s7.cookies.length ? (openBlock(), createBlock(g, {
            key: 0,
            class: "flex-1",
            columns: ["", ""]
          }, {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(s7.cookies, (o) => (openBlock(), createBlock(i2, {
                key: o.name
              }, {
                default: withCtx(() => [
                  createVNode(x5, {
                    text: o.name
                  }, null, 8, ["text"]),
                  createVNode(x5, {
                    text: o.value
                  }, null, 8, ["text"])
                ]),
                _: 2
              }, 1024))), 128))
            ]),
            _: 1
          })) : (openBlock(), createElementBlock("div", d5, " No cookies "))
        ], 64))
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/data/httpHeaders.js
var e3 = [
  {
    name: "Accept",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept"
  },
  {
    name: "Accept-CH",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-CH"
  },
  {
    name: "Accept-CH-Lifetime",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-CH-Lifetime"
  },
  {
    name: "Accept-Charset",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Charset"
  },
  {
    name: "Accept-Encoding",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding"
  },
  {
    name: "Accept-Language",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language"
  },
  {
    name: "Accept-Patch",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Patch"
  },
  {
    name: "Accept-Post",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Post"
  },
  {
    name: "Accept-Ranges",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Ranges"
  },
  {
    name: "Access-Control-Allow-Credentials",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Credentials"
  },
  {
    name: "Access-Control-Allow-Headers",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Headers"
  },
  {
    name: "Access-Control-Allow-Methods",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Methods"
  },
  {
    name: "Access-Control-Allow-Origin",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"
  },
  {
    name: "Access-Control-Expose-Headers",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose-Headers"
  },
  {
    name: "Access-Control-Max-Age",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Max-Age"
  },
  {
    name: "Access-Control-Request-Headers",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Request-Headers"
  },
  {
    name: "Access-Control-Request-Method",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Request-Method"
  },
  {
    name: "Age",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Age"
  },
  {
    name: "Allow",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Allow"
  },
  {
    name: "Alt-Svc",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Alt-Svc"
  },
  {
    name: "Alt-Used",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Alt-Used"
  },
  {
    name: "Authorization",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization"
  },
  {
    name: "Cache-Control",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control"
  },
  {
    name: "Clear-Site-Data",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Clear-Site-Data"
  },
  {
    name: "Connection",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Connection"
  },
  {
    name: "Content-Disposition",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Disposition"
  },
  {
    name: "Content-DPR",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-DPR"
  },
  {
    name: "Content-Encoding",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Encoding"
  },
  {
    name: "Content-Language",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Language"
  },
  {
    name: "Content-Length",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Length"
  },
  {
    name: "Content-Location",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Location"
  },
  {
    name: "Content-Range",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range"
  },
  {
    name: "Content-Security-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy"
  },
  {
    name: "Content-Security-Policy-Report-Only",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy-Report-Only"
  },
  {
    name: "Content-Type",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Type"
  },
  {
    name: "Cookie",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cookie"
  },
  {
    name: "Critical-CH",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Critical-CH"
  },
  {
    name: "Cross-Origin-Embedder-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Embedder-Policy"
  },
  {
    name: "Cross-Origin-Opener-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Opener-Policy"
  },
  {
    name: "Cross-Origin-Resource-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Resource-Policy"
  },
  {
    name: "Date",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Date"
  },
  {
    name: "Device-Memory",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Device-Memory"
  },
  {
    name: "Digest",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Digest"
  },
  {
    name: "DNT",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/DNT"
  },
  {
    name: "Downlink",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Downlink"
  },
  {
    name: "DPR",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/DPR"
  },
  {
    name: "Early-Data",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Early-Data"
  },
  {
    name: "ECT",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ECT"
  },
  {
    name: "ETag",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag"
  },
  {
    name: "Expect",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Expect"
  },
  {
    name: "Expect-CT",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Expect-CT"
  },
  {
    name: "Expires",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Expires"
  },
  {
    name: "Forwarded",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Forwarded"
  },
  {
    name: "From",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/From"
  },
  {
    name: "Host",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Host"
  },
  {
    name: "If-Match",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Match"
  },
  {
    name: "If-Modified-Since",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Modified-Since"
  },
  {
    name: "If-None-Match",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-None-Match"
  },
  {
    name: "If-Range",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Range"
  },
  {
    name: "If-Unmodified-Since",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/If-Unmodified-Since"
  },
  {
    name: "Keep-Alive",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Keep-Alive"
  },
  {
    name: "Large-Allocation",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Large-Allocation"
  },
  {
    name: "Last-Modified",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Last-Modified"
  },
  {
    name: "Link",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link"
  },
  {
    name: "Location",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Location"
  },
  {
    name: "Max-Forwards",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Max-Forwards"
  },
  {
    name: "NEL",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/NEL"
  },
  {
    name: "Origin",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Origin"
  },
  {
    name: "Permissions-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Permissions-Policy"
  },
  {
    name: "Pragma",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Pragma"
  },
  {
    name: "Proxy-Authenticate",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Proxy-Authenticate"
  },
  {
    name: "Proxy-Authorization",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Proxy-Authorization"
  },
  {
    name: "Range",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Range"
  },
  {
    name: "Referer",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referer"
  },
  {
    name: "Referrer-Policy",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referrer-Policy"
  },
  {
    name: "Retry-After",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After"
  },
  {
    name: "RTT",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/RTT"
  },
  {
    name: "Save-Data",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Save-Data"
  },
  {
    name: "Sec-CH-Prefers-Reduced-Motion",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-Prefers-Reduced-Motion"
  },
  {
    name: "Sec-CH-Prefers-Reduced-Transparency",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-Prefers-Reduced-Transparency"
  },
  {
    name: "Sec-CH-UA",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA"
  },
  {
    name: "Sec-CH-UA-Arch",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Arch"
  },
  {
    name: "Sec-CH-UA-Bitness",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Bitness"
  },
  {
    name: "Sec-CH-UA-Full-Version",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Full-Version"
  },
  {
    name: "Sec-CH-UA-Full-Version-List",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Full-Version-List"
  },
  {
    name: "Sec-CH-UA-Mobile",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Mobile"
  },
  {
    name: "Sec-CH-UA-Model",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Model"
  },
  {
    name: "Sec-CH-UA-Platform",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Platform"
  },
  {
    name: "Sec-CH-UA-Platform-Version",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-CH-UA-Platform-Version"
  },
  {
    name: "Sec-Fetch-Dest",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-Dest"
  },
  {
    name: "Sec-Fetch-Mode",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-Mode"
  },
  {
    name: "Sec-Fetch-Site",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-Site"
  },
  {
    name: "Sec-Fetch-User",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-User"
  },
  {
    name: "Sec-GPC",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-GPC"
  },
  {
    name: "Sec-Purpose",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Purpose"
  },
  {
    name: "Sec-WebSocket-Accept",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-WebSocket-Accept"
  },
  {
    name: "Server",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Server"
  },
  {
    name: "Server-Timing",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Server-Timing"
  },
  {
    name: "Service-Worker-Navigation-Preload",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Service-Worker-Navigation-Preload"
  },
  {
    name: "Set-Cookie",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie"
  },
  {
    name: "SourceMap",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/SourceMap"
  },
  {
    name: "Strict-Transport-Security",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security"
  },
  {
    name: "TE",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/TE"
  },
  {
    name: "Timing-Allow-Origin",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Timing-Allow-Origin"
  },
  {
    name: "Tk",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Tk"
  },
  {
    name: "Trailer",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Trailer"
  },
  {
    name: "Transfer-Encoding",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Transfer-Encoding"
  },
  {
    name: "Upgrade",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Upgrade"
  },
  {
    name: "Upgrade-Insecure-Requests",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Upgrade-Insecure-Requests"
  },
  {
    name: "User-Agent",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent"
  },
  {
    name: "Vary",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Vary"
  },
  {
    name: "Via",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Via"
  },
  {
    name: "Viewport-Width",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Viewport-Width"
  },
  {
    name: "Want-Digest",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Want-Digest"
  },
  {
    name: "Warning",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Warning"
  },
  {
    name: "Width",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Width"
  },
  {
    name: "WWW-Authenticate",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/WWW-Authenticate"
  },
  {
    name: "X-Content-Type-Options",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options"
  },
  {
    name: "X-DNS-Prefetch-Control",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-DNS-Prefetch-Control"
  },
  {
    name: "Non-standard",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Non-standard"
  },
  {
    name: "X-Forwarded-For",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For"
  },
  {
    name: "Non-standard",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Non-standard"
  },
  {
    name: "X-Forwarded-Host",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-Host"
  },
  {
    name: "Non-standard",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Non-standard"
  },
  {
    name: "X-Forwarded-Proto",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-Proto"
  },
  {
    name: "Non-standard",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Non-standard"
  },
  {
    name: "X-Frame-Options",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options"
  },
  {
    name: "X-XSS-Protection",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-XSS-Protection"
  },
  {
    name: "Cf-Cache-Status",
    url: "https://developers.cloudflare.com/cache/concepts/default-cache-behavior/#cloudflare-cache-responses"
  },
  {
    name: "Cf-Ray",
    url: "https://developers.cloudflare.com/fundamentals/get-started/reference/http-request-headers/#cf-ray"
  },
  {
    name: "Report-To",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/report-to"
  },
  {
    name: "X-Cloud-Trace-Context",
    url: "https://cloud.google.com/trace/docs/trace-context#legacy-http-header"
  },
  {
    name: "Speculation-Rules",
    url: "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Speculation-Rules"
  }
];

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseHeaders.vue.js
var b3 = {
  key: 0,
  class: "max-h-[calc(100%-32px)] overflow-y-auto border-t"
};
var k4 = {
  key: 1,
  class: "text-c-3 bg-b-1 flex min-h-12 items-center justify-center rounded border px-4 text-sm"
};
var D2 = defineComponent({
  __name: "ResponseHeaders",
  props: {
    headers: {}
  },
  setup(C2) {
    const f5 = (o) => e3.find(
      (a2) => a2.name.toLowerCase() === o.toLowerCase()
    );
    return (o, a2) => (openBlock(), createBlock(P, {
      class: "overflow-auto",
      defaultOpen: false,
      itemCount: o.headers.length
    }, {
      title: withCtx(() => a2[0] || (a2[0] = [
        createTextVNode("Headers")
      ])),
      default: withCtx(() => [
        o.headers.length ? (openBlock(), createElementBlock("div", b3, [
          createVNode(g, {
            columns: ["minmax(auto, min-content)", "minmax(50%, 1fr)"],
            scroll: ""
          }, {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(o.headers, (t) => (openBlock(), createBlock(i2, {
                key: t.name,
                class: "group/row text-c-1"
              }, {
                default: withCtx(() => [
                  createVNode(x5, { class: "z-1 bg-b-1 sticky left-0 max-w-48 group-first/row:border-t-0" }, {
                    default: withCtx(() => {
                      var d6;
                      return [
                        typeof ((d6 = f5(t.name)) == null ? void 0 : d6.url) == "string" ? (openBlock(), createBlock(p5, {
                          key: 0,
                          class: "decoration-c-3",
                          href: f5(t.name).url
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(t.name), 1)
                          ]),
                          _: 2
                        }, 1032, ["href"])) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                          createTextVNode(toDisplayString(t.name), 1)
                        ], 64))
                      ];
                    }),
                    _: 2
                  }, 1024),
                  createVNode(x5, {
                    class: "z-0 group-first/row:border-t-0",
                    text: t.value
                  }, null, 8, ["text"])
                ]),
                _: 2
              }, 1024))), 128))
            ]),
            _: 1
          })
        ])) : (openBlock(), createElementBlock("div", k4, " No Headers "))
      ]),
      _: 1
    }, 8, ["itemCount"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseSection.vue2.js
var L2 = { class: "flex h-8 flex-1 items-center" };
var N2 = ["id", "role"];
var x6 = 2e5;
var J = defineComponent({
  __name: "ResponseSection",
  props: {
    numWorkspaceRequests: {},
    response: {}
  },
  setup(n3) {
    const m4 = computed(() => {
      var o;
      const e4 = (o = n3.response) == null ? void 0 : o.headers;
      return e4 ? Object.keys(e4).map((a2) => ({
        name: a2,
        value: e4[a2] ?? "",
        required: false
      })).filter(
        (a2) => ![
          "rest-api-client-content-length",
          "X-API-Client-Content-Length"
        ].includes(a2.name)
      ) : [];
    }), B6 = computed(
      () => {
        var e4;
        return ((e4 = n3.response) == null ? void 0 : e4.cookieHeaderKeys.flatMap((o) => {
          var t, r4;
          const a2 = (r4 = (t = n3.response) == null ? void 0 : t.headers) == null ? void 0 : r4[o];
          return a2 ? {
            name: o,
            value: a2,
            required: false
          } : [];
        })) ?? [];
      }
    ), b4 = ["Cookies", "Headers", "Body"], s7 = ref("All"), f5 = computed(() => ["All", ...b4]), p7 = computed(
      () => Object.fromEntries(
        f5.value.map((e4) => [e4, useId()])
      )
    ), R = computed(() => {
      var t, r4;
      if (!n3.response) return false;
      const e4 = ((t = n3.response.headers) == null ? void 0 : t["content-type"]) || ((r4 = n3.response.headers) == null ? void 0 : r4["Content-Type"]);
      return !e4 || (n3.response.size ?? 0) <= x6 || e4.includes("text/html") ? false : [
        // Text types
        "text/",
        // JSON types
        "application/json",
        "application/ld+json",
        "application/problem+json",
        "application/vnd.api+json",
        // XML types
        "application/xml",
        "application/atom+xml",
        "application/rss+xml",
        "application/problem+xml",
        // Other structured text
        "application/javascript",
        "application/ecmascript",
        "application/x-yaml",
        "application/yaml",
        // Source code
        "application/x-httpd-php",
        "application/x-sh",
        "application/x-perl",
        "application/x-python",
        "application/x-ruby",
        "application/x-java-source",
        // Form data
        "application/x-www-form-urlencoded"
      ].some((C2) => e4.includes(C2)) && (n3.response.size ?? 0) > x6;
    });
    return (e4, o) => (openBlock(), createBlock(_, { "aria-label": "Response" }, {
      title: withCtx(() => [
        createBaseVNode("div", L2, [
          createBaseVNode("div", {
            "aria-live": "polite",
            class: normalizeClass(["flex items-center", { "animate-response-heading": e4.response }])
          }, [
            o[1] || (o[1] = createBaseVNode("span", { class: "response-heading pointer-events-none absolute" }, " Response ", -1)),
            e4.response ? (openBlock(), createBlock($4, {
              key: 0,
              class: "animate-response-children",
              response: e4.response
            }, null, 8, ["response"])) : createCommentVNode("", true)
          ], 2),
          createVNode(f, {
            modelValue: s7.value,
            "onUpdate:modelValue": o[0] || (o[0] = (a2) => s7.value = a2),
            filterIds: p7.value,
            filters: f5.value
          }, null, 8, ["modelValue", "filterIds", "filters"])
        ])
      ]),
      default: withCtx(() => {
        var a2, t, r4;
        return [
          createBaseVNode("div", {
            id: p7.value.All,
            class: normalizeClass(["custom-scroll relative grid h-full justify-stretch divide-y", {
              "content-start": e4.response
            }]),
            role: s7.value === "All" && e4.response ? "tabpanel" : "none"
          }, [
            e4.response ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
              s7.value === "All" || s7.value === "Cookies" ? (openBlock(), createBlock(B5, {
                key: 0,
                id: p7.value.Cookies,
                cookies: B6.value,
                role: s7.value === "All" ? "none" : "tabpanel"
              }, null, 8, ["id", "cookies", "role"])) : createCommentVNode("", true),
              s7.value === "All" || s7.value === "Headers" ? (openBlock(), createBlock(D2, {
                key: 1,
                id: p7.value.Headers,
                headers: m4.value,
                role: s7.value === "All" ? "none" : "tabpanel"
              }, null, 8, ["id", "headers", "role"])) : createCommentVNode("", true),
              s7.value === "All" || s7.value === "Body" ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
                R.value && typeof ((a2 = e4.response) == null ? void 0 : a2.data) == "string" ? (openBlock(), createBlock(N, {
                  key: 0,
                  id: p7.value.Body,
                  content: e4.response.data,
                  data: (t = e4.response) == null ? void 0 : t.data,
                  headers: m4.value,
                  role: s7.value === "All" ? "none" : "tabpanel"
                }, null, 8, ["id", "content", "data", "headers", "role"])) : (openBlock(), createBlock(e2, {
                  key: 1,
                  id: p7.value.Body,
                  active: true,
                  data: (r4 = e4.response) == null ? void 0 : r4.data,
                  headers: m4.value,
                  role: s7.value === "All" ? "none" : "tabpanel",
                  title: "Body"
                }, null, 8, ["id", "data", "headers", "role"]))
              ], 64)) : createCommentVNode("", true)
            ], 64)) : (openBlock(), createBlock(r3, {
              key: 0,
              numWorkspaceRequests: e4.numWorkspaceRequests
            }, null, 8, ["numWorkspaceRequests"])),
            createVNode(p4)
          ], 10, N2)
        ];
      }),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/ResponseSection/ResponseSection.vue.js
var s6 = s2(J, [["__scopeId", "data-v-76ba3474"]]);

// node_modules/@scalar/api-client/dist/views/Request/Request.vue2.js
var T2 = { class: "flex h-full" };
var $5 = {
  key: 0,
  class: "flex h-full flex-1 flex-col"
};
var ae = defineComponent({
  __name: "Request",
  props: {
    invalidParams: {}
  },
  emits: ["newTab"],
  setup(j) {
    const { events: C2 } = je(), { isSidebarOpen: l5 } = m2(), V = je(), { layout: d6 } = s(), {
      activeCollection: r4,
      activeExample: m4,
      activeRequest: s7,
      activeWorkspace: c4,
      activeServer: p7,
      activeEnvVariables: v,
      activeEnvironment: f5,
      activeWorkspaceRequests: _2
    } = F(), { modalState: R, requestHistory: q2 } = V, w3 = computed(
      () => q2.findLast((o) => {
        var t;
        return o.request.uid === ((t = m4.value) == null ? void 0 : t.uid);
      })
    ), E3 = computed(
      () => {
        var o, t;
        return (d6 === "modal" ? (o = r4.value) == null ? void 0 : o.selectedSecuritySchemeUids : (t = s7.value) == null ? void 0 : t.selectedSecuritySchemeUids) ?? [];
      }
    );
    function U(o) {
      var t;
      C2.commandPalette.emit({
        commandName: "Import from cURL",
        metaData: {
          parsedCurl: C(o),
          collectionUid: (t = r4.value) == null ? void 0 : t.uid
        }
      });
    }
    return (o, t) => unref(r4) && unref(c4) ? (openBlock(), createElementBlock("div", {
      key: 0,
      class: normalizeClass(["bg-b-1 relative z-0 flex h-full flex-1 flex-col overflow-hidden pt-0", {
        "!mb-0 !mr-0 !border-0": unref(d6) === "modal"
      }])
    }, [
      createBaseVNode("div", T2, [
        unref(s7) ? (openBlock(), createElementBlock("div", $5, [
          createVNode(d3, {
            modelValue: unref(l5),
            "onUpdate:modelValue": t[0] || (t[0] = (i6) => isRef(l5) ? l5.value = i6 : null),
            collection: unref(r4),
            envVariables: unref(v),
            environment: unref(f5),
            operation: unref(s7),
            server: unref(p7),
            workspace: unref(c4),
            onHideModal: t[1] || (t[1] = () => unref(R).hide()),
            onImportCurl: U
          }, null, 8, ["modelValue", "collection", "envVariables", "environment", "operation", "server", "workspace"]),
          createVNode(u, null, {
            default: withCtx(() => [
              unref(m4) ? (openBlock(), createBlock(x, {
                key: 0,
                class: normalizeClass(["flex-1", [unref(l5) ? "sidebar-active-hide-layout" : ""]])
              }, {
                default: withCtx(() => {
                  var i6;
                  return [
                    createVNode(d, {
                      collection: unref(r4),
                      envVariables: unref(v),
                      environment: unref(f5),
                      example: unref(m4),
                      invalidParams: o.invalidParams,
                      operation: unref(s7),
                      selectedSecuritySchemeUids: E3.value,
                      server: unref(p7),
                      workspace: unref(c4)
                    }, null, 8, ["collection", "envVariables", "environment", "example", "invalidParams", "operation", "selectedSecuritySchemeUids", "server", "workspace"]),
                    createVNode(s6, {
                      numWorkspaceRequests: unref(_2).length,
                      response: (i6 = w3.value) == null ? void 0 : i6.response
                    }, null, 8, ["numWorkspaceRequests", "response"])
                  ];
                }),
                _: 1
              }, 8, ["class"])) : createCommentVNode("", true)
            ]),
            _: 1
          })
        ])) : (openBlock(), createBlock(r, { key: 1 }))
      ])
    ], 2)) : (openBlock(), createBlock(r, { key: 1 }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/Request.vue.js
var p6 = s2(ae, [["__scopeId", "data-v-08ce2525"]]);
export {
  p6 as default
};
//# sourceMappingURL=Request.vue-VTQXE6GV.js.map

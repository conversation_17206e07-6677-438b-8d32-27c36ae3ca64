{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionSettings.vue2.js", "../../../../@scalar/api-client/dist/views/Collection/CollectionSettings.vue.js"], "sourcesContent": ["import { defineComponent as D, openBlock as d, createElementBlock as c, Fragment as u, createElementVNode as t, createVNode as s, unref as l, createTextVNode as b, toDisplayString as N, withCtx as w } from \"vue\";\nimport { useModal as S, <PERSON>alarToggle as W, ScalarIcon as _, ScalarButton as T, ScalarModal as V } from \"@scalar/components\";\nimport { useRouter as A } from \"vue-router\";\nimport B from \"../../components/Sidebar/Actions/DeleteSidebarListElement.vue.js\";\nimport { useActiveEntities as E } from \"../../store/active-entities.js\";\nimport { PathId as k } from \"../../routes.js\";\nimport { useWorkspace as I } from \"../../store/store.js\";\nconst $ = { class: \"flex h-full w-full flex-col gap-12 px-1.5 pt-8\" }, L = { class: \"flex flex-col gap-2\" }, O = { class: \"bg-b-2 rounded-lg border text-sm\" }, P = { class: \"bg-b-1 -m-1/2 flex items-center justify-between gap-4 rounded-t-lg border p-3\" }, R = { class: \"text-c-1 flex items-center overflow-x-auto whitespace-nowrap py-1.5\" }, j = { class: \"flex items-center\" }, z = [\"href\"], F = { class: \"flex flex-col gap-4\" }, Z = { class: \"flex items-center justify-between rounded-lg border p-3 text-sm\" }, te = /* @__PURE__ */ D({\n  __name: \"CollectionSettings\",\n  setup(q) {\n    const { activeCollection: o, activeWorkspace: a, activeWorkspaceCollections: C } = E(), { collectionMutators: m } = I(), { replace: y } = A(), i = S();\n    function M() {\n      var n, e;\n      o.value && (n = o.value) != null && n.documentUrl && m.edit(\n        o.value.uid,\n        \"watchMode\",\n        !((e = o.value) != null && e.watchMode)\n      );\n    }\n    function U() {\n      if (!o.value || !a.value) return;\n      m.delete(o.value, a.value);\n      const n = C.value[0];\n      n && y({\n        name: \"collection\",\n        params: {\n          [k.Workspace]: a.value.uid,\n          [k.Collection]: n.uid\n        }\n      }), i.hide();\n    }\n    return (n, e) => {\n      var f, p, v, x, g;\n      return d(), c(u, null, [\n        t(\"div\", $, [\n          t(\"div\", L, [\n            e[5] || (e[5] = t(\"div\", { class: \"flex h-8 items-center\" }, [\n              t(\"h3\", { class: \"font-bold\" }, \"Features\")\n            ], -1)),\n            t(\"div\", O, [\n              t(\"div\", P, [\n                e[2] || (e[2] = t(\"div\", null, [\n                  t(\"h4\", null, \"Watch Mode\"),\n                  t(\"p\", { class: \"text-c-2 mt-1\" }, \" When enabled, the OpenAPI document will be polled for changes. The collection will be updated automatically. \")\n                ], -1)),\n                s(l(W), {\n                  class: \"w-4\",\n                  disabled: !((f = l(o)) != null && f.documentUrl),\n                  modelValue: ((p = l(o)) == null ? void 0 : p.watchMode) ?? !1,\n                  \"onUpdate:modelValue\": M\n                }, null, 8, [\"disabled\", \"modelValue\"])\n              ]),\n              t(\"div\", R, [\n                t(\"div\", j, [\n                  (v = l(o)) != null && v.documentUrl ? (d(), c(u, { key: 0 }, [\n                    e[3] || (e[3] = t(\"span\", { class: \"bg-b-2 sticky left-0 pl-3 pr-2\" }, \"Source\", -1)),\n                    t(\"a\", {\n                      class: \"text-c-2 group rounded pr-3 no-underline hover:underline\",\n                      href: l(o).documentUrl,\n                      target: \"_blank\"\n                    }, [\n                      b(N(l(o).documentUrl) + \" \", 1),\n                      s(l(_), {\n                        class: \"ml-1 hidden w-2.5 group-hover:inline\",\n                        icon: \"ExternalLink\"\n                      })\n                    ], 8, z)\n                  ], 64)) : (d(), c(u, { key: 1 }, [\n                    s(l(_), {\n                      class: \"text-c-2 ml-3 mr-2 w-4\",\n                      icon: \"NotAllowed\",\n                      size: \"sm\"\n                    }),\n                    e[4] || (e[4] = t(\"span\", { class: \"text-c-2 pr-3\" }, \" No URL configured. Try importing an OpenAPI document from an URL. \", -1))\n                  ], 64))\n                ])\n              ])\n            ])\n          ]),\n          t(\"div\", F, [\n            e[8] || (e[8] = t(\"h3\", { class: \"font-bold\" }, \"Danger Zone\", -1)),\n            t(\"div\", Z, [\n              e[7] || (e[7] = t(\"div\", null, [\n                t(\"h4\", null, \"Delete Collection\"),\n                t(\"p\", { class: \"text-c-2 mt-1\" }, \" Be careful, my friend. Once deleted, there is no way to recover the collection. \")\n              ], -1)),\n              s(l(T), {\n                class: \"custom-scroll h-8 gap-1.5 whitespace-nowrap px-2.5 font-medium shadow-none focus:outline-none\",\n                variant: \"danger\",\n                onClick: e[0] || (e[0] = (r) => l(i).show())\n              }, {\n                default: w(() => e[6] || (e[6] = [\n                  b(\" Delete Collection \")\n                ])),\n                _: 1\n              })\n            ])\n          ])\n        ]),\n        s(l(V), {\n          size: \"xxs\",\n          state: l(i),\n          title: `Delete ${(g = (x = l(o)) == null ? void 0 : x.info) == null ? void 0 : g.title}`\n        }, {\n          default: w(() => {\n            var r, h;\n            return [\n              s(B, {\n                variableName: ((h = (r = l(o)) == null ? void 0 : r.info) == null ? void 0 : h.title) ?? \"\",\n                warningMessage: \"This action cannot be undone.\",\n                onClose: e[1] || (e[1] = (G) => l(i).hide()),\n                onDelete: U\n              }, null, 8, [\"variableName\"])\n            ];\n          }),\n          _: 1\n        }, 8, [\"state\", \"title\"])\n      ], 64);\n    };\n  }\n});\nexport {\n  te as default\n};\n", "import o from \"./CollectionSettings.vue2.js\";\n/* empty css                        */\nimport t from \"../../_virtual/_plugin-vue_export-helper.js\";\nconst f = /* @__PURE__ */ t(o, [[\"__scopeId\", \"data-v-ea98df67\"]]);\nexport {\n  f as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,KAAI,EAAE,OAAO,iDAAiD;AAApE,IAAuE,IAAI,EAAE,OAAO,sBAAsB;AAA1G,IAA6G,IAAI,EAAE,OAAO,mCAAmC;AAA7J,IAAgK,IAAI,EAAE,OAAO,gFAAgF;AAA7P,IAAgQ,IAAI,EAAE,OAAO,sEAAsE;AAAnV,IAAsV,IAAI,EAAE,OAAO,oBAAoB;AAAvX,IAA0X,IAAI,CAAC,MAAM;AAArY,IAAwYC,KAAI,EAAE,OAAO,sBAAsB;AAA3a,IAA8a,IAAI,EAAE,OAAO,kEAAkE;AAA7f,IAAggB,KAAqB,gBAAE;AAAA,EACrhB,QAAQ;AAAA,EACR,MAAM,GAAG;AACP,UAAM,EAAE,kBAAkB,GAAG,iBAAiBC,IAAG,4BAA4B,EAAE,IAAI,EAAE,GAAG,EAAE,oBAAoB,EAAE,IAAI,GAAE,GAAG,EAAE,SAAS,EAAE,IAAI,UAAE,GAAG,IAAI,EAAE;AACrJ,aAAS,IAAI;AACX,UAAI,GAAG;AACP,QAAE,UAAU,IAAI,EAAE,UAAU,QAAQ,EAAE,eAAe,EAAE;AAAA,QACrD,EAAE,MAAM;AAAA,QACR;AAAA,QACA,GAAG,IAAI,EAAE,UAAU,QAAQ,EAAE;AAAA,MAC/B;AAAA,IACF;AACA,aAAS,IAAI;AACX,UAAI,CAAC,EAAE,SAAS,CAACA,GAAE,MAAO;AAC1B,QAAE,OAAO,EAAE,OAAOA,GAAE,KAAK;AACzB,YAAM,IAAI,EAAE,MAAM,CAAC;AACnB,WAAK,EAAE;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,CAAC,EAAE,SAAS,GAAGA,GAAE,MAAM;AAAA,UACvB,CAAC,EAAE,UAAU,GAAG,EAAE;AAAA,QACpB;AAAA,MACF,CAAC,GAAG,EAAE,KAAK;AAAA,IACb;AACA,WAAO,CAAC,GAAG,MAAM;AACf,UAAIC,IAAG,GAAG,GAAG,GAAG;AAChB,aAAO,UAAE,GAAG,mBAAE,UAAG,MAAM;AAAA,QACrB,gBAAE,OAAOH,IAAG;AAAA,UACV,gBAAE,OAAO,GAAG;AAAA,YACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,OAAO,EAAE,OAAO,wBAAwB,GAAG;AAAA,cAC3D,gBAAE,MAAM,EAAE,OAAO,YAAY,GAAG,UAAU;AAAA,YAC5C,GAAG,EAAE;AAAA,YACL,gBAAE,OAAO,GAAG;AAAA,cACV,gBAAE,OAAO,GAAG;AAAA,gBACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,OAAO,MAAM;AAAA,kBAC7B,gBAAE,MAAM,MAAM,YAAY;AAAA,kBAC1B,gBAAE,KAAK,EAAE,OAAO,gBAAgB,GAAG,gHAAgH;AAAA,gBACrJ,GAAG,EAAE;AAAA,gBACL,YAAE,MAAE,CAAC,GAAG;AAAA,kBACN,OAAO;AAAA,kBACP,UAAU,GAAGG,KAAI,MAAE,CAAC,MAAM,QAAQA,GAAE;AAAA,kBACpC,cAAc,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,cAAc;AAAA,kBAC3D,uBAAuB;AAAA,gBACzB,GAAG,MAAM,GAAG,CAAC,YAAY,YAAY,CAAC;AAAA,cACxC,CAAC;AAAA,cACD,gBAAE,OAAO,GAAG;AAAA,gBACV,gBAAE,OAAO,GAAG;AAAA,mBACT,IAAI,MAAE,CAAC,MAAM,QAAQ,EAAE,eAAe,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,oBAC3D,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,EAAE,OAAO,iCAAiC,GAAG,UAAU,EAAE;AAAA,oBACnF,gBAAE,KAAK;AAAA,sBACL,OAAO;AAAA,sBACP,MAAM,MAAE,CAAC,EAAE;AAAA,sBACX,QAAQ;AAAA,oBACV,GAAG;AAAA,sBACD,gBAAE,gBAAE,MAAE,CAAC,EAAE,WAAW,IAAI,KAAK,CAAC;AAAA,sBAC9B,YAAE,MAAE,CAAC,GAAG;AAAA,wBACN,OAAO;AAAA,wBACP,MAAM;AAAA,sBACR,CAAC;AAAA,oBACH,GAAG,GAAG,CAAC;AAAA,kBACT,GAAG,EAAE,MAAM,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,oBAC/B,YAAE,MAAE,CAAC,GAAG;AAAA,sBACN,OAAO;AAAA,sBACP,MAAM;AAAA,sBACN,MAAM;AAAA,oBACR,CAAC;AAAA,oBACD,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,QAAQ,EAAE,OAAO,gBAAgB,GAAG,uEAAuE,EAAE;AAAA,kBACjI,GAAG,EAAE;AAAA,gBACP,CAAC;AAAA,cACH,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,UACD,gBAAE,OAAOF,IAAG;AAAA,YACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,MAAM,EAAE,OAAO,YAAY,GAAG,eAAe,EAAE;AAAA,YACjE,gBAAE,OAAO,GAAG;AAAA,cACV,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,OAAO,MAAM;AAAA,gBAC7B,gBAAE,MAAM,MAAM,mBAAmB;AAAA,gBACjC,gBAAE,KAAK,EAAE,OAAO,gBAAgB,GAAG,mFAAmF;AAAA,cACxH,GAAG,EAAE;AAAA,cACL,YAAE,MAAE,CAAC,GAAG;AAAA,gBACN,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,MAAE,CAAC,EAAE,KAAK;AAAA,cAC5C,GAAG;AAAA,gBACD,SAAS,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,kBAC/B,gBAAE,qBAAqB;AAAA,gBACzB,EAAE;AAAA,gBACF,GAAG;AAAA,cACL,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,QACD,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,MAAM;AAAA,UACN,OAAO,MAAE,CAAC;AAAA,UACV,OAAO,WAAW,KAAK,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,OAAO,SAAS,EAAE,KAAK;AAAA,QACxF,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AACf,gBAAI,GAAG;AACP,mBAAO;AAAA,cACL,YAAEG,IAAG;AAAA,gBACH,gBAAgB,KAAK,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE,SAAS,OAAO,SAAS,EAAE,UAAU;AAAA,gBACzF,gBAAgB;AAAA,gBAChB,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,MAAE,CAAC,EAAE,KAAK;AAAA,gBAC1C,UAAU;AAAA,cACZ,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC;AAAA,YAC9B;AAAA,UACF,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,GAAG,CAAC,SAAS,OAAO,CAAC;AAAA,MAC1B,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AACF,CAAC;;;ACrHD,IAAMC,KAAoB,EAAE,IAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;", "names": ["$", "F", "a", "f", "w", "f"]}
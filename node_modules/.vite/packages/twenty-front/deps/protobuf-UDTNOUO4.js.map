{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/protobuf/protobuf.ts\nvar namedLiterals = [\"true\", \"false\"];\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  autoCloseBefore: \".,=}])>' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".proto\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords: [\n    \"syntax\",\n    \"import\",\n    \"weak\",\n    \"public\",\n    \"package\",\n    \"option\",\n    \"repeated\",\n    \"oneof\",\n    \"map\",\n    \"reserved\",\n    \"to\",\n    \"max\",\n    \"enum\",\n    \"message\",\n    \"service\",\n    \"rpc\",\n    \"stream\",\n    \"returns\",\n    \"package\",\n    \"optional\",\n    \"true\",\n    \"false\"\n  ],\n  builtinTypes: [\n    \"double\",\n    \"float\",\n    \"int32\",\n    \"int64\",\n    \"uint32\",\n    \"uint64\",\n    \"sint32\",\n    \"sint64\",\n    \"fixed32\",\n    \"fixed64\",\n    \"sfixed32\",\n    \"sfixed64\",\n    \"bool\",\n    \"string\",\n    \"bytes\"\n  ],\n  operators: [\"=\", \"+\", \"-\"],\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  identifier: /[a-zA-Z]\\w*/,\n  fullIdentifier: /@identifier(?:\\s*\\.\\s*@identifier)*/,\n  optionName: /(?:@identifier|\\(\\s*@fullIdentifier\\s*\\))(?:\\s*\\.\\s*@identifier)*/,\n  messageName: /@identifier/,\n  enumName: /@identifier/,\n  messageType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@messageName/,\n  enumType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@enumName/,\n  floatLit: /[0-9]+\\s*\\.\\s*[0-9]*(?:@exponent)?|[0-9]+@exponent|\\.[0-9]+(?:@exponent)?/,\n  exponent: /[eE]\\s*[+-]?\\s*[0-9]+/,\n  boolLit: /true\\b|false\\b/,\n  decimalLit: /[1-9][0-9]*/,\n  octalLit: /0[0-7]*/,\n  hexLit: /0[xX][0-9a-fA-F]+/,\n  type: /double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes|@messageType|@enumType/,\n  keyType: /int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/syntax/, \"keyword\"],\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        /(\")(proto3)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto3\" }]\n      ],\n      [\n        /(\")(proto2)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto2\" }]\n      ],\n      [\n        // If no `syntax` provided, regarded as proto2\n        /.*?/,\n        { token: \"\", switchTo: \"@topLevel.proto2\" }\n      ]\n    ],\n    topLevel: [\n      // whitespace\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/[;.]/, \"delimiter\"],\n      [\n        /@fullIdentifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            service: { token: \"keyword\", next: \"@serviceDecl.$S2\" },\n            extend: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@extendDecl.$S2\" }\n              }\n            },\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    enumDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@enumBody.$S2\" }]\n    ],\n    enumBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/@identifier/, \"identifier\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    messageDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }]\n    ],\n    messageBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        \"(map)(s*)(<)\",\n        [\"keyword\", \"white\", { token: \"@brackets\", bracket: \"@open\", next: \"@map.$S2\" }]\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            oneof: { token: \"keyword\", next: \"@oneofDecl.$S2\" },\n            extensions: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@reserved.$S2\" }\n              }\n            },\n            reserved: { token: \"keyword\", next: \"@reserved.$S2\" },\n            \"(?:repeated|optional)\": { token: \"keyword\", next: \"@field.$S2\" },\n            required: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@field.$S2\" }\n              }\n            },\n            \"$S2==proto3\": { token: \"@rematch\", next: \"@field.$S2\" }\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    extendDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@extendBody.$S2\" }]\n    ],\n    extendBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(?:repeated|optional|required)/, \"keyword\", \"@field.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    options: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/\\]/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    option: [\n      { include: \"@whitespace\" },\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\", \"@pop\"]\n    ],\n    oneofDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@oneofBody.$S2\" }]\n    ],\n    oneofBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", \"delimiter\"]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    reserved: [\n      { include: \"@whitespace\" },\n      [/,/, \"delimiter\"],\n      [/;/, \"delimiter\", \"@pop\"],\n      { include: \"@constant\" },\n      [/to\\b|max\\b/, \"keyword\"]\n    ],\n    map: [\n      { include: \"@whitespace\" },\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/,/, \"delimiter\"],\n      [/>/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"identifier\" }]\n    ],\n    field: [\n      { include: \"@whitespace\" },\n      [\n        \"group\",\n        {\n          cases: {\n            \"$S2==proto2\": { token: \"keyword\", switchTo: \"@groupDecl.$S2\" }\n          }\n        }\n      ],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", { token: \"delimiter\", next: \"@pop\" }]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ]\n    ],\n    groupDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [\"=\", \"operator\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }],\n      { include: \"@constant\" }\n    ],\n    type: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\", \"@pop\"],\n      [/./, \"delimiter\"]\n    ],\n    identifier: [{ include: \"@whitespace\" }, [/@identifier/, \"identifier\", \"@pop\"]],\n    serviceDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@serviceBody.$S2\" }]\n    ],\n    serviceBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/rpc\\b/, \"keyword\", \"@rpc.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    rpc: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@request.$S2\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@methodOptions.$S2\" }],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    request: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@returns.$S2\" }]\n    ],\n    returns: [\n      { include: \"@whitespace\" },\n      [/returns\\b/, \"keyword\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@response.$S2\" }]\n    ],\n    response: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@rpc.$S2\" }]\n    ],\n    methodOptions: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [\"option\", \"keyword\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    constant: [\n      [\"@boolLit\", \"keyword.constant\"],\n      [\"@hexLit\", \"number.hex\"],\n      [\"@octalLit\", \"number.octal\"],\n      [\"@decimalLit\", \"number\"],\n      [\"@floatLit\", \"number.float\"],\n      [/(\"([^\"\\\\]|\\\\.)*|'([^'\\\\]|\\\\.)*)$/, \"string.invalid\"],\n      // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringSingle\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@prototext\" }],\n      [/identifier/, \"identifier\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    prototext: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/@identifier/, \"identifier\"],\n      [/[:;]/, \"delimiter\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,eACA,MAiCA;AA3CJ;AAAA;AASA,IAAI,gBAAgB,CAAC,QAAQ,OAAO;AACpC,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,QAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC7C;AAAA,MACA,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,QAChB,uBAAuB,IAAI,OAAO,4DAA4D;AAAA,QAC9F,uBAAuB,IAAI,OAAO,wCAAwC;AAAA,MAC5E;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MACpD;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB;AAAA,MACA,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,UAAU;AAAA,MACV,aAAa;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,UAAU,SAAS;AAAA,UACpB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA,CAAC,gBAAgB,UAAU,EAAE,OAAO,gBAAgB,UAAU,mBAAmB,CAAC;AAAA,UACpF;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,gBAAgB,UAAU,EAAE,OAAO,gBAAgB,UAAU,mBAAmB,CAAC;AAAA,UACpF;AAAA,UACA;AAAA;AAAA,YAEE;AAAA,YACA,EAAE,OAAO,IAAI,UAAU,mBAAmB;AAAA,UAC5C;AAAA,QACF;AAAA,QACA,UAAU;AAAA;AAAA,UAER,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,QAAQ,WAAW;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,WAAW,MAAM,cAAc;AAAA,gBAChD,MAAM,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,gBAChD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,gBACtD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,gBACtD,QAAQ;AAAA,kBACN,OAAO;AAAA,oBACL,eAAe,EAAE,OAAO,WAAW,MAAM,kBAAkB;AAAA,kBAC7D;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,iBAAiB;AAAA,UACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,gBAAgB,CAAC;AAAA,QAC3E;AAAA,QACA,UAAU;AAAA,UACR,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY,WAAW,aAAa;AAAA,UACrC,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,aAAa;AAAA,UACX,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,iBAAiB;AAAA,UACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,QAC9E;AAAA,QACA,aAAa;AAAA,UACX,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA,CAAC,WAAW,SAAS,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,UACjF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,WAAW,MAAM,cAAc;AAAA,gBAChD,MAAM,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,gBAChD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,gBACtD,OAAO,EAAE,OAAO,WAAW,MAAM,iBAAiB;AAAA,gBAClD,YAAY;AAAA,kBACV,OAAO;AAAA,oBACL,eAAe,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,kBAC3D;AAAA,gBACF;AAAA,gBACA,UAAU,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,gBACpD,yBAAyB,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,gBAChE,UAAU;AAAA,kBACR,OAAO;AAAA,oBACL,eAAe,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,kBACxD;AAAA,gBACF;AAAA,gBACA,eAAe,EAAE,OAAO,YAAY,MAAM,aAAa;AAAA,cACzD;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,YAAY;AAAA,UACV,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,iBAAiB;AAAA,UACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,kBAAkB,CAAC;AAAA,QAC7E;AAAA,QACA,YAAY;AAAA,UACV,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,kCAAkC,WAAW,YAAY;AAAA,UAC1D,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,SAAS;AAAA,UACP,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,QAAQ,qBAAqB;AAAA,UAC9B,CAAC,KAAK,UAAU;AAAA,UAChB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAChE;AAAA,QACA,QAAQ;AAAA,UACN,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,QAAQ,qBAAqB;AAAA,UAC9B,CAAC,KAAK,YAAY,MAAM;AAAA,QAC1B;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,iBAAiB,CAAC;AAAA,QAC5E;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,yBAAyB,CAAC,cAAc,SAAS,WAAW,CAAC;AAAA,UAC9D;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,iBAAiB;AAAA,gBACjB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,UAAU;AAAA,UACR,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,cAAc,SAAS;AAAA,QAC1B;AAAA,QACA,KAAK;AAAA,UACH,EAAE,SAAS,cAAc;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,iBAAiB;AAAA,gBACjB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,aAAa,CAAC;AAAA,QACzE;AAAA,QACA,OAAO;AAAA,UACL,EAAE,SAAS,cAAc;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,eAAe,EAAE,OAAO,WAAW,UAAU,iBAAiB;AAAA,cAChE;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,yBAAyB,CAAC,cAAc,SAAS,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,UACvF;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,iBAAiB;AAAA,gBACjB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,KAAK,UAAU;AAAA,UAChB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,UAC5E,EAAE,SAAS,YAAY;AAAA,QACzB;AAAA,QACA,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,mBAAmB,MAAM;AAAA,UACzC,CAAC,KAAK,WAAW;AAAA,QACnB;AAAA,QACA,YAAY,CAAC,EAAE,SAAS,cAAc,GAAG,CAAC,eAAe,cAAc,MAAM,CAAC;AAAA,QAC9E,aAAa;AAAA,UACX,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,QAC9E;AAAA,QACA,aAAa;AAAA,UACX,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY,WAAW,aAAa;AAAA,UACrC,CAAC,SAAS,WAAW,UAAU;AAAA,UAC/B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,KAAK;AAAA,UACH,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,eAAe,CAAC;AAAA,UACzE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,qBAAqB,CAAC;AAAA,UAC1E,CAAC,KAAK,aAAa,MAAM;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,UACP,EAAE,SAAS,cAAc;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,WAAW,MAAM,YAAY;AAAA,gBAC9C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,eAAe,CAAC;AAAA,QAC5E;AAAA,QACA,SAAS;AAAA,UACP,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,aAAa,SAAS;AAAA,UACvB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,gBAAgB,CAAC;AAAA,QAC5E;AAAA,QACA,UAAU;AAAA,UACR,EAAE,SAAS,cAAc;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,WAAW,MAAM,YAAY;AAAA,gBAC9C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,WAAW,CAAC;AAAA,QACxE;AAAA,QACA,eAAe;AAAA,UACb,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,UAAU,SAAS;AAAA,UACpB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,QAAQ,qBAAqB;AAAA,UAC9B,CAAC,KAAK,UAAU;AAAA,UAChB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,UAE3B,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,UAAU;AAAA,UACR,CAAC,YAAY,kBAAkB;AAAA,UAC/B,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,aAAa,cAAc;AAAA,UAC5B,CAAC,oCAAoC,gBAAgB;AAAA;AAAA,UAErD,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,UAClE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,gBAAgB,CAAC;AAAA,UACxE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,UAClE,CAAC,cAAc,YAAY;AAAA,QAC7B;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,OAAO;AAAA,UACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,eAAe,YAAY;AAAA,UAC5B,CAAC,QAAQ,WAAW;AAAA,UACpB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
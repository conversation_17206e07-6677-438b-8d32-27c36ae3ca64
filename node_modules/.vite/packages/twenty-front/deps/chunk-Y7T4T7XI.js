import {
  __extends,
  init_tslib_es6
} from "./chunk-I4HVGP7M.js";
import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/ts-invariant/lib/invariant.js
function invariant(condition, message) {
  if (!condition) {
    throw new InvariantError(message);
  }
}
function wrapConsoleMethod(name) {
  return function() {
    if (verbosityLevels.indexOf(name) >= verbosityLevel) {
      var method = console[name] || console.log;
      return method.apply(console, arguments);
    }
  };
}
function setVerbosity(level) {
  var old = verbosityLevels[verbosityLevel];
  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));
  return old;
}
var genericMessage, _a, setPrototypeOf, InvariantError, verbosityLevels, verbosityLevel;
var init_invariant = __esm({
  "node_modules/ts-invariant/lib/invariant.js"() {
    init_tslib_es6();
    genericMessage = "Invariant Violation";
    _a = Object.setPrototypeOf;
    setPrototypeOf = _a === void 0 ? function(obj, proto) {
      obj.__proto__ = proto;
      return obj;
    } : _a;
    InvariantError = /** @class */
    function(_super) {
      __extends(InvariantError2, _super);
      function InvariantError2(message) {
        if (message === void 0) {
          message = genericMessage;
        }
        var _this = _super.call(this, typeof message === "number" ? genericMessage + ": " + message + " (see https://github.com/apollographql/invariant-packages)" : message) || this;
        _this.framesToPop = 1;
        _this.name = genericMessage;
        setPrototypeOf(_this, InvariantError2.prototype);
        return _this;
      }
      return InvariantError2;
    }(Error);
    verbosityLevels = ["debug", "log", "warn", "error", "silent"];
    verbosityLevel = verbosityLevels.indexOf("log");
    (function(invariant4) {
      invariant4.debug = wrapConsoleMethod("debug");
      invariant4.log = wrapConsoleMethod("log");
      invariant4.warn = wrapConsoleMethod("warn");
      invariant4.error = wrapConsoleMethod("error");
    })(invariant || (invariant = {}));
  }
});

// node_modules/@apollo/client/utilities/globals/maybe.js
function maybe(thunk) {
  try {
    return thunk();
  } catch (_a2) {
  }
}
var init_maybe = __esm({
  "node_modules/@apollo/client/utilities/globals/maybe.js"() {
  }
});

// node_modules/@apollo/client/utilities/common/makeUniqueId.js
function makeUniqueId(prefix) {
  var count = prefixCounts.get(prefix) || 1;
  prefixCounts.set(prefix, count + 1);
  return "".concat(prefix, ":").concat(count, ":").concat(Math.random().toString(36).slice(2));
}
var prefixCounts;
var init_makeUniqueId = __esm({
  "node_modules/@apollo/client/utilities/common/makeUniqueId.js"() {
    prefixCounts = /* @__PURE__ */ new Map();
  }
});

// node_modules/@apollo/client/utilities/common/stringifyForDisplay.js
function stringifyForDisplay(value, space) {
  if (space === void 0) {
    space = 0;
  }
  var undefId = makeUniqueId("stringifyForDisplay");
  return JSON.stringify(value, function(key, value2) {
    return value2 === void 0 ? undefId : value2;
  }, space).split(JSON.stringify(undefId)).join("<undefined>");
}
var init_stringifyForDisplay = __esm({
  "node_modules/@apollo/client/utilities/common/stringifyForDisplay.js"() {
    init_makeUniqueId();
  }
});

// node_modules/@apollo/client/version.js
var version;
var init_version = __esm({
  "node_modules/@apollo/client/version.js"() {
    version = "3.11.4";
  }
});

// node_modules/@apollo/client/utilities/globals/global.js
var global_default;
var init_global = __esm({
  "node_modules/@apollo/client/utilities/globals/global.js"() {
    init_maybe();
    global_default = maybe(function() {
      return globalThis;
    }) || maybe(function() {
      return window;
    }) || maybe(function() {
      return self;
    }) || maybe(function() {
      return global;
    }) || // We don't expect the Function constructor ever to be invoked at runtime, as
    // long as at least one of globalThis, window, self, or global is defined, so
    // we are under no obligation to make it easy for static analysis tools to
    // detect syntactic usage of the Function constructor. If you think you can
    // improve your static analysis to detect this obfuscation, think again. This
    // is an arms race you cannot win, at least not in JavaScript.
    maybe(function() {
      return maybe.constructor("return this")();
    });
  }
});

// node_modules/@apollo/client/utilities/globals/invariantWrappers.js
function wrap(fn) {
  return function(message) {
    var args = [];
    for (var _i = 1; _i < arguments.length; _i++) {
      args[_i - 1] = arguments[_i];
    }
    if (typeof message === "number") {
      var arg0 = message;
      message = getHandledErrorMsg(arg0);
      if (!message) {
        message = getFallbackErrorMsg(arg0, args);
        args = [];
      }
    }
    fn.apply(void 0, [message].concat(args));
  };
}
function newInvariantError(message) {
  var optionalParams = [];
  for (var _i = 1; _i < arguments.length; _i++) {
    optionalParams[_i - 1] = arguments[_i];
  }
  return new InvariantError(getHandledErrorMsg(message, optionalParams) || getFallbackErrorMsg(message, optionalParams));
}
function stringify(arg) {
  if (typeof arg == "string") {
    return arg;
  }
  try {
    return stringifyForDisplay(arg, 2).slice(0, 1e3);
  } catch (_a2) {
    return "<non-serializable>";
  }
}
function getHandledErrorMsg(message, messageArgs) {
  if (messageArgs === void 0) {
    messageArgs = [];
  }
  if (!message)
    return;
  return global_default[ApolloErrorMessageHandler] && global_default[ApolloErrorMessageHandler](message, messageArgs.map(stringify));
}
function getFallbackErrorMsg(message, messageArgs) {
  if (messageArgs === void 0) {
    messageArgs = [];
  }
  if (!message)
    return;
  return "An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({
    version,
    message,
    args: messageArgs.map(stringify)
  })));
}
var invariant2, ApolloErrorMessageHandler;
var init_invariantWrappers = __esm({
  "node_modules/@apollo/client/utilities/globals/invariantWrappers.js"() {
    init_invariant();
    init_version();
    init_global();
    init_stringifyForDisplay();
    invariant2 = Object.assign(function invariant3(condition, message) {
      var args = [];
      for (var _i = 2; _i < arguments.length; _i++) {
        args[_i - 2] = arguments[_i];
      }
      if (!condition) {
        invariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));
      }
    }, {
      debug: wrap(invariant.debug),
      log: wrap(invariant.log),
      warn: wrap(invariant.warn),
      error: wrap(invariant.error)
    });
    ApolloErrorMessageHandler = Symbol.for("ApolloErrorMessageHandler_" + version);
  }
});

// node_modules/@apollo/client/utilities/globals/index.js
var DEV;
var init_globals = __esm({
  "node_modules/@apollo/client/utilities/globals/index.js"() {
    init_invariantWrappers();
    init_maybe();
    init_global();
    DEV = globalThis.__DEV__ !== false;
  }
});

export {
  setVerbosity,
  init_invariant,
  version,
  init_version,
  maybe,
  global_default,
  makeUniqueId,
  init_makeUniqueId,
  stringifyForDisplay,
  init_stringifyForDisplay,
  invariant2 as invariant,
  newInvariantError,
  ApolloErrorMessageHandler,
  init_invariantWrappers,
  DEV,
  init_globals
};
//# sourceMappingURL=chunk-Y7T4T7XI.js.map

{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/razor/razor.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/basic-languages/razor/razor.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/@@@@/],\n      // text\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/[^<@]+/]\n      // text\n    ],\n    doctype: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/@[^@]/, { token: \"@rematch\", switchTo: \"@razorInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /@[^@]/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@razorInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    razorInSimpleState: [\n      [/@\\*/, \"comment.cs\", \"@razorBlockCommentTopLevel\"],\n      [/@[{(]/, \"metatag.cs\", \"@razorRootTopLevel\"],\n      [/(@)(\\s*[\\w]+)/, [\"metatag.cs\", { token: \"identifier.cs\", switchTo: \"@$S2.$S3\" }]],\n      [/[})]/, { token: \"metatag.cs\", switchTo: \"@$S2.$S3\" }],\n      [/\\*@/, { token: \"comment.cs\", switchTo: \"@$S2.$S3\" }]\n    ],\n    razorInEmbeddedState: [\n      [/@\\*/, \"comment.cs\", \"@razorBlockCommentTopLevel\"],\n      [/@[{(]/, \"metatag.cs\", \"@razorRootTopLevel\"],\n      [\n        /(@)(\\s*[\\w]+)/,\n        [\n          \"metatag.cs\",\n          {\n            token: \"identifier.cs\",\n            switchTo: \"@$S2.$S3\",\n            nextEmbedded: \"$S3\"\n          }\n        ]\n      ],\n      [\n        /[})]/,\n        {\n          token: \"metatag.cs\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      [\n        /\\*@/,\n        {\n          token: \"comment.cs\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ]\n    ],\n    razorBlockCommentTopLevel: [\n      [/\\*@/, \"@rematch\", \"@pop\"],\n      [/[^*]+/, \"comment.cs\"],\n      [/./, \"comment.cs\"]\n    ],\n    razorBlockComment: [\n      [/\\*@/, \"comment.cs\", \"@pop\"],\n      [/[^*]+/, \"comment.cs\"],\n      [/./, \"comment.cs\"]\n    ],\n    razorRootTopLevel: [\n      [/\\{/, \"delimiter.bracket.cs\", \"@razorRoot\"],\n      [/\\(/, \"delimiter.parenthesis.cs\", \"@razorRoot\"],\n      [/[})]/, \"@rematch\", \"@pop\"],\n      { include: \"razorCommon\" }\n    ],\n    razorRoot: [\n      [/\\{/, \"delimiter.bracket.cs\", \"@razorRoot\"],\n      [/\\(/, \"delimiter.parenthesis.cs\", \"@razorRoot\"],\n      [/\\}/, \"delimiter.bracket.cs\", \"@pop\"],\n      [/\\)/, \"delimiter.parenthesis.cs\", \"@pop\"],\n      { include: \"razorCommon\" }\n    ],\n    razorCommon: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@razorKeywords\": { token: \"keyword.cs\" },\n            \"@default\": \"identifier.cs\"\n          }\n        }\n      ],\n      // brackets\n      [/[\\[\\]]/, \"delimiter.array.cs\"],\n      // whitespace\n      [/[ \\t\\r\\n]+/],\n      // comments\n      [/\\/\\/.*$/, \"comment.cs\"],\n      [/@\\*/, \"comment.cs\", \"@razorBlockComment\"],\n      // strings\n      [/\"([^\"]*)\"/, \"string.cs\"],\n      [/'([^']*)'/, \"string.cs\"],\n      // simple html\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([\\w\\-]+)(>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<\\/)([\\w\\-]+)(>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      // delimiters\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,]/, \"delimiter.cs\"],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.cs\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.cs\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.cs\"],\n      [/0[0-7']*[0-7]/, \"number.octal.cs\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.cs\"],\n      [/\\d[\\d']*/, \"number.cs\"],\n      [/\\d/, \"number.cs\"]\n    ]\n  },\n  razorKeywords: [\n    \"abstract\",\n    \"as\",\n    \"async\",\n    \"await\",\n    \"base\",\n    \"bool\",\n    \"break\",\n    \"by\",\n    \"byte\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"checked\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"decimal\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"double\",\n    \"descending\",\n    \"explicit\",\n    \"event\",\n    \"extern\",\n    \"else\",\n    \"enum\",\n    \"false\",\n    \"finally\",\n    \"fixed\",\n    \"float\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"goto\",\n    \"group\",\n    \"if\",\n    \"implicit\",\n    \"in\",\n    \"int\",\n    \"interface\",\n    \"internal\",\n    \"into\",\n    \"is\",\n    \"lock\",\n    \"long\",\n    \"nameof\",\n    \"new\",\n    \"null\",\n    \"namespace\",\n    \"object\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"orderby\",\n    \"params\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"ref\",\n    \"return\",\n    \"switch\",\n    \"struct\",\n    \"sbyte\",\n    \"sealed\",\n    \"short\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"static\",\n    \"string\",\n    \"select\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"uint\",\n    \"ulong\",\n    \"unchecked\",\n    \"unsafe\",\n    \"ushort\",\n    \"using\",\n    \"var\",\n    \"virtual\",\n    \"volatile\",\n    \"void\",\n    \"when\",\n    \"while\",\n    \"where\",\n    \"yield\",\n    \"model\",\n    \"inject\"\n    // Razor specific\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;;;;;AAAA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,gBAkBA,MA2CA;AAxFJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,QAAQ,KAAK;AAAA,MAC9B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,QAAQ,KAAK;AAAA,QACd,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,UACE,YAAY,IAAI;AAAA,YACd,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,UACA,WAAW;AAAA,UACX,QAAQ;AAAA,YACN,cAAc,2BAA2B,UAAU,aAAa;AAAA,UAClE;AAAA,QACF;AAAA,QACA;AAAA,UACE,YAAY,IAAI;AAAA,YACd,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,UACA,QAAQ,EAAE,cAAc,2BAA2B,UAAU,aAAa,OAAO;AAAA,QACnF;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA;AAAA;AAAA,MAGd,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,CAAC,MAAM;AAAA;AAAA,UAEP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,2BAA2B,CAAC;AAAA,UACrE,CAAC,aAAa,gBAAgB,UAAU;AAAA,UACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,UACnC,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,UACtE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,UAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,UACxE,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC9E,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC/E,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,QAAQ;AAAA;AAAA,QAEX;AAAA,QACA,SAAS;AAAA,UACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,UACxE,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,QAC9B;AAAA,QACA,SAAS;AAAA,UACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,UACxE,CAAC,OAAO,gBAAgB,MAAM;AAAA,UAC9B,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,sBAAsB;AAAA,QAC9B;AAAA,QACA,UAAU;AAAA,UACR,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,+BAA+B,CAAC;AAAA,UACzE,CAAC,QAAQ,kBAAkB,MAAM;AAAA,UACjC,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,QAEf;AAAA;AAAA;AAAA,QAGA,QAAQ;AAAA,UACN,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,6BAA6B,CAAC;AAAA,UACvE,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,UAC7C,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,iBAAiB;AAAA,UACf;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,UAC3C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA,QACA,gBAAgB;AAAA,UACd;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QACzE;AAAA;AAAA;AAAA;AAAA,QAIA,OAAO;AAAA,UACL,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,UACtE,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,UAC5C,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,gBAAgB;AAAA,UACd;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,UAC1C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,qBAAqB;AAAA,UACnB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,aAAa,iBAAiB;AAAA,UAC/B,CAAC,WAAW,gBAAgB;AAAA,UAC5B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA,QACA,eAAe;AAAA,UACb;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QACxE;AAAA;AAAA,QAEA,oBAAoB;AAAA,UAClB,CAAC,OAAO,cAAc,4BAA4B;AAAA,UAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,UAC5C,CAAC,iBAAiB,CAAC,cAAc,EAAE,OAAO,iBAAiB,UAAU,WAAW,CAAC,CAAC;AAAA,UAClF,CAAC,QAAQ,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,UACtD,CAAC,OAAO,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,QACvD;AAAA,QACA,sBAAsB;AAAA,UACpB,CAAC,OAAO,cAAc,4BAA4B;AAAA,UAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,UAC5C;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,UAAU;AAAA,gBACV,cAAc;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,QACA,2BAA2B;AAAA,UACzB,CAAC,OAAO,YAAY,MAAM;AAAA,UAC1B,CAAC,SAAS,YAAY;AAAA,UACtB,CAAC,KAAK,YAAY;AAAA,QACpB;AAAA,QACA,mBAAmB;AAAA,UACjB,CAAC,OAAO,cAAc,MAAM;AAAA,UAC5B,CAAC,SAAS,YAAY;AAAA,UACtB,CAAC,KAAK,YAAY;AAAA,QACpB;AAAA,QACA,mBAAmB;AAAA,UACjB,CAAC,MAAM,wBAAwB,YAAY;AAAA,UAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,UAC/C,CAAC,QAAQ,YAAY,MAAM;AAAA,UAC3B,EAAE,SAAS,cAAc;AAAA,QAC3B;AAAA,QACA,WAAW;AAAA,UACT,CAAC,MAAM,wBAAwB,YAAY;AAAA,UAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,UAC/C,CAAC,MAAM,wBAAwB,MAAM;AAAA,UACrC,CAAC,MAAM,4BAA4B,MAAM;AAAA,UACzC,EAAE,SAAS,cAAc;AAAA,QAC3B;AAAA,QACA,aAAa;AAAA,UACX;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,kBAAkB,EAAE,OAAO,aAAa;AAAA,gBACxC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,UAAU,oBAAoB;AAAA;AAAA,UAE/B,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,OAAO,cAAc,oBAAoB;AAAA;AAAA,UAE1C,CAAC,aAAa,WAAW;AAAA,UACzB,CAAC,aAAa,WAAW;AAAA;AAAA,UAEzB,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,UACtE,CAAC,mBAAmB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,UACpE,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA;AAAA,UAEtE,CAAC,0CAA0C,cAAc;AAAA;AAAA,UAEzD,CAAC,0BAA0B,iBAAiB;AAAA,UAC5C,CAAC,4BAA4B,iBAAiB;AAAA,UAC9C,CAAC,iCAAiC,eAAe;AAAA,UACjD,CAAC,iBAAiB,iBAAiB;AAAA,UACnC,CAAC,qBAAqB,kBAAkB;AAAA,UACxC,CAAC,YAAY,WAAW;AAAA,UACxB,CAAC,MAAM,WAAW;AAAA,QACpB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,MAEF;AAAA,MACA,SAAS;AAAA,IACX;AAAA;AAAA;", "names": []}
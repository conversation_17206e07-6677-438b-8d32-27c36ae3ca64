{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/pla/pla.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pla/pla.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"<\", \">\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"<\", close: \">\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pla\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \".i\",\n    \".o\",\n    \".mv\",\n    \".ilb\",\n    \".ob\",\n    \".label\",\n    \".type\",\n    \".phase\",\n    \".pair\",\n    \".symbolic\",\n    \".symbolic-output\",\n    \".kiss\",\n    \".p\",\n    \".e\",\n    \".end\"\n  ],\n  // regular expressions\n  comment: /#.*$/,\n  identifier: /[a-zA-Z]+[a-zA-Z0-9_\\-]*/,\n  plaContent: /[01\\-~\\|]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comments and whitespace\n      { include: \"@whitespace\" },\n      [/@comment/, \"comment\"],\n      // keyword\n      [\n        /\\.([a-zA-Z_\\-]+)/,\n        {\n          cases: {\n            \"@eos\": { token: \"keyword.$1\" },\n            \"@keywords\": {\n              cases: {\n                \".type\": { token: \"keyword.$1\", next: \"@type\" },\n                \"@default\": { token: \"keyword.$1\", next: \"@keywordArg\" }\n              }\n            },\n            \"@default\": { token: \"keyword.$1\" }\n          }\n        }\n      ],\n      // identifiers\n      [/@identifier/, \"identifier\"],\n      // PLA row\n      [/@plaContent/, \"string\"]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"\"]],\n    type: [{ include: \"@whitespace\" }, [/\\w+/, { token: \"type\", next: \"@pop\" }]],\n    keywordArg: [\n      // whitespace\n      [\n        /[ \\t\\r\\n]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@pop\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // comments\n      [/@comment/, \"comment\", \"@pop\"],\n      // brackets\n      [\n        /[<>()\\[\\]]/,\n        {\n          cases: {\n            \"@eos\": { token: \"@brackets\", next: \"@pop\" },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      // numbers\n      [\n        /\\-?\\d+/,\n        {\n          cases: {\n            \"@eos\": { token: \"number\", next: \"@pop\" },\n            \"@default\": \"number\"\n          }\n        }\n      ],\n      // identifiers\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@eos\": { token: \"identifier\", next: \"@pop\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // delimiter\n      [\n        /[;=]/,\n        {\n          cases: {\n            \"@eos\": { token: \"delimiter\", next: \"@pop\" },\n            \"@default\": \"delimiter\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAoBA;AA7BJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MAC1D;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAEZ,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,YAAY,SAAS;AAAA;AAAA,UAEtB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,aAAa;AAAA,gBAC9B,aAAa;AAAA,kBACX,OAAO;AAAA,oBACL,SAAS,EAAE,OAAO,cAAc,MAAM,QAAQ;AAAA,oBAC9C,YAAY,EAAE,OAAO,cAAc,MAAM,cAAc;AAAA,kBACzD;AAAA,gBACF;AAAA,gBACA,YAAY,EAAE,OAAO,aAAa;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,eAAe,YAAY;AAAA;AAAA,UAE5B,CAAC,eAAe,QAAQ;AAAA,QAC1B;AAAA,QACA,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAAA,QAC/B,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,QAC3E,YAAY;AAAA;AAAA,UAEV;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,IAAI,MAAM,OAAO;AAAA,gBAClC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY,WAAW,MAAM;AAAA;AAAA,UAE9B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,gBACxC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,cAAc,MAAM,OAAO;AAAA,gBAC5C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/fold/foldcode.js", "../../../../node_modules/codemirror/addon/fold/foldgutter.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function doFold(cm, pos, options, force) {\n    if (options && options.call) {\n      var finder = options;\n      options = null;\n    } else {\n      var finder = getOption(cm, options, \"rangeFinder\");\n    }\n    if (typeof pos == \"number\") pos = CodeMirror.Pos(pos, 0);\n    var minSize = getOption(cm, options, \"minFoldSize\");\n\n    function getRange(allowFolded) {\n      var range = finder(cm, pos);\n      if (!range || range.to.line - range.from.line < minSize) return null;\n      if (force === \"fold\") return range;\n\n      var marks = cm.findMarksAt(range.from);\n      for (var i = 0; i < marks.length; ++i) {\n        if (marks[i].__isFold) {\n          if (!allowFolded) return null;\n          range.cleared = true;\n          marks[i].clear();\n        }\n      }\n      return range;\n    }\n\n    var range = getRange(true);\n    if (getOption(cm, options, \"scanUp\")) while (!range && pos.line > cm.firstLine()) {\n      pos = CodeMirror.Pos(pos.line - 1, 0);\n      range = getRange(false);\n    }\n    if (!range || range.cleared || force === \"unfold\") return;\n\n    var myWidget = makeWidget(cm, options, range);\n    CodeMirror.on(myWidget, \"mousedown\", function(e) {\n      myRange.clear();\n      CodeMirror.e_preventDefault(e);\n    });\n    var myRange = cm.markText(range.from, range.to, {\n      replacedWith: myWidget,\n      clearOnEnter: getOption(cm, options, \"clearOnEnter\"),\n      __isFold: true\n    });\n    myRange.on(\"clear\", function(from, to) {\n      CodeMirror.signal(cm, \"unfold\", cm, from, to);\n    });\n    CodeMirror.signal(cm, \"fold\", cm, range.from, range.to);\n  }\n\n  function makeWidget(cm, options, range) {\n    var widget = getOption(cm, options, \"widget\");\n\n    if (typeof widget == \"function\") {\n      widget = widget(range.from, range.to);\n    }\n\n    if (typeof widget == \"string\") {\n      var text = document.createTextNode(widget);\n      widget = document.createElement(\"span\");\n      widget.appendChild(text);\n      widget.className = \"CodeMirror-foldmarker\";\n    } else if (widget) {\n      widget = widget.cloneNode(true)\n    }\n    return widget;\n  }\n\n  // Clumsy backwards-compatible interface\n  CodeMirror.newFoldFunction = function(rangeFinder, widget) {\n    return function(cm, pos) { doFold(cm, pos, {rangeFinder: rangeFinder, widget: widget}); };\n  };\n\n  // New-style interface\n  CodeMirror.defineExtension(\"foldCode\", function(pos, options, force) {\n    doFold(this, pos, options, force);\n  });\n\n  CodeMirror.defineExtension(\"isFolded\", function(pos) {\n    var marks = this.findMarksAt(pos);\n    for (var i = 0; i < marks.length; ++i)\n      if (marks[i].__isFold) return true;\n  });\n\n  CodeMirror.commands.toggleFold = function(cm) {\n    cm.foldCode(cm.getCursor());\n  };\n  CodeMirror.commands.fold = function(cm) {\n    cm.foldCode(cm.getCursor(), null, \"fold\");\n  };\n  CodeMirror.commands.unfold = function(cm) {\n    cm.foldCode(cm.getCursor(), { scanUp: false }, \"unfold\");\n  };\n  CodeMirror.commands.foldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"fold\");\n    });\n  };\n  CodeMirror.commands.unfoldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"unfold\");\n    });\n  };\n\n  CodeMirror.registerHelper(\"fold\", \"combine\", function() {\n    var funcs = Array.prototype.slice.call(arguments, 0);\n    return function(cm, start) {\n      for (var i = 0; i < funcs.length; ++i) {\n        var found = funcs[i](cm, start);\n        if (found) return found;\n      }\n    };\n  });\n\n  CodeMirror.registerHelper(\"fold\", \"auto\", function(cm, start) {\n    var helpers = cm.getHelpers(start, \"fold\");\n    for (var i = 0; i < helpers.length; i++) {\n      var cur = helpers[i](cm, start);\n      if (cur) return cur;\n    }\n  });\n\n  var defaultOptions = {\n    rangeFinder: CodeMirror.fold.auto,\n    widget: \"\\u2194\",\n    minFoldSize: 0,\n    scanUp: false,\n    clearOnEnter: true\n  };\n\n  CodeMirror.defineOption(\"foldOptions\", null);\n\n  function getOption(cm, options, name) {\n    if (options && options[name] !== undefined)\n      return options[name];\n    var editorOptions = cm.options.foldOptions;\n    if (editorOptions && editorOptions[name] !== undefined)\n      return editorOptions[name];\n    return defaultOptions[name];\n  }\n\n  CodeMirror.defineExtension(\"foldOption\", function(options, name) {\n    return getOption(this, options, name);\n  });\n});\n", "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./foldcode\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./foldcode\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"foldGutter\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.clearGutter(cm.state.foldGutter.options.gutter);\n      cm.state.foldGutter = null;\n      cm.off(\"gutterClick\", onGutterClick);\n      cm.off(\"changes\", onChange);\n      cm.off(\"viewportChange\", onViewportChange);\n      cm.off(\"fold\", onFold);\n      cm.off(\"unfold\", onFold);\n      cm.off(\"swapDoc\", onChange);\n    }\n    if (val) {\n      cm.state.foldGutter = new State(parseOptions(val));\n      updateInViewport(cm);\n      cm.on(\"gutterClick\", onGutterClick);\n      cm.on(\"changes\", onChange);\n      cm.on(\"viewportChange\", onViewportChange);\n      cm.on(\"fold\", onFold);\n      cm.on(\"unfold\", onFold);\n      cm.on(\"swapDoc\", onChange);\n    }\n  });\n\n  var Pos = CodeMirror.Pos;\n\n  function State(options) {\n    this.options = options;\n    this.from = this.to = 0;\n  }\n\n  function parseOptions(opts) {\n    if (opts === true) opts = {};\n    if (opts.gutter == null) opts.gutter = \"CodeMirror-foldgutter\";\n    if (opts.indicatorOpen == null) opts.indicatorOpen = \"CodeMirror-foldgutter-open\";\n    if (opts.indicatorFolded == null) opts.indicatorFolded = \"CodeMirror-foldgutter-folded\";\n    return opts;\n  }\n\n  function isFolded(cm, line) {\n    var marks = cm.findMarks(Pos(line, 0), Pos(line + 1, 0));\n    for (var i = 0; i < marks.length; ++i) {\n      if (marks[i].__isFold) {\n        var fromPos = marks[i].find(-1);\n        if (fromPos && fromPos.line === line)\n          return marks[i];\n      }\n    }\n  }\n\n  function marker(spec) {\n    if (typeof spec == \"string\") {\n      var elt = document.createElement(\"div\");\n      elt.className = spec + \" CodeMirror-guttermarker-subtle\";\n      return elt;\n    } else {\n      return spec.cloneNode(true);\n    }\n  }\n\n  function updateFoldInfo(cm, from, to) {\n    var opts = cm.state.foldGutter.options, cur = from - 1;\n    var minSize = cm.foldOption(opts, \"minFoldSize\");\n    var func = cm.foldOption(opts, \"rangeFinder\");\n    // we can reuse the built-in indicator element if its className matches the new state\n    var clsFolded = typeof opts.indicatorFolded == \"string\" && classTest(opts.indicatorFolded);\n    var clsOpen = typeof opts.indicatorOpen == \"string\" && classTest(opts.indicatorOpen);\n    cm.eachLine(from, to, function(line) {\n      ++cur;\n      var mark = null;\n      var old = line.gutterMarkers;\n      if (old) old = old[opts.gutter];\n      if (isFolded(cm, cur)) {\n        if (clsFolded && old && clsFolded.test(old.className)) return;\n        mark = marker(opts.indicatorFolded);\n      } else {\n        var pos = Pos(cur, 0);\n        var range = func && func(cm, pos);\n        if (range && range.to.line - range.from.line >= minSize) {\n          if (clsOpen && old && clsOpen.test(old.className)) return;\n          mark = marker(opts.indicatorOpen);\n        }\n      }\n      if (!mark && !old) return;\n      cm.setGutterMarker(line, opts.gutter, mark);\n    });\n  }\n\n  // copied from CodeMirror/src/util/dom.js\n  function classTest(cls) { return new RegExp(\"(^|\\\\s)\" + cls + \"(?:$|\\\\s)\\\\s*\") }\n\n  function updateInViewport(cm) {\n    var vp = cm.getViewport(), state = cm.state.foldGutter;\n    if (!state) return;\n    cm.operation(function() {\n      updateFoldInfo(cm, vp.from, vp.to);\n    });\n    state.from = vp.from; state.to = vp.to;\n  }\n\n  function onGutterClick(cm, line, gutter) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    if (gutter != opts.gutter) return;\n    var folded = isFolded(cm, line);\n    if (folded) folded.clear();\n    else cm.foldCode(Pos(line, 0), opts);\n  }\n\n  function onChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    state.from = state.to = 0;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() { updateInViewport(cm); }, opts.foldOnChangeTimeSpan || 600);\n  }\n\n  function onViewportChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() {\n      var vp = cm.getViewport();\n      if (state.from == state.to || vp.from - state.to > 20 || state.from - vp.to > 20) {\n        updateInViewport(cm);\n      } else {\n        cm.operation(function() {\n          if (vp.from < state.from) {\n            updateFoldInfo(cm, vp.from, state.from);\n            state.from = vp.from;\n          }\n          if (vp.to > state.to) {\n            updateFoldInfo(cm, state.to, vp.to);\n            state.to = vp.to;\n          }\n        });\n      }\n    }, opts.updateViewportTimeSpan || 400);\n  }\n\n  function onFold(cm, from) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var line = from.line;\n    if (line >= state.from && line < state.to)\n      updateFoldInfo(cm, line, line + 1);\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,KAAC,SAAS,KAAK;AAEX,UAAIA,kBAA+B,CAAA;IAKtC,GAAE,SAAS,YAAY;AAGtB,eAAS,OAAO,IAAI,KAAK,SAAS,OAAO;AACvC,YAAI,WAAW,QAAQ,MAAM;AAC3B,cAAI,SAAS;AACb,oBAAU;QAChB,OAAW;AACL,cAAI,SAAS,UAAU,IAAI,SAAS,aAAa;QAClD;AACD,YAAI,OAAO,OAAO,SAAU,OAAM,WAAW,IAAI,KAAK,CAAC;AACvD,YAAI,UAAU,UAAU,IAAI,SAAS,aAAa;AAElD,iBAAS,SAAS,aAAa;AAC7B,cAAIC,SAAQ,OAAO,IAAI,GAAG;AAC1B,cAAI,CAACA,UAASA,OAAM,GAAG,OAAOA,OAAM,KAAK,OAAO,QAAS,QAAO;AAChE,cAAI,UAAU,OAAQ,QAAOA;AAE7B,cAAI,QAAQ,GAAG,YAAYA,OAAM,IAAI;AACrC,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,MAAM,CAAC,EAAE,UAAU;AACrB,kBAAI,CAAC,YAAa,QAAO;AACzBA,qBAAM,UAAU;AAChB,oBAAM,CAAC,EAAE,MAAA;YACV;UACF;AACD,iBAAOA;QACR;AAED,YAAI,QAAQ,SAAS,IAAI;AACzB,YAAI,UAAU,IAAI,SAAS,QAAQ,EAAG,QAAO,CAAC,SAAS,IAAI,OAAO,GAAG,UAAS,GAAI;AAChF,gBAAM,WAAW,IAAI,IAAI,OAAO,GAAG,CAAC;AACpC,kBAAQ,SAAS,KAAK;QACvB;AACD,YAAI,CAAC,SAAS,MAAM,WAAW,UAAU,SAAU;AAEnD,YAAI,WAAW,WAAW,IAAI,SAAS,KAAK;AAC5C,mBAAW,GAAG,UAAU,aAAa,SAAS,GAAG;AAC/C,kBAAQ,MAAK;AACb,qBAAW,iBAAiB,CAAC;QACnC,CAAK;AACD,YAAI,UAAU,GAAG,SAAS,MAAM,MAAM,MAAM,IAAI;UAC9C,cAAc;UACd,cAAc,UAAU,IAAI,SAAS,cAAc;UACnD,UAAU;QAChB,CAAK;AACD,gBAAQ,GAAG,SAAS,SAAS,MAAM,IAAI;AACrC,qBAAW,OAAO,IAAI,UAAU,IAAI,MAAM,EAAE;QAClD,CAAK;AACD,mBAAW,OAAO,IAAI,QAAQ,IAAI,MAAM,MAAM,MAAM,EAAE;MACvD;AAED,eAAS,WAAW,IAAI,SAAS,OAAO;AACtC,YAAI,SAAS,UAAU,IAAI,SAAS,QAAQ;AAE5C,YAAI,OAAO,UAAU,YAAY;AAC/B,mBAAS,OAAO,MAAM,MAAM,MAAM,EAAE;QACrC;AAED,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,OAAO,SAAS,eAAe,MAAM;AACzC,mBAAS,SAAS,cAAc,MAAM;AACtC,iBAAO,YAAY,IAAI;AACvB,iBAAO,YAAY;QACpB,WAAU,QAAQ;AACjB,mBAAS,OAAO,UAAU,IAAI;QAC/B;AACD,eAAO;MACR;AAGD,iBAAW,kBAAkB,SAAS,aAAa,QAAQ;AACzD,eAAO,SAAS,IAAI,KAAK;AAAE,iBAAO,IAAI,KAAK,EAAC,aAA0B,OAAc,CAAC;QAAE;MAC3F;AAGE,iBAAW,gBAAgB,YAAY,SAAS,KAAK,SAAS,OAAO;AACnE,eAAO,MAAM,KAAK,SAAS,KAAK;MACpC,CAAG;AAED,iBAAW,gBAAgB,YAAY,SAAS,KAAK;AACnD,YAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAClC,cAAI,MAAM,CAAC,EAAE,SAAU,QAAO;MACpC,CAAG;AAED,iBAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,WAAG,SAAS,GAAG,UAAW,CAAA;MAC9B;AACE,iBAAW,SAAS,OAAO,SAAS,IAAI;AACtC,WAAG,SAAS,GAAG,UAAS,GAAI,MAAM,MAAM;MAC5C;AACE,iBAAW,SAAS,SAAS,SAAS,IAAI;AACxC,WAAG,SAAS,GAAG,UAAS,GAAI,EAAE,QAAQ,MAAA,GAAS,QAAQ;MAC3D;AACE,iBAAW,SAAS,UAAU,SAAS,IAAI;AACzC,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAA,GAAa,IAAI,GAAG,SAAU,GAAE,KAAK,GAAG;AACtD,eAAG,SAAS,WAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAO,GAAE,MAAM;QACnE,CAAK;MACL;AACE,iBAAW,SAAS,YAAY,SAAS,IAAI;AAC3C,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAA,GAAa,IAAI,GAAG,SAAU,GAAE,KAAK,GAAG;AACtD,eAAG,SAAS,WAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAO,GAAE,QAAQ;QACrE,CAAK;MACL;AAEE,iBAAW,eAAe,QAAQ,WAAW,WAAW;AACtD,YAAI,QAAQ,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACnD,eAAO,SAAS,IAAI,OAAO;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,QAAQ,MAAM,CAAC,EAAE,IAAI,KAAK;AAC9B,gBAAI,MAAO,QAAO;UACnB;QACP;MACA,CAAG;AAED,iBAAW,eAAe,QAAQ,QAAQ,SAAS,IAAI,OAAO;AAC5D,YAAI,UAAU,GAAG,WAAW,OAAO,MAAM;AACzC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,MAAM,QAAQ,CAAC,EAAE,IAAI,KAAK;AAC9B,cAAI,IAAK,QAAO;QACjB;MACL,CAAG;AAED,UAAI,iBAAiB;QACnB,aAAa,WAAW,KAAK;QAC7B,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;MAClB;AAEE,iBAAW,aAAa,eAAe,IAAI;AAE3C,eAAS,UAAU,IAAI,SAAS,MAAM;AACpC,YAAI,WAAW,QAAQ,IAAI,MAAM;AAC/B,iBAAO,QAAQ,IAAI;AACrB,YAAI,gBAAgB,GAAG,QAAQ;AAC/B,YAAI,iBAAiB,cAAc,IAAI,MAAM;AAC3C,iBAAO,cAAc,IAAI;AAC3B,eAAO,eAAe,IAAI;MAC3B;AAED,iBAAW,gBAAgB,cAAc,SAAS,SAAS,MAAM;AAC/D,eAAO,UAAU,MAAM,SAAS,IAAI;MACxC,CAAG;IACH,CAAC;EAAA,GAAA;;;;AC3JD,GAAC,SAAS,KAAK;AAEX,QAAID,kBAAA,GAAiCE,gBAAA,CAAqB;EAK7D,GAAE,SAAS,YAAY;AAGtB,eAAW,aAAa,cAAc,OAAO,SAAS,IAAI,KAAK,KAAK;AAClE,UAAI,OAAO,OAAO,WAAW,MAAM;AACjC,WAAG,YAAY,GAAG,MAAM,WAAW,QAAQ,MAAM;AACjD,WAAG,MAAM,aAAa;AACtB,WAAG,IAAI,eAAe,aAAa;AACnC,WAAG,IAAI,WAAW,QAAQ;AAC1B,WAAG,IAAI,kBAAkB,gBAAgB;AACzC,WAAG,IAAI,QAAQ,MAAM;AACrB,WAAG,IAAI,UAAU,MAAM;AACvB,WAAG,IAAI,WAAW,QAAQ;MAC3B;AACD,UAAI,KAAK;AACP,WAAG,MAAM,aAAa,IAAI,MAAM,aAAa,GAAG,CAAC;AACjD,yBAAiB,EAAE;AACnB,WAAG,GAAG,eAAe,aAAa;AAClC,WAAG,GAAG,WAAW,QAAQ;AACzB,WAAG,GAAG,kBAAkB,gBAAgB;AACxC,WAAG,GAAG,QAAQ,MAAM;AACpB,WAAG,GAAG,UAAU,MAAM;AACtB,WAAG,GAAG,WAAW,QAAQ;MAC1B;IACL,CAAG;AAED,QAAI,MAAM,WAAW;AAErB,aAAS,MAAM,SAAS;AACtB,WAAK,UAAU;AACf,WAAK,OAAO,KAAK,KAAK;IACvB;AAED,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS,KAAM,QAAO,CAAA;AAC1B,UAAI,KAAK,UAAU,KAAM,MAAK,SAAS;AACvC,UAAI,KAAK,iBAAiB,KAAM,MAAK,gBAAgB;AACrD,UAAI,KAAK,mBAAmB,KAAM,MAAK,kBAAkB;AACzD,aAAO;IACR;AAED,aAAS,SAAS,IAAI,MAAM;AAC1B,UAAI,QAAQ,GAAG,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,GAAG,CAAC,CAAC;AACvD,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,CAAC,EAAE,UAAU;AACrB,cAAI,UAAU,MAAM,CAAC,EAAE,KAAK,EAAE;AAC9B,cAAI,WAAW,QAAQ,SAAS;AAC9B,mBAAO,MAAM,CAAC;QACjB;MACF;IACF;AAED,aAAS,OAAO,MAAM;AACpB,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,MAAM,SAAS,cAAc,KAAK;AACtC,YAAI,YAAY,OAAO;AACvB,eAAO;MACb,OAAW;AACL,eAAO,KAAK,UAAU,IAAI;MAC3B;IACF;AAED,aAAS,eAAe,IAAI,MAAM,IAAI;AACpC,UAAI,OAAO,GAAG,MAAM,WAAW,SAAS,MAAM,OAAO;AACrD,UAAI,UAAU,GAAG,WAAW,MAAM,aAAa;AAC/C,UAAI,OAAO,GAAG,WAAW,MAAM,aAAa;AAE5C,UAAI,YAAY,OAAO,KAAK,mBAAmB,YAAY,UAAU,KAAK,eAAe;AACzF,UAAI,UAAU,OAAO,KAAK,iBAAiB,YAAY,UAAU,KAAK,aAAa;AACnF,SAAG,SAAS,MAAM,IAAI,SAAS,MAAM;AACnC,UAAE;AACF,YAAI,OAAO;AACX,YAAI,MAAM,KAAK;AACf,YAAI,IAAK,OAAM,IAAI,KAAK,MAAM;AAC9B,YAAI,SAAS,IAAI,GAAG,GAAG;AACrB,cAAI,aAAa,OAAO,UAAU,KAAK,IAAI,SAAS,EAAG;AACvD,iBAAO,OAAO,KAAK,eAAe;QAC1C,OAAa;AACL,cAAI,MAAM,IAAI,KAAK,CAAC;AACpB,cAAI,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAChC,cAAI,SAAS,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,SAAS;AACvD,gBAAI,WAAW,OAAO,QAAQ,KAAK,IAAI,SAAS,EAAG;AACnD,mBAAO,OAAO,KAAK,aAAa;UACjC;QACF;AACD,YAAI,CAAC,QAAQ,CAAC,IAAK;AACnB,WAAG,gBAAgB,MAAM,KAAK,QAAQ,IAAI;MAChD,CAAK;IACF;AAGD,aAAS,UAAU,KAAK;AAAE,aAAO,IAAI,OAAO,YAAY,MAAM,eAAe;IAAG;AAEhF,aAAS,iBAAiB,IAAI;AAC5B,UAAI,KAAK,GAAG,YAAW,GAAI,QAAQ,GAAG,MAAM;AAC5C,UAAI,CAAC,MAAO;AACZ,SAAG,UAAU,WAAW;AACtB,uBAAe,IAAI,GAAG,MAAM,GAAG,EAAE;MACvC,CAAK;AACD,YAAM,OAAO,GAAG;AAAM,YAAM,KAAK,GAAG;IACrC;AAED,aAAS,cAAc,IAAI,MAAM,QAAQ;AACvC,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,OAAO,MAAM;AACjB,UAAI,UAAU,KAAK,OAAQ;AAC3B,UAAI,SAAS,SAAS,IAAI,IAAI;AAC9B,UAAI,OAAQ,QAAO,MAAA;UACd,IAAG,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACpC;AAED,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,OAAO,MAAM;AACjB,YAAM,OAAO,MAAM,KAAK;AACxB,mBAAa,MAAM,YAAY;AAC/B,YAAM,eAAe,WAAW,WAAW;AAAE,yBAAiB,EAAE;MAAA,GAAM,KAAK,wBAAwB,GAAG;IACvG;AAED,aAAS,iBAAiB,IAAI;AAC5B,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,OAAO,MAAM;AACjB,mBAAa,MAAM,YAAY;AAC/B,YAAM,eAAe,WAAW,WAAW;AACzC,YAAI,KAAK,GAAG,YAAA;AACZ,YAAI,MAAM,QAAQ,MAAM,MAAM,GAAG,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,IAAI;AAChF,2BAAiB,EAAE;QAC3B,OAAa;AACL,aAAG,UAAU,WAAW;AACtB,gBAAI,GAAG,OAAO,MAAM,MAAM;AACxB,6BAAe,IAAI,GAAG,MAAM,MAAM,IAAI;AACtC,oBAAM,OAAO,GAAG;YACjB;AACD,gBAAI,GAAG,KAAK,MAAM,IAAI;AACpB,6BAAe,IAAI,MAAM,IAAI,GAAG,EAAE;AAClC,oBAAM,KAAK,GAAG;YACf;UACX,CAAS;QACF;MACP,GAAO,KAAK,0BAA0B,GAAG;IACtC;AAED,aAAS,OAAO,IAAI,MAAM;AACxB,UAAI,QAAQ,GAAG,MAAM;AACrB,UAAI,CAAC,MAAO;AACZ,UAAI,OAAO,KAAK;AAChB,UAAI,QAAQ,MAAM,QAAQ,OAAO,MAAM;AACrC,uBAAe,IAAI,MAAM,OAAO,CAAC;IACpC;EACH,CAAC;;;;;;;;", "names": ["require$$0", "range", "require$$1"]}
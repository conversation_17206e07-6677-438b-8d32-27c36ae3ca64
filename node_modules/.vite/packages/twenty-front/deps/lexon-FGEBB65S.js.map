{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/lexon/lexon.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/lexon/lexon.ts\nvar conf = {\n  comments: {\n    lineComment: \"COMMENT\"\n    // blockComment: ['COMMENT', '.'],\n  },\n  brackets: [[\"(\", \")\"]],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \":\", close: \".\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"`\", close: \"`\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \":\", close: \".\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  tokenPostfix: \".lexon\",\n  ignoreCase: true,\n  keywords: [\n    \"lexon\",\n    \"lex\",\n    \"clause\",\n    \"terms\",\n    \"contracts\",\n    \"may\",\n    \"pay\",\n    \"pays\",\n    \"appoints\",\n    \"into\",\n    \"to\"\n  ],\n  typeKeywords: [\"amount\", \"person\", \"key\", \"time\", \"date\", \"asset\", \"text\"],\n  operators: [\n    \"less\",\n    \"greater\",\n    \"equal\",\n    \"le\",\n    \"gt\",\n    \"or\",\n    \"and\",\n    \"add\",\n    \"added\",\n    \"subtract\",\n    \"subtracted\",\n    \"multiply\",\n    \"multiplied\",\n    \"times\",\n    \"divide\",\n    \"divided\",\n    \"is\",\n    \"be\",\n    \"certified\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comment\n      [/^(\\s*)(comment:?(?:\\s.*|))$/, [\"\", \"comment\"]],\n      // special identifier cases\n      [\n        /\"/,\n        {\n          token: \"identifier.quote\",\n          bracket: \"@open\",\n          next: \"@quoted_identifier\"\n        }\n      ],\n      [\n        \"LEX$\",\n        {\n          token: \"keyword\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\"LEXON\", { token: \"keyword\", bracket: \"@open\", next: \"@semver\" }],\n      [\n        \":\",\n        {\n          token: \"delimiter\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d*\\.\\d*/, \"number.semver\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"]\n    ],\n    quoted_identifier: [\n      [/[^\\\\\"]+/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    space_identifier_until_period: [\n      [\":\", \"delimiter\"],\n      [\" \", { token: \"white\", next: \"@identifier_rest\" }]\n    ],\n    identifier_until_period: [\n      { include: \"@whitespace\" },\n      [\":\", { token: \"delimiter\", next: \"@identifier_rest\" }],\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    identifier_rest: [\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    semver: [\n      { include: \"@whitespace\" },\n      [\":\", \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, { token: \"number.semver\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA6BA;AAtCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA;AAAA,MAEf;AAAA,MACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,kCAAkC;AAAA,UACpD,KAAK,IAAI,OAAO,qCAAqC;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA;AAAA;AAAA,MAGb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAc,CAAC,UAAU,UAAU,OAAO,QAAQ,QAAQ,SAAS,MAAM;AAAA,MACzE,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA;AAAA,MAET,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,+BAA+B,CAAC,IAAI,SAAS,CAAC;AAAA;AAAA,UAE/C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,CAAC,SAAS,EAAE,OAAO,WAAW,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,UACjE;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,gBACd,iBAAiB;AAAA,gBACjB,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,oBAAoB,WAAW;AAAA,UAChC,CAAC,YAAY,WAAW;AAAA;AAAA,UAExB,CAAC,iBAAiB,eAAe;AAAA,UACjC,CAAC,4BAA4B,cAAc;AAAA,UAC3C,CAAC,qBAAqB,YAAY;AAAA,UAClC,CAAC,OAAO,QAAQ;AAAA;AAAA,UAEhB,CAAC,SAAS,WAAW;AAAA,QACvB;AAAA,QACA,mBAAmB;AAAA,UACjB,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,KAAK,EAAE,OAAO,oBAAoB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QACtE;AAAA,QACA,+BAA+B;AAAA,UAC7B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,EAAE,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAAA,QACpD;AAAA,QACA,yBAAyB;AAAA,UACvB,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,KAAK,EAAE,OAAO,aAAa,MAAM,mBAAmB,CAAC;AAAA,UACtD,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAChE;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAChE;AAAA,QACA,QAAQ;AAAA,UACN,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,iBAAiB,EAAE,OAAO,iBAAiB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAC/E;AAAA,QACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/info-addon.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nCodeMirror.defineOption('info', false, (cm, options, old) => {\n    if (old && old !== CodeMirror.Init) {\n        const oldOnMouseOver = cm.state.info.onMouseOver;\n        CodeMirror.off(cm.getWrapperElement(), 'mouseover', oldOnMouseOver);\n        clearTimeout(cm.state.info.hoverTimeout);\n        delete cm.state.info;\n    }\n    if (options) {\n        const state = (cm.state.info = createState(options));\n        state.onMouseOver = onMouseOver.bind(null, cm);\n        CodeMirror.on(cm.getWrapperElement(), 'mouseover', state.onMouseOver);\n    }\n});\nfunction createState(options) {\n    return {\n        options: options instanceof Function\n            ? { render: options }\n            : options === true\n                ? {}\n                : options,\n    };\n}\nfunction getHoverTime(cm) {\n    const { options } = cm.state.info;\n    return (options === null || options === void 0 ? void 0 : options.hoverTime) || 500;\n}\nfunction onMouseOver(cm, e) {\n    const state = cm.state.info;\n    const target = e.target || e.srcElement;\n    if (!(target instanceof HTMLElement)) {\n        return;\n    }\n    if (target.nodeName !== 'SPAN' || state.hoverTimeout !== undefined) {\n        return;\n    }\n    const box = target.getBoundingClientRect();\n    const onMouseMove = function () {\n        clearTimeout(state.hoverTimeout);\n        state.hoverTimeout = setTimeout(onHover, hoverTime);\n    };\n    const onMouseOut = function () {\n        CodeMirror.off(document, 'mousemove', onMouseMove);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        clearTimeout(state.hoverTimeout);\n        state.hoverTimeout = undefined;\n    };\n    const onHover = function () {\n        CodeMirror.off(document, 'mousemove', onMouseMove);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        state.hoverTimeout = undefined;\n        onMouseHover(cm, box);\n    };\n    const hoverTime = getHoverTime(cm);\n    state.hoverTimeout = setTimeout(onHover, hoverTime);\n    CodeMirror.on(document, 'mousemove', onMouseMove);\n    CodeMirror.on(cm.getWrapperElement(), 'mouseout', onMouseOut);\n}\nfunction onMouseHover(cm, box) {\n    const pos = cm.coordsChar({\n        left: (box.left + box.right) / 2,\n        top: (box.top + box.bottom) / 2,\n    }, 'window');\n    const state = cm.state.info;\n    const { options } = state;\n    const render = options.render || cm.getHelper(pos, 'info');\n    if (render) {\n        const token = cm.getTokenAt(pos, true);\n        if (token) {\n            const info = render(token, options, cm, pos);\n            if (info) {\n                showPopup(cm, box, info);\n            }\n        }\n    }\n}\nfunction showPopup(cm, box, info) {\n    const popup = document.createElement('div');\n    popup.className = 'CodeMirror-info';\n    popup.append(info);\n    document.body.append(popup);\n    const popupBox = popup.getBoundingClientRect();\n    const popupStyle = window.getComputedStyle(popup);\n    const popupWidth = popupBox.right -\n        popupBox.left +\n        parseFloat(popupStyle.marginLeft) +\n        parseFloat(popupStyle.marginRight);\n    const popupHeight = popupBox.bottom -\n        popupBox.top +\n        parseFloat(popupStyle.marginTop) +\n        parseFloat(popupStyle.marginBottom);\n    let topPos = box.bottom;\n    if (popupHeight > window.innerHeight - box.bottom - 15 &&\n        box.top > window.innerHeight - box.bottom) {\n        topPos = box.top - popupHeight;\n    }\n    if (topPos < 0) {\n        topPos = box.bottom;\n    }\n    let leftPos = Math.max(0, window.innerWidth - popupWidth - 15);\n    if (leftPos > box.left) {\n        leftPos = box.left;\n    }\n    popup.style.opacity = '1';\n    popup.style.top = topPos + 'px';\n    popup.style.left = leftPos + 'px';\n    let popupTimeout;\n    const onMouseOverPopup = function () {\n        clearTimeout(popupTimeout);\n    };\n    const onMouseOut = function () {\n        clearTimeout(popupTimeout);\n        popupTimeout = setTimeout(hidePopup, 200);\n    };\n    const hidePopup = function () {\n        CodeMirror.off(popup, 'mouseover', onMouseOverPopup);\n        CodeMirror.off(popup, 'mouseout', onMouseOut);\n        CodeMirror.off(cm.getWrapperElement(), 'mouseout', onMouseOut);\n        if (popup.style.opacity) {\n            popup.style.opacity = '0';\n            setTimeout(() => {\n                if (popup.parentNode) {\n                    popup.remove();\n                }\n            }, 600);\n        }\n        else if (popup.parentNode) {\n            popup.remove();\n        }\n    };\n    CodeMirror.on(popup, 'mouseover', onMouseOverPopup);\n    CodeMirror.on(popup, 'mouseout', onMouseOut);\n    CodeMirror.on(cm.getWrapperElement(), 'mouseout', onMouseOut);\n}\n//# sourceMappingURL=info-addon.js.map"], "mappings": ";;;;;AACA,WAAW,aAAa,QAAQ,OAAO,CAAC,IAAI,SAAS,QAAQ;AACzD,MAAI,OAAO,QAAQ,WAAW,MAAM;AAChC,UAAM,iBAAiB,GAAG,MAAM,KAAK;AACrC,eAAW,IAAI,GAAG,kBAAiB,GAAI,aAAa,cAAc;AAClE,iBAAa,GAAG,MAAM,KAAK,YAAY;AACvC,WAAO,GAAG,MAAM;EACnB;AACD,MAAI,SAAS;AACT,UAAM,QAAS,GAAG,MAAM,OAAO,YAAY,OAAO;AAClD,UAAM,cAAc,YAAY,KAAK,MAAM,EAAE;AAC7C,eAAW,GAAG,GAAG,kBAAmB,GAAE,aAAa,MAAM,WAAW;EACvE;AACL,CAAC;AACD,SAAS,YAAY,SAAS;AAC1B,SAAO;IACH,SAAS,mBAAmB,WACtB,EAAE,QAAQ,QAAS,IACnB,YAAY,OACR,CAAE,IACF;EAClB;AACA;AACA,SAAS,aAAa,IAAI;AACtB,QAAM,EAAE,QAAS,IAAG,GAAG,MAAM;AAC7B,UAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc;AACpF;AACA,SAAS,YAAY,IAAI,GAAG;AACxB,QAAM,QAAQ,GAAG,MAAM;AACvB,QAAM,SAAS,EAAE,UAAU,EAAE;AAC7B,MAAI,EAAE,kBAAkB,cAAc;AAClC;EACH;AACD,MAAI,OAAO,aAAa,UAAU,MAAM,iBAAiB,QAAW;AAChE;EACH;AACD,QAAM,MAAM,OAAO,sBAAA;AACnB,QAAM,cAAc,WAAY;AAC5B,iBAAa,MAAM,YAAY;AAC/B,UAAM,eAAe,WAAW,SAAS,SAAS;EAC1D;AACI,QAAM,aAAa,WAAY;AAC3B,eAAW,IAAI,UAAU,aAAa,WAAW;AACjD,eAAW,IAAI,GAAG,kBAAiB,GAAI,YAAY,UAAU;AAC7D,iBAAa,MAAM,YAAY;AAC/B,UAAM,eAAe;EAC7B;AACI,QAAM,UAAU,WAAY;AACxB,eAAW,IAAI,UAAU,aAAa,WAAW;AACjD,eAAW,IAAI,GAAG,kBAAiB,GAAI,YAAY,UAAU;AAC7D,UAAM,eAAe;AACrB,iBAAa,IAAI,GAAG;EAC5B;AACI,QAAM,YAAY,aAAa,EAAE;AACjC,QAAM,eAAe,WAAW,SAAS,SAAS;AAClD,aAAW,GAAG,UAAU,aAAa,WAAW;AAChD,aAAW,GAAG,GAAG,kBAAiB,GAAI,YAAY,UAAU;AAChE;AACA,SAAS,aAAa,IAAI,KAAK;AAC3B,QAAM,MAAM,GAAG,WAAW;IACtB,OAAO,IAAI,OAAO,IAAI,SAAS;IAC/B,MAAM,IAAI,MAAM,IAAI,UAAU;EACjC,GAAE,QAAQ;AACX,QAAM,QAAQ,GAAG,MAAM;AACvB,QAAM,EAAE,QAAS,IAAG;AACpB,QAAM,SAAS,QAAQ,UAAU,GAAG,UAAU,KAAK,MAAM;AACzD,MAAI,QAAQ;AACR,UAAM,QAAQ,GAAG,WAAW,KAAK,IAAI;AACrC,QAAI,OAAO;AACP,YAAM,OAAO,OAAO,OAAO,SAAS,IAAI,GAAG;AAC3C,UAAI,MAAM;AACN,kBAAU,IAAI,KAAK,IAAI;MAC1B;IACJ;EACJ;AACL;AACA,SAAS,UAAU,IAAI,KAAK,MAAM;AAC9B,QAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,QAAM,YAAY;AAClB,QAAM,OAAO,IAAI;AACjB,WAAS,KAAK,OAAO,KAAK;AAC1B,QAAM,WAAW,MAAM,sBAAA;AACvB,QAAM,aAAa,OAAO,iBAAiB,KAAK;AAChD,QAAM,aAAa,SAAS,QACxB,SAAS,OACT,WAAW,WAAW,UAAU,IAChC,WAAW,WAAW,WAAW;AACrC,QAAM,cAAc,SAAS,SACzB,SAAS,MACT,WAAW,WAAW,SAAS,IAC/B,WAAW,WAAW,YAAY;AACtC,MAAI,SAAS,IAAI;AACjB,MAAI,cAAc,OAAO,cAAc,IAAI,SAAS,MAChD,IAAI,MAAM,OAAO,cAAc,IAAI,QAAQ;AAC3C,aAAS,IAAI,MAAM;EACtB;AACD,MAAI,SAAS,GAAG;AACZ,aAAS,IAAI;EAChB;AACD,MAAI,UAAU,KAAK,IAAI,GAAG,OAAO,aAAa,aAAa,EAAE;AAC7D,MAAI,UAAU,IAAI,MAAM;AACpB,cAAU,IAAI;EACjB;AACD,QAAM,MAAM,UAAU;AACtB,QAAM,MAAM,MAAM,SAAS;AAC3B,QAAM,MAAM,OAAO,UAAU;AAC7B,MAAI;AACJ,QAAM,mBAAmB,WAAY;AACjC,iBAAa,YAAY;EACjC;AACI,QAAM,aAAa,WAAY;AAC3B,iBAAa,YAAY;AACzB,mBAAe,WAAW,WAAW,GAAG;EAChD;AACI,QAAM,YAAY,WAAY;AAC1B,eAAW,IAAI,OAAO,aAAa,gBAAgB;AACnD,eAAW,IAAI,OAAO,YAAY,UAAU;AAC5C,eAAW,IAAI,GAAG,kBAAiB,GAAI,YAAY,UAAU;AAC7D,QAAI,MAAM,MAAM,SAAS;AACrB,YAAM,MAAM,UAAU;AACtB,iBAAW,MAAM;AACb,YAAI,MAAM,YAAY;AAClB,gBAAM,OAAM;QACf;MACJ,GAAE,GAAG;IACT,WACQ,MAAM,YAAY;AACvB,YAAM,OAAM;IACf;EACT;AACI,aAAW,GAAG,OAAO,aAAa,gBAAgB;AAClD,aAAW,GAAG,OAAO,YAAY,UAAU;AAC3C,aAAW,GAAG,GAAG,kBAAiB,GAAI,YAAY,UAAU;AAChE;", "names": []}
{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/ini/ini.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ini/ini.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".ini\",\n  // we include these common regular expressions\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // sections\n      [/^\\[[^\\]]*\\]/, \"metatag\"],\n      // keys\n      [/(^\\w+)(\\s*)(\\=)/, [\"key\", \"\", \"delimiter\"]],\n      // whitespace\n      { include: \"@whitespace\" },\n      // numbers\n      [/\\d+/, \"number\"],\n      // strings: recover on non-terminated strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*[#;].*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAwBA;AAjCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA;AAAA,MAEd,SAAS;AAAA;AAAA,MAET,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,eAAe,SAAS;AAAA;AAAA,UAEzB,CAAC,mBAAmB,CAAC,OAAO,IAAI,WAAW,CAAC;AAAA;AAAA,UAE5C,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,OAAO,QAAQ;AAAA;AAAA,UAEhB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,KAAK,UAAU,WAAW;AAAA,UAC3B,CAAC,KAAK,UAAU,WAAW;AAAA,QAC7B;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,eAAe,SAAS;AAAA,QAC3B;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
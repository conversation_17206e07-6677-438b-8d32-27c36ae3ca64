{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/twig/twig.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/twig/twig.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{#\", \"#}\"]\n  },\n  brackets: [\n    [\"{#\", \"#}\"],\n    [\"{%\", \"%}\"],\n    [\"{{\", \"}}\"],\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    // HTML\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{# \", close: \" #}\" },\n    { open: \"{% \", close: \" %}\" },\n    { open: \"{{ \", close: \" }}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    // HTML\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  ignoreCase: true,\n  keywords: [\n    // (opening) tags\n    \"apply\",\n    \"autoescape\",\n    \"block\",\n    \"deprecated\",\n    \"do\",\n    \"embed\",\n    \"extends\",\n    \"flush\",\n    \"for\",\n    \"from\",\n    \"if\",\n    \"import\",\n    \"include\",\n    \"macro\",\n    \"sandbox\",\n    \"set\",\n    \"use\",\n    \"verbatim\",\n    \"with\",\n    // closing tags\n    \"endapply\",\n    \"endautoescape\",\n    \"endblock\",\n    \"endembed\",\n    \"endfor\",\n    \"endif\",\n    \"endmacro\",\n    \"endsandbox\",\n    \"endset\",\n    \"endwith\",\n    // literals\n    \"true\",\n    \"false\"\n  ],\n  tokenizer: {\n    root: [\n      // whitespace\n      [/\\s+/],\n      // Twig Tag Delimiters\n      [/{#/, \"comment.twig\", \"@commentState\"],\n      [/{%[-~]?/, \"delimiter.twig\", \"@blockState\"],\n      [/{{[-~]?/, \"delimiter.twig\", \"@variableState\"],\n      // HTML\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    /**\n     * Comment Tag Handling\n     */\n    commentState: [\n      [/#}/, \"comment.twig\", \"@pop\"],\n      [/./, \"comment.twig\"]\n    ],\n    /**\n     * Block Tag Handling\n     */\n    blockState: [\n      [/[-~]?%}/, \"delimiter.twig\", \"@pop\"],\n      // whitespace\n      [/\\s+/],\n      // verbatim\n      // Unlike other blocks, verbatim ehas its own state\n      // transition to ensure we mark its contents as strings.\n      [\n        /(verbatim)(\\s*)([-~]?%})/,\n        [\"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@rawDataState\" }]\n      ],\n      { include: \"expression\" }\n    ],\n    rawDataState: [\n      // endverbatim\n      [\n        /({%[-~]?)(\\s*)(endverbatim)(\\s*)([-~]?%})/,\n        [\"delimiter.twig\", \"\", \"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@popall\" }]\n      ],\n      [/./, \"string.twig\"]\n    ],\n    /**\n     * Variable Tag Handling\n     */\n    variableState: [[/[-~]?}}/, \"delimiter.twig\", \"@pop\"], { include: \"expression\" }],\n    stringState: [\n      // closing double quoted string\n      [/\"/, \"string.twig\", \"@pop\"],\n      // interpolation start\n      [/#{\\s*/, \"string.twig\", \"@interpolationState\"],\n      // string part\n      [/[^#\"\\\\]*(?:(?:\\\\.|#(?!\\{))[^#\"\\\\]*)*/, \"string.twig\"]\n    ],\n    interpolationState: [\n      // interpolation end\n      [/}/, \"string.twig\", \"@pop\"],\n      { include: \"expression\" }\n    ],\n    /**\n     * Expression Handling\n     */\n    expression: [\n      // whitespace\n      [/\\s+/],\n      // operators - math\n      [/\\+|-|\\/{1,2}|%|\\*{1,2}/, \"operators.twig\"],\n      // operators - logic\n      [/(and|or|not|b-and|b-xor|b-or)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - comparison (symbols)\n      [/==|!=|<|>|>=|<=/, \"operators.twig\"],\n      // operators - comparison (words)\n      [/(starts with|ends with|matches)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - containment\n      [/(in)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - test\n      [/(is)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - misc\n      [/\\||~|:|\\.{1,2}|\\?{1,2}/, \"operators.twig\"],\n      // names\n      [\n        /[^\\W\\d][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword.twig\",\n            \"@default\": \"variable.twig\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number.twig\"],\n      // punctuation\n      [/\\(|\\)|\\[|\\]|{|}|,/, \"delimiter.twig\"],\n      // strings\n      [/\"([^#\"\\\\]*(?:\\\\.[^#\"\\\\]*)*)\"|\\'([^\\'\\\\]*(?:\\\\.[^\\'\\\\]*)*)\\'/, \"string.twig\"],\n      // opening double quoted string\n      [/\"/, \"string.twig\", \"@stringState\"],\n      // misc syntactic constructs\n      // These are not operators per se, but for the purposes of lexical analysis we\n      // can treat them as such.\n      // arrow functions\n      [/=>/, \"operators.twig\"],\n      // assignment\n      [/=/, \"operators.twig\"]\n    ],\n    /**\n     * HTML\n     */\n    doctype: [\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name.html\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter.html\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name.html\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter.html\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA+BA;AAxCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,MAAM,IAAI;AAAA,QACX,CAAC,MAAM,IAAI;AAAA,QACX,CAAC,MAAM,IAAI;AAAA,QACX,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA;AAAA,QAET,CAAC,QAAQ,KAAK;AAAA,QACd,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,QAC5B,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,QAC5B,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,QAC5B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA;AAAA,QAExB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA,QAER;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,KAAK;AAAA;AAAA,UAEN,CAAC,MAAM,gBAAgB,eAAe;AAAA,UACtC,CAAC,WAAW,kBAAkB,aAAa;AAAA,UAC3C,CAAC,WAAW,kBAAkB,gBAAgB;AAAA;AAAA,UAE9C,CAAC,aAAa,gBAAgB,UAAU;AAAA,UACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,UACnC,CAAC,uCAAuC,CAAC,kBAAkB,YAAY,IAAI,gBAAgB,CAAC;AAAA,UAC5F,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,UAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,UACxE,CAAC,6BAA6B,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC1F,CAAC,+BAA+B,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,UAC5F,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,QAAQ;AAAA;AAAA,QAEX;AAAA;AAAA;AAAA;AAAA,QAIA,cAAc;AAAA,UACZ,CAAC,MAAM,gBAAgB,MAAM;AAAA,UAC7B,CAAC,KAAK,cAAc;AAAA,QACtB;AAAA;AAAA;AAAA;AAAA,QAIA,YAAY;AAAA,UACV,CAAC,WAAW,kBAAkB,MAAM;AAAA;AAAA,UAEpC,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,UAIN;AAAA,YACE;AAAA,YACA,CAAC,gBAAgB,IAAI,EAAE,OAAO,kBAAkB,MAAM,gBAAgB,CAAC;AAAA,UACzE;AAAA,UACA,EAAE,SAAS,aAAa;AAAA,QAC1B;AAAA,QACA,cAAc;AAAA;AAAA,UAEZ;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,IAAI,gBAAgB,IAAI,EAAE,OAAO,kBAAkB,MAAM,UAAU,CAAC;AAAA,UACzF;AAAA,UACA,CAAC,KAAK,aAAa;AAAA,QACrB;AAAA;AAAA;AAAA;AAAA,QAIA,eAAe,CAAC,CAAC,WAAW,kBAAkB,MAAM,GAAG,EAAE,SAAS,aAAa,CAAC;AAAA,QAChF,aAAa;AAAA;AAAA,UAEX,CAAC,KAAK,eAAe,MAAM;AAAA;AAAA,UAE3B,CAAC,SAAS,eAAe,qBAAqB;AAAA;AAAA,UAE9C,CAAC,wCAAwC,aAAa;AAAA,QACxD;AAAA,QACA,oBAAoB;AAAA;AAAA,UAElB,CAAC,KAAK,eAAe,MAAM;AAAA,UAC3B,EAAE,SAAS,aAAa;AAAA,QAC1B;AAAA;AAAA;AAAA;AAAA,QAIA,YAAY;AAAA;AAAA,UAEV,CAAC,KAAK;AAAA;AAAA,UAEN,CAAC,0BAA0B,gBAAgB;AAAA;AAAA,UAE3C,CAAC,sCAAsC,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,UAE7D,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,wCAAwC,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,UAE/D,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,UAEpC,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAAA;AAAA,UAEpC,CAAC,0BAA0B,gBAAgB;AAAA;AAAA,UAE3C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,eAAe,aAAa;AAAA;AAAA,UAE7B,CAAC,qBAAqB,gBAAgB;AAAA;AAAA,UAEtC,CAAC,+DAA+D,aAAa;AAAA;AAAA,UAE7E,CAAC,KAAK,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,UAKnC,CAAC,MAAM,gBAAgB;AAAA;AAAA,UAEvB,CAAC,KAAK,gBAAgB;AAAA,QACxB;AAAA;AAAA;AAAA;AAAA,QAIA,SAAS;AAAA,UACP,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,QAC9B;AAAA,QACA,SAAS;AAAA,UACP,CAAC,OAAO,gBAAgB,MAAM;AAAA,UAC9B,CAAC,SAAS,sBAAsB;AAAA,UAChC,CAAC,KAAK,sBAAsB;AAAA,QAC9B;AAAA,QACA,UAAU;AAAA,UACR,CAAC,QAAQ,kBAAkB,MAAM;AAAA,UACjC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,WAAW,qBAAqB;AAAA,UACjC,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,YAAY;AAAA;AAAA,QAEf;AAAA;AAAA;AAAA,QAGA,QAAQ;AAAA,UACN,CAAC,QAAQ,uBAAuB,kBAAkB;AAAA,UAClD,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,WAAW,qBAAqB;AAAA,UACjC,CAAC,KAAK,gBAAgB;AAAA,UACtB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,iBAAiB;AAAA,UACf,CAAC,KAAK,kBAAkB,wBAAwB;AAAA,UAChD;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,WAAW,qBAAqB;AAAA,UACjC,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACvD;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UACvE,CAAC,SAAS,EAAE;AAAA,QACd;AAAA;AAAA;AAAA;AAAA,QAIA,OAAO;AAAA,UACL,CAAC,QAAQ,uBAAuB,iBAAiB;AAAA,UACjD,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,WAAW,qBAAqB;AAAA,UACjC,CAAC,KAAK,gBAAgB;AAAA,UACtB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,YAAY;AAAA;AAAA,UAEb;AAAA,YACE;AAAA,YACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA;AAAA,QAEA,gBAAgB;AAAA,UACd,CAAC,KAAK,kBAAkB,uBAAuB;AAAA,UAC/C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,sBAAsB;AAAA,UACpB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA;AAAA,QAEA,qBAAqB;AAAA,UACnB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,aAAa,sBAAsB;AAAA,UACpC,CAAC,WAAW,qBAAqB;AAAA,UACjC,CAAC,KAAK,gBAAgB;AAAA,UACtB,CAAC,YAAY;AAAA;AAAA,UAEb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,QACtD;AAAA,QACA,eAAe;AAAA,UACb,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UACtE,CAAC,SAAS,EAAE;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
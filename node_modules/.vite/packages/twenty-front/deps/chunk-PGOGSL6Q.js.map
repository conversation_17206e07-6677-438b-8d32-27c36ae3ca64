{"version": 3, "sources": ["../../../../graphql-ws/lib/utils.js", "../../../../graphql-ws/lib/common.js", "../../../../graphql-ws/lib/client.js", "../../../../graphql-ws/lib/server.js", "../../../../graphql-ws/lib/index.js", "../../../../@graphiql/toolkit/src/async-helpers/index.ts", "../../../../@graphiql/toolkit/src/create-fetcher/lib.ts", "../../../../meros/browser/index.mjs", "../../../../@n1ru4l/push-pull-async-iterable-iterator/index.mjs", "../../../../@graphiql/toolkit/src/create-fetcher/createFetcher.ts", "../../../../@graphiql/toolkit/src/format/index.ts", "../../../../@graphiql/toolkit/src/graphql-helpers/auto-complete.ts", "../../../../@graphiql/toolkit/src/graphql-helpers/merge-ast.ts", "../../../../@graphiql/toolkit/src/graphql-helpers/operation-name.ts", "../../../../@graphiql/toolkit/src/storage/base.ts", "../../../../@graphiql/toolkit/src/storage/history.ts", "../../../../@graphiql/toolkit/src/storage/query.ts", "../../../../@graphiql/toolkit/src/storage/custom.ts"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.limitCloseReason = exports.areGraphQLErrors = exports.isAsyncGenerator = exports.isAsyncIterable = exports.isObject = exports.extendedTypeof = void 0;\n/** @private */\nfunction extendedTypeof(val) {\n    if (val === null) {\n        return 'null';\n    }\n    if (Array.isArray(val)) {\n        return 'array';\n    }\n    return typeof val;\n}\nexports.extendedTypeof = extendedTypeof;\n/** @private */\nfunction isObject(val) {\n    return extendedTypeof(val) === 'object';\n}\nexports.isObject = isObject;\n/** @private */\nfunction isAsyncIterable(val) {\n    return typeof Object(val)[Symbol.asyncIterator] === 'function';\n}\nexports.isAsyncIterable = isAsyncIterable;\n/** @private */\nfunction isAsyncGenerator(val) {\n    return (isObject(val) &&\n        typeof Object(val)[Symbol.asyncIterator] === 'function' &&\n        typeof val.return === 'function'\n    // for lazy ones, we only need the return anyway\n    // typeof val.throw === 'function' &&\n    // typeof val.next === 'function'\n    );\n}\nexports.isAsyncGenerator = isAsyncGenerator;\n/** @private */\nfunction areGraphQLErrors(obj) {\n    return (Array.isArray(obj) &&\n        // must be at least one error\n        obj.length > 0 &&\n        // error has at least a message\n        obj.every((ob) => 'message' in ob));\n}\nexports.areGraphQLErrors = areGraphQLErrors;\n/**\n * Limits the WebSocket close event reason to not exceed a length of one frame.\n * Reference: https://datatracker.ietf.org/doc/html/rfc6455#section-5.2.\n *\n * @private\n */\nfunction limitCloseReason(reason, whenTooLong) {\n    return reason.length < 124 ? reason : whenTooLong;\n}\nexports.limitCloseReason = limitCloseReason;\n", "\"use strict\";\n/**\n *\n * common\n *\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.stringifyMessage = exports.parseMessage = exports.isMessage = exports.validateMessage = exports.MessageType = exports.CloseCode = exports.DEPRECATED_GRAPHQL_WS_PROTOCOL = exports.GRAPHQL_TRANSPORT_WS_PROTOCOL = void 0;\nconst utils_1 = require(\"./utils\");\n/**\n * The WebSocket sub-protocol used for the [GraphQL over WebSocket Protocol](/PROTOCOL.md).\n *\n * @category Common\n */\nexports.GRAPHQL_TRANSPORT_WS_PROTOCOL = 'graphql-transport-ws';\n/**\n * The deprecated subprotocol used by [subscriptions-transport-ws](https://github.com/apollographql/subscriptions-transport-ws).\n *\n * @private\n */\nexports.DEPRECATED_GRAPHQL_WS_PROTOCOL = 'graphql-ws';\n/**\n * `graphql-ws` expected and standard close codes of the [GraphQL over WebSocket Protocol](/PROTOCOL.md).\n *\n * @category Common\n */\nvar CloseCode;\n(function (CloseCode) {\n    CloseCode[CloseCode[\"InternalServerError\"] = 4500] = \"InternalServerError\";\n    CloseCode[CloseCode[\"InternalClientError\"] = 4005] = \"InternalClientError\";\n    CloseCode[CloseCode[\"BadRequest\"] = 4400] = \"BadRequest\";\n    CloseCode[CloseCode[\"BadResponse\"] = 4004] = \"BadResponse\";\n    /** Tried subscribing before connect ack */\n    CloseCode[CloseCode[\"Unauthorized\"] = 4401] = \"Unauthorized\";\n    CloseCode[CloseCode[\"Forbidden\"] = 4403] = \"Forbidden\";\n    CloseCode[CloseCode[\"SubprotocolNotAcceptable\"] = 4406] = \"SubprotocolNotAcceptable\";\n    CloseCode[CloseCode[\"ConnectionInitialisationTimeout\"] = 4408] = \"ConnectionInitialisationTimeout\";\n    CloseCode[CloseCode[\"ConnectionAcknowledgementTimeout\"] = 4504] = \"ConnectionAcknowledgementTimeout\";\n    /** Subscriber distinction is very important */\n    CloseCode[CloseCode[\"SubscriberAlreadyExists\"] = 4409] = \"SubscriberAlreadyExists\";\n    CloseCode[CloseCode[\"TooManyInitialisationRequests\"] = 4429] = \"TooManyInitialisationRequests\";\n})(CloseCode = exports.CloseCode || (exports.CloseCode = {}));\n/**\n * Types of messages allowed to be sent by the client/server over the WS protocol.\n *\n * @category Common\n */\nvar MessageType;\n(function (MessageType) {\n    MessageType[\"ConnectionInit\"] = \"connection_init\";\n    MessageType[\"ConnectionAck\"] = \"connection_ack\";\n    MessageType[\"Ping\"] = \"ping\";\n    MessageType[\"Pong\"] = \"pong\";\n    MessageType[\"Subscribe\"] = \"subscribe\";\n    MessageType[\"Next\"] = \"next\";\n    MessageType[\"Error\"] = \"error\";\n    MessageType[\"Complete\"] = \"complete\";\n})(MessageType = exports.MessageType || (exports.MessageType = {}));\n/**\n * Validates the message against the GraphQL over WebSocket Protocol.\n *\n * Invalid messages will throw descriptive errors.\n *\n * @category Common\n */\nfunction validateMessage(val) {\n    if (!(0, utils_1.isObject)(val)) {\n        throw new Error(`Message is expected to be an object, but got ${(0, utils_1.extendedTypeof)(val)}`);\n    }\n    if (!val.type) {\n        throw new Error(`Message is missing the 'type' property`);\n    }\n    if (typeof val.type !== 'string') {\n        throw new Error(`Message is expects the 'type' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.type)}`);\n    }\n    switch (val.type) {\n        case MessageType.ConnectionInit:\n        case MessageType.ConnectionAck:\n        case MessageType.Ping:\n        case MessageType.Pong: {\n            if (val.payload != null && !(0, utils_1.isObject)(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object or nullish or missing, but got \"${val.payload}\"`);\n            }\n            break;\n        }\n        case MessageType.Subscribe: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!(0, utils_1.isObject)(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${(0, utils_1.extendedTypeof)(val.payload)}`);\n            }\n            if (typeof val.payload.query !== 'string') {\n                throw new Error(`\"${val.type}\" message payload expects the 'query' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.payload.query)}`);\n            }\n            if (val.payload.variables != null && !(0, utils_1.isObject)(val.payload.variables)) {\n                throw new Error(`\"${val.type}\" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.variables)}`);\n            }\n            if (val.payload.operationName != null &&\n                (0, utils_1.extendedTypeof)(val.payload.operationName) !== 'string') {\n                throw new Error(`\"${val.type}\" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.operationName)}`);\n            }\n            if (val.payload.extensions != null && !(0, utils_1.isObject)(val.payload.extensions)) {\n                throw new Error(`\"${val.type}\" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.extensions)}`);\n            }\n            break;\n        }\n        case MessageType.Next: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!(0, utils_1.isObject)(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${(0, utils_1.extendedTypeof)(val.payload)}`);\n            }\n            break;\n        }\n        case MessageType.Error: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            if (!(0, utils_1.areGraphQLErrors)(val.payload)) {\n                throw new Error(`\"${val.type}\" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(val.payload)}`);\n            }\n            break;\n        }\n        case MessageType.Complete: {\n            if (typeof val.id !== 'string') {\n                throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);\n            }\n            if (!val.id) {\n                throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n            }\n            break;\n        }\n        default:\n            throw new Error(`Invalid message 'type' property \"${val.type}\"`);\n    }\n    return val;\n}\nexports.validateMessage = validateMessage;\n/**\n * Checks if the provided value is a valid GraphQL over WebSocket message.\n *\n * @deprecated Use `validateMessage` instead.\n *\n * @category Common\n */\nfunction isMessage(val) {\n    try {\n        validateMessage(val);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nexports.isMessage = isMessage;\n/**\n * Parses the raw websocket message data to a valid message.\n *\n * @category Common\n */\nfunction parseMessage(data, reviver) {\n    return validateMessage(typeof data === 'string' ? JSON.parse(data, reviver) : data);\n}\nexports.parseMessage = parseMessage;\n/**\n * Stringifies a valid message ready to be sent through the socket.\n *\n * @category Common\n */\nfunction stringifyMessage(msg, replacer) {\n    validateMessage(msg);\n    return JSON.stringify(msg, replacer);\n}\nexports.stringifyMessage = stringifyMessage;\n", "\"use strict\";\n/**\n *\n * client\n *\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createClient = void 0;\nconst common_1 = require(\"./common\");\nconst utils_1 = require(\"./utils\");\n/** This file is the entry point for browsers, re-export common elements. */\n__exportStar(require(\"./common\"), exports);\n/**\n * Creates a disposable GraphQL over WebSocket client.\n *\n * @category Client\n */\nfunction createClient(options) {\n    const { url, connectionParams, lazy = true, onNonLazyError = console.error, lazyCloseTimeout: lazyCloseTimeoutMs = 0, keepAlive = 0, disablePong, connectionAckWaitTimeout = 0, retryAttempts = 5, retryWait = async function randomisedExponentialBackoff(retries) {\n        let retryDelay = 1000; // start with 1s delay\n        for (let i = 0; i < retries; i++) {\n            retryDelay *= 2;\n        }\n        await new Promise((resolve) => setTimeout(resolve, retryDelay +\n            // add random timeout from 300ms to 3s\n            Math.floor(Math.random() * (3000 - 300) + 300)));\n    }, shouldRetry = isLikeCloseEvent, isFatalConnectionProblem, on, webSocketImpl, \n    /**\n     * Generates a v4 UUID to be used as the ID using `Math`\n     * as the random number generator. Supply your own generator\n     * in case you need more uniqueness.\n     *\n     * Reference: https://gist.github.com/jed/982883\n     */\n    generateID = function generateUUID() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }, jsonMessageReplacer: replacer, jsonMessageReviver: reviver, } = options;\n    let ws;\n    if (webSocketImpl) {\n        if (!isWebSocket(webSocketImpl)) {\n            throw new Error('Invalid WebSocket implementation provided');\n        }\n        ws = webSocketImpl;\n    }\n    else if (typeof WebSocket !== 'undefined') {\n        ws = WebSocket;\n    }\n    else if (typeof global !== 'undefined') {\n        ws =\n            global.WebSocket ||\n                // @ts-expect-error: Support more browsers\n                global.MozWebSocket;\n    }\n    else if (typeof window !== 'undefined') {\n        ws =\n            window.WebSocket ||\n                // @ts-expect-error: Support more browsers\n                window.MozWebSocket;\n    }\n    if (!ws)\n        throw new Error(\"WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`\");\n    const WebSocketImpl = ws;\n    // websocket status emitter, subscriptions are handled differently\n    const emitter = (() => {\n        const message = (() => {\n            const listeners = {};\n            return {\n                on(id, listener) {\n                    listeners[id] = listener;\n                    return () => {\n                        delete listeners[id];\n                    };\n                },\n                emit(message) {\n                    var _a;\n                    if ('id' in message)\n                        (_a = listeners[message.id]) === null || _a === void 0 ? void 0 : _a.call(listeners, message);\n                },\n            };\n        })();\n        const listeners = {\n            connecting: (on === null || on === void 0 ? void 0 : on.connecting) ? [on.connecting] : [],\n            opened: (on === null || on === void 0 ? void 0 : on.opened) ? [on.opened] : [],\n            connected: (on === null || on === void 0 ? void 0 : on.connected) ? [on.connected] : [],\n            ping: (on === null || on === void 0 ? void 0 : on.ping) ? [on.ping] : [],\n            pong: (on === null || on === void 0 ? void 0 : on.pong) ? [on.pong] : [],\n            message: (on === null || on === void 0 ? void 0 : on.message) ? [message.emit, on.message] : [message.emit],\n            closed: (on === null || on === void 0 ? void 0 : on.closed) ? [on.closed] : [],\n            error: (on === null || on === void 0 ? void 0 : on.error) ? [on.error] : [],\n        };\n        return {\n            onMessage: message.on,\n            on(event, listener) {\n                const l = listeners[event];\n                l.push(listener);\n                return () => {\n                    l.splice(l.indexOf(listener), 1);\n                };\n            },\n            emit(event, ...args) {\n                // we copy the listeners so that unlistens dont \"pull the rug under our feet\"\n                for (const listener of [...listeners[event]]) {\n                    // @ts-expect-error: The args should fit\n                    listener(...args);\n                }\n            },\n        };\n    })();\n    // invokes the callback either when an error or closed event is emitted,\n    // first one that gets called prevails, other emissions are ignored\n    function errorOrClosed(cb) {\n        const listening = [\n            // errors are fatal and more critical than close events, throw them first\n            emitter.on('error', (err) => {\n                listening.forEach((unlisten) => unlisten());\n                cb(err);\n            }),\n            // closes can be graceful and not fatal, throw them second (if error didnt throw)\n            emitter.on('closed', (event) => {\n                listening.forEach((unlisten) => unlisten());\n                cb(event);\n            }),\n        ];\n    }\n    let connecting, locks = 0, lazyCloseTimeout, retrying = false, retries = 0, disposed = false;\n    async function connect() {\n        // clear the lazy close timeout immediatelly so that close gets debounced\n        // see: https://github.com/enisdenjo/graphql-ws/issues/388\n        clearTimeout(lazyCloseTimeout);\n        const [socket, throwOnClose] = await (connecting !== null && connecting !== void 0 ? connecting : (connecting = new Promise((connected, denied) => (async () => {\n            if (retrying) {\n                await retryWait(retries);\n                // subscriptions might complete while waiting for retry\n                if (!locks) {\n                    connecting = undefined;\n                    return denied({ code: 1000, reason: 'All Subscriptions Gone' });\n                }\n                retries++;\n            }\n            emitter.emit('connecting');\n            const socket = new WebSocketImpl(typeof url === 'function' ? await url() : url, common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL);\n            let connectionAckTimeout, queuedPing;\n            function enqueuePing() {\n                if (isFinite(keepAlive) && keepAlive > 0) {\n                    clearTimeout(queuedPing); // in case where a pong was received before a ping (this is valid behaviour)\n                    queuedPing = setTimeout(() => {\n                        if (socket.readyState === WebSocketImpl.OPEN) {\n                            socket.send((0, common_1.stringifyMessage)({ type: common_1.MessageType.Ping }));\n                            emitter.emit('ping', false, undefined);\n                        }\n                    }, keepAlive);\n                }\n            }\n            errorOrClosed((errOrEvent) => {\n                connecting = undefined;\n                clearTimeout(connectionAckTimeout);\n                clearTimeout(queuedPing);\n                denied(errOrEvent);\n                if (isLikeCloseEvent(errOrEvent) && errOrEvent.code === 4499) {\n                    socket.close(4499, 'Terminated'); // close event is artificial and emitted manually, see `Client.terminate()` below\n                    socket.onerror = null;\n                    socket.onclose = null;\n                }\n            });\n            socket.onerror = (err) => emitter.emit('error', err);\n            socket.onclose = (event) => emitter.emit('closed', event);\n            socket.onopen = async () => {\n                try {\n                    emitter.emit('opened', socket);\n                    const payload = typeof connectionParams === 'function'\n                        ? await connectionParams()\n                        : connectionParams;\n                    // connectionParams might take too long causing the server to kick off the client\n                    // the necessary error/close event is already reported - simply stop execution\n                    if (socket.readyState !== WebSocketImpl.OPEN)\n                        return;\n                    socket.send((0, common_1.stringifyMessage)(payload\n                        ? {\n                            type: common_1.MessageType.ConnectionInit,\n                            payload,\n                        }\n                        : {\n                            type: common_1.MessageType.ConnectionInit,\n                            // payload is completely absent if not provided\n                        }, replacer));\n                    if (isFinite(connectionAckWaitTimeout) &&\n                        connectionAckWaitTimeout > 0) {\n                        connectionAckTimeout = setTimeout(() => {\n                            socket.close(common_1.CloseCode.ConnectionAcknowledgementTimeout, 'Connection acknowledgement timeout');\n                        }, connectionAckWaitTimeout);\n                    }\n                    enqueuePing(); // enqueue ping (noop if disabled)\n                }\n                catch (err) {\n                    emitter.emit('error', err);\n                    socket.close(common_1.CloseCode.InternalClientError, (0, utils_1.limitCloseReason)(err instanceof Error ? err.message : new Error(err).message, 'Internal client error'));\n                }\n            };\n            let acknowledged = false;\n            socket.onmessage = ({ data }) => {\n                try {\n                    const message = (0, common_1.parseMessage)(data, reviver);\n                    emitter.emit('message', message);\n                    if (message.type === 'ping' || message.type === 'pong') {\n                        emitter.emit(message.type, true, message.payload); // received\n                        if (message.type === 'pong') {\n                            enqueuePing(); // enqueue next ping (noop if disabled)\n                        }\n                        else if (!disablePong) {\n                            // respond with pong on ping\n                            socket.send((0, common_1.stringifyMessage)(message.payload\n                                ? {\n                                    type: common_1.MessageType.Pong,\n                                    payload: message.payload,\n                                }\n                                : {\n                                    type: common_1.MessageType.Pong,\n                                    // payload is completely absent if not provided\n                                }));\n                            emitter.emit('pong', false, message.payload);\n                        }\n                        return; // ping and pongs can be received whenever\n                    }\n                    if (acknowledged)\n                        return; // already connected and acknowledged\n                    if (message.type !== common_1.MessageType.ConnectionAck)\n                        throw new Error(`First message cannot be of type ${message.type}`);\n                    clearTimeout(connectionAckTimeout);\n                    acknowledged = true;\n                    emitter.emit('connected', socket, message.payload); // connected = socket opened + acknowledged\n                    retrying = false; // future lazy connects are not retries\n                    retries = 0; // reset the retries on connect\n                    connected([\n                        socket,\n                        new Promise((_, reject) => errorOrClosed(reject)),\n                    ]);\n                }\n                catch (err) {\n                    socket.onmessage = null; // stop reading messages as soon as reading breaks once\n                    emitter.emit('error', err);\n                    socket.close(common_1.CloseCode.BadResponse, (0, utils_1.limitCloseReason)(err instanceof Error ? err.message : new Error(err).message, 'Bad response'));\n                }\n            };\n        })())));\n        // if the provided socket is in a closing state, wait for the throw on close\n        if (socket.readyState === WebSocketImpl.CLOSING)\n            await throwOnClose;\n        let release = () => {\n            // releases this connection\n        };\n        const released = new Promise((resolve) => (release = resolve));\n        return [\n            socket,\n            release,\n            Promise.race([\n                // wait for\n                released.then(() => {\n                    if (!locks) {\n                        // and if no more locks are present, complete the connection\n                        const complete = () => socket.close(1000, 'Normal Closure');\n                        if (isFinite(lazyCloseTimeoutMs) && lazyCloseTimeoutMs > 0) {\n                            // if the keepalive is set, allow for the specified calmdown time and\n                            // then complete if the socket is still open.\n                            lazyCloseTimeout = setTimeout(() => {\n                                if (socket.readyState === WebSocketImpl.OPEN)\n                                    complete();\n                            }, lazyCloseTimeoutMs);\n                        }\n                        else {\n                            // otherwise complete immediately\n                            complete();\n                        }\n                    }\n                }),\n                // or\n                throwOnClose,\n            ]),\n        ];\n    }\n    /**\n     * Checks the `connect` problem and evaluates if the client should retry.\n     */\n    function shouldRetryConnectOrThrow(errOrCloseEvent) {\n        // some close codes are worth reporting immediately\n        if (isLikeCloseEvent(errOrCloseEvent) &&\n            (isFatalInternalCloseCode(errOrCloseEvent.code) ||\n                [\n                    common_1.CloseCode.InternalServerError,\n                    common_1.CloseCode.InternalClientError,\n                    common_1.CloseCode.BadRequest,\n                    common_1.CloseCode.BadResponse,\n                    common_1.CloseCode.Unauthorized,\n                    // CloseCode.Forbidden, might grant access out after retry\n                    common_1.CloseCode.SubprotocolNotAcceptable,\n                    // CloseCode.ConnectionInitialisationTimeout, might not time out after retry\n                    // CloseCode.ConnectionAcknowledgementTimeout, might not time out after retry\n                    common_1.CloseCode.SubscriberAlreadyExists,\n                    common_1.CloseCode.TooManyInitialisationRequests,\n                    // 4499, // Terminated, probably because the socket froze, we want to retry\n                ].includes(errOrCloseEvent.code)))\n            throw errOrCloseEvent;\n        // client was disposed, no retries should proceed regardless\n        if (disposed)\n            return false;\n        // normal closure (possibly all subscriptions have completed)\n        // if no locks were acquired in the meantime, shouldnt try again\n        if (isLikeCloseEvent(errOrCloseEvent) && errOrCloseEvent.code === 1000)\n            return locks > 0;\n        // retries are not allowed or we tried to many times, report error\n        if (!retryAttempts || retries >= retryAttempts)\n            throw errOrCloseEvent;\n        // throw non-retryable connection problems\n        if (!shouldRetry(errOrCloseEvent))\n            throw errOrCloseEvent;\n        // @deprecated throw fatal connection problems immediately\n        if (isFatalConnectionProblem === null || isFatalConnectionProblem === void 0 ? void 0 : isFatalConnectionProblem(errOrCloseEvent))\n            throw errOrCloseEvent;\n        // looks good, start retrying\n        return (retrying = true);\n    }\n    // in non-lazy (hot?) mode always hold one connection lock to persist the socket\n    if (!lazy) {\n        (async () => {\n            locks++;\n            for (;;) {\n                try {\n                    const [, , throwOnClose] = await connect();\n                    await throwOnClose; // will always throw because releaser is not used\n                }\n                catch (errOrCloseEvent) {\n                    try {\n                        if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                            return;\n                    }\n                    catch (errOrCloseEvent) {\n                        // report thrown error, no further retries\n                        return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(errOrCloseEvent);\n                    }\n                }\n            }\n        })();\n    }\n    return {\n        on: emitter.on,\n        subscribe(payload, sink) {\n            const id = generateID(payload);\n            let done = false, errored = false, releaser = () => {\n                // for handling completions before connect\n                locks--;\n                done = true;\n            };\n            (async () => {\n                locks++;\n                for (;;) {\n                    try {\n                        const [socket, release, waitForReleaseOrThrowOnClose] = await connect();\n                        // if done while waiting for connect, release the connection lock right away\n                        if (done)\n                            return release();\n                        const unlisten = emitter.onMessage(id, (message) => {\n                            switch (message.type) {\n                                case common_1.MessageType.Next: {\n                                    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- payload will fit type\n                                    sink.next(message.payload);\n                                    return;\n                                }\n                                case common_1.MessageType.Error: {\n                                    (errored = true), (done = true);\n                                    sink.error(message.payload);\n                                    releaser();\n                                    return;\n                                }\n                                case common_1.MessageType.Complete: {\n                                    done = true;\n                                    releaser(); // release completes the sink\n                                    return;\n                                }\n                            }\n                        });\n                        socket.send((0, common_1.stringifyMessage)({\n                            id,\n                            type: common_1.MessageType.Subscribe,\n                            payload,\n                        }, replacer));\n                        releaser = () => {\n                            if (!done && socket.readyState === WebSocketImpl.OPEN)\n                                // if not completed already and socket is open, send complete message to server on release\n                                socket.send((0, common_1.stringifyMessage)({\n                                    id,\n                                    type: common_1.MessageType.Complete,\n                                }, replacer));\n                            locks--;\n                            done = true;\n                            release();\n                        };\n                        // either the releaser will be called, connection completed and\n                        // the promise resolved or the socket closed and the promise rejected.\n                        // whatever happens though, we want to stop listening for messages\n                        await waitForReleaseOrThrowOnClose.finally(unlisten);\n                        return; // completed, shouldnt try again\n                    }\n                    catch (errOrCloseEvent) {\n                        if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                            return;\n                    }\n                }\n            })()\n                .then(() => {\n                // delivering either an error or a complete terminates the sequence\n                if (!errored)\n                    sink.complete();\n            }) // resolves on release or normal closure\n                .catch((err) => {\n                sink.error(err);\n            }); // rejects on close events and errors\n            return () => {\n                // dispose only of active subscriptions\n                if (!done)\n                    releaser();\n            };\n        },\n        async dispose() {\n            disposed = true;\n            if (connecting) {\n                // if there is a connection, close it\n                const [socket] = await connecting;\n                socket.close(1000, 'Normal Closure');\n            }\n        },\n        terminate() {\n            if (connecting) {\n                // only if there is a connection\n                emitter.emit('closed', {\n                    code: 4499,\n                    reason: 'Terminated',\n                    wasClean: false,\n                });\n            }\n        },\n    };\n}\nexports.createClient = createClient;\nfunction isLikeCloseEvent(val) {\n    return (0, utils_1.isObject)(val) && 'code' in val && 'reason' in val;\n}\nfunction isFatalInternalCloseCode(code) {\n    if ([\n        1000,\n        1001,\n        1006,\n        1005,\n        1012,\n        1013,\n        1013, // Bad Gateway\n    ].includes(code))\n        return false;\n    // all other internal errors are fatal\n    return code >= 1000 && code <= 1999;\n}\nfunction isWebSocket(val) {\n    return (typeof val === 'function' &&\n        'constructor' in val &&\n        'CLOSED' in val &&\n        'CLOSING' in val &&\n        'CONNECTING' in val &&\n        'OPEN' in val);\n}\n", "\"use strict\";\n/**\n *\n * server\n *\n */\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.handleProtocols = exports.makeServer = void 0;\nconst graphql_1 = require(\"graphql\");\nconst common_1 = require(\"./common\");\nconst utils_1 = require(\"./utils\");\n/**\n * Makes a Protocol complient WebSocket GraphQL server. The server\n * is actually an API which is to be used with your favourite WebSocket\n * server library!\n *\n * Read more about the Protocol in the PROTOCOL.md documentation file.\n *\n * @category Server\n */\nfunction makeServer(options) {\n    const { schema, context, roots, validate, execute, subscribe, connectionInitWaitTimeout = 3000, // 3 seconds\n    onConnect, onDisconnect, onClose, onSubscribe, onOperation, onNext, onError, onComplete, jsonMessageReviver: reviver, jsonMessageReplacer: replacer, } = options;\n    return {\n        opened(socket, extra) {\n            const ctx = {\n                connectionInitReceived: false,\n                acknowledged: false,\n                subscriptions: {},\n                extra,\n            };\n            if (socket.protocol !== common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL) {\n                socket.close(common_1.CloseCode.SubprotocolNotAcceptable, 'Subprotocol not acceptable');\n                return async (code, reason) => {\n                    /* nothing was set up, just notify the closure */\n                    await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));\n                };\n            }\n            // kick the client off (close socket) if the connection has\n            // not been initialised after the specified wait timeout\n            const connectionInitWait = connectionInitWaitTimeout > 0 && isFinite(connectionInitWaitTimeout)\n                ? setTimeout(() => {\n                    if (!ctx.connectionInitReceived)\n                        socket.close(common_1.CloseCode.ConnectionInitialisationTimeout, 'Connection initialisation timeout');\n                }, connectionInitWaitTimeout)\n                : null;\n            socket.onMessage(async function onMessage(data) {\n                var _a, e_1, _b, _c;\n                var _d;\n                let message;\n                try {\n                    message = (0, common_1.parseMessage)(data, reviver);\n                }\n                catch (err) {\n                    return socket.close(common_1.CloseCode.BadRequest, 'Invalid message received');\n                }\n                switch (message.type) {\n                    case common_1.MessageType.ConnectionInit: {\n                        if (ctx.connectionInitReceived)\n                            return socket.close(common_1.CloseCode.TooManyInitialisationRequests, 'Too many initialisation requests');\n                        // @ts-expect-error: I can write\n                        ctx.connectionInitReceived = true;\n                        if ((0, utils_1.isObject)(message.payload))\n                            // @ts-expect-error: I can write\n                            ctx.connectionParams = message.payload;\n                        const permittedOrPayload = await (onConnect === null || onConnect === void 0 ? void 0 : onConnect(ctx));\n                        if (permittedOrPayload === false)\n                            return socket.close(common_1.CloseCode.Forbidden, 'Forbidden');\n                        await socket.send((0, common_1.stringifyMessage)((0, utils_1.isObject)(permittedOrPayload)\n                            ? {\n                                type: common_1.MessageType.ConnectionAck,\n                                payload: permittedOrPayload,\n                            }\n                            : {\n                                type: common_1.MessageType.ConnectionAck,\n                                // payload is completely absent if not provided\n                            }, replacer));\n                        // @ts-expect-error: I can write\n                        ctx.acknowledged = true;\n                        return;\n                    }\n                    case common_1.MessageType.Ping: {\n                        if (socket.onPing)\n                            // if the onPing listener is registered, automatic pong is disabled\n                            return await socket.onPing(message.payload);\n                        await socket.send((0, common_1.stringifyMessage)(message.payload\n                            ? { type: common_1.MessageType.Pong, payload: message.payload }\n                            : {\n                                type: common_1.MessageType.Pong,\n                                // payload is completely absent if not provided\n                            }));\n                        return;\n                    }\n                    case common_1.MessageType.Pong:\n                        return await ((_d = socket.onPong) === null || _d === void 0 ? void 0 : _d.call(socket, message.payload));\n                    case common_1.MessageType.Subscribe: {\n                        if (!ctx.acknowledged)\n                            return socket.close(common_1.CloseCode.Unauthorized, 'Unauthorized');\n                        const { id, payload } = message;\n                        if (id in ctx.subscriptions)\n                            return socket.close(common_1.CloseCode.SubscriberAlreadyExists, `Subscriber for ${id} already exists`);\n                        // if this turns out to be a streaming operation, the subscription value\n                        // will change to an `AsyncIterable`, otherwise it will stay as is\n                        ctx.subscriptions[id] = null;\n                        const emit = {\n                            next: async (result, args) => {\n                                let nextMessage = {\n                                    id,\n                                    type: common_1.MessageType.Next,\n                                    payload: result,\n                                };\n                                const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, nextMessage, args, result));\n                                if (maybeResult)\n                                    nextMessage = Object.assign(Object.assign({}, nextMessage), { payload: maybeResult });\n                                await socket.send((0, common_1.stringifyMessage)(nextMessage, replacer));\n                            },\n                            error: async (errors) => {\n                                let errorMessage = {\n                                    id,\n                                    type: common_1.MessageType.Error,\n                                    payload: errors,\n                                };\n                                const maybeErrors = await (onError === null || onError === void 0 ? void 0 : onError(ctx, errorMessage, errors));\n                                if (maybeErrors)\n                                    errorMessage = Object.assign(Object.assign({}, errorMessage), { payload: maybeErrors });\n                                await socket.send((0, common_1.stringifyMessage)(errorMessage, replacer));\n                            },\n                            complete: async (notifyClient) => {\n                                const completeMessage = {\n                                    id,\n                                    type: common_1.MessageType.Complete,\n                                };\n                                await (onComplete === null || onComplete === void 0 ? void 0 : onComplete(ctx, completeMessage));\n                                if (notifyClient)\n                                    await socket.send((0, common_1.stringifyMessage)(completeMessage, replacer));\n                            },\n                        };\n                        try {\n                            let execArgs;\n                            const maybeExecArgsOrErrors = await (onSubscribe === null || onSubscribe === void 0 ? void 0 : onSubscribe(ctx, message));\n                            if (maybeExecArgsOrErrors) {\n                                if ((0, utils_1.areGraphQLErrors)(maybeExecArgsOrErrors))\n                                    return await emit.error(maybeExecArgsOrErrors);\n                                else if (Array.isArray(maybeExecArgsOrErrors))\n                                    throw new Error('Invalid return value from onSubscribe hook, expected an array of GraphQLError objects');\n                                // not errors, is exec args\n                                execArgs = maybeExecArgsOrErrors;\n                            }\n                            else {\n                                // you either provide a schema dynamically through\n                                // `onSubscribe` or you set one up during the server setup\n                                if (!schema)\n                                    throw new Error('The GraphQL schema is not provided');\n                                const args = {\n                                    operationName: payload.operationName,\n                                    document: (0, graphql_1.parse)(payload.query),\n                                    variableValues: payload.variables,\n                                };\n                                execArgs = Object.assign(Object.assign({}, args), { schema: typeof schema === 'function'\n                                        ? await schema(ctx, message, args)\n                                        : schema });\n                                const validationErrors = (validate !== null && validate !== void 0 ? validate : graphql_1.validate)(execArgs.schema, execArgs.document);\n                                if (validationErrors.length > 0)\n                                    return await emit.error(validationErrors);\n                            }\n                            const operationAST = (0, graphql_1.getOperationAST)(execArgs.document, execArgs.operationName);\n                            if (!operationAST)\n                                return await emit.error([\n                                    new graphql_1.GraphQLError('Unable to identify operation'),\n                                ]);\n                            // if `onSubscribe` didnt specify a rootValue, inject one\n                            if (!('rootValue' in execArgs))\n                                execArgs.rootValue = roots === null || roots === void 0 ? void 0 : roots[operationAST.operation];\n                            // if `onSubscribe` didn't specify a context, inject one\n                            if (!('contextValue' in execArgs))\n                                execArgs.contextValue =\n                                    typeof context === 'function'\n                                        ? await context(ctx, message, execArgs)\n                                        : context;\n                            // the execution arguments have been prepared\n                            // perform the operation and act accordingly\n                            let operationResult;\n                            if (operationAST.operation === 'subscription')\n                                operationResult = await (subscribe !== null && subscribe !== void 0 ? subscribe : graphql_1.subscribe)(execArgs);\n                            // operation === 'query' || 'mutation'\n                            else\n                                operationResult = await (execute !== null && execute !== void 0 ? execute : graphql_1.execute)(execArgs);\n                            const maybeResult = await (onOperation === null || onOperation === void 0 ? void 0 : onOperation(ctx, message, execArgs, operationResult));\n                            if (maybeResult)\n                                operationResult = maybeResult;\n                            if ((0, utils_1.isAsyncIterable)(operationResult)) {\n                                /** multiple emitted results */\n                                if (!(id in ctx.subscriptions)) {\n                                    // subscription was completed/canceled before the operation settled\n                                    if ((0, utils_1.isAsyncGenerator)(operationResult))\n                                        operationResult.return(undefined);\n                                }\n                                else {\n                                    ctx.subscriptions[id] = operationResult;\n                                    try {\n                                        for (var _e = true, operationResult_1 = __asyncValues(operationResult), operationResult_1_1; operationResult_1_1 = await operationResult_1.next(), _a = operationResult_1_1.done, !_a;) {\n                                            _c = operationResult_1_1.value;\n                                            _e = false;\n                                            try {\n                                                const result = _c;\n                                                await emit.next(result, execArgs);\n                                            }\n                                            finally {\n                                                _e = true;\n                                            }\n                                        }\n                                    }\n                                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                                    finally {\n                                        try {\n                                            if (!_e && !_a && (_b = operationResult_1.return)) await _b.call(operationResult_1);\n                                        }\n                                        finally { if (e_1) throw e_1.error; }\n                                    }\n                                }\n                            }\n                            else {\n                                /** single emitted result */\n                                // if the client completed the subscription before the single result\n                                // became available, he effectively canceled it and no data should be sent\n                                if (id in ctx.subscriptions)\n                                    await emit.next(operationResult, execArgs);\n                            }\n                            // lack of subscription at this point indicates that the client\n                            // completed the subscription, he doesnt need to be reminded\n                            await emit.complete(id in ctx.subscriptions);\n                        }\n                        finally {\n                            // whatever happens to the subscription, we finally want to get rid of the reservation\n                            delete ctx.subscriptions[id];\n                        }\n                        return;\n                    }\n                    case common_1.MessageType.Complete: {\n                        const subscription = ctx.subscriptions[message.id];\n                        delete ctx.subscriptions[message.id]; // deleting the subscription means no further activity should take place\n                        if ((0, utils_1.isAsyncGenerator)(subscription))\n                            await subscription.return(undefined);\n                        return;\n                    }\n                    default:\n                        throw new Error(`Unexpected message of type ${message.type} received`);\n                }\n            });\n            // wait for close, cleanup and the disconnect callback\n            return async (code, reason) => {\n                if (connectionInitWait)\n                    clearTimeout(connectionInitWait);\n                for (const sub of Object.values(ctx.subscriptions)) {\n                    if ((0, utils_1.isAsyncGenerator)(sub))\n                        await sub.return(undefined);\n                }\n                if (ctx.acknowledged)\n                    await (onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect(ctx, code, reason));\n                await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));\n            };\n        },\n    };\n}\nexports.makeServer = makeServer;\n/**\n * Helper utility for choosing the \"graphql-transport-ws\" subprotocol from\n * a set of WebSocket subprotocols.\n *\n * Accepts a set of already extracted WebSocket subprotocols or the raw\n * Sec-WebSocket-Protocol header value. In either case, if the right\n * protocol appears, it will be returned.\n *\n * By specification, the server should not provide a value with Sec-WebSocket-Protocol\n * if it does not agree with client's subprotocols. The client has a responsibility\n * to handle the connection afterwards.\n *\n * @category Server\n */\nfunction handleProtocols(protocols) {\n    switch (true) {\n        case protocols instanceof Set &&\n            protocols.has(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL):\n        case Array.isArray(protocols) &&\n            protocols.includes(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL):\n        case typeof protocols === 'string' &&\n            protocols\n                .split(',')\n                .map((p) => p.trim())\n                .includes(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL):\n            return common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL;\n        default:\n            return false;\n    }\n}\nexports.handleProtocols = handleProtocols;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./client\"), exports);\n__exportStar(require(\"./server\"), exports);\n__exportStar(require(\"./common\"), exports);\n", "import {\n  Fetcher<PERSON><PERSON>ult,\n  FetcherReturnType,\n  Observable,\n} from '../create-fetcher';\n\n// Duck-type promise detection.\nexport function isPromise<T>(value: Promise<T> | any): value is Promise<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    typeof value.then === 'function'\n  );\n}\n\n// Duck-type Observable.take(1).toPromise()\nfunction observableToPromise<T>(observable: Observable<T>): Promise<T> {\n  return new Promise((resolve, reject) => {\n    const subscription = observable.subscribe({\n      next(v) {\n        resolve(v);\n        subscription.unsubscribe();\n      },\n      error: reject,\n      complete() {\n        reject(new Error('no value resolved'));\n      },\n    });\n  });\n}\n\n// Duck-type observable detection.\nexport function isObservable<T>(value: any): value is Observable<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    'subscribe' in value &&\n    typeof value.subscribe === 'function'\n  );\n}\n\nexport function isAsyncIterable(\n  input: unknown,\n): input is AsyncIterable<unknown> {\n  return (\n    typeof input === 'object' &&\n    input !== null &&\n    // Some browsers still don't have Symbol.asyncIterator implemented (iOS Safari)\n    // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})\n    ((input as any)[Symbol.toStringTag] === 'AsyncGenerator' ||\n      Symbol.asyncIterator in input)\n  );\n}\n\nasync function asyncIterableToPromise<T>(\n  input: AsyncIterable<T> | AsyncIterableIterator<T>,\n): Promise<T> {\n  // Also support AsyncGenerator on Safari iOS.\n  // As mentioned in the isAsyncIterable function, there is no Symbol.asyncIterator available,\n  // so every AsyncIterable must be implemented using AsyncGenerator.\n  const iteratorReturn = (\n    'return' in input ? input : input[Symbol.asyncIterator]()\n  ).return?.bind(input);\n  const iteratorNext = (\n    'next' in input ? input : input[Symbol.asyncIterator]()\n  ).next.bind(input);\n\n  const result = await iteratorNext();\n  // ensure cleanup\n  void iteratorReturn?.();\n  return result.value;\n}\n\nexport async function fetcherReturnToPromise(\n  fetcherResult: FetcherReturnType,\n): Promise<FetcherResult> {\n  const result = await fetcherResult;\n  if (isAsyncIterable(result)) {\n    return asyncIterableToPromise(result);\n  }\n  if (isObservable(result)) {\n    return observableToPromise(result);\n  }\n  return result;\n}\n", "import { DocumentNode, visit } from 'graphql';\nimport { meros } from 'meros';\nimport {\n  Client,\n  ClientOptions,\n  ExecutionResult,\n  createClient as createClientType,\n} from 'graphql-ws';\nimport {\n  isAsyncIterable,\n  makeAsyncIterableIteratorFromSink,\n} from '@n1ru4l/push-pull-async-iterable-iterator';\n\nimport type {\n  <PERSON><PERSON>er,\n  FetcherParams,\n  FetcherOpts,\n  ExecutionResultPayload,\n  CreateFetcherOptions,\n} from './types';\n\nconst errorHasCode = (err: unknown): err is { code: string } => {\n  return typeof err === 'object' && err !== null && 'code' in err;\n};\n\n/**\n * Returns true if the name matches a subscription in the AST\n *\n * @param document {DocumentNode}\n * @param name the operation name to lookup\n * @returns {boolean}\n */\nexport const isSubscriptionWithName = (\n  document: DocumentNode,\n  name: string | undefined,\n): boolean => {\n  let isSubscription = false;\n  visit(document, {\n    OperationDefinition(node) {\n      if (name === node.name?.value && node.operation === 'subscription') {\n        isSubscription = true;\n      }\n    },\n  });\n  return isSubscription;\n};\n\n/**\n * create a simple HTTP/S fetcher using a fetch implementation where\n * multipart is not needed\n *\n * @param options {CreateFetcherOptions}\n * @param httpFetch {typeof fetch}\n * @returns {Fetcher}\n */\nexport const createSimpleFetcher =\n  (options: CreateFetcherOptions, httpFetch: typeof fetch): Fetcher =>\n  async (graphQLParams: FetcherParams, fetcherOpts?: FetcherOpts) => {\n    const data = await httpFetch(options.url, {\n      method: 'POST',\n      body: JSON.stringify(graphQLParams),\n      headers: {\n        'content-type': 'application/json',\n        ...options.headers,\n        ...fetcherOpts?.headers,\n      },\n    });\n    return data.json();\n  };\n\nexport const createWebsocketsFetcherFromUrl = (\n  url: string,\n  connectionParams?: ClientOptions['connectionParams'],\n) => {\n  let wsClient;\n  try {\n    const { createClient } = require('graphql-ws') as {\n      createClient: typeof createClientType;\n    };\n\n    // TODO: defaults?\n    wsClient = createClient({\n      url,\n      connectionParams,\n    });\n    return createWebsocketsFetcherFromClient(wsClient);\n  } catch (err) {\n    if (errorHasCode(err) && err.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        \"You need to install the 'graphql-ws' package to use websockets when passing a 'subscriptionUrl'\",\n      );\n    }\n    // eslint-disable-next-line no-console\n    console.error(`Error creating websocket client for ${url}`, err);\n  }\n};\n\n/**\n * Create ws/s fetcher using provided wsClient implementation\n *\n * @param wsClient {Client}\n * @returns {Fetcher}\n */\nexport const createWebsocketsFetcherFromClient =\n  (wsClient: Client) => (graphQLParams: FetcherParams) =>\n    makeAsyncIterableIteratorFromSink<ExecutionResult>(sink =>\n      wsClient.subscribe(graphQLParams, {\n        ...sink,\n        error(err) {\n          if (err instanceof CloseEvent) {\n            sink.error(\n              new Error(\n                `Socket closed with event ${err.code} ${\n                  err.reason || ''\n                }`.trim(),\n              ),\n            );\n          } else {\n            sink.error(err);\n          }\n        },\n      }),\n    );\n\n/**\n * Allow legacy websockets protocol client, but no definitions for it,\n * as the library is deprecated and has security issues\n *\n * @param legacyWsClient\n * @returns\n */\nexport const createLegacyWebsocketsFetcher =\n  (legacyWsClient: { request: (params: FetcherParams) => unknown }) =>\n  (graphQLParams: FetcherParams) => {\n    const observable = legacyWsClient.request(graphQLParams);\n    return makeAsyncIterableIteratorFromSink<ExecutionResult>(\n      // @ts-ignore\n      sink => observable.subscribe(sink).unsubscribe,\n    );\n  };\n/**\n * create a fetcher with the `IncrementalDelivery` HTTP/S spec for\n * `@stream` and `@defer` support using `fetch-multipart-graphql`\n *\n * @param options {CreateFetcherOptions}\n * @returns {Fetcher}\n */\nexport const createMultipartFetcher = (\n  options: CreateFetcherOptions,\n  httpFetch: typeof fetch,\n): Fetcher =>\n  async function* (graphQLParams: FetcherParams, fetcherOpts?: FetcherOpts) {\n    const response = await httpFetch(options.url, {\n      method: 'POST',\n      body: JSON.stringify(graphQLParams),\n      headers: {\n        'content-type': 'application/json',\n        accept: 'application/json, multipart/mixed',\n        ...options.headers,\n        // allow user-defined headers to override\n        // the static provided headers\n        ...fetcherOpts?.headers,\n      },\n    }).then(r =>\n      meros<Extract<ExecutionResultPayload, { hasNext: boolean }>>(r, {\n        multiple: true,\n      }),\n    );\n\n    // Follows the same as createSimpleFetcher above, in that we simply return it as json.\n    if (!isAsyncIterable(response)) {\n      return yield response.json();\n    }\n\n    for await (const chunk of response) {\n      if (chunk.some(part => !part.json)) {\n        const message = chunk.map(\n          part => `Headers::\\n${part.headers}\\n\\nBody::\\n${part.body}`,\n        );\n        throw new Error(\n          `Expected multipart chunks to be of json type. got:\\n${message}`,\n        );\n      }\n      yield chunk.map(part => part.body);\n    }\n  };\n\n/**\n * If `wsClient` or `legacyClient` are provided, then `subscriptionUrl` is overridden.\n * @param options {CreateFetcherOptions}\n * @returns\n */\nexport const getWsFetcher = (\n  options: CreateFetcherOptions,\n  fetcherOpts: FetcherOpts | undefined,\n) => {\n  if (options.wsClient) {\n    return createWebsocketsFetcherFromClient(options.wsClient);\n  }\n  if (options.subscriptionUrl) {\n    return createWebsocketsFetcherFromUrl(options.subscriptionUrl, {\n      ...options.wsConnectionParams,\n      ...fetcherOpts?.headers,\n    });\n  }\n  const legacyWebsocketsClient = options.legacyClient || options.legacyWsClient;\n  if (legacyWebsocketsClient) {\n    return createLegacyWebsocketsFetcher(legacyWebsocketsClient);\n  }\n};\n", "var e=new TextDecoder;async function t(t,n){if(!t.ok||!t.body||t.bodyUsed)return t;let i=t.headers.get(\"content-type\");if(!i||!~i.indexOf(\"multipart/\"))return t;let l=i.indexOf(\"boundary=\"),r=\"-\";if(~l){let e=l+9,t=i.indexOf(\";\",e);r=i.slice(e,t>-1?t:void 0).trim().replace(/\"/g,\"\")}return async function*(t,n,i){let l,r,d,o=t.getReader(),a=!i||!i.multiple,f=n.length,s=\"\",c=[];try{let t;e:for(;!(t=await o.read()).done;){let i=e.decode(t.value);l=s.length,s+=i;let o=i.indexOf(n);for(~o?l+=o:l=s.indexOf(n),c=[];~l;){let e=s.slice(0,l),t=s.slice(l+f);if(r){let n=e.indexOf(\"\\r\\n\\r\\n\")+4,i=e.lastIndexOf(\"\\r\\n\",n),l=!1,r=e.slice(n,i>-1?void 0:i),o=String(e.slice(0,n)).trim().split(\"\\r\\n\"),f={},s=o.length;for(;d=o[--s];d=d.split(\": \"),f[d.shift().toLowerCase()]=d.join(\": \"));if(d=f[\"content-type\"],d&&~d.indexOf(\"application/json\"))try{r=JSON.parse(r),l=!0}catch(e){}if(d={headers:f,body:r,json:l},a?yield d:c.push(d),\"--\"===t.slice(0,2))break e}else n=\"\\r\\n\"+n,r=f+=2;s=t,l=s.indexOf(n)}c.length&&(yield c)}}finally{c.length&&(yield c),await o.cancel()}}(t.body,`--${r}`,n)}export{t as meros};", "/**\r\n * Attaches a cleanup handler to a AsyncIterable.\r\n *\r\n * @param source The source that should have a return handler attached\r\n * @param onReturn The return handler that should be attached\r\n * @returns\r\n */\r\nfunction withHandlers(source, onReturn, onThrow) {\r\n    const stream = (async function* withReturnSource() {\r\n        yield* source;\r\n    })();\r\n    const originalReturn = stream.return.bind(stream);\r\n    if (onReturn) {\r\n        stream.return = (...args) => {\r\n            onReturn();\r\n            return originalReturn(...args);\r\n        };\r\n    }\r\n    if (onThrow) {\r\n        const originalThrow = stream.throw.bind(stream);\r\n        stream.throw = (err) => {\r\n            onThrow(err);\r\n            return originalThrow(err);\r\n        };\r\n    }\r\n    return stream;\r\n}\n\nfunction createDeferred() {\r\n    const d = {};\r\n    d.promise = new Promise((resolve, reject) => {\r\n        d.resolve = resolve;\r\n        d.reject = reject;\r\n    });\r\n    return d;\r\n}\r\n/**\r\n * makePushPullAsyncIterableIterator\r\n *\r\n * The iterable will publish values until return or throw is called.\r\n * Afterwards it is in the completed state and cannot be used for publishing any further values.\r\n * It will handle back-pressure and keep pushed values until they are consumed by a source.\r\n */\r\nfunction makePushPullAsyncIterableIterator() {\r\n    let state = {\r\n        type: \"running\" /* running */\r\n    };\r\n    let next = createDeferred();\r\n    const values = [];\r\n    function pushValue(value) {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        values.push(value);\r\n        next.resolve();\r\n        next = createDeferred();\r\n    }\r\n    const source = (async function* PushPullAsyncIterableIterator() {\r\n        while (true) {\r\n            if (values.length > 0) {\r\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n                yield values.shift();\r\n            }\r\n            else {\r\n                if (state.type === \"error\" /* error */) {\r\n                    throw state.error;\r\n                }\r\n                if (state.type === \"finished\" /* finished */) {\r\n                    return;\r\n                }\r\n                await next.promise;\r\n            }\r\n        }\r\n    })();\r\n    const stream = withHandlers(source, () => {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        state = {\r\n            type: \"finished\" /* finished */\r\n        };\r\n        next.resolve();\r\n    }, (error) => {\r\n        if (state.type !== \"running\" /* running */) {\r\n            return;\r\n        }\r\n        state = {\r\n            type: \"error\" /* error */,\r\n            error\r\n        };\r\n        next.resolve();\r\n    });\r\n    return {\r\n        pushValue,\r\n        asyncIterableIterator: stream\r\n    };\r\n}\n\nconst makeAsyncIterableIteratorFromSink = (make) => {\r\n    const { pushValue, asyncIterableIterator } = makePushPullAsyncIterableIterator();\r\n    const dispose = make({\r\n        next: (value) => {\r\n            pushValue(value);\r\n        },\r\n        complete: () => {\r\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n            asyncIterableIterator.return();\r\n        },\r\n        error: (err) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n            asyncIterableIterator.throw(err);\r\n        }\r\n    });\r\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\r\n    const originalReturn = asyncIterableIterator.return;\r\n    let returnValue = undefined;\r\n    asyncIterableIterator.return = () => {\r\n        if (returnValue === undefined) {\r\n            dispose();\r\n            returnValue = originalReturn();\r\n        }\r\n        return returnValue;\r\n    };\r\n    return asyncIterableIterator;\r\n};\n\nfunction applyAsyncIterableIteratorToSink(asyncIterableIterator, sink) {\r\n    const run = async () => {\r\n        try {\r\n            for await (const value of asyncIterableIterator) {\r\n                sink.next(value);\r\n            }\r\n            sink.complete();\r\n        }\r\n        catch (err) {\r\n            sink.error(err);\r\n        }\r\n    };\r\n    run();\r\n    return () => {\r\n        var _a;\r\n        (_a = asyncIterableIterator.return) === null || _a === void 0 ? void 0 : _a.call(asyncIterableIterator);\r\n    };\r\n}\n\nfunction isAsyncIterable(input) {\r\n    return (typeof input === \"object\" &&\r\n        input !== null &&\r\n        // The AsyncGenerator check is for Safari on iOS which currently does not have\r\n        // Symbol.asyncIterator implemented\r\n        // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        (input[Symbol.toStringTag] === \"AsyncGenerator\" ||\r\n            (Symbol.asyncIterator && Symbol.asyncIterator in input)));\r\n}\n\n/**\r\n * Attaches a cleanup handler from and AsyncIterable to an AsyncIterable.\r\n *\r\n * @param source\r\n * @param target\r\n */\r\nfunction withHandlersFrom(\r\n/** The source that should be returned with attached handlers. */\r\nsource, \r\n/**The target on which the return and throw methods should be called. */\r\ntarget) {\r\n    return withHandlers(source, () => { var _a; return (_a = target.return) === null || _a === void 0 ? void 0 : _a.call(target); }, err => { var _a; return (_a = target.throw) === null || _a === void 0 ? void 0 : _a.call(target, err); });\r\n}\n\nfunction filter(filter) {\r\n    return async function* filterGenerator(asyncIterable) {\r\n        for await (const value of asyncIterable) {\r\n            if (filter(value)) {\r\n                yield value;\r\n            }\r\n        }\r\n    };\r\n}\n\n/**\r\n * Map the events published by an AsyncIterable.\r\n */\r\nconst map = (map) => async function* mapGenerator(asyncIterable) {\r\n    for await (const value of asyncIterable) {\r\n        yield map(value);\r\n    }\r\n};\n\nexport { applyAsyncIterableIteratorToSink, filter, isAsyncIterable, makeAsyncIterableIteratorFromSink, makePushPullAsyncIterableIterator, map, withHandlers, withHandlersFrom };\n", "import type { Fetcher, CreateFetcherOptions } from './types';\n\nimport {\n  createMultipartFetcher,\n  createSimpleFetcher,\n  isSubscriptionWithName,\n  getWsFetcher,\n} from './lib';\n\n/**\n * build a GraphiQL fetcher that is:\n * - backwards compatible\n * - optionally supports graphql-ws or `\n *\n * @param options {CreateFetcherOptions}\n * @returns {Fetcher}\n */\nexport function createGraphiQLFetcher(options: CreateFetcherOptions): Fetcher {\n  let httpFetch;\n  if (typeof window !== 'undefined' && window.fetch) {\n    httpFetch = window.fetch;\n  }\n  if (\n    options?.enableIncrementalDelivery === null ||\n    options.enableIncrementalDelivery !== false\n  ) {\n    options.enableIncrementalDelivery = true;\n  }\n  if (options.fetch) {\n    httpFetch = options.fetch;\n  }\n  if (!httpFetch) {\n    throw new Error('No valid fetcher implementation available');\n  }\n  // simpler fetcher for schema requests\n  const simpleFetcher = createSimpleFetcher(options, httpFetch);\n\n  const httpFetcher = options.enableIncrementalDelivery\n    ? createMultipartFetcher(options, httpFetch)\n    : simpleFetcher;\n\n  return (graphQLParams, fetcherOpts) => {\n    if (graphQLParams.operationName === 'IntrospectionQuery') {\n      return (options.schemaFetcher || simpleFetcher)(\n        graphQLParams,\n        fetcherOpts,\n      );\n    }\n    const isSubscription = fetcherOpts?.documentAST\n      ? isSubscriptionWithName(\n          fetcherOpts.documentAST,\n          graphQLParams.operationName || undefined,\n        )\n      : false;\n    if (isSubscription) {\n      const wsFetcher = getWsFetcher(options, fetcherOpts);\n\n      if (!wsFetcher) {\n        throw new Error(\n          `Your GraphiQL createFetcher is not properly configured for websocket subscriptions yet. ${\n            options.subscriptionUrl\n              ? `Provided URL ${options.subscriptionUrl} failed`\n              : 'Please provide subscriptionUrl, wsClient or legacyClient option first.'\n          }`,\n        );\n      }\n      return wsFetcher(graphQLParams);\n    }\n    return httpFetcher(graphQLParams, fetcherOpts);\n  };\n}\n", "function stringify(obj: unknown): string {\n  return JSON.stringify(obj, null, 2);\n}\n\nfunction formatSingleError(error: Error): Error {\n  return {\n    ...error,\n    // Raise these details even if they're non-enumerable\n    message: error.message,\n    stack: error.stack,\n  };\n}\n\nfunction handleSingleError(error: unknown) {\n  if (error instanceof Error) {\n    return formatSingleError(error);\n  }\n  return error;\n}\n\nexport function formatError(error: unknown): string {\n  if (Array.isArray(error)) {\n    return stringify({\n      errors: error.map(e => handleSingleError(e)),\n    });\n  }\n  return stringify({ errors: [handleSingleError(error)] });\n}\n\nexport function formatResult(result: any): string {\n  return stringify(result);\n}\n", "import {\n  DocumentNode,\n  getNamedType,\n  GraphQLOutputType,\n  GraphQLSchema,\n  GraphQLType,\n  isLeafType,\n  Kind,\n  parse,\n  print,\n  SelectionSetNode,\n  TypeInfo,\n  visit,\n} from 'graphql';\n\ntype Insertion = {\n  index: number;\n  string: string;\n};\n\nexport type GetDefaultFieldNamesFn = (type: GraphQLType) => string[];\n\n/**\n * Given a document string which may not be valid due to terminal fields not\n * representing leaf values (Spec Section: \"Leaf Field Selections\"), and a\n * function which provides reasonable default field names for a given type,\n * this function will attempt to produce a schema which is valid after filling\n * in selection sets for the invalid fields.\n *\n * Note that there is no guarantee that the result will be a valid query, this\n * utility represents a \"best effort\" which may be useful within IDE tools.\n */\nexport function fillLeafs(\n  schema?: GraphQLSchema | null,\n  docString?: string,\n  getDefaultFieldNames?: GetDefaultFieldNamesFn,\n) {\n  const insertions: Insertion[] = [];\n\n  if (!schema || !docString) {\n    return { insertions, result: docString };\n  }\n\n  let ast: DocumentNode;\n  try {\n    ast = parse(docString);\n  } catch {\n    return { insertions, result: docString };\n  }\n\n  const fieldNameFn = getDefaultFieldNames || defaultGetDefaultFieldNames;\n  const typeInfo = new TypeInfo(schema);\n  visit(ast, {\n    leave(node) {\n      typeInfo.leave(node);\n    },\n    enter(node) {\n      typeInfo.enter(node);\n      if (node.kind === 'Field' && !node.selectionSet) {\n        const fieldType = typeInfo.getType();\n        const selectionSet = buildSelectionSet(\n          isFieldType(fieldType) as GraphQLOutputType,\n          fieldNameFn,\n        );\n        if (selectionSet && node.loc) {\n          const indent = getIndentation(docString, node.loc.start);\n          insertions.push({\n            index: node.loc.end,\n            string: ' ' + print(selectionSet).replaceAll('\\n', '\\n' + indent),\n          });\n        }\n      }\n    },\n  });\n\n  // Apply the insertions, but also return the insertions metadata.\n  return {\n    insertions,\n    result: withInsertions(docString, insertions),\n  };\n}\n\n// The default function to use for producing the default fields from a type.\n// This function first looks for some common patterns, and falls back to\n// including all leaf-type fields.\nfunction defaultGetDefaultFieldNames(type: GraphQLType) {\n  // If this type cannot access fields, then return an empty set.\n  // if (!type.getFields) {\n  if (!('getFields' in type)) {\n    return [];\n  }\n\n  const fields = type.getFields();\n\n  // Is there an `id` field?\n  if (fields.id) {\n    return ['id'];\n  }\n\n  // Is there an `edges` field?\n  if (fields.edges) {\n    return ['edges'];\n  }\n\n  // Is there an `node` field?\n  if (fields.node) {\n    return ['node'];\n  }\n\n  // Include all leaf-type fields.\n  const leafFieldNames: Array<string> = [];\n  for (const fieldName of Object.keys(fields)) {\n    if (isLeafType(fields[fieldName].type)) {\n      leafFieldNames.push(fieldName);\n    }\n  }\n  return leafFieldNames;\n}\n\n// Given a GraphQL type, and a function which produces field names, recursively\n// generate a SelectionSet which includes default fields.\nfunction buildSelectionSet(\n  type: GraphQLOutputType,\n  getDefaultFieldNames: GetDefaultFieldNamesFn,\n): SelectionSetNode | undefined {\n  // Unwrap any non-null or list types.\n  const namedType = getNamedType(type);\n\n  // Unknown types and leaf types do not have selection sets.\n  if (!type || isLeafType(type)) {\n    return;\n  }\n\n  // Get an array of field names to use.\n  const fieldNames = getDefaultFieldNames(namedType);\n\n  // If there are no field names to use, return no selection set.\n  if (\n    !Array.isArray(fieldNames) ||\n    fieldNames.length === 0 ||\n    !('getFields' in namedType)\n  ) {\n    return;\n  }\n\n  // Build a selection set of each field, calling buildSelectionSet recursively.\n  return {\n    kind: Kind.SELECTION_SET,\n    selections: fieldNames.map(fieldName => {\n      const fieldDef = namedType.getFields()[fieldName];\n      const fieldType = fieldDef ? fieldDef.type : null;\n      return {\n        kind: Kind.FIELD,\n        name: {\n          kind: Kind.NAME,\n          value: fieldName,\n        },\n        // we can use as here, because we already know that fieldType\n        // comes from an origin parameter\n        selectionSet: buildSelectionSet(fieldType!, getDefaultFieldNames),\n      };\n    }),\n  };\n}\n\n// Given an initial string, and a list of \"insertion\" { index, string } objects,\n// return a new string with these insertions applied.\nfunction withInsertions(initial: string, insertions: Insertion[]) {\n  if (insertions.length === 0) {\n    return initial;\n  }\n  let edited = '';\n  let prevIndex = 0;\n  for (const { index, string } of insertions) {\n    edited += initial.slice(prevIndex, index) + string;\n    prevIndex = index;\n  }\n  edited += initial.slice(prevIndex);\n  return edited;\n}\n\n// Given a string and an index, look backwards to find the string of whitespace\n// following the next previous line break.\nfunction getIndentation(str: string, index: number) {\n  let indentStart = index;\n  let indentEnd = index;\n  while (indentStart) {\n    const c = str.charCodeAt(indentStart - 1);\n    // line break\n    if (c === 10 || c === 13 || c === 0x2028 || c === 0x2029) {\n      break;\n    }\n    indentStart--;\n    // not white space\n    if (c !== 9 && c !== 11 && c !== 12 && c !== 32 && c !== 160) {\n      indentEnd = indentStart;\n    }\n  }\n  return str.slice(indentStart, indentEnd);\n}\n\nfunction isFieldType(\n  fieldType: GraphQLOutputType | null | undefined,\n): GraphQLOutputType | void {\n  if (fieldType) {\n    return fieldType;\n  }\n}\n", "import {\n  DocumentNode,\n  FieldNode,\n  FragmentDefinitionNode,\n  GraphQLOutputType,\n  GraphQLSchema,\n  SelectionNode,\n  TypeInfo,\n  getNamedType,\n  visit,\n  visitWithTypeInfo,\n  ASTVisitor,\n  Kind,\n} from 'graphql';\n\nfunction uniqueBy<T>(\n  array: readonly SelectionNode[],\n  iteratee: (item: FieldNode) => T,\n) {\n  const FilteredMap = new Map<T, FieldNode>();\n  const result: SelectionNode[] = [];\n  for (const item of array) {\n    if (item.kind === 'Field') {\n      const uniqueValue = iteratee(item);\n      const existing = FilteredMap.get(uniqueValue);\n      if (item.directives?.length) {\n        // Cannot inline fields with directives (yet)\n        const itemClone = { ...item };\n        result.push(itemClone);\n      } else if (existing?.selectionSet && item.selectionSet) {\n        // Merge the selection sets\n        existing.selectionSet.selections = [\n          ...existing.selectionSet.selections,\n          ...item.selectionSet.selections,\n        ];\n      } else if (!existing) {\n        const itemClone = { ...item };\n        FilteredMap.set(uniqueValue, itemClone);\n        result.push(itemClone);\n      }\n    } else {\n      result.push(item);\n    }\n  }\n  return result;\n}\n\nfunction inlineRelevantFragmentSpreads(\n  fragmentDefinitions: {\n    [key: string]: FragmentDefinitionNode | undefined;\n  },\n  selections: readonly SelectionNode[],\n  selectionSetType?: GraphQLOutputType | null,\n): readonly SelectionNode[] {\n  const selectionSetTypeName = selectionSetType\n    ? getNamedType(selectionSetType).name\n    : null;\n  const outputSelections = [];\n  const seenSpreads: string[] = [];\n  for (let selection of selections) {\n    if (selection.kind === 'FragmentSpread') {\n      const fragmentName = selection.name.value;\n      if (!selection.directives || selection.directives.length === 0) {\n        if (seenSpreads.includes(fragmentName)) {\n          /* It's a duplicate - skip it! */\n          continue;\n        } else {\n          seenSpreads.push(fragmentName);\n        }\n      }\n      const fragmentDefinition = fragmentDefinitions[selection.name.value];\n      if (fragmentDefinition) {\n        const { typeCondition, directives, selectionSet } = fragmentDefinition;\n        selection = {\n          kind: Kind.INLINE_FRAGMENT,\n          typeCondition,\n          directives,\n          selectionSet,\n        };\n      }\n    }\n    if (\n      selection.kind === Kind.INLINE_FRAGMENT &&\n      // Cannot inline if there are directives\n      (!selection.directives || selection.directives?.length === 0)\n    ) {\n      const fragmentTypeName = selection.typeCondition\n        ? selection.typeCondition.name.value\n        : null;\n      if (!fragmentTypeName || fragmentTypeName === selectionSetTypeName) {\n        outputSelections.push(\n          ...inlineRelevantFragmentSpreads(\n            fragmentDefinitions,\n            selection.selectionSet.selections,\n            selectionSetType,\n          ),\n        );\n        continue;\n      }\n    }\n    outputSelections.push(selection);\n  }\n  return outputSelections;\n}\n\n/**\n * Given a document AST, inline all named fragment definitions.\n */\nexport function mergeAst(\n  documentAST: DocumentNode,\n  schema?: GraphQLSchema | null,\n): DocumentNode {\n  // If we're given the schema, we can simplify even further by resolving object\n  // types vs unions/interfaces\n  const typeInfo = schema ? new TypeInfo(schema) : null;\n\n  const fragmentDefinitions: {\n    [key: string]: FragmentDefinitionNode | undefined;\n  } = Object.create(null);\n\n  for (const definition of documentAST.definitions) {\n    if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n      fragmentDefinitions[definition.name.value] = definition;\n    }\n  }\n\n  const flattenVisitors: ASTVisitor = {\n    SelectionSet(node: any) {\n      const selectionSetType = typeInfo ? typeInfo.getParentType() : null;\n      let { selections } = node;\n\n      selections = inlineRelevantFragmentSpreads(\n        fragmentDefinitions,\n        selections,\n        selectionSetType,\n      );\n\n      return {\n        ...node,\n        selections,\n      };\n    },\n    FragmentDefinition() {\n      return null;\n    },\n  };\n\n  const flattenedAST = visit(\n    documentAST,\n    typeInfo ? visitWithTypeInfo(typeInfo, flattenVisitors) : flattenVisitors,\n  );\n\n  const deduplicateVisitors: ASTVisitor = {\n    SelectionSet(node: any) {\n      let { selections } = node;\n\n      selections = uniqueBy(selections, selection =>\n        selection.alias ? selection.alias.value : selection.name.value,\n      );\n\n      return {\n        ...node,\n        selections,\n      };\n    },\n    FragmentDefinition() {\n      return null;\n    },\n  };\n\n  return visit(flattenedAST, deduplicateVisitors);\n}\n", "import { OperationDefinitionNode } from 'graphql';\n\n/**\n * Provided optional previous operations and selected name, and a next list of\n * operations, determine what the next selected operation should be.\n */\nexport function getSelectedOperationName(\n  prevOperations?: OperationDefinitionNode[] | undefined,\n  prevSelectedOperationName?: string,\n  operations?: OperationDefinitionNode[],\n) {\n  // If there are not enough operations to bother with, return nothing.\n  if (!operations || operations.length < 1) {\n    return;\n  }\n\n  // If a previous selection still exists, continue to use it.\n  const names = operations.map(op => op.name?.value);\n  if (prevSelectedOperationName && names.includes(prevSelectedOperationName)) {\n    return prevSelectedOperationName;\n  }\n\n  // If a previous selection was the Nth operation, use the same Nth.\n  if (prevSelectedOperationName && prevOperations) {\n    const prevNames = prevOperations.map(op => op.name?.value);\n    const prevIndex = prevNames.indexOf(prevSelectedOperationName);\n    if (prevIndex !== -1 && prevIndex < names.length) {\n      return names[prevIndex];\n    }\n  }\n\n  // Use the first operation.\n  return names[0];\n}\n", "/**\n * This describes the attributes and methods that a store has to support in\n * order to be used with GraphiQL. It closely resembles the `localStorage`\n * API as it is the default storage used in GraphiQL.\n */\nexport type Storage = {\n  /**\n   * Retrieve an item from the store by its key.\n   * @param key The key of the item to retrieve.\n   * @returns {?string} The stored value for the given key if it exists, `null`\n   * otherwise.\n   */\n  getItem(key: string): string | null;\n  /**\n   * Add a value to the store for a given key. If there already exists a value\n   * for the given key, this method will override the value.\n   * @param key The key to store the value for.\n   * @param value The value to store.\n   */\n  setItem(key: string, value: string): void;\n  /**\n   * Remove the value for a given key from the store. If there is no value for\n   * the given key this method does nothing.\n   * @param key The key to remove the value from the store.\n   */\n  removeItem(key: string): void;\n  /**\n   * Remove all items from the store.\n   */\n  clear(): void;\n  /**\n   * The number of items that are currently stored.\n   */\n  length: number;\n};\n\nfunction isQuotaError(storage: Storage, e: unknown) {\n  return (\n    e instanceof DOMException &&\n    // everything except Firefox\n    (e.code === 22 ||\n      // Firefox\n      e.code === 1014 ||\n      // test name field too, because code might not be present\n      // everything except Firefox\n      e.name === 'QuotaExceededError' ||\n      // Firefox\n      e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&\n    // acknowledge QuotaExceededError only if there's something already stored\n    storage.length !== 0\n  );\n}\n\nexport class StorageAPI {\n  storage: Storage | null;\n\n  constructor(storage?: Storage | null) {\n    if (storage) {\n      this.storage = storage;\n    } else if (storage === null) {\n      // Passing `null` creates a noop storage\n      this.storage = null;\n    } else if (typeof window === 'undefined') {\n      this.storage = null;\n    } else {\n      this.storage = {\n        getItem: localStorage.getItem.bind(localStorage),\n        setItem: localStorage.setItem.bind(localStorage),\n        removeItem: localStorage.removeItem.bind(localStorage),\n\n        get length() {\n          let keys = 0;\n          for (const key in localStorage) {\n            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {\n              keys += 1;\n            }\n          }\n          return keys;\n        },\n\n        clear() {\n          // We only want to clear the namespaced items\n          for (const key in localStorage) {\n            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {\n              localStorage.removeItem(key);\n            }\n          }\n        },\n      };\n    }\n  }\n\n  get(name: string): string | null {\n    if (!this.storage) {\n      return null;\n    }\n\n    const key = `${STORAGE_NAMESPACE}:${name}`;\n    const value = this.storage.getItem(key);\n    // Clean up any inadvertently saved null/undefined values.\n    if (value === 'null' || value === 'undefined') {\n      this.storage.removeItem(key);\n      return null;\n    }\n\n    return value || null;\n  }\n\n  set(\n    name: string,\n    value: string,\n  ): { isQuotaError: boolean; error: Error | null } {\n    let quotaError = false;\n    let error: Error | null = null;\n\n    if (this.storage) {\n      const key = `${STORAGE_NAMESPACE}:${name}`;\n      if (value) {\n        try {\n          this.storage.setItem(key, value);\n        } catch (e) {\n          error = e instanceof Error ? e : new Error(`${e}`);\n          quotaError = isQuotaError(this.storage, e);\n        }\n      } else {\n        // Clean up by removing the item if there's no value to set\n        this.storage.removeItem(key);\n      }\n    }\n\n    return { isQuotaError: quotaError, error };\n  }\n\n  clear() {\n    if (this.storage) {\n      this.storage.clear();\n    }\n  }\n}\n\nconst STORAGE_NAMESPACE = 'graphiql';\n", "import { parse } from 'graphql';\n\nimport { StorageAPI } from './base';\nimport { QueryStore, QueryStoreItem } from './query';\n\nconst MAX_QUERY_SIZE = 100000;\n\nexport class HistoryStore {\n  queries: Array<QueryStoreItem>;\n  history: QueryStore;\n  favorite: QueryStore;\n\n  constructor(\n    private storage: StorageAPI,\n    private maxHistoryLength: number,\n  ) {\n    this.history = new QueryStore(\n      'queries',\n      this.storage,\n      this.maxHistoryLength,\n    );\n    // favorites are not automatically deleted, so there's no need for a max length\n    this.favorite = new QueryStore('favorites', this.storage, null);\n\n    this.queries = [...this.history.fetchAll(), ...this.favorite.fetchAll()];\n  }\n\n  private shouldSaveQuery(\n    query?: string,\n    variables?: string,\n    headers?: string,\n    lastQuerySaved?: QueryStoreItem,\n  ) {\n    if (!query) {\n      return false;\n    }\n\n    try {\n      parse(query);\n    } catch {\n      return false;\n    }\n\n    // Don't try to save giant queries\n    if (query.length > MAX_QUERY_SIZE) {\n      return false;\n    }\n    if (!lastQuerySaved) {\n      return true;\n    }\n    if (JSON.stringify(query) === JSON.stringify(lastQuerySaved.query)) {\n      if (\n        JSON.stringify(variables) === JSON.stringify(lastQuerySaved.variables)\n      ) {\n        if (\n          JSON.stringify(headers) === JSON.stringify(lastQuerySaved.headers)\n        ) {\n          return false;\n        }\n        if (headers && !lastQuerySaved.headers) {\n          return false;\n        }\n      }\n      if (variables && !lastQuerySaved.variables) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  updateHistory = ({\n    query,\n    variables,\n    headers,\n    operationName,\n  }: QueryStoreItem) => {\n    if (\n      !this.shouldSaveQuery(\n        query,\n        variables,\n        headers,\n        this.history.fetchRecent(),\n      )\n    ) {\n      return;\n    }\n    this.history.push({\n      query,\n      variables,\n      headers,\n      operationName,\n    });\n    const historyQueries = this.history.items;\n    const favoriteQueries = this.favorite.items;\n    this.queries = historyQueries.concat(favoriteQueries);\n  };\n\n  toggleFavorite({\n    query,\n    variables,\n    headers,\n    operationName,\n    label,\n    favorite,\n  }: QueryStoreItem) {\n    const item: QueryStoreItem = {\n      query,\n      variables,\n      headers,\n      operationName,\n      label,\n    };\n    if (favorite) {\n      item.favorite = false;\n      this.favorite.delete(item);\n      this.history.push(item);\n    } else {\n      item.favorite = true;\n      this.favorite.push(item);\n      this.history.delete(item);\n    }\n    this.queries = [...this.history.items, ...this.favorite.items];\n  }\n\n  editLabel(\n    {\n      query,\n      variables,\n      headers,\n      operationName,\n      label,\n      favorite,\n    }: QueryStoreItem,\n    index?: number,\n  ) {\n    const item = {\n      query,\n      variables,\n      headers,\n      operationName,\n      label,\n    };\n    if (favorite) {\n      this.favorite.edit({ ...item, favorite }, index);\n    } else {\n      this.history.edit(item, index);\n    }\n    this.queries = [...this.history.items, ...this.favorite.items];\n  }\n\n  deleteHistory = (\n    { query, variables, headers, operationName, favorite }: QueryStoreItem,\n    clearFavorites = false,\n  ) => {\n    function deleteFromStore(store: QueryStore) {\n      const found = store.items.find(\n        x =>\n          x.query === query &&\n          x.variables === variables &&\n          x.headers === headers &&\n          x.operationName === operationName,\n      );\n      if (found) {\n        store.delete(found);\n      }\n    }\n\n    if (favorite || clearFavorites) {\n      deleteFromStore(this.favorite);\n    }\n    if (!favorite || clearFavorites) {\n      deleteFromStore(this.history);\n    }\n\n    this.queries = [...this.history.items, ...this.favorite.items];\n  };\n}\n", "import { StorageAPI } from './base';\n\nexport type QueryStoreItem = {\n  query?: string;\n  variables?: string;\n  headers?: string;\n  operationName?: string;\n  label?: string;\n  favorite?: boolean;\n};\n\nexport class QueryStore {\n  items: Array<QueryStoreItem>;\n\n  constructor(\n    private key: string,\n    private storage: StorageAPI,\n    private maxSize: number | null = null,\n  ) {\n    this.items = this.fetchAll();\n  }\n\n  get length() {\n    return this.items.length;\n  }\n\n  contains(item: QueryStoreItem) {\n    return this.items.some(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n  }\n\n  edit(item: QueryStoreItem, index?: number) {\n    if (typeof index === 'number' && this.items[index]) {\n      const found = this.items[index];\n      if (\n        found.query === item.query &&\n        found.variables === item.variables &&\n        found.headers === item.headers &&\n        found.operationName === item.operationName\n      ) {\n        this.items.splice(index, 1, item);\n        this.save();\n        return;\n      }\n    }\n\n    const itemIndex = this.items.findIndex(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n    if (itemIndex !== -1) {\n      this.items.splice(itemIndex, 1, item);\n      this.save();\n    }\n  }\n\n  delete(item: QueryStoreItem) {\n    const itemIndex = this.items.findIndex(\n      x =>\n        x.query === item.query &&\n        x.variables === item.variables &&\n        x.headers === item.headers &&\n        x.operationName === item.operationName,\n    );\n    if (itemIndex !== -1) {\n      this.items.splice(itemIndex, 1);\n      this.save();\n    }\n  }\n\n  fetchRecent() {\n    return this.items.at(-1);\n  }\n\n  fetchAll() {\n    const raw = this.storage.get(this.key);\n    if (raw) {\n      return JSON.parse(raw)[this.key] as Array<QueryStoreItem>;\n    }\n    return [];\n  }\n\n  push(item: QueryStoreItem) {\n    const items = [...this.items, item];\n\n    if (this.maxSize && items.length > this.maxSize) {\n      items.shift();\n    }\n\n    for (let attempts = 0; attempts < 5; attempts++) {\n      const response = this.storage.set(\n        this.key,\n        JSON.stringify({ [this.key]: items }),\n      );\n      if (!response?.error) {\n        this.items = items;\n      } else if (response.isQuotaError && this.maxSize) {\n        // Only try to delete last items on LRU stores\n        items.shift();\n      } else {\n        return; // We don't know what happened in this case, so just bailing out\n      }\n    }\n  }\n\n  save() {\n    this.storage.set(this.key, JSON.stringify({ [this.key]: this.items }));\n  }\n}\n", "/**\n * This function enables a custom namespace for localStorage\n */\n\nimport { Storage } from './base';\n\nexport type CreateLocalStorageOptions = {\n  /**\n   * specify a different storage namespace prefix from the default of 'graphiql'\n   */\n  namespace?: string;\n};\n/**\n * generate a custom local storage adapter for GraphiQL `storage` prop.\n */\nexport function createLocalStorage({\n  namespace,\n}: CreateLocalStorageOptions): Storage {\n  const storageKeyPrefix = `${namespace}:`;\n  const getStorageKey = (key: string) => `${storageKeyPrefix}${key}`;\n\n  const storage: Storage = {\n    setItem: (key, value) => localStorage.setItem(getStorageKey(key), value),\n    getItem: key => localStorage.getItem(getStorageKey(key)),\n    removeItem: key => localStorage.removeItem(getStorageKey(key)),\n    get length() {\n      let keys = 0;\n      for (const key in localStorage) {\n        if (key.indexOf(storageKeyPrefix) === 0) {\n          keys += 1;\n        }\n      }\n      return keys;\n    },\n\n    clear() {\n      // We only want to clear the namespaced items\n      for (const key in localStorage) {\n        if (key.indexOf(storageKeyPrefix) === 0) {\n          localStorage.removeItem(key);\n        }\n      }\n    },\n  };\n\n  return storage;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB,QAAQ,mBAAmB,QAAQ,mBAAmB,QAAQ,kBAAkB,QAAQ,WAAW,QAAQ,iBAAiB;AAEvJ,aAAS,eAAe,KAAK;AACzB,UAAI,QAAQ,MAAM;AACd,eAAO;AAAA,MACX;AACA,UAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO,OAAO;AAAA,IAClB;AACA,YAAQ,iBAAiB;AAEzB,aAAS,SAAS,KAAK;AACnB,aAAO,eAAe,GAAG,MAAM;AAAA,IACnC;AACA,YAAQ,WAAW;AAEnB,aAASA,iBAAgB,KAAK;AAC1B,aAAO,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM;AAAA,IACxD;AACA,YAAQ,kBAAkBA;AAE1B,aAAS,iBAAiB,KAAK;AAC3B,aAAQ,SAAS,GAAG,KAChB,OAAO,OAAO,GAAG,EAAE,OAAO,aAAa,MAAM,cAC7C,OAAO,IAAI,WAAW;AAAA,IAK9B;AACA,YAAQ,mBAAmB;AAE3B,aAAS,iBAAiB,KAAK;AAC3B,aAAQ,MAAM,QAAQ,GAAG;AAAA,MAErB,IAAI,SAAS;AAAA,MAEb,IAAI,MAAM,CAAC,OAAO,aAAa,EAAE;AAAA,IACzC;AACA,YAAQ,mBAAmB;AAO3B,aAAS,iBAAiB,QAAQ,aAAa;AAC3C,aAAO,OAAO,SAAS,MAAM,SAAS;AAAA,IAC1C;AACA,YAAQ,mBAAmB;AAAA;AAAA;;;ACrD3B;AAAA;AAAA;AAMA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB,QAAQ,eAAe,QAAQ,YAAY,QAAQ,kBAAkB,QAAQ,cAAc,QAAQ,YAAY,QAAQ,iCAAiC,QAAQ,gCAAgC;AAC3N,QAAM,UAAU;AAMhB,YAAQ,gCAAgC;AAMxC,YAAQ,iCAAiC;AAMzC,QAAI;AACJ,KAAC,SAAUC,YAAW;AAClB,MAAAA,WAAUA,WAAU,qBAAqB,IAAI,IAAI,IAAI;AACrD,MAAAA,WAAUA,WAAU,qBAAqB,IAAI,IAAI,IAAI;AACrD,MAAAA,WAAUA,WAAU,YAAY,IAAI,IAAI,IAAI;AAC5C,MAAAA,WAAUA,WAAU,aAAa,IAAI,IAAI,IAAI;AAE7C,MAAAA,WAAUA,WAAU,cAAc,IAAI,IAAI,IAAI;AAC9C,MAAAA,WAAUA,WAAU,WAAW,IAAI,IAAI,IAAI;AAC3C,MAAAA,WAAUA,WAAU,0BAA0B,IAAI,IAAI,IAAI;AAC1D,MAAAA,WAAUA,WAAU,iCAAiC,IAAI,IAAI,IAAI;AACjE,MAAAA,WAAUA,WAAU,kCAAkC,IAAI,IAAI,IAAI;AAElE,MAAAA,WAAUA,WAAU,yBAAyB,IAAI,IAAI,IAAI;AACzD,MAAAA,WAAUA,WAAU,+BAA+B,IAAI,IAAI,IAAI;AAAA,IACnE,GAAG,YAAY,QAAQ,cAAc,QAAQ,YAAY,CAAC,EAAE;AAM5D,QAAI;AACJ,KAAC,SAAUC,cAAa;AACpB,MAAAA,aAAY,gBAAgB,IAAI;AAChC,MAAAA,aAAY,eAAe,IAAI;AAC/B,MAAAA,aAAY,MAAM,IAAI;AACtB,MAAAA,aAAY,MAAM,IAAI;AACtB,MAAAA,aAAY,WAAW,IAAI;AAC3B,MAAAA,aAAY,MAAM,IAAI;AACtB,MAAAA,aAAY,OAAO,IAAI;AACvB,MAAAA,aAAY,UAAU,IAAI;AAAA,IAC9B,GAAG,cAAc,QAAQ,gBAAgB,QAAQ,cAAc,CAAC,EAAE;AAQlE,aAAS,gBAAgB,KAAK;AAC1B,UAAI,EAAE,GAAG,QAAQ,UAAU,GAAG,GAAG;AAC7B,cAAM,IAAI,MAAM,iDAAiD,GAAG,QAAQ,gBAAgB,GAAG,CAAC,EAAE;AAAA,MACtG;AACA,UAAI,CAAC,IAAI,MAAM;AACX,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC5D;AACA,UAAI,OAAO,IAAI,SAAS,UAAU;AAC9B,cAAM,IAAI,MAAM,mEAAmE,GAAG,QAAQ,gBAAgB,IAAI,IAAI,CAAC,EAAE;AAAA,MAC7H;AACA,cAAQ,IAAI,MAAM;AAAA,QACd,KAAK,YAAY;AAAA,QACjB,KAAK,YAAY;AAAA,QACjB,KAAK,YAAY;AAAA,QACjB,KAAK,YAAY,MAAM;AACnB,cAAI,IAAI,WAAW,QAAQ,EAAE,GAAG,QAAQ,UAAU,IAAI,OAAO,GAAG;AAC5D,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,4FAA4F,IAAI,OAAO,GAAG;AAAA,UAC1I;AACA;AAAA,QACJ;AAAA,QACA,KAAK,YAAY,WAAW;AACxB,cAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,gEAAgE,GAAG,QAAQ,gBAAgB,IAAI,EAAE,CAAC,EAAE;AAAA,UACpI;AACA,cAAI,CAAC,IAAI,IAAI;AACT,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,UAC9E;AACA,cAAI,EAAE,GAAG,QAAQ,UAAU,IAAI,OAAO,GAAG;AACrC,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,sEAAsE,GAAG,QAAQ,gBAAgB,IAAI,OAAO,CAAC,EAAE;AAAA,UAC/I;AACA,cAAI,OAAO,IAAI,QAAQ,UAAU,UAAU;AACvC,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,2EAA2E,GAAG,QAAQ,gBAAgB,IAAI,QAAQ,KAAK,CAAC,EAAE;AAAA,UAC1J;AACA,cAAI,IAAI,QAAQ,aAAa,QAAQ,EAAE,GAAG,QAAQ,UAAU,IAAI,QAAQ,SAAS,GAAG;AAChF,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,wGAAwG,GAAG,QAAQ,gBAAgB,IAAI,QAAQ,SAAS,CAAC,EAAE;AAAA,UAC3L;AACA,cAAI,IAAI,QAAQ,iBAAiB,SAC5B,GAAG,QAAQ,gBAAgB,IAAI,QAAQ,aAAa,MAAM,UAAU;AACrE,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,yGAAyG,GAAG,QAAQ,gBAAgB,IAAI,QAAQ,aAAa,CAAC,EAAE;AAAA,UAChM;AACA,cAAI,IAAI,QAAQ,cAAc,QAAQ,EAAE,GAAG,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG;AAClF,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,yGAAyG,GAAG,QAAQ,gBAAgB,IAAI,QAAQ,UAAU,CAAC,EAAE;AAAA,UAC7L;AACA;AAAA,QACJ;AAAA,QACA,KAAK,YAAY,MAAM;AACnB,cAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,gEAAgE,GAAG,QAAQ,gBAAgB,IAAI,EAAE,CAAC,EAAE;AAAA,UACpI;AACA,cAAI,CAAC,IAAI,IAAI;AACT,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,UAC9E;AACA,cAAI,EAAE,GAAG,QAAQ,UAAU,IAAI,OAAO,GAAG;AACrC,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,sEAAsE,GAAG,QAAQ,gBAAgB,IAAI,OAAO,CAAC,EAAE;AAAA,UAC/I;AACA;AAAA,QACJ;AAAA,QACA,KAAK,YAAY,OAAO;AACpB,cAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,gEAAgE,GAAG,QAAQ,gBAAgB,IAAI,EAAE,CAAC,EAAE;AAAA,UACpI;AACA,cAAI,CAAC,IAAI,IAAI;AACT,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,UAC9E;AACA,cAAI,EAAE,GAAG,QAAQ,kBAAkB,IAAI,OAAO,GAAG;AAC7C,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,sFAAsF,KAAK,UAAU,IAAI,OAAO,CAAC,EAAE;AAAA,UACnJ;AACA;AAAA,QACJ;AAAA,QACA,KAAK,YAAY,UAAU;AACvB,cAAI,OAAO,IAAI,OAAO,UAAU;AAC5B,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,gEAAgE,GAAG,QAAQ,gBAAgB,IAAI,EAAE,CAAC,EAAE;AAAA,UACpI;AACA,cAAI,CAAC,IAAI,IAAI;AACT,kBAAM,IAAI,MAAM,IAAI,IAAI,IAAI,8CAA8C;AAAA,UAC9E;AACA;AAAA,QACJ;AAAA,QACA;AACI,gBAAM,IAAI,MAAM,oCAAoC,IAAI,IAAI,GAAG;AAAA,MACvE;AACA,aAAO;AAAA,IACX;AACA,YAAQ,kBAAkB;AAQ1B,aAAS,UAAU,KAAK;AACpB,UAAI;AACA,wBAAgB,GAAG;AACnB,eAAO;AAAA,MACX,SACO,IAAI;AACP,eAAO;AAAA,MACX;AAAA,IACJ;AACA,YAAQ,YAAY;AAMpB,aAAS,aAAa,MAAM,SAAS;AACjC,aAAO,gBAAgB,OAAO,SAAS,WAAW,KAAK,MAAM,MAAM,OAAO,IAAI,IAAI;AAAA,IACtF;AACA,YAAQ,eAAe;AAMvB,aAAS,iBAAiB,KAAK,UAAU;AACrC,sBAAgB,GAAG;AACnB,aAAO,KAAK,UAAU,KAAK,QAAQ;AAAA,IACvC;AACA,YAAQ,mBAAmB;AAAA;AAAA;;;ACxL3B;AAAA;AAAA;AAMA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,eAAe;AACvB,QAAM,WAAW;AACjB,QAAM,UAAU;AAEhB,iBAAa,kBAAqB,OAAO;AAMzC,aAAS,aAAa,SAAS;AAC3B,YAAM;AAAA,QAAE;AAAA,QAAK;AAAA,QAAkB,OAAO;AAAA,QAAM,iBAAiB,QAAQ;AAAA,QAAO,kBAAkB,qBAAqB;AAAA,QAAG,YAAY;AAAA,QAAG;AAAA,QAAa,2BAA2B;AAAA,QAAG,gBAAgB;AAAA,QAAG,YAAY,eAAe,6BAA6BC,UAAS;AAChQ,cAAI,aAAa;AACjB,mBAAS,IAAI,GAAG,IAAIA,UAAS,KAAK;AAC9B,0BAAc;AAAA,UAClB;AACA,gBAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS;AAAA,UAE/C,KAAK,MAAM,KAAK,OAAO,KAAK,MAAO,OAAO,GAAG,CAAC,CAAC;AAAA,QACvD;AAAA,QAAG,cAAc;AAAA,QAAkB;AAAA,QAA0B;AAAA,QAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQjE,aAAa,SAAS,eAAe;AACjC,iBAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AAClE,kBAAM,IAAK,KAAK,OAAO,IAAI,KAAM,GAAG,IAAI,KAAK,MAAM,IAAK,IAAI,IAAO;AACnE,mBAAO,EAAE,SAAS,EAAE;AAAA,UACxB,CAAC;AAAA,QACL;AAAA,QAAG,qBAAqB;AAAA,QAAU,oBAAoB;AAAA,MAAS,IAAI;AACnE,UAAI;AACJ,UAAI,eAAe;AACf,YAAI,CAAC,YAAY,aAAa,GAAG;AAC7B,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AACA,aAAK;AAAA,MACT,WACS,OAAO,cAAc,aAAa;AACvC,aAAK;AAAA,MACT,WACS,OAAO,WAAW,aAAa;AACpC,aACI,OAAO;AAAA,QAEH,OAAO;AAAA,MACnB,WACS,OAAO,WAAW,aAAa;AACpC,aACI,OAAO;AAAA,QAEH,OAAO;AAAA,MACnB;AACA,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,uIAAuI;AAC3J,YAAM,gBAAgB;AAEtB,YAAM,WAAW,MAAM;AACnB,cAAM,UAAW,uBAAM;AACnB,gBAAMC,aAAY,CAAC;AACnB,iBAAO;AAAA,YACH,GAAG,IAAI,UAAU;AACb,cAAAA,WAAU,EAAE,IAAI;AAChB,qBAAO,MAAM;AACT,uBAAOA,WAAU,EAAE;AAAA,cACvB;AAAA,YACJ;AAAA,YACA,KAAKC,UAAS;AACV,kBAAI;AACJ,kBAAI,QAAQA;AACR,iBAAC,KAAKD,WAAUC,SAAQ,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAKD,YAAWC,QAAO;AAAA,YACpG;AAAA,UACJ;AAAA,QACJ,GAAG;AACH,cAAM,YAAY;AAAA,UACd,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,CAAC,GAAG,UAAU,IAAI,CAAC;AAAA,UACzF,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,UAC7E,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,CAAC,GAAG,SAAS,IAAI,CAAC;AAAA,UACtF,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,UACvE,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC;AAAA,UACvE,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,CAAC,QAAQ,MAAM,GAAG,OAAO,IAAI,CAAC,QAAQ,IAAI;AAAA,UAC1G,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,UAC7E,QAAQ,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC;AAAA,QAC9E;AACA,eAAO;AAAA,UACH,WAAW,QAAQ;AAAA,UACnB,GAAG,OAAO,UAAU;AAChB,kBAAM,IAAI,UAAU,KAAK;AACzB,cAAE,KAAK,QAAQ;AACf,mBAAO,MAAM;AACT,gBAAE,OAAO,EAAE,QAAQ,QAAQ,GAAG,CAAC;AAAA,YACnC;AAAA,UACJ;AAAA,UACA,KAAK,UAAU,MAAM;AAEjB,uBAAW,YAAY,CAAC,GAAG,UAAU,KAAK,CAAC,GAAG;AAE1C,uBAAS,GAAG,IAAI;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,GAAG;AAGH,eAAS,cAAc,IAAI;AACvB,cAAM,YAAY;AAAA;AAAA,UAEd,QAAQ,GAAG,SAAS,CAAC,QAAQ;AACzB,sBAAU,QAAQ,CAAC,aAAa,SAAS,CAAC;AAC1C,eAAG,GAAG;AAAA,UACV,CAAC;AAAA;AAAA,UAED,QAAQ,GAAG,UAAU,CAAC,UAAU;AAC5B,sBAAU,QAAQ,CAAC,aAAa,SAAS,CAAC;AAC1C,eAAG,KAAK;AAAA,UACZ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,YAAY,QAAQ,GAAG,kBAAkB,WAAW,OAAO,UAAU,GAAG,WAAW;AACvF,qBAAe,UAAU;AAGrB,qBAAa,gBAAgB;AAC7B,cAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,eAAe,QAAQ,eAAe,SAAS,aAAc,aAAa,IAAI,QAAQ,CAAC,WAAW,YAAY,YAAY;AAC5J,cAAI,UAAU;AACV,kBAAM,UAAU,OAAO;AAEvB,gBAAI,CAAC,OAAO;AACR,2BAAa;AACb,qBAAO,OAAO,EAAE,MAAM,KAAM,QAAQ,yBAAyB,CAAC;AAAA,YAClE;AACA;AAAA,UACJ;AACA,kBAAQ,KAAK,YAAY;AACzB,gBAAMC,UAAS,IAAI,cAAc,OAAO,QAAQ,aAAa,MAAM,IAAI,IAAI,KAAK,SAAS,6BAA6B;AACtH,cAAI,sBAAsB;AAC1B,mBAAS,cAAc;AACnB,gBAAI,SAAS,SAAS,KAAK,YAAY,GAAG;AACtC,2BAAa,UAAU;AACvB,2BAAa,WAAW,MAAM;AAC1B,oBAAIA,QAAO,eAAe,cAAc,MAAM;AAC1C,kBAAAA,QAAO,MAAM,GAAG,SAAS,kBAAkB,EAAE,MAAM,SAAS,YAAY,KAAK,CAAC,CAAC;AAC/E,0BAAQ,KAAK,QAAQ,OAAO,MAAS;AAAA,gBACzC;AAAA,cACJ,GAAG,SAAS;AAAA,YAChB;AAAA,UACJ;AACA,wBAAc,CAAC,eAAe;AAC1B,yBAAa;AACb,yBAAa,oBAAoB;AACjC,yBAAa,UAAU;AACvB,mBAAO,UAAU;AACjB,gBAAI,iBAAiB,UAAU,KAAK,WAAW,SAAS,MAAM;AAC1D,cAAAA,QAAO,MAAM,MAAM,YAAY;AAC/B,cAAAA,QAAO,UAAU;AACjB,cAAAA,QAAO,UAAU;AAAA,YACrB;AAAA,UACJ,CAAC;AACD,UAAAA,QAAO,UAAU,CAAC,QAAQ,QAAQ,KAAK,SAAS,GAAG;AACnD,UAAAA,QAAO,UAAU,CAAC,UAAU,QAAQ,KAAK,UAAU,KAAK;AACxD,UAAAA,QAAO,SAAS,YAAY;AACxB,gBAAI;AACA,sBAAQ,KAAK,UAAUA,OAAM;AAC7B,oBAAM,UAAU,OAAO,qBAAqB,aACtC,MAAM,iBAAiB,IACvB;AAGN,kBAAIA,QAAO,eAAe,cAAc;AACpC;AACJ,cAAAA,QAAO,MAAM,GAAG,SAAS,kBAAkB,UACrC;AAAA,gBACE,MAAM,SAAS,YAAY;AAAA,gBAC3B;AAAA,cACJ,IACE;AAAA,gBACE,MAAM,SAAS,YAAY;AAAA;AAAA,cAE/B,GAAG,QAAQ,CAAC;AAChB,kBAAI,SAAS,wBAAwB,KACjC,2BAA2B,GAAG;AAC9B,uCAAuB,WAAW,MAAM;AACpC,kBAAAA,QAAO,MAAM,SAAS,UAAU,kCAAkC,oCAAoC;AAAA,gBAC1G,GAAG,wBAAwB;AAAA,cAC/B;AACA,0BAAY;AAAA,YAChB,SACO,KAAK;AACR,sBAAQ,KAAK,SAAS,GAAG;AACzB,cAAAA,QAAO,MAAM,SAAS,UAAU,sBAAsB,GAAG,QAAQ,kBAAkB,eAAe,QAAQ,IAAI,UAAU,IAAI,MAAM,GAAG,EAAE,SAAS,uBAAuB,CAAC;AAAA,YAC5K;AAAA,UACJ;AACA,cAAI,eAAe;AACnB,UAAAA,QAAO,YAAY,CAAC,EAAE,KAAK,MAAM;AAC7B,gBAAI;AACA,oBAAM,WAAW,GAAG,SAAS,cAAc,MAAM,OAAO;AACxD,sBAAQ,KAAK,WAAW,OAAO;AAC/B,kBAAI,QAAQ,SAAS,UAAU,QAAQ,SAAS,QAAQ;AACpD,wBAAQ,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO;AAChD,oBAAI,QAAQ,SAAS,QAAQ;AACzB,8BAAY;AAAA,gBAChB,WACS,CAAC,aAAa;AAEnB,kBAAAA,QAAO,MAAM,GAAG,SAAS,kBAAkB,QAAQ,UAC7C;AAAA,oBACE,MAAM,SAAS,YAAY;AAAA,oBAC3B,SAAS,QAAQ;AAAA,kBACrB,IACE;AAAA,oBACE,MAAM,SAAS,YAAY;AAAA;AAAA,kBAE/B,CAAC,CAAC;AACN,0BAAQ,KAAK,QAAQ,OAAO,QAAQ,OAAO;AAAA,gBAC/C;AACA;AAAA,cACJ;AACA,kBAAI;AACA;AACJ,kBAAI,QAAQ,SAAS,SAAS,YAAY;AACtC,sBAAM,IAAI,MAAM,mCAAmC,QAAQ,IAAI,EAAE;AACrE,2BAAa,oBAAoB;AACjC,6BAAe;AACf,sBAAQ,KAAK,aAAaA,SAAQ,QAAQ,OAAO;AACjD,yBAAW;AACX,wBAAU;AACV,wBAAU;AAAA,gBACNA;AAAA,gBACA,IAAI,QAAQ,CAAC,GAAG,WAAW,cAAc,MAAM,CAAC;AAAA,cACpD,CAAC;AAAA,YACL,SACO,KAAK;AACR,cAAAA,QAAO,YAAY;AACnB,sBAAQ,KAAK,SAAS,GAAG;AACzB,cAAAA,QAAO,MAAM,SAAS,UAAU,cAAc,GAAG,QAAQ,kBAAkB,eAAe,QAAQ,IAAI,UAAU,IAAI,MAAM,GAAG,EAAE,SAAS,cAAc,CAAC;AAAA,YAC3J;AAAA,UACJ;AAAA,QACJ,GAAG,CAAC;AAEJ,YAAI,OAAO,eAAe,cAAc;AACpC,gBAAM;AACV,YAAI,UAAU,MAAM;AAAA,QAEpB;AACA,cAAM,WAAW,IAAI,QAAQ,CAAC,YAAa,UAAU,OAAQ;AAC7D,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA,QAAQ,KAAK;AAAA;AAAA,YAET,SAAS,KAAK,MAAM;AAChB,kBAAI,CAAC,OAAO;AAER,sBAAM,WAAW,MAAM,OAAO,MAAM,KAAM,gBAAgB;AAC1D,oBAAI,SAAS,kBAAkB,KAAK,qBAAqB,GAAG;AAGxD,qCAAmB,WAAW,MAAM;AAChC,wBAAI,OAAO,eAAe,cAAc;AACpC,+BAAS;AAAA,kBACjB,GAAG,kBAAkB;AAAA,gBACzB,OACK;AAED,2BAAS;AAAA,gBACb;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA;AAAA,YAED;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAIA,eAAS,0BAA0B,iBAAiB;AAEhD,YAAI,iBAAiB,eAAe,MAC/B,yBAAyB,gBAAgB,IAAI,KAC1C;AAAA,UACI,SAAS,UAAU;AAAA,UACnB,SAAS,UAAU;AAAA,UACnB,SAAS,UAAU;AAAA,UACnB,SAAS,UAAU;AAAA,UACnB,SAAS,UAAU;AAAA;AAAA,UAEnB,SAAS,UAAU;AAAA;AAAA;AAAA,UAGnB,SAAS,UAAU;AAAA,UACnB,SAAS,UAAU;AAAA;AAAA,QAEvB,EAAE,SAAS,gBAAgB,IAAI;AACnC,gBAAM;AAEV,YAAI;AACA,iBAAO;AAGX,YAAI,iBAAiB,eAAe,KAAK,gBAAgB,SAAS;AAC9D,iBAAO,QAAQ;AAEnB,YAAI,CAAC,iBAAiB,WAAW;AAC7B,gBAAM;AAEV,YAAI,CAAC,YAAY,eAAe;AAC5B,gBAAM;AAEV,YAAI,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,eAAe;AAC5H,gBAAM;AAEV,eAAQ,WAAW;AAAA,MACvB;AAEA,UAAI,CAAC,MAAM;AACP,SAAC,YAAY;AACT;AACA,qBAAS;AACL,gBAAI;AACA,oBAAM,CAAC,EAAE,EAAE,YAAY,IAAI,MAAM,QAAQ;AACzC,oBAAM;AAAA,YACV,SACO,iBAAiB;AACpB,kBAAI;AACA,oBAAI,CAAC,0BAA0B,eAAe;AAC1C;AAAA,cACR,SACOC,kBAAiB;AAEpB,uBAAO,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAeA,gBAAe;AAAA,cACzG;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,GAAG;AAAA,MACP;AACA,aAAO;AAAA,QACH,IAAI,QAAQ;AAAA,QACZ,UAAU,SAAS,MAAM;AACrB,gBAAM,KAAK,WAAW,OAAO;AAC7B,cAAI,OAAO,OAAO,UAAU,OAAO,WAAW,MAAM;AAEhD;AACA,mBAAO;AAAA,UACX;AACA,WAAC,YAAY;AACT;AACA,uBAAS;AACL,kBAAI;AACA,sBAAM,CAAC,QAAQ,SAAS,4BAA4B,IAAI,MAAM,QAAQ;AAEtE,oBAAI;AACA,yBAAO,QAAQ;AACnB,sBAAM,WAAW,QAAQ,UAAU,IAAI,CAAC,YAAY;AAChD,0BAAQ,QAAQ,MAAM;AAAA,oBAClB,KAAK,SAAS,YAAY,MAAM;AAE5B,2BAAK,KAAK,QAAQ,OAAO;AACzB;AAAA,oBACJ;AAAA,oBACA,KAAK,SAAS,YAAY,OAAO;AAC7B,sBAAC,UAAU,MAAQ,OAAO;AAC1B,2BAAK,MAAM,QAAQ,OAAO;AAC1B,+BAAS;AACT;AAAA,oBACJ;AAAA,oBACA,KAAK,SAAS,YAAY,UAAU;AAChC,6BAAO;AACP,+BAAS;AACT;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ,CAAC;AACD,uBAAO,MAAM,GAAG,SAAS,kBAAkB;AAAA,kBACvC;AAAA,kBACA,MAAM,SAAS,YAAY;AAAA,kBAC3B;AAAA,gBACJ,GAAG,QAAQ,CAAC;AACZ,2BAAW,MAAM;AACb,sBAAI,CAAC,QAAQ,OAAO,eAAe,cAAc;AAE7C,2BAAO,MAAM,GAAG,SAAS,kBAAkB;AAAA,sBACvC;AAAA,sBACA,MAAM,SAAS,YAAY;AAAA,oBAC/B,GAAG,QAAQ,CAAC;AAChB;AACA,yBAAO;AACP,0BAAQ;AAAA,gBACZ;AAIA,sBAAM,6BAA6B,QAAQ,QAAQ;AACnD;AAAA,cACJ,SACO,iBAAiB;AACpB,oBAAI,CAAC,0BAA0B,eAAe;AAC1C;AAAA,cACR;AAAA,YACJ;AAAA,UACJ,GAAG,EACE,KAAK,MAAM;AAEZ,gBAAI,CAAC;AACD,mBAAK,SAAS;AAAA,UACtB,CAAC,EACI,MAAM,CAAC,QAAQ;AAChB,iBAAK,MAAM,GAAG;AAAA,UAClB,CAAC;AACD,iBAAO,MAAM;AAET,gBAAI,CAAC;AACD,uBAAS;AAAA,UACjB;AAAA,QACJ;AAAA,QACA,MAAM,UAAU;AACZ,qBAAW;AACX,cAAI,YAAY;AAEZ,kBAAM,CAAC,MAAM,IAAI,MAAM;AACvB,mBAAO,MAAM,KAAM,gBAAgB;AAAA,UACvC;AAAA,QACJ;AAAA,QACA,YAAY;AACR,cAAI,YAAY;AAEZ,oBAAQ,KAAK,UAAU;AAAA,cACnB,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,UAAU;AAAA,YACd,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,eAAe;AACvB,aAAS,iBAAiB,KAAK;AAC3B,cAAQ,GAAG,QAAQ,UAAU,GAAG,KAAK,UAAU,OAAO,YAAY;AAAA,IACtE;AACA,aAAS,yBAAyB,MAAM;AACpC,UAAI;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,MACJ,EAAE,SAAS,IAAI;AACX,eAAO;AAEX,aAAO,QAAQ,OAAQ,QAAQ;AAAA,IACnC;AACA,aAAS,YAAY,KAAK;AACtB,aAAQ,OAAO,QAAQ,cACnB,iBAAiB,OACjB,YAAY,OACZ,aAAa,OACb,gBAAgB,OAChB,UAAU;AAAA,IAClB;AAAA;AAAA;;;ACneA;AAAA;AAAA;AAMA,QAAIC,iBAAiB,WAAQ,QAAK,iBAAkB,SAAU,GAAG;AAC7D,UAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,UAAI,IAAI,EAAE,OAAO,aAAa,GAAG;AACjC,aAAO,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,eAAO;AAAA,MAAM,GAAG;AAC9M,eAAS,KAAK,GAAG;AAAE,UAAE,CAAC,IAAI,EAAE,CAAC,KAAK,SAAU,GAAG;AAAE,iBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,gBAAI,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,UAAG,CAAC;AAAA,QAAG;AAAA,MAAG;AAC/J,eAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,gBAAQ,QAAQ,CAAC,EAAE,KAAK,SAASC,IAAG;AAAE,kBAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,QAAG,GAAG,MAAM;AAAA,MAAG;AAAA,IAC/H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB,QAAQ,aAAa;AAC/C,QAAM,YAAY;AAClB,QAAM,WAAW;AACjB,QAAM,UAAU;AAUhB,aAAS,WAAW,SAAS;AACzB,YAAM;AAAA,QAAE;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAO;AAAA,QAAU;AAAA,QAAS;AAAA,QAAW,4BAA4B;AAAA;AAAA,QAC1F;AAAA,QAAW;AAAA,QAAc;AAAA,QAAS;AAAA,QAAa;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAY,oBAAoB;AAAA,QAAS,qBAAqB;AAAA,MAAU,IAAI;AACzJ,aAAO;AAAA,QACH,OAAO,QAAQ,OAAO;AAClB,gBAAM,MAAM;AAAA,YACR,wBAAwB;AAAA,YACxB,cAAc;AAAA,YACd,eAAe,CAAC;AAAA,YAChB;AAAA,UACJ;AACA,cAAI,OAAO,aAAa,SAAS,+BAA+B;AAC5D,mBAAO,MAAM,SAAS,UAAU,0BAA0B,4BAA4B;AACtF,mBAAO,OAAO,MAAM,WAAW;AAE3B,qBAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAAA,YACtF;AAAA,UACJ;AAGA,gBAAM,qBAAqB,4BAA4B,KAAK,SAAS,yBAAyB,IACxF,WAAW,MAAM;AACf,gBAAI,CAAC,IAAI;AACL,qBAAO,MAAM,SAAS,UAAU,iCAAiC,mCAAmC;AAAA,UAC5G,GAAG,yBAAyB,IAC1B;AACN,iBAAO,UAAU,eAAe,UAAU,MAAM;AAC5C,gBAAI,IAAI,KAAK,IAAI;AACjB,gBAAI;AACJ,gBAAI;AACJ,gBAAI;AACA,yBAAW,GAAG,SAAS,cAAc,MAAM,OAAO;AAAA,YACtD,SACO,KAAK;AACR,qBAAO,OAAO,MAAM,SAAS,UAAU,YAAY,0BAA0B;AAAA,YACjF;AACA,oBAAQ,QAAQ,MAAM;AAAA,cAClB,KAAK,SAAS,YAAY,gBAAgB;AACtC,oBAAI,IAAI;AACJ,yBAAO,OAAO,MAAM,SAAS,UAAU,+BAA+B,kCAAkC;AAE5G,oBAAI,yBAAyB;AAC7B,qBAAK,GAAG,QAAQ,UAAU,QAAQ,OAAO;AAErC,sBAAI,mBAAmB,QAAQ;AACnC,sBAAM,qBAAqB,OAAO,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,GAAG;AACrG,oBAAI,uBAAuB;AACvB,yBAAO,OAAO,MAAM,SAAS,UAAU,WAAW,WAAW;AACjE,sBAAM,OAAO,MAAM,GAAG,SAAS,mBAAmB,GAAG,QAAQ,UAAU,kBAAkB,IACnF;AAAA,kBACE,MAAM,SAAS,YAAY;AAAA,kBAC3B,SAAS;AAAA,gBACb,IACE;AAAA,kBACE,MAAM,SAAS,YAAY;AAAA;AAAA,gBAE/B,GAAG,QAAQ,CAAC;AAEhB,oBAAI,eAAe;AACnB;AAAA,cACJ;AAAA,cACA,KAAK,SAAS,YAAY,MAAM;AAC5B,oBAAI,OAAO;AAEP,yBAAO,MAAM,OAAO,OAAO,QAAQ,OAAO;AAC9C,sBAAM,OAAO,MAAM,GAAG,SAAS,kBAAkB,QAAQ,UACnD,EAAE,MAAM,SAAS,YAAY,MAAM,SAAS,QAAQ,QAAQ,IAC5D;AAAA,kBACE,MAAM,SAAS,YAAY;AAAA;AAAA,gBAE/B,CAAC,CAAC;AACN;AAAA,cACJ;AAAA,cACA,KAAK,SAAS,YAAY;AACtB,uBAAO,QAAQ,KAAK,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,QAAQ,OAAO;AAAA,cAC3G,KAAK,SAAS,YAAY,WAAW;AACjC,oBAAI,CAAC,IAAI;AACL,yBAAO,OAAO,MAAM,SAAS,UAAU,cAAc,cAAc;AACvE,sBAAM,EAAE,IAAI,QAAQ,IAAI;AACxB,oBAAI,MAAM,IAAI;AACV,yBAAO,OAAO,MAAM,SAAS,UAAU,yBAAyB,kBAAkB,EAAE,iBAAiB;AAGzG,oBAAI,cAAc,EAAE,IAAI;AACxB,sBAAM,OAAO;AAAA,kBACT,MAAM,OAAO,QAAQ,SAAS;AAC1B,wBAAI,cAAc;AAAA,sBACd;AAAA,sBACA,MAAM,SAAS,YAAY;AAAA,sBAC3B,SAAS;AAAA,oBACb;AACA,0BAAM,cAAc,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,KAAK,aAAa,MAAM,MAAM;AAChH,wBAAI;AACA,oCAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,SAAS,YAAY,CAAC;AACxF,0BAAM,OAAO,MAAM,GAAG,SAAS,kBAAkB,aAAa,QAAQ,CAAC;AAAA,kBAC3E;AAAA,kBACA,OAAO,OAAO,WAAW;AACrB,wBAAI,eAAe;AAAA,sBACf;AAAA,sBACA,MAAM,SAAS,YAAY;AAAA,sBAC3B,SAAS;AAAA,oBACb;AACA,0BAAM,cAAc,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,cAAc,MAAM;AAC9G,wBAAI;AACA,qCAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,SAAS,YAAY,CAAC;AAC1F,0BAAM,OAAO,MAAM,GAAG,SAAS,kBAAkB,cAAc,QAAQ,CAAC;AAAA,kBAC5E;AAAA,kBACA,UAAU,OAAO,iBAAiB;AAC9B,0BAAM,kBAAkB;AAAA,sBACpB;AAAA,sBACA,MAAM,SAAS,YAAY;AAAA,oBAC/B;AACA,2BAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,KAAK,eAAe;AAC9F,wBAAI;AACA,4BAAM,OAAO,MAAM,GAAG,SAAS,kBAAkB,iBAAiB,QAAQ,CAAC;AAAA,kBACnF;AAAA,gBACJ;AACA,oBAAI;AACA,sBAAI;AACJ,wBAAM,wBAAwB,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,OAAO;AACvH,sBAAI,uBAAuB;AACvB,yBAAK,GAAG,QAAQ,kBAAkB,qBAAqB;AACnD,6BAAO,MAAM,KAAK,MAAM,qBAAqB;AAAA,6BACxC,MAAM,QAAQ,qBAAqB;AACxC,4BAAM,IAAI,MAAM,uFAAuF;AAE3G,+BAAW;AAAA,kBACf,OACK;AAGD,wBAAI,CAAC;AACD,4BAAM,IAAI,MAAM,oCAAoC;AACxD,0BAAM,OAAO;AAAA,sBACT,eAAe,QAAQ;AAAA,sBACvB,WAAW,GAAG,UAAU,OAAO,QAAQ,KAAK;AAAA,sBAC5C,gBAAgB,QAAQ;AAAA,oBAC5B;AACA,+BAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,QAAQ,OAAO,WAAW,aACpE,MAAM,OAAO,KAAK,SAAS,IAAI,IAC/B,OAAO,CAAC;AAClB,0BAAM,oBAAoB,aAAa,QAAQ,aAAa,SAAS,WAAW,UAAU,UAAU,SAAS,QAAQ,SAAS,QAAQ;AACtI,wBAAI,iBAAiB,SAAS;AAC1B,6BAAO,MAAM,KAAK,MAAM,gBAAgB;AAAA,kBAChD;AACA,wBAAM,gBAAgB,GAAG,UAAU,iBAAiB,SAAS,UAAU,SAAS,aAAa;AAC7F,sBAAI,CAAC;AACD,2BAAO,MAAM,KAAK,MAAM;AAAA,sBACpB,IAAI,UAAU,aAAa,8BAA8B;AAAA,oBAC7D,CAAC;AAEL,sBAAI,EAAE,eAAe;AACjB,6BAAS,YAAY,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,SAAS;AAEnG,sBAAI,EAAE,kBAAkB;AACpB,6BAAS,eACL,OAAO,YAAY,aACb,MAAM,QAAQ,KAAK,SAAS,QAAQ,IACpC;AAGd,sBAAI;AACJ,sBAAI,aAAa,cAAc;AAC3B,sCAAkB,OAAO,cAAc,QAAQ,cAAc,SAAS,YAAY,UAAU,WAAW,QAAQ;AAAA;AAG/G,sCAAkB,OAAO,YAAY,QAAQ,YAAY,SAAS,UAAU,UAAU,SAAS,QAAQ;AAC3G,wBAAM,cAAc,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK,SAAS,UAAU,eAAe;AACxI,sBAAI;AACA,sCAAkB;AACtB,uBAAK,GAAG,QAAQ,iBAAiB,eAAe,GAAG;AAE/C,wBAAI,EAAE,MAAM,IAAI,gBAAgB;AAE5B,2BAAK,GAAG,QAAQ,kBAAkB,eAAe;AAC7C,wCAAgB,OAAO,MAAS;AAAA,oBACxC,OACK;AACD,0BAAI,cAAc,EAAE,IAAI;AACxB,0BAAI;AACA,iCAAS,KAAK,MAAM,oBAAoBD,eAAc,eAAe,GAAG,qBAAqB,sBAAsB,MAAM,kBAAkB,KAAK,GAAG,KAAK,oBAAoB,MAAM,CAAC,MAAK;AACpL,+BAAK,oBAAoB;AACzB,+BAAK;AACL,8BAAI;AACA,kCAAM,SAAS;AACf,kCAAM,KAAK,KAAK,QAAQ,QAAQ;AAAA,0BACpC,UACA;AACI,iCAAK;AAAA,0BACT;AAAA,wBACJ;AAAA,sBACJ,SACO,OAAO;AAAE,8BAAM,EAAE,OAAO,MAAM;AAAA,sBAAG,UACxC;AACI,4BAAI;AACA,8BAAI,CAAC,MAAM,CAAC,OAAO,KAAK,kBAAkB,QAAS,OAAM,GAAG,KAAK,iBAAiB;AAAA,wBACtF,UACA;AAAU,8BAAI,IAAK,OAAM,IAAI;AAAA,wBAAO;AAAA,sBACxC;AAAA,oBACJ;AAAA,kBACJ,OACK;AAID,wBAAI,MAAM,IAAI;AACV,4BAAM,KAAK,KAAK,iBAAiB,QAAQ;AAAA,kBACjD;AAGA,wBAAM,KAAK,SAAS,MAAM,IAAI,aAAa;AAAA,gBAC/C,UACA;AAEI,yBAAO,IAAI,cAAc,EAAE;AAAA,gBAC/B;AACA;AAAA,cACJ;AAAA,cACA,KAAK,SAAS,YAAY,UAAU;AAChC,sBAAM,eAAe,IAAI,cAAc,QAAQ,EAAE;AACjD,uBAAO,IAAI,cAAc,QAAQ,EAAE;AACnC,qBAAK,GAAG,QAAQ,kBAAkB,YAAY;AAC1C,wBAAM,aAAa,OAAO,MAAS;AACvC;AAAA,cACJ;AAAA,cACA;AACI,sBAAM,IAAI,MAAM,8BAA8B,QAAQ,IAAI,WAAW;AAAA,YAC7E;AAAA,UACJ,CAAC;AAED,iBAAO,OAAO,MAAM,WAAW;AAC3B,gBAAI;AACA,2BAAa,kBAAkB;AACnC,uBAAW,OAAO,OAAO,OAAO,IAAI,aAAa,GAAG;AAChD,mBAAK,GAAG,QAAQ,kBAAkB,GAAG;AACjC,sBAAM,IAAI,OAAO,MAAS;AAAA,YAClC;AACA,gBAAI,IAAI;AACJ,qBAAO,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,MAAM,MAAM;AACrG,mBAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAAA,UACtF;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,aAAa;AAerB,aAAS,gBAAgB,WAAW;AAChC,cAAQ,MAAM;AAAA,QACV,MAAK,qBAAqB,OACtB,UAAU,IAAI,SAAS,6BAA6B;AAAA,QACxD,MAAK,MAAM,QAAQ,SAAS,KACxB,UAAU,SAAS,SAAS,6BAA6B;AAAA,QAC7D,MAAK,OAAO,cAAc,YACtB,UACK,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EACnB,SAAS,SAAS,6BAA6B;AACpD,iBAAO,SAAS;AAAA,QACpB;AACI,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,YAAQ,kBAAkB;AAAA;AAAA;;;AC9S1B;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGE,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,kBAAqB,OAAO;AACzC,iBAAa,kBAAqB,OAAO;AACzC,iBAAa,kBAAqB,OAAO;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXnC,SAAU,UAAa,OAAuB;AAClD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,OAAO,MAAM,SAAS;AAE1B;AAGA,SAAS,oBAAuB,YAAyB;AACvD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAM,eAAe,WAAW,UAAU;MACxC,KAAK,GAAC;AACJ,gBAAQ,CAAC;AACT,qBAAa,YAAW;MAC1B;MACA,OAAO;MACP,WAAQ;AACN,eAAO,IAAI,MAAM,mBAAmB,CAAC;MACvC;KACD;EACH,CAAC;AACH;AAGM,SAAU,aAAgB,OAAU;AACxC,SACE,OAAO,UAAU,YACjB,UAAU,QACV,eAAe,SACf,OAAO,MAAM,cAAc;AAE/B;AAEM,SAAU,gBACd,OAAc;AAEd,SACE,OAAO,UAAU,YACjB,UAAU,SAGR,MAAc,OAAO,WAAW,MAAM,oBACtC,OAAO,iBAAiB;AAE9B;AAEA,SAAe,uBACb,OAAkD;;;AAKlD,UAAM,kBAAiB,MACrB,YAAY,QAAQ,QAAQ,MAAM,OAAO,aAAa,EAAC,GACvD,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,KAAK;AACpB,UAAM,gBACJ,UAAU,QAAQ,QAAQ,MAAM,OAAO,aAAa,EAAC,GACrD,KAAK,KAAK,KAAK;AAEjB,UAAM,SAAS,MAAM,aAAY;AAEjC,UAAK,mBAAc,QAAd,mBAAc,SAAA,SAAd,eAAc;AACnB,WAAO,OAAO;;;AAGV,SAAgB,uBACpB,eAAgC;;AAEhC,UAAM,SAAS,MAAM;AACrB,QAAI,gBAAgB,MAAM,GAAG;AAC3B,aAAO,uBAAuB,MAAM;;AAEtC,QAAI,aAAa,MAAM,GAAG;AACxB,aAAO,oBAAoB,MAAM;;AAEnC,WAAO;EACT,CAAC;;;;ACpFD;;;ACAA,IAAI,IAAE,IAAI;AAAY,eAAe,EAAEC,IAAE,GAAE;AAAC,MAAG,CAACA,GAAE,MAAI,CAACA,GAAE,QAAMA,GAAE,SAAS,QAAOA;AAAE,MAAI,IAAEA,GAAE,QAAQ,IAAI,cAAc;AAAE,MAAG,CAAC,KAAG,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE,QAAOA;AAAE,MAAI,IAAE,EAAE,QAAQ,WAAW,GAAE,IAAE;AAAI,MAAG,CAAC,GAAE;AAAC,QAAIC,KAAE,IAAE,GAAED,KAAE,EAAE,QAAQ,KAAIC,EAAC;AAAE,QAAE,EAAE,MAAMA,IAAED,KAAE,KAAGA,KAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,MAAK,EAAE;AAAA,EAAC;AAAC,SAAO,iBAAgBA,IAAEE,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAE,GAAE,IAAEL,GAAE,UAAU,GAAE,IAAE,CAACG,MAAG,CAACA,GAAE,UAAS,IAAED,GAAE,QAAO,IAAE,IAAG,IAAE,CAAC;AAAE,QAAG;AAAC,UAAIF;AAAE,QAAE,QAAK,EAAEA,KAAE,MAAM,EAAE,KAAK,GAAG,QAAM;AAAC,YAAIG,KAAE,EAAE,OAAOH,GAAE,KAAK;AAAE,QAAAI,KAAE,EAAE,QAAO,KAAGD;AAAE,YAAIG,KAAEH,GAAE,QAAQD,EAAC;AAAE,aAAI,CAACI,KAAEF,MAAGE,KAAEF,KAAE,EAAE,QAAQF,EAAC,GAAE,IAAE,CAAC,GAAE,CAACE,MAAG;AAAC,cAAIH,KAAE,EAAE,MAAM,GAAEG,EAAC,GAAEJ,KAAE,EAAE,MAAMI,KAAE,CAAC;AAAE,cAAGC,IAAE;AAAC,gBAAIH,KAAED,GAAE,QAAQ,UAAU,IAAE,GAAEE,KAAEF,GAAE,YAAY,QAAOC,EAAC,GAAEE,KAAE,OAAGC,KAAEJ,GAAE,MAAMC,IAAEC,KAAE,KAAG,SAAOA,EAAC,GAAEG,KAAE,OAAOL,GAAE,MAAM,GAAEC,EAAC,CAAC,EAAE,KAAK,EAAE,MAAM,MAAM,GAAEK,KAAE,CAAC,GAAEC,KAAEF,GAAE;AAAO,mBAAK,IAAEA,GAAE,EAAEE,EAAC,GAAE,IAAE,EAAE,MAAM,IAAI,GAAED,GAAE,EAAE,MAAM,EAAE,YAAY,CAAC,IAAE,EAAE,KAAK,IAAI,EAAE;AAAC,gBAAG,IAAEA,GAAE,cAAc,GAAE,KAAG,CAAC,EAAE,QAAQ,kBAAkB,EAAE,KAAG;AAAC,cAAAF,KAAE,KAAK,MAAMA,EAAC,GAAED,KAAE;AAAA,YAAE,SAAOH,IAAE;AAAA,YAAC;AAAC,gBAAG,IAAE,EAAC,SAAQM,IAAE,MAAKF,IAAE,MAAKD,GAAC,GAAE,IAAE,MAAM,IAAE,EAAE,KAAK,CAAC,GAAE,SAAOJ,GAAE,MAAM,GAAE,CAAC,EAAE,OAAM;AAAA,UAAC,MAAM,CAAAE,KAAE,SAAOA,IAAEG,KAAE,KAAG;AAAE,cAAEL,IAAEI,KAAE,EAAE,QAAQF,EAAC;AAAA,QAAC;AAAC,UAAE,WAAS,MAAM;AAAA,MAAE;AAAA,IAAC,UAAC;AAAQ,QAAE,WAAS,MAAM,IAAG,MAAM,EAAE,OAAO;AAAA,IAAC;AAAA,EAAC,EAAEF,GAAE,MAAK,KAAK,CAAC,IAAG,CAAC;AAAC;;;ACOpjC,SAAS,aAAa,QAAQ,UAAU,SAAS;AAC7C,QAAM,SAAU,gBAAgB,mBAAmB;AAC/C,WAAO;AAAA,EACX,EAAG;AACH,QAAM,iBAAiB,OAAO,OAAO,KAAK,MAAM;AAChD,MAAI,UAAU;AACV,WAAO,SAAS,IAAI,SAAS;AACzB,eAAS;AACT,aAAO,eAAe,GAAG,IAAI;AAAA,IACjC;AAAA,EACJ;AACA,MAAI,SAAS;AACT,UAAM,gBAAgB,OAAO,MAAM,KAAK,MAAM;AAC9C,WAAO,QAAQ,CAAC,QAAQ;AACpB,cAAQ,GAAG;AACX,aAAO,cAAc,GAAG;AAAA,IAC5B;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB;AACtB,QAAM,IAAI,CAAC;AACX,IAAE,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AACzC,MAAE,UAAU;AACZ,MAAE,SAAS;AAAA,EACf,CAAC;AACD,SAAO;AACX;AAQA,SAAS,oCAAoC;AACzC,MAAI,QAAQ;AAAA,IACR,MAAM;AAAA;AAAA,EACV;AACA,MAAI,OAAO,eAAe;AAC1B,QAAM,SAAS,CAAC;AAChB,WAAS,UAAU,OAAO;AACtB,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,WAAO,KAAK,KAAK;AACjB,SAAK,QAAQ;AACb,WAAO,eAAe;AAAA,EAC1B;AACA,QAAM,SAAU,gBAAgB,gCAAgC;AAC5D,WAAO,MAAM;AACT,UAAI,OAAO,SAAS,GAAG;AAEnB,cAAM,OAAO,MAAM;AAAA,MACvB,OACK;AACD,YAAI,MAAM,SAAS,SAAqB;AACpC,gBAAM,MAAM;AAAA,QAChB;AACA,YAAI,MAAM,SAAS,YAA2B;AAC1C;AAAA,QACJ;AACA,cAAM,KAAK;AAAA,MACf;AAAA,IACJ;AAAA,EACJ,EAAG;AACH,QAAM,SAAS,aAAa,QAAQ,MAAM;AACtC,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,YAAQ;AAAA,MACJ,MAAM;AAAA;AAAA,IACV;AACA,SAAK,QAAQ;AAAA,EACjB,GAAG,CAAC,UAAU;AACV,QAAI,MAAM,SAAS,WAAyB;AACxC;AAAA,IACJ;AACA,YAAQ;AAAA,MACJ,MAAM;AAAA,MACN;AAAA,IACJ;AACA,SAAK,QAAQ;AAAA,EACjB,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA,uBAAuB;AAAA,EAC3B;AACJ;AAEA,IAAM,oCAAoC,CAAC,SAAS;AAChD,QAAM,EAAE,WAAW,sBAAsB,IAAI,kCAAkC;AAC/E,QAAM,UAAU,KAAK;AAAA,IACjB,MAAM,CAAC,UAAU;AACb,gBAAU,KAAK;AAAA,IACnB;AAAA,IACA,UAAU,MAAM;AAEZ,4BAAsB,OAAO;AAAA,IACjC;AAAA,IACA,OAAO,CAAC,QAAQ;AAEZ,4BAAsB,MAAM,GAAG;AAAA,IACnC;AAAA,EACJ,CAAC;AAED,QAAM,iBAAiB,sBAAsB;AAC7C,MAAI,cAAc;AAClB,wBAAsB,SAAS,MAAM;AACjC,QAAI,gBAAgB,QAAW;AAC3B,cAAQ;AACR,oBAAc,eAAe;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAqBA,SAASS,iBAAgB,OAAO;AAC5B,SAAQ,OAAO,UAAU,YACrB,UAAU;AAAA;AAAA;AAAA;AAAA,GAKT,MAAM,OAAO,WAAW,MAAM,oBAC1B,OAAO,iBAAiB,OAAO,iBAAiB;AAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AFrIA,IAAM,eAAe,CAAC,QAAyC;AAC7D,SAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,UAAU;AAC9D;AASO,IAAM,yBAAyB,CACpC,UACA,SACW;AACX,MAAI,iBAAiB;AACrB,QAAM,UAAU;IACd,oBAAoB,MAAI;;AACtB,UAAI,WAAS,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,KAAK,cAAc,gBAAgB;AAClE,yBAAiB;;IAErB;GACD;AACD,SAAO;AACT;AAUO,IAAM,sBACX,CAAC,SAA+B,cAChC,CAAO,eAA8B,gBAA6BC,WAAA,QAAA,QAAA,QAAA,aAAA;AAChE,QAAM,OAAO,MAAM,UAAU,QAAQ,KAAK;IACxC,QAAQ;IACR,MAAM,KAAK,UAAU,aAAa;IAClC,SAAO,OAAA,OAAA,OAAA,OAAA,EACL,gBAAgB,mBAAkB,GAC/B,QAAQ,OAAO,GACf,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO;GAE1B;AACD,SAAO,KAAK,KAAI;AAClB,CAAC;AAEI,IAAM,iCAAiC,CAC5C,KACA,qBACE;AACF,MAAI;AACJ,MAAI;AACF,UAAM,EAAE,aAAY,IAAK;AAKzB,eAAW,aAAa;MACtB;MACA;KACD;AACD,WAAO,kCAAkC,QAAQ;WAC1C,KAAK;AACZ,QAAI,aAAa,GAAG,KAAK,IAAI,SAAS,oBAAoB;AACxD,YAAM,IAAI,MACR,iGAAiG;;AAIrG,YAAQ,MAAM,uCAAuC,GAAG,IAAI,GAAG;;AAEnE;AAQO,IAAM,oCACX,CAAC,aAAqB,CAAC,kBACrB,kCAAmD,UACjD,SAAS,UAAU,eAAa,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3B,IAAI,GAAA,EACP,MAAM,KAAG;AACP,MAAI,eAAe,YAAY;AAC7B,SAAK,MACH,IAAI,MACF,4BAA4B,IAAI,IAAI,IAClC,IAAI,UAAU,EAChB,GAAG,KAAI,CAAE,CACV;SAEE;AACL,SAAK,MAAM,GAAG;;AAElB,EAAC,CAAA,CAAA,CACD;AAUD,IAAM,gCACX,CAAC,mBACD,CAAC,kBAAgC;AAC/B,QAAM,aAAa,eAAe,QAAQ,aAAa;AACvD,SAAO,kCAEL,UAAQ,WAAW,UAAU,IAAI,EAAE,WAAW;AAElD;AAQK,IAAM,yBAAyB,CACpC,SACA,cAEA,SAAiB,eAA8B,aAAyB;;;AACtE,UAAM,WAAW,MAAA,QAAM,UAAU,QAAQ,KAAK;MAC5C,QAAQ;MACR,MAAM,KAAK,UAAU,aAAa;MAClC,SAAO,OAAA,OAAA,OAAA,OAAA,EACL,gBAAgB,oBAChB,QAAQ,oCAAmC,GACxC,QAAQ,OAAO,GAGf,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO;KAE1B,EAAE,KAAK,OACN,EAA6D,GAAG;MAC9D,UAAU;KACX,CAAC,CACH;AAGD,QAAI,CAACC,iBAAgB,QAAQ,GAAG;AAC9B,aAAA,MAAA,QAAO,MAAA,MAAA,QAAM,SAAS,KAAI,CAAE,CAAA;;;AAG9B,eAA0B,aAAA,cAAA,QAAQ,GAAA,cAAA,eAAA,MAAA,QAAA,WAAA,KAAA,CAAA,GAAA,CAAA,aAAA,QAAA;AAAvB,cAAM,QAAK,aAAA;AACpB,YAAI,MAAM,KAAK,UAAQ,CAAC,KAAK,IAAI,GAAG;AAClC,gBAAM,UAAU,MAAM,IACpB,UAAQ;EAAc,KAAK,OAAO;;;EAAe,KAAK,IAAI,EAAE;AAE9D,gBAAM,IAAI,MACR;EAAuD,OAAO,EAAE;;AAGpE,cAAA,MAAA,QAAM,MAAM,IAAI,UAAQ,KAAK,IAAI,CAAC;;;;;;;;;;;EAEtC,CAAC;;AAOI,IAAM,eAAe,CAC1B,SACA,gBACE;AACF,MAAI,QAAQ,UAAU;AACpB,WAAO,kCAAkC,QAAQ,QAAQ;;AAE3D,MAAI,QAAQ,iBAAiB;AAC3B,WAAO,+BAA+B,QAAQ,iBAAe,OAAA,OAAA,OAAA,OAAA,CAAA,GACxD,QAAQ,kBAAkB,GAC1B,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,OAAO,CAAA;;AAG3B,QAAM,yBAAyB,QAAQ,gBAAgB,QAAQ;AAC/D,MAAI,wBAAwB;AAC1B,WAAO,8BAA8B,sBAAsB;;AAE/D;;;AGhMM,SAAU,sBAAsB,SAA6B;AACjE,MAAI;AACJ,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AACjD,gBAAY,OAAO;;AAErB,OACE,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,+BAA8B,QACvC,QAAQ,8BAA8B,OACtC;AACA,YAAQ,4BAA4B;;AAEtC,MAAI,QAAQ,OAAO;AACjB,gBAAY,QAAQ;;AAEtB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,2CAA2C;;AAG7D,QAAM,gBAAgB,oBAAoB,SAAS,SAAS;AAE5D,QAAM,cAAc,QAAQ,4BACxB,uBAAuB,SAAS,SAAS,IACzC;AAEJ,SAAO,CAAC,eAAe,gBAAe;AACpC,QAAI,cAAc,kBAAkB,sBAAsB;AACxD,cAAQ,QAAQ,iBAAiB,eAC/B,eACA,WAAW;;AAGf,UAAM,kBAAiB,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,eAChC,uBACE,YAAY,aACZ,cAAc,iBAAiB,MAAS,IAE1C;AACJ,QAAI,gBAAgB;AAClB,YAAM,YAAY,aAAa,SAAS,WAAW;AAEnD,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MACR,2FACE,QAAQ,kBACJ,gBAAgB,QAAQ,eAAe,YACvC,wEACN,EAAE;;AAGN,aAAO,UAAU,aAAa;;AAEhC,WAAO,YAAY,eAAe,WAAW;EAC/C;AACF;;;ACtEA,SAAS,UAAU,KAAY;AAC7B,SAAO,KAAK,UAAU,KAAK,MAAM,CAAC;AACpC;AAEA,SAAS,kBAAkB,OAAY;AACrC,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA,EAER,SAAS,MAAM,SACf,OAAO,MAAM,MAAK,CAAA;AAEtB;AAEA,SAAS,kBAAkB,OAAc;AACvC,MAAI,iBAAiB,OAAO;AAC1B,WAAO,kBAAkB,KAAK;;AAEhC,SAAO;AACT;AAEM,SAAU,YAAY,OAAc;AACxC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,UAAU;MACf,QAAQ,MAAM,IAAI,CAAAC,OAAK,kBAAkBA,EAAC,CAAC;KAC5C;;AAEH,SAAO,UAAU,EAAE,QAAQ,CAAC,kBAAkB,KAAK,CAAC,EAAC,CAAE;AACzD;AAEM,SAAU,aAAa,QAAW;AACtC,SAAO,UAAU,MAAM;AACzB;;;AC/BA;AAgCM,SAAU,UACd,QACA,WACA,sBAA6C;AAE7C,QAAM,aAA0B,CAAA;AAEhC,MAAI,CAAC,UAAU,CAAC,WAAW;AACzB,WAAO,EAAE,YAAY,QAAQ,UAAS;;AAGxC,MAAI;AACJ,MAAI;AACF,UAAM,MAAM,SAAS;WACrB,IAAM;AACN,WAAO,EAAE,YAAY,QAAQ,UAAS;;AAGxC,QAAM,cAAc,wBAAwB;AAC5C,QAAM,WAAW,IAAI,SAAS,MAAM;AACpC,QAAM,KAAK;IACT,MAAM,MAAI;AACR,eAAS,MAAM,IAAI;IACrB;IACA,MAAM,MAAI;AACR,eAAS,MAAM,IAAI;AACnB,UAAI,KAAK,SAAS,WAAW,CAAC,KAAK,cAAc;AAC/C,cAAM,YAAY,SAAS,QAAO;AAClC,cAAM,eAAe,kBACnB,YAAY,SAAS,GACrB,WAAW;AAEb,YAAI,gBAAgB,KAAK,KAAK;AAC5B,gBAAM,SAAS,eAAe,WAAW,KAAK,IAAI,KAAK;AACvD,qBAAW,KAAK;YACd,OAAO,KAAK,IAAI;YAChB,QAAQ,MAAM,MAAM,YAAY,EAAE,WAAW,MAAM,OAAO,MAAM;WACjE;;;IAGP;GACD;AAGD,SAAO;IACL;IACA,QAAQ,eAAe,WAAW,UAAU;;AAEhD;AAKA,SAAS,4BAA4B,MAAiB;AAGpD,MAAI,EAAE,eAAe,OAAO;AAC1B,WAAO,CAAA;;AAGT,QAAM,SAAS,KAAK,UAAS;AAG7B,MAAI,OAAO,IAAI;AACb,WAAO,CAAC,IAAI;;AAId,MAAI,OAAO,OAAO;AAChB,WAAO,CAAC,OAAO;;AAIjB,MAAI,OAAO,MAAM;AACf,WAAO,CAAC,MAAM;;AAIhB,QAAM,iBAAgC,CAAA;AACtC,aAAW,aAAa,OAAO,KAAK,MAAM,GAAG;AAC3C,QAAI,WAAW,OAAO,SAAS,EAAE,IAAI,GAAG;AACtC,qBAAe,KAAK,SAAS;;;AAGjC,SAAO;AACT;AAIA,SAAS,kBACP,MACA,sBAA4C;AAG5C,QAAM,YAAY,aAAa,IAAI;AAGnC,MAAI,CAAC,QAAQ,WAAW,IAAI,GAAG;AAC7B;;AAIF,QAAM,aAAa,qBAAqB,SAAS;AAGjD,MACE,CAAC,MAAM,QAAQ,UAAU,KACzB,WAAW,WAAW,KACtB,EAAE,eAAe,YACjB;AACA;;AAIF,SAAO;IACL,MAAM,KAAK;IACX,YAAY,WAAW,IAAI,eAAY;AACrC,YAAM,WAAW,UAAU,UAAS,EAAG,SAAS;AAChD,YAAM,YAAY,WAAW,SAAS,OAAO;AAC7C,aAAO;QACL,MAAM,KAAK;QACX,MAAM;UACJ,MAAM,KAAK;UACX,OAAO;;QAIT,cAAc,kBAAkB,WAAY,oBAAoB;;IAEpE,CAAC;;AAEL;AAIA,SAAS,eAAe,SAAiB,YAAuB;AAC9D,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;;AAET,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,aAAW,EAAE,OAAO,OAAM,KAAM,YAAY;AAC1C,cAAU,QAAQ,MAAM,WAAW,KAAK,IAAI;AAC5C,gBAAY;;AAEd,YAAU,QAAQ,MAAM,SAAS;AACjC,SAAO;AACT;AAIA,SAAS,eAAe,KAAa,OAAa;AAChD,MAAI,cAAc;AAClB,MAAI,YAAY;AAChB,SAAO,aAAa;AAClB,UAAM,IAAI,IAAI,WAAW,cAAc,CAAC;AAExC,QAAI,MAAM,MAAM,MAAM,MAAM,MAAM,QAAU,MAAM,MAAQ;AACxD;;AAEF;AAEA,QAAI,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AAC5D,kBAAY;;;AAGhB,SAAO,IAAI,MAAM,aAAa,SAAS;AACzC;AAEA,SAAS,YACP,WAA+C;AAE/C,MAAI,WAAW;AACb,WAAO;;AAEX;;;AC/MA;AAeA,SAAS,SACP,OACA,UAAgC;;AAEhC,QAAM,cAAc,oBAAI,IAAG;AAC3B,QAAM,SAA0B,CAAA;AAChC,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,SAAS;AACzB,YAAM,cAAc,SAAS,IAAI;AACjC,YAAM,WAAW,YAAY,IAAI,WAAW;AAC5C,WAAI,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ;AAE3B,cAAM,YAAS,OAAA,OAAA,CAAA,GAAQ,IAAI;AAC3B,eAAO,KAAK,SAAS;kBACZ,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,iBAAgB,KAAK,cAAc;AAEtD,iBAAS,aAAa,aAAa;UACjC,GAAG,SAAS,aAAa;UACzB,GAAG,KAAK,aAAa;;iBAEd,CAAC,UAAU;AACpB,cAAM,YAAS,OAAA,OAAA,CAAA,GAAQ,IAAI;AAC3B,oBAAY,IAAI,aAAa,SAAS;AACtC,eAAO,KAAK,SAAS;;WAElB;AACL,aAAO,KAAK,IAAI;;;AAGpB,SAAO;AACT;AAEA,SAAS,8BACP,qBAGA,YACA,kBAA2C;;AAE3C,QAAM,uBAAuB,mBACzB,aAAa,gBAAgB,EAAE,OAC/B;AACJ,QAAM,mBAAmB,CAAA;AACzB,QAAM,cAAwB,CAAA;AAC9B,WAAS,aAAa,YAAY;AAChC,QAAI,UAAU,SAAS,kBAAkB;AACvC,YAAM,eAAe,UAAU,KAAK;AACpC,UAAI,CAAC,UAAU,cAAc,UAAU,WAAW,WAAW,GAAG;AAC9D,YAAI,YAAY,SAAS,YAAY,GAAG;AAEtC;eACK;AACL,sBAAY,KAAK,YAAY;;;AAGjC,YAAM,qBAAqB,oBAAoB,UAAU,KAAK,KAAK;AACnE,UAAI,oBAAoB;AACtB,cAAM,EAAE,eAAe,YAAY,aAAY,IAAK;AACpD,oBAAY;UACV,MAAM,KAAK;UACX;UACA;UACA;;;;AAIN,QACE,UAAU,SAAS,KAAK,oBAEvB,CAAC,UAAU,gBAAc,KAAA,UAAU,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,IAC3D;AACA,YAAM,mBAAmB,UAAU,gBAC/B,UAAU,cAAc,KAAK,QAC7B;AACJ,UAAI,CAAC,oBAAoB,qBAAqB,sBAAsB;AAClE,yBAAiB,KACf,GAAG,8BACD,qBACA,UAAU,aAAa,YACvB,gBAAgB,CACjB;AAEH;;;AAGJ,qBAAiB,KAAK,SAAS;;AAEjC,SAAO;AACT;AAKM,SAAU,SACd,aACA,QAA6B;AAI7B,QAAM,WAAW,SAAS,IAAI,SAAS,MAAM,IAAI;AAEjD,QAAM,sBAEF,uBAAO,OAAO,IAAI;AAEtB,aAAW,cAAc,YAAY,aAAa;AAChD,QAAI,WAAW,SAAS,KAAK,qBAAqB;AAChD,0BAAoB,WAAW,KAAK,KAAK,IAAI;;;AAIjD,QAAM,kBAA8B;IAClC,aAAa,MAAS;AACpB,YAAM,mBAAmB,WAAW,SAAS,cAAa,IAAK;AAC/D,UAAI,EAAE,WAAU,IAAK;AAErB,mBAAa,8BACX,qBACA,YACA,gBAAgB;AAGlB,aAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,IAAI,GAAA,EACP,WAAU,CAAA;IAEd;IACA,qBAAkB;AAChB,aAAO;IACT;;AAGF,QAAM,eAAe,MACnB,aACA,WAAW,kBAAkB,UAAU,eAAe,IAAI,eAAe;AAG3E,QAAM,sBAAkC;IACtC,aAAa,MAAS;AACpB,UAAI,EAAE,WAAU,IAAK;AAErB,mBAAa,SAAS,YAAY,eAChC,UAAU,QAAQ,UAAU,MAAM,QAAQ,UAAU,KAAK,KAAK;AAGhE,aAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,IAAI,GAAA,EACP,WAAU,CAAA;IAEd;IACA,qBAAkB;AAChB,aAAO;IACT;;AAGF,SAAO,MAAM,cAAc,mBAAmB;AAChD;;;ACrKM,SAAU,yBACd,gBACA,2BACA,YAAsC;AAGtC,MAAI,CAAC,cAAc,WAAW,SAAS,GAAG;AACxC;;AAIF,QAAM,QAAQ,WAAW,IAAI,QAAK;AAAA,QAAA;AAAC,YAAA,KAAA,GAAG,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;EAAK,CAAA;AACjD,MAAI,6BAA6B,MAAM,SAAS,yBAAyB,GAAG;AAC1E,WAAO;;AAIT,MAAI,6BAA6B,gBAAgB;AAC/C,UAAM,YAAY,eAAe,IAAI,QAAK;AAAA,UAAA;AAAC,cAAA,KAAA,GAAG,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;IAAK,CAAA;AACzD,UAAM,YAAY,UAAU,QAAQ,yBAAyB;AAC7D,QAAI,cAAc,MAAM,YAAY,MAAM,QAAQ;AAChD,aAAO,MAAM,SAAS;;;AAK1B,SAAO,MAAM,CAAC;AAChB;;;ACGA,SAAS,aAAa,SAAkBC,IAAU;AAChD,SACEA,cAAa,iBAEZA,GAAE,SAAS,MAEVA,GAAE,SAAS,QAGXA,GAAE,SAAS,wBAEXA,GAAE,SAAS,iCAEb,QAAQ,WAAW;AAEvB;AAEM,IAAO,aAAP,MAAiB;EAGrB,YAAY,SAAwB;AAClC,QAAI,SAAS;AACX,WAAK,UAAU;eACN,YAAY,MAAM;AAE3B,WAAK,UAAU;eACN,OAAO,WAAW,aAAa;AACxC,WAAK,UAAU;WACV;AACL,WAAK,UAAU;QACb,SAAS,aAAa,QAAQ,KAAK,YAAY;QAC/C,SAAS,aAAa,QAAQ,KAAK,YAAY;QAC/C,YAAY,aAAa,WAAW,KAAK,YAAY;QAErD,IAAI,SAAM;AACR,cAAI,OAAO;AACX,qBAAW,OAAO,cAAc;AAC9B,gBAAI,IAAI,QAAQ,GAAG,iBAAiB,GAAG,MAAM,GAAG;AAC9C,sBAAQ;;;AAGZ,iBAAO;QACT;QAEA,QAAK;AAEH,qBAAW,OAAO,cAAc;AAC9B,gBAAI,IAAI,QAAQ,GAAG,iBAAiB,GAAG,MAAM,GAAG;AAC9C,2BAAa,WAAW,GAAG;;;QAGjC;;;EAGN;EAEA,IAAI,MAAY;AACd,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;;AAGT,UAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI;AACxC,UAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AAEtC,QAAI,UAAU,UAAU,UAAU,aAAa;AAC7C,WAAK,QAAQ,WAAW,GAAG;AAC3B,aAAO;;AAGT,WAAO,SAAS;EAClB;EAEA,IACE,MACA,OAAa;AAEb,QAAI,aAAa;AACjB,QAAI,QAAsB;AAE1B,QAAI,KAAK,SAAS;AAChB,YAAM,MAAM,GAAG,iBAAiB,IAAI,IAAI;AACxC,UAAI,OAAO;AACT,YAAI;AACF,eAAK,QAAQ,QAAQ,KAAK,KAAK;iBACxBA,IAAG;AACV,kBAAQA,cAAa,QAAQA,KAAI,IAAI,MAAM,GAAGA,EAAC,EAAE;AACjD,uBAAa,aAAa,KAAK,SAASA,EAAC;;aAEtC;AAEL,aAAK,QAAQ,WAAW,GAAG;;;AAI/B,WAAO,EAAE,cAAc,YAAY,MAAK;EAC1C;EAEA,QAAK;AACH,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,MAAK;;EAEtB;;AAGF,IAAM,oBAAoB;;;AC5I1B;;;ACWM,IAAO,aAAP,MAAiB;EAGrB,YACU,KACA,SACA,UAAyB,MAAI;AAF7B,SAAA,MAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AAER,SAAK,QAAQ,KAAK,SAAQ;EAC5B;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,MAAM;EACpB;EAEA,SAAS,MAAoB;AAC3B,WAAO,KAAK,MAAM,KAChB,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;EAE5C;EAEA,KAAK,MAAsB,OAAc;AACvC,QAAI,OAAO,UAAU,YAAY,KAAK,MAAM,KAAK,GAAG;AAClD,YAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,UACE,MAAM,UAAU,KAAK,SACrB,MAAM,cAAc,KAAK,aACzB,MAAM,YAAY,KAAK,WACvB,MAAM,kBAAkB,KAAK,eAC7B;AACA,aAAK,MAAM,OAAO,OAAO,GAAG,IAAI;AAChC,aAAK,KAAI;AACT;;;AAIJ,UAAM,YAAY,KAAK,MAAM,UAC3B,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;AAE1C,QAAI,cAAc,IAAI;AACpB,WAAK,MAAM,OAAO,WAAW,GAAG,IAAI;AACpC,WAAK,KAAI;;EAEb;EAEA,OAAO,MAAoB;AACzB,UAAM,YAAY,KAAK,MAAM,UAC3B,OACE,EAAE,UAAU,KAAK,SACjB,EAAE,cAAc,KAAK,aACrB,EAAE,YAAY,KAAK,WACnB,EAAE,kBAAkB,KAAK,aAAa;AAE1C,QAAI,cAAc,IAAI;AACpB,WAAK,MAAM,OAAO,WAAW,CAAC;AAC9B,WAAK,KAAI;;EAEb;EAEA,cAAW;AACT,WAAO,KAAK,MAAM,GAAG,EAAE;EACzB;EAEA,WAAQ;AACN,UAAM,MAAM,KAAK,QAAQ,IAAI,KAAK,GAAG;AACrC,QAAI,KAAK;AACP,aAAO,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG;;AAEjC,WAAO,CAAA;EACT;EAEA,KAAK,MAAoB;AACvB,UAAM,QAAQ,CAAC,GAAG,KAAK,OAAO,IAAI;AAElC,QAAI,KAAK,WAAW,MAAM,SAAS,KAAK,SAAS;AAC/C,YAAM,MAAK;;AAGb,aAAS,WAAW,GAAG,WAAW,GAAG,YAAY;AAC/C,YAAM,WAAW,KAAK,QAAQ,IAC5B,KAAK,KACL,KAAK,UAAU,EAAE,CAAC,KAAK,GAAG,GAAG,MAAK,CAAE,CAAC;AAEvC,UAAI,EAAC,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,QAAO;AACpB,aAAK,QAAQ;iBACJ,SAAS,gBAAgB,KAAK,SAAS;AAEhD,cAAM,MAAK;aACN;AACL;;;EAGN;EAEA,OAAI;AACF,SAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,UAAU,EAAE,CAAC,KAAK,GAAG,GAAG,KAAK,MAAK,CAAE,CAAC;EACvE;;;;AD9GF,IAAM,iBAAiB;AAEjB,IAAO,eAAP,MAAmB;EAKvB,YACU,SACA,kBAAwB;AADxB,SAAA,UAAA;AACA,SAAA,mBAAA;AAwDV,SAAA,gBAAgB,CAAC,EACf,OACA,WACA,SACA,cAAa,MACM;AACnB,UACE,CAAC,KAAK,gBACJ,OACA,WACA,SACA,KAAK,QAAQ,YAAW,CAAE,GAE5B;AACA;;AAEF,WAAK,QAAQ,KAAK;QAChB;QACA;QACA;QACA;OACD;AACD,YAAM,iBAAiB,KAAK,QAAQ;AACpC,YAAM,kBAAkB,KAAK,SAAS;AACtC,WAAK,UAAU,eAAe,OAAO,eAAe;IACtD;AAuDA,SAAA,gBAAgB,CACd,EAAE,OAAO,WAAW,SAAS,eAAe,SAAQ,GACpD,iBAAiB,UACf;AACF,eAAS,gBAAgB,OAAiB;AACxC,cAAM,QAAQ,MAAM,MAAM,KACxB,OACE,EAAE,UAAU,SACZ,EAAE,cAAc,aAChB,EAAE,YAAY,WACd,EAAE,kBAAkB,aAAa;AAErC,YAAI,OAAO;AACT,gBAAM,OAAO,KAAK;;MAEtB;AAEA,UAAI,YAAY,gBAAgB;AAC9B,wBAAgB,KAAK,QAAQ;;AAE/B,UAAI,CAAC,YAAY,gBAAgB;AAC/B,wBAAgB,KAAK,OAAO;;AAG9B,WAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,KAAK;IAC/D;AA/JE,SAAK,UAAU,IAAI,WACjB,WACA,KAAK,SACL,KAAK,gBAAgB;AAGvB,SAAK,WAAW,IAAI,WAAW,aAAa,KAAK,SAAS,IAAI;AAE9D,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,SAAQ,GAAI,GAAG,KAAK,SAAS,SAAQ,CAAE;EACzE;EAEQ,gBACN,OACA,WACA,SACA,gBAA+B;AAE/B,QAAI,CAAC,OAAO;AACV,aAAO;;AAGT,QAAI;AACF,YAAM,KAAK;aACX,IAAM;AACN,aAAO;;AAIT,QAAI,MAAM,SAAS,gBAAgB;AACjC,aAAO;;AAET,QAAI,CAAC,gBAAgB;AACnB,aAAO;;AAET,QAAI,KAAK,UAAU,KAAK,MAAM,KAAK,UAAU,eAAe,KAAK,GAAG;AAClE,UACE,KAAK,UAAU,SAAS,MAAM,KAAK,UAAU,eAAe,SAAS,GACrE;AACA,YACE,KAAK,UAAU,OAAO,MAAM,KAAK,UAAU,eAAe,OAAO,GACjE;AACA,iBAAO;;AAET,YAAI,WAAW,CAAC,eAAe,SAAS;AACtC,iBAAO;;;AAGX,UAAI,aAAa,CAAC,eAAe,WAAW;AAC1C,eAAO;;;AAGX,WAAO;EACT;EA6BA,eAAe,EACb,OACA,WACA,SACA,eACA,OACA,SAAQ,GACO;AACf,UAAM,OAAuB;MAC3B;MACA;MACA;MACA;MACA;;AAEF,QAAI,UAAU;AACZ,WAAK,WAAW;AAChB,WAAK,SAAS,OAAO,IAAI;AACzB,WAAK,QAAQ,KAAK,IAAI;WACjB;AACL,WAAK,WAAW;AAChB,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,QAAQ,OAAO,IAAI;;AAE1B,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,KAAK;EAC/D;EAEA,UACE,EACE,OACA,WACA,SACA,eACA,OACA,SAAQ,GAEV,OAAc;AAEd,UAAM,OAAO;MACX;MACA;MACA;MACA;MACA;;AAEF,QAAI,UAAU;AACZ,WAAK,SAAS,KAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GAAM,IAAI,GAAA,EAAE,SAAQ,CAAA,GAAI,KAAK;WAC1C;AACL,WAAK,QAAQ,KAAK,MAAM,KAAK;;AAE/B,SAAK,UAAU,CAAC,GAAG,KAAK,QAAQ,OAAO,GAAG,KAAK,SAAS,KAAK;EAC/D;;;;AErII,SAAU,mBAAmB,EACjC,UAAS,GACiB;AAC1B,QAAM,mBAAmB,GAAG,SAAS;AACrC,QAAM,gBAAgB,CAAC,QAAgB,GAAG,gBAAgB,GAAG,GAAG;AAEhE,QAAM,UAAmB;IACvB,SAAS,CAAC,KAAK,UAAU,aAAa,QAAQ,cAAc,GAAG,GAAG,KAAK;IACvE,SAAS,SAAO,aAAa,QAAQ,cAAc,GAAG,CAAC;IACvD,YAAY,SAAO,aAAa,WAAW,cAAc,GAAG,CAAC;IAC7D,IAAI,SAAM;AACR,UAAI,OAAO;AACX,iBAAW,OAAO,cAAc;AAC9B,YAAI,IAAI,QAAQ,gBAAgB,MAAM,GAAG;AACvC,kBAAQ;;;AAGZ,aAAO;IACT;IAEA,QAAK;AAEH,iBAAW,OAAO,cAAc;AAC9B,YAAI,IAAI,QAAQ,gBAAgB,MAAM,GAAG;AACvC,uBAAa,WAAW,GAAG;;;IAGjC;;AAGF,SAAO;AACT;", "names": ["isAsyncIterable", "CloseCode", "MessageType", "exports", "retries", "listeners", "message", "socket", "errOrCloseEvent", "__asyncValues", "v", "exports", "t", "e", "n", "i", "l", "r", "o", "f", "s", "isAsyncIterable", "__awaiter", "isAsyncIterable", "e", "e"]}
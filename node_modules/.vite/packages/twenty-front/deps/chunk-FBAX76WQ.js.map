{"version": 3, "sources": ["../../../../@apollo/src/utilities/graphql/directives.ts", "../../../../@wry/trie/src/index.ts", "../../../../@apollo/src/utilities/common/canUse.ts", "../../../../@apollo/src/utilities/common/objects.ts", "../../../../@apollo/src/utilities/graphql/fragments.ts", "../../../../@wry/caches/src/strong.ts", "../../../../@wry/caches/src/weak.ts", "../../../../@wry/caches/src/index.ts", "../../../../@apollo/src/utilities/caching/caches.ts", "../../../../@apollo/src/utilities/caching/sizes.ts", "../../../../@apollo/src/utilities/caching/index.ts", "../../../../@apollo/src/utilities/caching/getMemoryInternals.ts", "../../../../@apollo/src/utilities/common/canonicalStringify.ts", "../../../../@apollo/src/utilities/graphql/storeUtils.ts", "../../../../@apollo/src/utilities/graphql/getFromAST.ts", "../../../../optimism/node_modules/@wry/trie/src/index.ts", "../../../../@wry/context/src/slot.ts", "../../../../@wry/context/src/index.ts", "../../../../optimism/src/context.ts", "../../../../optimism/src/helpers.ts", "../../../../optimism/src/entry.ts", "../../../../optimism/src/dep.ts", "../../../../optimism/src/index.ts", "../../../../@apollo/src/utilities/graphql/DocumentTransform.ts", "../../../../@apollo/src/utilities/graphql/print.ts", "../../../../@apollo/src/utilities/common/arrays.ts", "../../../../@apollo/src/utilities/graphql/transform.ts", "../../../../@apollo/src/utilities/graphql/operations.ts", "../../../../@apollo/src/utilities/common/mergeDeep.ts", "../../../../@apollo/src/utilities/policies/pagination.ts", "../../../../zen-observable-ts/module.js", "../../../../symbol-observable/es/ponyfill.js", "../../../../symbol-observable/es/index.js", "../../../../@apollo/src/utilities/observables/Observable.ts", "../../../../@apollo/src/utilities/promises/decoration.ts", "../../../../@apollo/src/utilities/common/cloneDeep.ts", "../../../../@apollo/src/utilities/common/maybeDeepFreeze.ts", "../../../../@apollo/src/utilities/observables/iteration.ts", "../../../../@apollo/src/utilities/observables/asyncMap.ts", "../../../../@apollo/src/utilities/observables/subclassing.ts", "../../../../@apollo/src/utilities/observables/Concast.ts", "../../../../@apollo/src/utilities/common/incrementalResult.ts", "../../../../@apollo/src/utilities/common/errorHandling.ts", "../../../../@apollo/src/utilities/common/compact.ts", "../../../../@apollo/src/utilities/common/mergeOptions.ts", "../../../../@apollo/src/utilities/common/omitDeep.ts", "../../../../@apollo/src/utilities/common/stripTypename.ts", "../../../../@apollo/client/utilities/types/IsStrictlyAny.js", "../../../../@apollo/src/utilities/index.ts"], "sourcesContent": ["import { invariant } from \"../globals/index.js\";\n\n// Provides the methods that allow QueryManager to handle the `skip` and\n// `include` directives within GraphQL.\nimport type {\n  SelectionNode,\n  VariableNode,\n  BooleanValueNode,\n  DirectiveNode,\n  DocumentNode,\n  ArgumentNode,\n  ValueNode,\n  ASTNode,\n} from \"graphql\";\nimport { visit, BREAK } from \"graphql\";\n\nexport type DirectiveInfo = {\n  [fieldName: string]: { [argName: string]: any };\n};\n\nexport function shouldInclude(\n  { directives }: SelectionNode,\n  variables?: Record<string, any>\n): boolean {\n  if (!directives || !directives.length) {\n    return true;\n  }\n  return getInclusionDirectives(directives).every(\n    ({ directive, ifArgument }) => {\n      let evaledValue: boolean = false;\n      if (ifArgument.value.kind === \"Variable\") {\n        evaledValue =\n          variables && variables[(ifArgument.value as VariableNode).name.value];\n        invariant(\n          evaledValue !== void 0,\n          `Invalid variable referenced in @%s directive.`,\n          directive.name.value\n        );\n      } else {\n        evaledValue = (ifArgument.value as BooleanValueNode).value;\n      }\n      return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n    }\n  );\n}\n\nexport function getDirectiveNames(root: ASTNode) {\n  const names: string[] = [];\n\n  visit(root, {\n    Directive(node: DirectiveNode) {\n      names.push(node.name.value);\n    },\n  });\n\n  return names;\n}\n\nexport const hasAnyDirectives = (names: string[], root: ASTNode) =>\n  hasDirectives(names, root, false);\n\nexport const hasAllDirectives = (names: string[], root: ASTNode) =>\n  hasDirectives(names, root, true);\n\nexport function hasDirectives(names: string[], root: ASTNode, all?: boolean) {\n  const nameSet = new Set(names);\n  const uniqueCount = nameSet.size;\n\n  visit(root, {\n    Directive(node) {\n      if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n        return BREAK;\n      }\n    },\n  });\n\n  // If we found all the names, nameSet will be empty. If we only care about\n  // finding some of them, the < condition is sufficient.\n  return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\n\nexport function hasClientExports(document: DocumentNode) {\n  return document && hasDirectives([\"client\", \"export\"], document, true);\n}\n\nexport type InclusionDirectives = Array<{\n  directive: DirectiveNode;\n  ifArgument: ArgumentNode;\n}>;\n\nfunction isInclusionDirective({ name: { value } }: DirectiveNode): boolean {\n  return value === \"skip\" || value === \"include\";\n}\n\nexport function getInclusionDirectives(\n  directives: ReadonlyArray<DirectiveNode>\n): InclusionDirectives {\n  const result: InclusionDirectives = [];\n\n  if (directives && directives.length) {\n    directives.forEach((directive) => {\n      if (!isInclusionDirective(directive)) return;\n\n      const directiveArguments = directive.arguments;\n      const directiveName = directive.name.value;\n\n      invariant(\n        directiveArguments && directiveArguments.length === 1,\n        `Incorrect number of arguments for the @%s directive.`,\n        directiveName\n      );\n\n      const ifArgument = directiveArguments![0];\n      invariant(\n        ifArgument.name && ifArgument.name.value === \"if\",\n        `Invalid argument for the @%s directive.`,\n        directiveName\n      );\n\n      const ifValue: ValueNode = ifArgument.value;\n\n      // means it has to be a variable value if this is a valid @skip or @include directive\n      invariant(\n        ifValue &&\n          (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"),\n        `Argument for the @%s directive must be a variable or a boolean value.`,\n        directiveName\n      );\n\n      result.push({ directive, ifArgument });\n    });\n  }\n\n  return result;\n}\n", "// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\n\nexport class Trie<Data> {\n  // Since a `WeakMap` cannot hold primitive values as keys, we need a\n  // backup `Map` instance to hold primitive keys. Both `this._weakMap`\n  // and `this._strongMap` are lazily initialized.\n  private weak?: WeakMap<any, Trie<Data>>;\n  private strong?: Map<any, Trie<Data>>;\n  private data?: Data;\n\n  constructor(\n    private weakness = true,\n    private makeData: (array: any[]) => Data = defaultMakeData,\n  ) {}\n\n  public lookup<T extends any[]>(...array: T): Data;\n  public lookup(): Data {\n    return this.lookupArray(arguments);\n  }\n\n  public lookupArray<T extends IArguments | any[]>(array: T): Data {\n    let node: Trie<Data> = this;\n    forEach.call(array, key => node = node.getChildTrie(key));\n    return hasOwnProperty.call(node, \"data\")\n      ? node.data as Data\n      : node.data = this.makeData(slice.call(array));\n  }\n\n  public peek<T extends any[]>(...array: T): Data | undefined;\n  public peek(): Data | undefined {\n    return this.peekArray(arguments);\n  }\n\n  public peekArray<T extends IArguments | any[]>(array: T): Data | undefined {\n    let node: Trie<Data> | undefined = this;\n\n    for (let i = 0, len = array.length; node && i < len; ++i) {\n      const map = node.mapFor(array[i], false);\n      node = map && map.get(array[i]);\n    }\n\n    return node && node.data;\n  }\n\n  public remove(...array: any[]): Data | undefined;\n  public remove(): Data | undefined {\n    return this.removeArray(arguments);\n  }\n\n  public removeArray<T extends IArguments | any[]>(array: T): Data | undefined {\n    let data: Data | undefined;\n\n    if (array.length) {\n      const head = array[0];\n      const map = this.mapFor(head, false);\n      const child = map && map.get(head);\n      if (child) {\n        data = child.removeArray(slice.call(array, 1));\n        if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n          map.delete(head);\n        }\n      }\n    } else {\n      data = this.data;\n      delete this.data;\n    }\n\n    return data;\n  }\n\n  private getChildTrie(key: any) {\n    const map = this.mapFor(key, true)!;\n    let child = map.get(key);\n    if (!child) map.set(key, child = new Trie<Data>(this.weakness, this.makeData));\n    return child;\n  }\n\n  private mapFor(key: any, create: boolean): Trie<Data>[\"weak\" | \"strong\"] | undefined {\n    return this.weakness && isObjRef(key)\n      ? this.weak || (create ? this.weak = new WeakMap : void 0)\n      : this.strong || (create ? this.strong = new Map : void 0);\n  }\n}\n\nfunction isObjRef(value: any) {\n  switch (typeof value) {\n  case \"object\":\n    if (value === null) break;\n    // Fall through to return true...\n  case \"function\":\n    return true;\n  }\n  return false;\n}\n", "import { maybe } from \"../globals/index.js\";\n\nconst isReactNative = maybe(() => navigator.product) == \"ReactNative\";\n\nexport const canUseWeakMap =\n  typeof WeakMap === \"function\" &&\n  !(isReactNative && !(global as any).HermesInternal);\n\nexport const canUseWeakSet = typeof WeakSet === \"function\";\n\nexport const canUseSymbol =\n  typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\n\nexport const canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\n\nexport const canUseDOM =\n  typeof maybe(() => window.document.createElement) === \"function\";\n\nconst usingJSDOM: boolean =\n  // Following advice found in this comment from @domenic (maintainer of jsdom):\n  // https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n  //\n  // Since we control the version of Jest and jsdom used when running Apollo\n  // Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n  // at the end of the user agent string, I believe this case is all we need to\n  // check. Testing for \"Node.js\" was recommended for backwards compatibility\n  // with older version of jsdom, but we don't have that problem.\n  maybe(() => navigator.userAgent.indexOf(\"jsdom\") >= 0) || false;\n\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport const canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;\n", "export function isNonNullObject(obj: any): obj is Record<string | number, any> {\n  return obj !== null && typeof obj === \"object\";\n}\n\nexport function isPlainObject(obj: any): obj is Record<string | number, any> {\n  return (\n    obj !== null &&\n    typeof obj === \"object\" &&\n    (Object.getPrototypeOf(obj) === Object.prototype ||\n      Object.getPrototypeOf(obj) === null)\n  );\n}\n", "import { invariant, newInvariantError } from \"../globals/index.js\";\n\nimport type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  InlineFragmentNode,\n  SelectionNode,\n} from \"graphql\";\n\n// TODO(brian): A hack until this issue is resolved (https://github.com/graphql/graphql-js/issues/3356)\ntype Kind = any;\ntype OperationTypeNode = any;\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(\n  document: DocumentNode,\n  fragmentName?: string\n): DocumentNode {\n  let actualFragmentName = fragmentName;\n\n  // Build an array of all our fragment definitions that will be used for\n  // validations. We also do some validations on the other definitions in the\n  // document while building this list.\n  const fragments: Array<FragmentDefinitionNode> = [];\n  document.definitions.forEach((definition) => {\n    // Throw an error if we encounter an operation definition because we will\n    // define our own operation definition later on.\n    if (definition.kind === \"OperationDefinition\") {\n      throw newInvariantError(\n        `Found a %s operation%s. ` +\n          \"No operations are allowed when using a fragment as a query. Only fragments are allowed.\",\n        definition.operation,\n        definition.name ? ` named '${definition.name.value}'` : \"\"\n      );\n    }\n    // Add our definition to the fragments array if it is a fragment\n    // definition.\n    if (definition.kind === \"FragmentDefinition\") {\n      fragments.push(definition);\n    }\n  });\n\n  // If the user did not give us a fragment name then let us try to get a\n  // name from a single fragment in the definition.\n  if (typeof actualFragmentName === \"undefined\") {\n    invariant(\n      fragments.length === 1,\n      `Found %s fragments. \\`fragmentName\\` must be provided when there is not exactly 1 fragment.`,\n      fragments.length\n    );\n    actualFragmentName = fragments[0].name.value;\n  }\n\n  // Generate a query document with an operation that simply spreads the\n  // fragment inside of it.\n  const query: DocumentNode = {\n    ...document,\n    definitions: [\n      {\n        kind: \"OperationDefinition\" as Kind,\n        // OperationTypeNode is an enum\n        operation: \"query\" as OperationTypeNode,\n        selectionSet: {\n          kind: \"SelectionSet\" as Kind,\n          selections: [\n            {\n              kind: \"FragmentSpread\" as Kind,\n              name: {\n                kind: \"Name\" as Kind,\n                value: actualFragmentName,\n              },\n            },\n          ],\n        },\n      },\n      ...document.definitions,\n    ],\n  };\n\n  return query;\n}\n\n/**\n * This is an interface that describes a map from fragment names to fragment definitions.\n */\nexport interface FragmentMap {\n  [fragmentName: string]: FragmentDefinitionNode;\n}\n\nexport type FragmentMapFunction = (\n  fragmentName: string\n) => FragmentDefinitionNode | null;\n\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(\n  fragments: FragmentDefinitionNode[] = []\n): FragmentMap {\n  const symTable: FragmentMap = {};\n  fragments.forEach((fragment) => {\n    symTable[fragment.name.value] = fragment;\n  });\n  return symTable;\n}\n\nexport function getFragmentFromSelection(\n  selection: SelectionNode,\n  fragmentMap?: FragmentMap | FragmentMapFunction\n): InlineFragmentNode | FragmentDefinitionNode | null {\n  switch (selection.kind) {\n    case \"InlineFragment\":\n      return selection;\n    case \"FragmentSpread\": {\n      const fragmentName = selection.name.value;\n      if (typeof fragmentMap === \"function\") {\n        return fragmentMap(fragmentName);\n      }\n      const fragment = fragmentMap && fragmentMap[fragmentName];\n      invariant(fragment, `No fragment named %s`, fragmentName);\n      return fragment || null;\n    }\n    default:\n      return null;\n  }\n}\n", "import type { CommonCache } from \"./common\";\n\ninterface Node<K, V> {\n  key: K;\n  value: V;\n  newer: Node<K, V> | null;\n  older: Node<K, V> | null;\n}\n\nfunction defaultDispose() {}\n\nexport class StrongCache<K = any, V = any> implements CommonCache<K, V> {\n  private map = new Map<K, Node<K, V>>();\n  private newest: Node<K, V> | null = null;\n  private oldest: Node<K, V> | null = null;\n\n  constructor(\n    private max = Infinity,\n    public dispose: (value: V, key: K) => void = defaultDispose,\n  ) {}\n\n  public has(key: K): boolean {\n    return this.map.has(key);\n  }\n\n  public get(key: K): V | undefined {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n\n  public get size() {\n    return this.map.size;\n  }\n\n  private getNode(key: K): Node<K, V> | undefined {\n    const node = this.map.get(key);\n\n    if (node && node !== this.newest) {\n      const { older, newer } = node;\n\n      if (newer) {\n        newer.older = older;\n      }\n\n      if (older) {\n        older.newer = newer;\n      }\n\n      node.older = this.newest;\n      node.older!.newer = node;\n\n      node.newer = null;\n      this.newest = node;\n\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n\n    return node;\n  }\n\n  public set(key: K, value: V): V {\n    let node = this.getNode(key);\n    if (node) {\n      return node.value = value;\n    }\n\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest\n    };\n\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n\n    this.newest = node;\n    this.oldest = this.oldest || node;\n\n    this.map.set(key, node);\n\n    return node.value;\n  }\n\n  public clean() {\n    while (this.oldest && this.map.size > this.max) {\n      this.delete(this.oldest.key);\n    }\n  }\n\n  public delete(key: K): boolean {\n    const node = this.map.get(key);\n    if (node) {\n      if (node === this.newest) {\n        this.newest = node.older;\n      }\n\n      if (node === this.oldest) {\n        this.oldest = node.newer;\n      }\n\n      if (node.newer) {\n        node.newer.older = node.older;\n      }\n\n      if (node.older) {\n        node.older.newer = node.newer;\n      }\n\n      this.map.delete(key);\n      this.dispose(node.value, key);\n\n      return true;\n    }\n\n    return false;\n  }\n}\n", "import type { CommonCache } from \"./common\";\n\ninterface PartialNode<K extends object, V> {\n  value: V;\n  newer: Node<K, V> | null;\n  older: Node<K, V> | null;\n}\n\ninterface UnfinalizedNode<K extends object, V> extends PartialNode<K, V> {\n  keyRef?: undefined;\n  key: K;\n}\n\ninterface FullNode<K extends object, V> extends PartialNode<K, V> {\n  keyRef: WeakRef<K>;\n  key?: undefined;\n}\n\ntype Node<K extends object, V> = FullNode<K, V> | UnfinalizedNode<K, V>;\n\nfunction noop() {}\nconst defaultDispose = noop;\n\nconst _WeakRef =\n  typeof WeakRef !== \"undefined\"\n    ? WeakRef\n    : (function <T>(value: T) {\n        return { deref: () => value } satisfies Omit<\n          WeakRef<any>,\n          typeof Symbol.toStringTag\n        >;\n      } as any as typeof WeakRef);\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry =\n  typeof FinalizationRegistry !== \"undefined\"\n    ? FinalizationRegistry\n    : (function <T>() {\n        return {\n          register: noop,\n          unregister: noop,\n        } satisfies Omit<FinalizationRegistry<T>, typeof Symbol.toStringTag>;\n      } as any as typeof FinalizationRegistry);\n\nconst finalizationBatchSize = 10024;\n\nexport class WeakCache<K extends object = any, V = any>\n  implements CommonCache<K, V>\n{\n  private map = new _WeakMap<K, Node<K, V>>();\n  private registry: FinalizationRegistry<Node<K, V>>;\n  private newest: Node<K, V> | null = null;\n  private oldest: Node<K, V> | null = null;\n  private unfinalizedNodes: Set<UnfinalizedNode<K, V>> = new Set();\n  private finalizationScheduled = false;\n  public size = 0;\n\n  constructor(\n    private max = Infinity,\n    public dispose: (value: V, key?: K) => void = defaultDispose\n  ) {\n    this.registry = new _FinalizationRegistry<Node<K, V>>(\n      this.deleteNode.bind(this)\n    );\n  }\n\n  public has(key: K): boolean {\n    return this.map.has(key);\n  }\n\n  public get(key: K): V | undefined {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n\n  private getNode(key: K): Node<K, V> | undefined {\n    const node = this.map.get(key);\n\n    if (node && node !== this.newest) {\n      const { older, newer } = node;\n\n      if (newer) {\n        newer.older = older;\n      }\n\n      if (older) {\n        older.newer = newer;\n      }\n\n      node.older = this.newest;\n      node.older!.newer = node;\n\n      node.newer = null;\n      this.newest = node;\n\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n\n    return node;\n  }\n\n  public set(key: K, value: V): V {\n    let node = this.getNode(key);\n    if (node) {\n      return (node.value = value);\n    }\n\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest,\n    };\n\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n\n    this.newest = node;\n    this.oldest = this.oldest || node;\n\n    this.scheduleFinalization(node);\n    this.map.set(key, node);\n    this.size++;\n\n    return node.value;\n  }\n\n  public clean() {\n    while (this.oldest && this.size > this.max) {\n      this.deleteNode(this.oldest);\n    }\n  }\n\n  private deleteNode(node: Node<K, V>) {\n    if (node === this.newest) {\n      this.newest = node.older;\n    }\n\n    if (node === this.oldest) {\n      this.oldest = node.newer;\n    }\n\n    if (node.newer) {\n      node.newer.older = node.older;\n    }\n\n    if (node.older) {\n      node.older.newer = node.newer;\n    }\n\n    this.size--;\n    const key = node.key || (node.keyRef && node.keyRef.deref());\n    this.dispose(node.value, key);\n    if (!node.keyRef) {\n      this.unfinalizedNodes.delete(node);\n    } else {\n      this.registry.unregister(node);\n    }\n    if (key) this.map.delete(key);\n  }\n\n  public delete(key: K): boolean {\n    const node = this.map.get(key);\n    if (node) {\n      this.deleteNode(node);\n\n      return true;\n    }\n\n    return false;\n  }\n\n  private scheduleFinalization(node: UnfinalizedNode<K, V>) {\n    this.unfinalizedNodes.add(node);\n    if (!this.finalizationScheduled) {\n      this.finalizationScheduled = true;\n      queueMicrotask(this.finalize);\n    }\n  }\n\n  private finalize = () => {\n    const iterator = this.unfinalizedNodes.values();\n    for (let i = 0; i < finalizationBatchSize; i++) {\n      const node = iterator.next().value;\n      if (!node) break;\n      this.unfinalizedNodes.delete(node);\n      const key = node.key;\n      delete (node as unknown as FullNode<K, V>).key;\n      (node as unknown as FullNode<K, V>).keyRef = new _WeakRef(key);\n      this.registry.register(key, node, node);\n    }\n    if (this.unfinalizedNodes.size > 0) {\n      queueMicrotask(this.finalize);\n    } else {\n      this.finalizationScheduled = false;\n    }\n  };\n}\n", "export type { CommonCache } from \"./common.js\";\nexport { StrongCache } from \"./strong.js\";\nexport { WeakCache } from \"./weak.js\";\n", "import { WeakCache, StrongCache } from \"@wry/caches\";\n\ninterface CleanableCache {\n  size: number;\n  max?: number;\n  clean: () => void;\n}\nconst scheduledCleanup = new WeakSet<CleanableCache>();\nfunction schedule(cache: CleanableCache) {\n  if (cache.size <= (cache.max || -1)) {\n    return;\n  }\n  if (!scheduledCleanup.has(cache)) {\n    scheduledCleanup.add(cache);\n    setTimeout(() => {\n      cache.clean();\n      scheduledCleanup.delete(cache);\n    }, 100);\n  }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport const AutoCleanedWeakCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `WeakCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new WeakCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = WeakCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof WeakCache;\n/**\n * @internal\n */\nexport type AutoCleanedWeakCache<K extends object, V> = WeakCache<K, V>;\n\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport const AutoCleanedStrongCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `StrongCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new StrongCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = StrongCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof StrongCache;\n/**\n * @internal\n */\nexport type AutoCleanedStrongCache<K, V> = StrongCache<K, V>;\n", "import { global } from \"../globals/index.js\";\n\ndeclare global {\n  interface Window {\n    [cacheSizeSymbol]?: Partial<CacheSizes>;\n  }\n}\n\n/**\n * The cache sizes used by various Apollo Client caches.\n *\n * @remarks\n * All configurable caches hold memoized values. If an item is\n * cache-collected, it incurs only a small performance impact and\n * doesn't cause data loss. A smaller cache size might save you memory.\n *\n * You should choose cache sizes appropriate for storing a reasonable\n * number of values rather than every value. To prevent too much recalculation,\n * choose cache sizes that are at least large enough to hold memoized values for\n * all hooks/queries on the screen at any given time.\n */\n/*\n * We assume a \"base value\" of 1000 here, which is already very generous.\n * In most applications, it will be very unlikely that 1000 different queries\n * are on screen at the same time.\n */\nexport interface CacheSizes {\n  /**\n   * Cache size for the [`print`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/print.ts) function.\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * This method is called to transform a GraphQL query AST parsed by `gql`\n   * back into a GraphQL string.\n   *\n   * @privateRemarks\n   * This method is called from the `QueryManager` and various `ApolloLink`s,\n   * always with the \"serverQuery\", so the server-facing part of a transformed\n   * `DocumentNode`.\n   */\n  print: number;\n  /**\n   * Cache size for the [`parser`](https://github.com/apollographql/apollo-client/blob/main/src/react/parser/index.ts) function.\n   *\n   * It is called with user-provided `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This method is called by HOCs and hooks.\n   *\n   * @privateRemarks\n   * This function is used directly in HOCs, and nowadays mainly accessed by\n   * calling `verifyDocumentType` from various hooks.\n   * It is called with a user-provided DocumentNode.\n   */\n  parser: number;\n  /**\n   * Cache size for the cache of [`DocumentTransform`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/DocumentTransform.ts)\n   * instances with the `cache` option set to `true`.\n   *\n   * Can be called with user-defined or already-transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * The cache size here should be chosen with other `DocumentTransform`s in mind.\n   * For example, if there was a `DocumentTransform` that would take `x` `DocumentNode`s,\n   * and returned a differently-transformed `DocumentNode` depending if the app is\n   * online or offline, then we assume that the cache returns `2*x` documents.\n   * If that were concatenated with another `DocumentTransform` that would\n   * also duplicate the cache size, you'd need to account for `4*x` documents\n   * returned by the second transform.\n   *\n   * Due to an implementation detail of Apollo Client, if you use custom document\n   * transforms you should always add `n` (the \"base\" number of user-provided\n   * Documents) to the resulting cache size.\n   *\n   * If we assume that the user-provided transforms receive `n` documents and\n   * return `n` documents, the cache size should be `2*n`.\n   *\n   * If we assume that the chain of user-provided transforms receive `n` documents and\n   * return `4*n` documents, the cache size should be `5*n`.\n   *\n   * This size should also then be used in every other cache that mentions that\n   * it operates on a \"transformed\" `DocumentNode`.\n   *\n   * @privateRemarks\n   * Cache size for the `performWork` method of each [`DocumentTransform`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/DocumentTransform.ts).\n   *\n   * No user-provided DocumentNode will actually be \"the last one\", as we run the\n   * `defaultDocumentTransform` before *and* after the user-provided transforms.\n   * For that reason, we need the extra `n` here - `n` for \"before transformation\"\n   * plus the actual maximum cache size of the user-provided transform chain.\n   *\n   * This method is called from `transformDocument`, which is called from\n   * `QueryManager` with a user-provided DocumentNode.\n   * It is also called with already-transformed DocumentNodes, assuming the\n   * user provided additional transforms.\n   *\n   */\n  \"documentTransform.cache\": number;\n  /**\n   * A cache inside of [`QueryManager`](https://github.com/apollographql/apollo-client/blob/main/src/core/QueryManager.ts).\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   * Cache size for the `transformCache` used in the `getDocumentInfo` method of `QueryManager`.\n   * Called throughout the `QueryManager` with transformed DocumentNodes.\n   */\n  \"queryManager.getDocumentInfo\": number;\n  /**\n   * A cache inside of [`PersistedQueryLink`](https://github.com/apollographql/apollo-client/blob/main/src/link/persisted-queries/index.ts).\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * This cache is used to cache the hashes of persisted queries.\n   *\n   * @privateRemarks\n   * Cache size for the `hashesByQuery` cache in the `PersistedQueryLink`.\n   */\n  \"PersistedQueryLink.persistedQueryHashes\": number;\n  /**\n   * Cache used by [`canonicalStringify`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/common/canonicalStringify.ts).\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This cache contains the sorted keys of objects that are stringified by\n   * `canonicalStringify`.\n   * It uses the stringified unsorted keys of objects as keys.\n   * The cache will not grow beyond the size of different object **shapes**\n   * encountered in an application, no matter how much actual data gets stringified.\n   *\n   * @privateRemarks\n   * Cache size for the `sortingMap` in `canonicalStringify`.\n   */\n  canonicalStringify: number;\n  /**\n   * A cache inside of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * Can be called with user-defined or already-transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   *\n   * Cache size for the `transform` method of FragmentRegistry.\n   * This function is called as part of the `defaultDocumentTransform` which will be called with\n   * user-provided and already-transformed DocumentNodes.\n   *\n   */\n  \"fragmentRegistry.transform\": number;\n  /**\n   * A cache inside of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * This function is called with fragment names in the form of a string.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * The size of this case should be chosen with the number of fragments in\n   * your application in mind.\n   *\n   * Note:\n   * This function is a dependency of `fragmentRegistry.transform`, so having too small of a cache size here\n   * might involuntarily invalidate values in the `transform` cache.\n   *\n   * @privateRemarks\n   * Cache size for the `lookup` method of FragmentRegistry.\n   */\n  \"fragmentRegistry.lookup\": number;\n  /**\n   * Cache size for the `findFragmentSpreads` method of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * This function is called with transformed `DocumentNode`s, as well as recursively\n   * with every fragment spread referenced within that, or a fragment referenced by a\n   * fragment spread.\n   *\n   * @defaultValue\n   * Defaults to `4000`.\n   *\n   * @remarks\n   *\n   * Note: This function is a dependency of `fragmentRegistry.transform`, so having too small of cache size here\n   * might involuntarily invalidate values in the `transform` cache.\n   */\n  \"fragmentRegistry.findFragmentSpreads\": number;\n  /**\n   * Cache size for the `getFragmentDoc` method of [`ApolloCache`](https://github.com/apollographql/apollo-client/blob/main/src/cache/core/cache.ts).\n   *\n   * This function is called with user-provided fragment definitions.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This function is called from `readFragment` with user-provided fragment definitions.\n   */\n  \"cache.fragmentQueryDocuments\": number;\n  /**\n   * Cache used in [`removeTypenameFromVariables`](https://github.com/apollographql/apollo-client/blob/main/src/link/remove-typename/removeTypenameFromVariables.ts).\n   *\n   * This function is called transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   * Cache size for the `getVariableDefinitions` function of `removeTypenameFromVariables`.\n   */\n  \"removeTypenameFromVariables.getVariableDefinitions\": number;\n  /**\n   * Cache size for the `maybeBroadcastWatch` method on [`InMemoryCache`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/inMemoryCache.ts).\n   *\n   * Note: `maybeBroadcastWatch` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `5000`.\n   *\n   * @remarks\n   * This method is used for dependency tracking in the `InMemoryCache` and\n   * prevents from unnecessary re-renders.\n   * It is recommended to keep this value significantly higher than the number of\n   * possible subscribers you will have active at the same time in your application\n   * at any time.\n   */\n  \"inMemoryCache.maybeBroadcastWatch\": number;\n  /**\n   * Cache size for the `executeSelectionSet` method on [`StoreReader`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/readFromStore.ts).\n   *\n   * Note:\n   * `executeSelectionSet` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `10000`.\n   *\n   * @remarks\n   * Every object that is read from the cache will be cached here, so it is\n   * recommended to set this to a high value.\n   */\n  \"inMemoryCache.executeSelectionSet\": number;\n  /**\n   * Cache size for the `executeSubSelectedArray` method on [`StoreReader`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/readFromStore.ts).\n   *\n   * Note:\n   * `executeSubSelectedArray` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `5000`.\n   *\n   * @remarks\n   * Every array that is read from the cache will be cached here, so it is\n   * recommended to set this to a high value.\n   */\n  \"inMemoryCache.executeSubSelectedArray\": number;\n}\n\nconst cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport const cacheSizes: Partial<CacheSizes> = { ...global[cacheSizeSymbol] };\n\nexport const enum defaultCacheSizes {\n  parser = 1000,\n  canonicalStringify = 1000,\n  print = 2000,\n  \"documentTransform.cache\" = 2000,\n  \"queryManager.getDocumentInfo\" = 2000,\n  \"PersistedQueryLink.persistedQueryHashes\" = 2000,\n  \"fragmentRegistry.transform\" = 2000,\n  \"fragmentRegistry.lookup\" = 1000,\n  \"fragmentRegistry.findFragmentSpreads\" = 4000,\n  \"cache.fragmentQueryDocuments\" = 1000,\n  \"removeTypenameFromVariables.getVariableDefinitions\" = 2000,\n  \"inMemoryCache.maybeBroadcastWatch\" = 5000,\n  \"inMemoryCache.executeSelectionSet\" = 50000,\n  \"inMemoryCache.executeSubSelectedArray\" = 10000,\n}\n", "export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport type { CacheSizes } from \"./sizes.js\";\nexport { cacheSizes, defaultCacheSizes } from \"./sizes.js\";\n", "import type { OptimisticWrapperFunction } from \"optimism\";\nimport type {\n  InMemoryCache,\n  DocumentTransform,\n  ApolloLink,\n  ApolloCache,\n} from \"../../core/index.js\";\nimport type { ApolloClient } from \"../../core/index.js\";\nimport type { CacheSizes } from \"./sizes.js\";\nimport { cacheSizes, defaultCacheSizes } from \"./sizes.js\";\n\nconst globalCaches: {\n  print?: () => number;\n  parser?: () => number;\n  canonicalStringify?: () => number;\n} = {};\n\nexport function registerGlobalCache(\n  name: keyof typeof globalCaches,\n  getSize: () => number\n) {\n  globalCaches[name] = getSize;\n}\n\n/**\n * Transformative helper type to turn a function of the form\n * ```ts\n * (this: any) => R\n * ```\n * into a function of the form\n * ```ts\n * () => R\n * ```\n * preserving the return type, but removing the `this` parameter.\n *\n * @remarks\n *\n * Further down in the definitions of `_getApolloClientMemoryInternals`,\n * `_getApolloCacheMemoryInternals` and `_getInMemoryCacheMemoryInternals`,\n * having the `this` parameter annotation is extremely useful for type checking\n * inside the function.\n *\n * If this is preserved in the exported types, though, it leads to a situation\n * where `ApolloCache.getMemoryInternals` is a function that requires a `this`\n * of the type `ApolloCache`, while the extending class `InMemoryCache` has a\n * `getMemoryInternals` function that requires a `this` of the type\n * `InMemoryCache`.\n * This is not compatible with TypeScript's inheritence system (although it is\n * perfectly correct), and so TypeScript will complain loudly.\n *\n * We still want to define our functions with the `this` annotation, though,\n * and have the return type inferred.\n * (This requirement for return type inference here makes it impossible to use\n * a function overload that is more explicit on the inner overload than it is\n * on the external overload.)\n *\n * So in the end, we use this helper to remove the `this` annotation from the\n * exported function types, while keeping it in the internal implementation.\n *\n */\ntype RemoveThis<T> = T extends (this: any) => infer R ? () => R : never;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloClientMemoryInternals =\n  __DEV__ ?\n    (_getApolloClientMemoryInternals as RemoveThis<\n      typeof _getApolloClientMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getInMemoryCacheMemoryInternals =\n  __DEV__ ?\n    (_getInMemoryCacheMemoryInternals as RemoveThis<\n      typeof _getInMemoryCacheMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloCacheMemoryInternals =\n  __DEV__ ?\n    (_getApolloCacheMemoryInternals as RemoveThis<\n      typeof _getApolloCacheMemoryInternals\n    >)\n  : undefined;\n\nfunction getCurrentCacheSizes() {\n  // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n  const defaults: Record<keyof CacheSizes, number> = {\n    parser: defaultCacheSizes[\"parser\"],\n    canonicalStringify: defaultCacheSizes[\"canonicalStringify\"],\n    print: defaultCacheSizes[\"print\"],\n    \"documentTransform.cache\": defaultCacheSizes[\"documentTransform.cache\"],\n    \"queryManager.getDocumentInfo\":\n      defaultCacheSizes[\"queryManager.getDocumentInfo\"],\n    \"PersistedQueryLink.persistedQueryHashes\":\n      defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"],\n    \"fragmentRegistry.transform\":\n      defaultCacheSizes[\"fragmentRegistry.transform\"],\n    \"fragmentRegistry.lookup\": defaultCacheSizes[\"fragmentRegistry.lookup\"],\n    \"fragmentRegistry.findFragmentSpreads\":\n      defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"],\n    \"cache.fragmentQueryDocuments\":\n      defaultCacheSizes[\"cache.fragmentQueryDocuments\"],\n    \"removeTypenameFromVariables.getVariableDefinitions\":\n      defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"],\n    \"inMemoryCache.maybeBroadcastWatch\":\n      defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"],\n    \"inMemoryCache.executeSelectionSet\":\n      defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"],\n    \"inMemoryCache.executeSubSelectedArray\":\n      defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"],\n  };\n  return Object.fromEntries(\n    Object.entries(defaults).map(([k, v]) => [\n      k,\n      cacheSizes[k as keyof CacheSizes] || v,\n    ])\n  );\n}\n\nfunction _getApolloClientMemoryInternals(this: ApolloClient<any>) {\n  if (!__DEV__) throw new Error(\"only supported in development mode\");\n\n  return {\n    limits: getCurrentCacheSizes(),\n    sizes: {\n      print: globalCaches.print?.(),\n      parser: globalCaches.parser?.(),\n      canonicalStringify: globalCaches.canonicalStringify?.(),\n      links: linkInfo(this.link),\n      queryManager: {\n        getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n        documentTransforms: transformInfo(\n          this[\"queryManager\"].documentTransform\n        ),\n      },\n      ...(this.cache.getMemoryInternals?.() as Partial<\n        ReturnType<typeof _getApolloCacheMemoryInternals>\n      > &\n        Partial<ReturnType<typeof _getInMemoryCacheMemoryInternals>>),\n    },\n  };\n}\n\nfunction _getApolloCacheMemoryInternals(this: ApolloCache<any>) {\n  return {\n    cache: {\n      fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n    },\n  };\n}\n\nfunction _getInMemoryCacheMemoryInternals(this: InMemoryCache) {\n  const fragments = this.config.fragments as\n    | undefined\n    | {\n        findFragmentSpreads?: Function;\n        transform?: Function;\n        lookup?: Function;\n      };\n\n  return {\n    ..._getApolloCacheMemoryInternals.apply(this as any),\n    addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]),\n    inMemoryCache: {\n      executeSelectionSet: getWrapperInformation(\n        this[\"storeReader\"][\"executeSelectionSet\"]\n      ),\n      executeSubSelectedArray: getWrapperInformation(\n        this[\"storeReader\"][\"executeSubSelectedArray\"]\n      ),\n      maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n    },\n    fragmentRegistry: {\n      findFragmentSpreads: getWrapperInformation(\n        fragments?.findFragmentSpreads\n      ),\n      lookup: getWrapperInformation(fragments?.lookup),\n      transform: getWrapperInformation(fragments?.transform),\n    },\n  };\n}\n\nfunction isWrapper(f?: Function): f is OptimisticWrapperFunction<any, any> {\n  return !!f && \"dirtyKey\" in f;\n}\n\nfunction getWrapperInformation(f?: Function) {\n  return isWrapper(f) ? f.size : undefined;\n}\n\nfunction isDefined<T>(value: T | undefined | null): value is T {\n  return value != null;\n}\n\nfunction transformInfo(transform?: DocumentTransform) {\n  return recurseTransformInfo(transform).map((cache) => ({ cache }));\n}\n\nfunction recurseTransformInfo(transform?: DocumentTransform): number[] {\n  return transform ?\n      [\n        getWrapperInformation(transform?.[\"performWork\"]),\n        ...recurseTransformInfo(transform?.[\"left\"]),\n        ...recurseTransformInfo(transform?.[\"right\"]),\n      ].filter(isDefined)\n    : [];\n}\n\nfunction linkInfo(link?: ApolloLink): unknown[] {\n  return link ?\n      [\n        link?.getMemoryInternals?.(),\n        ...linkInfo(link?.left),\n        ...linkInfo(link?.right),\n      ].filter(isDefined)\n    : [];\n}\n", "import {\n  AutoCleanedStrongCache,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../../utilities/caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n\n/**\n * Like JSON.stringify, but with object keys always sorted in the same order.\n *\n * To achieve performant sorting, this function uses a Map from JSON-serialized\n * arrays of keys (in any order) to sorted arrays of the same keys, with a\n * single sorted array reference shared by all permutations of the keys.\n *\n * As a drawback, this function will add a little bit more memory for every\n * object encountered that has different (more, less, a different order of) keys\n * than in the past.\n *\n * In a typical application, this extra memory usage should not play a\n * significant role, as `canonicalStringify` will be called for only a limited\n * number of object shapes, and the cache will not grow beyond a certain point.\n * But in some edge cases, this could be a problem, so we provide\n * canonicalStringify.reset() as a way of clearing the cache.\n * */\nexport const canonicalStringify = Object.assign(\n  function canonicalStringify(value: any): string {\n    return JSON.stringify(value, stableObjectReplacer);\n  },\n  {\n    reset() {\n      // Clearing the sortingMap will reclaim all cached memory, without\n      // affecting the logical results of canonicalStringify, but potentially\n      // sacrificing performance until the cache is refilled.\n      sortingMap = new AutoCleanedStrongCache<string, readonly string[]>(\n        cacheSizes.canonicalStringify || defaultCacheSizes.canonicalStringify\n      );\n    },\n  }\n);\n\nif (__DEV__) {\n  registerGlobalCache(\"canonicalStringify\", () => sortingMap.size);\n}\n\n// Values are JSON-serialized arrays of object keys (in any order), and values\n// are sorted arrays of the same keys.\nlet sortingMap!: AutoCleanedStrongCache<string, readonly string[]>;\ncanonicalStringify.reset();\n\n// The JSON.stringify function takes an optional second argument called a\n// replacer function. This function is called for each key-value pair in the\n// object being stringified, and its return value is used instead of the\n// original value. If the replacer function returns a new value, that value is\n// stringified as JSON instead of the original value of the property.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#the_replacer_parameter\nfunction stableObjectReplacer(key: string, value: any) {\n  if (value && typeof value === \"object\") {\n    const proto = Object.getPrototypeOf(value);\n    // We don't want to mess with objects that are not \"plain\" objects, which\n    // means their prototype is either Object.prototype or null. This check also\n    // prevents needlessly rearranging the indices of arrays.\n    if (proto === Object.prototype || proto === null) {\n      const keys = Object.keys(value);\n      // If keys is already sorted, let JSON.stringify serialize the original\n      // value instead of creating a new object with keys in the same order.\n      if (keys.every(everyKeyInOrder)) return value;\n      const unsortedKey = JSON.stringify(keys);\n      let sortedKeys = sortingMap.get(unsortedKey);\n      if (!sortedKeys) {\n        keys.sort();\n        const sortedKey = JSON.stringify(keys);\n        // Checking for sortedKey in the sortingMap allows us to share the same\n        // sorted array reference for all permutations of the same set of keys.\n        sortedKeys = sortingMap.get(sortedKey) || keys;\n        sortingMap.set(unsortedKey, sortedKeys);\n        sortingMap.set(sortedKey, sortedKeys);\n      }\n      const sortedObject = Object.create(proto);\n      // Reassigning the keys in sorted order will cause JSON.stringify to\n      // serialize them in sorted order.\n      sortedKeys.forEach((key) => {\n        sortedObject[key] = value[key];\n      });\n      return sortedObject;\n    }\n  }\n  return value;\n}\n\n// Since everything that happens in stableObjectReplacer benefits from being as\n// efficient as possible, we use a static function as the callback for\n// keys.every in order to test if the provided keys are already sorted without\n// allocating extra memory for a callback.\nfunction everyKeyInOrder(\n  key: string,\n  i: number,\n  keys: readonly string[]\n): boolean {\n  return i === 0 || keys[i - 1] <= key;\n}\n", "import { newInvariantError } from \"../globals/index.js\";\n\nimport type {\n  DirectiveNode,\n  FieldNode,\n  IntValueNode,\n  FloatValueNode,\n  StringValueNode,\n  BooleanValueNode,\n  ObjectValueNode,\n  ListValueNode,\n  EnumValueNode,\n  NullValueNode,\n  VariableNode,\n  InlineFragmentNode,\n  ValueNode,\n  SelectionNode,\n  NameNode,\n  SelectionSetNode,\n  DocumentNode,\n  FragmentSpreadNode,\n} from \"graphql\";\n\nimport { isNonNullObject } from \"../common/objects.js\";\nimport type { FragmentMap } from \"./fragments.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\n\nexport interface Reference {\n  readonly __ref: string;\n}\n\nexport function makeReference(id: string): Reference {\n  return { __ref: String(id) };\n}\n\nexport function isReference(obj: any): obj is Reference {\n  return Boolean(\n    obj && typeof obj === \"object\" && typeof obj.__ref === \"string\"\n  );\n}\n\nexport type StoreValue =\n  | number\n  | string\n  | string[]\n  | Reference\n  | Reference[]\n  | null\n  | undefined\n  | void\n  | Object;\n\nexport interface StoreObject {\n  __typename?: string;\n  [storeFieldName: string]: StoreValue;\n}\n\n/**\n * Workaround for a TypeScript quirk:\n * types per default have an implicit index signature that makes them\n * assignable to `StoreObject`.\n * interfaces do not have that implicit index signature, so they cannot\n * be assigned to `StoreObject`.\n * This type just maps over a type or interface that is passed in,\n * implicitly adding the index signature.\n * That way, the result can be assigned to `StoreObject`.\n *\n * This is important if some user-defined interface is used e.g.\n * in cache.modify, where the `toReference` method expects a\n * `StoreObject` as input.\n */\nexport type AsStoreObject<T extends { __typename?: string }> = {\n  [K in keyof T]: T[K];\n};\n\nexport function isDocumentNode(value: any): value is DocumentNode {\n  return (\n    isNonNullObject(value) &&\n    (value as DocumentNode).kind === \"Document\" &&\n    Array.isArray((value as DocumentNode).definitions)\n  );\n}\n\nfunction isStringValue(value: ValueNode): value is StringValueNode {\n  return value.kind === \"StringValue\";\n}\n\nfunction isBooleanValue(value: ValueNode): value is BooleanValueNode {\n  return value.kind === \"BooleanValue\";\n}\n\nfunction isIntValue(value: ValueNode): value is IntValueNode {\n  return value.kind === \"IntValue\";\n}\n\nfunction isFloatValue(value: ValueNode): value is FloatValueNode {\n  return value.kind === \"FloatValue\";\n}\n\nfunction isVariable(value: ValueNode): value is VariableNode {\n  return value.kind === \"Variable\";\n}\n\nfunction isObjectValue(value: ValueNode): value is ObjectValueNode {\n  return value.kind === \"ObjectValue\";\n}\n\nfunction isListValue(value: ValueNode): value is ListValueNode {\n  return value.kind === \"ListValue\";\n}\n\nfunction isEnumValue(value: ValueNode): value is EnumValueNode {\n  return value.kind === \"EnumValue\";\n}\n\nfunction isNullValue(value: ValueNode): value is NullValueNode {\n  return value.kind === \"NullValue\";\n}\n\nexport function valueToObjectRepresentation(\n  argObj: any,\n  name: NameNode,\n  value: ValueNode,\n  variables?: Object\n) {\n  if (isIntValue(value) || isFloatValue(value)) {\n    argObj[name.value] = Number(value.value);\n  } else if (isBooleanValue(value) || isStringValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isObjectValue(value)) {\n    const nestedArgObj = {};\n    value.fields.map((obj) =>\n      valueToObjectRepresentation(nestedArgObj, obj.name, obj.value, variables)\n    );\n    argObj[name.value] = nestedArgObj;\n  } else if (isVariable(value)) {\n    const variableValue = (variables || ({} as any))[value.name.value];\n    argObj[name.value] = variableValue;\n  } else if (isListValue(value)) {\n    argObj[name.value] = value.values.map((listValue) => {\n      const nestedArgArrayObj = {};\n      valueToObjectRepresentation(\n        nestedArgArrayObj,\n        name,\n        listValue,\n        variables\n      );\n      return (nestedArgArrayObj as any)[name.value];\n    });\n  } else if (isEnumValue(value)) {\n    argObj[name.value] = (value as EnumValueNode).value;\n  } else if (isNullValue(value)) {\n    argObj[name.value] = null;\n  } else {\n    throw newInvariantError(\n      `The inline argument \"%s\" of kind \"%s\"` +\n        \"is not supported. Use variables instead of inline arguments to \" +\n        \"overcome this limitation.\",\n      name.value,\n      (value as any).kind\n    );\n  }\n}\n\nexport function storeKeyNameFromField(\n  field: FieldNode,\n  variables?: Object\n): string {\n  let directivesObj: any = null;\n  if (field.directives) {\n    directivesObj = {};\n    field.directives.forEach((directive) => {\n      directivesObj[directive.name.value] = {};\n\n      if (directive.arguments) {\n        directive.arguments.forEach(({ name, value }) =>\n          valueToObjectRepresentation(\n            directivesObj[directive.name.value],\n            name,\n            value,\n            variables\n          )\n        );\n      }\n    });\n  }\n\n  let argObj: any = null;\n  if (field.arguments && field.arguments.length) {\n    argObj = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n  }\n\n  return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\n\nexport type Directives = {\n  [directiveName: string]: {\n    [argName: string]: any;\n  };\n};\n\nconst KNOWN_DIRECTIVES: string[] = [\n  \"connection\",\n  \"include\",\n  \"skip\",\n  \"client\",\n  \"rest\",\n  \"export\",\n  \"nonreactive\",\n];\n\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nlet storeKeyNameStringify: (value: any) => string = canonicalStringify;\n\nexport const getStoreKeyName = Object.assign(\n  function (\n    fieldName: string,\n    args?: Record<string, any> | null,\n    directives?: Directives\n  ): string {\n    if (\n      args &&\n      directives &&\n      directives[\"connection\"] &&\n      directives[\"connection\"][\"key\"]\n    ) {\n      if (\n        directives[\"connection\"][\"filter\"] &&\n        (directives[\"connection\"][\"filter\"] as string[]).length > 0\n      ) {\n        const filterKeys =\n          directives[\"connection\"][\"filter\"] ?\n            (directives[\"connection\"][\"filter\"] as string[])\n          : [];\n        filterKeys.sort();\n\n        const filteredArgs = {} as { [key: string]: any };\n        filterKeys.forEach((key) => {\n          filteredArgs[key] = args[key];\n        });\n\n        return `${directives[\"connection\"][\"key\"]}(${storeKeyNameStringify(\n          filteredArgs\n        )})`;\n      } else {\n        return directives[\"connection\"][\"key\"];\n      }\n    }\n\n    let completeFieldName: string = fieldName;\n\n    if (args) {\n      // We can't use `JSON.stringify` here since it's non-deterministic,\n      // and can lead to different store key names being created even though\n      // the `args` object used during creation has the same properties/values.\n      const stringifiedArgs: string = storeKeyNameStringify(args);\n      completeFieldName += `(${stringifiedArgs})`;\n    }\n\n    if (directives) {\n      Object.keys(directives).forEach((key) => {\n        if (KNOWN_DIRECTIVES.indexOf(key) !== -1) return;\n        if (directives[key] && Object.keys(directives[key]).length) {\n          completeFieldName += `@${key}(${storeKeyNameStringify(\n            directives[key]\n          )})`;\n        } else {\n          completeFieldName += `@${key}`;\n        }\n      });\n    }\n\n    return completeFieldName;\n  },\n  {\n    setStringify(s: typeof storeKeyNameStringify) {\n      const previous = storeKeyNameStringify;\n      storeKeyNameStringify = s;\n      return previous;\n    },\n  }\n);\n\nexport function argumentsObjectFromField(\n  field: FieldNode | DirectiveNode,\n  variables?: Record<string, any>\n): Object | null {\n  if (field.arguments && field.arguments.length) {\n    const argObj: Object = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n    return argObj;\n  }\n  return null;\n}\n\nexport function resultKeyNameFromField(field: FieldNode): string {\n  return field.alias ? field.alias.value : field.name.value;\n}\n\nexport function getTypenameFromResult(\n  result: Record<string, any>,\n  selectionSet: SelectionSetNode,\n  fragmentMap?: FragmentMap\n): string | undefined {\n  let fragments: undefined | Array<InlineFragmentNode | FragmentSpreadNode>;\n  for (const selection of selectionSet.selections) {\n    if (isField(selection)) {\n      if (selection.name.value === \"__typename\") {\n        return result[resultKeyNameFromField(selection)];\n      }\n    } else if (fragments) {\n      fragments.push(selection);\n    } else {\n      fragments = [selection];\n    }\n  }\n  if (typeof result.__typename === \"string\") {\n    return result.__typename;\n  }\n  if (fragments) {\n    for (const selection of fragments) {\n      const typename = getTypenameFromResult(\n        result,\n        getFragmentFromSelection(selection, fragmentMap)!.selectionSet,\n        fragmentMap\n      );\n      if (typeof typename === \"string\") {\n        return typename;\n      }\n    }\n  }\n}\n\nexport function isField(selection: SelectionNode): selection is FieldNode {\n  return selection.kind === \"Field\";\n}\n\nexport function isInlineFragment(\n  selection: SelectionNode\n): selection is InlineFragmentNode {\n  return selection.kind === \"InlineFragment\";\n}\n\nexport type VariableValue = (node: VariableNode) => any;\n", "import { invariant, newInvariantError } from \"../globals/index.js\";\n\nimport type {\n  DocumentNode,\n  OperationDefinitionNode,\n  FragmentDefinitionNode,\n  ValueNode,\n} from \"graphql\";\n\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n\ntype OperationDefinitionWithName = OperationDefinitionNode & {\n  name: NonNullable<OperationDefinitionNode[\"name\"]>;\n};\n\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc: DocumentNode) {\n  invariant(\n    doc && doc.kind === \"Document\",\n    `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n  );\n\n  const operations = doc.definitions\n    .filter((d) => d.kind !== \"FragmentDefinition\")\n    .map((definition) => {\n      if (definition.kind !== \"OperationDefinition\") {\n        throw newInvariantError(\n          `Schema type definitions not allowed in queries. Found: \"%s\"`,\n          definition.kind\n        );\n      }\n      return definition;\n    });\n\n  invariant(\n    operations.length <= 1,\n    `Ambiguous GraphQL document: contains %s operations`,\n    operations.length\n  );\n\n  return doc;\n}\n\nexport function getOperationDefinition(\n  doc: DocumentNode\n): OperationDefinitionNode | undefined {\n  checkDocument(doc);\n  return doc.definitions.filter(\n    (definition): definition is OperationDefinitionNode =>\n      definition.kind === \"OperationDefinition\"\n  )[0];\n}\n\nexport function getOperationName(doc: DocumentNode): string | null {\n  return (\n    doc.definitions\n      .filter(\n        (definition): definition is OperationDefinitionWithName =>\n          definition.kind === \"OperationDefinition\" && !!definition.name\n      )\n      .map((x) => x.name.value)[0] || null\n  );\n}\n\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(\n  doc: DocumentNode\n): FragmentDefinitionNode[] {\n  return doc.definitions.filter(\n    (definition): definition is FragmentDefinitionNode =>\n      definition.kind === \"FragmentDefinition\"\n  );\n}\n\nexport function getQueryDefinition(doc: DocumentNode): OperationDefinitionNode {\n  const queryDef = getOperationDefinition(doc)!;\n\n  invariant(\n    queryDef && queryDef.operation === \"query\",\n    \"Must contain a query definition.\"\n  );\n\n  return queryDef;\n}\n\nexport function getFragmentDefinition(\n  doc: DocumentNode\n): FragmentDefinitionNode {\n  invariant(\n    doc.kind === \"Document\",\n    `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n  );\n\n  invariant(\n    doc.definitions.length <= 1,\n    \"Fragment must have exactly one definition.\"\n  );\n\n  const fragmentDef = doc.definitions[0] as FragmentDefinitionNode;\n\n  invariant(\n    fragmentDef.kind === \"FragmentDefinition\",\n    \"Must be a fragment definition.\"\n  );\n\n  return fragmentDef as FragmentDefinitionNode;\n}\n\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(\n  queryDoc: DocumentNode\n): OperationDefinitionNode | FragmentDefinitionNode {\n  checkDocument(queryDoc);\n\n  let fragmentDefinition;\n\n  for (let definition of queryDoc.definitions) {\n    if (definition.kind === \"OperationDefinition\") {\n      const operation = (definition as OperationDefinitionNode).operation;\n      if (\n        operation === \"query\" ||\n        operation === \"mutation\" ||\n        operation === \"subscription\"\n      ) {\n        return definition as OperationDefinitionNode;\n      }\n    }\n    if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n      // we do this because we want to allow multiple fragment definitions\n      // to precede an operation definition.\n      fragmentDefinition = definition as FragmentDefinitionNode;\n    }\n  }\n\n  if (fragmentDefinition) {\n    return fragmentDefinition;\n  }\n\n  throw newInvariantError(\n    \"Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment.\"\n  );\n}\n\nexport function getDefaultValues(\n  definition: OperationDefinitionNode | undefined\n): Record<string, any> {\n  const defaultValues = Object.create(null);\n  const defs = definition && definition.variableDefinitions;\n  if (defs && defs.length) {\n    defs.forEach((def) => {\n      if (def.defaultValue) {\n        valueToObjectRepresentation(\n          defaultValues,\n          def.variable.name,\n          def.defaultValue as ValueNode\n        );\n      }\n    });\n  }\n  return defaultValues;\n}\n", "// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\n\nexport class Trie<Data> {\n  // Since a `WeakMap` cannot hold primitive values as keys, we need a\n  // backup `Map` instance to hold primitive keys. Both `this._weakMap`\n  // and `this._strongMap` are lazily initialized.\n  private weak?: WeakMap<any, Trie<Data>>;\n  private strong?: Map<any, Trie<Data>>;\n  private data?: Data;\n\n  constructor(\n    private weakness = true,\n    private makeData: (array: any[]) => Data = defaultMakeData,\n  ) {}\n\n  public lookup<T extends any[]>(...array: T): Data {\n    return this.lookupArray(array);\n  }\n\n  public lookupArray<T extends IArguments | any[]>(array: T): Data {\n    let node: Trie<Data> = this;\n    forEach.call(array, key => node = node.getChildTrie(key));\n    return hasOwnProperty.call(node, \"data\")\n      ? node.data as Data\n      : node.data = this.makeData(slice.call(array));\n  }\n\n  public peek<T extends any[]>(...array: T): Data | undefined {\n    return this.peekArray(array);\n  }\n\n  public peekArray<T extends IArguments | any[]>(array: T): Data | undefined {\n    let node: Trie<Data> | undefined = this;\n\n    for (let i = 0, len = array.length; node && i < len; ++i) {\n      const map: Trie<Data>[\"weak\" | \"strong\"] =\n        this.weakness && isObjRef(array[i]) ? node.weak : node.strong;\n\n      node = map && map.get(array[i]);\n    }\n\n    return node && node.data;\n  }\n\n  private getChildTrie(key: any) {\n    const map = this.weakness && isObjRef(key)\n      ? this.weak || (this.weak = new WeakMap<any, Trie<Data>>())\n      : this.strong || (this.strong = new Map<any, Trie<Data>>());\n    let child = map.get(key);\n    if (!child) map.set(key, child = new Trie<Data>(this.weakness, this.makeData));\n    return child;\n  }\n}\n\nfunction isObjRef(value: any) {\n  switch (typeof value) {\n  case \"object\":\n    if (value === null) break;\n    // Fall through to return true...\n  case \"function\":\n    return true;\n  }\n  return false;\n}\n", "type Context = {\n  parent: Context | null;\n  slots: { [slotId: string]: any };\n}\n\n// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext: Context | null = null;\n\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE: any = {};\n\nlet idCounter = 1;\n\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot<TValue> {\n  // If you have a Slot object, you can find out its slot.id, but you cannot\n  // guess the slot.id of a Slot you don't have access to, thanks to the\n  // randomized suffix.\n  public readonly id = [\n    \"slot\",\n    idCounter++,\n    Date.now(),\n    Math.random().toString(36).slice(2),\n  ].join(\":\");\n\n  public hasValue() {\n    for (let context = currentContext; context; context = context.parent) {\n      // We use the Slot object iself as a key to its value, which means the\n      // value cannot be obtained without a reference to the Slot object.\n      if (this.id in context.slots) {\n        const value = context.slots[this.id];\n        if (value === MISSING_VALUE) break;\n        if (context !== currentContext) {\n          // Cache the value in currentContext.slots so the next lookup will\n          // be faster. This caching is safe because the tree of contexts and\n          // the values of the slots are logically immutable.\n          currentContext!.slots[this.id] = value;\n        }\n        return true;\n      }\n    }\n    if (currentContext) {\n      // If a value was not found for this Slot, it's never going to be found\n      // no matter how many times we look it up, so we might as well cache\n      // the absence of the value, too.\n      currentContext.slots[this.id] = MISSING_VALUE;\n    }\n    return false;\n  }\n\n  public getValue(): TValue | undefined {\n    if (this.hasValue()) {\n      return currentContext!.slots[this.id] as TValue;\n    }\n  }\n\n  public withValue<TResult, TArgs extends any[], TThis = any>(\n    value: TValue,\n    callback: (this: TThis, ...args: TArgs) => TResult,\n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args?: TArgs,\n    thisArg?: TThis,\n  ): TResult {\n    const slots = {\n      __proto__: null,\n      [this.id]: value,\n    };\n    const parent = currentContext;\n    currentContext = { parent, slots };\n    try {\n      // Function.prototype.apply allows the arguments array argument to be\n      // omitted or undefined, so args! is fine here.\n      return callback.apply(thisArg!, args!);\n    } finally {\n      currentContext = parent;\n    }\n  }\n\n  // Capture the current context and wrap a callback function so that it\n  // reestablishes the captured context when called.\n  static bind<TArgs extends any[], TResult, TThis = any>(\n    callback: (this: TThis, ...args: TArgs) => TResult,\n  ) {\n    const context = currentContext;\n    return function (this: TThis) {\n      const saved = currentContext;\n      try {\n        currentContext = context;\n        return callback.apply(this, arguments as any);\n      } finally {\n        currentContext = saved;\n      }\n    } as typeof callback;\n  }\n\n  // Immediately run a callback function without any captured context.\n  static noContext<TResult, TArgs extends any[], TThis = any>(\n    callback: (this: TThis, ...args: TArgs) => TResult,\n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args?: TArgs,\n    thisArg?: TThis,\n  ) {\n    if (currentContext) {\n      const saved = currentContext;\n      try {\n        currentContext = null;\n        // Function.prototype.apply allows the arguments array argument to be\n        // omitted or undefined, so args! is fine here.\n        return callback.apply(thisArg!, args!);\n      } finally {\n        currentContext = saved;\n      }\n    } else {\n      return callback.apply(thisArg!, args!);\n    }\n  }\n};\n\nfunction maybe<T>(fn: () => T): T | undefined {\n  try {\n    return fn();\n  } catch (ignored) {}\n}\n\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\n\nconst host =\n  // Prefer globalThis when available.\n  // https://github.com/benjamn/wryware/issues/347\n  maybe(() => globalThis) ||\n  // Fall back to global, which works in Node.js and may be converted by some\n  // bundlers to the appropriate identifier (window, self, ...) depending on the\n  // bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\n  maybe(() => global) ||\n  // Otherwise, use a dummy host that's local to this module. We used to fall\n  // back to using the Array constructor as a namespace, but that was flagged in\n  // https://github.com/benjamn/wryware/issues/347, and can be avoided.\n  Object.create(null) as typeof Array;\n\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost: typeof host & {\n  [globalKey]?: typeof Slot;\n} = host;\n\nexport const Slot: ReturnType<typeof makeSlotClass> =\n  globalHost[globalKey] ||\n  // Earlier versions of this package stored the globalKey property on the Array\n  // constructor, so we check there as well, to prevent Slot class duplication.\n  (Array as typeof globalHost)[globalKey] ||\n  (function (Slot) {\n    try {\n      Object.defineProperty(globalHost, globalKey, {\n        value: Slot,\n        enumerable: false,\n        writable: false,\n        // When it was possible for globalHost to be the Array constructor (a\n        // legacy Slot dedup strategy), it was important for the property to be\n        // configurable:true so it could be deleted. That does not seem to be as\n        // important when globalHost is the global object, but I don't want to\n        // cause similar problems again, and configurable:true seems safest.\n        // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n        configurable: true\n      });\n    } finally {\n      return Slot;\n    }\n  })(makeSlotClass());\n", "import { Slot } from \"./slot.js\";\nexport { Slot }\nexport const { bind, noContext } = Slot;\n\n// Relying on the @types/node declaration of global.setTimeout can make\n// things tricky for dowstream projects (see PR #7).\ndeclare function setTimeout(\n  callback: (...args: any[]) => any,\n  ms?: number,\n  ...args: any[]\n): any;\n\n// Like global.setTimeout, except the callback runs with captured context.\nexport { setTimeoutWithContext as setTimeout };\nfunction setTimeoutWithContext(callback: () => any, delay: number) {\n  return setTimeout(bind(callback), delay);\n}\n\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nexport function asyncFromGen<\n  TArgs extends any[],\n  TYield = any,\n  TReturn = any,\n  TNext = any,\n>(\n  genFn: (...args: TArgs) => Generator<TYield, TReturn, TNext>\n) {\n  return function (this: any) {\n    const gen = genFn.apply(this, arguments as any);\n\n    type Method = (\n      this: Generator<TYield, TReturn, TNext>,\n      arg: any,\n    ) => IteratorResult<TYield, TReturn>;\n\n    const boundNext: Method = bind(gen.next);\n    const boundThrow: Method = bind(gen.throw!);\n\n    return new Promise((resolve, reject) => {\n      function invoke(method: Method, argument: any) {\n        try {\n          var result: any = method.call(gen, argument);\n        } catch (error) {\n          return reject(error);\n        }\n        const next = result.done ? resolve : invokeNext;\n        if (isPromiseLike(result.value)) {\n          result.value.then(next, result.done ? reject : invokeThrow);\n        } else {\n          next(result.value);\n        }\n      }\n      const invokeNext = (value?: any) => invoke(boundNext, value);\n      const invokeThrow = (error: any) => invoke(boundThrow, error);\n      invokeNext();\n    });\n  } as (...args: TArgs) => Promise<any>;\n}\n\nfunction isPromiseLike(value: any): value is PromiseLike<any> {\n  return value && typeof value.then === \"function\";\n}\n\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers: Function[] = [];\nexport function wrapYieldingFiberMethods<F extends Function>(Fiber: F): F {\n  // There can be only one implementation of Fiber per process, so this array\n  // should never grow longer than one element.\n  if (wrappedFibers.indexOf(Fiber) < 0) {\n    const wrap = (obj: any, method: string) => {\n      const fn = obj[method];\n      obj[method] = function () {\n        return noContext(fn, arguments as any, this);\n      };\n    }\n    // These methods can yield, according to\n    // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n    wrap(Fiber, \"yield\");\n    wrap(Fiber.prototype, \"run\");\n    wrap(Fiber.prototype, \"throwInto\");\n    wrappedFibers.push(Fiber);\n  }\n  return Fiber;\n}\n", "import { Slot } from \"@wry/context\";\nimport { AnyEntry } from \"./entry.js\";\n\nexport const parentEntrySlot = new Slot<AnyEntry | undefined>();\n\nexport function nonReactive<R>(fn: () => R): R {\n  return parentEntrySlot.withValue(void 0, fn);\n}\n\nexport { Slot }\nexport {\n  bind as bindContext,\n  noContext,\n  setTimeout,\n  asyncFromGen,\n} from \"@wry/context\";\n", "export type NoInfer<T> = [T][T extends any ? 0 : never];\n\nexport const {\n  hasOwnProperty,\n} = Object.prototype;\n\nexport const arrayFromSet: <T>(set: Set<T>) => T[] =\n  Array.from ||\n  function (set) {\n    const array: any[] = [];\n    set.forEach(item => array.push(item));\n    return array;\n  };\n\nexport type Unsubscribable = {\n  unsubscribe?: void | (() => any);\n}\n\nexport function maybeUnsubscribe(entryOrDep: Unsubscribable) {\n  const { unsubscribe } = entryOrDep;\n  if (typeof unsubscribe === \"function\") {\n    entryOrDep.unsubscribe = void 0;\n    unsubscribe();\n  }\n}\n", "import { parentEntrySlot } from \"./context.js\";\nimport { OptimisticWrapOptions } from \"./index.js\";\nimport { Dep } from \"./dep.js\";\nimport { maybeUnsubscribe, arrayFromSet, Unsubscribable } from \"./helpers.js\";\n\nconst emptySetPool: Set<any>[] = [];\nconst POOL_TARGET_SIZE = 100;\n\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition: any, optionalMessage?: string) {\n  if (! condition) {\n    throw new Error(optionalMessage || \"assertion failure\");\n  }\n}\n\n// Since exceptions are cached just like normal values, we need an efficient\n// way of representing unknown, ordinary, and exceptional values.\ntype Value<T> =\n  | []           // unknown\n  | [T]          // known value\n  | [void, any]; // known exception\n\nfunction valueIs(a: Value<any>, b: Value<any>) {\n  const len = a.length;\n  return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n    // Both values must be ordinary (or both exceptional) to be equal.\n    len === b.length &&\n    // The underlying value or exception must be the same.\n    a[len - 1] === b[len - 1]\n  );\n}\n\nfunction valueGet<T>(value: Value<T>): T {\n  switch (value.length) {\n    case 0: throw new Error(\"unknown value\");\n    case 1: return value[0];\n    case 2: throw value[1];\n  }\n}\n\nfunction valueCopy<T>(value: Value<T>): Value<T> {\n  return value.slice(0) as Value<T>;\n}\n\nexport type AnyEntry = Entry<any, any>;\n\nexport class Entry<TArgs extends any[], TValue> {\n  public static count = 0;\n\n  public normalizeResult: OptimisticWrapOptions<TArgs, any, any, TValue>[\"normalizeResult\"];\n  public subscribe: OptimisticWrapOptions<TArgs>[\"subscribe\"];\n  public unsubscribe: Unsubscribable[\"unsubscribe\"];\n\n  public readonly parents = new Set<AnyEntry>();\n  public readonly childValues = new Map<AnyEntry, Value<any>>();\n\n  // When this Entry has children that are dirty, this property becomes\n  // a Set containing other Entry objects, borrowed from emptySetPool.\n  // When the set becomes empty, it gets recycled back to emptySetPool.\n  public dirtyChildren: Set<AnyEntry> | null = null;\n\n  public dirty = true;\n  public recomputing = false;\n  public readonly value: Value<TValue> = [];\n\n  constructor(\n    public readonly fn: (...args: TArgs) => TValue,\n  ) {\n    ++Entry.count;\n  }\n\n  public peek(): TValue | undefined {\n    if (this.value.length === 1 && !mightBeDirty(this)) {\n      rememberParent(this);\n      return this.value[0];\n    }\n  }\n\n  // This is the most important method of the Entry API, because it\n  // determines whether the cached this.value can be returned immediately,\n  // or must be recomputed. The overall performance of the caching system\n  // depends on the truth of the following observations: (1) this.dirty is\n  // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n  // (3) valueGet(this.value) is usually returned without recomputation.\n  public recompute(args: TArgs): TValue {\n    assert(! this.recomputing, \"already recomputing\");\n    rememberParent(this);\n    return mightBeDirty(this)\n      ? reallyRecompute(this, args)\n      : valueGet(this.value);\n  }\n\n  public setDirty() {\n    if (this.dirty) return;\n    this.dirty = true;\n    reportDirty(this);\n    // We can go ahead and unsubscribe here, since any further dirty\n    // notifications we receive will be redundant, and unsubscribing may\n    // free up some resources, e.g. file watchers.\n    maybeUnsubscribe(this);\n  }\n\n  public dispose() {\n    this.setDirty();\n\n    // Sever any dependency relationships with our own children, so those\n    // children don't retain this parent Entry in their child.parents sets,\n    // thereby preventing it from being fully garbage collected.\n    forgetChildren(this);\n\n    // Because this entry has been kicked out of the cache (in index.js),\n    // we've lost the ability to find out if/when this entry becomes dirty,\n    // whether that happens through a subscription, because of a direct call\n    // to entry.setDirty(), or because one of its children becomes dirty.\n    // Because of this loss of future information, we have to assume the\n    // worst (that this entry might have become dirty very soon), so we must\n    // immediately mark this entry's parents as dirty. Normally we could\n    // just call entry.setDirty() rather than calling parent.setDirty() for\n    // each parent, but that would leave this entry in parent.childValues\n    // and parent.dirtyChildren, which would prevent the child from being\n    // truly forgotten.\n    eachParent(this, (parent, child) => {\n      parent.setDirty();\n      forgetChild(parent, this);\n    });\n  }\n\n  public forget() {\n    // The code that creates Entry objects in index.ts will replace this method\n    // with one that actually removes the Entry from the cache, which will also\n    // trigger the entry.dispose method.\n    this.dispose();\n  }\n\n  private deps: Set<Dep<any>> | null = null;\n\n  public dependOn(dep: Dep<any>) {\n    dep.add(this);\n    if (! this.deps) {\n      this.deps = emptySetPool.pop() || new Set<Set<AnyEntry>>();\n    }\n    this.deps.add(dep);\n  }\n\n  public forgetDeps() {\n    if (this.deps) {\n      arrayFromSet(this.deps).forEach(dep => dep.delete(this));\n      this.deps.clear();\n      emptySetPool.push(this.deps);\n      this.deps = null;\n    }\n  }\n}\n\nfunction rememberParent(child: AnyEntry) {\n  const parent = parentEntrySlot.getValue();\n  if (parent) {\n    child.parents.add(parent);\n\n    if (! parent.childValues.has(child)) {\n      parent.childValues.set(child, []);\n    }\n\n    if (mightBeDirty(child)) {\n      reportDirtyChild(parent, child);\n    } else {\n      reportCleanChild(parent, child);\n    }\n\n    return parent;\n  }\n}\n\nfunction reallyRecompute(entry: AnyEntry, args: any[]) {\n  forgetChildren(entry);\n\n  // Set entry as the parent entry while calling recomputeNewValue(entry).\n  parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n\n  if (maybeSubscribe(entry, args)) {\n    // If we successfully recomputed entry.value and did not fail to\n    // (re)subscribe, then this Entry is no longer explicitly dirty.\n    setClean(entry);\n  }\n\n  return valueGet(entry.value);\n}\n\nfunction recomputeNewValue(entry: AnyEntry, args: any[]) {\n  entry.recomputing = true;\n\n  const { normalizeResult } = entry;\n  let oldValueCopy: Value<any> | undefined;\n  if (normalizeResult && entry.value.length === 1) {\n    oldValueCopy = valueCopy(entry.value);\n  }\n\n  // Make entry.value an empty array, representing an unknown value.\n  entry.value.length = 0;\n\n  try {\n    // If entry.fn succeeds, entry.value will become a normal Value.\n    entry.value[0] = entry.fn.apply(null, args);\n\n    // If we have a viable oldValueCopy to compare with the (successfully\n    // recomputed) new entry.value, and they are not already === identical, give\n    // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n    // and/or entry.value[0] to determine the final cached entry.value.\n    if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n      try {\n        entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n      } catch {\n        // If normalizeResult throws, just use the newer value, rather than\n        // saving the exception as entry.value[1].\n      }\n    }\n\n  } catch (e) {\n    // If entry.fn throws, entry.value will hold that exception.\n    entry.value[1] = e;\n  }\n\n  // Either way, this line is always reached.\n  entry.recomputing = false;\n}\n\nfunction mightBeDirty(entry: AnyEntry) {\n  return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\n\nfunction setClean(entry: AnyEntry) {\n  entry.dirty = false;\n\n  if (mightBeDirty(entry)) {\n    // This Entry may still have dirty children, in which case we can't\n    // let our parents know we're clean just yet.\n    return;\n  }\n\n  reportClean(entry);\n}\n\nfunction reportDirty(child: AnyEntry) {\n  eachParent(child, reportDirtyChild);\n}\n\nfunction reportClean(child: AnyEntry) {\n  eachParent(child, reportCleanChild);\n}\n\nfunction eachParent(\n  child: AnyEntry,\n  callback: (parent: AnyEntry, child: AnyEntry) => any,\n) {\n  const parentCount = child.parents.size;\n  if (parentCount) {\n    const parents = arrayFromSet(child.parents);\n    for (let i = 0; i < parentCount; ++i) {\n      callback(parents[i], child);\n    }\n  }\n}\n\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent: AnyEntry, child: AnyEntry) {\n  // Must have called rememberParent(child) before calling\n  // reportDirtyChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(mightBeDirty(child));\n  const parentWasClean = !mightBeDirty(parent);\n\n  if (! parent.dirtyChildren) {\n    parent.dirtyChildren = emptySetPool.pop() || new Set;\n\n  } else if (parent.dirtyChildren.has(child)) {\n    // If we already know this child is dirty, then we must have already\n    // informed our own parents that we are dirty, so we can terminate\n    // the recursion early.\n    return;\n  }\n\n  parent.dirtyChildren.add(child);\n\n  // If parent was clean before, it just became (possibly) dirty (according to\n  // mightBeDirty), since we just added child to parent.dirtyChildren.\n  if (parentWasClean) {\n    reportDirty(parent);\n  }\n}\n\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent: AnyEntry, child: AnyEntry) {\n  // Must have called rememberChild(child) before calling\n  // reportCleanChild(parent, child).\n  assert(parent.childValues.has(child));\n  assert(! mightBeDirty(child));\n\n  const childValue = parent.childValues.get(child)!;\n  if (childValue.length === 0) {\n    parent.childValues.set(child, valueCopy(child.value));\n  } else if (! valueIs(childValue, child.value)) {\n    parent.setDirty();\n  }\n\n  removeDirtyChild(parent, child);\n\n  if (mightBeDirty(parent)) {\n    return;\n  }\n\n  reportClean(parent);\n}\n\nfunction removeDirtyChild(parent: AnyEntry, child: AnyEntry) {\n  const dc = parent.dirtyChildren;\n  if (dc) {\n    dc.delete(child);\n    if (dc.size === 0) {\n      if (emptySetPool.length < POOL_TARGET_SIZE) {\n        emptySetPool.push(dc);\n      }\n      parent.dirtyChildren = null;\n    }\n  }\n}\n\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent: AnyEntry) {\n  if (parent.childValues.size > 0) {\n    parent.childValues.forEach((_value, child) => {\n      forgetChild(parent, child);\n    });\n  }\n\n  // Remove this parent Entry from any sets to which it was added by the\n  // addToSet method.\n  parent.forgetDeps();\n\n  // After we forget all our children, this.dirtyChildren must be empty\n  // and therefore must have been reset to null.\n  assert(parent.dirtyChildren === null);\n}\n\nfunction forgetChild(parent: AnyEntry, child: AnyEntry) {\n  child.parents.delete(parent);\n  parent.childValues.delete(child);\n  removeDirtyChild(parent, child);\n}\n\nfunction maybeSubscribe(entry: AnyEntry, args: any[]) {\n  if (typeof entry.subscribe === \"function\") {\n    try {\n      maybeUnsubscribe(entry); // Prevent double subscriptions.\n      entry.unsubscribe = entry.subscribe.apply(null, args);\n    } catch (e) {\n      // If this Entry has a subscribe function and it threw an exception\n      // (or an unsubscribe function it previously returned now throws),\n      // return false to indicate that we were not able to subscribe (or\n      // unsubscribe), and this Entry should remain dirty.\n      entry.setDirty();\n      return false;\n    }\n  }\n\n  // Returning true indicates either that there was no entry.subscribe\n  // function or that it succeeded.\n  return true;\n}\n", "import { AnyEntry } from \"./entry.js\";\nimport { OptimisticWrapOptions } from \"./index.js\";\nimport { parentEntrySlot } from \"./context.js\";\nimport {\n  hasOwnProperty,\n  Unsubscribable,\n  maybeUnsubscribe,\n  arrayFromSet,\n } from \"./helpers.js\";\n\ntype EntryMethodName = keyof typeof EntryMethods;\nconst EntryMethods = {\n  setDirty: true, // Mark parent Entry as needing to be recomputed (default)\n  dispose: true,  // Detach parent Entry from parents and children, but leave in LRU cache\n  forget: true,   // Fully remove parent Entry from LRU cache and computation graph\n};\n\nexport type OptimisticDependencyFunction<TKey> =\n  ((key: TKey) => void) & {\n    dirty: (key: TKey, entryMethodName?: EntryMethodName) => void;\n  };\n\nexport type Dep<TKey> = Set<AnyEntry> & {\n  subscribe: OptimisticWrapOptions<[TKey]>[\"subscribe\"];\n} & Unsubscribable;\n\nexport function dep<TKey>(options?: {\n  subscribe: Dep<TKey>[\"subscribe\"];\n}) {\n  const depsByKey = new Map<TKey, Dep<TKey>>();\n  const subscribe = options && options.subscribe;\n\n  function depend(key: TKey) {\n    const parent = parentEntrySlot.getValue();\n    if (parent) {\n      let dep = depsByKey.get(key);\n      if (!dep) {\n        depsByKey.set(key, dep = new Set as Dep<TKey>);\n      }\n      parent.dependOn(dep);\n      if (typeof subscribe === \"function\") {\n        maybeUnsubscribe(dep);\n        dep.unsubscribe = subscribe(key);\n      }\n    }\n  }\n\n  depend.dirty = function dirty(\n    key: TKey,\n    entryMethodName?: EntryMethodName,\n  ) {\n    const dep = depsByKey.get(key);\n    if (dep) {\n      const m: EntryMethodName = (\n        entryMethodName &&\n        hasOwnProperty.call(EntryMethods, entryMethodName)\n      ) ? entryMethodName : \"setDirty\";\n      // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n      // because modifying a Set while iterating over it can cause elements in\n      // the Set to be removed from the Set before they've been iterated over.\n      arrayFromSet(dep).forEach(entry => entry[m]());\n      depsByKey.delete(key);\n      maybeUnsubscribe(dep);\n    }\n  };\n\n  return depend as OptimisticDependencyFunction<TKey>;\n}\n", "import { <PERSON><PERSON> } from \"@wry/trie\";\n\nimport { StrongCache, CommonCache } from \"@wry/caches\";\nimport { Entry, AnyEntry } from \"./entry.js\";\nimport { parentEntrySlot } from \"./context.js\";\nimport type { NoInfer } from \"./helpers.js\";\n\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\nexport {\n  bindContext,\n  noContext,\n  nonReactive,\n  setTimeout,\n  asyncFromGen,\n  Slot,\n} from \"./context.js\";\n\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\nexport { dep, OptimisticDependencyFunction } from \"./dep.js\";\n\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie: Trie<object> | undefined;\nexport function defaultMakeCacheKey(...args: any[]): object {\n  const trie = defaultKeyTrie || (\n    defaultKeyTrie = new Trie(typeof WeakMap === \"function\")\n  );\n  return trie.lookupArray(args);\n}\n\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\nexport { Trie as KeyTrie }\n\nexport type OptimisticWrapperFunction<\n  TArgs extends any[],\n  TResult,\n  TKeyArgs extends any[] = TArgs,\n  TCacheKey = any,\n> = ((...args: TArgs) => TResult) & {\n  // Get the current number of Entry objects in the LRU cache.\n  readonly size: number;\n\n  // Snapshot of wrap options used to create this wrapper function.\n  options: OptionsWithCacheInstance<TArgs, TKeyArgs, TCacheKey>;\n\n  // \"Dirty\" any cached Entry stored for the given arguments, marking that Entry\n  // and its ancestors as potentially needing to be recomputed. The .dirty(...)\n  // method of an optimistic function takes the same parameter types as the\n  // original function by default, unless a keyArgs function is configured, and\n  // then it matters that .dirty takes TKeyArgs instead of TArgs.\n  dirty: (...args: TKeyArgs) => void;\n  // A version of .dirty that accepts a key returned by .getKey.\n  dirtyKey: (key: TCacheKey | undefined) => void;\n\n  // Examine the current value without recomputing it.\n  peek: (...args: TKeyArgs) => TResult | undefined;\n  // A version of .peek that accepts a key returned by .getKey.\n  peekKey: (key: TCacheKey | undefined) => TResult | undefined;\n\n  // Completely remove the entry from the cache, dirtying any parent entries.\n  forget: (...args: TKeyArgs) => boolean;\n  // A version of .forget that accepts a key returned by .getKey.\n  forgetKey: (key: TCacheKey | undefined) => boolean;\n\n  // In order to use the -Key version of the above functions, you need a key\n  // rather than the arguments used to compute the key. These two functions take\n  // TArgs or TKeyArgs and return the corresponding TCacheKey. If no keyArgs\n  // function has been configured, TArgs will be the same as TKeyArgs, and thus\n  // getKey and makeCacheKey will be synonymous.\n  getKey: (...args: TArgs) => TCacheKey | undefined;\n\n  // This property is equivalent to the makeCacheKey function provided in the\n  // OptimisticWrapOptions, or (if no options.makeCacheKey function is provided)\n  // a default implementation of makeCacheKey. This function is also exposed as\n  // optimistic.options.makeCacheKey, somewhat redundantly.\n  makeCacheKey: (...args: TKeyArgs) => TCacheKey | undefined;\n};\n\nexport { CommonCache }\nexport interface CommonCacheConstructor<TCacheKey, TResult, TArgs extends any[]> extends Function {\n  new <K extends TCacheKey, V extends Entry<TArgs, TResult>>(max?: number, dispose?: (value: V, key?: K) => void): CommonCache<K,V>;\n}\n\nexport type OptimisticWrapOptions<\n  TArgs extends any[],\n  TKeyArgs extends any[] = TArgs,\n  TCacheKey = any,\n  TResult = any,\n> = {\n  // The maximum number of cache entries that should be retained before the\n  // cache begins evicting the oldest ones.\n  max?: number;\n  // Transform the raw arguments to some other type of array, which will then\n  // be passed to makeCacheKey.\n  keyArgs?: (...args: TArgs) => TKeyArgs;\n  // The makeCacheKey function takes the same arguments that were passed to\n  // the wrapper function and returns a single value that can be used as a key\n  // in a Map to identify the cached result.\n  makeCacheKey?: (...args: NoInfer<TKeyArgs>) => TCacheKey | undefined;\n  // Called when a new value is computed to allow efficient normalization of\n  // results over time, for example by returning older if equal(newer, older).\n  normalizeResult?: (newer: TResult, older: TResult) => TResult;\n  // If provided, the subscribe function should either return an unsubscribe\n  // function or return nothing.\n  subscribe?: (...args: TArgs) => void | (() => any);\n  cache?: CommonCache<NoInfer<TCacheKey>, Entry<NoInfer<TArgs>, NoInfer<TResult>>>\n    | CommonCacheConstructor<NoInfer<TCacheKey>, NoInfer<TResult>, NoInfer<TArgs>>;\n};\n\nexport interface OptionsWithCacheInstance<\n  TArgs extends any[],\n  TKeyArgs extends any[] = TArgs,\n  TCacheKey = any,\n  TResult = any,\n> extends OptimisticWrapOptions<TArgs, TKeyArgs, TCacheKey, TResult> {\n  cache: CommonCache<NoInfer<TCacheKey>, Entry<NoInfer<TArgs>, NoInfer<TResult>>>;\n};\n\nconst caches = new Set<CommonCache<any, AnyEntry>>();\n\nexport function wrap<\n  TArgs extends any[],\n  TResult,\n  TKeyArgs extends any[] = TArgs,\n  TCacheKey = any,\n>(originalFunction: (...args: TArgs) => TResult, {\n  max = Math.pow(2, 16),\n  keyArgs,\n  makeCacheKey = (defaultMakeCacheKey as () => TCacheKey),\n  normalizeResult,\n  subscribe,\n  cache: cacheOption = StrongCache,\n}: OptimisticWrapOptions<TArgs, TKeyArgs, TCacheKey, TResult> = Object.create(null)) {\n  const cache: CommonCache<TCacheKey, Entry<TArgs, TResult>> =\n    typeof cacheOption === \"function\"\n      ? new cacheOption(max, entry => entry.dispose())\n      : cacheOption;\n\n  const optimistic = function (): TResult {\n    const key = makeCacheKey.apply(\n      null,\n      keyArgs ? keyArgs.apply(null, arguments as any) : arguments as any\n    );\n\n    if (key === void 0) {\n      return originalFunction.apply(null, arguments as any);\n    }\n\n    let entry = cache.get(key)!;\n    if (!entry) {\n      cache.set(key, entry = new Entry(originalFunction));\n      entry.normalizeResult = normalizeResult;\n      entry.subscribe = subscribe;\n      // Give the Entry the ability to trigger cache.delete(key), even though\n      // the Entry itself does not know about key or cache.\n      entry.forget = () => cache.delete(key);\n    }\n\n    const value = entry.recompute(\n      Array.prototype.slice.call(arguments) as TArgs,\n    );\n\n    // Move this entry to the front of the least-recently used queue,\n    // since we just finished computing its value.\n    cache.set(key, entry);\n\n    caches.add(cache);\n\n    // Clean up any excess entries in the cache, but only if there is no\n    // active parent entry, meaning we're not in the middle of a larger\n    // computation that might be flummoxed by the cleaning.\n    if (! parentEntrySlot.hasValue()) {\n      caches.forEach(cache => cache.clean());\n      caches.clear();\n    }\n\n    return value;\n  } as OptimisticWrapperFunction<TArgs, TResult, TKeyArgs, TCacheKey>;\n\n  Object.defineProperty(optimistic, \"size\", {\n    get: () => cache.size,\n    configurable: false,\n    enumerable: false,\n  });\n\n  Object.freeze(optimistic.options = {\n    max,\n    keyArgs,\n    makeCacheKey,\n    normalizeResult,\n    subscribe,\n    cache,\n  });\n\n  function dirtyKey(key: TCacheKey | undefined) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      entry.setDirty();\n    }\n  }\n  optimistic.dirtyKey = dirtyKey;\n  optimistic.dirty = function dirty() {\n    dirtyKey(makeCacheKey.apply(null, arguments as any));\n  };\n\n  function peekKey(key: TCacheKey | undefined) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      return entry.peek();\n    }\n  }\n  optimistic.peekKey = peekKey;\n  optimistic.peek = function peek() {\n    return peekKey(makeCacheKey.apply(null, arguments as any));\n  };\n\n  function forgetKey(key: TCacheKey | undefined) {\n    return key ? cache.delete(key) : false;\n  }\n  optimistic.forgetKey = forgetKey;\n  optimistic.forget = function forget() {\n    return forgetKey(makeCacheKey.apply(null, arguments as any));\n  };\n\n  optimistic.makeCacheKey = makeCacheKey;\n  optimistic.getKey = keyArgs ? function getKey() {\n    return makeCacheKey.apply(null, keyArgs.apply(null, arguments as any));\n  } : makeCacheKey as (...args: any[]) => TCacheKey | undefined;\n\n  return Object.freeze(optimistic);\n}\n", "import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport type { DocumentNode } from \"graphql\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\n\nexport type DocumentTransformCacheKey = ReadonlyArray<unknown>;\n\ntype TransformFn = (document: DocumentNode) => DocumentNode;\n\ninterface DocumentTransformOptions {\n  /**\n   * Determines whether to cache the transformed GraphQL document. Caching can speed up repeated calls to the document transform for the same input document. Set to `false` to completely disable caching for the document transform. When disabled, this option takes precedence over the [`getCacheKey`](#getcachekey) option.\n   *\n   * The default value is `true`.\n   */\n  cache?: boolean;\n  /**\n   * Defines a custom cache key for a GraphQL document that will determine whether to re-run the document transform when given the same input GraphQL document. Returns an array that defines the cache key. Return `undefined` to disable caching for that GraphQL document.\n   *\n   * > **Note:** The items in the array may be any type, but also need to be referentially stable to guarantee a stable cache key.\n   *\n   * The default implementation of this function returns the `document` as the cache key.\n   */\n  getCacheKey?: (\n    document: DocumentNode\n  ) => DocumentTransformCacheKey | undefined;\n}\n\nfunction identity(document: DocumentNode) {\n  return document;\n}\n\nexport class DocumentTransform {\n  private readonly transform: TransformFn;\n  private cached: boolean;\n\n  private readonly resultCache =\n    canUseWeakSet ? new WeakSet<DocumentNode>() : new Set<DocumentNode>();\n\n  // This default implementation of getCacheKey can be overridden by providing\n  // options.getCacheKey to the DocumentTransform constructor. In general, a\n  // getCacheKey function may either return an array of keys (often including\n  // the document) to be used as a cache key, or undefined to indicate the\n  // transform for this document should not be cached.\n  private getCacheKey(\n    document: DocumentNode\n  ): DocumentTransformCacheKey | undefined {\n    return [document];\n  }\n\n  static identity() {\n    // No need to cache this transform since it just returns the document\n    // unchanged. This should save a bit of memory that would otherwise be\n    // needed to populate the `documentCache` of this transform.\n    return new DocumentTransform(identity, { cache: false });\n  }\n\n  static split(\n    predicate: (document: DocumentNode) => boolean,\n    left: DocumentTransform,\n    right: DocumentTransform = DocumentTransform.identity()\n  ) {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          const documentTransform = predicate(document) ? left : right;\n\n          return documentTransform.transformDocument(document);\n        },\n        // Reasonably assume both `left` and `right` transforms handle their own caching\n        { cache: false }\n      ),\n      { left, right }\n    );\n  }\n\n  constructor(\n    transform: TransformFn,\n    options: DocumentTransformOptions = Object.create(null)\n  ) {\n    this.transform = transform;\n\n    if (options.getCacheKey) {\n      // Override default `getCacheKey` function, which returns [document].\n      this.getCacheKey = options.getCacheKey;\n    }\n    this.cached = options.cache !== false;\n\n    this.resetCache();\n  }\n\n  /**\n   * Resets the internal cache of this transform, if it has one.\n   */\n  resetCache() {\n    if (this.cached) {\n      const stableCacheKeys = new Trie<WeakKey>(canUseWeakMap);\n      this.performWork = wrap(\n        DocumentTransform.prototype.performWork.bind(this),\n        {\n          makeCacheKey: (document) => {\n            const cacheKeys = this.getCacheKey(document);\n            if (cacheKeys) {\n              invariant(\n                Array.isArray(cacheKeys),\n                \"`getCacheKey` must return an array or undefined\"\n              );\n              return stableCacheKeys.lookupArray(cacheKeys);\n            }\n          },\n          max: cacheSizes[\"documentTransform.cache\"],\n          cache: WeakCache<any, any>,\n        }\n      );\n    }\n  }\n\n  private performWork(document: DocumentNode) {\n    checkDocument(document);\n    return this.transform(document);\n  }\n\n  transformDocument(document: DocumentNode) {\n    // If a user passes an already transformed result back to this function,\n    // immediately return it.\n    if (this.resultCache.has(document)) {\n      return document;\n    }\n\n    const transformedDocument = this.performWork(document);\n\n    this.resultCache.add(transformedDocument);\n\n    return transformedDocument;\n  }\n\n  concat(otherTransform: DocumentTransform): DocumentTransform {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          return otherTransform.transformDocument(\n            this.transformDocument(document)\n          );\n        },\n        // Reasonably assume both transforms handle their own caching\n        { cache: false }\n      ),\n      {\n        left: this,\n        right: otherTransform,\n      }\n    );\n  }\n\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly left?: DocumentTransform;\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly right?: DocumentTransform;\n}\n", "import type { ASTNode } from \"graphql\";\nimport { print as origPrint } from \"graphql\";\nimport {\n  AutoCleanedWeakCache,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n\nlet printCache!: AutoCleanedWeakCache<ASTNode, string>;\nexport const print = Object.assign(\n  (ast: ASTNode) => {\n    let result = printCache.get(ast);\n\n    if (!result) {\n      result = origPrint(ast);\n      printCache.set(ast, result);\n    }\n    return result;\n  },\n  {\n    reset() {\n      printCache = new AutoCleanedWeakCache<ASTNode, string>(\n        cacheSizes.print || defaultCacheSizes.print\n      );\n    },\n  }\n);\nprint.reset();\n\nif (__DEV__) {\n  registerGlobalCache(\"print\", () => (printCache ? printCache.size : 0));\n}\n", "// A version of Array.isArray that works better with readonly arrays.\nexport const isArray: (a: any) => a is any[] | readonly any[] = Array.isArray;\n\nexport function isNonEmptyArray<T>(value?: ArrayLike<T>): value is Array<T> {\n  return Array.isArray(value) && value.length > 0;\n}\n", "import { invariant } from \"../globals/index.js\";\n\nimport type {\n  DocumentNode,\n  SelectionNode,\n  SelectionSetNode,\n  OperationDefinitionNode,\n  FieldNode,\n  DirectiveNode,\n  FragmentDefinitionNode,\n  ArgumentNode,\n  FragmentSpreadNode,\n  VariableDefinitionNode,\n  ASTNode,\n  ASTVisitFn,\n  InlineFragmentNode,\n} from \"graphql\";\nimport { visit, Kind } from \"graphql\";\n\nimport {\n  checkDocument,\n  getOperationDefinition,\n  getFragmentDefinition,\n  getFragmentDefinitions,\n  getMainDefinition,\n} from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport type { FragmentMap } from \"./fragments.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\n\n// https://github.com/graphql/graphql-js/blob/8d7c8fccf5a9846a50785de04abda58a7eb13fc0/src/language/visitor.ts#L20-L23\ninterface EnterLeaveVisitor<TVisitedNode extends ASTNode> {\n  readonly enter?: ASTVisitFn<TVisitedNode>;\n  readonly leave?: ASTVisitFn<TVisitedNode>;\n}\n\nexport type RemoveNodeConfig<N> = {\n  name?: string;\n  test?: (node: N) => boolean;\n  remove?: boolean;\n};\n\nexport type GetNodeConfig<N> = {\n  name?: string;\n  test?: (node: N) => boolean;\n};\n\nexport type RemoveDirectiveConfig = RemoveNodeConfig<DirectiveNode>;\nexport type GetDirectiveConfig = GetNodeConfig<DirectiveNode>;\nexport type RemoveArgumentsConfig = RemoveNodeConfig<ArgumentNode>;\nexport type GetFragmentSpreadConfig = GetNodeConfig<FragmentSpreadNode>;\nexport type RemoveFragmentSpreadConfig = RemoveNodeConfig<FragmentSpreadNode>;\nexport type RemoveFragmentDefinitionConfig =\n  RemoveNodeConfig<FragmentDefinitionNode>;\nexport type RemoveVariableDefinitionConfig =\n  RemoveNodeConfig<VariableDefinitionNode>;\n\nconst TYPENAME_FIELD: FieldNode = {\n  kind: Kind.FIELD,\n  name: {\n    kind: Kind.NAME,\n    value: \"__typename\",\n  },\n};\n\nfunction isEmpty(\n  op: OperationDefinitionNode | FragmentDefinitionNode,\n  fragmentMap: FragmentMap\n): boolean {\n  return (\n    !op ||\n    op.selectionSet.selections.every(\n      (selection) =>\n        selection.kind === Kind.FRAGMENT_SPREAD &&\n        isEmpty(fragmentMap[selection.name.value], fragmentMap)\n    )\n  );\n}\n\nfunction nullIfDocIsEmpty(doc: DocumentNode) {\n  return (\n      isEmpty(\n        getOperationDefinition(doc) || getFragmentDefinition(doc),\n        createFragmentMap(getFragmentDefinitions(doc))\n      )\n    ) ?\n      null\n    : doc;\n}\n\nfunction getDirectiveMatcher(\n  configs: (RemoveDirectiveConfig | GetDirectiveConfig)[]\n) {\n  const names = new Map<string, RemoveDirectiveConfig | GetDirectiveConfig>();\n\n  const tests = new Map<\n    (directive: DirectiveNode) => boolean,\n    RemoveDirectiveConfig | GetDirectiveConfig\n  >();\n\n  configs.forEach((directive) => {\n    if (directive) {\n      if (directive.name) {\n        names.set(directive.name, directive);\n      } else if (directive.test) {\n        tests.set(directive.test, directive);\n      }\n    }\n  });\n\n  return (directive: DirectiveNode) => {\n    let config = names.get(directive.name.value);\n    if (!config && tests.size) {\n      tests.forEach((testConfig, test) => {\n        if (test(directive)) {\n          config = testConfig;\n        }\n      });\n    }\n    return config;\n  };\n}\n\n// Helper interface and function used by removeDirectivesFromDocument to keep\n// track of variable references and fragments spreads found within a given\n// operation or fragment definition.\ninterface InternalInUseInfo {\n  variables: Set<string>;\n  fragmentSpreads: Set<string>;\n  // Set to true when we deliberately remove a fragment definition, so we can\n  // make sure also to remove dangling ...spreads that refer to it.\n  removed?: boolean;\n  // Populated by the populateTransitiveVars helper function below.\n  transitiveVars?: Set<string>;\n}\nfunction makeInUseGetterFunction<TKey>(defaultKey: TKey) {\n  const map = new Map<TKey, InternalInUseInfo>();\n\n  return function inUseGetterFunction(\n    key: TKey = defaultKey\n  ): InternalInUseInfo {\n    let inUse = map.get(key);\n    if (!inUse) {\n      map.set(\n        key,\n        (inUse = {\n          // Variable and fragment spread names used directly within this\n          // operation or fragment definition, as identified by key. These sets\n          // will be populated during the first traversal of the document in\n          // removeDirectivesFromDocument below.\n          variables: new Set(),\n          fragmentSpreads: new Set(),\n        })\n      );\n    }\n    return inUse;\n  };\n}\n\nexport function removeDirectivesFromDocument(\n  directives: RemoveDirectiveConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  checkDocument(doc);\n\n  // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n  // operations as if their names were \"\". Anonymous fragment definitions are\n  // not supposed to be possible, but the same default naming strategy seems\n  // appropriate for that case as well.\n  const getInUseByOperationName = makeInUseGetterFunction<string>(\"\");\n  const getInUseByFragmentName = makeInUseGetterFunction<string>(\"\");\n  const getInUse = (\n    ancestors: readonly (ASTNode | readonly ASTNode[])[]\n  ): InternalInUseInfo | null => {\n    for (\n      let p = 0, ancestor: ASTNode | readonly ASTNode[];\n      p < ancestors.length && (ancestor = ancestors[p]);\n      ++p\n    ) {\n      if (isArray(ancestor)) continue;\n      if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n        // If an operation is anonymous, we use the empty string as its key.\n        return getInUseByOperationName(ancestor.name && ancestor.name.value);\n      }\n      if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n        return getInUseByFragmentName(ancestor.name.value);\n      }\n    }\n    invariant.error(`Could not find operation or fragment`);\n    return null;\n  };\n\n  let operationCount = 0;\n  for (let i = doc.definitions.length - 1; i >= 0; --i) {\n    if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n      ++operationCount;\n    }\n  }\n\n  const directiveMatcher = getDirectiveMatcher(directives);\n  const shouldRemoveField = (nodeDirectives: FieldNode[\"directives\"]) =>\n    isNonEmptyArray(nodeDirectives) &&\n    nodeDirectives\n      .map(directiveMatcher)\n      .some(\n        (config: RemoveDirectiveConfig | undefined) => config && config.remove\n      );\n\n  const originalFragmentDefsByPath = new Map<string, FragmentDefinitionNode>();\n\n  // Any time the first traversal of the document below makes a change like\n  // removing a fragment (by returning null), this variable should be set to\n  // true. Once it becomes true, it should never be set to false again. If this\n  // variable remains false throughout the traversal, then we can return the\n  // original doc immediately without any modifications.\n  let firstVisitMadeChanges = false;\n\n  const fieldOrInlineFragmentVisitor: EnterLeaveVisitor<\n    FieldNode | InlineFragmentNode\n  > = {\n    enter(node) {\n      if (shouldRemoveField(node.directives)) {\n        firstVisitMadeChanges = true;\n        return null;\n      }\n    },\n  };\n\n  const docWithoutDirectiveSubtrees = visit(doc, {\n    // These two AST node types share the same implementation, defined above.\n    Field: fieldOrInlineFragmentVisitor,\n    InlineFragment: fieldOrInlineFragmentVisitor,\n\n    VariableDefinition: {\n      enter() {\n        // VariableDefinition nodes do not count as variables in use, though\n        // they do contain Variable nodes that might be visited below. To avoid\n        // counting variable declarations as usages, we skip visiting the\n        // contents of this VariableDefinition node by returning false.\n        return false;\n      },\n    },\n\n    Variable: {\n      enter(node, _key, _parent, _path, ancestors) {\n        const inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.variables.add(node.name.value);\n        }\n      },\n    },\n\n    FragmentSpread: {\n      enter(node, _key, _parent, _path, ancestors) {\n        if (shouldRemoveField(node.directives)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n        const inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.fragmentSpreads.add(node.name.value);\n        }\n        // We might like to remove this FragmentSpread by returning null here if\n        // the corresponding FragmentDefinition node is also going to be removed\n        // by the logic below, but we can't control the relative order of those\n        // events, so we have to postpone the removal of dangling FragmentSpread\n        // nodes until after the current visit of the document has finished.\n      },\n    },\n\n    FragmentDefinition: {\n      enter(node, _key, _parent, path) {\n        originalFragmentDefsByPath.set(JSON.stringify(path), node);\n      },\n      leave(node, _key, _parent, path) {\n        const originalNode = originalFragmentDefsByPath.get(\n          JSON.stringify(path)\n        );\n        if (node === originalNode) {\n          // If the FragmentNode received by this leave function is identical to\n          // the one received by the corresponding enter function (above), then\n          // the visitor must not have made any changes within this\n          // FragmentDefinition node. This fragment definition may still be\n          // removed if there are no ...spread references to it, but it won't be\n          // removed just because it has only a __typename field.\n          return node;\n        }\n\n        if (\n          // This logic applies only if the document contains one or more\n          // operations, since removing all fragments from a document containing\n          // only fragments makes the document useless.\n          operationCount > 0 &&\n          node.selectionSet.selections.every(\n            (selection) =>\n              selection.kind === Kind.FIELD &&\n              selection.name.value === \"__typename\"\n          )\n        ) {\n          // This is a somewhat opinionated choice: if a FragmentDefinition ends\n          // up having no fields other than __typename, we remove the whole\n          // fragment definition, and later prune ...spread references to it.\n          getInUseByFragmentName(node.name.value).removed = true;\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      },\n    },\n\n    Directive: {\n      leave(node) {\n        // If a matching directive is found, remove the directive itself. Note\n        // that this does not remove the target (field, argument, etc) of the\n        // directive, but only the directive itself.\n        if (directiveMatcher(node)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      },\n    },\n  });\n\n  if (!firstVisitMadeChanges) {\n    // If our first pass did not change anything about the document, then there\n    // is no cleanup we need to do, and we can return the original doc.\n    return doc;\n  }\n\n  // Utility for making sure inUse.transitiveVars is recursively populated.\n  // Because this logic assumes inUse.fragmentSpreads has been completely\n  // populated and inUse.removed has been set if appropriate,\n  // populateTransitiveVars must be called after that information has been\n  // collected by the first traversal of the document.\n  const populateTransitiveVars = (inUse: InternalInUseInfo) => {\n    if (!inUse.transitiveVars) {\n      inUse.transitiveVars = new Set(inUse.variables);\n      if (!inUse.removed) {\n        inUse.fragmentSpreads.forEach((childFragmentName) => {\n          populateTransitiveVars(\n            getInUseByFragmentName(childFragmentName)\n          ).transitiveVars!.forEach((varName) => {\n            inUse.transitiveVars!.add(varName);\n          });\n        });\n      }\n    }\n    return inUse;\n  };\n\n  // Since we've been keeping track of fragment spreads used by particular\n  // operations and fragment definitions, we now need to compute the set of all\n  // spreads used (transitively) by any operations in the document.\n  const allFragmentNamesUsed = new Set<string>();\n  docWithoutDirectiveSubtrees.definitions.forEach((def) => {\n    if (def.kind === Kind.OPERATION_DEFINITION) {\n      populateTransitiveVars(\n        getInUseByOperationName(def.name && def.name.value)\n      ).fragmentSpreads.forEach((childFragmentName) => {\n        allFragmentNamesUsed.add(childFragmentName);\n      });\n    } else if (\n      def.kind === Kind.FRAGMENT_DEFINITION &&\n      // If there are no operations in the document, then all fragment\n      // definitions count as usages of their own fragment names. This heuristic\n      // prevents accidentally removing all fragment definitions from the\n      // document just because it contains no operations that use the fragments.\n      operationCount === 0 &&\n      !getInUseByFragmentName(def.name.value).removed\n    ) {\n      allFragmentNamesUsed.add(def.name.value);\n    }\n  });\n  // Now that we have added all fragment spreads used by operations to the\n  // allFragmentNamesUsed set, we can complete the set by transitively adding\n  // all fragment spreads used by those fragments, and so on.\n  allFragmentNamesUsed.forEach((fragmentName) => {\n    // Once all the childFragmentName strings added here have been seen already,\n    // the top-level allFragmentNamesUsed.forEach loop will terminate.\n    populateTransitiveVars(\n      getInUseByFragmentName(fragmentName)\n    ).fragmentSpreads.forEach((childFragmentName) => {\n      allFragmentNamesUsed.add(childFragmentName);\n    });\n  });\n\n  const fragmentWillBeRemoved = (fragmentName: string) =>\n    !!(\n      // A fragment definition will be removed if there are no spreads that refer\n      // to it, or the fragment was explicitly removed because it had no fields\n      // other than __typename.\n      (\n        !allFragmentNamesUsed.has(fragmentName) ||\n        getInUseByFragmentName(fragmentName).removed\n      )\n    );\n\n  const enterVisitor: EnterLeaveVisitor<\n    FragmentSpreadNode | FragmentDefinitionNode\n  > = {\n    enter(node) {\n      if (fragmentWillBeRemoved(node.name.value)) {\n        return null;\n      }\n    },\n  };\n\n  return nullIfDocIsEmpty(\n    visit(docWithoutDirectiveSubtrees, {\n      // If the fragment is going to be removed, then leaving any dangling\n      // FragmentSpread nodes with the same name would be a mistake.\n      FragmentSpread: enterVisitor,\n\n      // This is where the fragment definition is actually removed.\n      FragmentDefinition: enterVisitor,\n\n      OperationDefinition: {\n        leave(node) {\n          // Upon leaving each operation in the depth-first AST traversal, prune\n          // any variables that are declared by the operation but unused within.\n          if (node.variableDefinitions) {\n            const usedVariableNames = populateTransitiveVars(\n              // If an operation is anonymous, we use the empty string as its key.\n              getInUseByOperationName(node.name && node.name.value)\n            ).transitiveVars!;\n\n            // According to the GraphQL spec, all variables declared by an\n            // operation must either be used by that operation or used by some\n            // fragment included transitively into that operation:\n            // https://spec.graphql.org/draft/#sec-All-Variables-Used\n            //\n            // To stay on the right side of this validation rule, if/when we\n            // remove the last $var references from an operation or its fragments,\n            // we must also remove the corresponding $var declaration from the\n            // enclosing operation. This pruning applies only to operations and\n            // not fragment definitions, at the moment. Fragments may be able to\n            // declare variables eventually, but today they can only consume them.\n            if (usedVariableNames.size < node.variableDefinitions.length) {\n              return {\n                ...node,\n                variableDefinitions: node.variableDefinitions.filter((varDef) =>\n                  usedVariableNames.has(varDef.variable.name.value)\n                ),\n              };\n            }\n          }\n        },\n      },\n    })\n  );\n}\n\nexport const addTypenameToDocument = Object.assign(\n  function <TNode extends ASTNode>(doc: TNode): TNode {\n    return visit(doc, {\n      SelectionSet: {\n        enter(node, _key, parent) {\n          // Don't add __typename to OperationDefinitions.\n          if (\n            parent &&\n            (parent as OperationDefinitionNode).kind ===\n              Kind.OPERATION_DEFINITION\n          ) {\n            return;\n          }\n\n          // No changes if no selections.\n          const { selections } = node;\n          if (!selections) {\n            return;\n          }\n\n          // If selections already have a __typename, or are part of an\n          // introspection query, do nothing.\n          const skip = selections.some((selection) => {\n            return (\n              isField(selection) &&\n              (selection.name.value === \"__typename\" ||\n                selection.name.value.lastIndexOf(\"__\", 0) === 0)\n            );\n          });\n          if (skip) {\n            return;\n          }\n\n          // If this SelectionSet is @export-ed as an input variable, it should\n          // not have a __typename field (see issue #4691).\n          const field = parent as FieldNode;\n          if (\n            isField(field) &&\n            field.directives &&\n            field.directives.some((d) => d.name.value === \"export\")\n          ) {\n            return;\n          }\n\n          // Create and return a new SelectionSet with a __typename Field.\n          return {\n            ...node,\n            selections: [...selections, TYPENAME_FIELD],\n          };\n        },\n      },\n    });\n  },\n  {\n    added(field: FieldNode): boolean {\n      return field === TYPENAME_FIELD;\n    },\n  }\n);\n\nconst connectionRemoveConfig = {\n  test: (directive: DirectiveNode) => {\n    const willRemove = directive.name.value === \"connection\";\n    if (willRemove) {\n      if (\n        !directive.arguments ||\n        !directive.arguments.some((arg) => arg.name.value === \"key\")\n      ) {\n        invariant.warn(\n          \"Removing an @connection directive even though it does not have a key. \" +\n            \"You may want to use the key parameter to specify a store key.\"\n        );\n      }\n    }\n\n    return willRemove;\n  },\n};\n\nexport function removeConnectionDirectiveFromDocument(doc: DocumentNode) {\n  return removeDirectivesFromDocument(\n    [connectionRemoveConfig],\n    checkDocument(doc)\n  );\n}\n\nfunction hasDirectivesInSelectionSet(\n  directives: GetDirectiveConfig[],\n  selectionSet: SelectionSetNode | undefined,\n  nestedCheck = true\n): boolean {\n  return (\n    !!selectionSet &&\n    selectionSet.selections &&\n    selectionSet.selections.some((selection) =>\n      hasDirectivesInSelection(directives, selection, nestedCheck)\n    )\n  );\n}\n\nfunction hasDirectivesInSelection(\n  directives: GetDirectiveConfig[],\n  selection: SelectionNode,\n  nestedCheck = true\n): boolean {\n  if (!isField(selection)) {\n    return true;\n  }\n\n  if (!selection.directives) {\n    return false;\n  }\n\n  return (\n    selection.directives.some(getDirectiveMatcher(directives)) ||\n    (nestedCheck &&\n      hasDirectivesInSelectionSet(\n        directives,\n        selection.selectionSet,\n        nestedCheck\n      ))\n  );\n}\n\nfunction getArgumentMatcher(config: RemoveArgumentsConfig[]) {\n  return function argumentMatcher(argument: ArgumentNode) {\n    return config.some(\n      (aConfig: RemoveArgumentsConfig) =>\n        argument.value &&\n        argument.value.kind === Kind.VARIABLE &&\n        argument.value.name &&\n        (aConfig.name === argument.value.name.value ||\n          (aConfig.test && aConfig.test(argument)))\n    );\n  };\n}\n\nexport function removeArgumentsFromDocument(\n  config: RemoveArgumentsConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  const argMatcher = getArgumentMatcher(config);\n\n  return nullIfDocIsEmpty(\n    visit(doc, {\n      OperationDefinition: {\n        enter(node) {\n          return {\n            ...node,\n            // Remove matching top level variables definitions.\n            variableDefinitions:\n              node.variableDefinitions ?\n                node.variableDefinitions.filter(\n                  (varDef) =>\n                    !config.some(\n                      (arg) => arg.name === varDef.variable.name.value\n                    )\n                )\n              : [],\n          };\n        },\n      },\n\n      Field: {\n        enter(node) {\n          // If `remove` is set to true for an argument, and an argument match\n          // is found for a field, remove the field as well.\n          const shouldRemoveField = config.some(\n            (argConfig) => argConfig.remove\n          );\n\n          if (shouldRemoveField) {\n            let argMatchCount = 0;\n            if (node.arguments) {\n              node.arguments.forEach((arg) => {\n                if (argMatcher(arg)) {\n                  argMatchCount += 1;\n                }\n              });\n            }\n\n            if (argMatchCount === 1) {\n              return null;\n            }\n          }\n        },\n      },\n\n      Argument: {\n        enter(node) {\n          // Remove all matching arguments.\n          if (argMatcher(node)) {\n            return null;\n          }\n        },\n      },\n    })\n  );\n}\n\nexport function removeFragmentSpreadFromDocument(\n  config: RemoveFragmentSpreadConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  function enter(\n    node: FragmentSpreadNode | FragmentDefinitionNode\n  ): null | void {\n    if (config.some((def) => def.name === node.name.value)) {\n      return null;\n    }\n  }\n\n  return nullIfDocIsEmpty(\n    visit(doc, {\n      FragmentSpread: { enter },\n      FragmentDefinition: { enter },\n    })\n  );\n}\n\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(\n  document: DocumentNode\n): DocumentNode {\n  const definition = getMainDefinition(document);\n  const definitionOperation = (<OperationDefinitionNode>definition).operation;\n\n  if (definitionOperation === \"query\") {\n    // Already a query, so return the existing document.\n    return document;\n  }\n\n  // Build a new query using the selection set of the main operation.\n  const modifiedDoc = visit(document, {\n    OperationDefinition: {\n      enter(node) {\n        return {\n          ...node,\n          operation: \"query\",\n        };\n      },\n    },\n  });\n  return modifiedDoc;\n}\n\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(\n  document: DocumentNode\n): DocumentNode | null {\n  checkDocument(document);\n\n  let modifiedDoc = removeDirectivesFromDocument(\n    [\n      {\n        test: (directive: DirectiveNode) => directive.name.value === \"client\",\n        remove: true,\n      },\n    ],\n    document\n  );\n\n  return modifiedDoc;\n}\n", "import type { DocumentNode } from \"../../core/index.js\";\nimport { getOperationDefinition } from \"./getFromAST.js\";\n\nfunction isOperation(\n  document: DocumentNode,\n  operation: \"query\" | \"mutation\" | \"subscription\"\n) {\n  return getOperationDefinition(document)?.operation === operation;\n}\n\nexport function isMutationOperation(document: DocumentNode) {\n  return isOperation(document, \"mutation\");\n}\n\nexport function isQueryOperation(document: DocumentNode) {\n  return isOperation(document, \"query\");\n}\n\nexport function isSubscriptionOperation(document: DocumentNode) {\n  return isOperation(document, \"subscription\");\n}\n", "import { isNonNullObject } from \"./objects.js\";\n\nconst { hasOwnProperty } = Object.prototype;\n\n// These mergeDeep and mergeDeepArray utilities merge any number of objects\n// together, sharing as much memory as possible with the source objects, while\n// remaining careful to avoid modifying any source objects.\n\n// Logically, the return type of mergeDeep should be the intersection of\n// all the argument types. The binary call signature is by far the most\n// common, but we support 0- through 5-ary as well. After that, the\n// resulting type is just the inferred array element type. Note to nerds:\n// there is a more clever way of doing this that converts the tuple type\n// first to a union type (easy enough: T[number]) and then converts the\n// union to an intersection type using distributive conditional type\n// inference, but that approach has several fatal flaws (boolean becomes\n// true & false, and the inferred type ends up as unknown in many cases),\n// in addition to being nearly impossible to explain/understand.\nexport type TupleToIntersection<T extends any[]> =\n  T extends [infer A] ? A\n  : T extends [infer A, infer B] ? A & B\n  : T extends [infer A, infer B, infer C] ? A & B & C\n  : T extends [infer A, infer B, infer C, infer D] ? A & B & C & D\n  : T extends [infer A, infer B, infer C, infer D, infer E] ? A & B & C & D & E\n  : T extends (infer U)[] ? U\n  : any;\n\nexport function mergeDeep<T extends any[]>(\n  ...sources: T\n): TupleToIntersection<T> {\n  return mergeDeepArray(sources);\n}\n\n// In almost any situation where you could succeed in getting the\n// TypeScript compiler to infer a tuple type for the sources array, you\n// could just use mergeDeep instead of mergeDeepArray, so instead of\n// trying to convert T[] to an intersection type we just infer the array\n// element type, which works perfectly when the sources array has a\n// consistent element type.\nexport function mergeDeepArray<T>(sources: T[]): T {\n  let target = sources[0] || ({} as T);\n  const count = sources.length;\n  if (count > 1) {\n    const merger = new DeepMerger();\n    for (let i = 1; i < count; ++i) {\n      target = merger.merge(target, sources[i]);\n    }\n  }\n  return target;\n}\n\nexport type ReconcilerFunction<TContextArgs extends any[]> = (\n  this: DeepMerger<TContextArgs>,\n  target: Record<string | number, any>,\n  source: Record<string | number, any>,\n  property: string | number,\n  ...context: TContextArgs\n) => any;\n\nconst defaultReconciler: ReconcilerFunction<any[]> = function (\n  target,\n  source,\n  property\n) {\n  return this.merge(target[property], source[property]);\n};\n\nexport class DeepMerger<TContextArgs extends any[]> {\n  constructor(\n    private reconciler: ReconcilerFunction<TContextArgs> = defaultReconciler as any as ReconcilerFunction<TContextArgs>\n  ) {}\n\n  public merge(target: any, source: any, ...context: TContextArgs): any {\n    if (isNonNullObject(source) && isNonNullObject(target)) {\n      Object.keys(source).forEach((sourceKey) => {\n        if (hasOwnProperty.call(target, sourceKey)) {\n          const targetValue = target[sourceKey];\n          if (source[sourceKey] !== targetValue) {\n            const result = this.reconciler(\n              target,\n              source,\n              sourceKey,\n              ...context\n            );\n            // A well-implemented reconciler may return targetValue to indicate\n            // the merge changed nothing about the structure of the target.\n            if (result !== targetValue) {\n              target = this.shallowCopyForMerge(target);\n              target[sourceKey] = result;\n            }\n          }\n        } else {\n          // If there is no collision, the target can safely share memory with\n          // the source, and the recursion can terminate here.\n          target = this.shallowCopyForMerge(target);\n          target[sourceKey] = source[sourceKey];\n        }\n      });\n\n      return target;\n    }\n\n    // If source (or target) is not an object, let source replace target.\n    return source;\n  }\n\n  public isObject = isNonNullObject;\n\n  private pastCopies = new Set<any>();\n\n  public shallowCopyForMerge<T>(value: T): T {\n    if (isNonNullObject(value)) {\n      if (!this.pastCopies.has(value)) {\n        if (Array.isArray(value)) {\n          value = (value as any).slice(0);\n        } else {\n          value = {\n            __proto__: Object.getPrototypeOf(value),\n            ...value,\n          };\n        }\n        this.pastCopies.add(value);\n      }\n    }\n    return value;\n  }\n}\n", "import { __rest } from \"tslib\";\n\nimport type { FieldPolicy, Reference } from \"../../cache/index.js\";\nimport { mergeDeep } from \"../common/mergeDeep.js\";\n\ntype KeyArgs = FieldPolicy<any>[\"keyArgs\"];\n\n// A very basic pagination field policy that always concatenates new\n// results onto the existing array, without examining options.args.\nexport function concatPagination<T = Reference>(\n  keyArgs: KeyArgs = false\n): FieldPolicy<T[]> {\n  return {\n    keyArgs,\n    merge(existing, incoming) {\n      return existing ? [...existing, ...incoming] : incoming;\n    },\n  };\n}\n\n// A basic field policy that uses options.args.{offset,limit} to splice\n// the incoming data into the existing array. If your arguments are called\n// something different (like args.{start,count}), feel free to copy/paste\n// this implementation and make the appropriate changes.\nexport function offsetLimitPagination<T = Reference>(\n  keyArgs: KeyArgs = false\n): FieldPolicy<T[]> {\n  return {\n    keyArgs,\n    merge(existing, incoming, { args }) {\n      const merged = existing ? existing.slice(0) : [];\n\n      if (incoming) {\n        if (args) {\n          // Assume an offset of 0 if args.offset omitted.\n          const { offset = 0 } = args;\n          for (let i = 0; i < incoming.length; ++i) {\n            merged[offset + i] = incoming[i];\n          }\n        } else {\n          // It's unusual (probably a mistake) for a paginated field not\n          // to receive any arguments, so you might prefer to throw an\n          // exception here, instead of recovering by appending incoming\n          // onto the existing array.\n          merged.push(...incoming);\n        }\n      }\n\n      return merged;\n    },\n  };\n}\n\n// Whether TRelayEdge<TNode> is a normalized Reference or a non-normalized\n// object, it needs a .cursor property where the relayStylePagination\n// merge function can store cursor strings taken from pageInfo. Storing an\n// extra reference.cursor property should be safe, and is easier than\n// attempting to update the cursor field of the normalized StoreObject\n// that the reference refers to, or managing edge wrapper objects\n// (something I attempted in #7023, but abandoned because of #7088).\nexport type TRelayEdge<TNode> =\n  | {\n      cursor?: string;\n      node: TNode;\n    }\n  | (Reference & { cursor?: string });\n\nexport type TRelayPageInfo = {\n  hasPreviousPage: boolean;\n  hasNextPage: boolean;\n  startCursor: string;\n  endCursor: string;\n};\n\nexport type TExistingRelay<TNode> = Readonly<{\n  edges: TRelayEdge<TNode>[];\n  pageInfo: TRelayPageInfo;\n}>;\n\nexport type TIncomingRelay<TNode> = {\n  edges?: TRelayEdge<TNode>[];\n  pageInfo?: TRelayPageInfo;\n};\n\nexport type RelayFieldPolicy<TNode> = FieldPolicy<\n  TExistingRelay<TNode> | null,\n  TIncomingRelay<TNode> | null,\n  TIncomingRelay<TNode> | null\n>;\n\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\nexport function relayStylePagination<TNode extends Reference = Reference>(\n  keyArgs: KeyArgs = false\n): RelayFieldPolicy<TNode> {\n  return {\n    keyArgs,\n\n    read(existing, { canRead, readField }) {\n      if (!existing) return existing;\n\n      const edges: TRelayEdge<TNode>[] = [];\n      let firstEdgeCursor = \"\";\n      let lastEdgeCursor = \"\";\n      existing.edges.forEach((edge) => {\n        // Edges themselves could be Reference objects, so it's important\n        // to use readField to access the edge.edge.node property.\n        if (canRead(readField(\"node\", edge))) {\n          edges.push(edge);\n          if (edge.cursor) {\n            firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n            lastEdgeCursor = edge.cursor || lastEdgeCursor;\n          }\n        }\n      });\n\n      if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n        firstEdgeCursor = \"\";\n      }\n\n      const { startCursor, endCursor } = existing.pageInfo || {};\n\n      return {\n        // Some implementations return additional Connection fields, such\n        // as existing.totalCount. These fields are saved by the merge\n        // function, so the read function should also preserve them.\n        ...getExtras(existing),\n        edges,\n        pageInfo: {\n          ...existing.pageInfo,\n          // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n          // to firstEdgeCursor and/or lastEdgeCursor.\n          startCursor: startCursor || firstEdgeCursor,\n          endCursor: endCursor || lastEdgeCursor,\n        },\n      };\n    },\n\n    merge(existing, incoming, { args, isReference, readField }) {\n      if (!existing) {\n        existing = makeEmptyData();\n      }\n\n      if (!incoming) {\n        return existing;\n      }\n\n      const incomingEdges =\n        incoming.edges ?\n          incoming.edges.map((edge) => {\n            if (isReference((edge = { ...edge }))) {\n              // In case edge is a Reference, we read out its cursor field and\n              // store it as an extra property of the Reference object.\n              edge.cursor = readField<string>(\"cursor\", edge);\n            }\n            return edge;\n          })\n        : [];\n\n      if (incoming.pageInfo) {\n        const { pageInfo } = incoming;\n        const { startCursor, endCursor } = pageInfo;\n        const firstEdge = incomingEdges[0];\n        const lastEdge = incomingEdges[incomingEdges.length - 1];\n        // In case we did not request the cursor field for edges in this\n        // query, we can still infer cursors from pageInfo.\n        if (firstEdge && startCursor) {\n          firstEdge.cursor = startCursor;\n        }\n        if (lastEdge && endCursor) {\n          lastEdge.cursor = endCursor;\n        }\n        // Cursors can also come from edges, so we default\n        // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n        const firstCursor = firstEdge && firstEdge.cursor;\n        if (firstCursor && !startCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              startCursor: firstCursor,\n            },\n          });\n        }\n        const lastCursor = lastEdge && lastEdge.cursor;\n        if (lastCursor && !endCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              endCursor: lastCursor,\n            },\n          });\n        }\n      }\n\n      let prefix = existing.edges;\n      let suffix: typeof prefix = [];\n\n      if (args && args.after) {\n        // This comparison does not need to use readField(\"cursor\", edge),\n        // because we stored the cursor field of any Reference edges as an\n        // extra property of the Reference object.\n        const index = prefix.findIndex((edge) => edge.cursor === args.after);\n        if (index >= 0) {\n          prefix = prefix.slice(0, index + 1);\n          // suffix = []; // already true\n        }\n      } else if (args && args.before) {\n        const index = prefix.findIndex((edge) => edge.cursor === args.before);\n        suffix = index < 0 ? prefix : prefix.slice(index);\n        prefix = [];\n      } else if (incoming.edges) {\n        // If we have neither args.after nor args.before, the incoming\n        // edges cannot be spliced into the existing edges, so they must\n        // replace the existing edges. See #6592 for a motivating example.\n        prefix = [];\n      }\n\n      const edges = [...prefix, ...incomingEdges, ...suffix];\n\n      const pageInfo: TRelayPageInfo = {\n        // The ordering of these two ...spreads may be surprising, but it\n        // makes sense because we want to combine PageInfo properties with a\n        // preference for existing values, *unless* the existing values are\n        // overridden by the logic below, which is permitted only when the\n        // incoming page falls at the beginning or end of the data.\n        ...incoming.pageInfo,\n        ...existing.pageInfo,\n      };\n\n      if (incoming.pageInfo) {\n        const {\n          hasPreviousPage,\n          hasNextPage,\n          startCursor,\n          endCursor,\n          ...extras\n        } = incoming.pageInfo;\n\n        // If incoming.pageInfo had any extra non-standard properties,\n        // assume they should take precedence over any existing properties\n        // of the same name, regardless of where this page falls with\n        // respect to the existing data.\n        Object.assign(pageInfo, extras);\n\n        // Keep existing.pageInfo.has{Previous,Next}Page unless the\n        // placement of the incoming edges means incoming.hasPreviousPage\n        // or incoming.hasNextPage should become the new values for those\n        // properties in existing.pageInfo. Note that these updates are\n        // only permitted when the beginning or end of the incoming page\n        // coincides with the beginning or end of the existing data, as\n        // determined using prefix.length and suffix.length.\n        if (!prefix.length) {\n          if (void 0 !== hasPreviousPage)\n            pageInfo.hasPreviousPage = hasPreviousPage;\n          if (void 0 !== startCursor) pageInfo.startCursor = startCursor;\n        }\n        if (!suffix.length) {\n          if (void 0 !== hasNextPage) pageInfo.hasNextPage = hasNextPage;\n          if (void 0 !== endCursor) pageInfo.endCursor = endCursor;\n        }\n      }\n\n      return {\n        ...getExtras(existing),\n        ...getExtras(incoming),\n        edges,\n        pageInfo,\n      };\n    },\n  };\n}\n\n// Returns any unrecognized properties of the given object.\nconst getExtras = (obj: Record<string, any>) => __rest(obj, notExtras);\nconst notExtras = [\"edges\", \"pageInfo\"];\n\nfunction makeEmptyData(): TExistingRelay<any> {\n  return {\n    edges: [],\n    pageInfo: {\n      hasPreviousPage: false,\n      hasNextPage: true,\n      startCursor: \"\",\n      endCursor: \"\",\n    },\n  };\n}\n", "function _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// === Symbol Support ===\nvar hasSymbols = function () {\n  return typeof Symbol === 'function';\n};\n\nvar hasSymbol = function (name) {\n  return hasSymbols() && Boolean(Symbol[name]);\n};\n\nvar getSymbol = function (name) {\n  return hasSymbol(name) ? Symbol[name] : '@@' + name;\n};\n\nif (hasSymbols() && !hasSymbol('observable')) {\n  Symbol.observable = Symbol('observable');\n}\n\nvar SymbolIterator = getSymbol('iterator');\nvar SymbolObservable = getSymbol('observable');\nvar SymbolSpecies = getSymbol('species'); // === Abstract Operations ===\n\nfunction getMethod(obj, key) {\n  var value = obj[key];\n  if (value == null) return undefined;\n  if (typeof value !== 'function') throw new TypeError(value + ' is not a function');\n  return value;\n}\n\nfunction getSpecies(obj) {\n  var ctor = obj.constructor;\n\n  if (ctor !== undefined) {\n    ctor = ctor[SymbolSpecies];\n\n    if (ctor === null) {\n      ctor = undefined;\n    }\n  }\n\n  return ctor !== undefined ? ctor : Observable;\n}\n\nfunction isObservable(x) {\n  return x instanceof Observable; // SPEC: Brand check\n}\n\nfunction hostReportError(e) {\n  if (hostReportError.log) {\n    hostReportError.log(e);\n  } else {\n    setTimeout(function () {\n      throw e;\n    });\n  }\n}\n\nfunction enqueue(fn) {\n  Promise.resolve().then(function () {\n    try {\n      fn();\n    } catch (e) {\n      hostReportError(e);\n    }\n  });\n}\n\nfunction cleanupSubscription(subscription) {\n  var cleanup = subscription._cleanup;\n  if (cleanup === undefined) return;\n  subscription._cleanup = undefined;\n\n  if (!cleanup) {\n    return;\n  }\n\n  try {\n    if (typeof cleanup === 'function') {\n      cleanup();\n    } else {\n      var unsubscribe = getMethod(cleanup, 'unsubscribe');\n\n      if (unsubscribe) {\n        unsubscribe.call(cleanup);\n      }\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n}\n\nfunction closeSubscription(subscription) {\n  subscription._observer = undefined;\n  subscription._queue = undefined;\n  subscription._state = 'closed';\n}\n\nfunction flushSubscription(subscription) {\n  var queue = subscription._queue;\n\n  if (!queue) {\n    return;\n  }\n\n  subscription._queue = undefined;\n  subscription._state = 'ready';\n\n  for (var i = 0; i < queue.length; ++i) {\n    notifySubscription(subscription, queue[i].type, queue[i].value);\n    if (subscription._state === 'closed') break;\n  }\n}\n\nfunction notifySubscription(subscription, type, value) {\n  subscription._state = 'running';\n  var observer = subscription._observer;\n\n  try {\n    var m = getMethod(observer, type);\n\n    switch (type) {\n      case 'next':\n        if (m) m.call(observer, value);\n        break;\n\n      case 'error':\n        closeSubscription(subscription);\n        if (m) m.call(observer, value);else throw value;\n        break;\n\n      case 'complete':\n        closeSubscription(subscription);\n        if (m) m.call(observer);\n        break;\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n\n  if (subscription._state === 'closed') cleanupSubscription(subscription);else if (subscription._state === 'running') subscription._state = 'ready';\n}\n\nfunction onNotify(subscription, type, value) {\n  if (subscription._state === 'closed') return;\n\n  if (subscription._state === 'buffering') {\n    subscription._queue.push({\n      type: type,\n      value: value\n    });\n\n    return;\n  }\n\n  if (subscription._state !== 'ready') {\n    subscription._state = 'buffering';\n    subscription._queue = [{\n      type: type,\n      value: value\n    }];\n    enqueue(function () {\n      return flushSubscription(subscription);\n    });\n    return;\n  }\n\n  notifySubscription(subscription, type, value);\n}\n\nvar Subscription = /*#__PURE__*/function () {\n  function Subscription(observer, subscriber) {\n    // ASSERT: observer is an object\n    // ASSERT: subscriber is callable\n    this._cleanup = undefined;\n    this._observer = observer;\n    this._queue = undefined;\n    this._state = 'initializing';\n    var subscriptionObserver = new SubscriptionObserver(this);\n\n    try {\n      this._cleanup = subscriber.call(undefined, subscriptionObserver);\n    } catch (e) {\n      subscriptionObserver.error(e);\n    }\n\n    if (this._state === 'initializing') this._state = 'ready';\n  }\n\n  var _proto = Subscription.prototype;\n\n  _proto.unsubscribe = function unsubscribe() {\n    if (this._state !== 'closed') {\n      closeSubscription(this);\n      cleanupSubscription(this);\n    }\n  };\n\n  _createClass(Subscription, [{\n    key: \"closed\",\n    get: function () {\n      return this._state === 'closed';\n    }\n  }]);\n\n  return Subscription;\n}();\n\nvar SubscriptionObserver = /*#__PURE__*/function () {\n  function SubscriptionObserver(subscription) {\n    this._subscription = subscription;\n  }\n\n  var _proto2 = SubscriptionObserver.prototype;\n\n  _proto2.next = function next(value) {\n    onNotify(this._subscription, 'next', value);\n  };\n\n  _proto2.error = function error(value) {\n    onNotify(this._subscription, 'error', value);\n  };\n\n  _proto2.complete = function complete() {\n    onNotify(this._subscription, 'complete');\n  };\n\n  _createClass(SubscriptionObserver, [{\n    key: \"closed\",\n    get: function () {\n      return this._subscription._state === 'closed';\n    }\n  }]);\n\n  return SubscriptionObserver;\n}();\n\nvar Observable = /*#__PURE__*/function () {\n  function Observable(subscriber) {\n    if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');\n    if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');\n    this._subscriber = subscriber;\n  }\n\n  var _proto3 = Observable.prototype;\n\n  _proto3.subscribe = function subscribe(observer) {\n    if (typeof observer !== 'object' || observer === null) {\n      observer = {\n        next: observer,\n        error: arguments[1],\n        complete: arguments[2]\n      };\n    }\n\n    return new Subscription(observer, this._subscriber);\n  };\n\n  _proto3.forEach = function forEach(fn) {\n    var _this = this;\n\n    return new Promise(function (resolve, reject) {\n      if (typeof fn !== 'function') {\n        reject(new TypeError(fn + ' is not a function'));\n        return;\n      }\n\n      function done() {\n        subscription.unsubscribe();\n        resolve();\n      }\n\n      var subscription = _this.subscribe({\n        next: function (value) {\n          try {\n            fn(value, done);\n          } catch (e) {\n            reject(e);\n            subscription.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n    });\n  };\n\n  _proto3.map = function map(fn) {\n    var _this2 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this2.subscribe({\n        next: function (value) {\n          try {\n            value = fn(value);\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.filter = function filter(fn) {\n    var _this3 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this3.subscribe({\n        next: function (value) {\n          try {\n            if (!fn(value)) return;\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.reduce = function reduce(fn) {\n    var _this4 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    var hasSeed = arguments.length > 1;\n    var hasValue = false;\n    var seed = arguments[1];\n    var acc = seed;\n    return new C(function (observer) {\n      return _this4.subscribe({\n        next: function (value) {\n          var first = !hasValue;\n          hasValue = true;\n\n          if (!first || hasSeed) {\n            try {\n              acc = fn(acc, value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          } else {\n            acc = value;\n          }\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));\n          observer.next(acc);\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.concat = function concat() {\n    var _this5 = this;\n\n    for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n      sources[_key] = arguments[_key];\n    }\n\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscription;\n      var index = 0;\n\n      function startNext(next) {\n        subscription = next.subscribe({\n          next: function (v) {\n            observer.next(v);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            if (index === sources.length) {\n              subscription = undefined;\n              observer.complete();\n            } else {\n              startNext(C.from(sources[index++]));\n            }\n          }\n        });\n      }\n\n      startNext(_this5);\n      return function () {\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = undefined;\n        }\n      };\n    });\n  };\n\n  _proto3.flatMap = function flatMap(fn) {\n    var _this6 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscriptions = [];\n\n      var outer = _this6.subscribe({\n        next: function (value) {\n          if (fn) {\n            try {\n              value = fn(value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          }\n\n          var inner = C.from(value).subscribe({\n            next: function (value) {\n              observer.next(value);\n            },\n            error: function (e) {\n              observer.error(e);\n            },\n            complete: function () {\n              var i = subscriptions.indexOf(inner);\n              if (i >= 0) subscriptions.splice(i, 1);\n              completeIfDone();\n            }\n          });\n          subscriptions.push(inner);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          completeIfDone();\n        }\n      });\n\n      function completeIfDone() {\n        if (outer.closed && subscriptions.length === 0) observer.complete();\n      }\n\n      return function () {\n        subscriptions.forEach(function (s) {\n          return s.unsubscribe();\n        });\n        outer.unsubscribe();\n      };\n    });\n  };\n\n  _proto3[SymbolObservable] = function () {\n    return this;\n  };\n\n  Observable.from = function from(x) {\n    var C = typeof this === 'function' ? this : Observable;\n    if (x == null) throw new TypeError(x + ' is not an object');\n    var method = getMethod(x, SymbolObservable);\n\n    if (method) {\n      var observable = method.call(x);\n      if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');\n      if (isObservable(observable) && observable.constructor === C) return observable;\n      return new C(function (observer) {\n        return observable.subscribe(observer);\n      });\n    }\n\n    if (hasSymbol('iterator')) {\n      method = getMethod(x, SymbolIterator);\n\n      if (method) {\n        return new C(function (observer) {\n          enqueue(function () {\n            if (observer.closed) return;\n\n            for (var _iterator = _createForOfIteratorHelperLoose(method.call(x)), _step; !(_step = _iterator()).done;) {\n              var item = _step.value;\n              observer.next(item);\n              if (observer.closed) return;\n            }\n\n            observer.complete();\n          });\n        });\n      }\n    }\n\n    if (Array.isArray(x)) {\n      return new C(function (observer) {\n        enqueue(function () {\n          if (observer.closed) return;\n\n          for (var i = 0; i < x.length; ++i) {\n            observer.next(x[i]);\n            if (observer.closed) return;\n          }\n\n          observer.complete();\n        });\n      });\n    }\n\n    throw new TypeError(x + ' is not observable');\n  };\n\n  Observable.of = function of() {\n    for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      items[_key2] = arguments[_key2];\n    }\n\n    var C = typeof this === 'function' ? this : Observable;\n    return new C(function (observer) {\n      enqueue(function () {\n        if (observer.closed) return;\n\n        for (var i = 0; i < items.length; ++i) {\n          observer.next(items[i]);\n          if (observer.closed) return;\n        }\n\n        observer.complete();\n      });\n    });\n  };\n\n  _createClass(Observable, null, [{\n    key: SymbolSpecies,\n    get: function () {\n      return this;\n    }\n  }]);\n\n  return Observable;\n}();\n\nif (hasSymbols()) {\n  Object.defineProperty(Observable, Symbol('extensions'), {\n    value: {\n      symbol: SymbolObservable,\n      hostReportError: hostReportError\n    },\n    configurable: true\n  });\n}\n\nexport { Observable };\n", "export default function symbolObservablePonyfill(root) {\n\tvar result;\n\tvar Symbol = root.Symbol;\n\n\tif (typeof Symbol === 'function') {\n\t\tif (Symbol.observable) {\n\t\t\tresult = Symbol.observable;\n\t\t} else {\n\n\t\t\tif (typeof Symbol.for === 'function') {\n\t\t\t\t// This just needs to be something that won't trample other user's Symbol.for use\n\t\t\t\t// It also will guide people to the source of their issues, if this is problematic.\n\t\t\t\t// META: It's a resource locator!\n\t\t\t\tresult = Symbol.for('https://github.com/benlesh/symbol-observable');\n\t\t\t} else {\n\t\t\t\t// Symbol.for didn't exist! The best we can do at this point is a totally \n\t\t\t\t// unique symbol. Note that the string argument here is a descriptor, not\n\t\t\t\t// an identifier. This symbol is unique.\n\t\t\t\tresult = Symbol('https://github.com/benlesh/symbol-observable');\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tSymbol.observable = result;\n\t\t\t} catch (err) {\n\t\t\t\t// Do nothing. In some environments, users have frozen `Symbol` for security reasons,\n\t\t\t\t// if it is frozen assigning to it will throw. In this case, we don't care, because\n\t\t\t\t// they will need to use the returned value from the ponyfill.\n\t\t\t}\n\t\t}\n\t} else {\n\t\tresult = '@@observable';\n\t}\n\n\treturn result;\n};\n", "/* global window */\nimport ponyfill from './ponyfill.js';\n\nvar root;\n\nif (typeof self !== 'undefined') {\n  root = self;\n} else if (typeof window !== 'undefined') {\n  root = window;\n} else if (typeof global !== 'undefined') {\n  root = global;\n} else if (typeof module !== 'undefined') {\n  root = module;\n} else {\n  root = Function('return this')();\n}\n\nvar result = ponyfill(root);\nexport default result;\n", "import type {\n  Observer,\n  Subscription as ObservableSubscription,\n  Subscriber,\n} from \"zen-observable-ts\";\nimport { Observable } from \"zen-observable-ts\";\n\n// This simplified polyfill attempts to follow the ECMAScript Observable\n// proposal (https://github.com/zenparsing/es-observable)\nimport \"symbol-observable\";\n\nexport type { Observer, ObservableSubscription, Subscriber };\n\n// The zen-observable package defines Observable.prototype[Symbol.observable]\n// when Symbol is supported, but RxJS interop depends on also setting this fake\n// '@@observable' string as a polyfill for Symbol.observable.\nconst { prototype } = Observable;\nconst fakeObsSymbol = \"@@observable\" as keyof typeof prototype;\nif (!prototype[fakeObsSymbol]) {\n  // @ts-expect-error\n  prototype[fakeObsSymbol] = function () {\n    return this;\n  };\n}\n\nexport { Observable };\n", "export interface PendingPromise<TValue> extends Promise<TValue> {\n  status: \"pending\";\n}\n\nexport interface FulfilledPromise<TValue> extends Promise<TValue> {\n  status: \"fulfilled\";\n  value: TValue;\n}\n\nexport interface RejectedPromise<TValue> extends Promise<TValue> {\n  status: \"rejected\";\n  reason: unknown;\n}\n\nexport type PromiseWithState<TValue> =\n  | PendingPromise<TValue>\n  | FulfilledPromise<TValue>\n  | RejectedPromise<TValue>;\n\nexport function createFulfilledPromise<TValue>(value: TValue) {\n  const promise = Promise.resolve(value) as FulfilledPromise<TValue>;\n\n  promise.status = \"fulfilled\";\n  promise.value = value;\n\n  return promise;\n}\n\nexport function createRejectedPromise<TValue = unknown>(reason: unknown) {\n  const promise = Promise.reject(reason) as RejectedPromise<TValue>;\n\n  // prevent potential edge cases leaking unhandled error rejections\n  promise.catch(() => {});\n\n  promise.status = \"rejected\";\n  promise.reason = reason;\n\n  return promise;\n}\n\nexport function isStatefulPromise<TValue>(\n  promise: Promise<TValue>\n): promise is PromiseWithState<TValue> {\n  return \"status\" in promise;\n}\n\nexport function wrapPromiseWithState<TValue>(\n  promise: Promise<TValue>\n): PromiseWithState<TValue> {\n  if (isStatefulPromise(promise)) {\n    return promise;\n  }\n\n  const pendingPromise = promise as PendingPromise<TValue>;\n  pendingPromise.status = \"pending\";\n\n  pendingPromise.then(\n    (value) => {\n      if (pendingPromise.status === \"pending\") {\n        const fulfilledPromise =\n          pendingPromise as unknown as FulfilledPromise<TValue>;\n\n        fulfilledPromise.status = \"fulfilled\";\n        fulfilledPromise.value = value;\n      }\n    },\n    (reason: unknown) => {\n      if (pendingPromise.status === \"pending\") {\n        const rejectedPromise =\n          pendingPromise as unknown as RejectedPromise<TValue>;\n\n        rejectedPromise.status = \"rejected\";\n        rejectedPromise.reason = reason;\n      }\n    }\n  );\n\n  return promise as PromiseWithState<TValue>;\n}\n", "const { toString } = Object.prototype;\n\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep<T>(value: T): T {\n  return cloneDeepHelper(value);\n}\n\nfunction cloneDeepHelper<T>(val: T, seen?: Map<any, any>): T {\n  switch (toString.call(val)) {\n    case \"[object Array]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      const copy: T & any[] = (val as any).slice(0);\n      seen.set(val, copy);\n      copy.forEach(function (child, i) {\n        copy[i] = cloneDeepHelper(child, seen);\n      });\n      return copy;\n    }\n\n    case \"[object Object]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n      // possible in all JS environments, so we will assume they exist/work.\n      const copy = Object.create(Object.getPrototypeOf(val));\n      seen.set(val, copy);\n      Object.keys(val as T & Record<string, any>).forEach((key) => {\n        copy[key] = cloneDeepHelper((val as any)[key], seen);\n      });\n      return copy;\n    }\n\n    default:\n      return val;\n  }\n}\n", "import { isNonNullObject } from \"./objects.js\";\n\nfunction deepFreeze(value: any) {\n  const workSet = new Set([value]);\n  workSet.forEach((obj) => {\n    if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n      Object.getOwnPropertyNames(obj).forEach((name) => {\n        if (isNonNullObject(obj[name])) workSet.add(obj[name]);\n      });\n    }\n  });\n  return value;\n}\n\nfunction shallowFreeze<T extends object>(obj: T): T | null {\n  if (__DEV__ && !Object.isFrozen(obj)) {\n    try {\n      Object.freeze(obj);\n    } catch (e) {\n      // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n      // they all throw a TypeError when you try, so we re-throw any exceptions\n      // that are not TypeErrors, since that would be unexpected.\n      if (e instanceof TypeError) return null;\n      throw e;\n    }\n  }\n  return obj;\n}\n\nexport function maybeDeepFreeze<T>(obj: T): T {\n  if (__DEV__) {\n    deepFreeze(obj);\n  }\n  return obj;\n}\n", "import type { Observer } from \"./Observable.js\";\n\nexport function iterateObserversSafely<E, A>(\n  observers: Set<Observer<E>>,\n  method: keyof Observer<E>,\n  argument?: A\n) {\n  // In case observers is modified during iteration, we need to commit to the\n  // original elements, which also provides an opportunity to filter them down\n  // to just the observers with the given method.\n  const observersWithMethod: Observer<E>[] = [];\n  observers.forEach((obs) => obs[method] && observersWithMethod.push(obs));\n  observersWithMethod.forEach((obs) => (obs as any)[method](argument));\n}\n", "import type { Observer } from \"./Observable.js\";\nimport { Observable } from \"./Observable.js\";\n\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap<V, R>(\n  observable: Observable<V>,\n  mapFn: (value: V) => R | PromiseLike<R>,\n  catchFn?: (error: any) => R | PromiseLike<R>\n): Observable<R> {\n  return new Observable<R>((observer) => {\n    let promiseQueue = {\n      // Normally we would initialize promiseQueue to Promise.resolve(), but\n      // in this case, for backwards compatibility, we need to be careful to\n      // invoke the first callback synchronously.\n      then(callback: () => any) {\n        return new Promise((resolve) => resolve(callback()));\n      },\n    } as Promise<void>;\n\n    function makeCallback(\n      examiner: typeof mapFn | typeof catchFn,\n      key: \"next\" | \"error\"\n    ): (arg: any) => void {\n      return (arg) => {\n        if (examiner) {\n          const both = () =>\n            // If the observer is closed, we don't want to continue calling the\n            // mapping function - it's result will be swallowed anyways.\n            observer.closed ?\n              /* will be swallowed */ (0 as any)\n            : examiner(arg);\n\n          promiseQueue = promiseQueue.then(both, both).then(\n            (result) => observer.next(result),\n            (error) => observer.error(error)\n          );\n        } else {\n          observer[key](arg);\n        }\n      };\n    }\n\n    const handler: Observer<V> = {\n      next: makeCallback(mapFn, \"next\"),\n      error: makeCallback(catchFn, \"error\"),\n      complete() {\n        // no need to reassign `promiseQueue`, after `observer.complete`,\n        // the observer will be closed and short-circuit everything anyways\n        /*promiseQueue = */ promiseQueue.then(() => observer.complete());\n      },\n    };\n\n    const sub = observable.subscribe(handler);\n    return () => sub.unsubscribe();\n  });\n}\n", "import { Observable } from \"./Observable.js\";\nimport { canUseSymbol } from \"../common/canUse.js\";\n\n// Generic implementations of Observable.prototype methods like map and\n// filter need to know how to create a new Observable from an Observable\n// subclass (like Concast or ObservableQuery). Those methods assume\n// (perhaps unwisely?) that they can call the subtype's constructor with a\n// Subscriber function, even though the subclass constructor might expect\n// different parameters. Defining this static Symbol.species property on\n// the subclass is a hint to generic Observable code to use the default\n// constructor instead of trying to do `new Subclass(observer => ...)`.\nexport function fixObservableSubclass<\n  S extends new (...args: any[]) => Observable<any>,\n>(subclass: S): S {\n  function set(key: symbol | string) {\n    // Object.defineProperty is necessary because the Symbol.species\n    // property is a getter by default in modern JS environments, so we\n    // can't assign to it with a normal assignment expression.\n    Object.defineProperty(subclass, key, { value: Observable });\n  }\n  if (canUseSymbol && Symbol.species) {\n    set(Symbol.species);\n  }\n  // The \"@@species\" string is used as a fake Symbol.species value in some\n  // polyfill systems (including the SymbolSpecies variable used by\n  // zen-observable), so we should set it as well, to be safe.\n  set(\"@@species\");\n  return subclass;\n}\n", "import type {\n  Observer,\n  ObservableSubscription,\n  Subscriber,\n} from \"./Observable.js\";\nimport { Observable } from \"./Observable.js\";\nimport { iterateObserversSafely } from \"./iteration.js\";\nimport { fixObservableSubclass } from \"./subclassing.js\";\n\ntype MaybeAsync<T> = T | PromiseLike<T>;\n\nfunction isPromiseLike<T>(value: MaybeAsync<T>): value is PromiseLike<T> {\n  return value && typeof (value as any).then === \"function\";\n}\n\n// Any individual Source<T> can be an Observable<T> or a promise for one.\ntype Source<T> = MaybeAsync<Observable<T>>;\n\nexport type ConcastSourcesIterable<T> = Iterable<Source<T>>;\nexport type ConcastSourcesArray<T> = Array<Source<T>>;\n\n// A Concast<T> observable concatenates the given sources into a single\n// non-overlapping sequence of Ts, automatically unwrapping any promises,\n// and broadcasts the T elements of that sequence to any number of\n// subscribers, all without creating a bunch of intermediary Observable\n// wrapper objects.\n//\n// Even though any number of observers can subscribe to the Concast, each\n// source observable is guaranteed to receive at most one subscribe call,\n// and the results are multicast to all observers.\n//\n// In addition to broadcasting every next/error message to this.observers,\n// the Concast stores the most recent message using this.latest, so any\n// new observers can immediately receive the latest message, even if it\n// was originally delivered in the past. This behavior means we can assume\n// every active observer in this.observers has received the same most\n// recent message.\n//\n// With the exception of this.latest replay, a Concast is a \"hot\"\n// observable in the sense that it does not replay past results from the\n// beginning of time for each new observer.\n//\n// Could we have used some existing RxJS class instead? Concast<T> is\n// similar to a BehaviorSubject<T>, because it is multicast and redelivers\n// the latest next/error message to new subscribers. Unlike Subject<T>,\n// Concast<T> does not expose an Observer<T> interface (this.handlers is\n// intentionally private), since Concast<T> gets its inputs from the\n// concatenated sources. If we ever switch to RxJS, there may be some\n// value in reusing their code, but for now we use zen-observable, which\n// does not contain any Subject implementations.\nexport class Concast<T> extends Observable<T> {\n  // Active observers receiving broadcast messages. Thanks to this.latest,\n  // we can assume all observers in this Set have received the same most\n  // recent message, though possibly at different times in the past.\n  private observers = new Set<Observer<T>>();\n\n  // This property starts off undefined to indicate the initial\n  // subscription has not yet begun, then points to each source\n  // subscription in turn, and finally becomes null after the sources have\n  // been exhausted. After that, it stays null.\n  private sub?: ObservableSubscription | null;\n\n  // Not only can the individual elements of the iterable be promises, but\n  // also the iterable itself can be wrapped in a promise.\n  constructor(sources: MaybeAsync<ConcastSourcesIterable<T>> | Subscriber<T>) {\n    super((observer) => {\n      this.addObserver(observer);\n      return () => this.removeObserver(observer);\n    });\n\n    // Suppress rejection warnings for this.promise, since it's perfectly\n    // acceptable to pay no attention to this.promise if you're consuming\n    // the results through the normal observable API.\n    this.promise.catch((_) => {});\n\n    // If someone accidentally tries to create a Concast using a subscriber\n    // function, recover by creating an Observable from that subscriber and\n    // using it as the source.\n    if (typeof sources === \"function\") {\n      sources = [new Observable(sources)];\n    }\n\n    if (isPromiseLike(sources)) {\n      sources.then((iterable) => this.start(iterable), this.handlers.error);\n    } else {\n      this.start(sources);\n    }\n  }\n\n  // A consumable array of source observables, incrementally consumed each time\n  // this.handlers.complete is called. This private field is not initialized\n  // until the concast.start method is called, which can happen asynchronously\n  // if a Promise is passed to the Concast constructor, so undefined is a\n  // possible value for this.sources before concast.start is called.\n  private sources: Source<T>[] | undefined;\n\n  private start(sources: ConcastSourcesIterable<T>) {\n    if (this.sub !== void 0) return;\n\n    // In practice, sources is most often simply an Array of observables.\n    // TODO Consider using sources[Symbol.iterator]() to take advantage\n    // of the laziness of non-Array iterables.\n    this.sources = Array.from(sources);\n\n    // Calling this.handlers.complete() kicks off consumption of the first\n    // source observable. It's tempting to do this step lazily in\n    // addObserver, but this.promise can be accessed without calling\n    // addObserver, so consumption needs to begin eagerly.\n    this.handlers.complete();\n  }\n\n  private deliverLastMessage(observer: Observer<T>) {\n    if (this.latest) {\n      const nextOrError = this.latest[0];\n      const method = observer[nextOrError];\n      if (method) {\n        method.call(observer, this.latest[1]);\n      }\n      // If the subscription is already closed, and the last message was\n      // a 'next' message, simulate delivery of the final 'complete'\n      // message again.\n      if (this.sub === null && nextOrError === \"next\" && observer.complete) {\n        observer.complete();\n      }\n    }\n  }\n\n  public addObserver(observer: Observer<T>) {\n    if (!this.observers.has(observer)) {\n      // Immediately deliver the most recent message, so we can always\n      // be sure all observers have the latest information.\n      this.deliverLastMessage(observer);\n      this.observers.add(observer);\n    }\n  }\n\n  public removeObserver(observer: Observer<T>) {\n    if (this.observers.delete(observer) && this.observers.size < 1) {\n      // In case there are still any listeners in this.nextResultListeners, and\n      // no error or completion has been broadcast yet, make sure those\n      // observers have a chance to run and then remove themselves from\n      // this.observers.\n      this.handlers.complete();\n    }\n  }\n\n  // Any Concast object can be trivially converted to a Promise, without\n  // having to create a new wrapper Observable. This promise provides an\n  // easy way to observe the final state of the Concast.\n  private resolve!: (result?: T | PromiseLike<T>) => void;\n  private reject!: (reason: any) => void;\n  public readonly promise = new Promise<T | undefined>((resolve, reject) => {\n    this.resolve = resolve;\n    this.reject = reject;\n  });\n\n  // Name and argument of the most recently invoked observer method, used\n  // to deliver latest results immediately to new observers.\n  private latest?: [\"next\", T] | [\"error\", any];\n\n  // Bound handler functions that can be reused for every internal\n  // subscription.\n  private handlers = {\n    next: (result: T) => {\n      if (this.sub !== null) {\n        this.latest = [\"next\", result];\n        this.notify(\"next\", result);\n        iterateObserversSafely(this.observers, \"next\", result);\n      }\n    },\n\n    error: (error: any) => {\n      const { sub } = this;\n      if (sub !== null) {\n        // Delay unsubscribing from the underlying subscription slightly,\n        // so that immediately subscribing another observer can keep the\n        // subscription active.\n        if (sub) setTimeout(() => sub.unsubscribe());\n        this.sub = null;\n        this.latest = [\"error\", error];\n        this.reject(error);\n        this.notify(\"error\", error);\n        iterateObserversSafely(this.observers, \"error\", error);\n      }\n    },\n\n    complete: () => {\n      const { sub, sources = [] } = this;\n      if (sub !== null) {\n        // If complete is called before concast.start, this.sources may be\n        // undefined, so we use a default value of [] for sources. That works\n        // here because it falls into the if (!value) {...} block, which\n        // appropriately terminates the Concast, even if this.sources might\n        // eventually have been initialized to a non-empty array.\n        const value = sources.shift();\n        if (!value) {\n          if (sub) setTimeout(() => sub.unsubscribe());\n          this.sub = null;\n          if (this.latest && this.latest[0] === \"next\") {\n            this.resolve(this.latest[1]);\n          } else {\n            this.resolve();\n          }\n          this.notify(\"complete\");\n          // We do not store this.latest = [\"complete\"], because doing so\n          // discards useful information about the previous next (or\n          // error) message. Instead, if new observers subscribe after\n          // this Concast has completed, they will receive the final\n          // 'next' message (unless there was an error) immediately\n          // followed by a 'complete' message (see addObserver).\n          iterateObserversSafely(this.observers, \"complete\");\n        } else if (isPromiseLike(value)) {\n          value.then(\n            (obs) => (this.sub = obs.subscribe(this.handlers)),\n            this.handlers.error\n          );\n        } else {\n          this.sub = value.subscribe(this.handlers);\n        }\n      }\n    },\n  };\n\n  private nextResultListeners = new Set<NextResultListener>();\n\n  private notify(\n    method: Parameters<NextResultListener>[0],\n    arg?: Parameters<NextResultListener>[1]\n  ) {\n    const { nextResultListeners } = this;\n    if (nextResultListeners.size) {\n      // Replacing this.nextResultListeners first ensures it does not grow while\n      // we are iterating over it, potentially leading to infinite loops.\n      this.nextResultListeners = new Set();\n      nextResultListeners.forEach((listener) => listener(method, arg));\n    }\n  }\n\n  // We need a way to run callbacks just *before* the next result (or error or\n  // completion) is delivered by this Concast, so we can be sure any code that\n  // runs as a result of delivering that result/error observes the effects of\n  // running the callback(s). It was tempting to reuse the Observer type instead\n  // of introducing NextResultListener, but that messes with the sizing and\n  // maintenance of this.observers, and ends up being more code overall.\n  beforeNext(callback: NextResultListener) {\n    let called = false;\n    this.nextResultListeners.add((method, arg) => {\n      if (!called) {\n        called = true;\n        callback(method, arg);\n      }\n    });\n  }\n\n  // A public way to abort observation and broadcast.\n  public cancel = (reason: any) => {\n    this.reject(reason);\n    this.sources = [];\n    this.handlers.error(reason);\n  };\n}\n\ntype NextResultListener = (\n  method: \"next\" | \"error\" | \"complete\",\n  arg?: any\n) => any;\n\n// Necessary because the Concast constructor has a different signature\n// than the Observable constructor.\nfixObservableSubclass(Concast);\n", "import type {\n  ExecutionPatchIncrementalResult,\n  ExecutionPatchInitialResult,\n  ExecutionPatchResult,\n  ApolloPayloadResult,\n  FetchResult,\n} from \"../../link/core/index.js\";\nimport { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\n\nexport function isExecutionPatchIncrementalResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchIncrementalResult {\n  return \"incremental\" in value;\n}\n\nexport function isExecutionPatchInitialResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchInitialResult<T> {\n  return \"hasNext\" in value && \"data\" in value;\n}\n\nexport function isExecutionPatchResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchResult<T> {\n  return (\n    isExecutionPatchIncrementalResult(value) ||\n    isExecutionPatchInitialResult(value)\n  );\n}\n\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(\n  value: unknown\n): value is ApolloPayloadResult {\n  return isNonNullObject(value) && \"payload\" in value;\n}\n\nexport function mergeIncrementalData<TData extends object>(\n  prevResult: TData,\n  result: ExecutionPatchResult<TData>\n) {\n  let mergedData = prevResult;\n  const merger = new DeepMerger();\n  if (\n    isExecutionPatchIncrementalResult(result) &&\n    isNonEmptyArray(result.incremental)\n  ) {\n    result.incremental.forEach(({ data, path }) => {\n      for (let i = path.length - 1; i >= 0; --i) {\n        const key = path[i];\n        const isNumericKey = !isNaN(+key);\n        const parent: Record<string | number, any> = isNumericKey ? [] : {};\n        parent[key] = data;\n        data = parent as typeof data;\n      }\n      mergedData = merger.merge(mergedData, data);\n    });\n  }\n  return mergedData as TData;\n}\n", "import type { FetchResult } from \"../../link/core/index.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\n\nexport function graphQLResultHasError<T>(result: FetchResult<T>): boolean {\n  const errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\n\nexport function getGraphQLErrorsFromResult<T>(result: FetchResult<T>) {\n  const graphQLErrors =\n    isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n\n  if (\n    isExecutionPatchIncrementalResult(result) &&\n    isNonEmptyArray(result.incremental)\n  ) {\n    result.incremental.forEach((incrementalResult) => {\n      if (incrementalResult.errors) {\n        graphQLErrors.push(...incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}\n", "import type { TupleToIntersection } from \"./mergeDeep.js\";\n\n/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact<TArgs extends any[]>(\n  ...objects: TArgs\n): TupleToIntersection<TArgs> {\n  const result = Object.create(null);\n\n  objects.forEach((obj) => {\n    if (!obj) return;\n    Object.keys(obj).forEach((key) => {\n      const value = (obj as any)[key];\n      if (value !== void 0) {\n        result[key] = value;\n      }\n    });\n  });\n\n  return result;\n}\n", "import type {\n  QueryOptions,\n  WatchQueryOptions,\n  MutationOptions,\n  OperationVariables,\n} from \"../../core/index.js\";\n\nimport { compact } from \"./compact.js\";\n\ntype OptionsUnion<TData, TVariables extends OperationVariables, TContext> =\n  | WatchQueryOptions<TVariables, TData>\n  | QueryOptions<TVariables, TData>\n  | MutationOptions<TData, TVariables, TContext, any>;\n\nexport function mergeOptions<\n  TDefaultOptions extends Partial<OptionsUnion<any, any, any>>,\n  TOptions extends TDefaultOptions,\n>(\n  defaults: TDefaultOptions | Partial<TDefaultOptions> | undefined,\n  options: TOptions | Partial<TOptions>\n): TOptions & TDefaultOptions {\n  return compact(\n    defaults,\n    options,\n    options.variables && {\n      variables: compact({\n        ...(defaults && defaults.variables),\n        ...options.variables,\n      }),\n    }\n  );\n}\n", "import type { DeepOmit } from \"../types/DeepOmit.js\";\nimport { isPlainObject } from \"./objects.js\";\n\nexport function omitDeep<T, K extends string>(value: T, key: K) {\n  return __omitDeep(value, key);\n}\n\nfunction __omitDeep<T, K extends string>(\n  value: T,\n  key: K,\n  known = new Map<any, any>()\n): DeepOmit<T, K> {\n  if (known.has(value)) {\n    return known.get(value);\n  }\n\n  let modified = false;\n\n  if (Array.isArray(value)) {\n    const array: any[] = [];\n    known.set(value, array);\n\n    value.forEach((value, index) => {\n      const result = __omitDeep(value, key, known);\n      modified ||= result !== value;\n\n      array[index] = result;\n    });\n\n    if (modified) {\n      return array as DeepOmit<T, K>;\n    }\n  } else if (isPlainObject(value)) {\n    const obj = Object.create(Object.getPrototypeOf(value));\n    known.set(value, obj);\n\n    Object.keys(value).forEach((k) => {\n      if (k === key) {\n        modified = true;\n        return;\n      }\n\n      const result = __omitDeep(value[k], key, known);\n      modified ||= result !== value[k];\n\n      obj[k] = result;\n    });\n\n    if (modified) {\n      return obj;\n    }\n  }\n\n  return value as DeepOmit<T, K>;\n}\n", "import { omitDeep } from \"./omitDeep.js\";\n\nexport function stripTypename<T>(value: T) {\n  return omitDeep(value, \"__typename\");\n}\n", "export {};\n//# sourceMappingURL=IsStrictlyAny.js.map", "export { DEV, maybe } from \"./globals/index.js\";\n\nexport type {\n  DirectiveInfo,\n  InclusionDirectives,\n} from \"./graphql/directives.js\";\nexport {\n  shouldInclude,\n  hasDirectives,\n  hasAnyDirectives,\n  hasAllDirectives,\n  hasClientExports,\n  getDirectiveNames,\n  getInclusionDirectives,\n} from \"./graphql/directives.js\";\n\nexport type { DocumentTransformCacheKey } from \"./graphql/DocumentTransform.js\";\nexport { DocumentTransform } from \"./graphql/DocumentTransform.js\";\n\nexport type { FragmentMap, FragmentMapFunction } from \"./graphql/fragments.js\";\nexport {\n  createFragmentMap,\n  getFragmentQueryDocument,\n  getFragmentFromSelection,\n} from \"./graphql/fragments.js\";\n\nexport {\n  checkDocument,\n  getOperationDefinition,\n  getOperationName,\n  getFragmentDefinitions,\n  getQueryDefinition,\n  getFragmentDefinition,\n  getMainDefinition,\n  getDefaultValues,\n} from \"./graphql/getFromAST.js\";\n\nexport { print } from \"./graphql/print.js\";\n\nexport type {\n  StoreObject,\n  AsStoreObject,\n  Reference,\n  StoreValue,\n  Directives,\n  VariableValue,\n} from \"./graphql/storeUtils.js\";\nexport {\n  makeReference,\n  isDocumentNode,\n  isReference,\n  isField,\n  isInlineFragment,\n  valueToObjectRepresentation,\n  storeKeyNameFromField,\n  argumentsObjectFromField,\n  resultKeyNameFromField,\n  getStoreKeyName,\n  getTypenameFromResult,\n} from \"./graphql/storeUtils.js\";\n\nexport type {\n  RemoveNodeConfig,\n  GetNodeConfig,\n  RemoveDirectiveConfig,\n  GetDirectiveConfig,\n  RemoveArgumentsConfig,\n  GetFragmentSpreadConfig,\n  RemoveFragmentSpreadConfig,\n  RemoveFragmentDefinitionConfig,\n  RemoveVariableDefinitionConfig,\n} from \"./graphql/transform.js\";\nexport {\n  addTypenameToDocument,\n  buildQueryFromSelectionSet,\n  removeDirectivesFromDocument,\n  removeConnectionDirectiveFromDocument,\n  removeArgumentsFromDocument,\n  removeFragmentSpreadFromDocument,\n  removeClientSetsFromDocument,\n} from \"./graphql/transform.js\";\n\nexport {\n  isMutationOperation,\n  isQueryOperation,\n  isSubscriptionOperation,\n} from \"./graphql/operations.js\";\n\nexport {\n  concatPagination,\n  offsetLimitPagination,\n  relayStylePagination,\n} from \"./policies/pagination.js\";\n\nexport type {\n  Observer,\n  ObservableSubscription,\n} from \"./observables/Observable.js\";\nexport { Observable } from \"./observables/Observable.js\";\n\nexport type { PromiseWithState } from \"./promises/decoration.js\";\nexport {\n  isStatefulPromise,\n  createFulfilledPromise,\n  createRejectedPromise,\n  wrapPromiseWithState,\n} from \"./promises/decoration.js\";\n\nexport * from \"./common/mergeDeep.js\";\nexport * from \"./common/cloneDeep.js\";\nexport * from \"./common/maybeDeepFreeze.js\";\nexport * from \"./observables/iteration.js\";\nexport * from \"./observables/asyncMap.js\";\nexport * from \"./observables/Concast.js\";\nexport * from \"./observables/subclassing.js\";\nexport * from \"./common/arrays.js\";\nexport * from \"./common/objects.js\";\nexport * from \"./common/errorHandling.js\";\nexport * from \"./common/canUse.js\";\nexport * from \"./common/compact.js\";\nexport * from \"./common/makeUniqueId.js\";\nexport * from \"./common/stringifyForDisplay.js\";\nexport * from \"./common/mergeOptions.js\";\nexport * from \"./common/incrementalResult.js\";\n\nexport { canonicalStringify } from \"./common/canonicalStringify.js\";\nexport { omitDeep } from \"./common/omitDeep.js\";\nexport { stripTypename } from \"./common/stripTypename.js\";\n\nexport * from \"./types/IsStrictlyAny.js\";\nexport type { DeepOmit } from \"./types/DeepOmit.js\";\nexport type { DeepPartial } from \"./types/DeepPartial.js\";\nexport type { OnlyRequiredProperties } from \"./types/OnlyRequiredProperties.js\";\n\nexport {\n  AutoCleanedStrongCache,\n  AutoCleanedWeakCache,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"./caching/index.js\";\nexport type { CacheSizes } from \"./caching/index.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBM,SAAU,cACd,IACA,WAA+B;MAD7B,aAAU,GAAA;AAGZ,MAAI,CAAC,cAAc,CAAC,WAAW,QAAQ;AACrC,WAAO;EACT;AACA,SAAO,uBAAuB,UAAU,EAAE,MACxC,SAACA,KAAyB;QAAvB,YAASA,IAAA,WAAE,aAAUA,IAAA;AACtB,QAAI,cAAuB;AAC3B,QAAI,WAAW,MAAM,SAAS,YAAY;AACxC,oBACE,aAAa,UAAW,WAAW,MAAuB,KAAK,KAAK;AACtE,gBACE,gBAAgB,QAChB,IAAA,UAAA,KAAA,KAAA;IAGJ,OAAO;AACL,oBAAe,WAAW,MAA2B;IACvD;AACA,WAAO,UAAU,KAAK,UAAU,SAAS,CAAC,cAAc;EAC1D,CAAC;AAEL;AAEM,SAAU,kBAAkBC,OAAa;AAC7C,MAAM,QAAkB,CAAA;AAExB,QAAMA,OAAM;IACV,WAAS,SAAC,MAAmB;AAC3B,YAAM,KAAK,KAAK,KAAK,KAAK;IAC5B;GACD;AAED,SAAO;AACT;AAQM,SAAU,cAAc,OAAiBA,OAAe,KAAa;AACzE,MAAM,UAAU,IAAI,IAAI,KAAK;AAC7B,MAAM,cAAc,QAAQ;AAE5B,QAAMA,OAAM;IACV,WAAS,SAAC,MAAI;AACZ,UAAI,QAAQ,OAAO,KAAK,KAAK,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,OAAO;AAC9D,eAAO;MACT;IACF;GACD;AAID,SAAO,MAAM,CAAC,QAAQ,OAAO,QAAQ,OAAO;AAC9C;AAEM,SAAU,iBAAiB,UAAsB;AACrD,SAAO,YAAY,cAAc,CAAC,UAAU,QAAQ,GAAG,UAAU,IAAI;AACvE;AAOA,SAAS,qBAAqB,IAAkC;MAAxB,QAAK,GAAA,KAAA;AAC3C,SAAO,UAAU,UAAU,UAAU;AACvC;AAEM,SAAU,uBACd,YAAwC;AAExC,MAAMC,UAA8B,CAAA;AAEpC,MAAI,cAAc,WAAW,QAAQ;AACnC,eAAW,QAAQ,SAAC,WAAS;AAC3B,UAAI,CAAC,qBAAqB,SAAS;AAAG;AAEtC,UAAM,qBAAqB,UAAU;AACrC,UAAM,gBAAgB,UAAU,KAAK;AAErC,gBACE,sBAAsB,mBAAmB,WAAW,GACpD,IAAA,aAAA;AAIF,UAAM,aAAa,mBAAoB,CAAC;AACxC,gBACE,WAAW,QAAQ,WAAW,KAAK,UAAU,MAC7C,IAAA,aAAA;AAIF,UAAM,UAAqB,WAAW;AAGtC,gBACE,YACG,QAAQ,SAAS,cAAc,QAAQ,SAAS,iBACnD,IAAA,aAAA;AAIF,MAAAA,QAAO,KAAK,EAAE,WAAW,WAAU,CAAE;IACvC,CAAC;EACH;AAEA,SAAOA;AACT;AAtIA,IA0Da,kBAGA;AA7Db;;;AAcA;AA4CO,IAAM,mBAAmB,SAAC,OAAiBD,OAAa;AAC7D,aAAA,cAAc,OAAOA,OAAM,KAAK;IAAhC;AAEK,IAAM,mBAAmB,SAAC,OAAiBA,OAAa;AAC7D,aAAA,cAAc,OAAOA,OAAM,IAAI;IAA/B;;;;;ACgCF,SAAS,SAAS,OAAU;AAC1B,UAAQ,OAAO,OAAO;IACtB,KAAK;AACH,UAAI,UAAU;AAAM;;IAEtB,KAAK;AACH,aAAO;;AAET,SAAO;AACT;AAvGA,IAMM,iBAGE,SAAS,OACT,gBAEK;AAZb;;AAMA,IAAM,kBAAkB,MAAM,uBAAO,OAAO,IAAI;AAGhD,KAAM,EAAE,SAAS,UAAU,MAAM;AACjC,KAAM,EAAE,mBAAmB,OAAO;AAE5B,IAAO,OAAP,MAAO,MAAI;MAQf,YACU,WAAW,MACX,WAAmC,iBAAe;AADlD,aAAA,WAAA;AACA,aAAA,WAAA;MACP;MAGI,SAAM;AACX,eAAO,KAAK,YAAY,SAAS;MACnC;MAEO,YAA0C,OAAQ;AACvD,YAAI,OAAmB;AACvB,gBAAQ,KAAK,OAAO,SAAO,OAAO,KAAK,aAAa,GAAG,CAAC;AACxD,eAAO,eAAe,KAAK,MAAM,MAAM,IACnC,KAAK,OACL,KAAK,OAAO,KAAK,SAAS,MAAM,KAAK,KAAK,CAAC;MACjD;MAGO,OAAI;AACT,eAAO,KAAK,UAAU,SAAS;MACjC;MAEO,UAAwC,OAAQ;AACrD,YAAI,OAA+B;AAEnC,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,QAAQ,IAAI,KAAK,EAAE,GAAG;AACxD,gBAAM,MAAM,KAAK,OAAO,MAAM,CAAC,GAAG,KAAK;AACvC,iBAAO,OAAO,IAAI,IAAI,MAAM,CAAC,CAAC;;AAGhC,eAAO,QAAQ,KAAK;MACtB;MAGO,SAAM;AACX,eAAO,KAAK,YAAY,SAAS;MACnC;MAEO,YAA0C,OAAQ;AACvD,YAAI;AAEJ,YAAI,MAAM,QAAQ;AAChB,gBAAM,OAAO,MAAM,CAAC;AACpB,gBAAM,MAAM,KAAK,OAAO,MAAM,KAAK;AACnC,gBAAM,QAAQ,OAAO,IAAI,IAAI,IAAI;AACjC,cAAI,OAAO;AACT,mBAAO,MAAM,YAAY,MAAM,KAAK,OAAO,CAAC,CAAC;AAC7C,gBAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,OAAO;AACtE,kBAAI,OAAO,IAAI;;;eAGd;AACL,iBAAO,KAAK;AACZ,iBAAO,KAAK;;AAGd,eAAO;MACT;MAEQ,aAAa,KAAQ;AAC3B,cAAM,MAAM,KAAK,OAAO,KAAK,IAAI;AACjC,YAAI,QAAQ,IAAI,IAAI,GAAG;AACvB,YAAI,CAAC;AAAO,cAAI,IAAI,KAAK,QAAQ,IAAI,MAAW,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC7E,eAAO;MACT;MAEQ,OAAO,KAAU,QAAe;AACtC,eAAO,KAAK,YAAY,SAAS,GAAG,IAChC,KAAK,SAAS,SAAS,KAAK,OAAO,oBAAI,YAAU,UACjD,KAAK,WAAW,SAAS,KAAK,SAAS,oBAAI,QAAM;MACvD;;;;;;AC3FF,IAEM,eAEO,eAIA,eAEA,cAGA,2BAEA,WAGP,YAiBO;AAnCb;;;AAEA,IAAM,gBAAgB,MAAM,WAAA;AAAM,aAAA,UAAU;IAAV,CAAiB,KAAK;AAEjD,IAAM,gBACX,OAAO,YAAY,cACnB,EAAE,iBAAiB,CAAE,OAAe;AAE/B,IAAM,gBAAgB,OAAO,YAAY;AAEzC,IAAM,eACX,OAAO,WAAW,cAAc,OAAO,OAAO,QAAQ;AAEjD,IAAM,4BAA4B,gBAAgB,OAAO;AAEzD,IAAM,YACX,OAAO,MAAM,WAAA;AAAM,aAAA,OAAO,SAAS;IAAhB,CAA6B,MAAM;AAExD,IAAM;;;;;;;;IASJ,MAAM,WAAA;AAAM,aAAA,UAAU,UAAU,QAAQ,OAAO,KAAK;IAAxC,CAAyC,KAAK;AAQrD,IAAM,sBAAsB,aAAa,kBAAkB,CAAC;;;;;ACnC7D,SAAU,gBAAgB,KAAQ;AACtC,SAAO,QAAQ,QAAQ,OAAO,QAAQ;AACxC;AAEM,SAAU,cAAc,KAAQ;AACpC,SACE,QAAQ,QACR,OAAO,QAAQ,aACd,OAAO,eAAe,GAAG,MAAM,OAAO,aACrC,OAAO,eAAe,GAAG,MAAM;AAErC;AAXA;;;;;;ACkCM,SAAU,yBACd,UACA,cAAqB;AAErB,MAAI,qBAAqB;AAKzB,MAAM,YAA2C,CAAA;AACjD,WAAS,YAAY,QAAQ,SAAC,YAAU;AAGtC,QAAI,WAAW,SAAS,uBAAuB;AAC7C,YAAM;QAEF;QAIL,WAAA;QACD,WAAA,OAAA,WAAA,OAAA,WAAA,KAAA,OAAgE,GAAA,IAAA;MAChE;IACA;AAGC,QAAA,WAAA,SAAA,sBAAA;AAEH,gBAAA,KAAA,UAAA;IACA;EACA,CAAA;AAOA,MAAC,OAAA,uBAAA,aAAA;AAED,cAAA,UAAA,WAAA,GAAA,IAAA,UAAA,MAAA;AACA,yBAAyB,UAAA,CAAA,EAAA,KAAA;EACzB;cAKM,SAAA,SAAA,CAAA,GAAA,QAAA,GAA+B,EAAA,aAAA,cAAA;;MAE/B,MAAA;;iBAEE;oBACE;;oBAEM;;kBAEF;kBACD;cACF,MAAA;cACF,OAAA;YACF;UACF;QACW;MAIJ;IACb;EAaD,GAAA,SAAA,aAAA,IAAA,EAAA,CAAA;AACA,SAAA;AACA;AAIE,SAAU,kBAAS,WAAQ;MACzB,cAAS,QAAa;AAAC,gBAAS,CAAA;EAAQ;AAC1C,MAAG,WAAA,CAAA;AACH,YAAO,QAAS,SAAA,UAAA;AACjB,aAAA,SAAA,KAAA,KAAA,IAAA;EAED,CAAA;AAIE,SAAA;;SAEI,yBAAiB,WAAA,aAAA;UACnB,UAAK,MAAA;SACH;AACA,aAAI;2BACK;AACT,UAAC,eAAA,UAAA,KAAA;AACD,UAAM,OAAA,gBAAsB,YAAI;AAChC,eAAU,YAAU,YAAA;MACpB;AACD,UAAA,WAAA,eAAA,YAAA,YAAA;AACD,gBAAA,UAAA,IAAA,YAAA;AACE,aAAO,YAAK;IACf;IACF;;;;;;;AAhJD;;;;;ACSA,SAAS,iBAAc;AAAI;AAA3B,IAEa;AAFb;;AAEM,IAAO,cAAP,MAAkB;MAKtB,YACU,MAAM,UACP,UAAsC,gBAAc;AADnD,aAAA,MAAA;AACD,aAAA,UAAA;AAND,aAAA,MAAM,oBAAI,IAAG;AACb,aAAA,SAA4B;AAC5B,aAAA,SAA4B;MAKjC;MAEI,IAAI,KAAM;AACf,eAAO,KAAK,IAAI,IAAI,GAAG;MACzB;MAEO,IAAI,KAAM;AACf,cAAM,OAAO,KAAK,QAAQ,GAAG;AAC7B,eAAO,QAAQ,KAAK;MACtB;MAEA,IAAW,OAAI;AACb,eAAO,KAAK,IAAI;MAClB;MAEQ,QAAQ,KAAM;AACpB,cAAM,OAAO,KAAK,IAAI,IAAI,GAAG;AAE7B,YAAI,QAAQ,SAAS,KAAK,QAAQ;AAChC,gBAAM,EAAE,OAAO,MAAK,IAAK;AAEzB,cAAI,OAAO;AACT,kBAAM,QAAQ;;AAGhB,cAAI,OAAO;AACT,kBAAM,QAAQ;;AAGhB,eAAK,QAAQ,KAAK;AAClB,eAAK,MAAO,QAAQ;AAEpB,eAAK,QAAQ;AACb,eAAK,SAAS;AAEd,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,SAAS;;;AAIlB,eAAO;MACT;MAEO,IAAI,KAAQ,OAAQ;AACzB,YAAI,OAAO,KAAK,QAAQ,GAAG;AAC3B,YAAI,MAAM;AACR,iBAAO,KAAK,QAAQ;;AAGtB,eAAO;UACL;UACA;UACA,OAAO;UACP,OAAO,KAAK;;AAGd,YAAI,KAAK,QAAQ;AACf,eAAK,OAAO,QAAQ;;AAGtB,aAAK,SAAS;AACd,aAAK,SAAS,KAAK,UAAU;AAE7B,aAAK,IAAI,IAAI,KAAK,IAAI;AAEtB,eAAO,KAAK;MACd;MAEO,QAAK;AACV,eAAO,KAAK,UAAU,KAAK,IAAI,OAAO,KAAK,KAAK;AAC9C,eAAK,OAAO,KAAK,OAAO,GAAG;;MAE/B;MAEO,OAAO,KAAM;AAClB,cAAM,OAAO,KAAK,IAAI,IAAI,GAAG;AAC7B,YAAI,MAAM;AACR,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,SAAS,KAAK;;AAGrB,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,SAAS,KAAK;;AAGrB,cAAI,KAAK,OAAO;AACd,iBAAK,MAAM,QAAQ,KAAK;;AAG1B,cAAI,KAAK,OAAO;AACd,iBAAK,MAAM,QAAQ,KAAK;;AAG1B,eAAK,IAAI,OAAO,GAAG;AACnB,eAAK,QAAQ,KAAK,OAAO,GAAG;AAE5B,iBAAO;;AAGT,eAAO;MACT;;;;;;ACnGF,SAAS,OAAI;AAAI;AAAjB,IACME,iBAEA,UASA,UACA,uBAUA,uBAEO;AAzBb;;AACA,IAAMA,kBAAiB;AAEvB,IAAM,WACJ,OAAO,YAAY,cACf,UACC,SAAa,OAAQ;AACpB,aAAO,EAAE,OAAO,MAAM,MAAK;IAI7B;AACN,IAAM,WAAW,OAAO,YAAY,cAAc,UAAU;AAC5D,IAAM,wBACJ,OAAO,yBAAyB,cAC5B,uBACC,WAAA;AACC,aAAO;QACL,UAAU;QACV,YAAY;;IAEhB;AAEN,IAAM,wBAAwB;AAExB,IAAO,YAAP,MAAgB;MAWpB,YACU,MAAM,UACP,UAAuCA,iBAAc;AADpD,aAAA,MAAA;AACD,aAAA,UAAA;AAVD,aAAA,MAAM,IAAI,SAAQ;AAElB,aAAA,SAA4B;AAC5B,aAAA,SAA4B;AAC5B,aAAA,mBAA+C,oBAAI,IAAG;AACtD,aAAA,wBAAwB;AACzB,aAAA,OAAO;AAgIN,aAAA,WAAW,MAAK;AACtB,gBAAM,WAAW,KAAK,iBAAiB,OAAM;AAC7C,mBAAS,IAAI,GAAG,IAAI,uBAAuB,KAAK;AAC9C,kBAAM,OAAO,SAAS,KAAI,EAAG;AAC7B,gBAAI,CAAC;AAAM;AACX,iBAAK,iBAAiB,OAAO,IAAI;AACjC,kBAAM,MAAM,KAAK;AACjB,mBAAQ,KAAmC;AAC1C,iBAAmC,SAAS,IAAI,SAAS,GAAG;AAC7D,iBAAK,SAAS,SAAS,KAAK,MAAM,IAAI;;AAExC,cAAI,KAAK,iBAAiB,OAAO,GAAG;AAClC,2BAAe,KAAK,QAAQ;iBACvB;AACL,iBAAK,wBAAwB;;QAEjC;AA1IE,aAAK,WAAW,IAAI,sBAClB,KAAK,WAAW,KAAK,IAAI,CAAC;MAE9B;MAEO,IAAI,KAAM;AACf,eAAO,KAAK,IAAI,IAAI,GAAG;MACzB;MAEO,IAAI,KAAM;AACf,cAAM,OAAO,KAAK,QAAQ,GAAG;AAC7B,eAAO,QAAQ,KAAK;MACtB;MAEQ,QAAQ,KAAM;AACpB,cAAM,OAAO,KAAK,IAAI,IAAI,GAAG;AAE7B,YAAI,QAAQ,SAAS,KAAK,QAAQ;AAChC,gBAAM,EAAE,OAAO,MAAK,IAAK;AAEzB,cAAI,OAAO;AACT,kBAAM,QAAQ;;AAGhB,cAAI,OAAO;AACT,kBAAM,QAAQ;;AAGhB,eAAK,QAAQ,KAAK;AAClB,eAAK,MAAO,QAAQ;AAEpB,eAAK,QAAQ;AACb,eAAK,SAAS;AAEd,cAAI,SAAS,KAAK,QAAQ;AACxB,iBAAK,SAAS;;;AAIlB,eAAO;MACT;MAEO,IAAI,KAAQ,OAAQ;AACzB,YAAI,OAAO,KAAK,QAAQ,GAAG;AAC3B,YAAI,MAAM;AACR,iBAAQ,KAAK,QAAQ;;AAGvB,eAAO;UACL;UACA;UACA,OAAO;UACP,OAAO,KAAK;;AAGd,YAAI,KAAK,QAAQ;AACf,eAAK,OAAO,QAAQ;;AAGtB,aAAK,SAAS;AACd,aAAK,SAAS,KAAK,UAAU;AAE7B,aAAK,qBAAqB,IAAI;AAC9B,aAAK,IAAI,IAAI,KAAK,IAAI;AACtB,aAAK;AAEL,eAAO,KAAK;MACd;MAEO,QAAK;AACV,eAAO,KAAK,UAAU,KAAK,OAAO,KAAK,KAAK;AAC1C,eAAK,WAAW,KAAK,MAAM;;MAE/B;MAEQ,WAAW,MAAgB;AACjC,YAAI,SAAS,KAAK,QAAQ;AACxB,eAAK,SAAS,KAAK;;AAGrB,YAAI,SAAS,KAAK,QAAQ;AACxB,eAAK,SAAS,KAAK;;AAGrB,YAAI,KAAK,OAAO;AACd,eAAK,MAAM,QAAQ,KAAK;;AAG1B,YAAI,KAAK,OAAO;AACd,eAAK,MAAM,QAAQ,KAAK;;AAG1B,aAAK;AACL,cAAM,MAAM,KAAK,OAAQ,KAAK,UAAU,KAAK,OAAO,MAAK;AACzD,aAAK,QAAQ,KAAK,OAAO,GAAG;AAC5B,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,iBAAiB,OAAO,IAAI;eAC5B;AACL,eAAK,SAAS,WAAW,IAAI;;AAE/B,YAAI;AAAK,eAAK,IAAI,OAAO,GAAG;MAC9B;MAEO,OAAO,KAAM;AAClB,cAAM,OAAO,KAAK,IAAI,IAAI,GAAG;AAC7B,YAAI,MAAM;AACR,eAAK,WAAW,IAAI;AAEpB,iBAAO;;AAGT,eAAO;MACT;MAEQ,qBAAqB,MAA2B;AACtD,aAAK,iBAAiB,IAAI,IAAI;AAC9B,YAAI,CAAC,KAAK,uBAAuB;AAC/B,eAAK,wBAAwB;AAC7B,yBAAe,KAAK,QAAQ;;MAEhC;;;;;;ACnLF,IAAAC,YAAA;;;AACA;;;;;ACMA,SAAS,SAAS,OAAqB;AACrC,MAAI,MAAM,SAAS,MAAM,OAAO,KAAK;AACnC;EACF;AACA,MAAI,CAAC,iBAAiB,IAAI,KAAK,GAAG;AAChC,qBAAiB,IAAI,KAAK;AAC1B,eAAW,WAAA;AACT,YAAM,MAAK;AACX,uBAAiB,OAAO,KAAK;IAC/B,GAAG,GAAG;EACR;AACF;AAnBA,IAOM,kBAwBO,sBAmCA;AAlEb;;IAAAC;AAOA,IAAM,mBAAmB,oBAAI,QAAO;AAwB7B,IAAM,uBAAuB,SAClC,KACA,SAAsD;AAStD,UAAM,QAAQ,IAAI,UAAU,KAAK,OAAO;AACxC,YAAM,MAAM,SAAU,KAAU,OAAU;AACxC,YAAM,MAAM,UAAU,UAAU,IAAI,KAAK,MAAM,KAAK,KAAK;AACzD,iBAAS,IAA6B;AACtC,eAAO;MACT;AACA,aAAO;IACT;AAiBO,IAAM,yBAAyB,SACpC,KACA,SAAsD;AAStD,UAAM,QAAQ,IAAI,YAAY,KAAK,OAAO;AAC1C,YAAM,MAAM,SAAU,KAAU,OAAU;AACxC,YAAM,MAAM,YAAY,UAAU,IAAI,KAAK,MAAM,KAAK,KAAK;AAC3D,iBAAS,IAA6B;AACtC,eAAO;MACT;AACA,aAAO;IACT;;;;;ICkMM,iBAuBO;;;;AA7Sb;AAsRA,IAAM,kBAAkB,OAAO,IAAI,kBAAkB;AAuB9C,IAAM,aAAU,SAAA,CAAA,GAA6B,eAAO,eAAe,CAAC;;;;;AC7S3E;;;AAEA;;;;;ACeM,SAAU,oBACd,MACA,SAAqB;AAErB,eAAa,IAAI,IAAI;AACvB;AAyEA,SAAS,uBAAoB;AAE3B,MAAM,WAA6C;IACjD,QAAM;IACN,oBAAkB;IAClB,OAAK;IACL,2BAAyB;IACzB,gCAA8B;IAE9B,2CAAyC;IAEzC,8BAA4B;IAE5B,2BAAyB;IACzB,wCAAsC;IAEtC,gCAA8B;IAE9B,sDAAoD;IAEpD,qCAAmC;IAEnC,qCAAmC;IAEnC,yCAAuC;;AAGzC,SAAO,OAAO,YACZ,OAAO,QAAQ,QAAQ,EAAE,IAAI,SAAC,IAAM;QAAL,IAAC,GAAA,CAAA,GAAE,IAAC,GAAA,CAAA;AAAM,WAAA;MACvC;MACA,WAAW,CAAqB,KAAK;;EAFE,CAGxC,CAAC;AAEN;AAEA,SAAS,kCAA+B;;AACtC,MAAI,EAAC,WAAO,YAAA;AAAE,UAAM,IAAI,MAAM,oCAAoC;AAElE,SAAO;IACL,QAAQ,qBAAoB;IAC5B,OAAK,SAAA,EACH,QAAO,KAAA,aAAa,WAAK,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,YAAA,GACzB,SAAQ,KAAA,aAAa,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,YAAA,GAC3B,qBAAoB,KAAA,aAAa,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,YAAA,GACnD,OAAO,SAAS,KAAK,IAAI,GACzB,cAAc;MACZ,iBAAiB,KAAK,cAAc,EAAE,gBAAgB,EAAE;MACxD,oBAAoB,cAClB,KAAK,cAAc,EAAE,iBAAiB;MAEzC,IACG,MAAA,KAAA,KAAK,OAAM,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA,CAG8B;;AAGrE;AAEA,SAAS,iCAA8B;AACrC,SAAO;IACL,OAAO;MACL,wBAAwB,sBAAsB,KAAK,gBAAgB,CAAC;;;AAG1E;AAEA,SAAS,mCAAgC;AACvC,MAAM,YAAY,KAAK,OAAO;AAQ9B,SAAA,SAAA,SAAA,CAAA,GACK,+BAA+B,MAAM,IAAW,CAAC,GAAA,EACpD,8BAA8B,cAAc,KAAK,sBAAsB,CAAC,GACxE,eAAe;IACb,qBAAqB,sBACnB,KAAK,aAAa,EAAE,qBAAqB,CAAC;IAE5C,yBAAyB,sBACvB,KAAK,aAAa,EAAE,yBAAyB,CAAC;IAEhD,qBAAqB,sBAAsB,KAAK,qBAAqB,CAAC;KAExE,kBAAkB;IAChB,qBAAqB,sBACnB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,mBAAmB;IAEhC,QAAQ,sBAAsB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,MAAM;IAC/C,WAAW,sBAAsB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,SAAS;IACtD,CAAA;AAEL;AAEA,SAAS,UAAU,GAAY;AAC7B,SAAO,CAAC,CAAC,KAAK,cAAc;AAC9B;AAEA,SAAS,sBAAsB,GAAY;AACzC,SAAO,UAAU,CAAC,IAAI,EAAE,OAAO;AACjC;AAEA,SAAS,UAAa,OAA2B;AAC/C,SAAO,SAAS;AAClB;AAEA,SAAS,cAAc,WAA6B;AAClD,SAAO,qBAAqB,SAAS,EAAE,IAAI,SAAC,OAAK;AAAK,WAAC,EAAE,MAAK;EAAR,CAAW;AACnE;AAEA,SAAS,qBAAqB,WAA6B;AACzD,SAAO,YACH,cAAA,cAAA;IACE,sBAAsB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAY,aAAa,CAAC;KAC7C,qBAAqB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAY,MAAM,CAAC,GAAC,IAAA,GACzC,qBAAqB,cAAS,QAAT,cAAS,SAAA,SAAT,UAAY,OAAO,CAAC,GAAC,IAAA,EAC7C,OAAO,SAAS,IAClB,CAAA;AACN;AAEA,SAAS,SAAS,MAAiB;;AACjC,SAAO,OACH,cAAA,cAAA;KACE,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA;KACrB,SAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,IAAI,GAAC,IAAA,GACpB,SAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,KAAK,GAAC,IAAA,EACxB,OAAO,SAAS,IAClB,CAAA;AACN;IAxNM,cAuDO,gCAWA,iCAWA;;;;AA/Eb;AAEA,IAAM,eAIF,CAAA;AAmDG,IAAM,iCACX,WAAS,YAAA,QACN,kCAGD;AAMG,IAAM,kCACX,WAAS,YAAA,QACN,mCAGD;AAMG,IAAM,gCACX,WAAS,YAAA,QACN,iCAGD;;;;;ACtCJ,SAAS,qBAAqB,KAAa,OAAU;AACnD,MAAI,SAAS,OAAO,UAAU,UAAU;AACtC,QAAM,QAAQ,OAAO,eAAe,KAAK;AAIzC,QAAI,UAAU,OAAO,aAAa,UAAU,MAAM;AAChD,UAAM,OAAO,OAAO,KAAK,KAAK;AAG9B,UAAI,KAAK,MAAM,eAAe;AAAG,eAAO;AACxC,UAAM,cAAc,KAAK,UAAU,IAAI;AACvC,UAAI,aAAa,WAAW,IAAI,WAAW;AAC3C,UAAI,CAAC,YAAY;AACf,aAAK,KAAI;AACT,YAAM,YAAY,KAAK,UAAU,IAAI;AAGrC,qBAAa,WAAW,IAAI,SAAS,KAAK;AAC1C,mBAAW,IAAI,aAAa,UAAU;AACtC,mBAAW,IAAI,WAAW,UAAU;MACtC;AACA,UAAM,iBAAe,OAAO,OAAO,KAAK;AAGxC,iBAAW,QAAQ,SAACC,MAAG;AACrB,uBAAaA,IAAG,IAAI,MAAMA,IAAG;MAC/B,CAAC;AACD,aAAO;IACT;EACF;AACA,SAAO;AACT;AAMA,SAAS,gBACP,KACA,GACA,MAAuB;AAEvB,SAAO,MAAM,KAAK,KAAK,IAAI,CAAC,KAAK;AACnC;AAnGA,IAwBa,oBAsBT;AA9CJ;;;AAKA;AAmBO,IAAM,qBAAqB,OAAO,OACvC,SAASC,oBAAmB,OAAU;AACpC,aAAO,KAAK,UAAU,OAAO,oBAAoB;IACnD,GACA;MACE,OAAK,WAAA;AAIH,qBAAa,IAAI;UACf,WAAW,sBAAkB;;QAAwC;MAEzE;KACD;AAGH,QAAI,WAAU,YAAA,OAAA;AACZ,0BAAoB,sBAAsB,WAAA;AAAM,eAAA,WAAW;MAAX,CAAe;IACjE;AAKA,uBAAmB,MAAK;;;;;ACflB,SAAU,cAAc,IAAU;AACtC,SAAO,EAAE,OAAO,OAAO,EAAE,EAAC;AAC5B;AAEM,SAAU,YAAY,KAAQ;AAClC,SAAO,QACL,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,UAAU,QAAQ;AAEnE;AAoCM,SAAU,eAAe,OAAU;AACvC,SACE,gBAAgB,KAAK,KACpB,MAAuB,SAAS,cACjC,MAAM,QAAS,MAAuB,WAAW;AAErD;AAEA,SAAS,cAAc,OAAgB;AACrC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,eAAe,OAAgB;AACtC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,WAAW,OAAgB;AAClC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,aAAa,OAAgB;AACpC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,WAAW,OAAgB;AAClC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,cAAc,OAAgB;AACrC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,YAAY,OAAgB;AACnC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,YAAY,OAAgB;AACnC,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,YAAY,OAAgB;AACnC,SAAO,MAAM,SAAS;AACxB;AAEM,SAAU,4BACd,QACA,MACA,OACA,WAAkB;AAElB,MAAI,WAAW,KAAK,KAAK,aAAa,KAAK,GAAG;AAC5C,WAAO,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK;EACzC,WAAW,eAAe,KAAK,KAAK,cAAc,KAAK,GAAG;AACxD,WAAO,KAAK,KAAK,IAAI,MAAM;EAC7B,WAAW,cAAc,KAAK,GAAG;AAC/B,QAAM,iBAAe,CAAA;AACrB,UAAM,OAAO,IAAI,SAAC,KAAG;AACnB,aAAA,4BAA4B,gBAAc,IAAI,MAAM,IAAI,OAAO,SAAS;IAAxE,CAAyE;AAE3E,WAAO,KAAK,KAAK,IAAI;EACvB,WAAW,WAAW,KAAK,GAAG;AAC5B,QAAM,iBAAiB,aAAc,CAAA,GAAY,MAAM,KAAK,KAAK;AACjE,WAAO,KAAK,KAAK,IAAI;EACvB,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAO,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,SAAC,WAAS;AAC9C,UAAM,oBAAoB,CAAA;AAC1B,kCACE,mBACA,MACA,WACA,SAAS;AAEX,aAAQ,kBAA0B,KAAK,KAAK;IAC9C,CAAC;EACH,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAO,KAAK,KAAK,IAAK,MAAwB;EAChD,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAO,KAAK,KAAK,IAAI;EACvB,OAAO;AACL,UAAM,kBACJ,IAAA,KAAA,OAAA,MAAA,IAAA;;;AAMH,SAAA,sBAAA,OAAA,WAAA;AACF,MAAA,gBAAA;AAED,MAAM,MAAA,YAAU;AAIV,oBAAqB,CAAA;AACrB,UAAM,WAAU,QAAG,SAAA,WAAA;AACrB,oBAAkB,UAAC,KAAA,KAAA,IAAA,CAAA;AACnB,UAAM,UAAW,WAAQ;AACvB,kBAAc,UAAU,QAAK,SAAY,IAAA;AAErC,cAAA,OAAU,GAAA,MAAY,QAAA,GAAA;AACxB,iBAAU,4BAAmB,cAAe,UAAA,KAAA,KAAA,GAAA,MAAA,OAAA,SAAA;;;;;MAShD,SAAG;AACL,MAAC,MAAA,aAAA,MAAA,UAAA,QAAA;AAEG,aAAc,CAAA;AACd,UAAM,UAAS,QAAS,SAAU,IAAC;AACrC,UAAM,OAAM,GAAA,MAAA,QAAA,GAAA;AACZ,aAAM,4BAAkC,QAAA,MAAA,OAAA,SAAA;;;SAEtC,gBAAA,MAAA,KAAA,OAAA,QAAA,aAAA;;AA4FD,SAAA,yBAAA,OAAA,WAAA;AAEH,MAAA,MAAA,aAAA,MAAA,UAAA,QAAA;AAEI,QAAA,WAAU,CAAA;AAIV,UAAM,UAAS,QAAS,SAAU,IAAC;AAC/B,UAAA,OAAiB,GAAG,MAAA,QAAA,GAAA;AAC1B,aAAM,4BAAkC,UAAA,MAAA,OAAA,SAAA;;WACtC;;SAEF;;AAEF,SAAY,uBAAA,OAAA;AACb,SAAA,MAAA,QAAA,MAAA,MAAA,QAAA,MAAA,KAAA;AAED;AACE,SAAY,sBAAqBC,SAAQ,cAAW,aAAM;AAC3D,MAAA;AAED,WAAM,KAAU,GAAA,KAAA,aACd,YACA,KAAA,GAA8B,QAC9B,MAAyB;AAErB,QAAA,YAAsE,GAAA,EAAA;AAC1E,QAAwB,QAAuB,SAAvB,GAAA;AAAb,UAAA,UAAS,KAAA,UAAA,cAAA;AACd,eAAQA,QAAU,uBAAG,SAAA,CAAA;MACvB;eAEC,WAAA;AACF,gBAAA,KAAA,SAAA;WACC;AACD,kBAAA,CAAA,SAAA;;;MAED,OAACA,QAAA,eAAA,UAAA;AACF,WAAAA,QAAA;EACD;MACE,WAAO;AACR,aAAA,KAAA,GAAA,cAAA,WAAA,KAAA,YAAA,QAAA,MAAA;AACG,UAAA,YAAY,YAAA,EAAA;AACd,UAAwB,WAAA,sBAASA,SAAT,yBAAY,WAAA,WAAA,EAAA,cAAA,WAAA;AAA/B,UAAM,OAAA,aAAS,UAAA;AACZ,eAAA;MAKN;;;;AAIH,SAAA,QAAA,WAAA;AACF,SAAA,UAAA,SAAA;AAED;AACE,SAAO,iBAAmB,WAAQ;AACnC,SAAA,UAAA,SAAA;AAED;AAzVA,IAoME,kBAoBF,uBACA;AAzNA;;;AAuBA;AAEA;AACA;IA0KE,mBAAO;MACR;MAQK;MACJ;MACA;MACA;MACA;MACA;;AAMF,IAAA,wBAAA;AACA,IAAA,kBAAA,OAAA,OAAA,SAAA,WAAsD,MAAA,YAAA;AAClD,UAAA,QAEG,cAOD,WAAI,YAAA,KACJ,WAAU,YAAA,EAAA,KAAA,GAAA;AACV,YAAA,WAAW,YAAa,EAAA,QAAA,KACxB,WAAW,YAAc,EAAA,QACxB,EAAA,SAAA,GAAA;AAEC,cAAA,aAAW,WAAc,YAAS,EAAA,QAAA,IACjC,WAAW,YAAc,EAAA,QAAuB,IAE3C,CAAA;qBAED,KAAW;cACd,iBAAK,CAAA;AACP,qBAAW,QAAO,SAAA,KAAA;AAEZ,2BAAY,GAAG,IAA6B,KAAA,GAAA;UAClD,CAAA;iBACE,GAAA,OAAA,WAAoB,YAAU,EAAA,KAAA,GAAA,GAAA,EAAA,OAAA,sBAAA,cAAA,GAAA,GAAA;eAGhC;AAGD,iBAAA,WAAA,YAAA,EAAA,KAAA;;;UAED,oBAAC;AACH,UAAC,MAAA;AAMC,YAAA,kBAAA,sBAAA,IAAA;AACA,6BAAA,IAAA,OAAA,iBAAA,GAAA;;UAEA,YAAA;AACD,eAAA,KAAA,UAAA,EAAA,QAAA,SAAA,KAAA;AAEG,cAAA,iBAAa,QAAA,GAAA,MAAA;AACR;AACL,cAAI,WAAA,GAAA,KAAiB,OAAQ,KAAI,WAAO,GAAA,CAAA,EAAA,QAAA;AAAE,iCAAO,IAAA,OAAA,KAAA,GAAA,EAAA,OAAA,sBAAA,WAAA,GAAA,CAAA,GAAA,GAAA;UACjD,OACE;AAGD,iCAAA,IAAA,OAAA,GAAA;;;;aAGA;;MAGL,cAAO,SAAiB,GAAC;AAE3B,YAAA,WAAA;AACE,gCAA4C;AAC1C,eAAM;;;;;;;AC1QN,SAAU,cAAc,KAAiB;AAC7C,YACE,OAAO,IAAI,SAAS,YACpB,EAAA;AAIF,MAAM,aAAa,IAAI,YACpB,OAAO,SAAC,GAAC;AAAK,WAAA,EAAE,SAAS;EAAX,CAA+B,EAC7C,IAAI,SAAC,YAAU;AACd,QAAI,WAAW,SAAS,uBAAuB;AAC7C,YAAM,kBACJ,IAAA,WAAA,IAAA;IAGJ;AACA,WAAO;EACT,CAAC;AAEH,YACE,WAAW,UAAU,GACrB,IAAA,WAAA,MAAA;AAIF,SAAO;AACT;AAEM,SAAU,uBACd,KAAiB;AAEjB,gBAAc,GAAG;AACjB,SAAO,IAAI,YAAY,OACrB,SAAC,YAAU;AACT,WAAA,WAAW,SAAS;EAApB,CAAyC,EAC3C,CAAC;AACL;AAEM,SAAU,iBAAiB,KAAiB;AAChD,SACE,IAAI,YACD,OACC,SAAC,YAAU;AACT,WAAA,WAAW,SAAS,yBAAyB,CAAC,CAAC,WAAW;EAA1D,CAA8D,EAEjE,IAAI,SAAC,GAAC;AAAK,WAAA,EAAE,KAAK;EAAP,CAAY,EAAE,CAAC,KAAK;AAEtC;AAGM,SAAU,uBACd,KAAiB;AAEjB,SAAO,IAAI,YAAY,OACrB,SAAC,YAAU;AACT,WAAA,WAAW,SAAS;EAApB,CAAwC;AAE9C;AAEM,SAAU,mBAAmB,KAAiB;AAClD,MAAM,WAAW,uBAAuB,GAAG;AAE3C,YACE,YAAY,SAAS,cAAc,SACnC,EAAA;AAGF,SAAO;AACT;AAEM,SAAU,sBACd,KAAiB;AAEjB,YACE,IAAI,SAAS,YACb,EAAA;AAIF,YACE,IAAI,YAAY,UAAU,GAC1B,EAAA;AAGF,MAAM,cAAc,IAAI,YAAY,CAAC;AAErC,YACE,YAAY,SAAS,sBACrB,EAAA;AAGF,SAAO;AACT;AAOM,SAAU,kBACd,UAAsB;AAEtB,gBAAc,QAAQ;AAEtB,MAAI;AAEJ,WAAuB,KAAA,GAAA,KAAA,SAAS,aAAT,KAAA,GAAA,QAAA,MAAsB;AAAxC,QAAI,aAAU,GAAA,EAAA;AACjB,QAAI,WAAW,SAAS,uBAAuB;AAC7C,UAAM,YAAa,WAAuC;AAC1D,UACE,cAAc,WACd,cAAc,cACd,cAAc,gBACd;AACA,eAAO;MACT;IACF;AACA,QAAI,WAAW,SAAS,wBAAwB,CAAC,oBAAoB;AAGnE,2BAAqB;IACvB;EACF;AAEA,MAAI,oBAAoB;AACtB,WAAO;EACT;AAEA,QAAM,kBACJ,EAAA;AAEJ;AAEM,SAAU,iBACd,YAA+C;AAE/C,MAAM,gBAAgB,uBAAO,OAAO,IAAI;AACxC,MAAM,OAAO,cAAc,WAAW;AACtC,MAAI,QAAQ,KAAK,QAAQ;AACvB,SAAK,QAAQ,SAAC,KAAG;AACf,UAAI,IAAI,cAAc;AACpB,oCACE,eACA,IAAI,SAAS,MACb,IAAI,YAAyB;MAEjC;IACF,CAAC;EACH;AACA,SAAO;AACT;AAtKA;;;AASA;;;;;ACuDA,SAASC,UAAS,OAAU;AAC1B,UAAQ,OAAO,OAAO;IACtB,KAAK;AACH,UAAI,UAAU;AAAM;;IAEtB,KAAK;AACH,aAAO;;AAET,SAAO;AACT;AAzEA,IAMMC,kBAGEC,UAASC,QACTC,iBAEKC;AAZb,IAAAC,YAAA;;AAMA,IAAML,mBAAkB,MAAM,uBAAO,OAAO,IAAI;AAGhD,KAAM,EAAE,SAAAC,UAAS,OAAAC,WAAU,MAAM;AACjC,KAAM,EAAE,gBAAAC,oBAAmB,OAAO;AAE5B,IAAOC,QAAP,MAAO,MAAI;MAQf,YACU,WAAW,MACX,WAAmCJ,kBAAe;AADlD,aAAA,WAAA;AACA,aAAA,WAAA;MACP;MAEI,UAA2B,OAAQ;AACxC,eAAO,KAAK,YAAY,KAAK;MAC/B;MAEO,YAA0C,OAAQ;AACvD,YAAI,OAAmB;AACvB,QAAAC,SAAQ,KAAK,OAAO,SAAO,OAAO,KAAK,aAAa,GAAG,CAAC;AACxD,eAAOE,gBAAe,KAAK,MAAM,MAAM,IACnC,KAAK,OACL,KAAK,OAAO,KAAK,SAASD,OAAM,KAAK,KAAK,CAAC;MACjD;MAEO,QAAyB,OAAQ;AACtC,eAAO,KAAK,UAAU,KAAK;MAC7B;MAEO,UAAwC,OAAQ;AACrD,YAAI,OAA+B;AAEnC,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,QAAQ,IAAI,KAAK,EAAE,GAAG;AACxD,gBAAM,MACJ,KAAK,YAAYH,UAAS,MAAM,CAAC,CAAC,IAAI,KAAK,OAAO,KAAK;AAEzD,iBAAO,OAAO,IAAI,IAAI,MAAM,CAAC,CAAC;;AAGhC,eAAO,QAAQ,KAAK;MACtB;MAEQ,aAAa,KAAQ;AAC3B,cAAM,MAAM,KAAK,YAAYA,UAAS,GAAG,IACrC,KAAK,SAAS,KAAK,OAAO,oBAAI,QAAO,KACrC,KAAK,WAAW,KAAK,SAAS,oBAAI,IAAG;AACzC,YAAI,QAAQ,IAAI,IAAI,GAAG;AACvB,YAAI,CAAC;AAAO,cAAI,IAAI,KAAK,QAAQ,IAAI,MAAW,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC7E,eAAO;MACT;;;;;;ACgEF,SAASO,OAAS,IAAW;AAC3B,MAAI;AACF,WAAO,GAAE;WACF,SAAS;EAAA;AACpB;AA5HA,IAGI,gBAIE,eAEF,WAKE,eAwHA,WAEA,MAeA,YAIO;AA3Jb;;AAGA,IAAI,iBAAiC;AAIrC,IAAM,gBAAqB,CAAA;AAE3B,IAAI,YAAY;AAKhB,IAAM,gBAAgB,MAAM,MAAM,KAAI;MAAV,cAAA;AAIV,aAAA,KAAK;UACnB;UACA;UACA,KAAK,IAAG;UACR,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,MAAM,CAAC;UAClC,KAAK,GAAG;MA+FZ;MA7FS,WAAQ;AACb,iBAAS,UAAU,gBAAgB,SAAS,UAAU,QAAQ,QAAQ;AAGpE,cAAI,KAAK,MAAM,QAAQ,OAAO;AAC5B,kBAAM,QAAQ,QAAQ,MAAM,KAAK,EAAE;AACnC,gBAAI,UAAU;AAAe;AAC7B,gBAAI,YAAY,gBAAgB;AAI9B,6BAAgB,MAAM,KAAK,EAAE,IAAI;;AAEnC,mBAAO;;;AAGX,YAAI,gBAAgB;AAIlB,yBAAe,MAAM,KAAK,EAAE,IAAI;;AAElC,eAAO;MACT;MAEO,WAAQ;AACb,YAAI,KAAK,SAAQ,GAAI;AACnB,iBAAO,eAAgB,MAAM,KAAK,EAAE;;MAExC;MAEO,UACL,OACA,UAGA,MACA,SAAe;AAEf,cAAM,QAAQ;UACZ,WAAW;UACX,CAAC,KAAK,EAAE,GAAG;;AAEb,cAAM,SAAS;AACf,yBAAiB,EAAE,QAAQ,MAAK;AAChC,YAAI;AAGF,iBAAO,SAAS,MAAM,SAAU,IAAK;;AAErC,2BAAiB;;MAErB;;;MAIA,OAAO,KACL,UAAkD;AAElD,cAAM,UAAU;AAChB,eAAO,WAAA;AACL,gBAAM,QAAQ;AACd,cAAI;AACF,6BAAiB;AACjB,mBAAO,SAAS,MAAM,MAAM,SAAgB;;AAE5C,6BAAiB;;QAErB;MACF;;MAGA,OAAO,UACL,UAGA,MACA,SAAe;AAEf,YAAI,gBAAgB;AAClB,gBAAM,QAAQ;AACd,cAAI;AACF,6BAAiB;AAGjB,mBAAO,SAAS,MAAM,SAAU,IAAK;;AAErC,6BAAiB;;eAEd;AACL,iBAAO,SAAS,MAAM,SAAU,IAAK;;MAEzC;;AAiBF,IAAM,YAAY;AAElB,IAAM;;IAGJA,OAAM,MAAM,UAAU;;;IAItBA,OAAM,MAAM,MAAM;;;IAIlB,uBAAO,OAAO,IAAI;AAIpB,IAAM,aAEF;AAEG,IAAM,OACX,WAAW,SAAS;;IAGnB,MAA4B,SAAS,KACrC,SAAUC,OAAI;AACb,UAAI;AACF,eAAO,eAAe,YAAY,WAAW;UAC3C,OAAOA;UACP,YAAY;UACZ,UAAU;;;;;;;UAOV,cAAc;SACf;;AAED,eAAOA;;IAEX,EAAG,cAAa,CAAE;;;;;ACtLpB,IAEe,MAAM;AAFrB,IAAAC,YAAA;;;AAEO,KAAM,EAAE,MAAM,cAAc;;;;;ACFnC,IAGa;AAHb;;IAAAC;AAUA,IAAAA;AAPO,IAAM,kBAAkB,IAAI,KAAI;;;;;ACejC,SAAU,iBAAiB,YAA0B;AACzD,QAAM,EAAE,YAAW,IAAK;AACxB,MAAI,OAAO,gBAAgB,YAAY;AACrC,eAAW,cAAc;AACzB,gBAAW;;AAEf;AAtBA,IACEC,iBAGW;AAJb;;AAAO,KAAM,EACX,gBAAAA,oBACE,OAAO;AAEJ,IAAM,eACX,MAAM,QACN,SAAU,KAAG;AACX,YAAM,QAAe,CAAA;AACrB,UAAI,QAAQ,UAAQ,MAAM,KAAK,IAAI,CAAC;AACpC,aAAO;IACT;;;;;ACFF,SAAS,OAAO,WAAgB,iBAAwB;AACtD,MAAI,CAAE,WAAW;AACf,UAAM,IAAI,MAAM,mBAAmB,mBAAmB;;AAE1D;AASA,SAAS,QAAQ,GAAe,GAAa;AAC3C,QAAM,MAAM,EAAE;AACd;;IAEE,MAAM;IAEN,QAAQ,EAAE;IAEV,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;;AAE5B;AAEA,SAAS,SAAY,OAAe;AAClC,UAAQ,MAAM,QAAQ;IACpB,KAAK;AAAG,YAAM,IAAI,MAAM,eAAe;IACvC,KAAK;AAAG,aAAO,MAAM,CAAC;IACtB,KAAK;AAAG,YAAM,MAAM,CAAC;;AAEzB;AAEA,SAAS,UAAa,OAAe;AACnC,SAAO,MAAM,MAAM,CAAC;AACtB;AAgHA,SAAS,eAAe,OAAe;AACrC,QAAM,SAAS,gBAAgB,SAAQ;AACvC,MAAI,QAAQ;AACV,UAAM,QAAQ,IAAI,MAAM;AAExB,QAAI,CAAE,OAAO,YAAY,IAAI,KAAK,GAAG;AACnC,aAAO,YAAY,IAAI,OAAO,CAAA,CAAE;;AAGlC,QAAI,aAAa,KAAK,GAAG;AACvB,uBAAiB,QAAQ,KAAK;WACzB;AACL,uBAAiB,QAAQ,KAAK;;AAGhC,WAAO;;AAEX;AAEA,SAAS,gBAAgB,OAAiB,MAAW;AACnD,iBAAe,KAAK;AAGpB,kBAAgB,UAAU,OAAO,mBAAmB,CAAC,OAAO,IAAI,CAAC;AAEjE,MAAI,eAAe,OAAO,IAAI,GAAG;AAG/B,aAAS,KAAK;;AAGhB,SAAO,SAAS,MAAM,KAAK;AAC7B;AAEA,SAAS,kBAAkB,OAAiB,MAAW;AACrD,QAAM,cAAc;AAEpB,QAAM,EAAE,gBAAe,IAAK;AAC5B,MAAI;AACJ,MAAI,mBAAmB,MAAM,MAAM,WAAW,GAAG;AAC/C,mBAAe,UAAU,MAAM,KAAK;;AAItC,QAAM,MAAM,SAAS;AAErB,MAAI;AAEF,UAAM,MAAM,CAAC,IAAI,MAAM,GAAG,MAAM,MAAM,IAAI;AAM1C,QAAI,mBAAmB,gBAAgB,CAAC,QAAQ,cAAc,MAAM,KAAK,GAAG;AAC1E,UAAI;AACF,cAAM,MAAM,CAAC,IAAI,gBAAgB,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,CAAC;eAChE,IAAM;;;WAMH,GAAG;AAEV,UAAM,MAAM,CAAC,IAAI;;AAInB,QAAM,cAAc;AACtB;AAEA,SAAS,aAAa,OAAe;AACnC,SAAO,MAAM,SAAS,CAAC,EAAE,MAAM,iBAAiB,MAAM,cAAc;AACtE;AAEA,SAAS,SAAS,OAAe;AAC/B,QAAM,QAAQ;AAEd,MAAI,aAAa,KAAK,GAAG;AAGvB;;AAGF,cAAY,KAAK;AACnB;AAEA,SAAS,YAAY,OAAe;AAClC,aAAW,OAAO,gBAAgB;AACpC;AAEA,SAAS,YAAY,OAAe;AAClC,aAAW,OAAO,gBAAgB;AACpC;AAEA,SAAS,WACP,OACA,UAAoD;AAEpD,QAAM,cAAc,MAAM,QAAQ;AAClC,MAAI,aAAa;AACf,UAAM,UAAU,aAAa,MAAM,OAAO;AAC1C,aAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACpC,eAAS,QAAQ,CAAC,GAAG,KAAK;;;AAGhC;AAGA,SAAS,iBAAiB,QAAkB,OAAe;AAGzD,SAAO,OAAO,YAAY,IAAI,KAAK,CAAC;AACpC,SAAO,aAAa,KAAK,CAAC;AAC1B,QAAM,iBAAiB,CAAC,aAAa,MAAM;AAE3C,MAAI,CAAE,OAAO,eAAe;AAC1B,WAAO,gBAAgB,aAAa,IAAG,KAAM,oBAAI;aAExC,OAAO,cAAc,IAAI,KAAK,GAAG;AAI1C;;AAGF,SAAO,cAAc,IAAI,KAAK;AAI9B,MAAI,gBAAgB;AAClB,gBAAY,MAAM;;AAEtB;AAGA,SAAS,iBAAiB,QAAkB,OAAe;AAGzD,SAAO,OAAO,YAAY,IAAI,KAAK,CAAC;AACpC,SAAO,CAAE,aAAa,KAAK,CAAC;AAE5B,QAAM,aAAa,OAAO,YAAY,IAAI,KAAK;AAC/C,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,YAAY,IAAI,OAAO,UAAU,MAAM,KAAK,CAAC;aAC3C,CAAE,QAAQ,YAAY,MAAM,KAAK,GAAG;AAC7C,WAAO,SAAQ;;AAGjB,mBAAiB,QAAQ,KAAK;AAE9B,MAAI,aAAa,MAAM,GAAG;AACxB;;AAGF,cAAY,MAAM;AACpB;AAEA,SAAS,iBAAiB,QAAkB,OAAe;AACzD,QAAM,KAAK,OAAO;AAClB,MAAI,IAAI;AACN,OAAG,OAAO,KAAK;AACf,QAAI,GAAG,SAAS,GAAG;AACjB,UAAI,aAAa,SAAS,kBAAkB;AAC1C,qBAAa,KAAK,EAAE;;AAEtB,aAAO,gBAAgB;;;AAG7B;AAIA,SAAS,eAAe,QAAgB;AACtC,MAAI,OAAO,YAAY,OAAO,GAAG;AAC/B,WAAO,YAAY,QAAQ,CAAC,QAAQ,UAAS;AAC3C,kBAAY,QAAQ,KAAK;IAC3B,CAAC;;AAKH,SAAO,WAAU;AAIjB,SAAO,OAAO,kBAAkB,IAAI;AACtC;AAEA,SAAS,YAAY,QAAkB,OAAe;AACpD,QAAM,QAAQ,OAAO,MAAM;AAC3B,SAAO,YAAY,OAAO,KAAK;AAC/B,mBAAiB,QAAQ,KAAK;AAChC;AAEA,SAAS,eAAe,OAAiB,MAAW;AAClD,MAAI,OAAO,MAAM,cAAc,YAAY;AACzC,QAAI;AACF,uBAAiB,KAAK;AACtB,YAAM,cAAc,MAAM,UAAU,MAAM,MAAM,IAAI;aAC7C,GAAG;AAKV,YAAM,SAAQ;AACd,aAAO;;;AAMX,SAAO;AACT;AAnXA,IAKM,cACA,kBA2CO;AAjDb;;;AAGA;AAEA,IAAM,eAA2B,CAAA;AACjC,IAAM,mBAAmB;AA2CnB,IAAO,QAAP,MAAO,OAAK;MAmBhB,YACkB,IAA8B;AAA9B,aAAA,KAAA;AAbF,aAAA,UAAU,oBAAI,IAAG;AACjB,aAAA,cAAc,oBAAI,IAAG;AAK9B,aAAA,gBAAsC;AAEtC,aAAA,QAAQ;AACR,aAAA,cAAc;AACL,aAAA,QAAuB,CAAA;AAuE/B,aAAA,OAA6B;AAlEnC,UAAE,OAAM;MACV;MAEO,OAAI;AACT,YAAI,KAAK,MAAM,WAAW,KAAK,CAAC,aAAa,IAAI,GAAG;AAClD,yBAAe,IAAI;AACnB,iBAAO,KAAK,MAAM,CAAC;;MAEvB;;;;;;;MAQO,UAAU,MAAW;AAC1B,eAAO,CAAE,KAAK,aAAa,qBAAqB;AAChD,uBAAe,IAAI;AACnB,eAAO,aAAa,IAAI,IACpB,gBAAgB,MAAM,IAAI,IAC1B,SAAS,KAAK,KAAK;MACzB;MAEO,WAAQ;AACb,YAAI,KAAK;AAAO;AAChB,aAAK,QAAQ;AACb,oBAAY,IAAI;AAIhB,yBAAiB,IAAI;MACvB;MAEO,UAAO;AACZ,aAAK,SAAQ;AAKb,uBAAe,IAAI;AAanB,mBAAW,MAAM,CAAC,QAAQ,UAAS;AACjC,iBAAO,SAAQ;AACf,sBAAY,QAAQ,IAAI;QAC1B,CAAC;MACH;MAEO,SAAM;AAIX,aAAK,QAAO;MACd;MAIO,SAASC,MAAa;AAC3B,QAAAA,KAAI,IAAI,IAAI;AACZ,YAAI,CAAE,KAAK,MAAM;AACf,eAAK,OAAO,aAAa,IAAG,KAAM,oBAAI,IAAG;;AAE3C,aAAK,KAAK,IAAIA,IAAG;MACnB;MAEO,aAAU;AACf,YAAI,KAAK,MAAM;AACb,uBAAa,KAAK,IAAI,EAAE,QAAQ,CAAAA,SAAOA,KAAI,OAAO,IAAI,CAAC;AACvD,eAAK,KAAK,MAAK;AACf,uBAAa,KAAK,KAAK,IAAI;AAC3B,eAAK,OAAO;;MAEhB;;AAxGc,UAAA,QAAQ;;;;;ACxBlB,SAAU,IAAU,SAEzB;AACC,QAAM,YAAY,oBAAI,IAAG;AACzB,QAAM,YAAY,WAAW,QAAQ;AAErC,WAAS,OAAO,KAAS;AACvB,UAAM,SAAS,gBAAgB,SAAQ;AACvC,QAAI,QAAQ;AACV,UAAIC,OAAM,UAAU,IAAI,GAAG;AAC3B,UAAI,CAACA,MAAK;AACR,kBAAU,IAAI,KAAKA,OAAM,oBAAI,KAAgB;;AAE/C,aAAO,SAASA,IAAG;AACnB,UAAI,OAAO,cAAc,YAAY;AACnC,yBAAiBA,IAAG;AACpB,QAAAA,KAAI,cAAc,UAAU,GAAG;;;EAGrC;AAEA,SAAO,QAAQ,SAAS,MACtB,KACA,iBAAiC;AAEjC,UAAMA,OAAM,UAAU,IAAI,GAAG;AAC7B,QAAIA,MAAK;AACP,YAAM,IACJ,mBACAC,gBAAe,KAAK,cAAc,eAAe,IAC/C,kBAAkB;AAItB,mBAAaD,IAAG,EAAE,QAAQ,WAAS,MAAM,CAAC,EAAC,CAAE;AAC7C,gBAAU,OAAO,GAAG;AACpB,uBAAiBA,IAAG;;EAExB;AAEA,SAAO;AACT;AAjEA,IASM;AATN;;;AACA;AAQA,IAAM,eAAe;MACnB,UAAU;MACV,SAAS;MACT,QAAQ;;;;;;;ACsBJ,SAAU,uBAAuB,MAAW;AAChD,QAAM,OAAO,mBACX,iBAAiB,IAAIE,MAAK,OAAO,YAAY,UAAU;AAEzD,SAAO,KAAK,YAAY,IAAI;AAC9B;AA8FM,SAAU,KAKd,kBAA+C,EAC/C,MAAM,KAAK,IAAI,GAAG,EAAE,GACpB,SACA,eAAgB,qBAChB,iBACA,WACA,OAAO,cAAc,YAAW,IAC8B,uBAAO,OAAO,IAAI,GAAC;AACjF,QAAM,QACJ,OAAO,gBAAgB,aACnB,IAAI,YAAY,KAAK,WAAS,MAAM,QAAO,CAAE,IAC7C;AAEN,QAAM,aAAa,WAAA;AACjB,UAAM,MAAM,aAAa,MACvB,MACA,UAAU,QAAQ,MAAM,MAAM,SAAgB,IAAI,SAAgB;AAGpE,QAAI,QAAQ,QAAQ;AAClB,aAAO,iBAAiB,MAAM,MAAM,SAAgB;;AAGtD,QAAI,QAAQ,MAAM,IAAI,GAAG;AACzB,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,KAAK,QAAQ,IAAI,MAAM,gBAAgB,CAAC;AAClD,YAAM,kBAAkB;AACxB,YAAM,YAAY;AAGlB,YAAM,SAAS,MAAM,MAAM,OAAO,GAAG;;AAGvC,UAAM,QAAQ,MAAM,UAClB,MAAM,UAAU,MAAM,KAAK,SAAS,CAAU;AAKhD,UAAM,IAAI,KAAK,KAAK;AAEpB,WAAO,IAAI,KAAK;AAKhB,QAAI,CAAE,gBAAgB,SAAQ,GAAI;AAChC,aAAO,QAAQ,CAAAC,WAASA,OAAM,MAAK,CAAE;AACrC,aAAO,MAAK;;AAGd,WAAO;EACT;AAEA,SAAO,eAAe,YAAY,QAAQ;IACxC,KAAK,MAAM,MAAM;IACjB,cAAc;IACd,YAAY;GACb;AAED,SAAO,OAAO,WAAW,UAAU;IACjC;IACA;IACA;IACA;IACA;IACA;GACD;AAED,WAAS,SAAS,KAA0B;AAC1C,UAAM,QAAQ,OAAO,MAAM,IAAI,GAAG;AAClC,QAAI,OAAO;AACT,YAAM,SAAQ;;EAElB;AACA,aAAW,WAAW;AACtB,aAAW,QAAQ,SAAS,QAAK;AAC/B,aAAS,aAAa,MAAM,MAAM,SAAgB,CAAC;EACrD;AAEA,WAAS,QAAQ,KAA0B;AACzC,UAAM,QAAQ,OAAO,MAAM,IAAI,GAAG;AAClC,QAAI,OAAO;AACT,aAAO,MAAM,KAAI;;EAErB;AACA,aAAW,UAAU;AACrB,aAAW,OAAO,SAAS,OAAI;AAC7B,WAAO,QAAQ,aAAa,MAAM,MAAM,SAAgB,CAAC;EAC3D;AAEA,WAAS,UAAU,KAA0B;AAC3C,WAAO,MAAM,MAAM,OAAO,GAAG,IAAI;EACnC;AACA,aAAW,YAAY;AACvB,aAAW,SAAS,SAAS,SAAM;AACjC,WAAO,UAAU,aAAa,MAAM,MAAM,SAAgB,CAAC;EAC7D;AAEA,aAAW,eAAe;AAC1B,aAAW,SAAS,UAAU,SAAS,SAAM;AAC3C,WAAO,aAAa,MAAM,MAAM,QAAQ,MAAM,MAAM,SAAgB,CAAC;EACvE,IAAI;AAEJ,SAAO,OAAO,OAAO,UAAU;AACjC;AArPA,IAmCI,gBAkGE;AArIN,IAAAC,YAAA;;IAAAA;AAEA,IAAAA;AACA;AACA;AASA;AAaA;AA2GA,IAAM,SAAS,oBAAI,IAAG;;;;;ACrGtB,SAAS,SAAS,UAAsB;AACtC,SAAO;AACT;AAlCA,IAoCA;AApCA;;;AACA;AACA;AACA;AAEA,IAAAC;AACA,IAAAA;AACA;AA6BA,IAAA;IAAA,WAAA;AA4CE,eAAAC,mBACE,WACA,SAAuD;AAAvD,YAAA,YAAA,QAAA;AAAA,oBAAoC,uBAAO,OAAO,IAAI;QAAC;AA1CxC,aAAA,cACf,gBAAgB,oBAAI,QAAO,IAAmB,oBAAI,IAAG;AA2CrD,aAAK,YAAY;AAEjB,YAAI,QAAQ,aAAa;AAEvB,eAAK,cAAc,QAAQ;QAC7B;AACA,aAAK,SAAS,QAAQ,UAAU;AAEhC,aAAK,WAAU;MACjB;AA7CQ,MAAAA,mBAAA,UAAA,cAAR,SACE,UAAsB;AAEtB,eAAO,CAAC,QAAQ;MAClB;AAEO,MAAAA,mBAAA,WAAP,WAAA;AAIE,eAAO,IAAIA,mBAAkB,UAAU,EAAE,OAAO,MAAK,CAAE;MACzD;AAEO,MAAAA,mBAAA,QAAP,SACE,WACA,MACA,OAAuD;AAAvD,YAAA,UAAA,QAAA;AAAA,kBAA2BA,mBAAkB,SAAQ;QAAE;AAEvD,eAAO,OAAO,OACZ,IAAIA;UACF,SAAC,UAAQ;AACP,gBAAM,oBAAoB,UAAU,QAAQ,IAAI,OAAO;AAEvD,mBAAO,kBAAkB,kBAAkB,QAAQ;UACrD;;UAEA,EAAE,OAAO,MAAK;QAAE,GAElB,EAAE,MAAM,MAAK,CAAE;MAEnB;AAoBA,MAAAA,mBAAA,UAAA,aAAA,WAAA;AAAA,YAAA,QAAA;AACE,YAAI,KAAK,QAAQ;AACf,cAAM,oBAAkB,IAAI,KAAc,aAAa;AACvD,eAAK,cAAc,KACjBA,mBAAkB,UAAU,YAAY,KAAK,IAAI,GACjD;YACE,cAAc,SAAC,UAAQ;AACrB,kBAAM,YAAY,MAAK,YAAY,QAAQ;AAC3C,kBAAI,WAAW;AACb,0BACE,MAAM,QAAQ,SAAS,GACvB,EAAA;AAEF,uBAAO,kBAAgB,YAAY,SAAS;cAC9C;YACF;YACA,KAAK,WAAW,yBAAyB;YACzC,OAAO;WACR;QAEL;MACF;AAEQ,MAAAA,mBAAA,UAAA,cAAR,SAAoB,UAAsB;AACxC,sBAAc,QAAQ;AACtB,eAAO,KAAK,UAAU,QAAQ;MAChC;AAEA,MAAAA,mBAAA,UAAA,oBAAA,SAAkB,UAAsB;AAGtC,YAAI,KAAK,YAAY,IAAI,QAAQ,GAAG;AAClC,iBAAO;QACT;AAEA,YAAM,sBAAsB,KAAK,YAAY,QAAQ;AAErD,aAAK,YAAY,IAAI,mBAAmB;AAExC,eAAO;MACT;AAEA,MAAAA,mBAAA,UAAA,SAAA,SAAO,gBAAiC;AAAxC,YAAA,QAAA;AACE,eAAO,OAAO,OACZ,IAAIA;UACF,SAAC,UAAQ;AACP,mBAAO,eAAe,kBACpB,MAAK,kBAAkB,QAAQ,CAAC;UAEpC;;UAEA,EAAE,OAAO,MAAK;QAAE,GAElB;UACE,MAAM;UACN,OAAO;SACR;MAEL;AAYF,aAAAA;IAAA,EApIA;;;;;ACnCA,IAQI,YACSC;AATb;;;AACA;AAKA;AAGO,IAAMA,SAAQ,OAAO,OAC1B,SAAC,KAAY;AACX,UAAIC,UAAS,WAAW,IAAI,GAAG;AAE/B,UAAI,CAACA,SAAQ;AACX,QAAAA,UAAS,MAAU,GAAG;AACtB,mBAAW,IAAI,KAAKA,OAAM;MAC5B;AACA,aAAOA;IACT,GACA;MACE,OAAK,WAAA;AACH,qBAAa,IAAI;UACf,WAAW,SAAK;;QAA2B;MAE/C;KACD;AAEH,IAAAD,OAAM,MAAK;AAEX,QAAI,WAAU,YAAA,OAAA;AACZ,0BAAoB,SAAS,WAAA;AAAM,eAAC,aAAa,WAAW,OAAO;MAAhC,CAAkC;IACvE;;;;;AC7BM,SAAU,gBAAmB,OAAoB;AACrD,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AAChD;AALA,IACa;AADb;;AACO,IAAM,UAAmD,MAAM;;;;;ACiEtE,SAAS,QACP,IACA,aAAwB;AAExB,SACE,CAAC,MACD,GAAG,aAAa,WAAW,MACzB,SAAC,WAAS;AACR,WAAA,UAAU,SAAS,KAAK,mBACxB,QAAQ,YAAY,UAAU,KAAK,KAAK,GAAG,WAAW;EADtD,CACuD;AAG/D;AAEA,SAAS,iBAAiB,KAAiB;AACzC,SACI,QACE,uBAAuB,GAAG,KAAK,sBAAsB,GAAG,GACxD,kBAAkB,uBAAuB,GAAG,CAAC,CAAC,IAGhD,OACA;AACN;AAEA,SAAS,oBACP,SAAuD;AAEvD,MAAM,QAAQ,oBAAI,IAAG;AAErB,MAAM,QAAQ,oBAAI,IAAG;AAKrB,UAAQ,QAAQ,SAAC,WAAS;AACxB,QAAI,WAAW;AACb,UAAI,UAAU,MAAM;AAClB,cAAM,IAAI,UAAU,MAAM,SAAS;MACrC,WAAW,UAAU,MAAM;AACzB,cAAM,IAAI,UAAU,MAAM,SAAS;MACrC;IACF;EACF,CAAC;AAED,SAAO,SAAC,WAAwB;AAC9B,QAAI,SAAS,MAAM,IAAI,UAAU,KAAK,KAAK;AAC3C,QAAI,CAAC,UAAU,MAAM,MAAM;AACzB,YAAM,QAAQ,SAAC,YAAY,MAAI;AAC7B,YAAI,KAAK,SAAS,GAAG;AACnB,mBAAS;QACX;MACF,CAAC;IACH;AACA,WAAO;EACT;AACF;AAcA,SAAS,wBAA8B,YAAgB;AACrD,MAAM,MAAM,oBAAI,IAAG;AAEnB,SAAO,SAAS,oBACd,KAAsB;AAAtB,QAAA,QAAA,QAAA;AAAA,YAAA;IAAsB;AAEtB,QAAI,QAAQ,IAAI,IAAI,GAAG;AACvB,QAAI,CAAC,OAAO;AACV,UAAI,IACF,KACC,QAAQ;;;;;QAKP,WAAW,oBAAI,IAAG;QAClB,iBAAiB,oBAAI,IAAG;OACxB;IAEN;AACA,WAAO;EACT;AACF;AAEM,SAAU,6BACd,YACA,KAAiB;AAEjB,gBAAc,GAAG;AAMjB,MAAM,0BAA0B,wBAAgC,EAAE;AAClE,MAAM,yBAAyB,wBAAgC,EAAE;AACjE,MAAM,WAAW,SACf,WAAoD;AAEpD,aACM,IAAI,GAAG,WAAQ,QACnB,IAAI,UAAU,WAAW,WAAW,UAAU,CAAC,IAC/C,EAAE,GACF;AACA,UAAI,QAAQ,QAAQ;AAAG;AACvB,UAAI,SAAS,SAAS,KAAK,sBAAsB;AAE/C,eAAO,wBAAwB,SAAS,QAAQ,SAAS,KAAK,KAAK;MACrE;AACA,UAAI,SAAS,SAAS,KAAK,qBAAqB;AAC9C,eAAO,uBAAuB,SAAS,KAAK,KAAK;MACnD;IACF;AACA,eAAU,YAAM,SAAA,UAAA,MAAA,EAAA;AAChB,WAAO;EACT;AAEA,MAAI,iBAAiB;AACrB,WAAS,IAAI,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,QAAI,IAAI,YAAY,CAAC,EAAE,SAAS,KAAK,sBAAsB;AACzD,QAAE;IACJ;EACF;AAEA,MAAM,mBAAmB,oBAAoB,UAAU;AACvD,MAAM,oBAAoB,SAAC,gBAAuC;AAChE,WAAA,gBAAgB,cAAc,KAC9B,eACG,IAAI,gBAAgB,EACpB,KACC,SAAC,QAAyC;AAAK,aAAA,UAAU,OAAO;IAAjB,CAAuB;EAJ1E;AAOF,MAAM,6BAA6B,oBAAI,IAAG;AAO1C,MAAI,wBAAwB;AAE5B,MAAM,+BAEF;IACF,OAAK,SAAC,MAAI;AACR,UAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,gCAAwB;AACxB,eAAO;MACT;IACF;;AAGF,MAAM,8BAA8B,MAAM,KAAK;;IAE7C,OAAO;IACP,gBAAgB;IAEhB,oBAAoB;MAClB,OAAK,WAAA;AAKH,eAAO;MACT;;IAGF,UAAU;MACR,OAAK,SAAC,MAAM,MAAM,SAAS,OAAO,WAAS;AACzC,YAAM,QAAQ,SAAS,SAAS;AAChC,YAAI,OAAO;AACT,gBAAM,UAAU,IAAI,KAAK,KAAK,KAAK;QACrC;MACF;;IAGF,gBAAgB;MACd,OAAK,SAAC,MAAM,MAAM,SAAS,OAAO,WAAS;AACzC,YAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,kCAAwB;AACxB,iBAAO;QACT;AACA,YAAM,QAAQ,SAAS,SAAS;AAChC,YAAI,OAAO;AACT,gBAAM,gBAAgB,IAAI,KAAK,KAAK,KAAK;QAC3C;MAMF;;IAGF,oBAAoB;MAClB,OAAK,SAAC,MAAM,MAAM,SAAS,MAAI;AAC7B,mCAA2B,IAAI,KAAK,UAAU,IAAI,GAAG,IAAI;MAC3D;MACA,OAAK,SAAC,MAAM,MAAM,SAAS,MAAI;AAC7B,YAAM,eAAe,2BAA2B,IAC9C,KAAK,UAAU,IAAI,CAAC;AAEtB,YAAI,SAAS,cAAc;AAOzB,iBAAO;QACT;AAEA;;;;UAIE,iBAAiB,KACjB,KAAK,aAAa,WAAW,MAC3B,SAAC,WAAS;AACR,mBAAA,UAAU,SAAS,KAAK,SACxB,UAAU,KAAK,UAAU;UADzB,CACqC;UAEzC;AAIA,iCAAuB,KAAK,KAAK,KAAK,EAAE,UAAU;AAClD,kCAAwB;AACxB,iBAAO;QACT;MACF;;IAGF,WAAW;MACT,OAAK,SAAC,MAAI;AAIR,YAAI,iBAAiB,IAAI,GAAG;AAC1B,kCAAwB;AACxB,iBAAO;QACT;MACF;;GAEH;AAED,MAAI,CAAC,uBAAuB;AAG1B,WAAO;EACT;AAOA,MAAM,yBAAyB,SAAC,OAAwB;AACtD,QAAI,CAAC,MAAM,gBAAgB;AACzB,YAAM,iBAAiB,IAAI,IAAI,MAAM,SAAS;AAC9C,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,gBAAgB,QAAQ,SAAC,mBAAiB;AAC9C,iCACE,uBAAuB,iBAAiB,CAAC,EACzC,eAAgB,QAAQ,SAAC,SAAO;AAChC,kBAAM,eAAgB,IAAI,OAAO;UACnC,CAAC;QACH,CAAC;MACH;IACF;AACA,WAAO;EACT;AAKA,MAAM,uBAAuB,oBAAI,IAAG;AACpC,8BAA4B,YAAY,QAAQ,SAAC,KAAG;AAClD,QAAI,IAAI,SAAS,KAAK,sBAAsB;AAC1C,6BACE,wBAAwB,IAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,EACnD,gBAAgB,QAAQ,SAAC,mBAAiB;AAC1C,6BAAqB,IAAI,iBAAiB;MAC5C,CAAC;IACH,WACE,IAAI,SAAS,KAAK;;;;IAKlB,mBAAmB,KACnB,CAAC,uBAAuB,IAAI,KAAK,KAAK,EAAE,SACxC;AACA,2BAAqB,IAAI,IAAI,KAAK,KAAK;IACzC;EACF,CAAC;AAID,uBAAqB,QAAQ,SAAC,cAAY;AAGxC,2BACE,uBAAuB,YAAY,CAAC,EACpC,gBAAgB,QAAQ,SAAC,mBAAiB;AAC1C,2BAAqB,IAAI,iBAAiB;IAC5C,CAAC;EACH,CAAC;AAED,MAAM,wBAAwB,SAAC,cAAoB;AACjD,WAAA,CAAC;;;KAKG,CAAC,qBAAqB,IAAI,YAAY,KACtC,uBAAuB,YAAY,EAAE;EANzC;AAUF,MAAM,eAEF;IACF,OAAK,SAAC,MAAI;AACR,UAAI,sBAAsB,KAAK,KAAK,KAAK,GAAG;AAC1C,eAAO;MACT;IACF;;AAGF,SAAO,iBACL,MAAM,6BAA6B;;;IAGjC,gBAAgB;;IAGhB,oBAAoB;IAEpB,qBAAqB;MACnB,OAAK,SAAC,MAAI;AAGR,YAAI,KAAK,qBAAqB;AAC5B,cAAM,sBAAoB;;YAExB,wBAAwB,KAAK,QAAQ,KAAK,KAAK,KAAK;UAAC,EACrD;AAaF,cAAI,oBAAkB,OAAO,KAAK,oBAAoB,QAAQ;AAC5D,mBAAA,SAAA,SAAA,CAAA,GACK,IAAI,GAAA,EACP,qBAAqB,KAAK,oBAAoB,OAAO,SAAC,QAAM;AAC1D,qBAAA,oBAAkB,IAAI,OAAO,SAAS,KAAK,KAAK;YAAhD,CAAiD,EAClD,CAAA;UAEL;QACF;MACF;;GAEH,CAAC;AAEN;AA+EE,SAAA,sCAAA,KAAA;AAEF,SAAM,6BAAU,CAAA,sBAAuD,GAAA,cAAA,GAAA,CAAA;;AA2CvE,SAAC,mBAAA,QAAA;AAED,SAAS,SAAA,gBAAkD,UAAA;AACzD,WAAO,OAAS,KAAA,SAAgB,SAAsB;AACpD,aAAO,SACL,SACE,SAAA,MAAS,SAAK,KAAA,YACd,SAAS,MAAM,SACf,QAAS,SAAM,SAAI,MAAA,KAAA,SAClB,QAAY,QAAK,QAAS,KAAM,QAAK;;;;AAI7C,SAAA,4BAAA,QAAA,KAAA;AAED,MAAM,aAAU,mBAAA,MACd;AAGA,SAAM,iBAAa,MAAA,KAAkB;IAErC,qBACE;MACE,OAAA,SAAqB,MAAA;AACnB,eAAK,SAAC,SAAI,CAAA,GAAA,IAAA,GAAA;;UAGN,qBAAA,KAAA,sBACA,KAAA,oBACO,OAAA,SAAoB,QAAC;AACpB,mBAAC,CAAA,OAAA,KAAmB,SACtB,KAAA;AAAA,qBAAO,IAAA,SAAA,OAAA,SAAA,KAAA;YAAA,CAAA;eAIR,CAAA;QAAA,CAAA;;;WAIV;MAED,OAAO,SAAA,MAAA;AAGH,YAAA,oBAAA,OAAA,KAAA,SAAA,WAAkD;AAAA,iBAAA,UAAA;QAAA,CAAA;AAClD,YAAM,mBAAiB;AAInB,cAAA,kBAAoB;AACtB,cAAI,KAAA,WAAa;AACb,iBAAK,UAAS,QAAG,SAAA,KAAA;AACf,kBAAC,WAAU,GAAO,GAAC;AACjB,mCAAkB;;;;AAI1B,cAAC,oBAAA,GAAA;AAEG,mBAAA;;;;;cAKT;MAED,OAAU,SAAA,MAAA;AAEN,YAAA,WAAA,IAAA,GAAA;AACI,iBAAA;;;;;;AAOb,SAAA,iCAAA,QAAA,KAAA;AAED,WAAM,MAAU,MAAA;AAId,QAAA,OACE,KAAiD,SAAA,KAAA;AAAA,aAAA,IAAA,SAAA,KAAA,KAAA;IAAA,CAAA,GAAA;AAE7C,aAAO;;;AAGb,SAAC,iBAAA,MAAA,KAAA;IAED,gBAAO,EAAA,MACM;IACT,oBAAkB,EAAA,MAAO;;;AAQ/B,SAAA,2BAAkC,UAAA;AAClC,MAAM,aAAU,kBAAA,QACd;AAEA,MAAM,sBAAa,WAAkB;AACrC,MAAM,wBAAgD,SAAY;AAGhE,WAAA;;AAIF,MAAA,cAAA,MAAA,UAAA;IACM,qBAAoB;MACxB,OAAA,SAAqB,MAAA;AACnB,eAAK,SAAC,SAAI,CAAA,GAAA,IAAA,GAAA,EAAA,WAAA,QAAA,CAAA;;;;SAOX;;AAIL,SAAA,6BAAA,UAAA;AACA,gBAAgB,QAAA;AAGd,MAAA,cAAc,6BAAU;IAEpB;MAEA,MAAA,SAAA,WAAA;AAAA,eAAA,UAAA,KAAA,UAAA;MAAA;MACE,QAAM;;aAEP;SAEH;;IA/oBE,gBA0YO,uBA4DP;;;;AAhgBN;AAiBA;AAEA;AAOA;AAEA;AACA;AA6BA,IAAM,iBAA4B;MAChC,MAAM,KAAK;MACX,MAAM;QACJ,MAAM,KAAK;QACX,OAAO;;;AAsYJ,IAAM,wBAAwB,OAAO,OAC1C,SAAiC,KAAU;AACzC,aAAO,MAAM,KAAK;QAChB,cAAc;UACZ,OAAK,SAAC,MAAM,MAAM,QAAM;AAEtB,gBACE,UACC,OAAmC,SAClC,KAAK,sBACP;AACA;YACF;AAGQ,gBAAA,aAAe,KAAI;AAC3B,gBAAI,CAAC,YAAY;AACf;YACF;AAIA,gBAAM,OAAO,WAAW,KAAK,SAAC,WAAS;AACrC,qBACE,QAAQ,SAAS,MAChB,UAAU,KAAK,UAAU,gBACxB,UAAU,KAAK,MAAM,YAAY,MAAM,CAAC,MAAM;YAEpD,CAAC;AACD,gBAAI,MAAM;AACR;YACF;AAIA,gBAAM,QAAQ;AACd,gBACE,QAAQ,KAAK,KACb,MAAM,cACN,MAAM,WAAW,KAAK,SAAC,GAAC;AAAK,qBAAA,EAAE,KAAK,UAAU;YAAjB,CAAyB,GACtD;AACA;YACF;AAGA,mBAAA,SAAA,SAAA,CAAA,GACK,IAAI,GAAA,EACP,YAAU,cAAA,cAAA,CAAA,GAAM,YAAU,IAAA,GAAA,CAAE,cAAc,GAAA,KAAA,EAAA,CAAA;UAE9C;;OAEH;IACH,GACA;MACE,OAAK,SAAC,OAAgB;AACpB,eAAO,UAAU;MACnB;KACD;AAGH,IAAM,yBAAyB;MAC7B,MAAM,SAAC,WAAwB;AAC7B,YAAM,aAAa,UAAU,KAAK,UAAU;AAC5C,YAAI,YAAY;AACd,cACE,CAAC,UAAU,aACX,CAAC,UAAU,UAAU,KAAK,SAAC,KAAG;AAAK,mBAAA,IAAI,KAAK,UAAU;UAAnB,CAAwB,GAC3D;AACA,uBAAU,YACR,SAAA,UAAA,KAAA,EAAA;;;AAIN,eAAC;;;;;;;AC1gBL,SAAS,YACP,UACA,WAAgD;;AAEhD,WAAO,KAAA,uBAAuB,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,eAAc;AACzD;AAEM,SAAU,oBAAoB,UAAsB;AACxD,SAAO,YAAY,UAAU,UAAU;AACzC;AAEM,SAAU,iBAAiB,UAAsB;AACrD,SAAO,YAAY,UAAU,OAAO;AACtC;AAEM,SAAU,wBAAwB,UAAsB;AAC5D,SAAO,YAAY,UAAU,cAAc;AAC7C;AAnBA;;;;;;;AC0BM,SAAU,YAAS;AACvB,MAAA,UAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAa;AAAb,YAAA,EAAA,IAAA,UAAA,EAAA;;AAEA,SAAO,eAAe,OAAO;AAC/B;AAQM,SAAU,eAAkB,SAAY;AAC5C,MAAI,SAAS,QAAQ,CAAC,KAAM,CAAA;AAC5B,MAAM,QAAQ,QAAQ;AACtB,MAAI,QAAQ,GAAG;AACb,QAAM,SAAS,IAAI,WAAU;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,eAAS,OAAO,MAAM,QAAQ,QAAQ,CAAC,CAAC;IAC1C;EACF;AACA,SAAO;AACT;IA/CQE,iBAyDF,mBAQN;;;;AAnEA;AAEQ,IAAAA,kBAAmB,OAAO,UAAS;AAyD3C,IAAM,oBAA+C,SACnD,QACA,QACA,UAAQ;AAER,aAAO,KAAK,MAAM,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC;IACtD;AAEA,IAAA;IAAA,WAAA;AACE,eAAAC,YACU,YAA2G;AAA3G,YAAA,eAAA,QAAA;AAAA,uBAA+C;QAA4D;AAA3G,aAAA,aAAA;AAqCH,aAAA,WAAW;AAEV,aAAA,aAAa,oBAAI,IAAG;MAtCzB;AAEI,MAAAA,YAAA,UAAA,QAAP,SAAa,QAAa,QAAW;AAArC,YAAA,QAAA;AAAuC,YAAA,UAAA,CAAA;iBAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAwB;AAAxB,kBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AACrC,YAAI,gBAAgB,MAAM,KAAK,gBAAgB,MAAM,GAAG;AACtD,iBAAO,KAAK,MAAM,EAAE,QAAQ,SAAC,WAAS;AACpC,gBAAID,gBAAe,KAAK,QAAQ,SAAS,GAAG;AAC1C,kBAAM,cAAc,OAAO,SAAS;AACpC,kBAAI,OAAO,SAAS,MAAM,aAAa;AACrC,oBAAME,UAAS,MAAK,WAAU,MAAf,OAAI,cAAA;kBACjB;kBACA;kBACA;gBAAS,GACN,SAAO,KAAA,CAAA;AAIZ,oBAAIA,YAAW,aAAa;AAC1B,2BAAS,MAAK,oBAAoB,MAAM;AACxC,yBAAO,SAAS,IAAIA;gBACtB;cACF;YACF,OAAO;AAGL,uBAAS,MAAK,oBAAoB,MAAM;AACxC,qBAAO,SAAS,IAAI,OAAO,SAAS;YACtC;UACF,CAAC;AAED,iBAAO;QACT;AAGA,eAAO;MACT;AAMO,MAAAD,YAAA,UAAA,sBAAP,SAA8B,OAAQ;AACpC,YAAI,gBAAgB,KAAK,GAAG;AAC1B,cAAI,CAAC,KAAK,WAAW,IAAI,KAAK,GAAG;AAC/B,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,sBAAS,MAAc,MAAM,CAAC;YAChC,OAAO;AACL,sBAAK,SAAA,EACH,WAAW,OAAO,eAAe,KAAK,EAAC,GACpC,KAAK;YAEZ;AACA,iBAAK,WAAW,IAAI,KAAK;UAC3B;QACF;AACA,eAAO;MACT;AACF,aAAAA;IAAA,EA3DA;;;;;AC1DM,SAAU,iBACd,SAAwB;AAAxB,MAAA,YAAA,QAAA;AAAA,cAAA;EAAwB;AAExB,SAAO;IACL;IACA,OAAK,SAAC,UAAU,UAAQ;AACtB,aAAO,WAAU,cAAA,cAAA,CAAA,GAAK,UAAQ,IAAA,GAAK,UAAQ,IAAA,IAAI;IACjD;;AAEJ;AAMM,SAAU,sBACd,SAAwB;AAAxB,MAAA,YAAA,QAAA;AAAA,cAAA;EAAwB;AAExB,SAAO;IACL;IACA,OAAK,SAAC,UAAU,UAAU,IAAQ;UAAN,OAAI,GAAA;AAC9B,UAAM,SAAS,WAAW,SAAS,MAAM,CAAC,IAAI,CAAA;AAE9C,UAAI,UAAU;AACZ,YAAI,MAAM;AAEA,cAAA,KAAe,KAAI,QAAnB,SAAM,OAAA,SAAG,IAAC;AAClB,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,mBAAO,SAAS,CAAC,IAAI,SAAS,CAAC;UACjC;QACF,OAAO;AAKL,iBAAO,KAAI,MAAX,QAAe,QAAQ;QACzB;MACF;AAEA,aAAO;IACT;;AAEJ;AA0CM,SAAU,qBACd,SAAwB;AAAxB,MAAA,YAAA,QAAA;AAAA,cAAA;EAAwB;AAExB,SAAO;IACL;IAEA,MAAI,SAAC,UAAU,IAAsB;UAApB,UAAO,GAAA,SAAE,YAAS,GAAA;AACjC,UAAI,CAAC;AAAU,eAAO;AAEtB,UAAM,QAA6B,CAAA;AACnC,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,eAAS,MAAM,QAAQ,SAAC,MAAI;AAG1B,YAAI,QAAQ,UAAU,QAAQ,IAAI,CAAC,GAAG;AACpC,gBAAM,KAAK,IAAI;AACf,cAAI,KAAK,QAAQ;AACf,8BAAkB,mBAAmB,KAAK,UAAU;AACpD,6BAAiB,KAAK,UAAU;UAClC;QACF;MACF,CAAC;AAED,UAAI,MAAM,SAAS,KAAK,oBAAoB,gBAAgB;AAC1D,0BAAkB;MACpB;AAEM,UAAA,KAA6B,SAAS,YAAY,CAAA,GAAhD,cAAW,GAAA,aAAE,YAAS,GAAA;AAE9B,aAAA,SAAA,SAAA,CAAA,GAIK,UAAU,QAAQ,CAAC,GAAA,EACtB,OACA,UAAQ,SAAA,SAAA,CAAA,GACH,SAAS,QAAQ,GAAA;;;QAGpB,aAAa,eAAe;QAC5B,WAAW,aAAa;MAAc,CAAA,EAAA,CAAA;IAG5C;IAEA,OAAK,SAAC,UAAU,UAAU,IAAgC;UAA9B,OAAI,GAAA,MAAEE,eAAW,GAAA,aAAE,YAAS,GAAA;AACtD,UAAI,CAAC,UAAU;AACb,mBAAW,cAAa;MAC1B;AAEA,UAAI,CAAC,UAAU;AACb,eAAO;MACT;AAEA,UAAM,gBACJ,SAAS,QACP,SAAS,MAAM,IAAI,SAAC,MAAI;AACtB,YAAIA,aAAa,OAAI,SAAA,CAAA,GAAQ,IAAI,CAAG,GAAG;AAGrC,eAAK,SAAS,UAAkB,UAAU,IAAI;QAChD;AACA,eAAO;MACT,CAAC,IACD,CAAA;AAEJ,UAAI,SAAS,UAAU;AACb,YAAA,aAAa,SAAQ;AACrB,YAAA,cAA2B,WAAQ,aAAtB,YAAc,WAAQ;AAC3C,YAAM,YAAY,cAAc,CAAC;AACjC,YAAM,WAAW,cAAc,cAAc,SAAS,CAAC;AAGvD,YAAI,aAAa,aAAa;AAC5B,oBAAU,SAAS;QACrB;AACA,YAAI,YAAY,WAAW;AACzB,mBAAS,SAAS;QACpB;AAGA,YAAM,cAAc,aAAa,UAAU;AAC3C,YAAI,eAAe,CAAC,aAAa;AAC/B,qBAAW,UAAU,UAAU;YAC7B,UAAU;cACR,aAAa;;WAEhB;QACH;AACA,YAAM,aAAa,YAAY,SAAS;AACxC,YAAI,cAAc,CAAC,WAAW;AAC5B,qBAAW,UAAU,UAAU;YAC7B,UAAU;cACR,WAAW;;WAEd;QACH;MACF;AAEA,UAAI,SAAS,SAAS;AACtB,UAAI,SAAwB,CAAA;AAE5B,UAAI,QAAQ,KAAK,OAAO;AAItB,YAAM,QAAQ,OAAO,UAAU,SAAC,MAAI;AAAK,iBAAA,KAAK,WAAW,KAAK;QAArB,CAA0B;AACnE,YAAI,SAAS,GAAG;AACd,mBAAS,OAAO,MAAM,GAAG,QAAQ,CAAC;QAEpC;MACF,WAAW,QAAQ,KAAK,QAAQ;AAC9B,YAAM,QAAQ,OAAO,UAAU,SAAC,MAAI;AAAK,iBAAA,KAAK,WAAW,KAAK;QAArB,CAA2B;AACpE,iBAAS,QAAQ,IAAI,SAAS,OAAO,MAAM,KAAK;AAChD,iBAAS,CAAA;MACX,WAAW,SAAS,OAAO;AAIzB,iBAAS,CAAA;MACX;AAEA,UAAM,QAAK,cAAA,cAAA,cAAA,CAAA,GAAO,QAAM,IAAA,GAAK,eAAa,IAAA,GAAK,QAAM,IAAA;AAErD,UAAM,WAAQ,SAAA,SAAA,CAAA,GAMT,SAAS,QAAQ,GACjB,SAAS,QAAQ;AAGtB,UAAI,SAAS,UAAU;AACrB,YAAM,KAMF,SAAS,UALX,kBAAe,GAAA,iBACf,cAAW,GAAA,aACX,cAAW,GAAA,aACX,YAAS,GAAA,WACN,SAAM,OAAA,IALL,CAAA,mBAAA,eAAA,eAAA,WAAA,CAML;AAMD,eAAO,OAAO,UAAU,MAAM;AAS9B,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,WAAW;AACb,qBAAS,kBAAkB;AAC7B,cAAI,WAAW;AAAa,qBAAS,cAAc;QACrD;AACA,YAAI,CAAC,OAAO,QAAQ;AAClB,cAAI,WAAW;AAAa,qBAAS,cAAc;AACnD,cAAI,WAAW;AAAW,qBAAS,YAAY;QACjD;MACF;AAEA,aAAA,SAAA,SAAA,SAAA,CAAA,GACK,UAAU,QAAQ,CAAC,GACnB,UAAU,QAAQ,CAAC,GAAA,EACtB,OACA,SAAQ,CAAA;IAEZ;;AAEJ;AAMA,SAAS,gBAAa;AACpB,SAAO;IACL,OAAO,CAAA;IACP,UAAU;MACR,iBAAiB;MACjB,aAAa;MACb,aAAa;MACb,WAAW;;;AAGjB;IAbM,WACA;;;;AAjRN;AAGA;AA6QA,IAAM,YAAY,SAAC,KAAwB;AAAK,aAAA,OAAO,KAAK,SAAS;IAArB;AAChD,IAAM,YAAY,CAAC,SAAS,UAAU;;;;;ACjRtC,SAAS,gCAAgC,GAAG,gBAAgB;AAAE,MAAI,KAAK,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,MAAI,GAAI,SAAQ,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK,KAAK,EAAE;AAAG,MAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,4BAA4B,CAAC,MAAM,kBAAkB,KAAK,OAAO,EAAE,WAAW,UAAU;AAAE,QAAI,GAAI,KAAI;AAAI,QAAI,IAAI;AAAG,WAAO,WAAY;AAAE,UAAI,KAAK,EAAE,OAAQ,QAAO,EAAE,MAAM,KAAK;AAAG,aAAO,EAAE,MAAM,OAAO,OAAO,EAAE,GAAG,EAAE;AAAA,IAAG;AAAA,EAAG;AAAE,QAAM,IAAI,UAAU,uIAAuI;AAAG;AAE3lB,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC,EAAG;AAAQ,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAG;AAE/Z,SAAS,kBAAkB,KAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK;AAAE,SAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAAG;AAAE,SAAO;AAAM;AAEtL,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAAG;AAAE;AAE5T,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,SAAO;AAAa;AAuB5R,SAAS,UAAU,KAAK,KAAK;AAC3B,MAAI,QAAQ,IAAI,GAAG;AACnB,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,QAAQ,oBAAoB;AACjF,SAAO;AACT;AAEA,SAAS,WAAW,KAAK;AACvB,MAAI,OAAO,IAAI;AAEf,MAAI,SAAS,QAAW;AACtB,WAAO,KAAK,aAAa;AAEzB,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,SAAS,SAAY,OAAO;AACrC;AAEA,SAAS,aAAa,GAAG;AACvB,SAAO,aAAa;AACtB;AAEA,SAAS,gBAAgB,GAAG;AAC1B,MAAI,gBAAgB,KAAK;AACvB,oBAAgB,IAAI,CAAC;AAAA,EACvB,OAAO;AACL,eAAW,WAAY;AACrB,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAEA,SAAS,QAAQ,IAAI;AACnB,UAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,QAAI;AACF,SAAG;AAAA,IACL,SAAS,GAAG;AACV,sBAAgB,CAAC;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,oBAAoB,cAAc;AACzC,MAAI,UAAU,aAAa;AAC3B,MAAI,YAAY,OAAW;AAC3B,eAAa,WAAW;AAExB,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AAEA,MAAI;AACF,QAAI,OAAO,YAAY,YAAY;AACjC,cAAQ;AAAA,IACV,OAAO;AACL,UAAI,cAAc,UAAU,SAAS,aAAa;AAElD,UAAI,aAAa;AACf,oBAAY,KAAK,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,oBAAgB,CAAC;AAAA,EACnB;AACF;AAEA,SAAS,kBAAkB,cAAc;AACvC,eAAa,YAAY;AACzB,eAAa,SAAS;AACtB,eAAa,SAAS;AACxB;AAEA,SAAS,kBAAkB,cAAc;AACvC,MAAI,QAAQ,aAAa;AAEzB,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA,eAAa,SAAS;AACtB,eAAa,SAAS;AAEtB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,uBAAmB,cAAc,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,KAAK;AAC9D,QAAI,aAAa,WAAW,SAAU;AAAA,EACxC;AACF;AAEA,SAAS,mBAAmB,cAAc,MAAM,OAAO;AACrD,eAAa,SAAS;AACtB,MAAI,WAAW,aAAa;AAE5B,MAAI;AACF,QAAI,IAAI,UAAU,UAAU,IAAI;AAEhC,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,YAAI,EAAG,GAAE,KAAK,UAAU,KAAK;AAC7B;AAAA,MAEF,KAAK;AACH,0BAAkB,YAAY;AAC9B,YAAI,EAAG,GAAE,KAAK,UAAU,KAAK;AAAA,YAAO,OAAM;AAC1C;AAAA,MAEF,KAAK;AACH,0BAAkB,YAAY;AAC9B,YAAI,EAAG,GAAE,KAAK,QAAQ;AACtB;AAAA,IACJ;AAAA,EACF,SAAS,GAAG;AACV,oBAAgB,CAAC;AAAA,EACnB;AAEA,MAAI,aAAa,WAAW,SAAU,qBAAoB,YAAY;AAAA,WAAW,aAAa,WAAW,UAAW,cAAa,SAAS;AAC5I;AAEA,SAAS,SAAS,cAAc,MAAM,OAAO;AAC3C,MAAI,aAAa,WAAW,SAAU;AAEtC,MAAI,aAAa,WAAW,aAAa;AACvC,iBAAa,OAAO,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,IACF,CAAC;AAED;AAAA,EACF;AAEA,MAAI,aAAa,WAAW,SAAS;AACnC,iBAAa,SAAS;AACtB,iBAAa,SAAS,CAAC;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC;AACD,YAAQ,WAAY;AAClB,aAAO,kBAAkB,YAAY;AAAA,IACvC,CAAC;AACD;AAAA,EACF;AAEA,qBAAmB,cAAc,MAAM,KAAK;AAC9C;AAhLA,IAWI,YAIA,WAIA,WAQA,gBACA,kBACA,eAqJA,cAsCA,sBA6BA;AArPJ;AAAA;AAWA,IAAI,aAAa,WAAY;AAC3B,aAAO,OAAO,WAAW;AAAA,IAC3B;AAEA,IAAI,YAAY,SAAU,MAAM;AAC9B,aAAO,WAAW,KAAK,QAAQ,OAAO,IAAI,CAAC;AAAA,IAC7C;AAEA,IAAI,YAAY,SAAU,MAAM;AAC9B,aAAO,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO;AAAA,IACjD;AAEA,QAAI,WAAW,KAAK,CAAC,UAAU,YAAY,GAAG;AAC5C,aAAO,aAAa,OAAO,YAAY;AAAA,IACzC;AAEA,IAAI,iBAAiB,UAAU,UAAU;AACzC,IAAI,mBAAmB,UAAU,YAAY;AAC7C,IAAI,gBAAgB,UAAU,SAAS;AAqJvC,IAAI,eAA4B,WAAY;AAC1C,eAASC,cAAa,UAAU,YAAY;AAG1C,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,SAAS;AACd,aAAK,SAAS;AACd,YAAI,uBAAuB,IAAI,qBAAqB,IAAI;AAExD,YAAI;AACF,eAAK,WAAW,WAAW,KAAK,QAAW,oBAAoB;AAAA,QACjE,SAAS,GAAG;AACV,+BAAqB,MAAM,CAAC;AAAA,QAC9B;AAEA,YAAI,KAAK,WAAW,eAAgB,MAAK,SAAS;AAAA,MACpD;AAEA,UAAI,SAASA,cAAa;AAE1B,aAAO,cAAc,SAAS,cAAc;AAC1C,YAAI,KAAK,WAAW,UAAU;AAC5B,4BAAkB,IAAI;AACtB,8BAAoB,IAAI;AAAA,QAC1B;AAAA,MACF;AAEA,mBAAaA,eAAc,CAAC;AAAA,QAC1B,KAAK;AAAA,QACL,KAAK,WAAY;AACf,iBAAO,KAAK,WAAW;AAAA,QACzB;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE;AAEF,IAAI,uBAAoC,WAAY;AAClD,eAASC,sBAAqB,cAAc;AAC1C,aAAK,gBAAgB;AAAA,MACvB;AAEA,UAAI,UAAUA,sBAAqB;AAEnC,cAAQ,OAAO,SAAS,KAAK,OAAO;AAClC,iBAAS,KAAK,eAAe,QAAQ,KAAK;AAAA,MAC5C;AAEA,cAAQ,QAAQ,SAAS,MAAM,OAAO;AACpC,iBAAS,KAAK,eAAe,SAAS,KAAK;AAAA,MAC7C;AAEA,cAAQ,WAAW,SAAS,WAAW;AACrC,iBAAS,KAAK,eAAe,UAAU;AAAA,MACzC;AAEA,mBAAaA,uBAAsB,CAAC;AAAA,QAClC,KAAK;AAAA,QACL,KAAK,WAAY;AACf,iBAAO,KAAK,cAAc,WAAW;AAAA,QACvC;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE;AAEF,IAAI,aAA0B,WAAY;AACxC,eAASC,YAAW,YAAY;AAC9B,YAAI,EAAE,gBAAgBA,aAAa,OAAM,IAAI,UAAU,2CAA2C;AAClG,YAAI,OAAO,eAAe,WAAY,OAAM,IAAI,UAAU,2CAA2C;AACrG,aAAK,cAAc;AAAA,MACrB;AAEA,UAAI,UAAUA,YAAW;AAEzB,cAAQ,YAAY,SAAS,UAAU,UAAU;AAC/C,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,qBAAW;AAAA,YACT,MAAM;AAAA,YACN,OAAO,UAAU,CAAC;AAAA,YAClB,UAAU,UAAU,CAAC;AAAA,UACvB;AAAA,QACF;AAEA,eAAO,IAAI,aAAa,UAAU,KAAK,WAAW;AAAA,MACpD;AAEA,cAAQ,UAAU,SAASC,SAAQ,IAAI;AACrC,YAAI,QAAQ;AAEZ,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAI,OAAO,OAAO,YAAY;AAC5B,mBAAO,IAAI,UAAU,KAAK,oBAAoB,CAAC;AAC/C;AAAA,UACF;AAEA,mBAAS,OAAO;AACd,yBAAa,YAAY;AACzB,oBAAQ;AAAA,UACV;AAEA,cAAI,eAAe,MAAM,UAAU;AAAA,YACjC,MAAM,SAAU,OAAO;AACrB,kBAAI;AACF,mBAAG,OAAO,IAAI;AAAA,cAChB,SAAS,GAAG;AACV,uBAAO,CAAC;AACR,6BAAa,YAAY;AAAA,cAC3B;AAAA,YACF;AAAA,YACA,OAAO;AAAA,YACP,UAAU;AAAA,UACZ,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,cAAQ,MAAM,SAAS,IAAI,IAAI;AAC7B,YAAI,SAAS;AAEb,YAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,KAAK,oBAAoB;AAC3E,YAAI,IAAI,WAAW,IAAI;AACvB,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,iBAAO,OAAO,UAAU;AAAA,YACtB,MAAM,SAAU,OAAO;AACrB,kBAAI;AACF,wBAAQ,GAAG,KAAK;AAAA,cAClB,SAAS,GAAG;AACV,uBAAO,SAAS,MAAM,CAAC;AAAA,cACzB;AAEA,uBAAS,KAAK,KAAK;AAAA,YACrB;AAAA,YACA,OAAO,SAAU,GAAG;AAClB,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,YACA,UAAU,WAAY;AACpB,uBAAS,SAAS;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,cAAQ,SAAS,SAAS,OAAO,IAAI;AACnC,YAAI,SAAS;AAEb,YAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,KAAK,oBAAoB;AAC3E,YAAI,IAAI,WAAW,IAAI;AACvB,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,iBAAO,OAAO,UAAU;AAAA,YACtB,MAAM,SAAU,OAAO;AACrB,kBAAI;AACF,oBAAI,CAAC,GAAG,KAAK,EAAG;AAAA,cAClB,SAAS,GAAG;AACV,uBAAO,SAAS,MAAM,CAAC;AAAA,cACzB;AAEA,uBAAS,KAAK,KAAK;AAAA,YACrB;AAAA,YACA,OAAO,SAAU,GAAG;AAClB,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,YACA,UAAU,WAAY;AACpB,uBAAS,SAAS;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,cAAQ,SAAS,SAAS,OAAO,IAAI;AACnC,YAAI,SAAS;AAEb,YAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,KAAK,oBAAoB;AAC3E,YAAI,IAAI,WAAW,IAAI;AACvB,YAAI,UAAU,UAAU,SAAS;AACjC,YAAI,WAAW;AACf,YAAI,OAAO,UAAU,CAAC;AACtB,YAAI,MAAM;AACV,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,iBAAO,OAAO,UAAU;AAAA,YACtB,MAAM,SAAU,OAAO;AACrB,kBAAI,QAAQ,CAAC;AACb,yBAAW;AAEX,kBAAI,CAAC,SAAS,SAAS;AACrB,oBAAI;AACF,wBAAM,GAAG,KAAK,KAAK;AAAA,gBACrB,SAAS,GAAG;AACV,yBAAO,SAAS,MAAM,CAAC;AAAA,gBACzB;AAAA,cACF,OAAO;AACL,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,YACA,OAAO,SAAU,GAAG;AAClB,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,YACA,UAAU,WAAY;AACpB,kBAAI,CAAC,YAAY,CAAC,QAAS,QAAO,SAAS,MAAM,IAAI,UAAU,iCAAiC,CAAC;AACjG,uBAAS,KAAK,GAAG;AACjB,uBAAS,SAAS;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,cAAQ,SAAS,SAAS,SAAS;AACjC,YAAI,SAAS;AAEb,iBAAS,OAAO,UAAU,QAAQ,UAAU,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1F,kBAAQ,IAAI,IAAI,UAAU,IAAI;AAAA,QAChC;AAEA,YAAI,IAAI,WAAW,IAAI;AACvB,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,cAAI;AACJ,cAAI,QAAQ;AAEZ,mBAAS,UAAU,MAAM;AACvB,2BAAe,KAAK,UAAU;AAAA,cAC5B,MAAM,SAAU,GAAG;AACjB,yBAAS,KAAK,CAAC;AAAA,cACjB;AAAA,cACA,OAAO,SAAU,GAAG;AAClB,yBAAS,MAAM,CAAC;AAAA,cAClB;AAAA,cACA,UAAU,WAAY;AACpB,oBAAI,UAAU,QAAQ,QAAQ;AAC5B,iCAAe;AACf,2BAAS,SAAS;AAAA,gBACpB,OAAO;AACL,4BAAU,EAAE,KAAK,QAAQ,OAAO,CAAC,CAAC;AAAA,gBACpC;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAEA,oBAAU,MAAM;AAChB,iBAAO,WAAY;AACjB,gBAAI,cAAc;AAChB,2BAAa,YAAY;AACzB,6BAAe;AAAA,YACjB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,cAAQ,UAAU,SAAS,QAAQ,IAAI;AACrC,YAAI,SAAS;AAEb,YAAI,OAAO,OAAO,WAAY,OAAM,IAAI,UAAU,KAAK,oBAAoB;AAC3E,YAAI,IAAI,WAAW,IAAI;AACvB,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,cAAI,gBAAgB,CAAC;AAErB,cAAI,QAAQ,OAAO,UAAU;AAAA,YAC3B,MAAM,SAAU,OAAO;AACrB,kBAAI,IAAI;AACN,oBAAI;AACF,0BAAQ,GAAG,KAAK;AAAA,gBAClB,SAAS,GAAG;AACV,yBAAO,SAAS,MAAM,CAAC;AAAA,gBACzB;AAAA,cACF;AAEA,kBAAI,QAAQ,EAAE,KAAK,KAAK,EAAE,UAAU;AAAA,gBAClC,MAAM,SAAUC,QAAO;AACrB,2BAAS,KAAKA,MAAK;AAAA,gBACrB;AAAA,gBACA,OAAO,SAAU,GAAG;AAClB,2BAAS,MAAM,CAAC;AAAA,gBAClB;AAAA,gBACA,UAAU,WAAY;AACpB,sBAAI,IAAI,cAAc,QAAQ,KAAK;AACnC,sBAAI,KAAK,EAAG,eAAc,OAAO,GAAG,CAAC;AACrC,iCAAe;AAAA,gBACjB;AAAA,cACF,CAAC;AACD,4BAAc,KAAK,KAAK;AAAA,YAC1B;AAAA,YACA,OAAO,SAAU,GAAG;AAClB,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,YACA,UAAU,WAAY;AACpB,6BAAe;AAAA,YACjB;AAAA,UACF,CAAC;AAED,mBAAS,iBAAiB;AACxB,gBAAI,MAAM,UAAU,cAAc,WAAW,EAAG,UAAS,SAAS;AAAA,UACpE;AAEA,iBAAO,WAAY;AACjB,0BAAc,QAAQ,SAAU,GAAG;AACjC,qBAAO,EAAE,YAAY;AAAA,YACvB,CAAC;AACD,kBAAM,YAAY;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,cAAQ,gBAAgB,IAAI,WAAY;AACtC,eAAO;AAAA,MACT;AAEA,MAAAF,YAAW,OAAO,SAAS,KAAK,GAAG;AACjC,YAAI,IAAI,OAAO,SAAS,aAAa,OAAOA;AAC5C,YAAI,KAAK,KAAM,OAAM,IAAI,UAAU,IAAI,mBAAmB;AAC1D,YAAI,SAAS,UAAU,GAAG,gBAAgB;AAE1C,YAAI,QAAQ;AACV,cAAI,aAAa,OAAO,KAAK,CAAC;AAC9B,cAAI,OAAO,UAAU,MAAM,WAAY,OAAM,IAAI,UAAU,aAAa,mBAAmB;AAC3F,cAAI,aAAa,UAAU,KAAK,WAAW,gBAAgB,EAAG,QAAO;AACrE,iBAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,mBAAO,WAAW,UAAU,QAAQ;AAAA,UACtC,CAAC;AAAA,QACH;AAEA,YAAI,UAAU,UAAU,GAAG;AACzB,mBAAS,UAAU,GAAG,cAAc;AAEpC,cAAI,QAAQ;AACV,mBAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,sBAAQ,WAAY;AAClB,oBAAI,SAAS,OAAQ;AAErB,yBAAS,YAAY,gCAAgC,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO,EAAE,QAAQ,UAAU,GAAG,QAAO;AACzG,sBAAI,OAAO,MAAM;AACjB,2BAAS,KAAK,IAAI;AAClB,sBAAI,SAAS,OAAQ;AAAA,gBACvB;AAEA,yBAAS,SAAS;AAAA,cACpB,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,iBAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,oBAAQ,WAAY;AAClB,kBAAI,SAAS,OAAQ;AAErB,uBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,yBAAS,KAAK,EAAE,CAAC,CAAC;AAClB,oBAAI,SAAS,OAAQ;AAAA,cACvB;AAEA,uBAAS,SAAS;AAAA,YACpB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAEA,cAAM,IAAI,UAAU,IAAI,oBAAoB;AAAA,MAC9C;AAEA,MAAAA,YAAW,KAAK,SAAS,KAAK;AAC5B,iBAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,gBAAM,KAAK,IAAI,UAAU,KAAK;AAAA,QAChC;AAEA,YAAI,IAAI,OAAO,SAAS,aAAa,OAAOA;AAC5C,eAAO,IAAI,EAAE,SAAU,UAAU;AAC/B,kBAAQ,WAAY;AAClB,gBAAI,SAAS,OAAQ;AAErB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,uBAAS,KAAK,MAAM,CAAC,CAAC;AACtB,kBAAI,SAAS,OAAQ;AAAA,YACvB;AAEA,qBAAS,SAAS;AAAA,UACpB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,mBAAaA,aAAY,MAAM,CAAC;AAAA,QAC9B,KAAK;AAAA,QACL,KAAK,WAAY;AACf,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE;AAEF,QAAI,WAAW,GAAG;AAChB,aAAO,eAAe,YAAY,OAAO,YAAY,GAAG;AAAA,QACtD,OAAO;AAAA,UACL,QAAQ;AAAA,UACR;AAAA,QACF;AAAA,QACA,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AAAA;AAAA;;;AC7jBe,SAAR,yBAA0CG,OAAM;AACtD,MAAIC;AACJ,MAAIC,UAASF,MAAK;AAElB,MAAI,OAAOE,YAAW,YAAY;AACjC,QAAIA,QAAO,YAAY;AACtB,MAAAD,UAASC,QAAO;AAAA,IACjB,OAAO;AAEN,UAAI,OAAOA,QAAO,QAAQ,YAAY;AAIrC,QAAAD,UAASC,QAAO,IAAI,8CAA8C;AAAA,MACnE,OAAO;AAIN,QAAAD,UAASC,QAAO,8CAA8C;AAAA,MAC/D;AACA,UAAI;AACH,QAAAA,QAAO,aAAaD;AAAA,MACrB,SAAS,KAAK;AAAA,MAId;AAAA,IACD;AAAA,EACD,OAAO;AACN,IAAAA,UAAS;AAAA,EACV;AAEA,SAAOA;AACR;AAjCA;AAAA;AAAA;AAAA;;;ACAA,IAGI,MAcA;AAjBJ;AAAA;AACA;AAIA,QAAI,OAAO,SAAS,aAAa;AAC/B,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,aAAa;AACxC,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,aAAa;AACxC,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,aAAa;AACxC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS,aAAa,EAAE;AAAA,IACjC;AAEA,IAAI,SAAS,yBAAS,IAAI;AAAA;AAAA;;;ACZ1B,IAWQ,WACF;AAZN;;;AAIA;AAOQ,IAAA,YAAc,WAAU;AAChC,IAAM,gBAAgB;AACtB,QAAI,CAAC,UAAU,aAAa,GAAG;AAE7B,gBAAU,aAAa,IAAI,WAAA;AACzB,eAAO;MACT;IACF;;;;;ACJM,SAAU,uBAA+B,OAAa;AAC1D,MAAM,UAAU,QAAQ,QAAQ,KAAK;AAErC,UAAQ,SAAS;AACjB,UAAQ,QAAQ;AAEhB,SAAO;AACT;AAEM,SAAU,sBAAwC,QAAe;AACrE,MAAM,UAAU,QAAQ,OAAO,MAAM;AAGrC,UAAQ,MAAM,WAAA;EAAO,CAAC;AAEtB,UAAQ,SAAS;AACjB,UAAQ,SAAS;AAEjB,SAAO;AACT;AAEM,SAAU,kBACd,SAAwB;AAExB,SAAO,YAAY;AACrB;AAEM,SAAU,qBACd,SAAwB;AAExB,MAAI,kBAAkB,OAAO,GAAG;AAC9B,WAAO;EACT;AAEA,MAAM,iBAAiB;AACvB,iBAAe,SAAS;AAExB,iBAAe,KACb,SAAC,OAAK;AACJ,QAAI,eAAe,WAAW,WAAW;AACvC,UAAM,mBACJ;AAEF,uBAAiB,SAAS;AAC1B,uBAAiB,QAAQ;IAC3B;EACF,GACA,SAAC,QAAe;AACd,QAAI,eAAe,WAAW,WAAW;AACvC,UAAM,kBACJ;AAEF,sBAAgB,SAAS;AACzB,sBAAgB,SAAS;IAC3B;EACF,CAAC;AAGH,SAAO;AACT;AA3DA;;;;;;ACdM,SAAU,UAAa,OAAQ;AACnC,SAAO,gBAAgB,KAAK;AAC9B;AAEA,SAAS,gBAAmB,KAAQ,MAAoB;AACtD,UAAQ,SAAS,KAAK,GAAG,GAAG;IAC1B,KAAK,kBAAkB;AACrB,aAAO,QAAQ,oBAAI,IAAG;AACtB,UAAI,KAAK,IAAI,GAAG;AAAG,eAAO,KAAK,IAAI,GAAG;AACtC,UAAM,SAAmB,IAAY,MAAM,CAAC;AAC5C,WAAK,IAAI,KAAK,MAAI;AAClB,aAAK,QAAQ,SAAU,OAAO,GAAC;AAC7B,eAAK,CAAC,IAAI,gBAAgB,OAAO,IAAI;MACvC,CAAC;AACD,aAAO;IACT;IAEA,KAAK,mBAAmB;AACtB,aAAO,QAAQ,oBAAI,IAAG;AACtB,UAAI,KAAK,IAAI,GAAG;AAAG,eAAO,KAAK,IAAI,GAAG;AAGtC,UAAM,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,CAAC;AACrD,WAAK,IAAI,KAAK,MAAI;AAClB,aAAO,KAAK,GAA8B,EAAE,QAAQ,SAAC,KAAG;AACtD,eAAK,GAAG,IAAI,gBAAiB,IAAY,GAAG,GAAG,IAAI;MACrD,CAAC;AACD,aAAO;IACT;IAEA;AACE,aAAO;EACX;AACF;AAtCQ,IAAA;AAAA;;IAAA,WAAa,OAAO,UAAS;;;;;ACErC,SAAS,WAAW,OAAU;AAC5B,MAAM,UAAU,oBAAI,IAAI,CAAC,KAAK,CAAC;AAC/B,UAAQ,QAAQ,SAAC,KAAG;AAClB,QAAI,gBAAgB,GAAG,KAAK,cAAc,GAAG,MAAM,KAAK;AACtD,aAAO,oBAAoB,GAAG,EAAE,QAAQ,SAAC,MAAI;AAC3C,YAAI,gBAAgB,IAAI,IAAI,CAAC;AAAG,kBAAQ,IAAI,IAAI,IAAI,CAAC;MACvD,CAAC;IACH;EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,cAAgC,KAAM;AAC7C,MAAI,WAAW,YAAQ,SAAa,CAAA,OAAG,SAAA,GAAA,GAAA;AACrC,QAAI;AACF,aAAO,OAAO,GAAG;IACnB,SAAS,GAAG;AAIV,UAAI,aAAa;AAAW,eAAO;AACnC,YAAM;IACR;EACF;AACA,SAAO;AACT;AAEM,SAAU,gBAAmB,KAAM;AACvC,MAAI,WAAU,YAAA,OAAA;AACZ,eAAW,GAAG;EAChB;AACA,SAAO;AACT;AAlCA;;;;;;;ACEM,SAAU,uBACd,WACA,QACA,UAAY;AAKZ,MAAM,sBAAqC,CAAA;AAC3C,YAAU,QAAQ,SAAC,KAAG;AAAK,WAAA,IAAI,MAAM,KAAK,oBAAoB,KAAK,GAAG;EAA3C,CAA4C;AACvE,sBAAoB,QAAQ,SAAC,KAAG;AAAK,WAAC,IAAY,MAAM,EAAE,QAAQ;EAA7B,CAA8B;AACrE;AAXA;;;;;;ACGM,SAAU,SACd,YACA,OACA,SAA4C;AAE5C,SAAO,IAAI,WAAc,SAAC,UAAQ;AAChC,QAAI,eAAe;;;;MAIjB,MAAI,SAAC,UAAmB;AACtB,eAAO,IAAI,QAAQ,SAAC,SAAO;AAAK,iBAAA,QAAQ,SAAQ,CAAE;QAAlB,CAAmB;MACrD;;AAGF,aAAS,aACP,UACA,KAAqB;AAErB,aAAO,SAAC,KAAG;AACT,YAAI,UAAU;AACZ,cAAM,OAAO,WAAA;AAGX,mBAAA,SAAS;;cACkB;gBACzB,SAAS,GAAG;UAFd;AAIF,yBAAe,aAAa,KAAK,MAAM,IAAI,EAAE,KAC3C,SAACE,SAAM;AAAK,mBAAA,SAAS,KAAKA,OAAM;UAApB,GACZ,SAAC,OAAK;AAAK,mBAAA,SAAS,MAAM,KAAK;UAApB,CAAqB;QAEpC,OAAO;AACL,mBAAS,GAAG,EAAE,GAAG;QACnB;MACF;IACF;AAEA,QAAM,UAAuB;MAC3B,MAAM,aAAa,OAAO,MAAM;MAChC,OAAO,aAAa,SAAS,OAAO;MACpC,UAAQ,WAAA;AAGc,qBAAa,KAAK,WAAA;AAAM,iBAAA,SAAS,SAAQ;QAAjB,CAAmB;MACjE;;AAGF,QAAM,MAAM,WAAW,UAAU,OAAO;AACxC,WAAO,WAAA;AAAM,aAAA,IAAI,YAAW;IAAf;EACf,CAAC;AACH;AAvDA;;;;;;;ACUM,SAAU,sBAEd,UAAW;AACX,WAAS,IAAI,KAAoB;AAI/B,WAAO,eAAe,UAAU,KAAK,EAAE,OAAO,WAAU,CAAE;EAC5D;AACA,MAAI,gBAAgB,OAAO,SAAS;AAClC,QAAI,OAAO,OAAO;EACpB;AAIA,MAAI,WAAW;AACf,SAAO;AACT;AA5BA;;;AACA;;;;;ACUA,SAAS,cAAiB,OAAoB;AAC5C,SAAO,SAAS,OAAQ,MAAc,SAAS;AACjD;IAqCA;;;;AA7CA;AACA;AACA;AA2CA,IAAA;IAAA,SAAA,QAAA;AAAgC,gBAAAC,UAAA,MAAA;AAc9B,eAAAA,SAAY,SAA8D;AACxE,YAAA,QAAA,OAAK,KAAA,MAAC,SAAC,UAAQ;AACb,gBAAK,YAAY,QAAQ;AACzB,iBAAO,WAAA;AAAM,mBAAA,MAAK,eAAe,QAAQ;UAA5B;QACf,CAAC,KAAC;AAdI,cAAA,YAAY,oBAAI,IAAG;AAiGX,cAAA,UAAU,IAAI,QAAuB,SAAC,SAAS,QAAM;AACnE,gBAAK,UAAU;AACf,gBAAK,SAAS;QAChB,CAAC;AAQO,cAAA,WAAW;UACjB,MAAM,SAACC,SAAS;AACd,gBAAI,MAAK,QAAQ,MAAM;AACrB,oBAAK,SAAS,CAAC,QAAQA,OAAM;AAC7B,oBAAK,OAAO,QAAQA,OAAM;AAC1B,qCAAuB,MAAK,WAAW,QAAQA,OAAM;YACvD;UACF;UAEA,OAAO,SAAC,OAAU;AACR,gBAAA,MAAQ,MAAI;AACpB,gBAAI,QAAQ,MAAM;AAIhB,kBAAI;AAAK,2BAAW,WAAA;AAAM,yBAAA,IAAI,YAAW;gBAAf,CAAiB;AAC3C,oBAAK,MAAM;AACX,oBAAK,SAAS,CAAC,SAAS,KAAK;AAC7B,oBAAK,OAAO,KAAK;AACjB,oBAAK,OAAO,SAAS,KAAK;AAC1B,qCAAuB,MAAK,WAAW,SAAS,KAAK;YACvD;UACF;UAEA,UAAU,WAAA;AACF,gBAAA,KAAwB,OAAtB,MAAG,GAAA,KAAE,KAAA,GAAA,SAAAC,WAAO,OAAA,SAAG,CAAA,IAAE;AACzB,gBAAI,QAAQ,MAAM;AAMhB,kBAAM,QAAQA,SAAQ,MAAK;AAC3B,kBAAI,CAAC,OAAO;AACV,oBAAI;AAAK,6BAAW,WAAA;AAAM,2BAAA,IAAI,YAAW;kBAAf,CAAiB;AAC3C,sBAAK,MAAM;AACX,oBAAI,MAAK,UAAU,MAAK,OAAO,CAAC,MAAM,QAAQ;AAC5C,wBAAK,QAAQ,MAAK,OAAO,CAAC,CAAC;gBAC7B,OAAO;AACL,wBAAK,QAAO;gBACd;AACA,sBAAK,OAAO,UAAU;AAOtB,uCAAuB,MAAK,WAAW,UAAU;cACnD,WAAW,cAAc,KAAK,GAAG;AAC/B,sBAAM,KACJ,SAAC,KAAG;AAAK,yBAAC,MAAK,MAAM,IAAI,UAAU,MAAK,QAAQ;gBAAvC,GACT,MAAK,SAAS,KAAK;cAEvB,OAAO;AACL,sBAAK,MAAM,MAAM,UAAU,MAAK,QAAQ;cAC1C;YACF;UACF;;AAGM,cAAA,sBAAsB,oBAAI,IAAG;AAgC9B,cAAA,SAAS,SAAC,QAAW;AAC1B,gBAAK,OAAO,MAAM;AAClB,gBAAK,UAAU,CAAA;AACf,gBAAK,SAAS,MAAM,MAAM;QAC5B;AA1LE,cAAK,QAAQ,MAAM,SAAC,GAAC;QAAM,CAAC;AAK5B,YAAI,OAAO,YAAY,YAAY;AACjC,oBAAU,CAAC,IAAI,WAAW,OAAO,CAAC;QACpC;AAEA,YAAI,cAAc,OAAO,GAAG;AAC1B,kBAAQ,KAAK,SAAC,UAAQ;AAAK,mBAAA,MAAK,MAAM,QAAQ;UAAnB,GAAsB,MAAK,SAAS,KAAK;QACtE,OAAO;AACL,gBAAK,MAAM,OAAO;QACpB;;MACF;AASQ,MAAAF,SAAA,UAAA,QAAR,SAAc,SAAkC;AAC9C,YAAI,KAAK,QAAQ;AAAQ;AAKzB,aAAK,UAAU,MAAM,KAAK,OAAO;AAMjC,aAAK,SAAS,SAAQ;MACxB;AAEQ,MAAAA,SAAA,UAAA,qBAAR,SAA2B,UAAqB;AAC9C,YAAI,KAAK,QAAQ;AACf,cAAM,cAAc,KAAK,OAAO,CAAC;AACjC,cAAM,SAAS,SAAS,WAAW;AACnC,cAAI,QAAQ;AACV,mBAAO,KAAK,UAAU,KAAK,OAAO,CAAC,CAAC;UACtC;AAIA,cAAI,KAAK,QAAQ,QAAQ,gBAAgB,UAAU,SAAS,UAAU;AACpE,qBAAS,SAAQ;UACnB;QACF;MACF;AAEO,MAAAA,SAAA,UAAA,cAAP,SAAmB,UAAqB;AACtC,YAAI,CAAC,KAAK,UAAU,IAAI,QAAQ,GAAG;AAGjC,eAAK,mBAAmB,QAAQ;AAChC,eAAK,UAAU,IAAI,QAAQ;QAC7B;MACF;AAEO,MAAAA,SAAA,UAAA,iBAAP,SAAsB,UAAqB;AACzC,YAAI,KAAK,UAAU,OAAO,QAAQ,KAAK,KAAK,UAAU,OAAO,GAAG;AAK9D,eAAK,SAAS,SAAQ;QACxB;MACF;AAiFQ,MAAAA,SAAA,UAAA,SAAR,SACE,QACA,KAAuC;AAE/B,YAAA,sBAAwB,KAAI;AACpC,YAAI,oBAAoB,MAAM;AAG5B,eAAK,sBAAsB,oBAAI,IAAG;AAClC,8BAAoB,QAAQ,SAAC,UAAQ;AAAK,mBAAA,SAAS,QAAQ,GAAG;UAApB,CAAqB;QACjE;MACF;AAQA,MAAAA,SAAA,UAAA,aAAA,SAAW,UAA4B;AACrC,YAAI,SAAS;AACb,aAAK,oBAAoB,IAAI,SAAC,QAAQ,KAAG;AACvC,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,qBAAS,QAAQ,GAAG;UACtB;QACF,CAAC;MACH;AAQF,aAAAA;IAAA,EAlNgC,UAAU;AA2N1C,0BAAsB,OAAO;;;;;AClQvB,SAAU,kCACd,OAAqB;AAErB,SAAO,iBAAiB;AAC1B;AAEM,SAAU,8BACd,OAAqB;AAErB,SAAO,aAAa,SAAS,UAAU;AACzC;AAEM,SAAU,uBACd,OAAqB;AAErB,SACE,kCAAkC,KAAK,KACvC,8BAA8B,KAAK;AAEvC;AAKM,SAAU,sBACd,OAAc;AAEd,SAAO,gBAAgB,KAAK,KAAK,aAAa;AAChD;AAEM,SAAU,qBACd,YACAG,SAAmC;AAEnC,MAAI,aAAa;AACjB,MAAM,SAAS,IAAI,WAAU;AAC7B,MACE,kCAAkCA,OAAM,KACxC,gBAAgBA,QAAO,WAAW,GAClC;AACA,IAAAA,QAAO,YAAY,QAAQ,SAAC,IAAc;UAAZ,OAAI,GAAA,MAAE,OAAI,GAAA;AACtC,eAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,eAAe,CAAC,MAAM,CAAC,GAAG;AAChC,YAAM,WAAuC,eAAe,CAAA,IAAK,CAAA;AACjE,iBAAO,GAAG,IAAI;AACd,eAAO;MACT;AACA,mBAAa,OAAO,MAAM,YAAY,IAAI;IAC5C,CAAC;EACH;AACA,SAAO;AACT;AAxDA;;;AACA;AACA;;;;;ACLM,SAAU,sBAAyBC,SAAsB;AAC7D,MAAM,SAAS,2BAA2BA,OAAM;AAChD,SAAO,gBAAgB,MAAM;AAC/B;AAEM,SAAU,2BAA8BA,SAAsB;AAClE,MAAM,gBACJ,gBAAgBA,QAAO,MAAM,IAAIA,QAAO,OAAO,MAAM,CAAC,IAAI,CAAA;AAE5D,MACE,kCAAkCA,OAAM,KACxC,gBAAgBA,QAAO,WAAW,GAClC;AACA,IAAAA,QAAO,YAAY,QAAQ,SAAC,mBAAiB;AAC3C,UAAI,kBAAkB,QAAQ;AAC5B,sBAAc,KAAI,MAAlB,eAAsB,kBAAkB,MAAM;MAChD;IACF,CAAC;EACH;AACA,SAAO;AACT;AAvBA;;;AACA;;;;;ACIM,SAAU,UAAO;AACrB,MAAA,UAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAiB;AAAjB,YAAA,EAAA,IAAA,UAAA,EAAA;;AAEA,MAAMC,UAAS,uBAAO,OAAO,IAAI;AAEjC,UAAQ,QAAQ,SAAC,KAAG;AAClB,QAAI,CAAC;AAAK;AACV,WAAO,KAAK,GAAG,EAAE,QAAQ,SAAC,KAAG;AAC3B,UAAM,QAAS,IAAY,GAAG;AAC9B,UAAI,UAAU,QAAQ;AACpB,QAAAA,QAAO,GAAG,IAAI;MAChB;IACF,CAAC;EACH,CAAC;AAED,SAAOA;AACT;AApBA;;;;;;ACYM,SAAU,aAId,UACA,SAAqC;AAErC,SAAO,QACL,UACA,SACA,QAAQ,aAAa;IACnB,WAAW,QAAO,SAAA,SAAA,CAAA,GACZ,YAAY,SAAS,SAAU,GAChC,QAAQ,SAAS,CAAA;GAEvB;AAEL;;;;AAxBA;;;;;ACJM,SAAU,SAA8B,OAAU,KAAM;AAC5D,SAAO,WAAW,OAAO,GAAG;AAC9B;AAEA,SAAS,WACP,OACA,KACA,OAA2B;AAA3B,MAAA,UAAA,QAAA;AAAA,YAAA,oBAAY,IAAG;EAAY;AAE3B,MAAI,MAAM,IAAI,KAAK,GAAG;AACpB,WAAO,MAAM,IAAI,KAAK;EACxB;AAEA,MAAI,WAAW;AAEf,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,QAAM,UAAe,CAAA;AACrB,UAAM,IAAI,OAAO,OAAK;AAEtB,UAAM,QAAQ,SAACC,QAAO,OAAK;AACzB,UAAMC,UAAS,WAAWD,QAAO,KAAK,KAAK;AAC3C,mBAAA,WAAaC,YAAWD;AAExB,cAAM,KAAK,IAAIC;IACjB,CAAC;AAED,QAAI,UAAU;AACZ,aAAO;IACT;EACF,WAAW,cAAc,KAAK,GAAG;AAC/B,QAAM,QAAM,OAAO,OAAO,OAAO,eAAe,KAAK,CAAC;AACtD,UAAM,IAAI,OAAO,KAAG;AAEpB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAC,GAAC;AAC3B,UAAI,MAAM,KAAK;AACb,mBAAW;AACX;MACF;AAEA,UAAMA,UAAS,WAAW,MAAM,CAAC,GAAG,KAAK,KAAK;AAC9C,mBAAA,WAAaA,YAAW,MAAM,CAAC;AAE/B,YAAI,CAAC,IAAIA;IACX,CAAC;AAED,QAAI,UAAU;AACZ,aAAO;IACT;EACF;AAEA,SAAO;AACT;AArDA;;;;;;;ACCM,SAAU,cAAiB,OAAQ;AACvC,SAAO,SAAS,OAAO,YAAY;AACrC;AAJA;;;;;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;;;AAMA;AAWA;AAGA;AAMA;AAWA;AAUA;AAyBA;AAUA;AAMA;AAUA;AAGA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAKA;;;", "names": ["_a", "root", "result", "defaultDispose", "init_lib", "init_lib", "key", "canonicalStringify", "result", "isObjRef", "defaultMakeData", "for<PERSON>ach", "slice", "hasOwnProperty", "<PERSON><PERSON>", "init_lib", "maybe", "Slot", "init_lib", "init_lib", "hasOwnProperty", "dep", "dep", "hasOwnProperty", "<PERSON><PERSON>", "cache", "init_lib", "init_lib", "DocumentTransform", "print", "result", "hasOwnProperty", "DeepMerger", "result", "isReference", "Subscription", "SubscriptionObserver", "Observable", "for<PERSON>ach", "value", "root", "result", "Symbol", "result", "Concast", "result", "sources", "result", "result", "result", "value", "result"]}
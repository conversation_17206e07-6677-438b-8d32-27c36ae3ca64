{"version": 3, "sources": ["../../../../date-fns/esm/locale/uk/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/uk/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/uk/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/uk/_lib/localize/index.js", "../../../../date-fns/esm/locale/uk/_lib/match/index.js", "../../../../date-fns/esm/locale/uk/index.js"], "sourcesContent": ["function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  var rem10 = count % 10;\n  var rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'за ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' тому';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nvar halfAtMinute = function halfAtMinute(_, options) {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за півхвилини';\n    } else {\n      return 'півхвилини тому';\n    }\n  }\n  return 'півхвилини';\n};\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'менше секунди',\n      singularNominative: 'менше {{count}} секунди',\n      singularGenitive: 'менше {{count}} секунд',\n      pluralGenitive: 'менше {{count}} секунд'\n    },\n    future: {\n      one: 'менше, ніж за секунду',\n      singularNominative: 'менше, ніж за {{count}} секунду',\n      singularGenitive: 'менше, ніж за {{count}} секунди',\n      pluralGenitive: 'менше, ніж за {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунди',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду тому',\n      singularGenitive: '{{count}} секунди тому',\n      pluralGenitive: '{{count}} секунд тому'\n    },\n    future: {\n      singularNominative: 'за {{count}} секунду',\n      singularGenitive: 'за {{count}} секунди',\n      pluralGenitive: 'за {{count}} секунд'\n    }\n  }),\n  halfAMinute: halfAtMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'менше хвилини',\n      singularNominative: 'менше {{count}} хвилини',\n      singularGenitive: 'менше {{count}} хвилин',\n      pluralGenitive: 'менше {{count}} хвилин'\n    },\n    future: {\n      one: 'менше, ніж за хвилину',\n      singularNominative: 'менше, ніж за {{count}} хвилину',\n      singularGenitive: 'менше, ніж за {{count}} хвилини',\n      pluralGenitive: 'менше, ніж за {{count}} хвилин'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} хвилина',\n      singularGenitive: '{{count}} хвилини',\n      pluralGenitive: '{{count}} хвилин'\n    },\n    past: {\n      singularNominative: '{{count}} хвилину тому',\n      singularGenitive: '{{count}} хвилини тому',\n      pluralGenitive: '{{count}} хвилин тому'\n    },\n    future: {\n      singularNominative: 'за {{count}} хвилину',\n      singularGenitive: 'за {{count}} хвилини',\n      pluralGenitive: 'за {{count}} хвилин'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} години',\n      singularGenitive: 'близько {{count}} годин',\n      pluralGenitive: 'близько {{count}} годин'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} годину',\n      singularGenitive: 'приблизно за {{count}} години',\n      pluralGenitive: 'приблизно за {{count}} годин'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} годину',\n      singularGenitive: '{{count}} години',\n      pluralGenitive: '{{count}} годин'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} днi',\n      pluralGenitive: '{{count}} днів'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} тижня',\n      singularGenitive: 'близько {{count}} тижнів',\n      pluralGenitive: 'близько {{count}} тижнів'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} тиждень',\n      singularGenitive: 'приблизно за {{count}} тижні',\n      pluralGenitive: 'приблизно за {{count}} тижнів'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} тиждень',\n      singularGenitive: '{{count}} тижні',\n      pluralGenitive: '{{count}} тижнів'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} місяця',\n      singularGenitive: 'близько {{count}} місяців',\n      pluralGenitive: 'близько {{count}} місяців'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} місяць',\n      singularGenitive: 'приблизно за {{count}} місяці',\n      pluralGenitive: 'приблизно за {{count}} місяців'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} місяць',\n      singularGenitive: '{{count}} місяці',\n      pluralGenitive: '{{count}} місяців'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} року',\n      singularGenitive: 'близько {{count}} років',\n      pluralGenitive: 'близько {{count}} років'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} рік',\n      singularGenitive: 'приблизно за {{count}} роки',\n      pluralGenitive: 'приблизно за {{count}} років'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} рік',\n      singularGenitive: '{{count}} роки',\n      pluralGenitive: '{{count}} років'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'більше {{count}} року',\n      singularGenitive: 'більше {{count}} років',\n      pluralGenitive: 'більше {{count}} років'\n    },\n    future: {\n      singularNominative: 'більше, ніж за {{count}} рік',\n      singularGenitive: 'більше, ніж за {{count}} роки',\n      pluralGenitive: 'більше, ніж за {{count}} років'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'майже {{count}} рік',\n      singularGenitive: 'майже {{count}} роки',\n      pluralGenitive: 'майже {{count}} років'\n    },\n    future: {\n      singularNominative: 'майже за {{count}} рік',\n      singularGenitive: 'майже за {{count}} роки',\n      pluralGenitive: 'майже за {{count}} років'\n    }\n  })\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'р.'\",\n  long: \"do MMMM y 'р.'\",\n  medium: \"d MMM y 'р.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'о' {{time}}\",\n  long: \"{{date}} 'о' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "import { toDate } from \"../../../../index.js\";\nimport isSameUTCWeek from \"../../../../_lib/isSameUTCWeek/index.js\";\nvar accusativeWeekdays = ['неділю', 'понеділок', 'вівторок', 'середу', 'четвер', 'п’ятницю', 'суботу'];\nfunction lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\nvar lastWeekFormat = function lastWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nvar nextWeekFormat = function nextWeekFormat(dirtyDate, baseDate, options) {\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['до н.е.', 'н.е.'],\n  abbreviated: ['до н. е.', 'н. е.'],\n  wide: ['до нашої ери', 'нашої ери']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  // ДСТУ 3582:2013\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січень', 'лютий', 'березень', 'квітень', 'травень', 'червень', 'липень', 'серпень', 'вересень', 'жовтень', 'листопад', 'грудень']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вів', 'сер', 'чтв', 'птн', 'суб'],\n  wide: ['неділя', 'понеділок', 'вівторок', 'середа', 'четвер', 'п’ятниця', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'вечір',\n    night: 'ніч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n  if (unit === 'date') {\n    if (number === 3 || number === 23) {\n      suffix = '-є';\n    } else {\n      suffix = '-е';\n    }\n  } else if (unit === 'minute' || unit === 'second' || unit === 'hour') {\n    suffix = '-а';\n  } else {\n    suffix = '-й';\n  }\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n  wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nvar parseEraPatterns = {\n  any: [/^д/i, /^н/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n  wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[слбктчвжг]/i,\n  abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n  wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^с/i, /^л/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^л/i, /^с/i, /^в/i, /^ж/i, /^л/i, /^г/i],\n  any: [/^сі/i, /^лю/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^лип/i, /^се/i, /^в/i, /^ж/i, /^лис/i, /^г/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n  abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n  wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н/i, /^п[он]/i, /^в/i, /^с[ер]/i, /^ч/i, /^п\\W*?[ят]/i, /^с[уб]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^півн/i,\n    noon: /^пол/i,\n    morning: /^р/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> [@korzhyk]{@link https://github.com/korzhyk}\n * <AUTHOR> [@shcherbyakdev]{@link https://github.com/shcherbyakdev}\n */\nvar locale = {\n  code: 'uk',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,WAAW,QAAQ,OAAO;AAEjC,MAAI,OAAO,QAAQ,UAAa,UAAU,GAAG;AAC3C,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAGrB,MAAI,UAAU,KAAK,WAAW,IAAI;AAChC,WAAO,OAAO,mBAAmB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGrE,WAAW,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK;AACnE,WAAO,OAAO,iBAAiB,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAGnE,OAAO;AACL,WAAO,OAAO,eAAe,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACjE;AACF;AACA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,SAAU,OAAO,SAAS;AAC/B,QAAI,WAAW,QAAQ,WAAW;AAChC,UAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,OAAO,QAAQ,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,QAAQ,WAAW,OAAO,SAAS,KAAK;AAAA,QACjD;AAAA,MACF,OAAO;AACL,YAAI,OAAO,MAAM;AACf,iBAAO,WAAW,OAAO,MAAM,KAAK;AAAA,QACtC,OAAO;AACL,iBAAO,WAAW,OAAO,SAAS,KAAK,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,WAAW,OAAO,SAAS,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AACA,IAAI,eAAe,SAASA,cAAa,GAAG,SAAS;AACnD,MAAI,WAAW,QAAQ,WAAW;AAChC,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa;AAAA,EACb,kBAAkB,qBAAqB;AAAA,IACrC,SAAS;AAAA,MACP,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,UAAU,qBAAqB;AAAA,IAC7B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACJ,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,qBAAqB;AAAA,IAC1B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,SAAS,qBAAqB;AAAA,IAC5B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,aAAa,qBAAqB;AAAA,IAChC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,QAAQ,qBAAqB;AAAA,IAC3B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,YAAY,qBAAqB;AAAA,IAC/B,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAAA,EACD,cAAc,qBAAqB;AAAA,IACjC,SAAS;AAAA,MACP,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACN,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,OAAO,SAAS;AAClE,YAAU,WAAW,CAAC;AACtB,SAAO,qBAAqB,KAAK,EAAE,OAAO,OAAO;AACnD;AACA,IAAO,yBAAQ;;;ACnOf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;AC/Bf,IAAI,qBAAqB,CAAC,UAAU,aAAa,YAAY,UAAU,UAAU,YAAY,QAAQ;AACrG,SAAS,SAAS,KAAK;AACrB,MAAI,UAAU,mBAAmB,GAAG;AACpC,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,eAAe,UAAU;AAAA,IAClC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,UAAU;AAAA,EACrC;AACF;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,UAAU,mBAAmB,GAAG;AACpC,SAAO,QAAQ,UAAU;AAC3B;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,UAAU,mBAAmB,GAAG;AACpC,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,iBAAiB,UAAU;AAAA,IACpC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,kBAAkB,UAAU;AAAA,EACvC;AACF;AACA,IAAI,iBAAiB,SAASC,gBAAe,WAAW,UAAU,SAAS;AACzE,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,WAAO,SAAS,GAAG;AAAA,EACrB,OAAO;AACL,WAAO,SAAS,GAAG;AAAA,EACrB;AACF;AACA,IAAI,iBAAiB,SAASC,gBAAe,WAAW,UAAU,SAAS;AACzE,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,WAAO,SAAS,GAAG;AAAA,EACrB,OAAO;AACL,WAAO,SAAS,GAAG;AAAA,EACrB;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,MAAM,UAAU,SAAS;AAC3E,MAAI,SAAS,qBAAqB,KAAK;AACvC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;ACnEf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,WAAW,MAAM;AAAA,EAC1B,aAAa,CAAC,YAAY,OAAO;AAAA,EACjC,MAAM,CAAC,gBAAgB,WAAW;AACpC;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,WAAW,WAAW,WAAW,SAAS;AAAA,EACxD,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AACA,IAAI,cAAc;AAAA;AAAA,EAEhB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,QAAQ,QAAQ,UAAU,SAAS,SAAS,SAAS,QAAQ,SAAS,UAAU,SAAS,WAAW,OAAO;AAAA,EACzH,MAAM,CAAC,UAAU,SAAS,YAAY,WAAW,WAAW,WAAW,UAAU,WAAW,YAAY,WAAW,YAAY,SAAS;AAC1I;AACA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,QAAQ,QAAQ,UAAU,SAAS,SAAS,SAAS,QAAQ,SAAS,UAAU,SAAS,WAAW,OAAO;AAAA,EACzH,MAAM,CAAC,SAAS,UAAU,WAAW,UAAU,UAAU,UAAU,SAAS,UAAU,WAAW,UAAU,aAAa,QAAQ;AAClI;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,UAAU,aAAa,YAAY,UAAU,UAAU,YAAY,QAAQ;AACpF;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa,SAAS;AAC/D,MAAI,OAAO,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AAChF,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,QAAI,WAAW,KAAK,WAAW,IAAI;AACjC,eAAS;AAAA,IACX,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF,WAAW,SAAS,YAAY,SAAS,YAAY,SAAS,QAAQ;AACpE,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,SAAS;AAClB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAO,mBAAQ;;;ACzIf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,SAAS,QAAQ,OAAO,OAAO,SAAS,KAAK;AACjG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,OAAO,WAAW,OAAO,WAAW,OAAO,eAAe,SAAS;AAC3E;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;ACpFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["halfAtMinute", "formatDistance", "lastWeekFormat", "nextWeekFormat", "formatRelative", "ordinalNumber", "valueCallback"]}
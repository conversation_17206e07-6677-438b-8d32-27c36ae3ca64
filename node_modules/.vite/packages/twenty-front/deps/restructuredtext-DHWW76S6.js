import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js
var conf, language;
var init_restructuredtext = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js"() {
    conf = {
      brackets: [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
      ],
      autoClosingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: "<", close: ">", notIn: ["string"] }
      ],
      surroundingPairs: [
        { open: "(", close: ")" },
        { open: "[", close: "]" },
        { open: "`", close: "`" }
      ],
      folding: {
        markers: {
          start: new RegExp("^\\s*<!--\\s*#?region\\b.*-->"),
          end: new RegExp("^\\s*<!--\\s*#?endregion\\b.*-->")
        }
      }
    };
    language = {
      defaultToken: "",
      tokenPostfix: ".rst",
      control: /[\\`*_\[\]{}()#+\-\.!]/,
      escapes: /\\(?:@control)/,
      empty: [
        "area",
        "base",
        "basefont",
        "br",
        "col",
        "frame",
        "hr",
        "img",
        "input",
        "isindex",
        "link",
        "meta",
        "param"
      ],
      alphanumerics: /[A-Za-z0-9]/,
      simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,
      simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,
      phrase: /@simpleRefNameWithoutBq(?:\s@simpleRefNameWithoutBq)*/,
      citationName: /[A-Za-z][A-Za-z0-9-_.]*/,
      blockLiteralStart: /(?:[!"#$%&'()*+,-./:;<=>?@\[\]^_`{|}~]|[\s])/,
      precedingChars: /(?:[ -:/'"<([{])/,
      followingChars: /(?:[ -.,:;!?/'")\]}>]|$)/,
      punctuation: /(=|-|~|`|#|"|\^|\+|\*|:|\.|'|_|\+)/,
      tokenizer: {
        root: [
          //sections
          [/^(@punctuation{3,}$){1,1}?/, "keyword"],
          //line-blocks
          //No rules on it
          //bullet-lists
          [/^\s*([\*\-+‣•]|[a-zA-Z0-9]+\.|\([a-zA-Z0-9]+\)|[a-zA-Z0-9]+\))\s/, "keyword"],
          //literal-blocks
          [/([ ]::)\s*$/, "keyword", "@blankLineOfLiteralBlocks"],
          [/(::)\s*$/, "keyword", "@blankLineOfLiteralBlocks"],
          { include: "@tables" },
          { include: "@explicitMarkupBlocks" },
          { include: "@inlineMarkup" }
        ],
        explicitMarkupBlocks: [
          //citations
          { include: "@citations" },
          //footnotes
          { include: "@footnotes" },
          //directives
          [
            /^(\.\.\s)(@simpleRefName)(::\s)(.*)$/,
            [{ token: "", next: "subsequentLines" }, "keyword", "", ""]
          ],
          //hyperlink-targets
          [
            /^(\.\.)(\s+)(_)(@simpleRefName)(:)(\s+)(.*)/,
            [{ token: "", next: "hyperlinks" }, "", "", "string.link", "", "", "string.link"]
          ],
          //anonymous-hyperlinks
          [
            /^((?:(?:\.\.)(?:\s+))?)(__)(:)(\s+)(.*)/,
            [{ token: "", next: "subsequentLines" }, "", "", "", "string.link"]
          ],
          [/^(__\s+)(.+)/, ["", "string.link"]],
          //substitution-definitions
          [
            /^(\.\.)( \|)([^| ]+[^|]*[^| ]*)(\| )(@simpleRefName)(:: .*)/,
            [{ token: "", next: "subsequentLines" }, "", "string.link", "", "keyword", ""],
            "@rawBlocks"
          ],
          [/(\|)([^| ]+[^|]*[^| ]*)(\|_{0,2})/, ["", "string.link", ""]],
          //comments
          [/^(\.\.)([ ].*)$/, [{ token: "", next: "@comments" }, "comment"]]
        ],
        inlineMarkup: [
          { include: "@citationsReference" },
          { include: "@footnotesReference" },
          //hyperlink-references
          [/(@simpleRefName)(_{1,2})/, ["string.link", ""]],
          //embedded-uris-and-aliases
          [/(`)([^<`]+\s+)(<)(.*)(>)(`)(_)/, ["", "string.link", "", "string.link", "", "", ""]],
          //emphasis
          [/\*\*([^\\*]|\*(?!\*))+\*\*/, "strong"],
          [/\*[^*]+\*/, "emphasis"],
          //inline-literals
          [/(``)((?:[^`]|\`(?!`))+)(``)/, ["", "keyword", ""]],
          [/(__\s+)(.+)/, ["", "keyword"]],
          //interpreted-text
          [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, ["", "keyword", "", "", ""]],
          [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, ["", "", "", "keyword", ""]],
          [/(`)([^`]+)(`)/, ""],
          //inline-internal-targets
          [/(_`)(@phrase)(`)/, ["", "string.link", ""]]
        ],
        citations: [
          [
            /^(\.\.\s+\[)((?:@citationName))(\]\s+)(.*)/,
            [{ token: "", next: "@subsequentLines" }, "string.link", "", ""]
          ]
        ],
        citationsReference: [[/(\[)(@citationName)(\]_)/, ["", "string.link", ""]]],
        footnotes: [
          [
            /^(\.\.\s+\[)((?:[0-9]+))(\]\s+.*)/,
            [{ token: "", next: "@subsequentLines" }, "string.link", ""]
          ],
          [
            /^(\.\.\s+\[)((?:#@simpleRefName?))(\]\s+)(.*)/,
            [{ token: "", next: "@subsequentLines" }, "string.link", "", ""]
          ],
          [
            /^(\.\.\s+\[)((?:\*))(\]\s+)(.*)/,
            [{ token: "", next: "@subsequentLines" }, "string.link", "", ""]
          ]
        ],
        footnotesReference: [
          [/(\[)([0-9]+)(\])(_)/, ["", "string.link", "", ""]],
          [/(\[)(#@simpleRefName?)(\])(_)/, ["", "string.link", "", ""]],
          [/(\[)(\*)(\])(_)/, ["", "string.link", "", ""]]
        ],
        blankLineOfLiteralBlocks: [
          [/^$/, "", "@subsequentLinesOfLiteralBlocks"],
          [/^.*$/, "", "@pop"]
        ],
        subsequentLinesOfLiteralBlocks: [
          [/(@blockLiteralStart+)(.*)/, ["keyword", ""]],
          [/^(?!blockLiteralStart)/, "", "@popall"]
        ],
        subsequentLines: [
          [/^[\s]+.*/, ""],
          [/^(?!\s)/, "", "@pop"]
        ],
        hyperlinks: [
          [/^[\s]+.*/, "string.link"],
          [/^(?!\s)/, "", "@pop"]
        ],
        comments: [
          [/^[\s]+.*/, "comment"],
          [/^(?!\s)/, "", "@pop"]
        ],
        tables: [
          [/\+-[+-]+/, "keyword"],
          [/\+=[+=]+/, "keyword"]
        ]
      }
    };
  }
});
init_restructuredtext();
export {
  conf,
  language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=restructuredtext-DHWW76S6.js.map

{"version": 3, "sources": ["../../../../@graphiql/react/dist/searchcursor.es.js"], "sourcesContent": ["import { g as getDefaultExportFromCjs } from \"./codemirror.es2.js\";\nimport { r as requireSearchcursor } from \"./searchcursor.es2.js\";\nfunction _mergeNamespaces(n, m) {\n  for (var i = 0; i < m.length; i++) {\n    const e = m[i];\n    if (typeof e !== \"string\" && !Array.isArray(e)) {\n      for (const k in e) {\n        if (k !== \"default\" && !(k in n)) {\n          const d = Object.getOwnPropertyDescriptor(e, k);\n          if (d) {\n            Object.defineProperty(n, k, d.get ? d : {\n              enumerable: true,\n              get: () => e[k]\n            });\n          }\n        }\n      }\n    }\n  }\n  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: \"Module\" }));\n}\nvar searchcursorExports = requireSearchcursor();\nconst searchcursor = /* @__PURE__ */ getDefaultExportFromCjs(searchcursorExports);\nconst searchcursor$1 = /* @__PURE__ */ _mergeNamespaces({\n  __proto__: null,\n  default: searchcursor\n}, [searchcursorExports]);\nexport {\n  searchcursor$1 as s\n};\n//# sourceMappingURL=searchcursor.es.js.map\n"], "mappings": ";;;;;;;;;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC9C,iBAAW,KAAK,GAAG;AACjB,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,cAAI,GAAG;AACL,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACtC,YAAY;AAAA,cACZ,KAAK,MAAM,EAAE,CAAC;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,IAAI,sBAAsB,oBAAoB;AAC9C,IAAM,eAA+B,wBAAwB,mBAAmB;AAChF,IAAM,iBAAiC,iBAAiB;AAAA,EACtD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,mBAAmB,CAAC;", "names": []}
{"version": 3, "sources": ["../../../../react-phone-number-input/source/PhoneInputWithCountry.js", "../../../../react-phone-number-input/source/InputSmart.js", "../../../../input-format/source/react/Input.js", "../../../../input-format/source/edit.js", "../../../../input-format/source/parse.js", "../../../../input-format/source/helpers.js", "../../../../input-format/source/closeBraces.js", "../../../../input-format/source/templateFormatter.js", "../../../../input-format/source/format.js", "../../../../input-format/source/dom.js", "../../../../input-format/source/inputControl.js", "../../../../react-phone-number-input/source/helpers/inputValuePrefix.js", "../../../../react-phone-number-input/source/helpers/parsePhoneNumberCharacter.js", "../../../../react-phone-number-input/source/useInputKeyDownHandler.js", "../../../../react-phone-number-input/source/InputBasic.js", "../../../../react-phone-number-input/source/CountrySelect.js", "../../../../country-flag-icons/source/unicode.js", "../../../../react-phone-number-input/source/Flag.js", "../../../../react-phone-number-input/source/InternationalIcon.js", "../../../../react-phone-number-input/source/helpers/isE164Number.js", "../../../../react-phone-number-input/source/helpers/countries.js", "../../../../react-phone-number-input/source/CountryIcon.js", "../../../../react-phone-number-input/source/useExternalRef.js", "../../../../react-phone-number-input/source/PropTypes.js", "../../../../react-phone-number-input/source/helpers/getInternationalPhoneNumberPrefix.js", "../../../../react-phone-number-input/source/helpers/phoneInputHelpers.js", "../../../../react-phone-number-input/source/helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js", "../../../../react-phone-number-input/source/libphonenumber/formatPhoneNumber.js", "../../../../react-phone-number-input/source/PhoneInputWithCountryDefault.js", "../../../../react-phone-number-input/locale/en.json.js", "../../../../react-phone-number-input/min/index.js"], "sourcesContent": ["import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\nimport InputSmart from './InputSmart.js'\r\nimport InputBasic from './InputBasic.js'\r\n\r\nimport { CountrySelectWithIcon as CountrySelect } from './CountrySelect.js'\r\n\r\nimport Flag from './Flag.js'\r\nimport InternationalIcon from './InternationalIcon.js'\r\n\r\nimport { validateE164Number } from './helpers/isE164Number.js'\r\n\r\nimport {\r\n\tsortCountryOptions,\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries,\r\n\tgetSupportedCountryOptions,\r\n\tgetCountries\r\n} from './helpers/countries.js'\r\n\r\nimport { createCountryIconComponent } from './CountryIcon.js'\r\n\r\nimport { setRefsValue } from './useExternalRef.js'\r\n\r\nimport {\r\n\tmetadata as metadataPropType,\r\n\tlabels as labelsPropType\r\n} from './PropTypes.js'\r\n\r\nimport {\r\n\tgetPreSelectedCountry,\r\n\tgetCountrySelectOptions,\r\n\tcouldNumberBelongToCountry,\r\n\tparsePhoneNumber,\r\n\tgenerateNationalNumberDigits,\r\n\tgetPhoneDigitsForNewCountry,\r\n\tgetInitialPhoneDigits,\r\n\tonPhoneDigitsChange,\r\n\te164\r\n} from './helpers/phoneInputHelpers.js'\r\n\r\nimport getPhoneInputWithCountryStateUpdateFromNewProps from './helpers/getPhoneInputWithCountryStateUpdateFromNewProps.js'\r\n\r\nclass PhoneNumberInput_ extends React.PureComponent {\r\n\tconstructor(props) {\r\n\t\tsuper(props)\r\n\r\n\t\tthis.inputRef = React.createRef()\r\n\r\n\t\tconst {\r\n\t\t\tvalue,\r\n\t\t\tlabels,\r\n\t\t\tinternational,\r\n\t\t\taddInternationalOption,\r\n\t\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t\t// superceded by `initialValueFormat` property.\r\n\t\t\tdisplayInitialValueAsLocalNumber,\r\n\t\t\tinitialValueFormat,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\r\n\t\tlet {\r\n\t\t\tdefaultCountry,\r\n\t\t\tcountries\r\n\t\t} = this.props\r\n\r\n\t\t// Validate `defaultCountry`.\r\n\t\tif (defaultCountry) {\r\n\t\t\tif (!this.isCountrySupportedWithError(defaultCountry)) {\r\n\t\t\t\tdefaultCountry = undefined\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Validate that the initially-supplied `value` is in `E.164` format.\r\n\t\t// Because sometimes people attempt to supply a `value` like \"+****************\".\r\n\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796\r\n\t\tif (value) {\r\n\t\t\tvalidateE164Number(value)\r\n\t\t}\r\n\r\n\t\t// Validate `countries`.\r\n\t\tcountries = getSupportedCountries(countries, metadata)\r\n\r\n\t\tconst phoneNumber = parsePhoneNumber(value, metadata)\r\n\r\n\t\tthis.CountryIcon = createCountryIconComponent(this.props)\r\n\r\n\t\tconst preSelectedCountry = getPreSelectedCountry({\r\n\t\t\tvalue,\r\n\t\t\tphoneNumber,\r\n\t\t\tdefaultCountry,\r\n\t\t\trequired: !addInternationalOption,\r\n\t\t\tcountries: countries || getCountries(metadata),\r\n\t\t\tgetAnyCountry: () => this.getFirstSupportedCountry({ countries }),\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tthis.state = {\r\n\t\t\t// Workaround for `this.props` inside `getDerivedStateFromProps()`.\r\n\t\t\tprops: this.props,\r\n\r\n\t\t\t// The country selected.\r\n\t\t\tcountry: preSelectedCountry,\r\n\r\n\t\t\t// `countries` are stored in `this.state` because they're filtered.\r\n\t\t\t// For example, a developer might theoretically pass some unsupported\r\n\t\t\t// countries as part of the `countries` property, and because of that\r\n\t\t\t// the component uses `this.state.countries` (which are filtered)\r\n\t\t\t// instead of `this.props.countries`\r\n\t\t\t// (which could potentially contain unsupported countries).\r\n\t\t\tcountries,\r\n\r\n\t\t\t// `phoneDigits` state property holds non-formatted user's input.\r\n\t\t\t// The reason is that there's no way of finding out\r\n\t\t\t// in which form should `value` be displayed: international or national.\r\n\t\t\t// E.g. if `value` is `+78005553535` then it could be input\r\n\t\t\t// by a user both as `8 (800) 555-35-35` and `****** 555 35 35`.\r\n\t\t\t// Hence storing just `value` is not sufficient for correct formatting.\r\n\t\t\t// E.g. if a user entered `8 (800) 555-35-35`\r\n\t\t\t// then value is `+78005553535` and `phoneDigits` are `88005553535`\r\n\t\t\t// and if a user entered `****** 555 35 35`\r\n\t\t\t// then value is `+78005553535` and `phoneDigits` are `+78005553535`.\r\n\t\t\tphoneDigits: getInitialPhoneDigits({\r\n\t\t\t\tvalue,\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tdefaultCountry,\r\n\t\t\t\tinternational,\r\n\t\t\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\t\t\tmetadata\r\n\t\t\t}),\r\n\r\n\t\t\t// `value` property is duplicated in state.\r\n\t\t\t// The reason is that `getDerivedStateFromProps()`\r\n\t\t\t// needs this `value` to compare to the new `value` property\r\n\t\t\t// to find out if `phoneDigits` needs updating:\r\n\t\t\t// If the `value` property was changed externally\r\n\t\t\t// then it won't be equal to `state.value`\r\n\t\t\t// in which case `phoneDigits` and `country` should be updated.\r\n\t\t\tvalue\r\n\t\t}\r\n\t}\r\n\r\n\tcomponentDidMount() {\r\n\t\tconst { onCountryChange } = this.props\r\n\t\tlet { defaultCountry } = this.props\r\n\t\tconst { country: selectedCountry } = this.state\r\n\t\tif (onCountryChange) {\r\n\t\t\tif (defaultCountry) {\r\n\t\t\t\tif (!this.isCountrySupportedWithError(defaultCountry)) {\r\n\t\t\t\t\tdefaultCountry = undefined\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (selectedCountry !== defaultCountry) {\r\n\t\t\t\tonCountryChange(selectedCountry)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tcomponentDidUpdate(prevProps, prevState) {\r\n\t\tconst { onCountryChange } = this.props\r\n\t\tconst { country } = this.state\r\n\t\t// Call `onCountryChange` when user selects another country.\r\n\t\tif (onCountryChange && country !== prevState.country) {\r\n\t\t\tonCountryChange(country)\r\n\t\t}\r\n\t}\r\n\r\n\t// This function mimicks `refSetter` function returned from `useExternalRef()` hook\r\n\t// because this class-like React component can't use the `useExternalRef()` hook.\r\n\tsetInputRef = (instance) => {\r\n\t\tsetRefsValue([this.props.inputRef, this.inputRef], instance)\r\n\t}\r\n\r\n\tgetCountrySelectOptions({ countries }) {\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\taddInternationalOption,\r\n\t\t\tlabels,\r\n\t\t\tlocales,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\t\treturn this.useMemoCountrySelectOptions(() => {\r\n\t\t\treturn sortCountryOptions(\r\n\t\t\t\tgetCountrySelectOptions({\r\n\t\t\t\t\tcountries: countries || getCountries(metadata),\r\n\t\t\t\t\tcountryNames: labels,\r\n\t\t\t\t\taddInternationalOption: (international && countryCallingCodeEditable === false) ? false : addInternationalOption,\r\n\t\t\t\t\tcompareStringsLocales: locales,\r\n\t\t\t\t\t// compareStrings\r\n\t\t\t\t}),\r\n\t\t\t\tgetSupportedCountryOptions(countryOptionsOrder, metadata)\r\n\t\t\t)\r\n\t\t}, [\r\n\t\t\tcountries,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\taddInternationalOption,\r\n\t\t\tlabels,\r\n\t\t\tmetadata\r\n\t\t])\r\n\t}\r\n\r\n\tuseMemoCountrySelectOptions(generator, dependencies) {\r\n\t\tif (\r\n\t\t\t!this.countrySelectOptionsMemoDependencies ||\r\n\t\t\t!areEqualArrays(dependencies, this.countrySelectOptionsMemoDependencies)\r\n\t\t) {\r\n\t\t\tthis.countrySelectOptionsMemo = generator()\r\n\t\t\tthis.countrySelectOptionsMemoDependencies = dependencies\r\n\t\t}\r\n\t\treturn this.countrySelectOptionsMemo\r\n\t}\r\n\r\n\tgetFirstSupportedCountry({ countries }) {\r\n\t\tconst countryOptions = this.getCountrySelectOptions({ countries })\r\n\t\treturn countryOptions[0].value\r\n\t}\r\n\r\n\t// A shorthand for not passing `metadata` as a second argument.\r\n\tisCountrySupportedWithError = (country) => {\r\n\t\tconst { metadata } = this.props\r\n\t\treturn isCountrySupportedWithError(country, metadata)\r\n\t}\r\n\r\n\t// Country `<select/>` `onChange` handler.\r\n\tonCountryChange = (newCountry) => {\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tmetadata,\r\n\t\t\tonChange,\r\n\t\t\tfocusInputOnCountrySelection\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tphoneDigits: prevPhoneDigits,\r\n\t\t\tcountry: prevCountry\r\n\t\t} = this.state\r\n\r\n\t\t// After the new `country` has been selected,\r\n\t\t// if the phone number `<input/>` holds any digits\r\n\t\t// then migrate those digits for the new `country`.\r\n\t\tconst newPhoneDigits = getPhoneDigitsForNewCountry(prevPhoneDigits, {\r\n\t\t\tprevCountry,\r\n\t\t\tnewCountry,\r\n\t\t\tmetadata,\r\n\t\t\t// Convert the phone number to \"national\" format\r\n\t\t\t// when the user changes the selected country by hand.\r\n\t\t\tuseNationalFormat: !international\r\n\t\t})\r\n\r\n\t\tconst newValue = e164(newPhoneDigits, newCountry, metadata)\r\n\r\n\t\t// Focus phone number `<input/>` upon country selection.\r\n\t\tif (focusInputOnCountrySelection) {\r\n\t\t\tthis.inputRef.current.focus()\r\n\t\t}\r\n\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\r\n\t\tthis.setState({\r\n\t\t\tcountry: newCountry,\r\n\t\t\tlatestCountrySelectedByUser: newCountry,\r\n\t\t\thasUserSelectedACountry: true,\r\n\t\t\tphoneDigits: newPhoneDigits,\r\n\t\t\tvalue: newValue\r\n\t\t},\r\n\t\t() => {\r\n\t\t\t// Update the new `value` property.\r\n\t\t\t// Doing it after the `state` has been updated\r\n\t\t\t// because `onChange()` will trigger `getDerivedStateFromProps()`\r\n\t\t\t// with the new `value` which will be compared to `state.value` there.\r\n\t\t\tonChange(newValue)\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * `<input/>` `onChange()` handler.\r\n\t * Updates `value` property accordingly (so that they are kept in sync).\r\n\t * @param {string?} input — Either a parsed phone number or an empty string. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t */\r\n\tonChange = (_phoneDigits) => {\r\n\t\tconst {\r\n\t\t\tdefaultCountry,\r\n\t\t\tonChange,\r\n\t\t\taddInternationalOption,\r\n\t\t\tinternational,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tmetadata\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tcountries,\r\n\t\t\tphoneDigits: prevPhoneDigits,\r\n\t\t\tcountry: currentlySelectedCountry,\r\n\t\t\tlatestCountrySelectedByUser\r\n\t\t} = this.state\r\n\r\n\t\tconst {\r\n\t\t\t// `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`.\r\n\t\t\t// The returned `phoneDigits` shouldn't be used anywhere except for passing it as\r\n\t\t\t// `prevPhoneDigits` parameter to the same `onPhoneDigitsChange()` function\r\n\t\t\t// on next input change event.\r\n\t\t\tphoneDigits,\r\n\t\t\tcountry,\r\n\t\t\tvalue\r\n\t\t} = onPhoneDigitsChange(_phoneDigits, {\r\n\t\t\tprevPhoneDigits,\r\n\t\t\tcountry: currentlySelectedCountry,\r\n\t\t\tcountryRequired: !addInternationalOption,\r\n\t\t\tdefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser,\r\n\t\t\tgetAnyCountry: () => this.getFirstSupportedCountry({ countries }),\r\n\t\t\tcountries,\r\n\t\t\tinternational,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tconst stateUpdate = {\r\n\t\t\tphoneDigits,\r\n\t\t\tvalue,\r\n\t\t\tcountry\r\n\t\t}\r\n\r\n\t\t// Reset `latestCountrySelectedByUser` if it no longer fits the `value`.\r\n\t\tif (latestCountrySelectedByUser && value && !couldNumberBelongToCountry(value, latestCountrySelectedByUser, metadata)) {\r\n\t\t\tstateUpdate.latestCountrySelectedByUser = undefined\r\n\t\t}\r\n\r\n\t\tif (countryCallingCodeEditable === false) {\r\n\t\t\t// If it simply did `setState({ phoneDigits: intlPrefix })` here,\r\n\t\t\t// then it would have no effect when erasing an inital international prefix\r\n\t\t\t// via Backspace, because `phoneDigits` in `state` wouldn't change\r\n\t\t\t// as a result, because it was `prefix` and it became `prefix`,\r\n\t\t\t// so the component wouldn't rerender, and the user would be able\r\n\t\t\t// to erase the country calling code part, and that part is\r\n\t\t\t// assumed to be non-eraseable. That's why the component is\r\n\t\t\t// forcefully rerendered here.\r\n\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/367#issuecomment-721703501\r\n\t\t\tif (!value && phoneDigits === this.state.phoneDigits) {\r\n\t\t\t\t// Force a re-render of the `<input/>` in order to reset its value.\r\n\t\t\t\tstateUpdate.forceRerender = {}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.setState(\r\n\t\t\tstateUpdate,\r\n\t\t\t// Update the new `value` property.\r\n\t\t\t// Doing it after the `state` has been updated\r\n\t\t\t// because `onChange()` will trigger `getDerivedStateFromProps()`\r\n\t\t\t// with the new `value` which will be compared to `state.value` there.\r\n\t\t\t() => onChange(value)\r\n\t\t)\r\n\t}\r\n\r\n\t// Toggles the `--focus` CSS class.\r\n\t_onFocus = () => this.setState({ isFocused: true })\r\n\r\n\t// Toggles the `--focus` CSS class.\r\n\t_onBlur = () => this.setState({ isFocused: false })\r\n\r\n\tonFocus = (event) => {\r\n\t\tthis._onFocus()\r\n\t\tconst { onFocus } = this.props\r\n\t\tif (onFocus) {\r\n\t\t\tonFocus(event)\r\n\t\t}\r\n\t}\r\n\r\n\tonBlur = (event) => {\r\n\t\tconst { onBlur } = this.props\r\n\t\tthis._onBlur()\r\n\t\tif (onBlur) {\r\n\t\t\tonBlur(event)\r\n\t\t}\r\n\t}\r\n\r\n\tonCountryFocus = (event) => {\r\n\t\tthis._onFocus()\r\n\t\t// this.setState({ countrySelectFocused: true })\r\n\t\tconst { countrySelectProps } = this.props\r\n\t\tif (countrySelectProps) {\r\n\t\t\tconst { onFocus } = countrySelectProps\r\n\t\t\tif (onFocus) {\r\n\t\t\t\tonFocus(event)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tonCountryBlur = (event) => {\r\n\t\tthis._onBlur()\r\n\t\t// this.setState({ countrySelectFocused: false })\r\n\t\tconst { countrySelectProps } = this.props\r\n\t\tif (countrySelectProps) {\r\n\t\t\tconst { onBlur } = countrySelectProps\r\n\t\t\tif (onBlur) {\r\n\t\t\t\tonBlur(event)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// `state` holds previous props as `props`, and also:\r\n\t// * `country` — The currently selected country, e.g. `\"RU\"`.\r\n\t// * `value` — The currently entered phone number (E.164), e.g. `+78005553535`.\r\n\t// * `phoneDigits` — The parsed `<input/>` value, e.g. `8005553535`.\r\n\t// (and a couple of other less significant properties)\r\n\tstatic getDerivedStateFromProps(props, state) {\r\n\t\treturn {\r\n\t\t\t// Emulate `prevProps` via `state.props`.\r\n\t\t\tprops,\r\n\t\t\t...getPhoneInputWithCountryStateUpdateFromNewProps(props, state.props, state)\r\n\t\t}\r\n\t}\r\n\r\n\trender() {\r\n\t\tconst {\r\n\t\t\t// Generic HTML attributes.\r\n\t\t\tname,\r\n\t\t\tdisabled,\r\n\t\t\treadOnly,\r\n\t\t\tautoComplete,\r\n\t\t\tstyle,\r\n\t\t\tclassName,\r\n\r\n\t\t\t// Number `<input/>` properties.\r\n\t\t\tinputRef,\r\n\t\t\tinputComponent,\r\n\t\t\tnumberInputProps,\r\n\t\t\tsmartCaret,\r\n\r\n\t\t\t// Country `<select/>` properties.\r\n\t\t\tcountrySelectComponent: CountrySelectComponent,\r\n\t\t\tcountrySelectProps,\r\n\r\n\t\t\t// Container `<div/>` properties.\r\n\t\t\tcontainerComponent: ContainerComponent,\r\n\t\t\tcontainerComponentProps,\r\n\r\n\t\t\t// Get \"rest\" properties (passed through to number `<input/>`).\r\n\t\t\tdefaultCountry,\r\n\t\t\tcountries: countriesProperty,\r\n\t\t\tcountryOptionsOrder,\r\n\t\t\tlabels,\r\n\t\t\tflags,\r\n\t\t\tflagComponent,\r\n\t\t\tflagUrl,\r\n\t\t\taddInternationalOption,\r\n\t\t\tinternationalIcon,\r\n\t\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t\t// superceded by `initialValueFormat` property.\r\n\t\t\tdisplayInitialValueAsLocalNumber,\r\n\t\t\tinitialValueFormat,\r\n\t\t\tonCountryChange,\r\n\t\t\tlimitMaxLength,\r\n\t\t\tcountryCallingCodeEditable,\r\n\t\t\tfocusInputOnCountrySelection,\r\n\t\t\treset,\r\n\t\t\tmetadata,\r\n\t\t\tinternational,\r\n\t\t\tlocales,\r\n\t\t\t// compareStrings,\r\n\t\t\t...rest\r\n\t\t} = this.props\r\n\r\n\t\tconst {\r\n\t\t\tcountry,\r\n\t\t\tcountries,\r\n\t\t\tphoneDigits,\r\n\t\t\tisFocused\r\n\t\t} = this.state\r\n\r\n\t\tconst InputComponent = smartCaret ? InputSmart : InputBasic\r\n\r\n\t\tconst countrySelectOptions = this.getCountrySelectOptions({ countries })\r\n\r\n\t\treturn (\r\n\t\t\t<ContainerComponent\r\n\t\t\t\tstyle={style}\r\n\t\t\t\tclassName={classNames(className, 'PhoneInput', {\r\n\t\t\t\t\t'PhoneInput--focus': isFocused,\r\n\t\t\t\t\t'PhoneInput--disabled': disabled,\r\n\t\t\t\t\t'PhoneInput--readOnly': readOnly\r\n\t\t\t\t})}\r\n\t\t\t\t{...containerComponentProps}>\r\n\r\n\t\t\t\t{/* Country `<select/>` */}\r\n\t\t\t\t<CountrySelectComponent\r\n\t\t\t\t\tname={name ? `${name}Country` : undefined}\r\n\t\t\t\t\taria-label={labels.country}\r\n\t\t\t\t\t{...countrySelectProps}\r\n\t\t\t\t\tvalue={country}\r\n\t\t\t\t\toptions={countrySelectOptions}\r\n\t\t\t\t\tonChange={this.onCountryChange}\r\n\t\t\t\t\tonFocus={this.onCountryFocus}\r\n\t\t\t\t\tonBlur={this.onCountryBlur}\r\n\t\t\t\t\tdisabled={disabled || (countrySelectProps && countrySelectProps.disabled)}\r\n\t\t\t\t\treadOnly={readOnly || (countrySelectProps && countrySelectProps.readOnly)}\r\n\t\t\t\t\ticonComponent={this.CountryIcon}/>\r\n\r\n\t\t\t\t{/* Phone number `<input/>` */}\r\n\t\t\t\t<InputComponent\r\n\t\t\t\t\tref={this.setInputRef}\r\n\t\t\t\t\ttype=\"tel\"\r\n\t\t\t\t\tautoComplete={autoComplete}\r\n\t\t\t\t\t{...numberInputProps}\r\n\t\t\t\t\t{...rest}\r\n\t\t\t\t\tinternational={international ? true : undefined}\r\n\t\t\t\t\twithCountryCallingCode={international ? true : undefined}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t\tmetadata={metadata}\r\n\t\t\t\t\tcountry={country}\r\n\t\t\t\t\tvalue={phoneDigits || ''}\r\n\t\t\t\t\tonChange={this.onChange}\r\n\t\t\t\t\tonFocus={this.onFocus}\r\n\t\t\t\t\tonBlur={this.onBlur}\r\n\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\treadOnly={readOnly}\r\n\t\t\t\t\tinputComponent={inputComponent}\r\n\t\t\t\t\tclassName={classNames(\r\n\t\t\t\t\t\t'PhoneInputInput',\r\n\t\t\t\t\t\tnumberInputProps && numberInputProps.className,\r\n\t\t\t\t\t\trest.className\r\n\t\t\t\t\t)}/>\r\n\t\t\t</ContainerComponent>\r\n\t\t)\r\n\t}\r\n}\r\n\r\n// This wrapper is only to `.forwardRef()` to the `<input/>`.\r\nconst PhoneNumberInput = React.forwardRef((props, ref) => (\r\n\t<PhoneNumberInput_ {...withDefaultProps(props)} inputRef={ref}/>\r\n))\r\n\r\nPhoneNumberInput.propTypes = {\r\n\t/**\r\n\t * Phone number in `E.164` format.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `\"+12223333333\"`\r\n\t *\r\n\t * Any \"falsy\" value like `undefined`, `null` or an empty string `\"\"` is treated like \"empty\".\r\n\t */\r\n\tvalue: PropTypes.string,\r\n\r\n\t/**\r\n\t * A function of `value: string?`.\r\n\t *\r\n\t * Updates the `value` property as the user inputs a phone number.\r\n\t *\r\n\t * If the user erases the input value, the argument is `undefined`.\r\n\t */\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t/**\r\n\t * Toggles the `--focus` CSS class.\r\n\t * @ignore\r\n\t */\r\n\tonFocus: PropTypes.func,\r\n\r\n\t/**\r\n\t * `onBlur` is usually passed by `redux-form`.\r\n\t * @ignore\r\n\t */\r\n\tonBlur: PropTypes.func,\r\n\r\n\t/**\r\n\t * Set to `true` to mark both the phone number `<input/>`\r\n\t * and the country `<select/>` as `disabled`.\r\n\t */\r\n\tdisabled: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Set to `true` to mark both the phone number `<input/>`\r\n\t * and the country `<select/>` as `readonly`.\r\n\t */\r\n\treadOnly: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Sets `autoComplete` property for phone number `<input/>`.\r\n\t *\r\n\t * Web browser's \"autocomplete\" feature\r\n\t * remembers the phone number being input\r\n\t * and can also autofill the `<input/>`\r\n\t * with previously remembered phone numbers.\r\n\t *\r\n\t * https://developers.google.com\r\n\t * /web/updates/2015/06/checkout-faster-with-autofill\r\n\t *\r\n\t * For example, can be used to turn it off:\r\n\t *\r\n\t * \"So when should you use `autocomplete=\"off\"`?\r\n\t *  One example is when you've implemented your own version\r\n\t *  of autocomplete for search. Another example is any form field\r\n\t *  where users will input and submit different kinds of information\r\n\t *  where it would not be useful to have the browser remember\r\n\t *  what was submitted previously\".\r\n\t */\r\n\t// (is `\"tel\"` by default)\r\n\tautoComplete: PropTypes.string,\r\n\r\n\t/**\r\n\t * Set to `\"national\"` to show the initial `value` in\r\n\t * \"national\" format rather than \"international\".\r\n\t *\r\n\t * For example, if `initialValueFormat` is `\"national\"`\r\n\t * and the initial `value=\"+12133734253\"` is passed\r\n\t * then the `<input/>` value will be `\"(*************\"`.\r\n\t *\r\n\t * By default, `initialValueFormat` is `undefined`,\r\n\t * meaning that if the initial `value=\"+12133734253\"` is passed\r\n\t * then the `<input/>` value will be `\"****** 373 4253\"`.\r\n\t *\r\n\t * The reason for such default behaviour is that\r\n\t * the newer generation grows up when there are no stationary phones\r\n\t * and therefore everyone inputs phone numbers in international format\r\n\t * in their smartphones so people gradually get more accustomed to\r\n\t * writing phone numbers in international format rather than in local format.\r\n\t * Future people won't be using \"national\" format, only \"international\".\r\n\t */\r\n\t// (is `undefined` by default)\r\n\tinitialValueFormat: PropTypes.oneOf(['national']),\r\n\r\n\t// `displayInitialValueAsLocalNumber` property has been\r\n\t// superceded by `initialValueFormat` property.\r\n\tdisplayInitialValueAsLocalNumber: PropTypes.bool,\r\n\r\n\t/**\r\n\t * The country to be selected by default.\r\n\t * For example, can be set after a GeoIP lookup.\r\n\t *\r\n\t * Example: `\"US\"`.\r\n\t */\r\n\t// A two-letter country code (\"ISO 3166-1 alpha-2\").\r\n\tdefaultCountry: PropTypes.string,\r\n\r\n\t/**\r\n\t * If specified, only these countries will be available for selection.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `[\"RU\", \"UA\", \"KZ\"]`\r\n\t */\r\n\tcountries: PropTypes.arrayOf(PropTypes.string),\r\n\r\n\t/**\r\n\t * Custom country `<select/>` option names.\r\n\t * Also some labels like \"ext\" and country `<select/>` `aria-label`.\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `{ \"ZZ\": \"Международный\", RU: \"Россия\", US: \"США\", ... }`\r\n\t *\r\n\t * See the `locales` directory for examples.\r\n\t */\r\n\tlabels: labelsPropType,\r\n\r\n\t/**\r\n\t * Country `<select/>` options are sorted by their labels.\r\n\t * The default sorting function uses `a.localeCompare(b, locales)`,\r\n\t * and, if that's not available, falls back to simple `a > b` / `a < b`.\r\n\t * Some languages, like Chinese, support multiple sorting variants\r\n\t * (called \"collations\"), and the user might prefer one or another.\r\n\t * Also, sometimes the Operating System language is not always\r\n\t * the preferred language for a person using a website or an application,\r\n\t * so there should be a way to specify custom locale.\r\n\t * This `locales` property mimicks the `locales` argument of `Intl` constructors,\r\n\t * and can be either a Unicode BCP 47 locale identifier or an array of such locale identifiers.\r\n\t * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locales_argument\r\n\t */\r\n\tlocales: PropTypes.oneOfType([\r\n\t\tPropTypes.string,\r\n\t\tPropTypes.arrayOf(PropTypes.string)\r\n\t]),\r\n\r\n\t/*\r\n\t * Custom country `<select/>` options sorting function.\r\n\t * The default one uses `a.localeCompare(b)`, and,\r\n\t * if that's not available, falls back to simple `a > b`/`a < b`.\r\n\t * There have been requests to add custom sorter for cases\r\n\t * like Chinese language and \"pinyin\" (non-default) sorting order.\r\n\t * https://stackoverflow.com/questions/22907288/chinese-sorting-by-pinyin-in-javascript-with-localecompare\r\n\tcompareStrings: PropTypes.func,\r\n\t */\r\n\r\n\t/**\r\n\t * A URL template of a country flag, where\r\n\t * \"{XX}\" is a two-letter country code in upper case,\r\n\t * or where \"{xx}\" is a two-letter country code in lower case.\r\n\t * By default it points to `country-flag-icons` gitlab pages website.\r\n\t * I imagine someone might want to download those country flag icons\r\n\t * and host them on their own servers instead\r\n\t * (all flags are available in the `country-flag-icons` library).\r\n\t * There's a catch though: new countries may be added in future,\r\n\t * so when hosting country flag icons on your own server\r\n\t * one should check the `CHANGELOG.md` every time before updating this library,\r\n\t * otherwise there's a possibility that some new country flag would be missing.\r\n\t */\r\n\tflagUrl: PropTypes.string,\r\n\r\n\t/**\r\n\t * Custom country flag icon components.\r\n\t * These flags will be used instead of the default ones.\r\n\t * The the \"Flags\" section of the readme for more info.\r\n\t *\r\n\t * The shape is an object where keys are country codes\r\n\t * and values are flag icon components.\r\n\t * Flag icon components receive the same properties\r\n\t * as `flagComponent` (see below).\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `{ \"RU\": (props) => <img src=\"...\"/> }`\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `import flags from 'country-flag-icons/react/3x2'`\r\n\t *\r\n\t * `import PhoneInput from 'react-phone-number-input'`\r\n\t *\r\n\t * `<PhoneInput flags={flags} .../>`\r\n\t */\r\n\tflags: PropTypes.objectOf(PropTypes.elementType),\r\n\r\n\t/**\r\n\t * Country flag icon component.\r\n\t *\r\n\t * Takes properties:\r\n\t *\r\n\t * * `country: string` — The country code.\r\n\t * * `countryName: string` — The country name.\r\n\t * * `flagUrl: string` — The `flagUrl` property (see above).\r\n\t * * `flags: object` — The `flags` property (see above).\r\n\t */\r\n\tflagComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Set to `false` to remove the \"International\" option from country `<select/>`.\r\n\t */\r\n\taddInternationalOption: PropTypes.bool,\r\n\r\n\t/**\r\n\t * \"International\" icon component.\r\n\t * Should have the same aspect ratio.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `title: string` — \"International\" country option label.\r\n\t */\r\n\tinternationalIcon: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Can be used to place some countries on top of the list of country `<select/>` options.\r\n\t *\r\n\t * * `\"XX\"` — inserts an option for \"XX\" country.\r\n\t * * `\"🌐\"` — inserts \"International\" option.\r\n\t * * `\"|\"` — inserts a separator.\r\n\t * * `\"...\"` — inserts options for the rest of the countries (can be omitted, in which case it will be automatically added at the end).\r\n\t *\r\n\t * Example:\r\n\t *\r\n\t * `[\"US\", \"CA\", \"AU\", \"|\", \"...\"]`\r\n\t */\r\n\tcountryOptionsOrder: PropTypes.arrayOf(PropTypes.string),\r\n\r\n\t/**\r\n\t * `<Phone/>` component CSS style object.\r\n\t */\r\n\tstyle: PropTypes.object,\r\n\r\n\t/**\r\n\t * `<Phone/>` component CSS class.\r\n\t */\r\n\tclassName: PropTypes.string,\r\n\r\n\t/**\r\n\t * Country `<select/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `name: string?` — HTML `name` attribute.\r\n\t * * `value: string?` — The currently selected country code.\r\n\t * * `onChange(value: string?)` — Updates the `value`.\r\n\t * * `onFocus()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `onBlur()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `options: object[]` — The list of all selectable countries (including \"International\") each being an object of shape `{ value: string?, label: string }`.\r\n\t * * `iconComponent: PropTypes.elementType` — React component that renders a country icon: `<Icon country={value}/>`. If `country` is `undefined` then it renders an \"International\" icon.\r\n\t * * `disabled: boolean?` — HTML `disabled` attribute.\r\n\t * * `readOnly: boolean?` — HTML `readOnly` attribute.\r\n\t * * `tabIndex: (number|string)?` — HTML `tabIndex` attribute.\r\n\t * * `className: string` — CSS class name.\r\n\t */\r\n\tcountrySelectComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Country `<select/>` component props.\r\n\t * Along with the usual DOM properties such as `aria-label` and `tabIndex`,\r\n\t * some custom properties are supported, such as `arrowComponent` and `unicodeFlags`.\r\n\t */\r\n\tcountrySelectProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `value: string` — The formatted `value`.\r\n\t * * `onChange(event: Event)` — Updates the formatted `value` from `event.target.value`.\r\n\t * * `onFocus()` — Is used to toggle the `--focus` CSS class.\r\n\t * * `onBlur()` — Is used to toggle the `--focus` CSS class.\r\n\t * * Other properties like `type=\"tel\"` or `autoComplete=\"tel\"` that should be passed through to the DOM `<input/>`.\r\n\t *\r\n\t * Must also either use `React.forwardRef()` to \"forward\" `ref` to the `<input/>` or implement `.focus()` method.\r\n\t */\r\n\tinputComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component props.\r\n\t */\r\n\tnumberInputProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component.\r\n\t *\r\n\t * Receives properties:\r\n\t *\r\n\t * * `style: object` — A component CSS style object.\r\n\t * * `className: string` — Classes to attach to the component, typically changes when component focuses or blurs.\r\n\t */\r\n\tcontainerComponent: PropTypes.elementType,\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component props.\r\n\t */\r\n\tcontainerComponentProps: PropTypes.object,\r\n\r\n\t/**\r\n\t * When the user attempts to insert a digit somewhere in the middle of a phone number,\r\n\t * the caret position is moved right before the next available digit skipping\r\n\t * any punctuation in between. This is called \"smart\" caret positioning.\r\n\t * Another case would be the phone number format changing as a result of\r\n\t * the user inserting the digit somewhere in the middle, which would require\r\n\t * re-positioning the caret because all digit positions have changed.\r\n\t * This \"smart\" caret positioning feature can be turned off by passing\r\n\t * `smartCaret={false}` property: use it in case of any possible issues\r\n\t * with caret position during phone number input.\r\n\t */\r\n\t// Is `true` by default.\r\n\tsmartCaret: PropTypes.bool,\r\n\r\n\t/**\r\n\t * Set to `true` to force \"international\" phone number format.\r\n\t * Set to `false` to force \"national\" phone number format.\r\n\t * By default it's `undefined` meaning that it doesn't enforce any phone number format.\r\n\t */\r\n\tinternational: PropTypes.bool,\r\n\r\n\t/**\r\n\t * If set to `true`, the phone number input will get trimmed\r\n\t * if it exceeds the maximum length for the country.\r\n\t */\r\n\tlimitMaxLength: PropTypes.bool,\r\n\r\n\t/**\r\n\t * If set to `false`, and `international` is `true`, then\r\n\t * users won't be able to erase the \"country calling part\"\r\n\t * of a phone number in the `<input/>`.\r\n\t */\r\n\tcountryCallingCodeEditable: PropTypes.bool,\r\n\r\n\t/**\r\n\t * `libphonenumber-js` metadata.\r\n\t *\r\n\t * Can be used to pass custom `libphonenumber-js` metadata\r\n\t * to reduce the overall bundle size for those who compile \"custom\" metadata.\r\n\t */\r\n\tmetadata: metadataPropType,\r\n\r\n\t/**\r\n\t * Is called every time the selected country changes:\r\n\t * either programmatically or when user selects it manually from the list.\r\n\t */\r\n\t// People have been asking for a way to get the selected country.\r\n\t// @see  https://github.com/catamphetamine/react-phone-number-input/issues/128\r\n\t// For some it's just a \"business requirement\".\r\n\t// I guess it's about gathering as much info on the user as a website can\r\n\t// without introducing any addional fields that would complicate the form\r\n\t// therefore reducing \"conversion\" (that's a marketing term).\r\n\t// Assuming that the phone number's country is the user's country\r\n\t// is not 100% correct but in most cases I guess it's valid.\r\n\tonCountryChange: PropTypes.func,\r\n\r\n\t/**\r\n\t * If set to `false`, will not focus the `<input/>` component\r\n\t * when the user selects a country from the list of countries.\r\n\t * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).\r\n\t * Quote:\r\n\t * \"On input: Changing the setting of any user interface component\r\n\t *  does not automatically cause a change of context unless the user\r\n\t *  has been advised of the behaviour before using the component.\"\r\n\t */\r\n\tfocusInputOnCountrySelection: PropTypes.bool\r\n}\r\n\r\nconst defaultProps = {\r\n\t/**\r\n\t * Remember (and autofill) the value as a phone number.\r\n\t */\r\n\tautoComplete: 'tel',\r\n\r\n\t/**\r\n\t * Country `<select/>` component.\r\n\t */\r\n\tcountrySelectComponent: CountrySelect,\r\n\r\n\t/**\r\n\t * Flag icon component.\r\n\t */\r\n\tflagComponent: Flag,\r\n\r\n\t/**\r\n\t * By default, uses icons from `country-flag-icons` gitlab pages website.\r\n\t */\r\n\t// Must be equal to `flagUrl` in `./CountryIcon.js`.\r\n\tflagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',\r\n\r\n\t/**\r\n\t * Default \"International\" country `<select/>` option icon.\r\n\t */\r\n\tinternationalIcon: InternationalIcon,\r\n\r\n\t/**\r\n\t * Phone number `<input/>` component.\r\n\t */\r\n\tinputComponent: 'input',\r\n\r\n\t/**\r\n\t * Wrapping `<div/>` component.\r\n\t */\r\n\tcontainerComponent: 'div',\r\n\r\n\t/**\r\n\t * Some users requested a way to reset the component:\r\n\t * both number `<input/>` and country `<select/>`.\r\n\t * Whenever `reset` property changes both number `<input/>`\r\n\t * and country `<select/>` are reset.\r\n\t * It's not implemented as some instance `.reset()` method\r\n\t * because `ref` is forwarded to `<input/>`.\r\n\t * It's also not replaced with just resetting `country` on\r\n\t * external `value` reset, because a user could select a country\r\n\t * and then not input any `value`, and so the selected country\r\n\t * would be \"stuck\", if not using this `reset` property.\r\n\t */\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\treset: PropTypes.any,\r\n\r\n\t/**\r\n\t *\r\n\t */\r\n\r\n\t/**\r\n\t * Set to `false` to use \"basic\" caret instead of the \"smart\" one.\r\n\t */\r\n\tsmartCaret: true,\r\n\r\n\t/**\r\n\t * Whether to add the \"International\" option\r\n\t * to the list of countries.\r\n\t */\r\n\taddInternationalOption: true,\r\n\r\n\t/**\r\n\t * If set to `false`, and `international` is `true`, then\r\n\t * users won't be able to erase the \"country calling part\"\r\n\t * of a phone number in the `<input/>`.\r\n\t */\r\n\tcountryCallingCodeEditable: true,\r\n\r\n\t/**\r\n\t * If set to `false`, will not focus the `<input/>` component\r\n\t * when the user selects a country from the list of countries.\r\n\t * This can be used to conform to the Web Content Accessibility Guidelines (WCAG).\r\n\t * Quote:\r\n\t * \"On input: Changing the setting of any user interface component\r\n\t *  does not automatically cause a change of context unless the user\r\n\t *  has been advised of the behaviour before using the component.\"\r\n\t */\r\n\tfocusInputOnCountrySelection: true\r\n}\r\n\r\nfunction withDefaultProps(props) {\r\n\tprops = { ...props }\r\n\r\n\tfor (const key in defaultProps) {\r\n\t\tif (props[key] === undefined) {\r\n\t\t\tprops[key] = defaultProps[key]\r\n\t\t}\r\n\t}\r\n\r\n\treturn props\r\n}\r\n\r\nexport default PhoneNumberInput\r\n\r\nfunction areEqualArrays(a, b) {\r\n\tif (a.length !== b.length) {\r\n\t\treturn false\r\n\t}\r\n\tlet i = 0\r\n\twhile (i < a.length) {\r\n\t\tif (a[i] !== b[i]) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n", "import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport Input from 'input-format/react'\r\nimport { AsYouType } from 'libphonenumber-js/core'\r\n\r\nimport { getInputValuePrefix, removeInputValuePrefix } from './helpers/inputValuePrefix.js'\r\nimport parsePhoneNumberCharacter from './helpers/parsePhoneNumberCharacter.js'\r\n\r\nimport useInputKeyDownHandler from './useInputKeyDownHandler.js'\r\n\r\nexport function createInput(defaultMetadata)\r\n{\r\n\t/**\r\n\t * `InputSmart` is a \"smarter\" implementation of a `Component`\r\n\t * that can be passed to `<PhoneInput/>`. It parses and formats\r\n\t * the user's and maintains the caret's position in the process.\r\n\t * The caret positioning is maintained using `input-format` library.\r\n\t * Relies on being run in a DOM environment for calling caret positioning functions.\r\n\t */\r\n\tfunction InputSmart({\r\n\t\tonKeyDown,\r\n\t\tcountry,\r\n\t\tinternational,\r\n\t\twithCountryCallingCode,\r\n\t\tmetadata = defaultMetadata,\r\n\t\t...rest\r\n\t}, ref) {\r\n\t\tconst format = useCallback((value) => {\r\n\t\t\t// \"As you type\" formatter.\r\n\t\t\tconst formatter = new AsYouType(country, metadata)\r\n\t\t\tconst prefix = getInputValuePrefix({\r\n\t\t\t\tcountry,\r\n\t\t\t\tinternational,\r\n\t\t\t\twithCountryCallingCode,\r\n\t\t\t\tmetadata\r\n\t\t\t})\r\n\t\t\t// Format the number.\r\n\t\t\tlet text = formatter.input(prefix + value)\r\n\t\t\tlet template = formatter.getTemplate()\r\n\t\t\tif (prefix) {\r\n\t\t\t\ttext = removeInputValuePrefix(text, prefix)\r\n\t\t\t\t// `AsYouType.getTemplate()` can be `undefined`.\r\n\t\t\t\tif (template) {\r\n\t\t\t\t\ttemplate = removeInputValuePrefix(template, prefix)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\ttext,\r\n\t\t\t\ttemplate\r\n\t\t\t}\r\n\t\t}, [country, metadata])\r\n\r\n\t\tconst _onKeyDown = useInputKeyDownHandler({\r\n\t\t\tonKeyDown,\r\n\t\t\tinternational\r\n\t\t})\r\n\r\n\t\treturn (\r\n\t\t\t<Input\r\n\t\t\t\t{...rest}\r\n\t\t\t\tref={ref}\r\n\t\t\t\tparse={parsePhoneNumberCharacter}\r\n\t\t\t\tformat={format}\r\n\t\t\t\tonKeyDown={_onKeyDown}/>\r\n\t\t)\r\n\t}\r\n\r\n\tInputSmart = React.forwardRef(InputSmart)\r\n\r\n\tInputSmart.propTypes = {\r\n\t\t/**\r\n\t\t * The parsed phone number.\r\n\t\t * \"Parsed\" not in a sense of \"E.164\"\r\n\t\t * but rather in a sense of \"having only\r\n\t\t * digits and possibly a leading plus character\".\r\n\t\t * Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t\t */\r\n\t\tvalue: PropTypes.string.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `value: string`.\r\n\t\t * Updates the `value` property.\r\n\t\t */\r\n\t\tonChange: PropTypes.func.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `event: Event`.\r\n\t\t * Handles `keydown` events.\r\n\t\t */\r\n\t\tonKeyDown: PropTypes.func,\r\n\r\n\t\t/**\r\n\t\t * A two-letter country code for formatting `value`\r\n\t\t * as a national phone number (e.g. `(800) 555 35 35`).\r\n\t\t * E.g. \"US\", \"RU\", etc.\r\n\t\t * If no `country` is passed then `value`\r\n\t\t * is formatted as an international phone number.\r\n\t\t * (e.g. `****** 555 35 35`)\r\n\t\t * Perhaps the `country` property should have been called `defaultCountry`\r\n\t\t * because if `value` is an international number then `country` is ignored.\r\n\t\t */\r\n\t\tcountry: PropTypes.string,\r\n\r\n\t\t/**\r\n\t\t * If `country` property is passed along with `international={true}` property\r\n\t\t * then the phone number will be input in \"international\" format for that `country`\r\n\t\t * (without \"country calling code\").\r\n\t\t * For example, if `country=\"US\"` property is passed to \"without country select\" input\r\n\t\t * then the phone number will be input in the \"national\" format for `US` (`(*************`).\r\n\t\t * But if both `country=\"US\"` and `international={true}` properties are passed then\r\n\t\t * the phone number will be input in the \"international\" format for `US` (`************`)\r\n\t\t * (without \"country calling code\" `+1`).\r\n\t\t */\r\n\t\tinternational: PropTypes.bool,\r\n\r\n\t\t/**\r\n\t\t * If `country` and `international` properties are set,\r\n\t\t * then by default it won't include \"country calling code\" in the input field.\r\n\t\t * To change that, pass `withCountryCallingCode` property,\r\n\t\t * and it will include \"country calling code\" in the input field.\r\n\t\t */\r\n\t\twithCountryCallingCode: PropTypes.bool,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: PropTypes.object\r\n\t}\r\n\r\n\treturn InputSmart\r\n}\r\n\r\nexport default createInput()", "// This is just `./ReactInput.js` rewritten in Hooks.\r\n\r\nimport React, { useCallback, useRef } from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport {\r\n\tonChange as onInputChange,\r\n\tonKeyDown as onInputKeyDown\r\n} from '../inputControl.js'\r\n\r\n// Usage:\r\n//\r\n// <ReactInput\r\n// \tvalue={this.state.phone}\r\n// \tonChange={phone => this.setState({ phone })}\r\n// \tparse={character => character}\r\n// \tformat={value => ({ text: value, template: 'xxxxxxxx' })}/>\r\n//\r\nfunction Input({\r\n\tvalue,\r\n\tparse,\r\n\tformat,\r\n\tinputComponent: InputComponent,\r\n\tonChange,\r\n\tonKeyDown,\r\n\t...rest\r\n}, ref) {\r\n\tconst internalRef = useRef();\r\n\tconst setRef = useCallback((instance) => {\r\n\t\tinternalRef.current = instance;\r\n\t\tif (ref) {\r\n\t\t\tif (typeof ref === 'function') {\r\n\t\t\t\tref(instance)\r\n\t\t\t} else {\r\n\t\t\t\tref.current = instance\r\n\t\t\t}\r\n\t\t}\r\n\t}, [ref]);\r\n\tconst _onChange = useCallback((event) => {\r\n\t\treturn onInputChange(\r\n\t\t\tevent,\r\n\t\t\tinternalRef.current,\r\n\t\t\tparse,\r\n\t\t\tformat,\r\n\t\t\tonChange\r\n\t\t)\r\n\t}, [internalRef, parse, format, onChange])\r\n\r\n\tconst _onKeyDown = useCallback((event) => {\r\n\t\tif (onKeyDown) {\r\n\t\t\tonKeyDown(event)\r\n\t\t}\r\n\t\t// If `onKeyDown()` handler above has called `event.preventDefault()`\r\n\t\t// then ignore this `keydown` event.\r\n\t\tif (event.defaultPrevented) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\treturn onInputKeyDown(\r\n\t\t\tevent,\r\n\t\t\tinternalRef.current,\r\n\t\t\tparse,\r\n\t\t\tformat,\r\n\t\t\tonChange\r\n\t\t)\r\n\t}, [internalRef, parse, format, onChange, onKeyDown])\r\n\r\n\treturn (\r\n\t\t<InputComponent\r\n\t\t\t{...rest}\r\n\t\t\tref={setRef}\r\n\t\t\tvalue={format(isEmptyValue(value) ? '' : value).text}\r\n\t\t\tonKeyDown={_onKeyDown}\r\n\t\t\tonChange={_onChange}/>\r\n\t)\r\n}\r\n\r\nInput = React.forwardRef(Input)\r\n\r\nInput.propTypes = {\r\n\t// Parses a single characher of `<input/>` text.\r\n\tparse: PropTypes.func.isRequired,\r\n\r\n\t// Formats `value` into `<input/>` text.\r\n\tformat: PropTypes.func.isRequired,\r\n\r\n\t// Renders `<input/>` by default.\r\n\tinputComponent: PropTypes.elementType.isRequired,\r\n\r\n\t// `<input/>` `type` attribute.\r\n\ttype: PropTypes.string.isRequired,\r\n\r\n\t// Is parsed from <input/> text.\r\n\tvalue: PropTypes.string,\r\n\r\n\t// This handler is called each time `<input/>` text is changed.\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t// Passthrough\r\n\tonKeyDown: PropTypes.func,\r\n\tonCut: PropTypes.func,\r\n\tonPaste: PropTypes.func\r\n}\r\n\r\nInput.defaultProps = {\r\n\t// Renders `<input/>` by default.\r\n\tinputComponent: 'input',\r\n\r\n\t// `<input/>` `type` attribute.\r\n\ttype: 'text'\r\n}\r\n\r\nexport default Input\r\n\r\nfunction isEmptyValue(value) {\r\n\treturn value === undefined || value === null\r\n}", "// Edits text `value` (if `operation` is passed) and repositions the `caret` if needed.\r\n//\r\n// Example:\r\n//\r\n// value - '88005553535'\r\n// caret - 2 // starting from 0; is positioned before the first zero\r\n// operation - 'Backspace'\r\n//\r\n// Returns\r\n// {\r\n// \tvalue: '8005553535'\r\n// \tcaret: 1\r\n// }\r\n//\r\n// Currently supports just 'Delete' and 'Backspace' operations\r\n//\r\nexport default function edit(value, caret, operation)\r\n{\r\n\tswitch (operation)\r\n\t{\r\n\t\tcase 'Backspace':\r\n\t\t\t// If there exists the previous character,\r\n\t\t\t// then erase it and reposition the caret.\r\n\t\t\tif (caret > 0)\r\n\t\t\t{\r\n\t\t\t\t// Remove the previous character\r\n\t\t\t\tvalue = value.slice(0, caret - 1) + value.slice(caret)\r\n\t\t\t\t// Position the caret where the previous (erased) character was\r\n\t\t\t\tcaret--\r\n\t\t\t}\r\n\t\t\tbreak\r\n\r\n\t\tcase 'Delete':\r\n\t\t\t// Remove current digit (if any)\r\n\t\t\tvalue = value.slice(0, caret) + value.slice(caret + 1)\r\n\t\t\tbreak\r\n\t}\r\n\r\n\treturn { value, caret }\r\n}", "// Parses the `text`.\r\n//\r\n// Returns `{ value, caret }` where `caret` is\r\n// the caret position inside `value`\r\n// corresponding to the `caret_position` inside `text`.\r\n//\r\n// The `text` is parsed by feeding each character sequentially to\r\n// `parse_character(character, value, context)` function\r\n// and appending the result (if it's not `undefined`) to `value`.\r\n//\r\n// `context` argument is just a utility empty object that is shared within the bounds\r\n// of parsing a single input string. The `_parse()` function could use that object\r\n// to store any kind of \"flags\" in it in order to alter its behavior based when\r\n// parsing next characters within the same string. Or it could completely ignore it.\r\n//\r\n// Example:\r\n//\r\n// `text` is `8 (800) 555-35-35`,\r\n// `caret_position` is `4` (before the first `0`).\r\n// `parse_character` is `(character, value) =>\r\n//   if (character >= '0' && character <= '9') { return character }`.\r\n//\r\n// then `parse()` outputs `{ value: '88005553535', caret: 2 }`.\r\n//\r\nexport default function parse(text, caret_position, parse_character)\r\n{\r\n\tconst context = {}\r\n\r\n\tlet value = ''\r\n\r\n\tlet focused_input_character_index = 0\r\n\r\n\tlet index = 0\r\n\twhile (index < text.length)\r\n\t{\r\n\t\tconst character = parse_character(text[index], value, context)\r\n\r\n\t\tif (character !== undefined)\r\n\t\t{\r\n\t\t\tvalue += character\r\n\r\n\t\t\tif (caret_position !== undefined)\r\n\t\t\t{\r\n\t\t\t\tif (caret_position === index)\r\n\t\t\t\t{\r\n\t\t\t\t\tfocused_input_character_index = value.length - 1;\r\n\t\t\t\t}\r\n\t\t\t\telse if (caret_position > index)\r\n\t\t\t\t{\r\n\t\t\t\t\tfocused_input_character_index = value.length\r\n\t\t\t\t}\r\n\t\t\t }\r\n\t\t}\r\n\r\n\t\tindex++\r\n\t}\r\n\r\n\t// If caret position wasn't specified\r\n\tif (caret_position === undefined)\r\n\t{\r\n\t\t// Then set caret position to \"after the last input character\"\r\n\t\tfocused_input_character_index = value.length\r\n\t}\r\n\r\n\tconst result =\r\n\t{\r\n\t\tvalue,\r\n\t\tcaret : focused_input_character_index\r\n\t}\r\n\r\n\treturn result\r\n}", "// Counts all occurences of a symbol in a string\r\nexport function count_occurences(symbol, string) {\r\n\tlet count = 0\r\n\t// Using `.split('')` here instead of normal `for ... of`\r\n\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t// (the ones consisting of four bytes)\r\n\t// but template placeholder characters don't fall into that range\r\n\t// so skipping such miscellaneous \"exotic\" characters\r\n\t// won't matter here for just counting placeholder character occurrences.\r\n\tfor (const character of string.split('')) {\r\n\t\tif (character === symbol) {\r\n\t\t\tcount++\r\n\t\t}\r\n\t}\r\n\treturn count\r\n}", "import { count_occurences } from './helpers.js'\r\n\r\nexport default function closeBraces(retained_template, template, placeholder = 'x', empty_placeholder = ' ')\r\n{\r\n\tlet cut_before = retained_template.length\r\n\r\n\tconst opening_braces = count_occurences('(', retained_template)\r\n\tconst closing_braces = count_occurences(')', retained_template)\r\n\r\n\tlet dangling_braces = opening_braces - closing_braces\r\n\r\n\twhile (dangling_braces > 0 && cut_before < template.length)\r\n\t{\r\n\t\tretained_template += template[cut_before].replace(placeholder, empty_placeholder)\r\n\r\n\t\tif (template[cut_before] === ')')\r\n\t\t{\r\n\t\t\tdangling_braces--\r\n\t\t}\r\n\r\n\t\tcut_before++\r\n\t}\r\n\r\n\treturn retained_template\r\n}\r\n", "import { count_occurences } from './helpers.js'\r\nimport close_braces from './closeBraces.js'\r\n\r\n// Takes a `template` where character placeholders\r\n// are denoted by 'x'es (e.g. 'x (xxx) xxx-xx-xx').\r\n//\r\n// Returns a function which takes `value` characters\r\n// and returns the `template` filled with those characters.\r\n// If the `template` can only be partially filled\r\n// then it is cut off.\r\n//\r\n// If `should_close_braces` is `true`,\r\n// then it will also make sure all dangling braces are closed,\r\n// e.g. \"8 (8\" -> \"8 (8  )\" (iPhone style phone number input).\r\n//\r\nexport default function(template, placeholder = 'x', should_close_braces)\r\n{\r\n\tif (!template)\r\n\t{\r\n\t\treturn value => ({ text: value })\r\n\t}\r\n\r\n\tconst characters_in_template = count_occurences(placeholder, template)\r\n\r\n\treturn function(value)\r\n\t{\r\n\t\tif (!value)\r\n\t\t{\r\n\t\t\treturn { text: '', template }\r\n\t\t}\r\n\r\n\t\tlet value_character_index = 0\r\n\t\tlet filled_in_template = ''\r\n\r\n\t\t// Using `.split('')` here instead of normal `for ... of`\r\n\t\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t\t// (the ones consisting of four bytes)\r\n\t\t// but template placeholder characters don't fall into that range\r\n\t\t// and appending UTF-8 characters to a string in parts still works.\r\n\t\tfor (const character of template.split(''))\r\n\t\t{\r\n\t\t\tif (character !== placeholder)\r\n\t\t\t{\r\n\t\t\t\tfilled_in_template += character\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\r\n\t\t\tfilled_in_template += value[value_character_index]\r\n\t\t\tvalue_character_index++\r\n\r\n\t\t\t// If the last available value character has been filled in,\r\n\t\t\t// then return the filled in template\r\n\t\t\t// (either trim the right part or retain it,\r\n\t\t\t//  if no more character placeholders in there)\r\n\t\t\tif (value_character_index === value.length)\r\n\t\t\t{\r\n\t\t\t\t// If there are more character placeholders\r\n\t\t\t\t// in the right part of the template\r\n\t\t\t\t// then simply trim it.\r\n\t\t\t\tif (value.length < characters_in_template)\r\n\t\t\t\t{\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (should_close_braces)\r\n\t\t{\r\n\t\t\tfilled_in_template = close_braces(filled_in_template, template)\r\n\t\t}\r\n\r\n\t\treturn { text: filled_in_template, template }\r\n\t}\r\n}", "import template_formatter from './templateFormatter.js'\r\n\r\n// Formats `value` value preserving `caret` at the same character.\r\n//\r\n// `{ value, caret }` attribute is the result of `parse()` function call.\r\n//\r\n// Returns `{ text, caret }` where the new `caret` is the caret position\r\n// inside `text` text corresponding to the original `caret` position inside `value`.\r\n//\r\n// `formatter(value)` is a function returning `{ text, template }`.\r\n//\r\n// `text` is the `value` value formatted using `template`.\r\n// It may either cut off the non-filled right part of the `template`\r\n// or it may fill the non-filled character placeholders\r\n// in the right part of the `template` with `spacer`\r\n// which is a space (' ') character by default.\r\n//\r\n// `template` is the template used to format the `value`.\r\n// It can be either a full-length template or a partial template.\r\n//\r\n// `formatter` can also be a string — a `template`\r\n// where character placeholders are denoted by 'x'es.\r\n// In this case `formatter` function is automatically created.\r\n//\r\n// Example:\r\n//\r\n// `value` is '880',\r\n// `caret` is `2` (before the first `0`)\r\n//\r\n// `formatter` is `'880' =>\r\n//   { text: '8 (80 )', template: 'x (xxx) xxx-xx-xx' }`\r\n//\r\n// The result is `{ text: '8 (80 )', caret: 4 }`.\r\n//\r\nexport default function format(value, caret, formatter)\r\n{\r\n\tif (typeof formatter === 'string')\r\n\t{\r\n\t\tformatter = template_formatter(formatter)\r\n\t}\r\n\r\n\tlet { text, template } = formatter(value) || {}\r\n\r\n\tif (text === undefined)\r\n\t{\r\n\t\t text = value\r\n\t}\r\n\r\n\tif (template)\r\n\t{\r\n\t\tif (caret === undefined)\r\n\t\t{\r\n\t\t\tcaret = text.length\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tlet index = 0\r\n\t\t\tlet found = false\r\n\r\n\t\t\tlet possibly_last_input_character_index = -1\r\n\r\n\t\t\twhile (index < text.length && index < template.length)\r\n\t\t\t{\r\n\t\t\t\t// Character placeholder found\r\n\t\t\t\tif (text[index] !== template[index])\r\n\t\t\t\t{\r\n\t\t\t\t\tif (caret === 0)\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tfound = true\r\n\t\t\t\t\t\tcaret = index\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tpossibly_last_input_character_index = index\r\n\r\n\t\t\t\t\tcaret--\r\n\t\t\t\t}\r\n\r\n\t\t\t\tindex++\r\n\t\t\t}\r\n\r\n\t\t\t// If the caret was positioned after last input character,\r\n\t\t\t// then the text caret index is just after the last input character.\r\n\t\t\tif (!found)\r\n\t\t\t{\r\n\t\t\t\tcaret = possibly_last_input_character_index + 1\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn { text, caret }\r\n}", "export function isReadOnly(element)\r\n{\r\n\treturn element.hasAttribute('readonly')\r\n}\r\n\r\n// Gets <input/> selection bounds\r\nexport function getSelection(element)\r\n{\r\n\t// If no selection, return nothing\r\n\tif (element.selectionStart === element.selectionEnd)\r\n\t{\r\n\t\treturn\r\n\t}\r\n\r\n\treturn { start: element.selectionStart, end: element.selectionEnd }\r\n}\r\n\r\n// Key codes\r\nexport const Keys =\r\n{\r\n\tBackspace : 8,\r\n\tDelete    : 46\r\n}\r\n\r\n// Finds out the operation to be intercepted and performed\r\n// based on the key down event `keyCode`.\r\nexport function getOperation(event)\r\n{\r\n\tswitch (event.keyCode)\r\n\t{\r\n\t\tcase Keys.Backspace:\r\n\t\t\treturn 'Backspace'\r\n\r\n\t\tcase Keys.Delete:\r\n\t\t\treturn 'Delete'\r\n\t}\r\n}\r\n\r\n// Gets <input/> caret position\r\nexport function getCaretPosition(element)\r\n{\r\n\treturn element.selectionStart\r\n}\r\n\r\n// Sets <input/> caret position\r\nexport function setCaretPosition(element, caret_position)\r\n{\r\n\t// Sanity check\r\n\tif (caret_position === undefined)\r\n\t{\r\n\t\treturn\r\n\t}\r\n\r\n\t// Set caret position.\r\n\t// There has been an issue with caret positioning on Android devices.\r\n\t// https://github.com/catamphetamine/input-format/issues/2\r\n\t// I was revisiting this issue and looked for similar issues in other libraries.\r\n\t// For example, there's [`text-mask`](https://github.com/text-mask/text-mask) library.\r\n\t// They've had exactly the same issue when the caret seemingly refused to be repositioned programmatically.\r\n\t// The symptoms were the same: whenever the caret passed through a non-digit character of a mask (a whitespace, a bracket, a dash, etc), it looked as if it placed itself one character before its correct position.\r\n\t// https://github.com/text-mask/text-mask/issues/300\r\n\t// They seem to have found a basic fix for it: calling `input.setSelectionRange()` in a timeout rather than instantly for Android devices.\r\n\t// https://github.com/text-mask/text-mask/pull/400/files\r\n\t// I've implemented the same workaround here.\r\n\tif (isAndroid()) {\r\n      setTimeout(() => element.setSelectionRange(caret_position, caret_position), 0)\r\n\t} else {\r\n\t\telement.setSelectionRange(caret_position, caret_position)\r\n\t}\r\n}\r\n\r\nfunction isAndroid() {\r\n\t// `navigator` is not defined when running mocha tests.\r\n\tif (typeof navigator !== 'undefined') {\r\n\t\treturn ANDROID_USER_AGENT_REG_EXP.test(navigator.userAgent)\r\n\t}\r\n}\r\n\r\nconst ANDROID_USER_AGENT_REG_EXP = /Android/i", "import edit   from './edit.js'\r\nimport parse  from './parse.js'\r\nimport format from './format.js'\r\n\r\nimport\r\n{\r\n\tisReadOnly,\r\n\tgetOperation,\r\n\tgetSelection,\r\n\tgetCaretPosition,\r\n\tsetCaretPosition\r\n}\r\nfrom './dom.js'\r\n\r\n// Deprecated.\r\n// I don't know why this function exists.\r\nexport function onCut(event, input, _parse, _format, on_change)\r\n{\r\n\tif (isReadOnly(input)) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// The actual cut hasn't happened just yet hence the timeout.\r\n\tsetTimeout(() => formatInputText(input, _parse, _format, undefined, on_change), 0)\r\n}\r\n\r\n// Deprecated.\r\n// I don't know why this function exists.\r\nexport function onPaste(event, input, _parse, _format, on_change)\r\n{\r\n\tif (isReadOnly(input)) {\r\n\t\treturn\r\n\t}\r\n\r\n\tconst selection = getSelection(input)\r\n\r\n\t// If selection is made,\r\n\t// just erase the selected text\r\n\t// prior to pasting\r\n\tif (selection)\r\n\t{\r\n\t\teraseSelection(input, selection)\r\n\t}\r\n\r\n\tformatInputText(input, _parse, _format, undefined, on_change)\r\n}\r\n\r\nexport function onChange(event, input, _parse, _format, on_change)\r\n{\r\n\tformatInputText(input, _parse, _format, undefined, on_change)\r\n}\r\n\r\n// \"Delete\" and \"Backspace\" keys are special\r\n// in a way that they're not handled by the regular `onChange()` handler\r\n// and instead are intercepted and re-applied manually.\r\n// The reason is that normally hitting \"Backspace\" or \"Delete\"\r\n// results in erasing a character, but that character might be any character,\r\n// while it would be a better \"user experience\" if it erased not just any character\r\n// but the closest \"meaningful\" character.\r\n// For example, if a template is `(xxx) xxx-xxxx`,\r\n// and the `<input/>` value is `(111) 222-3333`,\r\n// then, if a user begins erasing the `3333` part via \"Backspace\"\r\n// and reaches the \"-\" character, then it would just erase the \"-\" character.\r\n// Nothing wrong with that, but it would be a better \"user experience\"\r\n// if hitting \"Backspace\" at that position would erase the closest \"meaningful\"\r\n// character, which would be the rightmost `2`.\r\n// So, what this `onKeyDown()` handler does is it intercepts\r\n// \"Backspace\" and \"Delete\" keys and re-applies those operations manually\r\n// following the logic described above.\r\nexport function onKeyDown(event, input, _parse, _format, on_change)\r\n{\r\n\tif (isReadOnly(input)) {\r\n\t\treturn\r\n\t}\r\n\r\n\tconst operation = getOperation(event)\r\n\tswitch (operation)\r\n\t{\r\n\t\tcase 'Delete':\r\n\t\tcase 'Backspace':\r\n\t\t\t// Intercept this operation and perform it manually.\r\n\t\t\tevent.preventDefault()\r\n\r\n\t\t\tconst selection = getSelection(input)\r\n\r\n\t\t\t// If a selection is made, just erase the selected text.\r\n\t\t\tif (selection)\r\n\t\t\t{\r\n\t\t\t\teraseSelection(input, selection)\r\n\t\t\t\treturn formatInputText(input, _parse, _format, undefined, on_change)\r\n\t\t\t}\r\n\r\n\t\t\t// Else, perform the (character erasing) operation manually.\r\n\t\t\treturn formatInputText(input, _parse, _format, operation, on_change)\r\n\r\n\t\tdefault:\r\n\t\t\t// Will be handled normally as part of the `onChange` handler.\r\n\t}\r\n}\r\n\r\n/**\r\n * Erases the selected text inside an `<input/>`.\r\n * @param  {DOMElement} input\r\n * @param  {Selection} selection\r\n */\r\nfunction eraseSelection(input, selection)\r\n{\r\n\tlet text = input.value\r\n\ttext = text.slice(0, selection.start) + text.slice(selection.end)\r\n\r\n\tinput.value = text\r\n\tsetCaretPosition(input, selection.start)\r\n}\r\n\r\n/**\r\n * Parses and re-formats `<input/>` textual value.\r\n * E.g. when a user enters something into the `<input/>`\r\n * that raw input must first be parsed and the re-formatted properly.\r\n * Is called either after some user input (e.g. entered a character, pasted something)\r\n * or after the user performed an `operation` (e.g. \"Backspace\", \"Delete\").\r\n * @param  {DOMElement} input\r\n * @param  {Function} parse\r\n * @param  {Function} format\r\n * @param  {string} [operation] - The operation that triggered `<input/>` textual value change. E.g. \"Backspace\", \"Delete\".\r\n * @param  {Function} onChange\r\n */\r\nfunction formatInputText(input, _parse, _format, operation, on_change)\r\n{\r\n\t// Parse `<input/>` textual value.\r\n\t// Get the `value` and `caret` position.\r\n\tlet { value, caret } = parse(input.value, getCaretPosition(input), _parse)\r\n\r\n\t// If a user performed an operation (\"Backspace\", \"Delete\")\r\n\t// then apply that operation and get the new `value` and `caret` position.\r\n\tif (operation)\r\n\t{\r\n\t\tconst newValueAndCaret = edit(value, caret, operation)\r\n\r\n\t\tvalue = newValueAndCaret.value\r\n\t\tcaret = newValueAndCaret.caret\r\n\t}\r\n\r\n\t// Format the `value`.\r\n\t// (and reposition the caret accordingly)\r\n\tconst formatted = format(value, caret, _format)\r\n\r\n\tconst text = formatted.text\r\n\tcaret      = formatted.caret\r\n\r\n\t// Set `<input/>` textual value manually\r\n\t// to prevent React from resetting the caret position\r\n\t// later inside a subsequent `render()`.\r\n\t// Doesn't work for custom `inputComponent`s for some reason.\r\n\tinput.value = text\r\n\t// Position the caret properly.\r\n\tsetCaretPosition(input, caret)\r\n\r\n\t// If the `<input/>` textual value did change,\r\n\t// then the parsed `value` may have changed too.\r\n\ton_change(value)\r\n}", "import { getCountryCallingCode } from 'libphonenumber-js/core'\r\n\r\nexport function getInputValuePrefix({\r\n\tcountry,\r\n\tinternational,\r\n\twithCountryCallingCode,\r\n\tmetadata\r\n}) {\r\n\treturn country && international && !withCountryCallingCode ?\r\n\t\t`+${getCountryCallingCode(country, metadata)}` :\r\n\t\t''\r\n}\r\n\r\nexport function removeInputValuePrefix(value, prefix) {\r\n\tif (prefix) {\r\n\t\tvalue = value.slice(prefix.length)\r\n\t\tif (value[0] === ' ') {\r\n\t\t\tvalue = value.slice(1)\r\n\t\t}\r\n\t}\r\n\treturn value\r\n}", "import { parsePhone<PERSON>umberCharacter } from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Parses next character while parsing phone number digits (including a `+`)\r\n * from text: discards everything except `+` and digits, and `+` is only allowed\r\n * at the start of a phone number.\r\n * For example, is used in `react-phone-number-input` where it uses\r\n * [`input-format`](https://gitlab.com/catamphetamine/input-format).\r\n * @param  {string} character - Yet another character from raw input string.\r\n * @param  {string?} prevParsedCharacters - Previous parsed characters.\r\n * @param  {object?} context - An optional object that could be used by this function to set arbitrary \"flags\". The object should be shared within the parsing of the whole string.\r\n * @return {string?} The parsed character.\r\n */\r\nexport default function parsePhoneNumber<PERSON>haracter_(character, prevParsedCharacters, context) {\r\n\t// `context` argument was added as a third argument of `parse()` function\r\n\t// in `input-format` package on Dec 26th, 2023. So it could potentially be\r\n\t// `undefined` here if a 3rd-party app somehow ends up with this newer version\r\n\t// of `react-phone-number-input` and an older version of `input-format`.\r\n\t// Dunno how, but just in case, it could be `undefined` here and it wouldn't break.\r\n\t// Maybe it's not required to handle `undefined` case here.\r\n\t//\r\n\t// The addition of the `context` argument was to fix the slightly-weird behavior\r\n\t// of parsing an input string when the user inputs something like `\"2+7\"\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/437\r\n\t//\r\n\t// If the parser encounters an unexpected `+` in a string being parsed\r\n\t// then it simply discards that out-of-place `+` and any following characters.\r\n\t//\r\n\tif (context && context.ignoreRest) {\r\n\t\treturn\r\n\t}\r\n\r\n\tconst emitEvent = (eventName) => {\r\n\t\tif (context) {\r\n\t\t\tswitch (eventName) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tcontext.ignoreRest = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn parsePhoneNumberCharacter(character, prevParsedCharacters, emitEvent)\r\n}", "import { useCallback } from 'react'\r\n\r\n// Returns a custom `onKeyDown` handler that works around a Backspace keypress edge case:\r\n// * `<PhoneInputWithCountrySelect international countryCallingCodeEditable={false}/>`\r\n// * When placing the caret before the leading plus character and pressing Backspace,\r\n//   it duplicates the country calling code in the `<input/>`.\r\n// https://github.com/catamphetamine/react-phone-number-input/issues/442\r\nexport default function useInputKeyDownHandler({\r\n\tonKeyDown,\r\n\tinternational\r\n}) {\r\n\treturn useCallback((event) => {\r\n\t\tif (event.keyCode === BACKSPACE_KEY_CODE && international) {\r\n\t\t\t// It checks `event.target` here for being an `<input/>` element\r\n\t\t\t// because \"keydown\" events may bubble from arbitrary child elements\r\n\t\t\t// so there's no guarantee that `event.target` represents an `<input/>` element.\r\n\t\t\t// Also, since `inputComponent` is not neceesarily an `<input/>`, this check is required too.\r\n\t\t\tif (event.target instanceof HTMLInputElement) {\r\n\t\t\t\tif (getCaretPosition(event.target) === AFTER_LEADING_PLUS_CARET_POSITION) {\r\n\t\t\t\t\tevent.preventDefault()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (onKeyDown) {\r\n\t\t\tonKeyDown(event)\r\n\t\t}\r\n\t}, [\r\n\t\tonKeyDown,\r\n\t\tinternational\r\n\t])\r\n}\r\n\r\nconst BACKSPACE_KEY_CODE = 8\r\n\r\n// Gets the caret position in an `<input/>` field.\r\n// The caret position starts with `0` which means \"before the first character\".\r\nfunction getCaretPosition(element) {\r\n\treturn element.selectionStart\r\n}\r\n\r\nconst AFTER_LEADING_PLUS_CARET_POSITION = '+'.length", "import React, { useCallback } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport { parseIncompletePhoneNumber, formatIncompletePhoneNumber } from 'libphonenumber-js/core'\r\n\r\nimport { getInputValuePrefix, removeInputValuePrefix } from './helpers/inputValuePrefix.js'\r\n\r\nimport useInputKeyDownHandler from './useInputKeyDownHandler.js'\r\n\r\nexport function createInput(defaultMetadata) {\r\n\t/**\r\n\t * `InputBasic` is the most basic implementation of a `Component`\r\n\t * that can be passed to `<PhoneInput/>`. It parses and formats\r\n\t * the user's input but doesn't control the caret in the process:\r\n\t * when erasing or inserting digits in the middle of a phone number\r\n\t * the caret usually jumps to the end (this is the expected behavior).\r\n\t * Why does `InputBasic` exist when there's `InputSmart`?\r\n\t * One reason is working around the [Samsung Galaxy smart caret positioning bug]\r\n\t * (https://github.com/catamphetamine/react-phone-number-input/issues/75).\r\n\t * Another reason is that, unlike `InputSmart`, it doesn't require DOM environment.\r\n\t */\r\n\tfunction InputBasic({\r\n\t\tvalue,\r\n\t\tonChange,\r\n\t\tonKeyDown,\r\n\t\tcountry,\r\n\t\tinternational,\r\n\t\twithCountryCallingCode,\r\n\t\tmetadata = defaultMetadata,\r\n\t\tinputComponent: Input = 'input',\r\n\t\t...rest\r\n\t}, ref) {\r\n\t\tconst prefix = getInputValuePrefix({\r\n\t\t\tcountry,\r\n\t\t\tinternational,\r\n\t\t\twithCountryCallingCode,\r\n\t\t\tmetadata\r\n\t\t})\r\n\r\n\t\tconst _onChange = useCallback((event) => {\r\n\t\t\tlet newValue = parseIncompletePhoneNumber(event.target.value)\r\n\t\t\t// By default, if a value is something like `\"(123)\"`\r\n\t\t\t// then Backspace would only erase the rightmost brace\r\n\t\t\t// becoming something like `\"(123\"`\r\n\t\t\t// which would give the same `\"123\"` value\r\n\t\t\t// which would then be formatted back to `\"(123)\"`\r\n\t\t\t// and so a user wouldn't be able to erase the phone number.\r\n\t\t\t//\r\n\t\t\t// This issue is worked around with this simple hack:\r\n\t\t\t// when \"old\" and \"new\" parsed values are the same,\r\n\t\t\t// it checks if the \"new\" formatted value could be obtained\r\n\t\t\t// from the \"old\" formatted value by erasing some (or no) characters at the right side.\r\n\t\t\t// If it could then it's likely that the user has hit a Backspace key\r\n\t\t\t// and what they really intended was to erase a rightmost digit rather than\r\n\t\t\t// a rightmost punctuation character.\r\n\t\t\t//\r\n\t\t\tif (newValue === value) {\r\n\t\t\t\tconst newValueFormatted = format(prefix, newValue, country, metadata)\r\n\t\t\t\tif (newValueFormatted.indexOf(event.target.value) === 0) {\r\n\t\t\t\t\t// Trim the last digit (or plus sign).\r\n\t\t\t\t\tnewValue = newValue.slice(0, -1)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tonChange(newValue)\r\n\t\t}, [\r\n\t\t\tprefix,\r\n\t\t\tvalue,\r\n\t\t\tonChange,\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t])\r\n\r\n\t\tconst _onKeyDown = useInputKeyDownHandler({\r\n\t\t\tonKeyDown,\r\n\t\t\tinternational\r\n\t\t})\r\n\r\n\t\treturn (\r\n\t\t\t<Input\r\n\t\t\t\t{...rest}\r\n\t\t\t\tref={ref}\r\n\t\t\t\tvalue={format(prefix, value, country, metadata)}\r\n\t\t\t\tonChange={_onChange}\r\n\t\t\t\tonKeyDown={_onKeyDown}/>\r\n\t\t)\r\n\t}\r\n\r\n\tInputBasic = React.forwardRef(InputBasic)\r\n\r\n\tInputBasic.propTypes = {\r\n\t\t/**\r\n\t\t * The parsed phone number.\r\n\t\t * \"Parsed\" not in a sense of \"E.164\"\r\n\t\t * but rather in a sense of \"having only\r\n\t\t * digits and possibly a leading plus character\".\r\n\t\t * Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n\t\t */\r\n\t\tvalue: PropTypes.string.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `value: string`.\r\n\t\t * Updates the `value` property.\r\n\t\t */\r\n\t\tonChange: PropTypes.func.isRequired,\r\n\r\n\t\t/**\r\n\t\t * A function of `event: Event`.\r\n\t\t * Handles `keydown` events.\r\n\t\t */\r\n\t\tonKeyDown: PropTypes.func,\r\n\r\n\t\t/**\r\n\t\t * A two-letter country code for formatting `value`\r\n\t\t * as a national phone number (e.g. `(800) 555 35 35`).\r\n\t\t * E.g. \"US\", \"RU\", etc.\r\n\t\t * If no `country` is passed then `value`\r\n\t\t * is formatted as an international phone number.\r\n\t\t * (e.g. `****** 555 35 35`)\r\n\t\t * Perhaps the `country` property should have been called `defaultCountry`\r\n\t\t * because if `value` is an international number then `country` is ignored.\r\n\t\t */\r\n\t\tcountry : PropTypes.string,\r\n\r\n\t\t/**\r\n\t\t * If `country` property is passed along with `international={true}` property\r\n\t\t * then the phone number will be input in \"international\" format for that `country`\r\n\t\t * (without \"country calling code\").\r\n\t\t * For example, if `country=\"US\"` property is passed to \"without country select\" input\r\n\t\t * then the phone number will be input in the \"national\" format for `US` (`(*************`).\r\n\t\t * But if both `country=\"US\"` and `international={true}` properties are passed then\r\n\t\t * the phone number will be input in the \"international\" format for `US` (`************`)\r\n\t\t * (without \"country calling code\" `+1`).\r\n\t\t */\r\n\t\tinternational: PropTypes.bool,\r\n\r\n\t\t/**\r\n\t\t * If `country` and `international` properties are set,\r\n\t\t * then by default it won't include \"country calling code\" in the input field.\r\n\t\t * To change that, pass `withCountryCallingCode` property,\r\n\t\t * and it will include \"country calling code\" in the input field.\r\n\t\t */\r\n\t\twithCountryCallingCode: PropTypes.bool,\r\n\r\n\t\t/**\r\n\t\t * `libphonenumber-js` metadata.\r\n\t\t */\r\n\t\tmetadata: PropTypes.object,\r\n\r\n\t\t/**\r\n\t\t * The `<input/>` component.\r\n\t\t */\r\n\t\tinputComponent: PropTypes.elementType\r\n\t}\r\n\r\n\treturn InputBasic\r\n}\r\n\r\nexport default createInput()\r\n\r\nfunction format(prefix, value, country, metadata) {\r\n\treturn removeInputValuePrefix(\r\n\t\tformatIncompletePhoneNumber(\r\n\t\t\tprefix + value,\r\n\t\t\tcountry,\r\n\t\t\tmetadata\r\n\t\t),\r\n\t\tprefix\r\n\t)\r\n}", "import React, { useCallback, useMemo } from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\nimport getUnicodeFlagIcon from 'country-flag-icons/unicode'\r\n\r\nexport default function CountrySelect({\r\n\tvalue,\r\n\tonChange,\r\n\toptions,\r\n\tdisabled,\r\n\treadOnly,\r\n\t...rest\r\n}) {\r\n\tconst onChange_ = useCallback((event) => {\r\n\t\tconst value = event.target.value\r\n\t\tonChange(value === 'ZZ' ? undefined : value)\r\n\t}, [onChange])\r\n\r\n\tconst selectedOption = useMemo(() => {\r\n\t\treturn getSelectedOption(options, value)\r\n\t}, [options, value])\r\n\r\n\t// \"ZZ\" means \"International\".\r\n\t// (HTML requires each `<option/>` have some string `value`).\r\n\treturn (\r\n\t\t<select\r\n\t\t\t{...rest}\r\n\t\t\tdisabled={disabled || readOnly}\r\n\t\t\treadOnly={readOnly}\r\n\t\t\tvalue={value || 'ZZ'}\r\n\t\t\tonChange={onChange_}>\r\n\t\t\t{options.map(({ value, label, divider }) => (\r\n\t\t\t\t<option\r\n\t\t\t\t\tkey={divider ? '|' : value || 'ZZ'}\r\n\t\t\t\t\tvalue={divider ? '|' : value || 'ZZ'}\r\n\t\t\t\t\tdisabled={divider ? true : false}\r\n\t\t\t\t\tstyle={divider ? DIVIDER_STYLE : undefined}>\r\n\t\t\t\t\t{label}\r\n\t\t\t\t</option>\r\n\t\t\t))}\r\n\t\t</select>\r\n\t)\r\n}\r\n\r\nCountrySelect.propTypes = {\r\n\t/**\r\n\t * A two-letter country code.\r\n\t * Example: \"US\", \"RU\", etc.\r\n\t */\r\n\tvalue: PropTypes.string,\r\n\r\n\t/**\r\n\t * A function of `value: string`.\r\n\t * Updates the `value` property.\r\n\t */\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t// `<select/>` options.\r\n\toptions: PropTypes.arrayOf(PropTypes.shape({\r\n\t\tvalue: PropTypes.string,\r\n\t\tlabel: PropTypes.string,\r\n\t\tdivider: PropTypes.bool\r\n\t})).isRequired,\r\n\r\n\t// `readonly` attribute doesn't work on a `<select/>`.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/419#issuecomment-1764384480\r\n\t// https://www.delftstack.com/howto/html/html-select-readonly/\r\n\t// To work around that, if `readOnly: true` property is passed\r\n\t// to this component, it behaves analogous to `disabled: true`.\r\n\tdisabled: PropTypes.bool,\r\n\treadOnly: PropTypes.bool\r\n}\r\n\r\nconst DIVIDER_STYLE = {\r\n\tfontSize: '1px',\r\n\tbackgroundColor: 'currentColor',\r\n\tcolor: 'inherit'\r\n}\r\n\r\nexport function CountrySelectWithIcon({\r\n\tvalue,\r\n\toptions,\r\n\tclassName,\r\n\ticonComponent: Icon,\r\n\tgetIconAspectRatio,\r\n\tarrowComponent: Arrow = DefaultArrowComponent,\r\n\tunicodeFlags,\r\n\t...rest\r\n}) {\r\n\tconst selectedOption = useMemo(() => {\r\n\t\treturn getSelectedOption(options, value)\r\n\t}, [options, value])\r\n\r\n\treturn (\r\n\t\t<div className=\"PhoneInputCountry\">\r\n\t\t\t<CountrySelect\r\n\t\t\t\t{...rest}\r\n\t\t\t\tvalue={value}\r\n\t\t\t\toptions={options}\r\n\t\t\t\tclassName={classNames('PhoneInputCountrySelect', className)}/>\r\n\r\n\t\t\t{/* Either a Unicode flag icon. */}\r\n\t\t\t{(unicodeFlags && value) &&\r\n\t\t\t\t<div className=\"PhoneInputCountryIconUnicode\">\r\n\t\t\t\t\t{getUnicodeFlagIcon(value)}\r\n\t\t\t\t</div>\r\n\t\t\t}\r\n\r\n\t\t\t{/* Or an SVG flag icon. */}\r\n\t\t\t{!(unicodeFlags && value) &&\r\n\t\t\t\t<Icon\r\n\t\t\t\t\taria-hidden\r\n\t\t\t\t\tcountry={value}\r\n\t\t\t\t\tlabel={selectedOption && selectedOption.label}\r\n\t\t\t\t\taspectRatio={unicodeFlags ? 1 : undefined}/>\r\n\t\t\t}\r\n\r\n\t\t\t<Arrow/>\r\n\t\t</div>\r\n\t)\r\n}\r\n\r\nCountrySelectWithIcon.propTypes = {\r\n\t// Country flag component.\r\n\ticonComponent: PropTypes.elementType,\r\n\r\n\t// Select arrow component.\r\n\tarrowComponent: PropTypes.elementType,\r\n\r\n\t// Set to `true` to render Unicode flag icons instead of SVG images.\r\n\tunicodeFlags: PropTypes.bool\r\n}\r\n\r\nfunction DefaultArrowComponent() {\r\n\treturn <div className=\"PhoneInputCountrySelectArrow\"/>\r\n}\r\n\r\nfunction getSelectedOption(options, value) {\r\n\tfor (const option of options) {\r\n\t\tif (!option.divider && option.value === value) {\r\n\t\t\treturn option\r\n\t\t}\r\n\t}\r\n}", "/**\r\n * Creates Unicode flag from a two-letter ISO country code.\r\n * https://stackoverflow.com/questions/24050671/how-to-put-japan-flag-character-in-a-string\r\n * @param  {string} country — A two-letter ISO country code (case-insensitive).\r\n * @return {string}\r\n */\r\nexport default function getCountryFlag(country) {\r\n\treturn getRegionalIndicatorSymbol(country[0]) + getRegionalIndicatorSymbol(country[1])\r\n}\r\n\r\n/**\r\n * Converts a letter to a Regional Indicator Symbol.\r\n * @param  {string} letter\r\n * @return {string}\r\n */\r\nfunction getRegionalIndicatorSymbol(letter) {\r\n\treturn String.fromCodePoint(0x1F1E6 - 65 + letter.toUpperCase().charCodeAt(0))\r\n}", "import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\n// Default country flag icon.\r\n// `<img/>` is wrapped in a `<div/>` to prevent SVGs from exploding in size in IE 11.\r\n// https://github.com/catamphetamine/react-phone-number-input/issues/111\r\nexport default function FlagComponent({\r\n\tcountry,\r\n\tcountryName,\r\n\tflags,\r\n\tflagUrl,\r\n\t...rest\r\n}) {\r\n\tif (flags && flags[country]) {\r\n\t\treturn flags[country]({ title: countryName })\r\n\t}\r\n\treturn (\r\n\t\t<img\r\n\t\t\t{...rest}\r\n\t\t\talt={countryName}\r\n\t\t\trole={countryName ? undefined : \"presentation\"}\r\n\t\t\tsrc={flagUrl.replace('{XX}', country).replace('{xx}', country.toLowerCase())}/>\r\n\t)\r\n}\r\n\r\nFlagComponent.propTypes = {\r\n\t// The country to be selected by default.\r\n\t// Two-letter country code (\"ISO 3166-1 alpha-2\").\r\n\tcountry: PropTypes.string.isRequired,\r\n\r\n\t// Will be HTML `title` attribute of the `<img/>`.\r\n\tcountryName: PropTypes.string.isRequired,\r\n\r\n\t// Country flag icon components.\r\n\t// By default flag icons are inserted as `<img/>`s\r\n\t// with their `src` pointed to `country-flag-icons` gitlab pages website.\r\n\t// There might be cases (e.g. an offline application)\r\n\t// where having a large (3 megabyte) `<svg/>` flags\r\n\t// bundle is more appropriate.\r\n\t// `import flags from 'react-phone-number-input/flags'`.\r\n\tflags: PropTypes.objectOf(PropTypes.elementType),\r\n\r\n\t// A URL for a country flag icon.\r\n\t// By default it points to `country-flag-icons` gitlab pages website.\r\n\tflagUrl: PropTypes.string.isRequired\r\n}\r\n", "import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nexport default function InternationalIcon({ aspectRatio, ...rest }) {\r\n\tif (aspectRatio === 1) {\r\n\t\treturn <InternationalIcon1x1 {...rest}/>\r\n\t} else {\r\n\t\treturn <InternationalIcon3x2 {...rest}/>\r\n\t}\r\n}\r\n\r\nInternationalIcon.propTypes = {\r\n\ttitle: PropTypes.string.isRequired,\r\n\taspectRatio: PropTypes.number\r\n}\r\n\r\n// 3x2.\r\n// Using `<title/>` in `<svg/>`s:\r\n// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title\r\nfunction InternationalIcon3x2({ title, ...rest }) {\r\n\treturn (\r\n\t\t<svg\r\n\t\t\t{...rest}\r\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\tviewBox=\"0 0 75 50\">\r\n\t\t\t<title>{title}</title>\r\n\t\t\t<g\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconGlobe\"\r\n\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\tfill=\"none\"\r\n\t\t\t\tstrokeWidth=\"2\"\r\n\t\t\t\tstrokeMiterlimit=\"10\">\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M47.2,36.1C48.1,36,49,36,50,36c7.4,0,14,1.7,18.5,4.3\"/>\r\n\t\t\t\t<path d=\"M68.6,9.6C64.2,12.3,57.5,14,50,14c-7.4,0-14-1.7-18.5-4.3\"/>\r\n\t\t\t\t<line x1=\"26\" y1=\"25\" x2=\"74\" y2=\"25\"/>\r\n\t\t\t\t<line x1=\"50\" y1=\"1\" x2=\"50\" y2=\"49\"/>\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M46.3,48.7c1.2,0.2,2.5,0.3,3.7,0.3c13.3,0,24-10.7,24-24S63.3,1,50,1S26,11.7,26,25c0,2,0.3,3.9,0.7,5.8\"/>\r\n\t\t\t\t<path strokeLinecap=\"round\" d=\"M46.8,48.2c1,0.6,2.1,0.8,3.2,0.8c6.6,0,12-10.7,12-24S56.6,1,50,1S38,11.7,38,25c0,1.4,0.1,2.7,0.2,4c0,0.1,0,0.2,0,0.2\"/>\r\n\t\t\t</g>\r\n\t\t\t<path\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconPhone\"\r\n\t\t\t\tstroke=\"none\"\r\n\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\td=\"M12.4,17.9c2.9-2.9,5.4-4.8,0.3-11.2S4.1,5.2,1.3,8.1C-2,11.4,1.1,23.5,13.1,35.6s24.3,15.2,27.5,11.9c2.8-2.8,7.8-6.3,1.4-11.5s-8.3-2.6-11.2,0.3c-2,2-7.2-2.2-11.7-6.7S10.4,19.9,12.4,17.9z\"/>\r\n\t\t</svg>\r\n\t)\r\n}\r\n\r\nInternationalIcon3x2.propTypes = {\r\n\ttitle: PropTypes.string.isRequired\r\n}\r\n\r\n// 1x1.\r\n// Using `<title/>` in `<svg/>`s:\r\n// https://developer.mozilla.org/en-US/docs/Web/SVG/Element/title\r\nfunction InternationalIcon1x1({ title, ...rest }) {\r\n\treturn (\r\n\t\t<svg\r\n\t\t\t{...rest}\r\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\r\n\t\t\tviewBox=\"0 0 50 50\">\r\n\t\t\t<title>{title}</title>\r\n\t\t\t<g\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconGlobe\"\r\n\t\t\t\tstroke=\"currentColor\"\r\n\t\t\t\tfill=\"none\"\r\n\t\t\t\tstrokeWidth=\"2\"\r\n\t\t\t\tstrokeLinecap=\"round\">\r\n\t\t\t\t<path d=\"M8.45,13A21.44,21.44,0,1,1,37.08,41.56\"/>\r\n\t\t\t\t<path d=\"M19.36,35.47a36.9,36.9,0,0,1-2.28-13.24C17.08,10.39,21.88.85,27.8.85s10.72,9.54,10.72,21.38c0,6.48-1.44,12.28-3.71,16.21\"/>\r\n\t\t\t\t<path d=\"M17.41,33.4A39,39,0,0,1,27.8,32.06c6.62,0,12.55,1.5,16.48,3.86\"/>\r\n\t\t\t\t<path d=\"M44.29,8.53c-3.93,2.37-9.86,3.88-16.49,3.88S15.25,10.9,11.31,8.54\"/>\r\n\t\t\t\t<line x1=\"27.8\" y1=\"0.85\" x2=\"27.8\" y2=\"34.61\"/>\r\n\t\t\t\t<line x1=\"15.2\" y1=\"22.23\" x2=\"49.15\" y2=\"22.23\"/>\r\n\t\t\t</g>\r\n\t\t\t<path\r\n\t\t\t\tclassName=\"PhoneInputInternationalIconPhone\"\r\n\t\t\t\tstroke=\"transparent\"\r\n\t\t\t\tfill=\"currentColor\"\r\n\t\t\t\td=\"M9.42,26.64c2.22-2.22,4.15-3.59.22-8.49S3.08,17,.93,19.17c-2.49,2.48-.13,11.74,9,20.89s18.41,11.5,20.89,9c2.15-2.15,5.91-4.77,1-8.71s-6.27-2-8.49.22c-1.55,1.55-5.48-1.69-8.86-5.08S7.87,28.19,9.42,26.64Z\"/>\r\n\t\t</svg>\r\n\t)\r\n}\r\n\r\nInternationalIcon1x1.propTypes = {\r\n\ttitle: PropTypes.string.isRequired\r\n}\r\n", "// Tells if `value: string` is an `E.164` phone number.\r\n//\r\n// Returns a boolean.\r\n//\r\n// It doesn't validate that the minimum national (significant) number length\r\n// is at least 2 characters.\r\n//\r\nexport default function isE164Number(value) {\r\n\tif (value.length < 2) {\r\n\t\treturn false\r\n\t}\r\n\tif (value[0] !== '+') {\r\n\t\treturn false\r\n\t}\r\n\tlet i = 1\r\n\twhile (i < value.length) {\r\n\t\tconst character = value.charCodeAt(i)\r\n\t\tif (character >= 48 && character <= 57) {\r\n\t\t\t// Is a digit.\r\n\t\t} else {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n\r\nexport function validateE164Number(value) {\r\n\tif (!isE164Number(value)) {\r\n\t\tconsole.error('[react-phone-number-input] Expected the initial `value` to be a E.164 phone number. Got', value)\r\n\t}\r\n}", "// Ignores weird istanbul error: \"else path not taken\".\r\nimport { isSupportedCountry } from 'libphonenumber-js/core'\r\nexport { getCountries } from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Sorts country `<select/>` options.\r\n * Can move some country `<select/>` options\r\n * to the top of the list, for example.\r\n * @param  {object[]} countryOptions — Country `<select/>` options.\r\n * @param  {string[]} [countryOptionsOrder] — Country `<select/>` options order. Example: `[\"US\", \"CA\", \"AU\", \"|\", \"...\"]`.\r\n * @return {object[]}\r\n */\r\nexport function sortCountryOptions(options, order) {\r\n\tif (!order) {\r\n\t\treturn options\r\n\t}\r\n\tconst optionsOnTop = []\r\n\tconst optionsOnBottom = []\r\n\tlet appendTo = optionsOnTop\r\n\tfor (const element of order) {\r\n\t\tif (element === '|') {\r\n\t\t\tappendTo.push({ divider: true })\r\n\t\t} else if (element === '...' || element === '…') {\r\n\t\t\tappendTo = optionsOnBottom\r\n\t\t} else {\r\n\t\t\tlet countryCode\r\n\t\t\tif (element === '🌐') {\r\n\t\t\t\tcountryCode = undefined\r\n\t\t\t} else {\r\n\t\t\t\tcountryCode = element\r\n\t\t\t}\r\n\t\t\t// Find the position of the option.\r\n\t\t\tconst index = options.indexOf(options.filter(option => option.value === countryCode)[0])\r\n\t\t\t// Get the option.\r\n\t\t\tconst option = options[index]\r\n\t\t\t// Remove the option from its default position.\r\n\t\t\toptions.splice(index, 1)\r\n\t\t\t// Add the option on top.\r\n\t\t\tappendTo.push(option)\r\n\t\t}\r\n\t}\r\n\treturn optionsOnTop.concat(options).concat(optionsOnBottom)\r\n}\r\n\r\nexport function getSupportedCountryOptions(countryOptions, metadata) {\r\n\tif (countryOptions) {\r\n\t\tcountryOptions = countryOptions.filter((option) => {\r\n\t\t\tswitch (option) {\r\n\t\t\t\tcase '🌐':\r\n\t\t\t\tcase '|':\r\n\t\t\t\tcase '...':\r\n\t\t\t\tcase '…':\r\n\t\t\t\t\treturn true\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn isCountrySupportedWithError(option, metadata)\r\n\t\t\t}\r\n\t\t})\r\n\t\tif (countryOptions.length > 0) {\r\n\t\t\treturn countryOptions\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isCountrySupportedWithError(country, metadata) {\r\n\tif (isSupportedCountry(country, metadata)) {\r\n\t\treturn true\r\n\t} else {\r\n\t\tconsole.error(`Country not found: ${country}`)\r\n\t\treturn false\r\n\t}\r\n}\r\n\r\nexport function getSupportedCountries(countries, metadata) {\r\n\tif (countries) {\r\n\t\tcountries = countries.filter(country => isCountrySupportedWithError(country, metadata))\r\n\t\tif (countries.length === 0) {\r\n\t\t\tcountries = undefined\r\n\t\t}\r\n\t}\r\n\treturn countries\r\n}", "import React from 'react'\r\nimport PropTypes from 'prop-types'\r\nimport classNames from 'classnames'\r\n\r\nimport DefaultInternationalIcon from './InternationalIcon.js'\r\nimport Flag from './Flag.js'\r\n\r\nexport function createCountryIconComponent({\r\n\tflags,\r\n\tflagUrl,\r\n\tflagComponent: FlagComponent,\r\n\tinternationalIcon: InternationalIcon\r\n}) {\r\n\tfunction CountryIcon({\r\n\t\tcountry,\r\n\t\tlabel,\r\n\t\taspectRatio,\r\n\t\t...rest\r\n\t}) {\r\n\t\t// `aspectRatio` is currently a hack for the default \"International\" icon\r\n\t\t// to render it as a square when Unicode flag icons are used.\r\n\t\t// So `aspectRatio` property is only used with the default \"International\" icon.\r\n\t\tconst _aspectRatio = InternationalIcon === DefaultInternationalIcon ? aspectRatio : undefined\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\t{...rest}\r\n\t\t\t\tclassName={classNames('PhoneInputCountryIcon', {\r\n\t\t\t\t\t'PhoneInputCountryIcon--square': _aspectRatio === 1,\r\n\t\t\t\t\t'PhoneInputCountryIcon--border': country\r\n\t\t\t\t})}>\r\n\t\t\t\t{\r\n\t\t\t\t\tcountry\r\n\t\t\t\t\t?\r\n\t\t\t\t\t<FlagComponent\r\n\t\t\t\t\t\tcountry={country}\r\n\t\t\t\t\t\tcountryName={label}\r\n\t\t\t\t\t\tflags={flags}\r\n\t\t\t\t\t\tflagUrl={flagUrl}\r\n\t\t\t\t\t\tclassName=\"PhoneInputCountryIconImg\"/>\r\n\t\t\t\t\t:\r\n\t\t\t\t\t<InternationalIcon\r\n\t\t\t\t\t\ttitle={label}\r\n\t\t\t\t\t\taspectRatio={_aspectRatio}\r\n\t\t\t\t\t\tclassName=\"PhoneInputCountryIconImg\"/>\r\n\t\t\t\t}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n\r\n\tCountryIcon.propTypes = {\r\n\t\tcountry: PropTypes.string,\r\n\t\tlabel: PropTypes.string.isRequired,\r\n\t\taspectRatio: PropTypes.number\r\n\t}\r\n\r\n\treturn CountryIcon\r\n}\r\n\r\nexport default createCountryIconComponent({\r\n\t// Must be equal to `defaultProps.flagUrl` in `./PhoneInputWithCountry.js`.\r\n\tflagUrl: 'https://purecatamphetamine.github.io/country-flag-icons/3x2/{XX}.svg',\r\n\tflagComponent: Flag,\r\n\tinternationalIcon: DefaultInternationalIcon\r\n})", "import { useRef, useCallback } from 'react'\r\n\r\n/**\r\n * This hook creates an internal copy of a `ref`\r\n * and returns a new `ref`-alike setter function\r\n * that updates both `ref` and the internal copy of it.\r\n * That `ref`-alike setter function could then be passed\r\n * to child elements instead of the original `ref`.\r\n *\r\n * The internal copy of the `ref` can then be used to\r\n * call instance methods like `.focus()`, etc.\r\n *\r\n * One may ask: why create a copy of `ref` for \"internal\" use\r\n * when the code could use the original `ref` for that.\r\n * The answer is: the code would have to dance around the original `ref` anyway\r\n * to figure out whether it exists and to find out the internal implementation of it\r\n * in order to read its value correctly. This hook encapsulates all that \"boilerplate\" code.\r\n * The returned copy of the `ref` is guaranteed to exist and functions as a proper ref \"object\".\r\n * The returned `ref`-alike setter function must be used instead of the original `ref`\r\n * when passing it to child elements.\r\n *\r\n * @param  {(object|function)} [externalRef] — The original `ref` that may have any internal implementation and might not even exist.\r\n * @return {any[]} Returns an array of two elements: a copy of the `ref` for \"internal\" use and a `ref`-alike setter function that should be used in-place of the original `ref` when passing it to child elements.\r\n */\r\nexport default function useExternalRef(externalRef) {\r\n  // Create a copy of the original `ref` (which might not exist).\r\n  // Both refs will point to the same value.\r\n  const refCopy = useRef()\r\n\r\n  // Updates both `ref`s with the same `value`.\r\n  const refSetter = useCallback((value) => {\r\n    setRefsValue([externalRef, refCopy], value)\r\n  }, [\r\n    externalRef,\r\n    refCopy\r\n  ])\r\n\r\n  return [refCopy, refSetter]\r\n}\r\n\r\n// Sets the same `value` of all `ref`s.\r\n// Some of the `ref`s may not exist in which case they'll be skipped.\r\nexport function setRefsValue(refs, value) {\r\n  for (const ref of refs) {\r\n    if (ref) {\r\n      setRefValue(ref, value)\r\n    }\r\n  }\r\n}\r\n\r\n// Sets the value of a `ref`.\r\n// Before React Hooks were introduced, `ref`s used to be functions.\r\n// After React Hooks were introduces, `ref`s became objects with `.current` property.\r\n// This function sets a `ref`'s value regardless of its internal implementation,\r\n// so it supports both types of `ref`s.\r\nfunction setRefValue(ref, value) {\r\n  if (typeof ref === 'function') {\r\n    ref(value)\r\n  } else {\r\n    ref.current = value\r\n  }\r\n}", "import PropTypes from 'prop-types'\r\n\r\nexport const metadata = PropTypes.shape({\r\n\tcountry_calling_codes : PropTypes.object.isRequired,\r\n\tcountries : PropTypes.object.isRequired\r\n})\r\n\r\nexport const labels = PropTypes.objectOf(PropTypes.string)", "import {\r\n\tgetCountryCallingCode,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nconst ONLY_DIGITS_REGEXP = /^\\d+$/\r\n\r\nexport default function getInternationalPhoneNumberPrefix(country, metadata) {\r\n\t// Standard international phone number prefix: \"+\" and \"country calling code\".\r\n\tlet prefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t// \"Leading digits\" can't be used to rule out any countries.\r\n\t// So the \"pre-fill with leading digits on country selection\" feature had to be reverted.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10#note_1231042367\r\n\t// // Get \"leading digits\" for a phone number of the country.\r\n\t// // If there're \"leading digits\" then they can be part of the prefix too.\r\n\t// // https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/10\r\n\t// metadata = new Metadata(metadata)\r\n\t// metadata.selectNumberingPlan(country)\r\n\t// // \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t// // By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t// // condition for a phone number to belong to a country.\r\n\t// // The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t// // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t// const leadingDigits = metadata.numberingPlan.leadingDigits()\r\n\t// if (leadingDigits && ONLY_DIGITS_REGEXP.test(leadingDigits)) {\r\n\t// \tprefix += leadingDigits\r\n\t// }\r\n\r\n\treturn prefix\r\n}", "import parsePhoneNumber_, {\r\n\tgetCountryCallingCode,\r\n\tAsYouType,\r\n\tMetadata\r\n} from 'libphonenumber-js/core'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\n/**\r\n * Decides which country should be pre-selected\r\n * when the phone number input component is first mounted.\r\n * @param  {object?} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {string?} country - Pre-defined country (two-letter code).\r\n * @param  {string[]?} countries - A list of countries available.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string?}\r\n */\r\nexport function getPreSelectedCountry({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\tlet country\r\n\r\n\t// If can get country from E.164 phone number\r\n\t// then it overrides the `country` passed (or not passed).\r\n\tif (phoneNumber && phoneNumber.country) {\r\n\t\t// `country` will be left `undefined` in case of non-detection.\r\n\t\tcountry = phoneNumber.country\r\n\t} else if (defaultCountry) {\r\n\t\tif (!value || couldNumberBelongToCountry(value, defaultCountry, metadata)) {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// Only pre-select a country if it's in the available `countries` list.\r\n\tif (countries && countries.indexOf(country) < 0) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// If there will be no \"International\" option\r\n\t// then some `country` must be selected.\r\n\t// It will still be the wrong country though.\r\n\t// But still country `<select/>` can't be left in a broken state.\r\n\tif (!country && required && countries && countries.length > 0) {\r\n\t\tcountry = getAnyCountry()\r\n\t\t// noCountryMatchesTheNumber = true\r\n\t}\r\n\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Generates a sorted list of country `<select/>` options.\r\n * @param  {string[]} countries - A list of two-letter (\"ISO 3166-1 alpha-2\") country codes.\r\n * @param  {object} labels - Custom country labels. E.g. `{ RU: 'Россия', US: 'США', ... }`.\r\n * @param  {boolean} addInternationalOption - Whether should include \"International\" option at the top of the list.\r\n * @return {object[]} A list of objects having shape `{ value : string, label : string }`.\r\n */\r\nexport function getCountrySelectOptions({\r\n\tcountries,\r\n\tcountryNames,\r\n\taddInternationalOption,\r\n\t// `locales` are only used in country name comparator:\r\n\t// depending on locale, string sorting order could be different.\r\n\tcompareStringsLocales,\r\n\tcompareStrings: _compareStrings\r\n}) {\r\n\t// Default country name comparator uses `String.localeCompare()`.\r\n\tif (!_compareStrings) {\r\n\t\t_compareStrings = compareStrings\r\n\t}\r\n\r\n\t// Generates a `<Select/>` option for each country.\r\n\tconst countrySelectOptions = countries.map((country) => ({\r\n\t\tvalue: country,\r\n\t\t// All `locale` country names included in this library\r\n\t\t// include all countries (this is checked at build time).\r\n\t\t// The only case when a country name might be missing\r\n\t\t// is when a developer supplies their own `labels` property.\r\n\t\t// To guard against such cases, a missing country name\r\n\t\t// is substituted by country code.\r\n\t\tlabel: countryNames[country] || country\r\n\t}))\r\n\r\n\t// Sort the list of countries alphabetically.\r\n\tcountrySelectOptions.sort((a, b) => _compareStrings(a.label, b.label, compareStringsLocales))\r\n\r\n\t// Add the \"International\" option to the country list (if suitable)\r\n\tif (addInternationalOption) {\r\n\t\tcountrySelectOptions.unshift({\r\n\t\t\tlabel: countryNames.ZZ\r\n\t\t})\r\n\t}\r\n\r\n\treturn countrySelectOptions\r\n}\r\n\r\n/**\r\n * Parses a E.164 phone number to an instance of `PhoneNumber` class.\r\n * @param {string?} value = E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {object} Object having shape `{ country: string?, countryCallingCode: string, number: string }`. `PhoneNumber`: https://gitlab.com/catamphetamine/libphonenumber-js#phonenumber.\r\n * @example\r\n * parsePhoneNumber('+78005553535')\r\n */\r\nexport function parsePhoneNumber(value, metadata) {\r\n\treturn parsePhoneNumber_(value || '', metadata)\r\n}\r\n\r\n/**\r\n * Generates national number digits for a parsed phone.\r\n * May prepend national prefix.\r\n * The phone number must be a complete and valid phone number.\r\n * @param  {object} phoneNumber - An instance of `PhoneNumber` class.\r\n * @param  {object} metadata - `libphonenumber-js` metadata\r\n * @return {string}\r\n * @example\r\n * getNationalNumberDigits({ country: 'RU', phone: '8005553535' })\r\n * // returns '88005553535'\r\n */\r\nexport function generateNationalNumberDigits(phoneNumber) {\r\n\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n}\r\n\r\n/**\r\n * Migrates parsed `<input/>` `value` for the newly selected `country`.\r\n * @param {string?} phoneDigits - Phone number digits (and `+`) parsed from phone number `<input/>` (it's not the same as the `value` property).\r\n * @param {string?} prevCountry - Previously selected country.\r\n * @param {string?} newCountry - Newly selected country. Can't be same as previously selected country.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @param {boolean} useNationalFormat - whether should attempt to convert from international to national number for the new country.\r\n * @return {string?}\r\n */\r\nexport function getPhoneDigitsForNewCountry(phoneDigits, {\r\n\tprevCountry,\r\n\tnewCountry,\r\n\tmetadata,\r\n\tuseNationalFormat\r\n}) {\r\n\tif (prevCountry === newCountry) {\r\n\t\treturn phoneDigits\r\n\t}\r\n\r\n\t// If `parsed_input` is empty\r\n\t// then no need to migrate anything.\r\n\tif (!phoneDigits) {\r\n\t\tif (useNationalFormat) {\r\n\t\t\treturn ''\r\n\t\t} else {\r\n\t\t\tif (newCountry) {\r\n\t\t\t\t// If `phoneDigits` is empty then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t}\r\n\t\t\treturn ''\r\n\t\t}\r\n\t}\r\n\r\n\t// If switching to some country.\r\n\t// (from \"International\" or another country)\r\n\t// If switching from \"International\" then `phoneDigits` starts with a `+`.\r\n\t// Otherwise it may or may not start with a `+`.\r\n\tif (newCountry) {\r\n\t\t// If the phone number was entered in international format\r\n\t\t// then migrate it to the newly selected country.\r\n\t\t// The phone number may be incomplete.\r\n\t\t// The phone number entered not necessarily starts with\r\n\t\t// the previously selected country phone prefix.\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\t// If the international phone number is for the new country\r\n\t\t\t// then convert it to local if required.\r\n\t\t\tif (useNationalFormat) {\r\n\t\t\t\t// // If a phone number is being input in international form\r\n\t\t\t\t// // and the country can already be derived from it,\r\n\t\t\t\t// // and if it is the new country, then format as a national number.\r\n\t\t\t\t// const derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(phoneDigits, metadata)\r\n\t\t\t\t// if (derived_country === newCountry) {\r\n\t\t\t\t// \treturn stripCountryCallingCode(phoneDigits, derived_country, metadata)\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t// Actually, the two countries don't necessarily need to match:\r\n\t\t\t\t// the condition could be looser here, because several countries\r\n\t\t\t\t// might share the same international phone number format\r\n\t\t\t\t// (for example, \"NANPA\" countries like US, Canada, etc).\r\n\t\t\t\t// The looser condition would be just \"same nternational phone number format\"\r\n\t\t\t\t// which would mean \"same country calling code\" in the context of `libphonenumber-js`.\r\n\t\t\t\tif (phoneDigits.indexOf('+' + getCountryCallingCode(newCountry, metadata)) === 0) {\r\n\t\t\t\t\treturn stripCountryCallingCode(phoneDigits, newCountry, metadata)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Simply discard the previously entered international phone number,\r\n\t\t\t\t// because otherwise any \"smart\" transformation like getting the\r\n\t\t\t\t// \"national (significant) number\" part and then prepending the\r\n\t\t\t\t// newly selected country's \"country calling code\" to it\r\n\t\t\t\t// would just be confusing for a user without being actually useful.\r\n\t\t\t\treturn ''\r\n\r\n\t\t\t\t// // Simply strip the leading `+` character\r\n\t\t\t\t// // therefore simply converting all digits into a \"local\" phone number.\r\n\t\t\t\t// // https://github.com/catamphetamine/react-phone-number-input/issues/287\r\n\t\t\t\t// return phoneDigits.slice(1)\r\n\t\t\t}\r\n\r\n\t\t\tif (prevCountry) {\r\n\t\t\t\tconst newCountryPrefix = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\tif (phoneDigits.indexOf(newCountryPrefix) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn newCountryPrefix\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconst defaultValue = getInternationalPhoneNumberPrefix(newCountry, metadata)\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is the same\r\n\t\t\t\t// as for the new `country`, then leave `phoneDigits` as is.\r\n\t\t\t\tif (phoneDigits.indexOf(defaultValue) === 0) {\r\n\t\t\t\t\treturn phoneDigits\r\n\t\t\t\t}\r\n\t\t\t\t// If `phoneDigits`'s country calling code part is not the same\r\n\t\t\t\t// as for the new `country`, then set `phoneDigits` to\r\n\t\t\t\t// `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\treturn defaultValue\r\n\t\t\t}\r\n\r\n\t\t\t// // If the international phone number already contains\r\n\t\t\t// // any country calling code then trim the country calling code part.\r\n\t\t\t// // (that could also be the newly selected country phone code prefix as well)\r\n\t\t\t// // `phoneDigits` doesn't neccessarily belong to `prevCountry`.\r\n\t\t\t// // (e.g. if a user enters an international number\r\n\t\t\t// //  not belonging to any of the reduced `countries` list).\r\n\t\t\t// phoneDigits = stripCountryCallingCode(phoneDigits, prevCountry, metadata)\r\n\r\n\t\t\t// // Prepend country calling code prefix\r\n\t\t\t// // for the newly selected country.\r\n\t\t\t// return e164(phoneDigits, newCountry, metadata) || `+${getCountryCallingCode(newCountry, metadata)}`\r\n\t\t}\r\n\t}\r\n\t// If switching to \"International\" from a country.\r\n\telse {\r\n\t\t// If the phone number was entered in national format.\r\n\t\tif (phoneDigits[0] !== '+') {\r\n\t\t\t// Format the national phone number as an international one.\r\n\t\t\t// The phone number entered not necessarily even starts with\r\n\t\t\t// the previously selected country phone prefix.\r\n\t\t\t// Even if the phone number belongs to whole another country\r\n\t\t\t// it will still be parsed into some national phone number.\r\n\t\t\t//\r\n\t\t\t// Ignore the now-uncovered `|| ''` code branch:\r\n\t\t\t// previously `e164()` function could return an empty string\r\n\t\t\t// even when `phoneDigits` were not empty.\r\n\t\t\t// Now it always returns some `value` when there're any `phoneDigits`.\r\n\t\t\t// Still, didn't remove the `|| ''` code branch just in case\r\n\t\t\t// that logic changes somehow in some future, so there're no\r\n\t\t\t// possible bugs related to that.\r\n\t\t\t//\r\n\t\t\t// (ignore the `|| ''` code branch)\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn e164(phoneDigits, prevCountry, metadata) || ''\r\n\t\t}\r\n\t}\r\n\r\n\treturn phoneDigits\r\n}\r\n\r\n/**\r\n * Converts phone number digits to a (possibly incomplete) E.164 phone number.\r\n * @param  {string?} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string?} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function e164(number, country, metadata) {\r\n\tif (!number) {\r\n\t\treturn\r\n\t}\r\n\t// If the phone number is being input in international format.\r\n\tif (number[0] === '+') {\r\n\t\t// If it's just the `+` sign then return nothing.\r\n\t\tif (number === '+') {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Return a E.164 phone number.\r\n\t\t//\r\n\t\t// Could return `number` \"as is\" here, but there's a possibility\r\n\t\t// that some user might incorrectly input an international number\r\n\t\t// with a \"national prefix\". Such numbers aren't considered valid,\r\n\t\t// but `libphonenumber-js` is \"forgiving\" when it comes to parsing\r\n\t\t// user's input, and this input component follows that behavior.\r\n\t\t//\r\n\t\tconst asYouType = new AsYouType(country, metadata)\r\n\t\tasYouType.input(number)\r\n\t\t// This function would return `undefined` only when `number` is `\"+\"`,\r\n\t\t// but at this point it is known that `number` is not `\"+\"`.\r\n\t\treturn asYouType.getNumberValue()\r\n\t}\r\n\t// For non-international phone numbers\r\n\t// an accompanying country code is required.\r\n\t// The situation when `country` is `undefined`\r\n\t// and a non-international phone number is passed\r\n\t// to this function shouldn't happen.\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\tconst partial_national_significant_number = getNationalSignificantNumberDigits(number, country, metadata)\r\n\t//\r\n\t// Even if no \"national (significant) number\" digits have been input,\r\n\t// still return a non-`undefined` value.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113\r\n\t//\r\n\t// For example, if the user has selected country `US` and entered `\"1\"`\r\n\t// then that `\"1\"` is just a \"national prefix\" and no \"national (significant) number\"\r\n\t// digits have been input yet. Still, return `\"+1\"` as `value` in such cases,\r\n\t// because otherwise the app would think that the input is empty and mark it as such\r\n\t// while in reality it isn't empty, which might be thought of as a \"bug\", or just\r\n\t// a \"weird\" behavior.\r\n\t//\r\n\t// if (partial_national_significant_number) {\r\n\t\treturn `+${getCountryCallingCode(country, metadata)}${partial_national_significant_number || ''}`\r\n\t// }\r\n}\r\n\r\n/**\r\n * Trims phone number digits if they exceed the maximum possible length\r\n * for a national (significant) number for the country.\r\n * @param  {string} number - A possibly incomplete phone number digits string. Can be a possibly incomplete E.164 phone number.\r\n * @param  {string} country\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} Can be empty.\r\n */\r\nexport function trimNumber(number, country, metadata) {\r\n\tconst nationalSignificantNumberPart = getNationalSignificantNumberDigits(number, country, metadata)\r\n\tif (nationalSignificantNumberPart) {\r\n\t\tconst overflowDigitsCount = nationalSignificantNumberPart.length - getMaxNumberLength(country, metadata)\r\n\t\tif (overflowDigitsCount > 0) {\r\n\t\t\treturn number.slice(0, number.length - overflowDigitsCount)\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}\r\n\r\nfunction getMaxNumberLength(country, metadata) {\r\n\t// Get \"possible lengths\" for a phone number of the country.\r\n\tmetadata = new Metadata(metadata)\r\n\tmetadata.selectNumberingPlan(country)\r\n\t// Return the last \"possible length\".\r\n\treturn metadata.numberingPlan.possibleLengths()[metadata.numberingPlan.possibleLengths().length - 1]\r\n}\r\n\r\n// If the phone number being input is an international one\r\n// then tries to derive the country from the phone number.\r\n// (regardless of whether there's any country currently selected)\r\n/**\r\n * @param {string} partialE164Number - A possibly incomplete E.164 phone number.\r\n * @param {string?} country - Currently selected country.\r\n * @param {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param {string?} defaultCountry — Default country.\r\n * @param {string?} latestCountrySelectedByUser — The latest country that has been manually selected by the user.\r\n * @param {boolean?} required — Whether \"International\" option could be selected, meaning \"no country is selected\".\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryForPartialE164Number(partialE164Number, {\r\n\tcountry,\r\n\tcountries,\r\n\tdefaultCountry,\r\n\tlatestCountrySelectedByUser,\r\n\trequired,\r\n\tmetadata\r\n}) {\r\n\t// `partialE164Number` is supposed to be an E.164 phone number.\r\n\r\n\t// `partialE164Number` is supposed to be non-empty when calling this function\r\n\t// so it doesn't check for `if (!partialE164Number)`.\r\n\r\n\tif (partialE164Number === '+') {\r\n\t\t// Don't change the currently selected country yet.\r\n\t\treturn country\r\n\t}\r\n\r\n\tconst derived_country = getCountryFromPossiblyIncompleteInternationalPhoneNumber(partialE164Number, metadata)\r\n\r\n\t// If a phone number is being input in international form\r\n\t// and the country can already be derived from it,\r\n\t// then select that country.\r\n\tif (derived_country) {\r\n\t\tif (!countries || (countries.indexOf(derived_country) >= 0)) {\r\n\t\t\treturn derived_country\r\n\t\t} else {\r\n\t\t\treturn undefined\r\n\t\t}\r\n\t}\r\n\t// Otherwise, if the phone number doesn't correspond to any particular country.\r\n\t// If some country was previously selected.\r\n\telse if (country) {\r\n\t\t// If the international phone number entered could still correspond to the previously selected country\r\n\t\t// and also to some other country or countries corresponding to the same calling code\r\n\t\t// then it should reset the currently selected country to reflect the ambiguity.\r\n\t\tif (couldNumberBelongToCountry(partialE164Number, country, metadata)) {\r\n\t\t\t// Reset the country either to the latest one that was manually selected by the user\r\n\t\t\t// or to the default country or just reset the country selection.\r\n\t\t\tif (latestCountrySelectedByUser && couldNumberBelongToCountry(partialE164Number, latestCountrySelectedByUser, metadata)) {\r\n\t\t\t\treturn latestCountrySelectedByUser\r\n\t\t\t} else if (defaultCountry && couldNumberBelongToCountry(partialE164Number, defaultCountry, metadata)) {\r\n\t\t\t\treturn defaultCountry\r\n\t\t\t} else {\r\n\t\t\t\tif (!required) {\r\n\t\t\t\t\t// Just reset the currently selected country.\r\n\t\t\t\t\treturn undefined\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// If \"International\" country option has not been disabled\r\n\t\t\t// and the international phone number entered doesn't necessarily correspond to\r\n\t\t\t// the currently selected country and it could not possibly correspond to it\r\n\t\t\t// then reset the currently selected country.\r\n\t\t\tif (!required) {\r\n\t\t\t\treturn undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Don't change the currently selected country.\r\n\treturn country\r\n}\r\n\r\n/**\r\n * Parses `<input/>` value. Derives `country` from `input`. Derives an E.164 `value`.\r\n * @param  {string?} phoneDigits — Parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} prevPhoneDigits — Previous parsed `<input/>` value. Examples: `\"\"`, `\"+\"`, `\"+123\"`, `\"123\"`.\r\n * @param  {string?} country - Currently selected country.\r\n * @param  {string?} defaultCountry - Default country.\r\n * @param  {string?} latestCountrySelectedByUser - The latest country that has been manually selected by the user.\r\n * @param  {boolean} countryRequired - Is selecting some country required.\r\n * @param  {function} getAnyCountry - Can be used to get any country when selecting some country required.\r\n * @param  {string[]?} countries - A list of available countries. If not passed then \"all countries\" are assumed.\r\n * @param  {boolean} international - Set to `true` to force international phone number format (leading `+`). Set to `false` to force \"national\" phone number format. Is `undefined` by default.\r\n * @param  {boolean} limitMaxLength — Whether to enable limiting phone number max length.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {object} An object of shape `{ phoneDigits, country, value }`. `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`. The returned `phoneDigits` shouldn't be used anywhere except for passing it as `prevPhoneDigits` parameter to this same function on next input change event.\r\n */\r\nexport function onPhoneDigitsChange(phoneDigits, {\r\n\tprevPhoneDigits,\r\n\tcountry,\r\n\tdefaultCountry,\r\n\tlatestCountrySelectedByUser,\r\n\tcountryRequired,\r\n\tgetAnyCountry,\r\n\tcountries,\r\n\tinternational,\r\n\tlimitMaxLength,\r\n\tcountryCallingCodeEditable,\r\n\tmetadata\r\n}) {\r\n\t// When the input is in `international` and `countryCallingCodeEditable={false}` mode,\r\n\t// the `country` should not change. If the user attempted to overwrite the country callling code part,\r\n\t// the component should reset it back to the correct country calling code for the `country`.\r\n\tif (international && countryCallingCodeEditable === false) {\r\n\t\tif (country) {\r\n\t\t\t// For international phone numbers written with non-editable country calling code,\r\n\t\t\t// the `<input/>` value must always start with that non-editable country calling code.\r\n\t\t\tconst prefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\t\t\t// If the input value doesn't start with the non-editable country calling code,\r\n\t\t\t// it should be fixed.\r\n\t\t\tif (phoneDigits.indexOf(prefix) !== 0) {\r\n\t\t\t\tlet value\r\n\t\t\t\t// If a phone number input is declared as\r\n\t\t\t\t// `international: true` and `countryCallingCodeEditable: false`,\r\n\t\t\t\t// then the value of the `<input/>` is gonna be non-empty at all times,\r\n\t\t\t\t// even before the user has started to input any digits in the input field,\r\n\t\t\t\t// because the country calling code is always there by design.\r\n\t\t\t\t//\r\n\t\t\t\t// The fact that the input value is always non-empty results in a side effect:\r\n\t\t\t\t// whenever a user tabs into such input field, its value gets automatically selected.\r\n\t\t\t\t// If at that moment in time the user starts typing in the national digits of the phone number,\r\n\t\t\t\t// the selected `<input/>` value gets automatically replaced by those typed-in digits\r\n\t\t\t\t// so the value changes from `+xxx` to `y`, because inputting anything while having\r\n\t\t\t\t// the `<input/>` value selected results in erasing that `<input/>` value.\r\n\t\t\t\t//\r\n\t\t\t\t// This component handles such cases by restoring the `<input/>` value to what\r\n\t\t\t\t// it should be in such cases: `+xxxy`.\r\n\t\t\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/43\r\n\t\t\t\t//\r\n\t\t\t\tconst hasStartedTypingInNationalNumberDigitsHavingInputValueSelected = phoneDigits && phoneDigits[0] !== '+'\r\n\t\t\t\tif (hasStartedTypingInNationalNumberDigitsHavingInputValueSelected) {\r\n\t\t\t\t\t// Fix the input value to what it should be: `y` → `+xxxy`.\r\n\t\t\t\t\tphoneDigits = prefix + phoneDigits\r\n\t\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// In other cases, simply reset the `<input/>` value, because there're only two\r\n\t\t\t\t\t// possible cases:\r\n\t\t\t\t\t// * The user has selected the `<input/>` value and then hit Delete/Backspace to erase it.\r\n\t\t\t\t\t// * The user has pasted an international phone number for another country calling code,\r\n\t\t\t\t\t//   which is considered a non-valid value.\r\n\t\t\t\t\tphoneDigits = prefix\r\n\t\t\t\t}\r\n\t\t\t\treturn {\r\n\t\t\t\t\tphoneDigits,\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\tcountry\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `international` property is `false`, then it means\r\n\t// \"enforce national-only format during input\",\r\n\t// so, if that's the case, then remove all `+` characters,\r\n\t// but only if some country is currently selected.\r\n\t// (not if \"International\" country is selected).\r\n\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// Trim the input to not exceed the maximum possible number length.\r\n\tif (phoneDigits && country && limitMaxLength) {\r\n\t\tphoneDigits = trimNumber(phoneDigits, country, metadata)\r\n\t}\r\n\r\n\t// If this `onChange()` event was triggered\r\n\t// as a result of selecting \"International\" country,\r\n\t// then force-prepend a `+` sign if the phone number\r\n\t// `<input/>` value isn't in international format.\r\n\t// Also, force-prepend a `+` sign if international\r\n\t// phone number input format is set.\r\n\tif (phoneDigits && phoneDigits[0] !== '+' && (!country || international)) {\r\n\t\tphoneDigits = '+' + phoneDigits\r\n\t}\r\n\r\n\t// If the previously entered phone number\r\n\t// has been entered in international format\r\n\t// and the user decides to erase it,\r\n\t// then also reset the `country`\r\n\t// because it was most likely automatically selected\r\n\t// while the user was typing in the phone number\r\n\t// in international format.\r\n\t// This fixes the issue when a user is presented\r\n\t// with a phone number input with no country selected\r\n\t// and then types in their local phone number\r\n\t// then discovers that the input's messed up\r\n\t// (a `+` has been prepended at the start of their input\r\n\t//  and a random country has been selected),\r\n\t// decides to undo it all by erasing everything\r\n\t// and then types in their local phone number again\r\n\t// resulting in a seemingly correct phone number\r\n\t// but in reality that phone number has incorrect country.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/273\r\n\tif (!phoneDigits && prevPhoneDigits && prevPhoneDigits[0] === '+') {\r\n\t\tif (international) {\r\n\t\t\tcountry = undefined\r\n\t\t} else {\r\n\t\t\tcountry = defaultCountry\r\n\t\t}\r\n\t}\r\n\t// Also resets such \"randomly\" selected country\r\n\t// as soon as the user erases the number\r\n\t// digit-by-digit up to the leading `+` sign.\r\n\tif (phoneDigits === '+' && prevPhoneDigits && prevPhoneDigits[0] === '+' && prevPhoneDigits.length > '+'.length) {\r\n\t\tcountry = undefined\r\n\t}\r\n\r\n\t// Generate the new `value` property.\r\n\tlet value\r\n\tif (phoneDigits) {\r\n\t\tif (phoneDigits[0] === '+') {\r\n\t\t\tif (phoneDigits === '+') {\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else if (country && getInternationalPhoneNumberPrefix(country, metadata).indexOf(phoneDigits) === 0) {\r\n\t\t\t\t// Selected a `country` and started inputting an\r\n\t\t\t\t// international phone number for this country\r\n\t\t\t\t// but hasn't input any \"national (significant) number\" digits yet.\r\n\t\t\t\t// In that case, assume `value` be `undefined`.\r\n\t\t\t\t//\r\n\t\t\t\t// For example, if selected `country` `\"US\"`\r\n\t\t\t\t// and started inputting phone number `\"+1\"`\r\n\t\t\t\t// then `value` `undefined` will be returned from this function.\r\n\t\t\t\t//\r\n\t\t\t\tvalue = undefined\r\n\t\t\t} else {\r\n\t\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\t// Derive the country from the phone number.\r\n\t// (regardless of whether there's any country currently selected,\r\n\t//  because there could be several countries corresponding to one country calling code)\r\n\tif (value) {\r\n\t\tcountry = getCountryForPartialE164Number(value, {\r\n\t\t\tcountry,\r\n\t\t\tcountries,\r\n\t\t\tdefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser,\r\n\t\t\t// `countryRequired` flag is not passed here.\r\n\t\t\t// Instead, it's explicitly checked a bit later in the code.\r\n\t\t\trequired: false,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\t// If `international` property is `false`, then it means\r\n\t\t// \"enforce national-only format during input\",\r\n\t\t// so, if that's the case, then remove all `+` characters,\r\n\t\t// but only if some country is currently selected.\r\n\t\t// (not if \"International\" country is selected).\r\n\t\tif (international === false && country && phoneDigits && phoneDigits[0] === '+') {\r\n\t\t\tphoneDigits = convertInternationalPhoneDigitsToNational(phoneDigits, country, metadata)\r\n\t\t\t// Re-calculate `value` because `phoneDigits` has changed.\r\n\t\t\tvalue = e164(phoneDigits, country, metadata)\r\n\t\t}\r\n\t}\r\n\r\n\tif (!country && countryRequired) {\r\n\t\tcountry = defaultCountry || getAnyCountry()\r\n\t}\r\n\r\n\treturn {\r\n\t\t// `phoneDigits` returned here are a \"normalized\" version of the original `phoneDigits`.\r\n\t\t// The returned `phoneDigits` shouldn't be used anywhere except for passing it as\r\n\t\t// `prevPhoneDigits` parameter to this same function on next input change event.\r\n\t\tphoneDigits,\r\n\t\tcountry,\r\n\t\tvalue\r\n\t}\r\n}\r\n\r\nfunction convertInternationalPhoneDigitsToNational(input, country, metadata) {\r\n\t// Handle the case when a user might have pasted\r\n\t// a phone number in international format.\r\n\tif (input.indexOf(getInternationalPhoneNumberPrefix(country, metadata)) === 0) {\r\n\t\t// Create \"as you type\" formatter.\r\n\t\tconst formatter = new AsYouType(country, metadata)\r\n\t\t// Input partial national phone number.\r\n\t\tformatter.input(input)\r\n\t\t// Return the parsed partial national phone number.\r\n\t\tconst phoneNumber = formatter.getNumber()\r\n\t\tif (phoneNumber) {\r\n\t\t\t// Transform the number to a national one,\r\n\t\t\t// and remove all non-digits.\r\n\t\t\treturn phoneNumber.formatNational().replace(/\\D/g, '')\r\n\t\t} else {\r\n\t\t\treturn ''\r\n\t\t}\r\n\t} else {\r\n\t\t// Just remove the `+` sign.\r\n\t\treturn input.replace(/\\D/g, '')\r\n\t}\r\n}\r\n\r\n/**\r\n * Determines the country for a given (possibly incomplete) E.164 phone number.\r\n * @param  {string} number - A possibly incomplete E.164 phone number.\r\n * @param  {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string?}\r\n */\r\nexport function getCountryFromPossiblyIncompleteInternationalPhoneNumber(number, metadata) {\r\n\tconst formatter = new AsYouType(null, metadata)\r\n\tformatter.input(number)\r\n\t// // `001` is a special \"non-geograpical entity\" code\r\n\t// // in Google's `libphonenumber` library.\r\n\t// if (formatter.getCountry() === '001') {\r\n\t// \treturn\r\n\t// }\r\n\treturn formatter.getCountry()\r\n}\r\n\r\n/**\r\n * Compares two strings.\r\n * A helper for `Array.sort()`.\r\n * @param {string} a — First string.\r\n * @param {string} b — Second string.\r\n * @param {(string[]|string)} [locales] — The `locales` argument of `String.localeCompare`.\r\n */\r\nexport function compareStrings(a, b, locales) {\r\n  // Use `String.localeCompare` if it's available.\r\n  // https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/String/localeCompare\r\n  // Which means everyone except IE <= 10 and Safari <= 10.\r\n  // `localeCompare()` is available in latest Node.js versions.\r\n  /* istanbul ignore else */\r\n  if (String.prototype.localeCompare) {\r\n    return a.localeCompare(b, locales);\r\n  }\r\n  /* istanbul ignore next */\r\n  return a < b ? -1 : (a > b ? 1 : 0);\r\n}\r\n\r\n/**\r\n * Strips `+${countryCallingCode}` prefix from an E.164 phone number.\r\n * @param {string} number - (possibly incomplete) E.164 phone number.\r\n * @param {string?} country - A possible country for this phone number.\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string}\r\n */\r\nexport function stripCountryCallingCode(number, country, metadata) {\r\n\t// Just an optimization, so that it\r\n\t// doesn't have to iterate through all country calling codes.\r\n\tif (country) {\r\n\t\tconst countryCallingCodePrefix = '+' + getCountryCallingCode(country, metadata)\r\n\r\n\t\t// If `country` fits the actual `number`.\r\n\t\tif (number.length < countryCallingCodePrefix.length) {\r\n\t\t\tif (countryCallingCodePrefix.indexOf(number) === 0) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (number.indexOf(countryCallingCodePrefix) === 0) {\r\n\t\t\t\treturn number.slice(countryCallingCodePrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If `country` doesn't fit the actual `number`.\r\n\t// Try all available country calling codes.\r\n\tfor (const country_calling_code of Object.keys(metadata.country_calling_codes)) {\r\n\t\tif (number.indexOf(country_calling_code) === '+'.length) {\r\n\t\t\treturn number.slice('+'.length + country_calling_code.length)\r\n\t\t}\r\n\t}\r\n\r\n\treturn ''\r\n}\r\n\r\n/**\r\n * Parses a partially entered national phone number digits\r\n * (or a partially entered E.164 international phone number)\r\n * and returns the national significant number part.\r\n * National significant number returned doesn't come with a national prefix.\r\n * @param {string} number - National number digits. Or possibly incomplete E.164 phone number.\r\n * @param {string?} country\r\n * @param {object} metadata - `libphonenumber-js` metadata.\r\n * @return {string} [result]\r\n */\r\nexport function getNationalSignificantNumberDigits(number, country, metadata) {\r\n\t// Create \"as you type\" formatter.\r\n\tconst formatter = new AsYouType(country, metadata)\r\n\t// Input partial national phone number.\r\n\tformatter.input(number)\r\n\t// Return the parsed partial national phone number.\r\n\tconst phoneNumber = formatter.getNumber()\r\n\treturn phoneNumber && phoneNumber.nationalNumber\r\n}\r\n\r\n/**\r\n * Checks if a partially entered E.164 phone number could belong to a country.\r\n * @param  {string} number\r\n * @param  {string} country\r\n * @return {boolean}\r\n */\r\nexport function couldNumberBelongToCountry(number, country, metadata) {\r\n\tconst intlPhoneNumberPrefix = getInternationalPhoneNumberPrefix(country, metadata)\r\n\tlet i = 0\r\n\twhile (i < number.length && i < intlPhoneNumberPrefix.length) {\r\n\t\tif (number[i] !== intlPhoneNumberPrefix[i]) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn true\r\n}\r\n\r\n/**\r\n * Gets initial \"phone digits\" (including `+`, if using international format).\r\n * @return {string} [phoneDigits] Returns `undefined` if there should be no initial \"phone digits\".\r\n */\r\nexport function getInitialPhoneDigits({\r\n\tvalue,\r\n\tphoneNumber,\r\n\tdefaultCountry,\r\n\tinternational,\r\n\tuseNationalFormat,\r\n\tmetadata\r\n}) {\r\n\t// If the `value` (E.164 phone number)\r\n\t// belongs to the currently selected country\r\n\t// and `useNationalFormat` is `true`\r\n\t// then convert `value` (E.164 phone number)\r\n\t// to a local phone number digits.\r\n\t// E.g. '+78005553535' -> '88005553535'.\r\n\tif ((international === false || useNationalFormat) && phoneNumber && phoneNumber.country) {\r\n\t\treturn generateNationalNumberDigits(phoneNumber)\r\n\t}\r\n\t// If `international` property is `true`,\r\n\t// meaning \"enforce international phone number format\",\r\n\t// then always show country calling code in the input field.\r\n\tif (!value && international && defaultCountry) {\r\n\t\treturn getInternationalPhoneNumberPrefix(defaultCountry, metadata)\r\n\t}\r\n\treturn value\r\n}\r\n\r\n// function doesIncompletePhoneNumberCorrespondToASingleCountry(value, metadata) {\r\n// \t// Create \"as you type\" formatter.\r\n// \tconst formatter = new AsYouType(undefined, metadata)\r\n// \t// Input partial national phone number.\r\n// \tformatter.input(value)\r\n// \t// Return the parsed partial national phone number.\r\n// \tconst phoneNumber = formatter.getNumber()\r\n// \tif (phoneNumber) {\r\n// \t\treturn phoneNumber.getPossibleCountries().length === 1\r\n// \t} else {\r\n// \t\treturn false\r\n// \t}\r\n// }", "import {\r\n\tgetInitialPhoneDigits,\r\n\tgetCountryForPartialE164Number,\r\n\tparsePhoneNumber,\r\n\tcouldNumberBelongToCountry\r\n} from './phoneInputHelpers.js'\r\n\r\nimport { validateE164Number } from './isE164Number.js'\r\n\r\nimport getInternationalPhoneNumberPrefix from './getInternationalPhoneNumberPrefix.js'\r\n\r\nimport {\r\n\tisCountrySupportedWithError,\r\n\tgetSupportedCountries\r\n} from './countries.js'\r\n\r\nexport default function getPhoneInputWithCountryStateUpdateFromNewProps(props, prevProps, state) {\r\n\tconst {\r\n\t\tmetadata,\r\n\t\tcountries,\r\n\t\tdefaultCountry: newDefaultCountry,\r\n\t\tvalue: newValue,\r\n\t\treset: newReset,\r\n\t\tinternational,\r\n\t\t// `displayInitialValueAsLocalNumber` property has been\r\n\t\t// superceded by `initialValueFormat` property.\r\n\t\tdisplayInitialValueAsLocalNumber,\r\n\t\tinitialValueFormat\r\n\t} = props\r\n\r\n\tconst {\r\n\t\tdefaultCountry: prevDefaultCountry,\r\n\t\tvalue: prevValue,\r\n\t\treset: prevReset\r\n\t} = prevProps\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tvalue,\r\n\t\t// If the user has already manually selected a country\r\n\t\t// then don't override that already selected country\r\n\t\t// if the `defaultCountry` property changes.\r\n\t\t// That's what `hasUserSelectedACountry` flag is for.\r\n\t\thasUserSelectedACountry,\r\n\t\tlatestCountrySelectedByUser\r\n\t} = state\r\n\r\n\tconst _getInitialPhoneDigits = (parameters) => getInitialPhoneDigits({\r\n\t\t...parameters,\r\n\t\tinternational,\r\n\t\tuseNationalFormat: displayInitialValueAsLocalNumber || initialValueFormat === 'national',\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Some users requested a way to reset the component\r\n\t// (both number `<input/>` and country `<select/>`).\r\n\t// Whenever `reset` property changes both number `<input/>`\r\n\t// and country `<select/>` are reset.\r\n\t// It's not implemented as some instance `.reset()` method\r\n\t// because `ref` is forwarded to `<input/>`.\r\n\t// It's also not replaced with just resetting `country` on\r\n\t// external `value` reset, because a user could select a country\r\n\t// and then not input any `value`, and so the selected country\r\n\t// would be \"stuck\", if not using this `reset` property.\r\n\t// https://github.com/catamphetamine/react-phone-number-input/issues/300\r\n\tif (newReset !== prevReset) {\r\n\t\treturn {\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: undefined,\r\n\t\t\tcountry: newDefaultCountry,\r\n\t\t\tlatestCountrySelectedByUser: undefined,\r\n\t\t\thasUserSelectedACountry: undefined\r\n\t\t}\r\n\t}\r\n\r\n\t// `value` is the value currently shown in the component:\r\n\t// it's stored in the component's `state`, and it's not the `value` property.\r\n\t// `prevValue` is \"previous `value` property\".\r\n\t// `newValue` is \"new `value` property\".\r\n\r\n\t// If the default country changed\r\n\t// (e.g. in case of ajax GeoIP detection after page loaded)\r\n\t// then select it, but only if the user hasn't already manually\r\n\t// selected a country, and no phone number has been manually entered so far.\r\n\t// Because if the user has already started inputting a phone number\r\n\t// then they're okay with no country being selected at all (\"International\")\r\n\t// and they don't want to be disturbed, don't want their input to be screwed, etc.\r\n\tif (newDefaultCountry !== prevDefaultCountry) {\r\n\t\tconst isNewDefaultCountrySupported = !newDefaultCountry || isCountrySupportedWithError(newDefaultCountry, metadata)\r\n\t\tconst noValueHasBeenEnteredByTheUser = (\r\n\t\t\t// By default, \"no value has been entered\" means `value` is `undefined`.\r\n\t\t\t!value ||\r\n\t\t\t// When `international` is `true`, and some country has been pre-selected,\r\n\t\t\t// then the `<input/>` contains a pre-filled value of `+${countryCallingCode}${leadingDigits}`,\r\n\t\t\t// so in case of `international` being `true`, \"the user hasn't entered anything\" situation\r\n\t\t\t// doesn't just mean `value` is `undefined`, but could also mean `value` is `+${countryCallingCode}`.\r\n\t\t\t(international && value === _getInitialPhoneDigits({\r\n\t\t\t\tvalue: undefined,\r\n\t\t\t\tdefaultCountry: prevDefaultCountry\r\n\t\t\t}))\r\n\t\t)\r\n\t\t// Only update the `defaultCountry` property if no phone number\r\n\t\t// has been entered by the user or pre-set by the application.\r\n\t\tconst noValueHasBeenEntered = !newValue && noValueHasBeenEnteredByTheUser\r\n\t\tif (!hasUserSelectedACountry && isNewDefaultCountrySupported && noValueHasBeenEntered) {\r\n\t\t\treturn {\r\n\t\t\t\tcountry: newDefaultCountry,\r\n\t\t\t\t// If `phoneDigits` is empty, then automatically select the new `country`\r\n\t\t\t\t// and set `phoneDigits` to `+{getCountryCallingCode(newCountry)}`.\r\n\t\t\t\t// The code assumes that \"no phone number has been entered by the user\",\r\n\t\t\t\t// and no `value` property has been passed, so the `phoneNumber` parameter\r\n\t\t\t\t// of `_getInitialPhoneDigits({ value, phoneNumber, ... })` is `undefined`.\r\n\t\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\t\tvalue: undefined,\r\n\t\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t\t}),\r\n\t\t\t\t// `value` is `undefined` and it stays so.\r\n\t\t\t\tvalue: undefined\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// If a new `value` is set externally.\r\n\t// (e.g. as a result of an ajax API request\r\n\t//  to get user's phone after page loaded)\r\n\t// The first part — `newValue !== prevValue` —\r\n\t// is basically `props.value !== prevProps.value`\r\n\t// so it means \"if value property was changed externally\".\r\n\t// The second part — `newValue !== value` —\r\n\t// is for ignoring the `getDerivedStateFromProps()` call\r\n\t// which happens in `this.onChange()` right after `this.setState()`.\r\n\t// If this `getDerivedStateFromProps()` call isn't ignored\r\n\t// then the country flag would reset on each input.\r\n\tif (!valuesAreEqual(newValue, prevValue) && !valuesAreEqual(newValue, value)) {\r\n\t\tlet phoneNumber\r\n\t\tlet parsedCountry\r\n\t\tif (newValue) {\r\n\t\t\t// Validate that the newly-supplied `value` is in `E.164` format.\r\n\t\t\t// Because sometimes people attempt to supply a `value` like \"+****************\".\r\n\t\t\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/231#note_2016334796\r\n\t\t\tif (newValue) {\r\n\t\t\t\tvalidateE164Number(newValue)\r\n\t\t\t}\r\n\t\t\tphoneNumber = parsePhoneNumber(newValue, metadata)\r\n\t\t\tconst supportedCountries = getSupportedCountries(countries, metadata)\r\n\t\t\tif (phoneNumber && phoneNumber.country) {\r\n\t\t\t\t// Ignore `else` because all countries are supported in metadata.\r\n\t\t\t\t/* istanbul ignore next */\r\n\t\t\t\tif (!supportedCountries || supportedCountries.indexOf(phoneNumber.country) >= 0) {\r\n\t\t\t\t\tparsedCountry = phoneNumber.country\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tparsedCountry = getCountryForPartialE164Number(newValue, {\r\n\t\t\t\t\tcountry: undefined,\r\n\t\t\t\t\tcountries: supportedCountries,\r\n\t\t\t\t\tmetadata\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// In cases when multiple countries correspond to the same country calling code,\r\n\t\t\t\t// the phone number digits of `newValue` have to be matched against country-specific\r\n\t\t\t\t// regular expressions in order to determine the exact country.\r\n\t\t\t\t// Sometimes, that algorithm can't decide for sure which country does the phone number belong to,\r\n\t\t\t\t// for example when the digits of `newValue` don't match any of those regular expressions.\r\n\t\t\t\t// and the country of the phone number couldn't be determined.\r\n\t\t\t\t// In those cases, people prefer the component to show the flag of the `defaultCountry`\r\n\t\t\t\t// if the phone number could potentially belong to that `defaultCountry`.\r\n\t\t\t\t// At least that's how the component behaves when a user pastes an international\r\n\t\t\t\t// phone number into the input field: for example, when `defaultCountry` is `\"US\"`\r\n\t\t\t\t// and the user pastes value \"****** 555 5555\" into the input field, it keep showing \"US\" flag.\r\n\t\t\t\t// So when setting new `value` property externally, the component should behave the same way:\r\n\t\t\t\t// it should select the `defaultCountry` when the new `value` could potentially belong\r\n\t\t\t\t// to that country in cases when the exact country can't be determined.\r\n\t\t\t\t// https://github.com/catamphetamine/react-phone-number-input/issues/413#issuecomment-1536219404\r\n\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\tif (newDefaultCountry) {\r\n\t\t\t\t\t\tif (newValue.indexOf(getInternationalPhoneNumberPrefix(newDefaultCountry, metadata)) === 0) {\r\n\t\t\t\t\t\t\tparsedCountry = newDefaultCountry\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet userCountrySelectionHistoryStateUpdate\r\n\t\tif (newValue) {\r\n\t\t\t// If the latest country that has been manually selected by the user\r\n\t\t\t// no longer corresponds to the new value then reset it.\r\n\t\t\tif (latestCountrySelectedByUser) {\r\n\t\t\t\tconst couldNewValueCorrespondToLatestCountrySelectedByUser =\r\n\t\t\t\t\tparsedCountry\r\n\t\t\t\t\t\t? latestCountrySelectedByUser === parsedCountry\r\n\t\t\t\t\t\t: couldNumberBelongToCountry(newValue, latestCountrySelectedByUser, metadata)\r\n\r\n\t\t\t\tif (couldNewValueCorrespondToLatestCountrySelectedByUser) {\r\n\t\t\t\t\tif (!parsedCountry) {\r\n\t\t\t\t\t\tparsedCountry = latestCountrySelectedByUser\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuserCountrySelectionHistoryStateUpdate = {\r\n\t\t\t\t\t\tlatestCountrySelectedByUser: undefined\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// When the `value` property is being reset \"externally\",\r\n\t\t\t// reset any tracking of the country that the user has previously selected.\r\n\t\t\tuserCountrySelectionHistoryStateUpdate = {\r\n\t\t\t\tlatestCountrySelectedByUser: undefined,\r\n\t\t\t\thasUserSelectedACountry: undefined\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\t...userCountrySelectionHistoryStateUpdate,\r\n\t\t\tphoneDigits: _getInitialPhoneDigits({\r\n\t\t\t\tphoneNumber,\r\n\t\t\t\tvalue: newValue,\r\n\t\t\t\tdefaultCountry: newDefaultCountry\r\n\t\t\t}),\r\n\t\t\tvalue: newValue,\r\n\t\t\tcountry: newValue ? parsedCountry : newDefaultCountry\r\n\t\t}\r\n\t}\r\n\r\n\t// `defaultCountry` didn't change.\r\n\t// `value` didn't change.\r\n\t// `phoneDigits` didn't change, because `value` didn't change.\r\n\t//\r\n\t// So no need to update state.\r\n}\r\n\r\nexport function valuesAreEqual(value1, value2) {\r\n\t// If `value` has been set to `null` externally then convert it to `undefined`.\r\n\t//\r\n\t// For example, `react-hook-form` sets `value` to `null` when the user clears the input.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/164\r\n\t// In that case, without this conversion of `null` to `undefined`, it would reset\r\n\t// the selected country to `defaultCountry` because in that case `newValue !== value`\r\n\t// because `null !== undefined`.\r\n\t//\r\n\t// Historically, empty `value` is encoded as `undefined`.\r\n\t// Perhaps empty `value` would be better encoded as `null` instead.\r\n\t// But because that would be a potentially breaking change for some people,\r\n\t// it's left as is for the current \"major\" version of this library.\r\n\t//\r\n\tif (value1 === null) {\r\n\t\tvalue1 = undefined\r\n\t}\r\n\tif (value2 === null) {\r\n\t\tvalue2 = undefined\r\n\t}\r\n\treturn value1 === value2\r\n}", "import parsePhoneNumber from 'libphonenumber-js/core'\r\n\r\n/**\r\n * Formats a phone number.\r\n * Is a proxy for `libphonenumber-js`'s `.format()` function of a parsed `PhoneNumber`.\r\n * @param  {string} value\r\n * @param  {string} [format]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatPhoneNumber(value, format, metadata) {\r\n\tif (!metadata) {\r\n\t\tif (typeof format === 'object') {\r\n\t\t\tmetadata = format\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t}\r\n\t}\r\n\tif (!value) {\r\n\t\treturn ''\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(value, metadata)\r\n\tif (!phoneNumber) {\r\n\t\treturn ''\r\n\t}\r\n\t// Deprecated.\r\n\t// Legacy `format`s.\r\n\tswitch (format) {\r\n\t\tcase 'National':\r\n\t\t\tformat = 'NATIONAL'\r\n\t\t\tbreak\r\n\t\tcase 'International':\r\n\t\t\tformat = 'INTERNATIONAL'\r\n\t\t\tbreak\r\n\t}\r\n\treturn phoneNumber.format(format)\r\n}\r\n\r\nexport function formatPhoneNumberIntl(value, metadata) {\r\n\treturn formatPhoneNumber(value, 'INTERNATIONAL', metadata)\r\n}", "import React from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport defaultLabels from '../locale/en.json.js'\r\n\r\nimport {\r\n\tmetadata as metadataPropType,\r\n\tlabels as labelsPropType\r\n} from './PropTypes.js'\r\n\r\nimport PhoneInput from './PhoneInputWithCountry.js'\r\n\r\nexport function createPhoneInput(defaultMetadata) {\r\n\tconst PhoneInputDefault = React.forwardRef(({\r\n\t\tmetadata = defaultMetadata,\r\n\t\tlabels = defaultLabels,\r\n\t\t...rest\r\n\t}, ref) => (\r\n\t\t<PhoneInput\r\n\t\t\t{...rest}\r\n\t\t\tref={ref}\r\n\t\t\tmetadata={metadata}\r\n\t\t\tlabels={labels}\r\n\t\t/>\r\n\t))\r\n\r\n\tPhoneInputDefault.propTypes = {\r\n\t\tmetadata: metadataPropType,\r\n\t\tlabels: labelsPropType\r\n\t}\r\n\r\n\treturn PhoneInputDefault\r\n}\r\n\r\nexport default createPhoneInput()", "export default {\n  \"ext\": \"ext.\",\n  \"country\": \"Phone number country\",\n  \"phone\": \"Phone\",\n  \"AB\": \"Abkhazia\",\n  \"AC\": \"Ascension Island\",\n  \"AD\": \"Andorra\",\n  \"AE\": \"United Arab Emirates\",\n  \"AF\": \"Afghanistan\",\n  \"AG\": \"Antigua and Barbuda\",\n  \"AI\": \"Anguilla\",\n  \"AL\": \"Albania\",\n  \"AM\": \"Armenia\",\n  \"AO\": \"Angola\",\n  \"AQ\": \"Antarctica\",\n  \"AR\": \"Argentina\",\n  \"AS\": \"American Samoa\",\n  \"AT\": \"Austria\",\n  \"AU\": \"Australia\",\n  \"AW\": \"Aruba\",\n  \"AX\": \"Åland Islands\",\n  \"AZ\": \"Azerbaijan\",\n  \"BA\": \"Bosnia and Herzegovina\",\n  \"BB\": \"Barbados\",\n  \"BD\": \"Bangladesh\",\n  \"BE\": \"Belgium\",\n  \"BF\": \"Burkina Faso\",\n  \"BG\": \"Bulgaria\",\n  \"BH\": \"Bahrain\",\n  \"BI\": \"Burundi\",\n  \"BJ\": \"Benin\",\n  \"BL\": \"Saint Barthélemy\",\n  \"BM\": \"Bermuda\",\n  \"BN\": \"Brunei Darussalam\",\n  \"BO\": \"Bolivia\",\n  \"BQ\": \"Bonaire, Sint Eustatius and Saba\",\n  \"BR\": \"Brazil\",\n  \"BS\": \"Bahamas\",\n  \"BT\": \"Bhutan\",\n  \"BV\": \"Bouvet Island\",\n  \"BW\": \"Botswana\",\n  \"BY\": \"Belarus\",\n  \"BZ\": \"Belize\",\n  \"CA\": \"Canada\",\n  \"CC\": \"Cocos (Keeling) Islands\",\n  \"CD\": \"Congo, Democratic Republic of the\",\n  \"CF\": \"Central African Republic\",\n  \"CG\": \"Congo\",\n  \"CH\": \"Switzerland\",\n  \"CI\": \"Cote d'Ivoire\",\n  \"CK\": \"Cook Islands\",\n  \"CL\": \"Chile\",\n  \"CM\": \"Cameroon\",\n  \"CN\": \"China\",\n  \"CO\": \"Colombia\",\n  \"CR\": \"Costa Rica\",\n  \"CU\": \"Cuba\",\n  \"CV\": \"Cape Verde\",\n  \"CW\": \"Curaçao\",\n  \"CX\": \"Christmas Island\",\n  \"CY\": \"Cyprus\",\n  \"CZ\": \"Czech Republic\",\n  \"DE\": \"Germany\",\n  \"DJ\": \"Djibouti\",\n  \"DK\": \"Denmark\",\n  \"DM\": \"Dominica\",\n  \"DO\": \"Dominican Republic\",\n  \"DZ\": \"Algeria\",\n  \"EC\": \"Ecuador\",\n  \"EE\": \"Estonia\",\n  \"EG\": \"Egypt\",\n  \"EH\": \"Western Sahara\",\n  \"ER\": \"Eritrea\",\n  \"ES\": \"Spain\",\n  \"ET\": \"Ethiopia\",\n  \"FI\": \"Finland\",\n  \"FJ\": \"Fiji\",\n  \"FK\": \"Falkland Islands\",\n  \"FM\": \"Federated States of Micronesia\",\n  \"FO\": \"Faroe Islands\",\n  \"FR\": \"France\",\n  \"GA\": \"Gabon\",\n  \"GB\": \"United Kingdom\",\n  \"GD\": \"Grenada\",\n  \"GE\": \"Georgia\",\n  \"GF\": \"French Guiana\",\n  \"GG\": \"Guernsey\",\n  \"GH\": \"Ghana\",\n  \"GI\": \"Gibraltar\",\n  \"GL\": \"Greenland\",\n  \"GM\": \"Gambia\",\n  \"GN\": \"Guinea\",\n  \"GP\": \"Guadeloupe\",\n  \"GQ\": \"Equatorial Guinea\",\n  \"GR\": \"Greece\",\n  \"GS\": \"South Georgia and the South Sandwich Islands\",\n  \"GT\": \"Guatemala\",\n  \"GU\": \"Guam\",\n  \"GW\": \"Guinea-Bissau\",\n  \"GY\": \"Guyana\",\n  \"HK\": \"Hong Kong\",\n  \"HM\": \"Heard Island and McDonald Islands\",\n  \"HN\": \"Honduras\",\n  \"HR\": \"Croatia\",\n  \"HT\": \"Haiti\",\n  \"HU\": \"Hungary\",\n  \"ID\": \"Indonesia\",\n  \"IE\": \"Ireland\",\n  \"IL\": \"Israel\",\n  \"IM\": \"Isle of Man\",\n  \"IN\": \"India\",\n  \"IO\": \"British Indian Ocean Territory\",\n  \"IQ\": \"Iraq\",\n  \"IR\": \"Iran\",\n  \"IS\": \"Iceland\",\n  \"IT\": \"Italy\",\n  \"JE\": \"Jersey\",\n  \"JM\": \"Jamaica\",\n  \"JO\": \"Jordan\",\n  \"JP\": \"Japan\",\n  \"KE\": \"Kenya\",\n  \"KG\": \"Kyrgyzstan\",\n  \"KH\": \"Cambodia\",\n  \"KI\": \"Kiribati\",\n  \"KM\": \"Comoros\",\n  \"KN\": \"Saint Kitts and Nevis\",\n  \"KP\": \"North Korea\",\n  \"KR\": \"South Korea\",\n  \"KW\": \"Kuwait\",\n  \"KY\": \"Cayman Islands\",\n  \"KZ\": \"Kazakhstan\",\n  \"LA\": \"Laos\",\n  \"LB\": \"Lebanon\",\n  \"LC\": \"Saint Lucia\",\n  \"LI\": \"Liechtenstein\",\n  \"LK\": \"Sri Lanka\",\n  \"LR\": \"Liberia\",\n  \"LS\": \"Lesotho\",\n  \"LT\": \"Lithuania\",\n  \"LU\": \"Luxembourg\",\n  \"LV\": \"Latvia\",\n  \"LY\": \"Libya\",\n  \"MA\": \"Morocco\",\n  \"MC\": \"Monaco\",\n  \"MD\": \"Moldova\",\n  \"ME\": \"Montenegro\",\n  \"MF\": \"Saint Martin (French Part)\",\n  \"MG\": \"Madagascar\",\n  \"MH\": \"Marshall Islands\",\n  \"MK\": \"North Macedonia\",\n  \"ML\": \"Mali\",\n  \"MM\": \"Myanmar\",\n  \"MN\": \"Mongolia\",\n  \"MO\": \"Macao\",\n  \"MP\": \"Northern Mariana Islands\",\n  \"MQ\": \"Martinique\",\n  \"MR\": \"Mauritania\",\n  \"MS\": \"Montserrat\",\n  \"MT\": \"Malta\",\n  \"MU\": \"Mauritius\",\n  \"MV\": \"Maldives\",\n  \"MW\": \"Malawi\",\n  \"MX\": \"Mexico\",\n  \"MY\": \"Malaysia\",\n  \"MZ\": \"Mozambique\",\n  \"NA\": \"Namibia\",\n  \"NC\": \"New Caledonia\",\n  \"NE\": \"Niger\",\n  \"NF\": \"Norfolk Island\",\n  \"NG\": \"Nigeria\",\n  \"NI\": \"Nicaragua\",\n  \"NL\": \"Netherlands\",\n  \"NO\": \"Norway\",\n  \"NP\": \"Nepal\",\n  \"NR\": \"Nauru\",\n  \"NU\": \"Niue\",\n  \"NZ\": \"New Zealand\",\n  \"OM\": \"Oman\",\n  \"OS\": \"South Ossetia\",\n  \"PA\": \"Panama\",\n  \"PE\": \"Peru\",\n  \"PF\": \"French Polynesia\",\n  \"PG\": \"Papua New Guinea\",\n  \"PH\": \"Philippines\",\n  \"PK\": \"Pakistan\",\n  \"PL\": \"Poland\",\n  \"PM\": \"Saint Pierre and Miquelon\",\n  \"PN\": \"Pitcairn\",\n  \"PR\": \"Puerto Rico\",\n  \"PS\": \"Palestine\",\n  \"PT\": \"Portugal\",\n  \"PW\": \"Palau\",\n  \"PY\": \"Paraguay\",\n  \"QA\": \"Qatar\",\n  \"RE\": \"Reunion\",\n  \"RO\": \"Romania\",\n  \"RS\": \"Serbia\",\n  \"RU\": \"Russia\",\n  \"RW\": \"Rwanda\",\n  \"SA\": \"Saudi Arabia\",\n  \"SB\": \"Solomon Islands\",\n  \"SC\": \"Seychelles\",\n  \"SD\": \"Sudan\",\n  \"SE\": \"Sweden\",\n  \"SG\": \"Singapore\",\n  \"SH\": \"Saint Helena\",\n  \"SI\": \"Slovenia\",\n  \"SJ\": \"Svalbard and Jan Mayen\",\n  \"SK\": \"Slovakia\",\n  \"SL\": \"Sierra Leone\",\n  \"SM\": \"San Marino\",\n  \"SN\": \"Senegal\",\n  \"SO\": \"Somalia\",\n  \"SR\": \"Suriname\",\n  \"SS\": \"South Sudan\",\n  \"ST\": \"Sao Tome and Principe\",\n  \"SV\": \"El Salvador\",\n  \"SX\": \"Sint Maarten\",\n  \"SY\": \"Syria\",\n  \"SZ\": \"Swaziland\",\n  \"TA\": \"Tristan da Cunha\",\n  \"TC\": \"Turks and Caicos Islands\",\n  \"TD\": \"Chad\",\n  \"TF\": \"French Southern Territories\",\n  \"TG\": \"Togo\",\n  \"TH\": \"Thailand\",\n  \"TJ\": \"Tajikistan\",\n  \"TK\": \"Tokelau\",\n  \"TL\": \"Timor-Leste\",\n  \"TM\": \"Turkmenistan\",\n  \"TN\": \"Tunisia\",\n  \"TO\": \"Tonga\",\n  \"TR\": \"Turkey\",\n  \"TT\": \"Trinidad and Tobago\",\n  \"TV\": \"Tuvalu\",\n  \"TW\": \"Taiwan\",\n  \"TZ\": \"Tanzania\",\n  \"UA\": \"Ukraine\",\n  \"UG\": \"Uganda\",\n  \"UM\": \"United States Minor Outlying Islands\",\n  \"US\": \"United States\",\n  \"UY\": \"Uruguay\",\n  \"UZ\": \"Uzbekistan\",\n  \"VA\": \"Holy See (Vatican City State)\",\n  \"VC\": \"Saint Vincent and the Grenadines\",\n  \"VE\": \"Venezuela\",\n  \"VG\": \"Virgin Islands, British\",\n  \"VI\": \"Virgin Islands, U.S.\",\n  \"VN\": \"Vietnam\",\n  \"VU\": \"Vanuatu\",\n  \"WF\": \"Wallis and Futuna\",\n  \"WS\": \"Samoa\",\n  \"XK\": \"Kosovo\",\n  \"YE\": \"Yemen\",\n  \"YT\": \"Mayotte\",\n  \"ZA\": \"South Africa\",\n  \"ZM\": \"Zambia\",\n  \"ZW\": \"Zimbabwe\",\n  \"ZZ\": \"International\"\n}", "import metadata from 'libphonenumber-js/min/metadata'\r\n\r\nimport {\r\n\tparsePhoneNumber as _parsePhoneNumber,\r\n\tformatPhoneNumber as _formatPhoneNumber,\r\n\tformatPhoneNumberIntl as _formatPhoneNumberIntl,\r\n\tisValidPhoneNumber as _isValidPhoneNumber,\r\n\tisPossiblePhoneNumber as _isPossiblePhoneNumber,\r\n\tgetCountries as _getCountries,\r\n\tgetCountryCallingCode as _getCountryCallingCode,\r\n\tisSupportedCountry as _isSupportedCountry\r\n} from '../core/index.js'\r\n\r\nimport { createPhoneInput } from '../modules/PhoneInputWithCountryDefault.js'\r\n\r\nfunction call(func, _arguments) {\r\n\tvar args = Array.prototype.slice.call(_arguments)\r\n\targs.push(metadata)\r\n\treturn func.apply(this, args)\r\n}\r\n\r\nexport default createPhoneInput(metadata)\r\n\r\nexport function parsePhoneNumber() {\r\n\treturn call(_parsePhoneNumber, arguments)\r\n}\r\n\r\nexport function formatPhoneNumber() {\r\n\treturn call(_formatPhoneNumber, arguments)\r\n}\r\n\r\nexport function formatPhoneNumberIntl() {\r\n\treturn call(_formatPhoneNumberIntl, arguments)\r\n}\r\n\r\nexport function isValidPhoneNumber() {\r\n\treturn call(_isValidPhoneNumber, arguments)\r\n}\r\n\r\nexport function isPossiblePhoneNumber() {\r\n\treturn call(_isPossiblePhoneNumber, arguments)\r\n}\r\n\r\nexport function getCountries() {\r\n\treturn call(_getCountries, arguments)\r\n}\r\n\r\nexport function getCountryCallingCode() {\r\n\treturn call(_getCountryCallingCode, arguments)\r\n}\r\n\r\nexport function isSupportedCountry() {\r\n\treturn call(_isSupportedCountry, arguments)\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAkB;AAClB,IAAAC,qBAAsB;AACtB,IAAAC,qBAAuB;;;ACFvB,IAAAC,gBAAmC;AACnC,IAAAC,qBAAsB;;;ACCtB,mBAA2C;AAC3C,wBAAsB;;;ACaP,SAAf,KAA6BC,OAAOC,OAAOC,WAC3C;AACC,UAAQA,WAAR;IAEC,KAAK;AAGJ,UAAID,QAAQ,GACZ;AAECD,gBAAQA,MAAMG,MAAM,GAAGF,QAAQ,CAAvB,IAA4BD,MAAMG,MAAMF,KAAZ;AAEpCA;MACA;AACD;IAED,KAAK;AAEJD,cAAQA,MAAMG,MAAM,GAAGF,KAAf,IAAwBD,MAAMG,MAAMF,QAAQ,CAApB;AAChC;EAjBF;AAoBA,SAAO;IAAED;IAAOC;EAAT;AACP;;;ACfc,SAAf,MAA8BG,MAAMC,gBAAgBC,iBACpD;AACC,MAAMC,UAAU,CAAA;AAEhB,MAAIC,QAAQ;AAEZ,MAAIC,gCAAgC;AAEpC,MAAIC,QAAQ;AACZ,SAAOA,QAAQN,KAAKO,QACpB;AACC,QAAMC,YAAYN,gBAAgBF,KAAKM,KAAD,GAASF,OAAOD,OAArB;AAEjC,QAAIK,cAAcC,QAClB;AACCL,eAASI;AAET,UAAIP,mBAAmBQ,QACvB;AACC,YAAIR,mBAAmBK,OACvB;AACCD,0CAAgCD,MAAMG,SAAS;QAC/C,WACQN,iBAAiBK,OAC1B;AACCD,0CAAgCD,MAAMG;QACtC;MACA;IACF;AAEDD;EACA;AAGD,MAAIL,mBAAmBQ,QACvB;AAECJ,oCAAgCD,MAAMG;EACtC;AAED,MAAMG,SACN;IACCN;IACAO,OAAQN;EAFT;AAKA,SAAOK;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEM,SAASE,iBAAiBC,QAAQC,QAAQ;AAChD,MAAIC,QAAQ;AAQZ,WAAA,YAAA,gCAAwBD,OAAOE,MAAM,EAAb,CAAxB,GAAA,OAAA,EAAA,QAAA,UAAA,GAAA,QAA0C;AAAA,QAA/BC,YAA+B,MAAA;AACzC,QAAIA,cAAcJ,QAAQ;AACzBE;IACA;EACD;AACD,SAAOA;AACP;;;ACdc,SAAf,YAAoCG,mBAAmBC,UACvD;AAAA,MADiEC,cACjE,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAD+E;AAC/E,MADoFC,oBACpF,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADwG;AAEvG,MAAIC,aAAaJ,kBAAkBK;AAEnC,MAAMC,iBAAiBC,iBAAiB,KAAKP,iBAAN;AACvC,MAAMQ,iBAAiBD,iBAAiB,KAAKP,iBAAN;AAEvC,MAAIS,kBAAkBH,iBAAiBE;AAEvC,SAAOC,kBAAkB,KAAKL,aAAaH,SAASI,QACpD;AACCL,yBAAqBC,SAASG,UAAD,EAAaM,QAAQR,aAAaC,iBAA1C;AAErB,QAAIF,SAASG,UAAD,MAAiB,KAC7B;AACCK;IACA;AAEDL;EACA;AAED,SAAOJ;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTc,SAAf,0BAAwBW,UACxB;AAAA,MADkCC,cAClC,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADgD;AAChD,MADqDC,sBACrD,UAAA,SAAA,IAAA,UAAA,CAAA,IAAA;AACC,MAAI,CAACF,UACL;AACC,WAAO,SAAAG,OAAK;AAAA,aAAK;QAAEC,MAAMD;MAAR;IAAL;EACZ;AAED,MAAME,yBAAyBC,iBAAiBL,aAAaD,QAAd;AAE/C,SAAO,SAASG,OAChB;AACC,QAAI,CAACA,OACL;AACC,aAAO;QAAEC,MAAM;QAAIJ;MAAZ;IACP;AAED,QAAIO,wBAAwB;AAC5B,QAAIC,qBAAqB;AAQzB,aAAA,YAAAC,iCAAwBT,SAASU,MAAM,EAAf,CAAxB,GAAA,OAAA,EAAA,QAAA,UAAA,GAAA,QACA;AAAA,UADWC,YACX,MAAA;AACC,UAAIA,cAAcV,aAClB;AACCO,8BAAsBG;AACtB;MACA;AAEDH,4BAAsBL,MAAMI,qBAAD;AAC3BA;AAMA,UAAIA,0BAA0BJ,MAAMS,QACpC;AAIC,YAAIT,MAAMS,SAASP,wBACnB;AACC;QACA;MACD;IACD;AAED,QAAIH,qBACJ;AACCM,2BAAqBK,YAAaL,oBAAoBR,QAArB;IACjC;AAED,WAAO;MAAEI,MAAMI;MAAoBR;IAA5B;EACP;AACD;;;ACxCc,SAAf,OAA+Bc,OAAOC,OAAOC,WAC7C;AACC,MAAI,OAAOA,cAAc,UACzB;AACCA,gBAAYC,0BAAmBD,SAAD;EAC9B;AAED,MAAA,OAAyBA,UAAUF,KAAD,KAAW,CAAA,GAAvCI,OAAN,KAAMA,MAAMC,WAAZ,KAAYA;AAEZ,MAAID,SAASE,QACb;AACEF,WAAOJ;EACR;AAED,MAAIK,UACJ;AACC,QAAIJ,UAAUK,QACd;AACCL,cAAQG,KAAKG;IACb,OAED;AACC,UAAIC,QAAQ;AACZ,UAAIC,QAAQ;AAEZ,UAAIC,sCAAsC;AAE1C,aAAOF,QAAQJ,KAAKG,UAAUC,QAAQH,SAASE,QAC/C;AAEC,YAAIH,KAAKI,KAAD,MAAYH,SAASG,KAAD,GAC5B;AACC,cAAIP,UAAU,GACd;AACCQ,oBAAQ;AACRR,oBAAQO;AACR;UACA;AAEDE,gDAAsCF;AAEtCP;QACA;AAEDO;MACA;AAID,UAAI,CAACC,OACL;AACCR,gBAAQS,sCAAsC;MAC9C;IACD;EACD;AAED,SAAO;IAAEN;IAAMH;EAAR;AACP;;;AC3FM,SAASU,WAAWC,SAC3B;AACC,SAAOA,QAAQC,aAAa,UAArB;AACP;AAGM,SAASC,aAAaF,SAC7B;AAEC,MAAIA,QAAQG,mBAAmBH,QAAQI,cACvC;AACC;EACA;AAED,SAAO;IAAEC,OAAOL,QAAQG;IAAgBG,KAAKN,QAAQI;EAA9C;AACP;AAGM,IAAMG,OACb;EACCC,WAAY;EACZC,QAAY;AAFb;AAOO,SAASC,aAAaC,OAC7B;AACC,UAAQA,MAAMC,SAAd;IAEC,KAAKL,KAAKC;AACT,aAAO;IAER,KAAKD,KAAKE;AACT,aAAO;EANT;AAQA;AAGM,SAASI,iBAAiBb,SACjC;AACC,SAAOA,QAAQG;AACf;AAGM,SAASW,iBAAiBd,SAASe,gBAC1C;AAEC,MAAIA,mBAAmBC,QACvB;AACC;EACA;AAaD,MAAIC,UAAS,GAAI;AACZC,eAAW,WAAA;AAAA,aAAMlB,QAAQmB,kBAAkBJ,gBAAgBA,cAA1C;IAAN,GAAiE,CAAlE;EACd,OAAM;AACNf,YAAQmB,kBAAkBJ,gBAAgBA,cAA1C;EACA;AACD;AAED,SAASE,YAAY;AAEpB,MAAI,OAAOG,cAAc,aAAa;AACrC,WAAOC,2BAA2BC,KAAKF,UAAUG,SAA1C;EACP;AACD;AAED,IAAMF,6BAA6B;;;AC/B5B,SAASG,SAASC,OAAOC,OAAOC,QAAQC,SAASC,WACxD;AACCC,kBAAgBJ,OAAOC,QAAQC,SAASG,QAAWF,SAApC;AACf;AAmBM,SAASG,UAAUP,OAAOC,OAAOC,QAAQC,SAASC,WACzD;AACC,MAAII,WAAWP,KAAD,GAAS;AACtB;EACA;AAED,MAAMQ,YAAYC,aAAaV,KAAD;AAC9B,UAAQS,WAAR;IAEC,KAAK;IACL,KAAK;AAEJT,YAAMW,eAAN;AAEA,UAAMC,YAAYC,aAAaZ,KAAD;AAG9B,UAAIW,WACJ;AACCE,uBAAeb,OAAOW,SAAR;AACd,eAAOP,gBAAgBJ,OAAOC,QAAQC,SAASG,QAAWF,SAApC;MACtB;AAGD,aAAOC,gBAAgBJ,OAAOC,QAAQC,SAASM,WAAWL,SAApC;IAEvB;EAnBD;AAsBA;AAOD,SAASU,eAAeb,OAAOW,WAC/B;AACC,MAAIG,OAAOd,MAAMe;AACjBD,SAAOA,KAAKE,MAAM,GAAGL,UAAUM,KAAxB,IAAiCH,KAAKE,MAAML,UAAUO,GAArB;AAExClB,QAAMe,QAAQD;AACdK,mBAAiBnB,OAAOW,UAAUM,KAAlB;AAChB;AAcD,SAASb,gBAAgBJ,OAAOC,QAAQC,SAASM,WAAWL,WAC5D;AAGC,MAAA,UAAuBiB,MAAMpB,MAAMe,OAAOM,iBAAiBrB,KAAD,GAASC,MAAvC,GAAtBc,QAAN,QAAMA,OAAOO,QAAb,QAAaA;AAIb,MAAId,WACJ;AACC,QAAMe,mBAAmBC,KAAKT,OAAOO,OAAOd,SAAf;AAE7BO,YAAQQ,iBAAiBR;AACzBO,YAAQC,iBAAiBD;EACzB;AAID,MAAMG,YAAYC,OAAOX,OAAOO,OAAOpB,OAAf;AAExB,MAAMY,OAAOW,UAAUX;AACvBQ,UAAaG,UAAUH;AAMvBtB,QAAMe,QAAQD;AAEdK,mBAAiBnB,OAAOsB,KAAR;AAIhBnB,YAAUY,KAAD;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AR9ID,SAASY,MAAT,MAQGC,KAAK;AAAA,MAPPC,QAOO,KAPPA,OACAC,SAMO,KANPA,OACAC,UAKO,KALPA,QACgBC,iBAIT,KAJPC,gBACAC,YAGO,KAHPA,UACAC,aAEO,KAFPA,WACGC,OACI,yBAAA,MAAA,SAAA;AACP,MAAMC,kBAAcC,qBAAM;AAC1B,MAAMC,aAASC,0BAAY,SAACC,UAAa;AACxCJ,gBAAYK,UAAUD;AACtB,QAAIb,KAAK;AACR,UAAI,OAAOA,QAAQ,YAAY;AAC9BA,YAAIa,QAAD;MACH,OAAM;AACNb,YAAIc,UAAUD;MACd;IACD;EACD,GAAE,CAACb,GAAD,CATuB;AAU1B,MAAMe,gBAAYH,0BAAY,SAACI,OAAU;AACxC,WAAOC,SACND,OACAP,YAAYK,SACZZ,QACAC,SACAG,SALmB;EAOpB,GAAE,CAACG,aAAaP,QAAOC,SAAQG,SAA7B,CAR0B;AAU7B,MAAMY,iBAAaN,0BAAY,SAACI,OAAU;AACzC,QAAIT,YAAW;AACdA,MAAAA,WAAUS,KAAD;IACT;AAGD,QAAIA,MAAMG,kBAAkB;AAC3B;IACA;AACD,WAAOC,UACNJ,OACAP,YAAYK,SACZZ,QACAC,SACAG,SALoB;EAOrB,GAAE,CAACG,aAAaP,QAAOC,SAAQG,WAAUC,UAAvC,CAhB2B;AAkB9B,SACC,aAAAc,QAAA,cAAC,gBAAD,SAAA,CAAA,GACKb,MADL;IAEC,KAAKG;IACL,OAAOR,QAAOmB,aAAarB,KAAD,IAAU,KAAKA,KAA5B,EAAmCsB;IAChD,WAAWL;IACX,UAAUH;EALX,CAAA,CAAA;AAOD;AAEDhB,QAAQsB,aAAAA,QAAMG,WAAWzB,KAAjB;AAERA,MAAM0B,YAAY;;EAEjBvB,OAAOwB,kBAAAA,QAAUC,KAAKC;;EAGtBzB,QAAQuB,kBAAAA,QAAUC,KAAKC;;EAGvBvB,gBAAgBqB,kBAAAA,QAAUG,YAAYD;;EAGtCE,MAAMJ,kBAAAA,QAAUK,OAAOH;;EAGvB3B,OAAOyB,kBAAAA,QAAUK;;EAGjBzB,UAAUoB,kBAAAA,QAAUC,KAAKC;;EAGzBrB,WAAWmB,kBAAAA,QAAUC;EACrBK,OAAON,kBAAAA,QAAUC;EACjBM,SAASP,kBAAAA,QAAUC;AAtBF;AAyBlB5B,MAAMmC,eAAe;;EAEpB7B,gBAAgB;;EAGhByB,MAAM;AALc;AAQrB,IAAA,gBAAe/B;AAEf,SAASuB,aAAarB,OAAO;AAC5B,SAAOA,UAAUkC,UAAalC,UAAU;AACxC;;;ASjHM,SAASmC,oBAAmBC,MAKhC;AAAA,MAJFC,UAAOD,KAAPC,SACAC,gBAAaF,KAAbE,eACAC,yBAAsBH,KAAtBG,wBACAC,YAAQJ,KAARI;AAEA,SAAOH,WAAWC,iBAAiB,CAACC,yBAAsB,IAAAE,OACrDC,sBAAsBL,SAASG,SAAQ,CAAC,IAC5C;AACF;AAEO,SAASG,uBAAuBC,OAAOC,QAAQ;AACrD,MAAIA,QAAQ;AACXD,YAAQA,MAAME,MAAMD,OAAOE,MAAM;AACjC,QAAIH,MAAM,CAAC,MAAM,KAAK;AACrBA,cAAQA,MAAME,MAAM,CAAC;IACtB;EACD;AACA,SAAOF;AACR;;;ACRe,SAAf,2BAAmDI,WAAWC,sBAAsBC,SAAS;AAe5F,MAAIA,WAAWA,QAAQC,YAAY;AAClC;EACD;AAEA,MAAMC,YAAY,SAAZA,WAAaC,WAAc;AAChC,QAAIH,SAAS;AACZ,cAAQG,WAAS;QAChB,KAAK;AACJH,kBAAQC,aAAa;AACrB;MACF;IACD;EACD;AAEA,SAAOG,0BAA0BN,WAAWC,sBAAsBG,SAAS;AAC5E;;;AC3CA,IAAAG,gBAA4B;AAOb,SAAf,uBAA8CC,MAG3C;AAAA,MAFFC,aAASD,KAATC,WACAC,gBAAaF,KAAbE;AAEA,aAAOC,2BAAY,SAACC,OAAU;AAC7B,QAAIA,MAAMC,YAAYC,sBAAsBJ,eAAe;AAK1D,UAAIE,MAAMG,kBAAkBC,kBAAkB;AAC7C,YAAIC,kBAAiBL,MAAMG,MAAM,MAAMG,mCAAmC;AACzEN,gBAAMO,eAAe;AACrB;QACD;MACD;IACD;AACA,QAAIV,YAAW;AACdA,MAAAA,WAAUG,KAAK;IAChB;EACD,GAAG,CACFH,YACAC,aAAa,CACb;AACF;AAEA,IAAMI,qBAAqB;AAI3B,SAASG,kBAAiBG,SAAS;AAClC,SAAOA,QAAQC;AAChB;AAEA,IAAMH,oCAAoC,IAAII;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AZ/BvC,SAASC,YAAYC,iBAC5B;AAQC,WAASC,WAAUC,MAOhBC,KAAK;AAAA,QANPC,aAASF,KAATE,WACAC,UAAOH,KAAPG,SACAC,gBAAaJ,KAAbI,eACAC,yBAAsBL,KAAtBK,wBAAsBC,gBAAAN,KACtBO,UAAAA,YAAQD,kBAAA,SAAGR,kBAAeQ,eACvBE,OAAIC,0BAAAT,MAAAU,UAAA;AAEP,QAAMC,cAASC,2BAAY,SAACC,OAAU;AAErC,UAAMC,YAAY,IAAIC,UAAUZ,SAASI,SAAQ;AACjD,UAAMS,SAASC,oBAAoB;QAClCd;QACAC;QACAC;QACAE,UAAAA;MACD,CAAC;AAED,UAAIW,OAAOJ,UAAUK,MAAMH,SAASH,KAAK;AACzC,UAAIO,WAAWN,UAAUO,YAAY;AACrC,UAAIL,QAAQ;AACXE,eAAOI,uBAAuBJ,MAAMF,MAAM;AAE1C,YAAII,UAAU;AACbA,qBAAWE,uBAAuBF,UAAUJ,MAAM;QACnD;MACD;AACA,aAAO;QACNE;QACAE;MACD;IACD,GAAG,CAACjB,SAASI,SAAQ,CAAC;AAEtB,QAAMgB,aAAaC,uBAAuB;MACzCtB,WAAAA;MACAE;IACD,CAAC;AAED,WACCqB,cAAAA,QAAAC,cAACC,eAAKC,UAAA,CAAA,GACDpB,MAAI;MACRP;MACA4B,OAAOC;MACPnB,QAAQA;MACRT,WAAWqB;IAAW,CAAA,CAAC;EAE1B;AAEAxB,eAAa0B,cAAAA,QAAMM,WAAWhC,UAAU;AAExCA,aAAWiC,YAAY;;;;;;;;IAQtBnB,OAAOoB,mBAAAA,QAAUC,OAAOC;;;;;IAMxBC,UAAUH,mBAAAA,QAAUI,KAAKF;;;;;IAMzBjC,WAAW+B,mBAAAA,QAAUI;;;;;;;;;;;IAYrBlC,SAAS8B,mBAAAA,QAAUC;;;;;;;;;;;IAYnB9B,eAAe6B,mBAAAA,QAAUK;;;;;;;IAQzBjC,wBAAwB4B,mBAAAA,QAAUK;;;;IAKlC/B,UAAU0B,mBAAAA,QAAUM;EACrB;AAEA,SAAOxC;AACR;AAEA,IAAA,qBAAeF,YAAY;;;AapI3B,IAAA2C,gBAAmC;AACnC,IAAAC,qBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf,SAASC,aAAYC,iBAAiB;AAY5C,WAASC,WAAUC,MAUhBC,KAAK;AAAA,QATPC,QAAKF,KAALE,OACAC,YAAQH,KAARG,UACAC,aAASJ,KAATI,WACAC,UAAOL,KAAPK,SACAC,gBAAaN,KAAbM,eACAC,yBAAsBP,KAAtBO,wBAAsBC,gBAAAR,KACtBS,UAAAA,YAAQD,kBAAA,SAAGV,kBAAeU,eAAAE,sBAAAV,KAC1BW,gBAAgBC,SAAKF,wBAAA,SAAG,UAAOA,qBAC5BG,OAAIC,0BAAAd,MAAAe,UAAA;AAEP,QAAMC,SAASC,oBAAoB;MAClCZ;MACAC;MACAC;MACAE,UAAAA;IACD,CAAC;AAED,QAAMS,gBAAYC,2BAAY,SAACC,OAAU;AACxC,UAAIC,WAAWC,2BAA2BF,MAAMG,OAAOrB,KAAK;AAgB5D,UAAImB,aAAanB,OAAO;AACvB,YAAMsB,oBAAoBC,QAAOT,QAAQK,UAAUhB,SAASI,SAAQ;AACpE,YAAIe,kBAAkBE,QAAQN,MAAMG,OAAOrB,KAAK,MAAM,GAAG;AAExDmB,qBAAWA,SAASM,MAAM,GAAG,EAAE;QAChC;MACD;AACAxB,MAAAA,UAASkB,QAAQ;IAClB,GAAG,CACFL,QACAd,OACAC,WACAE,SACAI,SAAQ,CACR;AAED,QAAMmB,aAAaC,uBAAuB;MACzCzB,WAAAA;MACAE;IACD,CAAC;AAED,WACCwB,cAAAA,QAAAC,cAACnB,QAAKoB,UAAA,CAAA,GACDnB,MAAI;MACRZ;MACAC,OAAOuB,QAAOT,QAAQd,OAAOG,SAASI,SAAQ;MAC9CN,UAAUe;MACVd,WAAWwB;IAAW,CAAA,CAAC;EAE1B;AAEA7B,eAAa+B,cAAAA,QAAMG,WAAWlC,UAAU;AAExCA,aAAWmC,YAAY;;;;;;;;IAQtBhC,OAAOiC,mBAAAA,QAAUC,OAAOC;;;;;IAMxBlC,UAAUgC,mBAAAA,QAAUG,KAAKD;;;;;IAMzBjC,WAAW+B,mBAAAA,QAAUG;;;;;;;;;;;IAYrBjC,SAAU8B,mBAAAA,QAAUC;;;;;;;;;;;IAYpB9B,eAAe6B,mBAAAA,QAAUI;;;;;;;IAQzBhC,wBAAwB4B,mBAAAA,QAAUI;;;;IAKlC9B,UAAU0B,mBAAAA,QAAUK;;;;IAKpB7B,gBAAgBwB,mBAAAA,QAAUM;EAC3B;AAEA,SAAO1C;AACR;AAEA,IAAA,qBAAeF,aAAY;AAE3B,SAAS4B,QAAOT,QAAQd,OAAOG,SAASI,WAAU;AACjD,SAAOiC,uBACNC,4BACC3B,SAASd,OACTG,SACAI,SACD,GACAO,MACD;AACD;;;ACvKA,IAAA4B,gBAA4C;AAC5C,IAAAC,qBAAsB;AACtB,wBAAuB;;;ACIR,SAAf,eAAuCC,SAAS;AAC/C,SAAOC,2BAA2BD,QAAQ,CAAD,CAAR,IAAeC,2BAA2BD,QAAQ,CAAD,CAAR;AAC1E;AAOD,SAASC,2BAA2BC,QAAQ;AAC3C,SAAOC,OAAOC,cAAc,SAAU,KAAKF,OAAOG,YAAP,EAAqBC,WAAW,CAAhC,CAApC;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADZc,SAAf,cAAqCC,MAOlC;AAAA,MANFC,QAAKD,KAALC,OACAC,YAAQF,KAARE,UACAC,UAAOH,KAAPG,SACAC,WAAQJ,KAARI,UACAC,WAAQL,KAARK,UACGC,OAAIC,0BAAAP,MAAAQ,UAAA;AAEP,MAAMC,gBAAYC,2BAAY,SAACC,OAAU;AACxC,QAAMV,SAAQU,MAAMC,OAAOX;AAC3BC,IAAAA,UAASD,WAAU,OAAOY,SAAYZ,MAAK;EAC5C,GAAG,CAACC,SAAQ,CAAC;AAEb,MAAMY,qBAAiBC,uBAAQ,WAAM;AACpC,WAAOC,kBAAkBb,SAASF,KAAK;EACxC,GAAG,CAACE,SAASF,KAAK,CAAC;AAInB,SACCgB,cAAAA,QAAAC,cAAA,UAAAC,UAAA,CAAA,GACKb,MAAI;IACRF,UAAUA,YAAYC;IACtBA;IACAJ,OAAOA,SAAS;IAChBC,UAAUO;EAAU,CAAA,GACnBN,QAAQiB,IAAI,SAAAC,OAAA;AAAA,QAAGpB,SAAKoB,MAALpB,OAAOqB,QAAKD,MAALC,OAAOC,UAAOF,MAAPE;AAAO,WACpCN,cAAAA,QAAAC,cAAA,UAAA;MACCM,KAAKD,UAAU,MAAMtB,UAAS;MAC9BA,OAAOsB,UAAU,MAAMtB,UAAS;MAChCG,UAAUmB,UAAU,OAAO;MAC3BE,OAAOF,UAAUG,gBAAgBb;IAAU,GAC1CS,KACM;EAAC,CACT,CACM;AAEV;AAEAK,cAAcC,YAAY;;;;;EAKzB3B,OAAO4B,mBAAAA,QAAUC;;;;;EAMjB5B,UAAU2B,mBAAAA,QAAUE,KAAKC;;EAGzB7B,SAAS0B,mBAAAA,QAAUI,QAAQJ,mBAAAA,QAAUK,MAAM;IAC1CjC,OAAO4B,mBAAAA,QAAUC;IACjBR,OAAOO,mBAAAA,QAAUC;IACjBP,SAASM,mBAAAA,QAAUM;EACpB,CAAC,CAAC,EAAEH;;;;;;EAOJ5B,UAAUyB,mBAAAA,QAAUM;EACpB9B,UAAUwB,mBAAAA,QAAUM;AACrB;AAEA,IAAMT,gBAAgB;EACrBU,UAAU;EACVC,iBAAiB;EACjBC,OAAO;AACR;AAEO,SAASC,sBAAqBC,OASlC;AAAA,MARFvC,QAAKuC,MAALvC,OACAE,UAAOqC,MAAPrC,SACAsC,YAASD,MAATC,WACeC,OAAIF,MAAnBG,eACAC,qBAAkBJ,MAAlBI,oBAAkBC,uBAAAL,MAClBM,gBAAgBC,QAAKF,yBAAA,SAAGG,wBAAqBH,sBAC7CI,eAAYT,MAAZS,cACG3C,OAAIC,0BAAAiC,OAAAU,WAAA;AAEP,MAAMpC,qBAAiBC,uBAAQ,WAAM;AACpC,WAAOC,kBAAkBb,SAASF,KAAK;EACxC,GAAG,CAACE,SAASF,KAAK,CAAC;AAEnB,SACCgB,cAAAA,QAAAC,cAAA,OAAA;IAAKuB,WAAU;EAAmB,GACjCxB,cAAAA,QAAAC,cAACS,eAAaR,UAAA,CAAA,GACTb,MAAI;IACRL;IACAE;IACAsC,eAAWU,kBAAAA,SAAW,2BAA2BV,SAAS;EAAE,CAAA,CAAC,GAG5DQ,gBAAgBhD,SACjBgB,cAAAA,QAAAC,cAAA,OAAA;IAAKuB,WAAU;EAA8B,GAC3CW,eAAmBnD,KAAK,CACrB,GAIL,EAAEgD,gBAAgBhD,UAClBgB,cAAAA,QAAAC,cAACwB,MAAI;IACJ,eAAA;IACAW,SAASpD;IACTqB,OAAOR,kBAAkBA,eAAeQ;IACxCgC,aAAaL,eAAe,IAAIpC;EAAU,CAAC,GAG7CI,cAAAA,QAAAC,cAAC6B,OAAK,IAAC,CACH;AAEP;AAEAR,sBAAsBX,YAAY;;EAEjCe,eAAed,mBAAAA,QAAU0B;;EAGzBT,gBAAgBjB,mBAAAA,QAAU0B;;EAG1BN,cAAcpB,mBAAAA,QAAUM;AACzB;AAEA,SAASa,wBAAwB;AAChC,SAAO/B,cAAAA,QAAAC,cAAA,OAAA;IAAKuB,WAAU;EAA8B,CAAC;AACtD;AAEA,SAASzB,kBAAkBb,SAASF,OAAO;AAC1C,WAAAuD,YAAAC,iCAAqBtD,OAAO,GAAAuD,OAAA,EAAAA,QAAAF,UAAA,GAAAG,QAAE;AAAA,QAAnBC,SAAMF,MAAAzD;AAChB,QAAI,CAAC2D,OAAOrC,WAAWqC,OAAO3D,UAAUA,OAAO;AAC9C,aAAO2D;IACR;EACD;AACD;;;AE/IA,IAAAC,gBAAkB;AAClB,IAAAC,qBAAsB;AACtB,IAAAC,qBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKR,SAAf,cAAqCC,MAMlC;AAAA,MALFC,UAAOD,KAAPC,SACAC,cAAWF,KAAXE,aACAC,QAAKH,KAALG,OACAC,UAAOJ,KAAPI,SACGC,OAAIC,0BAAAN,MAAAO,UAAA;AAEP,MAAIJ,SAASA,MAAMF,OAAO,GAAG;AAC5B,WAAOE,MAAMF,OAAO,EAAE;MAAEO,OAAON;IAAY,CAAC;EAC7C;AACA,SACCO,cAAAA,QAAAC,cAAA,OAAAC,UAAA,CAAA,GACKN,MAAI;IACRO,KAAKV;IACLW,MAAMX,cAAcY,SAAY;IAChCC,KAAKX,QAAQY,QAAQ,QAAQf,OAAO,EAAEe,QAAQ,QAAQf,QAAQgB,YAAY,CAAC;EAAE,CAAA,CAAC;AAEjF;AAEAC,cAAcC,YAAY;;;EAGzBlB,SAASmB,mBAAAA,QAAUC,OAAOC;;EAG1BpB,aAAakB,mBAAAA,QAAUC,OAAOC;;;;;;;;EAS9BnB,OAAOiB,mBAAAA,QAAUG,SAASH,mBAAAA,QAAUI,WAAW;;;EAI/CpB,SAASgB,mBAAAA,QAAUC,OAAOC;AAC3B;;;AC9CA,IAAAG,gBAAkB;AAClB,IAAAC,qBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,SAAf,kBAAyCC,MAA2B;AAAA,MAAxBC,cAAWD,KAAXC,aAAgBC,OAAIC,0BAAAH,MAAAI,UAAA;AAC/D,MAAIH,gBAAgB,GAAG;AACtB,WAAOI,cAAAA,QAAAC,cAACC,sBAAyBL,IAAM;EACxC,OAAO;AACN,WAAOG,cAAAA,QAAAC,cAACE,sBAAyBN,IAAM;EACxC;AACD;AAEAO,kBAAkBC,YAAY;EAC7BC,OAAOC,mBAAAA,QAAUC,OAAOC;EACxBb,aAAaW,mBAAAA,QAAUG;AACxB;AAKA,SAASP,qBAAoBQ,OAAqB;AAAA,MAAlBL,QAAKK,MAALL,OAAUT,OAAIC,0BAAAa,OAAAC,WAAA;AAC7C,SACCZ,cAAAA,QAAAC,cAAA,OAAAY,UAAA,CAAA,GACKhB,MAAI;IACRiB,OAAM;IACNC,SAAQ;EAAW,CAAA,GACnBf,cAAAA,QAAAC,cAAA,SAAA,MAAQK,KAAa,GACrBN,cAAAA,QAAAC,cAAA,KAAA;IACCe,WAAU;IACVC,QAAO;IACPC,MAAK;IACLC,aAAY;IACZC,kBAAiB;EAAI,GACrBpB,cAAAA,QAAAC,cAAA,QAAA;IAAMoB,eAAc;IAAQC,GAAE;EAAsD,CAAC,GACrFtB,cAAAA,QAAAC,cAAA,QAAA;IAAMqB,GAAE;EAA0D,CAAC,GACnEtB,cAAAA,QAAAC,cAAA,QAAA;IAAMsB,IAAG;IAAKC,IAAG;IAAKC,IAAG;IAAKC,IAAG;EAAI,CAAC,GACtC1B,cAAAA,QAAAC,cAAA,QAAA;IAAMsB,IAAG;IAAKC,IAAG;IAAIC,IAAG;IAAKC,IAAG;EAAI,CAAC,GACrC1B,cAAAA,QAAAC,cAAA,QAAA;IAAMoB,eAAc;IAAQC,GAAE;EAAuG,CAAC,GACtItB,cAAAA,QAAAC,cAAA,QAAA;IAAMoB,eAAc;IAAQC,GAAE;EAAsH,CAAC,CACnJ,GACHtB,cAAAA,QAAAC,cAAA,QAAA;IACCe,WAAU;IACVC,QAAO;IACPC,MAAK;IACLI,GAAE;EAA0L,CAAC,CAC1L;AAEP;AAEAnB,qBAAqBE,YAAY;EAChCC,OAAOC,mBAAAA,QAAUC,OAAOC;AACzB;AAKA,SAASP,qBAAoByB,OAAqB;AAAA,MAAlBrB,QAAKqB,MAALrB,OAAUT,OAAIC,0BAAA6B,OAAAC,WAAA;AAC7C,SACC5B,cAAAA,QAAAC,cAAA,OAAAY,UAAA,CAAA,GACKhB,MAAI;IACRiB,OAAM;IACNC,SAAQ;EAAW,CAAA,GACnBf,cAAAA,QAAAC,cAAA,SAAA,MAAQK,KAAa,GACrBN,cAAAA,QAAAC,cAAA,KAAA;IACCe,WAAU;IACVC,QAAO;IACPC,MAAK;IACLC,aAAY;IACZE,eAAc;EAAO,GACrBrB,cAAAA,QAAAC,cAAA,QAAA;IAAMqB,GAAE;EAAwC,CAAC,GACjDtB,cAAAA,QAAAC,cAAA,QAAA;IAAMqB,GAAE;EAA0H,CAAC,GACnItB,cAAAA,QAAAC,cAAA,QAAA;IAAMqB,GAAE;EAAgE,CAAC,GACzEtB,cAAAA,QAAAC,cAAA,QAAA;IAAMqB,GAAE;EAAmE,CAAC,GAC5EtB,cAAAA,QAAAC,cAAA,QAAA;IAAMsB,IAAG;IAAOC,IAAG;IAAOC,IAAG;IAAOC,IAAG;EAAO,CAAC,GAC/C1B,cAAAA,QAAAC,cAAA,QAAA;IAAMsB,IAAG;IAAOC,IAAG;IAAQC,IAAG;IAAQC,IAAG;EAAO,CAAC,CAC/C,GACH1B,cAAAA,QAAAC,cAAA,QAAA;IACCe,WAAU;IACVC,QAAO;IACPC,MAAK;IACLI,GAAE;EAA4M,CAAC,CAC5M;AAEP;AAEApB,qBAAqBG,YAAY;EAChCC,OAAOC,mBAAAA,QAAUC,OAAOC;AACzB;;;AC/Ee,SAAf,aAAqCoB,OAAO;AAC3C,MAAIA,MAAMC,SAAS,GAAG;AACrB,WAAO;EACR;AACA,MAAID,MAAM,CAAC,MAAM,KAAK;AACrB,WAAO;EACR;AACA,MAAIE,IAAI;AACR,SAAOA,IAAIF,MAAMC,QAAQ;AACxB,QAAME,YAAYH,MAAMI,WAAWF,CAAC;AACpC,QAAIC,aAAa,MAAMA,aAAa,IAAI;IACvC,OACM;AACN,aAAO;IACR;AACAD;EACD;AACA,SAAO;AACR;AAEO,SAASG,mBAAmBL,OAAO;AACzC,MAAI,CAACM,aAAaN,KAAK,GAAG;AACzBO,YAAQC,MAAM,2FAA2FR,KAAK;EAC/G;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBO,SAASS,mBAAmBC,SAASC,OAAO;AAClD,MAAI,CAACA,OAAO;AACX,WAAOD;EACR;AACA,MAAME,eAAe,CAAA;AACrB,MAAMC,kBAAkB,CAAA;AACxB,MAAIC,WAAWF;AAAY,MAAAG,QAAA,SAAAA,SACE;AAAA,QAAlBC,UAAOC,MAAAC;AACjB,QAAIF,YAAY,KAAK;AACpBF,eAASK,KAAK;QAAEC,SAAS;MAAK,CAAC;IAChC,WAAWJ,YAAY,SAASA,YAAY,KAAK;AAChDF,iBAAWD;IACZ,OAAO;AACN,UAAIQ;AACJ,UAAIL,YAAY,MAAM;AACrBK,sBAAcC;MACf,OAAO;AACND,sBAAcL;MACf;AAEA,UAAMO,QAAQb,QAAQc,QAAQd,QAAQe,OAAO,SAAAC,SAAM;AAAA,eAAIA,QAAOR,UAAUG;MAAW,CAAA,EAAE,CAAC,CAAC;AAEvF,UAAMK,SAAShB,QAAQa,KAAK;AAE5Bb,cAAQiB,OAAOJ,OAAO,CAAC;AAEvBT,eAASK,KAAKO,MAAM;IACrB;EACD;AArBA,WAAAE,YAAAC,iCAAsBlB,KAAK,GAAAM,OAAA,EAAAA,QAAAW,UAAA,GAAAE,QAAA;AAAAf,UAAA;EAAA;AAsB3B,SAAOH,aAAamB,OAAOrB,OAAO,EAAEqB,OAAOlB,eAAe;AAC3D;AAEO,SAASmB,2BAA2BC,gBAAgBC,WAAU;AACpE,MAAID,gBAAgB;AACnBA,qBAAiBA,eAAeR,OAAO,SAACC,QAAW;AAClD,cAAQA,QAAM;QACb,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACJ,iBAAO;QACR;AACC,iBAAOS,4BAA4BT,QAAQQ,SAAQ;MACrD;IACD,CAAC;AACD,QAAID,eAAeG,SAAS,GAAG;AAC9B,aAAOH;IACR;EACD;AACD;AAEO,SAASE,4BAA4BE,SAASH,WAAU;AAC9D,MAAII,mBAAmBD,SAASH,SAAQ,GAAG;AAC1C,WAAO;EACR,OAAO;AACNK,YAAQC,MAAK,sBAAAT,OAAuBM,OAAO,CAAE;AAC7C,WAAO;EACR;AACD;AAEO,SAASI,sBAAsBC,WAAWR,WAAU;AAC1D,MAAIQ,WAAW;AACdA,gBAAYA,UAAUjB,OAAO,SAAAY,SAAO;AAAA,aAAIF,4BAA4BE,SAASH,SAAQ;IAAC,CAAA;AACtF,QAAIQ,UAAUN,WAAW,GAAG;AAC3BM,kBAAYpB;IACb;EACD;AACA,SAAOoB;AACR;;;AChFA,IAAAC,gBAAkB;AAClB,IAAAC,qBAAsB;AACtB,IAAAC,qBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKhB,SAASC,2BAA0BC,MAKvC;AAAA,MAJFC,QAAKD,KAALC,OACAC,UAAOF,KAAPE,SACeC,iBAAaH,KAA5BI,eACmBC,qBAAiBL,KAApCM;AAEA,WAASC,YAAWC,OAKjB;AAAA,QAJFC,UAAOD,MAAPC,SACAC,QAAKF,MAALE,OACAC,cAAWH,MAAXG,aACGC,OAAIC,0BAAAL,OAAAM,UAAA;AAKP,QAAMC,eAAeV,uBAAsBW,oBAA2BL,cAAcM;AACpF,WACCC,cAAAA,QAAAC,cAAA,OAAAC,UAAA,CAAA,GACKR,MAAI;MACRS,eAAWC,mBAAAA,SAAW,yBAAyB;QAC9C,iCAAiCP,iBAAiB;QAClD,iCAAiCN;MAClC,CAAC;IAAE,CAAA,GAEFA,UAEAS,cAAAA,QAAAC,cAAChB,gBAAa;MACbM;MACAc,aAAab;MACbT;MACAC;MACAmB,WAAU;IAA0B,CAAC,IAEtCH,cAAAA,QAAAC,cAACd,oBAAiB;MACjBmB,OAAOd;MACPC,aAAaI;MACbM,WAAU;IAA0B,CAAC,CAEnC;EAEP;AAEAd,cAAYkB,YAAY;IACvBhB,SAASiB,mBAAAA,QAAUC;IACnBjB,OAAOgB,mBAAAA,QAAUC,OAAOC;IACxBjB,aAAae,mBAAAA,QAAUG;EACxB;AAEA,SAAOtB;AACR;AAEA,IAAA,sBAAeR,2BAA2B;;EAEzCG,SAAS;EACTE,eAAe0B;EACfxB,mBAAmBU;AACpB,CAAC;;;AC/DD,IAAAe,iBAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0C7B,SAASC,aAAaC,MAAMC,OAAO;AACxC,WAAAC,YAAAC,iCAAkBH,IAAI,GAAAI,OAAA,EAAAA,QAAAF,UAAA,GAAAG,QAAE;AAAA,QAAbC,MAAGF,MAAAH;AACZ,QAAIK,KAAK;AACPC,kBAAYD,KAAKL,KAAK;IACxB;EACF;AACF;AAOA,SAASM,YAAYD,KAAKL,OAAO;AAC/B,MAAI,OAAOK,QAAQ,YAAY;AAC7BA,QAAIL,KAAK;EACX,OAAO;AACLK,QAAIE,UAAUP;EAChB;AACF;;;AC7DA,IAAAQ,qBAAsB;AAEf,IAAMC,WAAWC,mBAAAA,QAAUC,MAAM;EACvCC,uBAAwBF,mBAAAA,QAAUG,OAAOC;EACzCC,WAAYL,mBAAAA,QAAUG,OAAOC;AAC9B,CAAC;AAEM,IAAME,SAASN,mBAAAA,QAAUO,SAASP,mBAAAA,QAAUQ,MAAM;;;ACA1C,SAAf,kCAA0DC,SAASC,WAAU;AAE5E,MAAIC,SAAS,MAAMC,sBAAsBH,SAASC,SAAQ;AAoB1D,SAAOC;AACR;;;ACbO,SAASE,sBAAqBC,MAQlC;AAAA,MAPFC,QAAKD,KAALC,OACAC,cAAWF,KAAXE,aACAC,iBAAcH,KAAdG,gBACAC,gBAAaJ,KAAbI,eACAC,YAASL,KAATK,WACAC,WAAQN,KAARM,UACAC,YAAQP,KAARO;AAEA,MAAIC;AAIJ,MAAIN,eAAeA,YAAYM,SAAS;AAEvCA,cAAUN,YAAYM;EACvB,WAAWL,gBAAgB;AAC1B,QAAI,CAACF,SAASQ,2BAA2BR,OAAOE,gBAAgBI,SAAQ,GAAG;AAC1EC,gBAAUL;IACX;EACD;AAGA,MAAIE,aAAaA,UAAUK,QAAQF,OAAO,IAAI,GAAG;AAChDA,cAAUG;EACX;AAMA,MAAI,CAACH,WAAWF,YAAYD,aAAaA,UAAUO,SAAS,GAAG;AAC9DJ,cAAUJ,cAAc;EAEzB;AAEA,SAAOI;AACR;AASO,SAASK,wBAAuBC,OAQpC;AAAA,MAPFT,YAASS,MAATT,WACAU,eAAYD,MAAZC,cACAC,yBAAsBF,MAAtBE,wBAGAC,wBAAqBH,MAArBG,uBACgBC,kBAAeJ,MAA/BK;AAGA,MAAI,CAACD,iBAAiB;AACrBA,sBAAkBC;EACnB;AAGA,MAAMC,uBAAuBf,UAAUgB,IAAI,SAACb,SAAO;AAAA,WAAM;MACxDP,OAAOO;;;;;;;MAOPc,OAAOP,aAAaP,OAAO,KAAKA;IACjC;EAAC,CAAC;AAGFY,uBAAqBG,KAAK,SAACC,GAAGC,GAAC;AAAA,WAAKP,gBAAgBM,EAAEF,OAAOG,EAAEH,OAAOL,qBAAqB;EAAC,CAAA;AAG5F,MAAID,wBAAwB;AAC3BI,yBAAqBM,QAAQ;MAC5BJ,OAAOP,aAAaY;IACrB,CAAC;EACF;AAEA,SAAOP;AACR;AAUO,SAASQ,kBAAiB3B,OAAOM,WAAU;AACjD,SAAOsB,iBAAkB5B,SAAS,IAAIM,SAAQ;AAC/C;AAaO,SAASuB,6BAA6B5B,aAAa;AACzD,SAAOA,YAAY6B,eAAe,EAAEC,QAAQ,OAAO,EAAE;AACtD;AAWO,SAASC,4BAA4BC,aAAWC,OAKpD;AAAA,MAJFC,cAAWD,MAAXC,aACAC,aAAUF,MAAVE,YACA9B,YAAQ4B,MAAR5B,UACA+B,oBAAiBH,MAAjBG;AAEA,MAAIF,gBAAgBC,YAAY;AAC/B,WAAOH;EACR;AAIA,MAAI,CAACA,aAAa;AACjB,QAAII,mBAAmB;AACtB,aAAO;IACR,OAAO;AACN,UAAID,YAAY;AAGf,eAAOE,kCAAkCF,YAAY9B,SAAQ;MAC9D;AACA,aAAO;IACR;EACD;AAMA,MAAI8B,YAAY;AAMf,QAAIH,YAAY,CAAC,MAAM,KAAK;AAG3B,UAAII,mBAAmB;AAetB,YAAIJ,YAAYxB,QAAQ,MAAM8B,sBAAsBH,YAAY9B,SAAQ,CAAC,MAAM,GAAG;AACjF,iBAAOkC,wBAAwBP,aAAaG,YAAY9B,SAAQ;QACjE;AAOA,eAAO;MAMR;AAEA,UAAI6B,aAAa;AAChB,YAAMM,mBAAmBH,kCAAkCF,YAAY9B,SAAQ;AAC/E,YAAI2B,YAAYxB,QAAQgC,gBAAgB,MAAM,GAAG;AAChD,iBAAOR;QACR,OAAO;AACN,iBAAOQ;QACR;MACD,OAAO;AACN,YAAMC,eAAeJ,kCAAkCF,YAAY9B,SAAQ;AAG3E,YAAI2B,YAAYxB,QAAQiC,YAAY,MAAM,GAAG;AAC5C,iBAAOT;QACR;AAIA,eAAOS;MACR;IAaD;EACD,OAEK;AAEJ,QAAIT,YAAY,CAAC,MAAM,KAAK;AAiB3B,aAAOU,KAAKV,aAAaE,aAAa7B,SAAQ,KAAK;IACpD;EACD;AAEA,SAAO2B;AACR;AASO,SAASU,KAAKC,QAAQrC,SAASD,WAAU;AAC/C,MAAI,CAACsC,QAAQ;AACZ;EACD;AAEA,MAAIA,OAAO,CAAC,MAAM,KAAK;AAEtB,QAAIA,WAAW,KAAK;AACnB;IACD;AASA,QAAMC,YAAY,IAAIC,UAAUvC,SAASD,SAAQ;AACjDuC,cAAUE,MAAMH,MAAM;AAGtB,WAAOC,UAAUG,eAAe;EACjC;AAMA,MAAI,CAACzC,SAAS;AACb;EACD;AACA,MAAM0C,sCAAsCC,mCAAmCN,QAAQrC,SAASD,SAAQ;AAcvG,SAAA,IAAA6C,OAAWZ,sBAAsBhC,SAASD,SAAQ,CAAC,EAAA6C,OAAGF,uCAAuC,EAAE;AAEjG;AAUO,SAASG,WAAWR,QAAQrC,SAASD,WAAU;AACrD,MAAM+C,gCAAgCH,mCAAmCN,QAAQrC,SAASD,SAAQ;AAClG,MAAI+C,+BAA+B;AAClC,QAAMC,sBAAsBD,8BAA8B1C,SAAS4C,mBAAmBhD,SAASD,SAAQ;AACvG,QAAIgD,sBAAsB,GAAG;AAC5B,aAAOV,OAAOY,MAAM,GAAGZ,OAAOjC,SAAS2C,mBAAmB;IAC3D;EACD;AACA,SAAOV;AACR;AAEA,SAASW,mBAAmBhD,SAASD,WAAU;AAE9CA,EAAAA,YAAW,IAAImD,SAASnD,SAAQ;AAChCA,EAAAA,UAASoD,oBAAoBnD,OAAO;AAEpC,SAAOD,UAASqD,cAAcC,gBAAgB,EAAEtD,UAASqD,cAAcC,gBAAgB,EAAEjD,SAAS,CAAC;AACpG;AAeO,SAASkD,+BAA+BC,mBAAiBC,OAO7D;AAAA,MANFxD,UAAOwD,MAAPxD,SACAH,YAAS2D,MAAT3D,WACAF,iBAAc6D,MAAd7D,gBACA8D,8BAA2BD,MAA3BC,6BACA3D,WAAQ0D,MAAR1D,UACAC,YAAQyD,MAARzD;AAOA,MAAIwD,sBAAsB,KAAK;AAE9B,WAAOvD;EACR;AAEA,MAAM0D,kBAAkBC,yDAAyDJ,mBAAmBxD,SAAQ;AAK5G,MAAI2D,iBAAiB;AACpB,QAAI,CAAC7D,aAAcA,UAAUK,QAAQwD,eAAe,KAAK,GAAI;AAC5D,aAAOA;IACR,OAAO;AACN,aAAOvD;IACR;EACD,WAGSH,SAAS;AAIjB,QAAIC,2BAA2BsD,mBAAmBvD,SAASD,SAAQ,GAAG;AAGrE,UAAI0D,+BAA+BxD,2BAA2BsD,mBAAmBE,6BAA6B1D,SAAQ,GAAG;AACxH,eAAO0D;MACR,WAAW9D,kBAAkBM,2BAA2BsD,mBAAmB5D,gBAAgBI,SAAQ,GAAG;AACrG,eAAOJ;MACR,OAAO;AACN,YAAI,CAACG,UAAU;AAEd,iBAAOK;QACR;MACD;IACD,OAAO;AAKN,UAAI,CAACL,UAAU;AACd,eAAOK;MACR;IACD;EACD;AAGA,SAAOH;AACR;AAiBO,SAAS4D,oBAAoBlC,aAAWmC,OAY5C;AAAA,MAXFC,kBAAeD,MAAfC,iBACA9D,UAAO6D,MAAP7D,SACAL,iBAAckE,MAAdlE,gBACA8D,8BAA2BI,MAA3BJ,6BACAM,kBAAeF,MAAfE,iBACAnE,gBAAaiE,MAAbjE,eACAC,YAASgE,MAAThE,WACAmE,gBAAaH,MAAbG,eACAC,iBAAcJ,MAAdI,gBACAC,6BAA0BL,MAA1BK,4BACAnE,YAAQ8D,MAAR9D;AAKA,MAAIiE,iBAAiBE,+BAA+B,OAAO;AAC1D,QAAIlE,SAAS;AAGZ,UAAMmE,SAASpC,kCAAkC/B,SAASD,SAAQ;AAGlE,UAAI2B,YAAYxB,QAAQiE,MAAM,MAAM,GAAG;AACtC,YAAI1E;AAkBJ,YAAM2E,iEAAiE1C,eAAeA,YAAY,CAAC,MAAM;AACzG,YAAI0C,gEAAgE;AAEnE1C,wBAAcyC,SAASzC;AACvBjC,mBAAQ2C,KAAKV,aAAa1B,SAASD,SAAQ;QAC5C,OAAO;AAMN2B,wBAAcyC;QACf;AACA,eAAO;UACNzC;UACAjC,OAAAA;UACAO;QACD;MACD;IACD;EACD;AAOA,MAAIgE,kBAAkB,SAAShE,WAAW0B,eAAeA,YAAY,CAAC,MAAM,KAAK;AAChFA,kBAAc2C,0CAA0C3C,aAAa1B,SAASD,SAAQ;EACvF;AAGA,MAAI2B,eAAe1B,WAAWiE,gBAAgB;AAC7CvC,kBAAcmB,WAAWnB,aAAa1B,SAASD,SAAQ;EACxD;AAQA,MAAI2B,eAAeA,YAAY,CAAC,MAAM,QAAQ,CAAC1B,WAAWgE,gBAAgB;AACzEtC,kBAAc,MAAMA;EACrB;AAoBA,MAAI,CAACA,eAAeoC,mBAAmBA,gBAAgB,CAAC,MAAM,KAAK;AAClE,QAAIE,eAAe;AAClBhE,gBAAUG;IACX,OAAO;AACNH,gBAAUL;IACX;EACD;AAIA,MAAI+B,gBAAgB,OAAOoC,mBAAmBA,gBAAgB,CAAC,MAAM,OAAOA,gBAAgB1D,SAAS,IAAIA,QAAQ;AAChHJ,cAAUG;EACX;AAGA,MAAIV;AACJ,MAAIiC,aAAa;AAChB,QAAIA,YAAY,CAAC,MAAM,KAAK;AAC3B,UAAIA,gBAAgB,KAAK;AACxBjC,gBAAQU;MACT,WAAWH,WAAW+B,kCAAkC/B,SAASD,SAAQ,EAAEG,QAAQwB,WAAW,MAAM,GAAG;AAUtGjC,gBAAQU;MACT,OAAO;AACNV,gBAAQ2C,KAAKV,aAAa1B,SAASD,SAAQ;MAC5C;IACD,OAAO;AACNN,cAAQ2C,KAAKV,aAAa1B,SAASD,SAAQ;IAC5C;EACD;AAKA,MAAIN,OAAO;AACVO,cAAUsD,+BAA+B7D,OAAO;MAC/CO;MACAH;MACAF;MACA8D;;;MAGA3D,UAAU;MACVC,UAAAA;IACD,CAAC;AAMD,QAAIiE,kBAAkB,SAAShE,WAAW0B,eAAeA,YAAY,CAAC,MAAM,KAAK;AAChFA,oBAAc2C,0CAA0C3C,aAAa1B,SAASD,SAAQ;AAEtFN,cAAQ2C,KAAKV,aAAa1B,SAASD,SAAQ;IAC5C;EACD;AAEA,MAAI,CAACC,WAAW+D,iBAAiB;AAChC/D,cAAUL,kBAAkBC,cAAc;EAC3C;AAEA,SAAO;;;;IAIN8B;IACA1B;IACAP;EACD;AACD;AAEA,SAAS4E,0CAA0C7B,OAAOxC,SAASD,WAAU;AAG5E,MAAIyC,MAAMtC,QAAQ6B,kCAAkC/B,SAASD,SAAQ,CAAC,MAAM,GAAG;AAE9E,QAAMuE,YAAY,IAAI/B,UAAUvC,SAASD,SAAQ;AAEjDuE,cAAU9B,MAAMA,KAAK;AAErB,QAAM9C,cAAc4E,UAAUC,UAAU;AACxC,QAAI7E,aAAa;AAGhB,aAAOA,YAAY6B,eAAe,EAAEC,QAAQ,OAAO,EAAE;IACtD,OAAO;AACN,aAAO;IACR;EACD,OAAO;AAEN,WAAOgB,MAAMhB,QAAQ,OAAO,EAAE;EAC/B;AACD;AAQO,SAASmC,yDAAyDtB,QAAQtC,WAAU;AAC1F,MAAMuE,YAAY,IAAI/B,UAAU,MAAMxC,SAAQ;AAC9CuE,YAAU9B,MAAMH,MAAM;AAMtB,SAAOiC,UAAUE,WAAW;AAC7B;AASO,SAAS7D,eAAeK,GAAGC,GAAGwD,SAAS;AAM5C,MAAIC,OAAOC,UAAUC,eAAe;AAClC,WAAO5D,EAAE4D,cAAc3D,GAAGwD,OAAO;EACnC;AAEA,SAAOzD,IAAIC,IAAI,KAAMD,IAAIC,IAAI,IAAI;AACnC;AASO,SAASgB,wBAAwBI,QAAQrC,SAASD,WAAU;AAGlE,MAAIC,SAAS;AACZ,QAAM6E,2BAA2B,MAAM7C,sBAAsBhC,SAASD,SAAQ;AAG9E,QAAIsC,OAAOjC,SAASyE,yBAAyBzE,QAAQ;AACpD,UAAIyE,yBAAyB3E,QAAQmC,MAAM,MAAM,GAAG;AACnD,eAAO;MACR;IACD,OAAO;AACN,UAAIA,OAAOnC,QAAQ2E,wBAAwB,MAAM,GAAG;AACnD,eAAOxC,OAAOY,MAAM4B,yBAAyBzE,MAAM;MACpD;IACD;EACD;AAIA,WAAA0E,KAAA,GAAAC,eAAmCC,OAAOC,KAAKlF,UAASmF,qBAAqB,GAACJ,KAAAC,aAAA3E,QAAA0E,MAAE;AAA3E,QAAMK,uBAAoBJ,aAAAD,EAAA;AAC9B,QAAIzC,OAAOnC,QAAQiF,oBAAoB,MAAM,IAAI/E,QAAQ;AACxD,aAAOiC,OAAOY,MAAM,IAAI7C,SAAS+E,qBAAqB/E,MAAM;IAC7D;EACD;AAEA,SAAO;AACR;AAYO,SAASuC,mCAAmCN,QAAQrC,SAASD,WAAU;AAE7E,MAAMuE,YAAY,IAAI/B,UAAUvC,SAASD,SAAQ;AAEjDuE,YAAU9B,MAAMH,MAAM;AAEtB,MAAM3C,cAAc4E,UAAUC,UAAU;AACxC,SAAO7E,eAAeA,YAAY0F;AACnC;AAQO,SAASnF,2BAA2BoC,QAAQrC,SAASD,WAAU;AACrE,MAAMsF,wBAAwBtD,kCAAkC/B,SAASD,SAAQ;AACjF,MAAIuF,IAAI;AACR,SAAOA,IAAIjD,OAAOjC,UAAUkF,IAAID,sBAAsBjF,QAAQ;AAC7D,QAAIiC,OAAOiD,CAAC,MAAMD,sBAAsBC,CAAC,GAAG;AAC3C,aAAO;IACR;AACAA;EACD;AACA,SAAO;AACR;AAMO,SAASC,sBAAqBC,OAOlC;AAAA,MANF/F,QAAK+F,MAAL/F,OACAC,cAAW8F,MAAX9F,aACAC,iBAAc6F,MAAd7F,gBACAqE,gBAAawB,MAAbxB,eACAlC,oBAAiB0D,MAAjB1D,mBACA/B,YAAQyF,MAARzF;AAQA,OAAKiE,kBAAkB,SAASlC,sBAAsBpC,eAAeA,YAAYM,SAAS;AACzF,WAAOsB,6BAA6B5B,WAAW;EAChD;AAIA,MAAI,CAACD,SAASuE,iBAAiBrE,gBAAgB;AAC9C,WAAOoC,kCAAkCpC,gBAAgBI,SAAQ;EAClE;AACA,SAAON;AACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvwBe,SAAf,gDAAwEgG,OAAOC,WAAWC,OAAO;AAChG,MACCC,YAUGH,MAVHG,UACAC,YASGJ,MATHI,WACgBC,oBAQbL,MARHM,gBACOC,WAOJP,MAPHQ,OACOC,WAMJT,MANHU,OACAC,gBAKGX,MALHW,eAGAC,mCAEGZ,MAFHY,kCACAC,qBACGb,MADHa;AAGD,MACiBC,qBAGbb,UAHHK,gBACOS,YAEJd,UAFHO,OACOQ,YACJf,UADHS;AAGD,MACCO,UAQGf,MARHe,SACAT,QAOGN,MAPHM,OAKAU,0BAEGhB,MAFHgB,yBACAC,8BACGjB,MADHiB;AAGD,MAAMC,yBAAyB,SAAzBA,wBAA0BC,YAAU;AAAA,WAAKC,sBAAqBC,cAAAA,cAAA,CAAA,GAChEF,UAAU,GAAA,CAAA,GAAA;MACbV;MACAa,mBAAmBZ,oCAAoCC,uBAAuB;MAC9EV,UAAAA;IAAQ,CAAA,CACR;EAAC;AAaF,MAAIM,aAAaO,WAAW;AAC3B,WAAO;MACNS,aAAaL,uBAAuB;QACnCZ,OAAOkB;QACPpB,gBAAgBD;MACjB,CAAC;MACDG,OAAOkB;MACPT,SAASZ;MACTc,6BAA6BO;MAC7BR,yBAAyBQ;IAC1B;EACD;AAcA,MAAIrB,sBAAsBS,oBAAoB;AAC7C,QAAMa,+BAA+B,CAACtB,qBAAqBuB,4BAA4BvB,mBAAmBF,SAAQ;AAClH,QAAM0B;;MAEL,CAACrB;;;;MAKAG,iBAAiBH,UAAUY,uBAAuB;QAClDZ,OAAOkB;QACPpB,gBAAgBQ;MACjB,CAAC;;AAIF,QAAMgB,wBAAwB,CAACvB,YAAYsB;AAC3C,QAAI,CAACX,2BAA2BS,gCAAgCG,uBAAuB;AACtF,aAAO;QACNb,SAASZ;;;;;;QAMToB,aAAaL,uBAAuB;UACnCZ,OAAOkB;UACPpB,gBAAgBD;QACjB,CAAC;;QAEDG,OAAOkB;MACR;IACD;EACD;AAaA,MAAI,CAACK,eAAexB,UAAUQ,SAAS,KAAK,CAACgB,eAAexB,UAAUC,KAAK,GAAG;AAC7E,QAAIwB;AACJ,QAAIC;AACJ,QAAI1B,UAAU;AAIb,UAAIA,UAAU;AACb2B,2BAAmB3B,QAAQ;MAC5B;AACAyB,oBAAcG,kBAAiB5B,UAAUJ,SAAQ;AACjD,UAAMiC,qBAAqBC,sBAAsBjC,WAAWD,SAAQ;AACpE,UAAI6B,eAAeA,YAAYf,SAAS;AAGvC,YAAI,CAACmB,sBAAsBA,mBAAmBE,QAAQN,YAAYf,OAAO,KAAK,GAAG;AAChFgB,0BAAgBD,YAAYf;QAC7B;MACD,OAAO;AACNgB,wBAAgBM,+BAA+BhC,UAAU;UACxDU,SAASS;UACTtB,WAAWgC;UACXjC,UAAAA;QACD,CAAC;AAiBD,YAAI,CAAC8B,eAAe;AACnB,cAAI5B,mBAAmB;AACtB,gBAAIE,SAAS+B,QAAQE,kCAAkCnC,mBAAmBF,SAAQ,CAAC,MAAM,GAAG;AAC3F8B,8BAAgB5B;YACjB;UACD;QACD;MACD;IACD;AAEA,QAAIoC;AACJ,QAAIlC,UAAU;AAGb,UAAIY,6BAA6B;AAChC,YAAMuB,uDACLT,gBACGd,gCAAgCc,gBAChCU,2BAA2BpC,UAAUY,6BAA6BhB,SAAQ;AAE9E,YAAIuC,sDAAsD;AACzD,cAAI,CAACT,eAAe;AACnBA,4BAAgBd;UACjB;QACD,OAAO;AACNsB,mDAAyC;YACxCtB,6BAA6BO;UAC9B;QACD;MACD;IACD,OAAO;AAGNe,+CAAyC;QACxCtB,6BAA6BO;QAC7BR,yBAAyBQ;MAC1B;IACD;AAEA,WAAAH,cAAAA,cAAA,CAAA,GACIkB,sCAAsC,GAAA,CAAA,GAAA;MACzChB,aAAaL,uBAAuB;QACnCY;QACAxB,OAAOD;QACPD,gBAAgBD;MACjB,CAAC;MACDG,OAAOD;MACPU,SAASV,WAAW0B,gBAAgB5B;IAAiB,CAAA;EAEvD;AAOD;AAEO,SAAS0B,eAAea,QAAQC,QAAQ;AAc9C,MAAID,WAAW,MAAM;AACpBA,aAASlB;EACV;AACA,MAAImB,WAAW,MAAM;AACpBA,aAASnB;EACV;AACA,SAAOkB,WAAWC;AACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;A1BpN0H,IAEpHC,oBAAiB,SAAAC,sBAAA;AACtB,WAAAD,mBAAYE,OAAO;AAAA,QAAAC;AAAAC,oBAAA,MAAAJ,kBAAA;AAClBG,YAAAE,WAAA,MAAAL,oBAAA,CAAME,KAAK,CAAA;AA2HZI,IAAAA,iBAAAH,OAAA,eACc,SAACI,UAAa;AAC3BC,mBAAa,CAACL,MAAKD,MAAMO,UAAUN,MAAKM,QAAQ,GAAGF,QAAQ;IAC5D,CAAC;AAgDDD,IAAAA,iBAAAH,OAAA,+BAC8B,SAACO,SAAY;AAC1C,UAAQC,YAAaR,MAAKD,MAAlBS;AACR,aAAOC,4BAA4BF,SAASC,SAAQ;IACrD,CAAC;AAEDL,IAAAA,iBAAAH,OAAA,mBACkB,SAACU,YAAe;AACjC,UAAAC,cAKIX,MAAKD,OAJRa,gBAAaD,YAAbC,eACAJ,YAAQG,YAARH,UACAK,YAAQF,YAARE,UACAC,+BAA4BH,YAA5BG;AAGD,UAAAC,cAGIf,MAAKgB,OAFKC,kBAAeF,YAA5BG,aACSC,cAAWJ,YAApBR;AAMD,UAAMa,iBAAiBC,4BAA4BJ,iBAAiB;QACnEE;QACAT;QACAF,UAAAA;;;QAGAc,mBAAmB,CAACV;MACrB,CAAC;AAED,UAAMW,WAAWC,KAAKJ,gBAAgBV,YAAYF,SAAQ;AAG1D,UAAIM,8BAA8B;AACjCd,cAAKM,SAASmB,QAAQC,MAAM;MAC7B;AAOA1B,YAAK2B,SAAS;QACbpB,SAASG;QACTkB,6BAA6BlB;QAC7BmB,yBAAyB;QACzBX,aAAaE;QACbU,OAAOP;MACR,GACA,WAAM;AAKLV,QAAAA,UAASU,QAAQ;MAClB,CAAC;IACF,CAAC;AAEDpB,IAAAA,iBAAAH,OAAA,YAKW,SAAC+B,cAAiB;AAC5B,UAAAC,eAQIhC,MAAKD,OAPRkC,iBAAcD,aAAdC,gBACApB,YAAQmB,aAARnB,UACAqB,yBAAsBF,aAAtBE,wBACAtB,gBAAaoB,aAAbpB,eACAuB,iBAAcH,aAAdG,gBACAC,6BAA0BJ,aAA1BI,4BACA5B,YAAQwB,aAARxB;AAGD,UAAA6B,eAKIrC,MAAKgB,OAJRsB,YAASD,aAATC,WACarB,kBAAeoB,aAA5BnB,aACSqB,2BAAwBF,aAAjC9B,SACAqB,8BAA2BS,aAA3BT;AAGD,UAAAY,uBAQIC,oBAAoBV,cAAc;QACrCd;QACAV,SAASgC;QACTG,iBAAiB,CAACR;QAClBD;QACAL;QACAe,eAAe,SAAAA,gBAAA;AAAA,iBAAM3C,MAAK4C,yBAAyB;YAAEN;UAAU,CAAC;QAAC;QACjEA;QACA1B;QACAuB;QACAC;QACA5B,UAAAA;MACD,CAAC,GAfAU,cAAWsB,qBAAXtB,aACAX,UAAOiC,qBAAPjC,SACAuB,QAAKU,qBAALV;AAeD,UAAMe,cAAc;QACnB3B;QACAY;QACAvB;MACD;AAGA,UAAIqB,+BAA+BE,SAAS,CAACgB,2BAA2BhB,OAAOF,6BAA6BpB,SAAQ,GAAG;AACtHqC,oBAAYjB,8BAA8BmB;MAC3C;AAEA,UAAIX,+BAA+B,OAAO;AAUzC,YAAI,CAACN,SAASZ,gBAAgBlB,MAAKgB,MAAME,aAAa;AAErD2B,sBAAYG,gBAAgB,CAAC;QAC9B;MACD;AAEAhD,YAAK2B;QACJkB;;;;;QAKA,WAAA;AAAA,iBAAMhC,UAASiB,KAAK;QAAC;MACtB;IACD,CAAC;AAED3B,IAAAA,iBAAAH,OAAA,YACW,WAAA;AAAA,aAAMA,MAAK2B,SAAS;QAAEsB,WAAW;MAAK,CAAC;IAAC,CAAA;AAEnD9C,IAAAA,iBAAAH,OAAA,WACU,WAAA;AAAA,aAAMA,MAAK2B,SAAS;QAAEsB,WAAW;MAAM,CAAC;IAAC,CAAA;AAAA9C,IAAAA,iBAAAH,OAAA,WAEzC,SAACkD,OAAU;AACpBlD,YAAKmD,SAAS;AACd,UAAQC,UAAYpD,MAAKD,MAAjBqD;AACR,UAAIA,SAAS;AACZA,gBAAQF,KAAK;MACd;IACD,CAAC;AAAA/C,IAAAA,iBAAAH,OAAA,UAEQ,SAACkD,OAAU;AACnB,UAAQG,SAAWrD,MAAKD,MAAhBsD;AACRrD,YAAKsD,QAAQ;AACb,UAAID,QAAQ;AACXA,eAAOH,KAAK;MACb;IACD,CAAC;AAAA/C,IAAAA,iBAAAH,OAAA,kBAEgB,SAACkD,OAAU;AAC3BlD,YAAKmD,SAAS;AAEd,UAAQI,qBAAuBvD,MAAKD,MAA5BwD;AACR,UAAIA,oBAAoB;AACvB,YAAQH,UAAYG,mBAAZH;AACR,YAAIA,SAAS;AACZA,kBAAQF,KAAK;QACd;MACD;IACD,CAAC;AAAA/C,IAAAA,iBAAAH,OAAA,iBAEe,SAACkD,OAAU;AAC1BlD,YAAKsD,QAAQ;AAEb,UAAQC,qBAAuBvD,MAAKD,MAA5BwD;AACR,UAAIA,oBAAoB;AACvB,YAAQF,SAAWE,mBAAXF;AACR,YAAIA,QAAQ;AACXA,iBAAOH,KAAK;QACb;MACD;IACD,CAAC;AAtWAlD,UAAKM,WAAWkD,eAAAA,QAAMC,UAAU;AAEhC,QAAAC,eAUI1D,MAAKD,OATR+B,SAAK4B,aAAL5B,OACA6B,UAAMD,aAANC,QACA/C,iBAAa8C,aAAb9C,eACAsB,0BAAsBwB,aAAtBxB,wBAGA0B,mCAAgCF,aAAhCE,kCACAC,qBAAkBH,aAAlBG,oBACArD,YAAQkD,aAARlD;AAGD,QAAAsD,eAGI9D,MAAKD,OAFRkC,kBAAc6B,aAAd7B,gBACAK,aAASwB,aAATxB;AAID,QAAIL,iBAAgB;AACnB,UAAI,CAACjC,MAAKS,4BAA4BwB,eAAc,GAAG;AACtDA,0BAAiBc;MAClB;IACD;AAKA,QAAIjB,QAAO;AACViC,yBAAmBjC,MAAK;IACzB;AAGAQ,iBAAY0B,sBAAsB1B,YAAW9B,SAAQ;AAErD,QAAMyD,cAAcC,kBAAiBpC,QAAOtB,SAAQ;AAEpDR,UAAKmE,cAAcC,2BAA2BpE,MAAKD,KAAK;AAExD,QAAMsE,qBAAqBC,sBAAsB;MAChDxC,OAAAA;MACAmC;MACAhC,gBAAAA;MACAsC,UAAU,CAACrC;MACXI,WAAWA,cAAakC,aAAahE,SAAQ;MAC7CmC,eAAe,SAAAA,gBAAA;AAAA,eAAM3C,MAAK4C,yBAAyB;UAAEN,WAAAA;QAAU,CAAC;MAAC;MACjE9B,UAAAA;IACD,CAAC;AAEDR,UAAKgB,QAAQ;;MAEZjB,OAAOC,MAAKD;;MAGZQ,SAAS8D;;;;;;;MAQT/B,WAAAA;;;;;;;;;;;MAYApB,aAAauD,sBAAsB;QAClC3C,OAAAA;QACAmC;QACAhC,gBAAAA;QACArB,eAAAA;QACAU,mBAAmBsC,oCAAoCC,uBAAuB;QAC9ErD,UAAAA;MACD,CAAC;;;;;;;;MASDsB,OAAAA;IACD;AAAC,WAAA9B;EACF;AAAC0E,YAAA7E,oBAAAC,oBAAA;AAAA,SAAA6E,aAAA9E,oBAAA,CAAA;IAAA+E,KAAA;IAAA9C,OAED,SAAA+C,oBAAoB;AACnB,UAAQC,kBAAoB,KAAK/E,MAAzB+E;AACR,UAAM7C,iBAAmB,KAAKlC,MAAxBkC;AACN,UAAiB8C,kBAAoB,KAAK/D,MAAlCT;AACR,UAAIuE,iBAAiB;AACpB,YAAI7C,gBAAgB;AACnB,cAAI,CAAC,KAAKxB,4BAA4BwB,cAAc,GAAG;AACtDA,6BAAiBc;UAClB;QACD;AACA,YAAIgC,oBAAoB9C,gBAAgB;AACvC6C,0BAAgBC,eAAe;QAChC;MACD;IACD;EAAC,GAAA;IAAAH,KAAA;IAAA9C,OAED,SAAAkD,mBAAmBC,WAAWC,WAAW;AACxC,UAAQJ,kBAAoB,KAAK/E,MAAzB+E;AACR,UAAQvE,UAAY,KAAKS,MAAjBT;AAER,UAAIuE,mBAAmBvE,YAAY2E,UAAU3E,SAAS;AACrDuE,wBAAgBvE,OAAO;MACxB;IACD;EAAC,GAAA;IAAAqE,KAAA;IAAA9C,OAQD,SAAAqD,yBAAAC,MAAuC;AAAA,UAAb9C,YAAS8C,KAAT9C;AACzB,UAAA+C,eAQI,KAAKtF,OAPRa,gBAAayE,aAAbzE,eACAwB,6BAA0BiD,aAA1BjD,4BACAkD,sBAAmBD,aAAnBC,qBACApD,yBAAsBmD,aAAtBnD,wBACAyB,UAAM0B,aAAN1B,QACA4B,UAAOF,aAAPE,SACA/E,YAAQ6E,aAAR7E;AAED,aAAO,KAAKgF,4BAA4B,WAAM;AAC7C,eAAOC,mBACNN,wBAAwB;UACvB7C,WAAWA,aAAakC,aAAahE,SAAQ;UAC7CkF,cAAc/B;UACdzB,wBAAyBtB,iBAAiBwB,+BAA+B,QAAS,QAAQF;UAC1FyD,uBAAuBJ;;QAExB,CAAC,GACDK,2BAA2BN,qBAAqB9E,SAAQ,CACzD;MACD,GAAG,CACF8B,WACAgD,qBACApD,wBACAyB,SACAnD,SAAQ,CACR;IACF;EAAC,GAAA;IAAAoE,KAAA;IAAA9C,OAED,SAAA0D,4BAA4BK,WAAWC,cAAc;AACpD,UACC,CAAC,KAAKC,wCACN,CAACC,eAAeF,cAAc,KAAKC,oCAAoC,GACtE;AACD,aAAKE,2BAA2BJ,UAAU;AAC1C,aAAKE,uCAAuCD;MAC7C;AACA,aAAO,KAAKG;IACb;EAAC,GAAA;IAAArB,KAAA;IAAA9C,OAED,SAAAc,yBAAAsD,OAAwC;AAAA,UAAb5D,YAAS4D,MAAT5D;AAC1B,UAAM6D,iBAAiB,KAAKhB,wBAAwB;QAAE7C;MAAU,CAAC;AACjE,aAAO6D,eAAe,CAAC,EAAErE;IAC1B;EAAC,GAAA;IAAA8C,KAAA;IAAA9C,OA2MD,SAAAsE,SAAS;AACR,UAAAC,eA+CI,KAAKtG,OA7CRuG,OAAID,aAAJC,MACAC,WAAQF,aAARE,UACAC,WAAQH,aAARG,UACAC,eAAYJ,aAAZI,cACAC,QAAKL,aAALK,OACAC,YAASN,aAATM,WAGArG,WAAQ+F,aAAR/F,UACAsG,iBAAcP,aAAdO,gBACAC,mBAAgBR,aAAhBQ,kBACAC,aAAUT,aAAVS,YAGwBC,yBAAsBV,aAA9CW,wBACAzD,qBAAkB8C,aAAlB9C,oBAGoB0D,qBAAkBZ,aAAtCa,oBACAC,0BAAuBd,aAAvBc,yBAGAlF,iBAAcoE,aAAdpE,gBACWmF,oBAAiBf,aAA5B/D,WACAgD,sBAAmBe,aAAnBf,qBACA3B,UAAM0C,aAAN1C,QACA0D,QAAKhB,aAALgB,OACAC,gBAAajB,aAAbiB,eACAC,UAAOlB,aAAPkB,SACArF,yBAAsBmE,aAAtBnE,wBACAsF,oBAAiBnB,aAAjBmB,mBAGA5D,mCAAgCyC,aAAhCzC,kCACAC,qBAAkBwC,aAAlBxC,oBACAiB,kBAAeuB,aAAfvB,iBACA3C,iBAAckE,aAAdlE,gBACAC,6BAA0BiE,aAA1BjE,4BACAtB,+BAA4BuF,aAA5BvF,8BACA2G,QAAKpB,aAALoB,OACAjH,YAAQ6F,aAAR7F,UACAI,gBAAayF,aAAbzF,eACA2E,UAAOc,aAAPd,SAEGmC,OAAIC,0BAAAtB,cAAAuB,UAAA;AAGR,UAAAC,eAKI,KAAK7G,OAJRT,UAAOsH,aAAPtH,SACA+B,YAASuF,aAATvF,WACApB,cAAW2G,aAAX3G,aACA+B,YAAS4E,aAAT5E;AAGD,UAAM6E,iBAAiBhB,aAAaiB,qBAAaC;AAEjD,UAAMC,uBAAuB,KAAK9C,wBAAwB;QAAE7C;MAAU,CAAC;AAEvE,aACCkB,eAAAA,QAAA0E,cAACjB,oBAAkBkB,UAAA;QAClBzB;QACAC,eAAWyB,mBAAAA,SAAWzB,WAAW,cAAc;UAC9C,qBAAqB1D;UACrB,wBAAwBsD;UACxB,wBAAwBC;QACzB,CAAC;MAAE,GACCW,uBAAuB,GAG3B3D,eAAAA,QAAA0E,cAACnB,wBAAsBoB,UAAA;QACtB7B,MAAMA,OAAI,GAAA+B,OAAM/B,MAAI,SAAA,IAAYvD;QAChC,cAAYY,QAAOpD;MAAQ,GACvBgD,oBAAkB;QACtBzB,OAAOvB;QACP+H,SAASL;QACTpH,UAAU,KAAKiE;QACf1B,SAAS,KAAKmF;QACdlF,QAAQ,KAAKmF;QACbjC,UAAUA,YAAahD,sBAAsBA,mBAAmBgD;QAChEC,UAAUA,YAAajD,sBAAsBA,mBAAmBiD;QAChEiC,eAAe,KAAKtE;MAAY,CAAA,CAAC,GAGlCX,eAAAA,QAAA0E,cAACJ,gBAAcK,UAAA;QACdO,KAAK,KAAKC;QACVC,MAAK;QACLnC;MAA2B,GACvBI,kBACAa,MAAI;QACR9G,eAAeA,gBAAgB,OAAOmC;QACtC8F,wBAAwBjI,gBAAgB,OAAOmC;QAC/CuD;QACA9F,UAAUA;QACVD;QACAuB,OAAOZ,eAAe;QACtBL,UAAU,KAAKA;QACfuC,SAAS,KAAKA;QACdC,QAAQ,KAAKA;QACbkD;QACAC;QACAI;QACAD,eAAWyB,mBAAAA,SACV,mBACAvB,oBAAoBA,iBAAiBF,WACrCe,KAAKf,SACN;MAAE,CAAA,CAAC,CACe;IAEtB;EAAC,CAAA,GAAA,CAAA;IAAA/B,KAAA;IAAA9C;;;;;;MAvHD,SAAAgH,yBAAgC/I,OAAOiB,OAAO;AAC7C,eAAA+H,eAAA;;UAEChJ;QAAK,GACFiJ,gDAAgDjJ,OAAOiB,MAAMjB,OAAOiB,KAAK,CAAC;MAE/E;;EAAC,CAAA,CAAA;AAAA,EAvX8BwC,eAAAA,QAAMyF,aAAa;AA4enD,IAAMC,mBAAmB1F,eAAAA,QAAM2F,WAAW,SAACpJ,OAAO2I,KAAG;AAAA,SACpDlF,eAAAA,QAAA0E,cAACrI,mBAAiBsI,UAAA,CAAA,GAAKiB,iBAAiBrJ,KAAK,GAAC;IAAEO,UAAUoI;EAAI,CAAA,CAAC;AAAC,CAChE;AAEDQ,iBAAiBG,YAAY;;;;;;;;;;EAU5BvH,OAAOwH,mBAAAA,QAAUC;;;;;;;;EASjB1I,UAAUyI,mBAAAA,QAAUE,KAAKC;;;;;EAMzBrG,SAASkG,mBAAAA,QAAUE;;;;;EAMnBnG,QAAQiG,mBAAAA,QAAUE;;;;;EAMlBjD,UAAU+C,mBAAAA,QAAUI;;;;;EAMpBlD,UAAU8C,mBAAAA,QAAUI;;;;;;;;;;;;;;;;;;;;;;EAuBpBjD,cAAc6C,mBAAAA,QAAUC;;;;;;;;;;;;;;;;;;;;;EAsBxB1F,oBAAoByF,mBAAAA,QAAUK,MAAM,CAAC,UAAU,CAAC;;;EAIhD/F,kCAAkC0F,mBAAAA,QAAUI;;;;;;;;EAS5CzH,gBAAgBqH,mBAAAA,QAAUC;;;;;;;;EAS1BjH,WAAWgH,mBAAAA,QAAUM,QAAQN,mBAAAA,QAAUC,MAAM;;;;;;;;;;;EAY7C5F;;;;;;;;;;;;;;EAeA4B,SAAS+D,mBAAAA,QAAUO,UAAU,CAC5BP,mBAAAA,QAAUC,QACVD,mBAAAA,QAAUM,QAAQN,mBAAAA,QAAUC,MAAM,CAAC,CACnC;;;;;;;;;;;;;;;;;;;;;;;EAyBDhC,SAAS+B,mBAAAA,QAAUC;;;;;;;;;;;;;;;;;;;;;;;EAwBnBlC,OAAOiC,mBAAAA,QAAUQ,SAASR,mBAAAA,QAAUS,WAAW;;;;;;;;;;;EAY/CzC,eAAegC,mBAAAA,QAAUS;;;;EAKzB7H,wBAAwBoH,mBAAAA,QAAUI;;;;;;;;;EAUlClC,mBAAmB8B,mBAAAA,QAAUS;;;;;;;;;;;;;EAc7BzE,qBAAqBgE,mBAAAA,QAAUM,QAAQN,mBAAAA,QAAUC,MAAM;;;;EAKvD7C,OAAO4C,mBAAAA,QAAUU;;;;EAKjBrD,WAAW2C,mBAAAA,QAAUC;;;;;;;;;;;;;;;;;;EAmBrBvC,wBAAwBsC,mBAAAA,QAAUS;;;;;;EAOlCxG,oBAAoB+F,mBAAAA,QAAUU;;;;;;;;;;;;;;EAe9BpD,gBAAgB0C,mBAAAA,QAAUS;;;;EAK1BlD,kBAAkByC,mBAAAA,QAAUU;;;;;;;;;EAU5B9C,oBAAoBoC,mBAAAA,QAAUS;;;;EAK9B5C,yBAAyBmC,mBAAAA,QAAUU;;;;;;;;;;;;;EAcnClD,YAAYwC,mBAAAA,QAAUI;;;;;;EAOtB9I,eAAe0I,mBAAAA,QAAUI;;;;;EAMzBvH,gBAAgBmH,mBAAAA,QAAUI;;;;;;EAO1BtH,4BAA4BkH,mBAAAA,QAAUI;;;;;;;EAQtClJ;;;;;;;;;;;;;EAcAsE,iBAAiBwE,mBAAAA,QAAUE;;;;;;;;;;EAW3B1I,8BAA8BwI,mBAAAA,QAAUI;AACzC;AAEA,IAAMO,eAAe;;;;EAIpBxD,cAAc;;;;EAKdO,wBAAwBkD;;;;EAKxB5C,eAAe6C;;;;;EAMf5C,SAAS;;;;EAKTC,mBAAmB4C;;;;EAKnBxD,gBAAgB;;;;EAKhBM,oBAAoB;;;;;;;;;;;;;;EAepBO,OAAO6B,mBAAAA,QAAUe;;;;;;;EASjBvD,YAAY;;;;;EAMZ5E,wBAAwB;;;;;;EAOxBE,4BAA4B;;;;;;;;;;EAW5BtB,8BAA8B;AAC/B;AAEA,SAASsI,iBAAiBrJ,OAAO;AAChCA,UAAKgJ,eAAA,CAAA,GAAQhJ,KAAK;AAElB,WAAW6E,OAAOqF,cAAc;AAC/B,QAAIlK,MAAM6E,GAAG,MAAM7B,QAAW;AAC7BhD,YAAM6E,GAAG,IAAIqF,aAAarF,GAAG;IAC9B;EACD;AAEA,SAAO7E;AACR;AAEA,IAAA,gCAAemJ;AAEf,SAASlD,eAAesE,GAAGC,GAAG;AAC7B,MAAID,EAAEE,WAAWD,EAAEC,QAAQ;AAC1B,WAAO;EACR;AACA,MAAIC,IAAI;AACR,SAAOA,IAAIH,EAAEE,QAAQ;AACpB,QAAIF,EAAEG,CAAC,MAAMF,EAAEE,CAAC,GAAG;AAClB,aAAO;IACR;AACAA;EACD;AACA,SAAO;AACR;;;;;;;;;;;A2Bt/Be,SAAf,kBAA0CC,OAAOC,SAAQC,WAAU;AAClE,MAAI,CAACA,WAAU;AACd,QAAIC,SAAOF,OAAM,MAAK,UAAU;AAC/BC,MAAAA,YAAWD;AACXA,MAAAA,UAAS;IACV;EACD;AACA,MAAI,CAACD,OAAO;AACX,WAAO;EACR;AACA,MAAMI,cAAcC,iBAAiBL,OAAOE,SAAQ;AACpD,MAAI,CAACE,aAAa;AACjB,WAAO;EACR;AAGA,UAAQH,SAAM;IACb,KAAK;AACJA,MAAAA,UAAS;AACT;IACD,KAAK;AACJA,MAAAA,UAAS;AACT;EACF;AACA,SAAOG,YAAYH,OAAOA,OAAM;AACjC;AAEO,SAASK,sBAAsBN,OAAOE,WAAU;AACtD,SAAOK,kBAAkBP,OAAO,iBAAiBE,SAAQ;AAC1D;;;ACvCA,IAAAM,iBAAkB;AAClB,IAAAC,sBAAsB;;;ACDtB,IAAO,kBAAQ;AAAA,EACb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vPO,SAASC,iBAAiBC,iBAAiB;AACjD,MAAMC,oBAAoBC,eAAAA,QAAMC,WAAW,SAAAC,MAIxCC,KAAG;AAAA,QAAAC,gBAAAF,KAHLG,UAAAA,YAAQD,kBAAA,SAAGN,kBAAeM,eAAAE,cAAAJ,KAC1BK,QAAAA,UAAMD,gBAAA,SAAGE,kBAAaF,aACnBG,OAAIC,0BAAAR,MAAAS,UAAA;AAAA,WAEPX,eAAAA,QAAAY,cAACC,+BAAUC,UAAA,CAAA,GACNL,MAAI;MACRN;MACAE,UAAUA;MACVE,QAAQA;IAAO,CAAA,CACf;EAAC,CACF;AAEDR,oBAAkBgB,YAAY;IAC7BV;IACAE;EACD;AAEA,SAAOR;AACR;AAEA,IAAA,uCAAeF,iBAAiB;;;AEnBhC,SAAS,KAAK,MAAM,YAAY;AAC/B,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,UAAU;AAChD,OAAK,KAAK,yBAAQ;AAClB,SAAO,KAAK,MAAM,MAAM,IAAI;AAC7B;AAEA,IAAO,cAAQ,iBAAiB,yBAAQ;AAEjC,SAASmB,oBAAmB;AAClC,SAAO,KAAK,kBAAmB,SAAS;AACzC;AAEO,SAASC,qBAAoB;AACnC,SAAO,KAAK,mBAAoB,SAAS;AAC1C;AAEO,SAASC,yBAAwB;AACvC,SAAO,KAAK,uBAAwB,SAAS;AAC9C;AAEO,SAASC,sBAAqB;AACpC,SAAO,KAAK,oBAAqB,SAAS;AAC3C;AAEO,SAASC,yBAAwB;AACvC,SAAO,KAAK,uBAAwB,SAAS;AAC9C;AAEO,SAASC,gBAAe;AAC9B,SAAO,KAAK,cAAe,SAAS;AACrC;AAEO,SAASC,yBAAwB;AACvC,SAAO,KAAK,uBAAwB,SAAS;AAC9C;AAEO,SAASC,sBAAqB;AACpC,SAAO,KAAK,oBAAqB,SAAS;AAC3C;", "names": ["import_react", "import_prop_types", "import_classnames", "import_react", "import_prop_types", "value", "caret", "operation", "slice", "text", "caret_position", "parse_character", "context", "value", "focused_input_character_index", "index", "length", "character", "undefined", "result", "caret", "count_occurences", "symbol", "string", "count", "split", "character", "retained_template", "template", "placeholder", "empty_placeholder", "cut_before", "length", "opening_braces", "count_occurences", "closing_braces", "dangling_braces", "replace", "template", "placeholder", "should_close_braces", "value", "text", "characters_in_template", "count_occurences", "value_character_index", "filled_in_template", "_createForOfIteratorHelperLoose", "split", "character", "length", "close_braces", "value", "caret", "formatter", "template_formatter", "text", "template", "undefined", "length", "index", "found", "possibly_last_input_character_index", "isReadOnly", "element", "hasAttribute", "getSelection", "selectionStart", "selectionEnd", "start", "end", "Keys", "Backspace", "Delete", "getOperation", "event", "keyCode", "getCaretPosition", "setCaretPosition", "caret_position", "undefined", "isAndroid", "setTimeout", "setSelectionRange", "navigator", "ANDROID_USER_AGENT_REG_EXP", "test", "userAgent", "onChange", "event", "input", "_parse", "_format", "on_change", "formatInputText", "undefined", "onKeyDown", "isReadOnly", "operation", "getOperation", "preventDefault", "selection", "getSelection", "eraseSelection", "text", "value", "slice", "start", "end", "setCaretPosition", "parse", "getCaretPosition", "caret", "newValueAndCaret", "edit", "formatted", "format", "Input", "ref", "value", "parse", "format", "InputComponent", "inputComponent", "onChange", "onKeyDown", "rest", "internalRef", "useRef", "setRef", "useCallback", "instance", "current", "_onChange", "event", "onInputChange", "_onKeyDown", "defaultPrevented", "onInputKeyDown", "React", "isEmptyValue", "text", "forwardRef", "propTypes", "PropTypes", "func", "isRequired", "elementType", "type", "string", "onCut", "onPaste", "defaultProps", "undefined", "getInputValuePrefix", "_ref", "country", "international", "withCountryCallingCode", "metadata", "concat", "getCountryCallingCode", "removeInputValuePrefix", "value", "prefix", "slice", "length", "character", "prevParsedCharacters", "context", "ignoreRest", "emitEvent", "eventName", "parsePhoneNumberCharacter", "import_react", "_ref", "onKeyDown", "international", "useCallback", "event", "keyCode", "BACKSPACE_KEY_CODE", "target", "HTMLInputElement", "getCaretPosition", "AFTER_LEADING_PLUS_CARET_POSITION", "preventDefault", "element", "selectionStart", "length", "createInput", "defaultMetadata", "InputSmart", "_ref", "ref", "onKeyDown", "country", "international", "withCountryCallingCode", "_ref$metadata", "metadata", "rest", "_objectWithoutProperties", "_excluded", "format", "useCallback", "value", "formatter", "AsYouType", "prefix", "getInputValuePrefix", "text", "input", "template", "getTemplate", "removeInputValuePrefix", "_onKeyDown", "useInputKeyDownHandler", "React", "createElement", "Input", "_extends", "parse", "parsePhoneNumberCharacter", "forwardRef", "propTypes", "PropTypes", "string", "isRequired", "onChange", "func", "bool", "object", "import_react", "import_prop_types", "createInput", "defaultMetadata", "InputBasic", "_ref", "ref", "value", "onChange", "onKeyDown", "country", "international", "withCountryCallingCode", "_ref$metadata", "metadata", "_ref$inputComponent", "inputComponent", "Input", "rest", "_objectWithoutProperties", "_excluded", "prefix", "getInputValuePrefix", "_onChange", "useCallback", "event", "newValue", "parseIncompletePhoneNumber", "target", "newValueFormatted", "format", "indexOf", "slice", "_onKeyDown", "useInputKeyDownHandler", "React", "createElement", "_extends", "forwardRef", "propTypes", "PropTypes", "string", "isRequired", "func", "bool", "object", "elementType", "removeInputValuePrefix", "formatIncompletePhoneNumber", "import_react", "import_prop_types", "country", "getRegionalIndicatorSymbol", "letter", "String", "fromCodePoint", "toUpperCase", "charCodeAt", "_ref", "value", "onChange", "options", "disabled", "readOnly", "rest", "_objectWithoutProperties", "_excluded", "onChange_", "useCallback", "event", "target", "undefined", "selectedOption", "useMemo", "getSelectedOption", "React", "createElement", "_extends", "map", "_ref2", "label", "divider", "key", "style", "DIVIDER_STYLE", "CountrySelect", "propTypes", "PropTypes", "string", "func", "isRequired", "arrayOf", "shape", "bool", "fontSize", "backgroundColor", "color", "CountrySelectWithIcon", "_ref3", "className", "Icon", "iconComponent", "getIconAspectRatio", "_ref3$arrowComponent", "arrowComponent", "Arrow", "DefaultArrowComponent", "unicodeFlags", "_excluded2", "classNames", "getUnicodeFlagIcon", "country", "aspectRatio", "elementType", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "option", "import_react", "import_prop_types", "import_classnames", "_ref", "country", "countryName", "flags", "flagUrl", "rest", "_objectWithoutProperties", "_excluded", "title", "React", "createElement", "_extends", "alt", "role", "undefined", "src", "replace", "toLowerCase", "FlagComponent", "propTypes", "PropTypes", "string", "isRequired", "objectOf", "elementType", "import_react", "import_prop_types", "_ref", "aspectRatio", "rest", "_objectWithoutProperties", "_excluded", "React", "createElement", "InternationalIcon1x1", "InternationalIcon3x2", "InternationalIcon", "propTypes", "title", "PropTypes", "string", "isRequired", "number", "_ref2", "_excluded2", "_extends", "xmlns", "viewBox", "className", "stroke", "fill", "strokeWidth", "strokeMiterlimit", "strokeLinecap", "d", "x1", "y1", "x2", "y2", "_ref3", "_excluded3", "value", "length", "i", "character", "charCodeAt", "validateE164Number", "isE164Number", "console", "error", "sortCountryOptions", "options", "order", "optionsOnTop", "optionsOnBottom", "appendTo", "_loop", "element", "_step", "value", "push", "divider", "countryCode", "undefined", "index", "indexOf", "filter", "option", "splice", "_iterator", "_createForOfIteratorHelperLoose", "done", "concat", "getSupportedCountryOptions", "countryOptions", "metadata", "isCountrySupportedWithError", "length", "country", "isSupportedCountry", "console", "error", "getSupportedCountries", "countries", "import_react", "import_prop_types", "import_classnames", "createCountryIconComponent", "_ref", "flags", "flagUrl", "FlagComponent", "flagComponent", "InternationalIcon", "internationalIcon", "CountryIcon", "_ref2", "country", "label", "aspectRatio", "rest", "_objectWithoutProperties", "_excluded", "_aspectRatio", "DefaultInternationalIcon", "undefined", "React", "createElement", "_extends", "className", "classNames", "countryName", "title", "propTypes", "PropTypes", "string", "isRequired", "number", "Flag", "import_react", "setRefsValue", "refs", "value", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "ref", "setRefValue", "current", "import_prop_types", "metadata", "PropTypes", "shape", "country_calling_codes", "object", "isRequired", "countries", "labels", "objectOf", "string", "country", "metadata", "prefix", "getCountryCallingCode", "getPreSelectedCountry", "_ref", "value", "phoneNumber", "defaultCountry", "getAnyCountry", "countries", "required", "metadata", "country", "couldNumberBelongToCountry", "indexOf", "undefined", "length", "getCountrySelectOptions", "_ref2", "countryNames", "addInternationalOption", "compareStringsLocales", "_compareStrings", "compareStrings", "countrySelectOptions", "map", "label", "sort", "a", "b", "unshift", "ZZ", "parsePhoneNumber", "parsePhoneNumber_", "generateNationalNumberDigits", "formatNational", "replace", "getPhoneDigitsForNewCountry", "phoneDigits", "_ref3", "prevCountry", "newCountry", "useNationalFormat", "getInternationalPhoneNumberPrefix", "getCountryCallingCode", "stripCountryCallingCode", "newCountryPrefix", "defaultValue", "e164", "number", "asYouType", "AsYouType", "input", "getNumberValue", "partial_national_significant_number", "getNationalSignificantNumberDigits", "concat", "trimNumber", "nationalSignificantNumberPart", "overflowDigitsCount", "getMaxNumberLength", "slice", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "numberingPlan", "possibleLengths", "getCountryForPartialE164Number", "partialE164Number", "_ref4", "latestCountrySelectedByUser", "derived_country", "getCountryFromPossiblyIncompleteInternationalPhoneNumber", "onPhoneDigitsChange", "_ref5", "prevPhoneDigits", "countryRequired", "international", "limitMaxLength", "countryCallingCodeEditable", "prefix", "hasStartedTypingInNationalNumberDigitsHavingInputValueSelected", "convertInternationalPhoneDigitsToNational", "formatter", "getNumber", "getCountry", "locales", "String", "prototype", "localeCompare", "countryCallingCodePrefix", "_i", "_Object$keys", "Object", "keys", "country_calling_codes", "country_calling_code", "nationalNumber", "intlPhoneNumberPrefix", "i", "getInitialPhoneDigits", "_ref6", "props", "prevProps", "state", "metadata", "countries", "newDefaultCountry", "defaultCountry", "newValue", "value", "newReset", "reset", "international", "displayInitialValueAsLocalNumber", "initialValueFormat", "prevDefaultCountry", "prevValue", "prevReset", "country", "hasUserSelectedACountry", "latestCountrySelectedByUser", "_getInitialPhoneDigits", "parameters", "getInitialPhoneDigits", "_objectSpread", "useNationalFormat", "phoneDigits", "undefined", "isNewDefaultCountrySupported", "isCountrySupportedWithError", "noValueHasBeenEnteredByTheUser", "noValueHasBeenEntered", "valuesAreEqual", "phoneNumber", "parsedCountry", "validateE164Number", "parsePhoneNumber", "supportedCountries", "getSupportedCountries", "indexOf", "getCountryForPartialE164Number", "getInternationalPhoneNumberPrefix", "userCountrySelectionHistoryStateUpdate", "couldNewValueCorrespondToLatestCountrySelectedByUser", "couldNumberBelongToCountry", "value1", "value2", "PhoneNumberInput_", "_React$PureComponent", "props", "_this", "_classCallCheck", "_callSuper", "_defineProperty", "instance", "setRefsValue", "inputRef", "country", "metadata", "isCountrySupportedWithError", "newCountry", "_this$props", "international", "onChange", "focusInputOnCountrySelection", "_this$state", "state", "prevPhoneDigits", "phoneDigits", "prevCountry", "newPhoneDigits", "getPhoneDigitsForNewCountry", "useNationalFormat", "newValue", "e164", "current", "focus", "setState", "latestCountrySelectedByUser", "hasUserSelectedACountry", "value", "_phoneDigits", "_this$props2", "defaultCountry", "addInternationalOption", "limitMaxLength", "countryCallingCodeEditable", "_this$state2", "countries", "currentlySelectedCountry", "_onPhoneDigitsChange", "onPhoneDigitsChange", "countryRequired", "getAnyCountry", "getFirstSupportedCountry", "stateUpdate", "couldNumberBelongToCountry", "undefined", "force<PERSON><PERSON>nder", "isFocused", "event", "_onFocus", "onFocus", "onBlur", "_onBlur", "countrySelectProps", "React", "createRef", "_this$props3", "labels", "displayInitialValueAsLocalNumber", "initialValueFormat", "_this$props4", "validateE164Number", "getSupportedCountries", "phoneNumber", "parsePhoneNumber", "CountryIcon", "createCountryIconComponent", "preSelectedCountry", "getPreSelectedCountry", "required", "getCountries", "getInitialPhoneDigits", "_inherits", "_createClass", "key", "componentDidMount", "onCountryChange", "selectedCountry", "componentDidUpdate", "prevProps", "prevState", "getCountrySelectOptions", "_ref", "_this$props5", "countryOptionsOrder", "locales", "useMemoCountrySelectOptions", "sortCountryOptions", "countryNames", "compareStringsLocales", "getSupportedCountryOptions", "generator", "dependencies", "countrySelectOptionsMemoDependencies", "areEqualArrays", "countrySelectOptionsMemo", "_ref2", "countryOptions", "render", "_this$props6", "name", "disabled", "readOnly", "autoComplete", "style", "className", "inputComponent", "numberInputProps", "smartCaret", "CountrySelectComponent", "countrySelectComponent", "ContainerComponent", "containerComponent", "containerComponentProps", "countriesProperty", "flags", "flagComponent", "flagUrl", "internationalIcon", "reset", "rest", "_objectWithoutProperties", "_excluded", "_this$state3", "InputComponent", "InputSmart", "InputBasic", "countrySelectOptions", "createElement", "_extends", "classNames", "concat", "options", "onCountryFocus", "onCountryBlur", "iconComponent", "ref", "setInputRef", "type", "withCountryCallingCode", "getDerivedStateFromProps", "_objectSpread", "getPhoneInputWithCountryStateUpdateFromNewProps", "PureComponent", "PhoneNumberInput", "forwardRef", "withDefaultProps", "propTypes", "PropTypes", "string", "func", "isRequired", "bool", "oneOf", "arrayOf", "oneOfType", "objectOf", "elementType", "object", "defaultProps", "CountrySelect", "Flag", "InternationalIcon", "any", "a", "b", "length", "i", "value", "format", "metadata", "_typeof", "phoneNumber", "parsePhoneNumber", "formatPhoneNumberIntl", "formatPhoneNumber", "import_react", "import_prop_types", "createPhoneInput", "defaultMetadata", "PhoneInputDefault", "React", "forwardRef", "_ref", "ref", "_ref$metadata", "metadata", "_ref$labels", "labels", "defaultLabels", "rest", "_objectWithoutProperties", "_excluded", "createElement", "PhoneInput", "_extends", "propTypes", "parsePhoneNumber", "formatPhoneNumber", "formatPhoneNumberIntl", "isValidPhoneNumber", "isPossiblePhoneNumber", "getCountries", "getCountryCallingCode", "isSupportedCountry"]}
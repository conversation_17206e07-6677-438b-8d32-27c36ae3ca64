import {
  _ as _2,
  a,
  b,
  h
} from "./chunk-JBILVJ3R.js";
import {
  g
} from "./chunk-Q7FPBQNE.js";
import {
  r
} from "./chunk-N6JVQRIS.js";
import {
  x
} from "./chunk-FUMRHDK6.js";
import "./chunk-HUQ3BRN5.js";
import "./chunk-WPCBYP5E.js";
import {
  u
} from "./chunk-XV35K7AU.js";
import {
  q
} from "./chunk-4LSRZQCZ.js";
import "./chunk-PEQF4OOI.js";
import {
  m
} from "./chunk-EKB3HY5M.js";
import {
  cookieSchema
} from "./chunk-ERAQ5RRJ.js";
import "./chunk-SGBOHCGH.js";
import "./chunk-RBNUC7A3.js";
import "./chunk-GXEHGJ3I.js";
import "./chunk-FXOF7VZK.js";
import {
  useRoute,
  useRouter
} from "./chunk-EFYDGGJO.js";
import {
  F,
  a as a2,
  je,
  s
} from "./chunk-K3FRCNXE.js";
import {
  _
} from "./chunk-LNIR43HZ.js";
import {
  E2 as E,
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  i3 as i,
  onBeforeUnmount,
  onMounted,
  openBlock,
  ref,
  renderList,
  unref,
  watch,
  withCtx,
  withModifiers
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-O3EO7ESF.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-WESWXL2S.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-2S2EUIOI.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Cookies/CookieForm.vue.js
var x2 = defineComponent({
  __name: "CookieForm",
  setup(f2) {
    const { activeCookieId: e } = F(), { cookies: t, cookieMutators: i2 } = je(), c = [
      { label: "Name", key: "name", placeholder: "session_id" },
      { label: "Value", key: "value", placeholder: "my-cookie-session-id" },
      { label: "Domain", key: "domain", placeholder: "example.com" }
      // TODO: We don’t check the path (yet), so we don’t need to show it.
      // { label: 'Path', key: 'path', placeholder: '/' },
    ], m2 = computed(
      () => t[e.value] || cookieSchema.parse({
        name: "",
        value: "",
        domain: "",
        path: ""
      })
    ), n = (o, a3) => {
      i2.edit(e.value, o, a3);
    };
    return (o, a3) => (openBlock(), createBlock(q, {
      data: m2.value,
      onUpdate: n,
      options: c
    }, null, 8, ["data"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Cookies/CookieModal.vue2.js
var U = { class: "flex h-8 items-start gap-2 text-sm" };
var $ = { class: "flex h-8 items-start gap-2 text-sm" };
var w = { class: "flex h-8 items-start gap-2 text-sm" };
var _3 = defineComponent({
  __name: "CookieModal",
  props: {
    state: {}
  },
  emits: ["cancel", "submit"],
  setup(r2, { emit: p2 }) {
    const u2 = r2, d = p2, a3 = ref({
      name: "",
      value: "",
      domain: ""
    }), { toast: f2 } = i(), v = () => {
      if (!a3.value.name || !a3.value.value) {
        f2("Please fill in all fields before adding a cookie.", "error");
        return;
      }
      d("submit", a3.value), u2.state.hide();
    };
    return watch(
      () => u2.state.open,
      (s2) => {
        s2 && (a3.value = {
          name: "",
          value: "",
          domain: ""
        });
      }
    ), (s2, e) => (openBlock(), createBlock(unref(f), {
      size: "xs",
      state: s2.state,
      title: "Add Cookie"
    }, {
      default: withCtx(() => [
        createVNode(h, {
          disabled: !a3.value.name || !a3.value.value,
          onCancel: e[3] || (e[3] = (o) => d("cancel")),
          onSubmit: v
        }, {
          submit: withCtx(() => e[7] || (e[7] = [
            createTextVNode("Add Cookie")
          ])),
          default: withCtx(() => [
            createBaseVNode("div", U, [
              e[4] || (e[4] = createTextVNode(" Name: ")),
              createVNode(b, {
                modelValue: a3.value.name,
                "onUpdate:modelValue": e[0] || (e[0] = (o) => a3.value.name = o),
                autofocus: "",
                class: "!p-0",
                placeholder: "session_id"
              }, null, 8, ["modelValue"])
            ]),
            createBaseVNode("div", $, [
              e[5] || (e[5] = createTextVNode(" Value: ")),
              createVNode(b, {
                modelValue: a3.value.value,
                "onUpdate:modelValue": e[1] || (e[1] = (o) => a3.value.value = o),
                autofocus: "",
                class: "!p-0",
                placeholder: "my-cookie-session-id"
              }, null, 8, ["modelValue"])
            ]),
            createBaseVNode("div", w, [
              e[6] || (e[6] = createTextVNode(" Domain: ")),
              createVNode(b, {
                modelValue: a3.value.domain,
                "onUpdate:modelValue": e[2] || (e[2] = (o) => a3.value.domain = o),
                autofocus: "",
                class: "!p-0",
                placeholder: "example.com"
              }, null, 8, ["modelValue"])
            ])
          ]),
          _: 1
        }, 8, ["disabled"])
      ]),
      _: 1
    }, 8, ["state"]));
  }
});

// node_modules/@scalar/api-client/dist/views/Cookies/CookieModal.vue.js
var p = s(_3, [["__scopeId", "data-v-694018d6"]]);

// node_modules/@scalar/api-client/dist/views/Cookies/Cookies.vue2.js
var Z = { class: "flex-1" };
var ee = { class: "relative mb-[.5px] last:mb-0" };
var he = defineComponent({
  __name: "Cookies",
  setup(oe) {
    const { cookies: c, cookieMutators: p2, events: k, workspaceMutators: v } = je(), { activeWorkspace: l, activeCookieId: g2 } = F(), m2 = useRouter(), M = useRoute(), f2 = E(), E2 = (t) => {
      var e, i2;
      const o = cookieSchema.parse({
        name: t.name,
        value: t.value,
        domain: t.domain,
        path: "/"
      });
      p2.add(o), v.edit((e = l.value) == null ? void 0 : e.uid, "cookies", [
        ...((i2 = l.value) == null ? void 0 : i2.cookies) ?? [],
        o.uid
      ]), m2.push({
        name: "cookies",
        params: {
          cookies: o.uid
        }
      });
    }, N = (t) => {
      var e, i2;
      p2.delete(t), v.edit((e = l.value) == null ? void 0 : e.uid, "cookies", [
        ...(((i2 = l.value) == null ? void 0 : i2.cookies) ?? []).filter((s2) => s2 !== t)
      ]);
      const o = Object.values(c).filter(
        (s2) => s2.uid !== t
      );
      if (o.length > 0) {
        const s2 = o[o.length - 1];
        s2 && m2.push(s2.uid);
      } else
        m2.push({
          name: "cookies",
          params: {
            [a2.Cookies]: "default"
          }
        });
    }, C = () => {
      f2.show();
    }, _4 = (t) => {
      t != null && t.createNew && M.name === "cookies" && C();
    }, S = (t, o) => {
      var i2;
      const e = {
        name: "cookies",
        params: {
          workspace: ((i2 = l.value) == null ? void 0 : i2.uid) ?? "default",
          cookies: o
        }
      };
      if (t.metaKey) {
        const s2 = m2.resolve(e).href;
        window.open(s2, "_blank");
        return;
      }
      m2.push(e);
    };
    onMounted(() => k.hotKeys.on(_4)), onBeforeUnmount(() => k.hotKeys.off(_4));
    const $2 = computed(
      () => c[g2.value]
    ), B = computed(
      () => Object.keys(c).length > 0 && $2.value
    );
    return (t, o) => (openBlock(), createBlock(u, null, {
      default: withCtx(() => [
        createVNode(unref(m), { title: "Cookies" }, {
          content: withCtx(() => [
            createBaseVNode("div", Z, [
              createVNode(_2, null, {
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(Object.values(unref(c)), (e) => (openBlock(), createElementBlock("li", {
                    key: e.uid,
                    class: "gap-1/2 flex flex-col"
                  }, [
                    createBaseVNode("div", ee, [
                      (openBlock(), createBlock(a, {
                        key: e.uid,
                        class: "text-xs",
                        isDeletable: "",
                        to: {
                          name: "cookies",
                          params: {
                            [unref(a2).Cookies]: e.uid
                          }
                        },
                        type: "cookies",
                        variable: { name: e.name, uid: e.uid },
                        warningMessage: "Are you sure you want to delete this cookie?",
                        onClick: withModifiers((i2) => S(i2, e.uid), ["prevent"]),
                        onDelete: (i2) => N(e.uid)
                      }, null, 8, ["to", "variable", "onClick", "onDelete"]))
                    ])
                  ]))), 128))
                ]),
                _: 1
              })
            ])
          ]),
          button: withCtx(() => [
            createVNode(g, {
              click: C,
              hotkey: "N"
            }, {
              title: withCtx(() => o[1] || (o[1] = [
                createTextVNode(" Add Cookie ")
              ])),
              _: 1
            })
          ]),
          _: 1
        }),
        createVNode(x, { class: "flex-1" }, {
          default: withCtx(() => [
            B.value ? (openBlock(), createBlock(_, {
              key: 0,
              class: "*:border-b-0"
            }, {
              title: withCtx(() => o[2] || (o[2] = [
                createTextVNode("Edit Cookie")
              ])),
              default: withCtx(() => [
                createVNode(x2)
              ]),
              _: 1
            })) : (openBlock(), createBlock(r, { key: 1 }))
          ]),
          _: 1
        }),
        createVNode(p, {
          state: unref(f2),
          onCancel: o[0] || (o[0] = (e) => unref(f2).hide()),
          onSubmit: E2
        }, null, 8, ["state"])
      ]),
      _: 1
    }));
  }
});
export {
  he as default
};
//# sourceMappingURL=Cookies.vue-RBQ3WKAO.js.map

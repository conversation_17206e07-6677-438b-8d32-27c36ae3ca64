// node_modules/hast-util-is-element/lib/index.js
var isElement = (
  // Note: overloads in JSDoc can’t yet use different `@template`s.
  /**
   * @type {(
   *   (<Condition extends TestFunction>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &
   *   (<Condition extends string>(element: unknown, test: Condition, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &
   *   ((element?: null | undefined) => false) &
   *   ((element: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &
   *   ((element: unknown, test?: Test, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => boolean)
   * )}
   */
  /**
   * @param {unknown} [element]
   * @param {Test | undefined} [test]
   * @param {number | null | undefined} [index]
   * @param {Parents | null | undefined} [parent]
   * @param {unknown} [context]
   * @returns {boolean}
   */
  // eslint-disable-next-line max-params
  function(element2, test, index, parent, context) {
    const check = convertElement(test);
    if (index !== null && index !== void 0 && (typeof index !== "number" || index < 0 || index === Number.POSITIVE_INFINITY)) {
      throw new Error("Expected positive finite `index`");
    }
    if (parent !== null && parent !== void 0 && (!parent.type || !parent.children)) {
      throw new Error("Expected valid `parent`");
    }
    if ((index === null || index === void 0) !== (parent === null || parent === void 0)) {
      throw new Error("Expected both `index` and `parent`");
    }
    return looksLikeAnElement(element2) ? check.call(context, element2, index, parent) : false;
  }
);
var convertElement = (
  // Note: overloads in JSDoc can’t yet use different `@template`s.
  /**
   * @type {(
   *   (<Condition extends TestFunction>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & Predicate<Condition, Element>) &
   *   (<Condition extends string>(test: Condition) => (element: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element & {tagName: Condition}) &
   *   ((test?: null | undefined) => (element?: unknown, index?: number | null | undefined, parent?: Parents | null | undefined, context?: unknown) => element is Element) &
   *   ((test?: Test) => Check)
   * )}
   */
  /**
   * @param {Test | null | undefined} [test]
   * @returns {Check}
   */
  function(test) {
    if (test === null || test === void 0) {
      return element;
    }
    if (typeof test === "string") {
      return tagNameFactory(test);
    }
    if (typeof test === "object") {
      return anyFactory(test);
    }
    if (typeof test === "function") {
      return castFactory(test);
    }
    throw new Error("Expected function, string, or array as `test`");
  }
);
function anyFactory(tests) {
  const checks = [];
  let index = -1;
  while (++index < tests.length) {
    checks[index] = convertElement(tests[index]);
  }
  return castFactory(any);
  function any(...parameters) {
    let index2 = -1;
    while (++index2 < checks.length) {
      if (checks[index2].apply(this, parameters)) return true;
    }
    return false;
  }
}
function tagNameFactory(check) {
  return castFactory(tagName);
  function tagName(element2) {
    return element2.tagName === check;
  }
}
function castFactory(testFunction) {
  return check;
  function check(value, index, parent) {
    return Boolean(
      looksLikeAnElement(value) && testFunction.call(
        this,
        value,
        typeof index === "number" ? index : void 0,
        parent || void 0
      )
    );
  }
}
function element(element2) {
  return Boolean(
    element2 && typeof element2 === "object" && "type" in element2 && element2.type === "element" && "tagName" in element2 && typeof element2.tagName === "string"
  );
}
function looksLikeAnElement(value) {
  return value !== null && typeof value === "object" && "type" in value && "tagName" in value;
}

// node_modules/hast-util-embedded/lib/index.js
var embedded = convertElement(
  /**
   * @param element
   * @returns {element is {tagName: 'audio' | 'canvas' | 'embed' | 'iframe' | 'img' | 'math' | 'object' | 'picture' | 'svg' | 'video'}}
   */
  function(element2) {
    return element2.tagName === "audio" || element2.tagName === "canvas" || element2.tagName === "embed" || element2.tagName === "iframe" || element2.tagName === "img" || element2.tagName === "math" || element2.tagName === "object" || element2.tagName === "picture" || element2.tagName === "svg" || element2.tagName === "video";
  }
);

// node_modules/hast-util-has-property/lib/index.js
var own = {}.hasOwnProperty;
function hasProperty(node, name) {
  const value = node.type === "element" && own.call(node.properties, name) && node.properties[name];
  return value !== null && value !== void 0 && value !== false;
}

// node_modules/hast-util-is-body-ok-link/lib/index.js
var list = /* @__PURE__ */ new Set(["pingback", "prefetch", "stylesheet"]);
function isBodyOkLink(node) {
  if (node.type !== "element" || node.tagName !== "link") {
    return false;
  }
  if (node.properties.itemProp) {
    return true;
  }
  const rel = node.properties.rel;
  let index = -1;
  if (!Array.isArray(rel) || rel.length === 0) {
    return false;
  }
  while (++index < rel.length) {
    if (!list.has(String(rel[index]))) {
      return false;
    }
  }
  return true;
}

// node_modules/hast-util-phrasing/lib/index.js
var basic = convertElement([
  "a",
  "abbr",
  // `area` is in fact only phrasing if it is inside a `map` element.
  // However, since `area`s are required to be inside a `map` element, and it’s
  // a rather involved check, it’s ignored here for now.
  "area",
  "b",
  "bdi",
  "bdo",
  "br",
  "button",
  "cite",
  "code",
  "data",
  "datalist",
  "del",
  "dfn",
  "em",
  "i",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "map",
  "mark",
  "meter",
  "noscript",
  "output",
  "progress",
  "q",
  "ruby",
  "s",
  "samp",
  "script",
  "select",
  "small",
  "span",
  "strong",
  "sub",
  "sup",
  "template",
  "textarea",
  "time",
  "u",
  "var",
  "wbr"
]);
var meta = convertElement("meta");
function phrasing(value) {
  return Boolean(
    value.type === "text" || basic(value) || embedded(value) || isBodyOkLink(value) || meta(value) && hasProperty(value, "itemProp")
  );
}

export {
  isElement,
  convertElement,
  embedded,
  phrasing
};
//# sourceMappingURL=chunk-M2KGN5WX.js.map

{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/scss/scss.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scss/scss.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".scss\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@variabledeclaration\" },\n      { include: \"@warndebug\" },\n      // sass: log statements\n      [\"[@](include)\", { token: \"keyword\", next: \"@includedeclaration\" }],\n      // sass: include statement\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      // sass: placeholder for includes\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"[@](function)\", { token: \"keyword\", next: \"@functiondeclaration\" }],\n      [\"[@](mixin)\", { token: \"keyword\", next: \"@mixindeclaration\" }],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@controlstatement\" },\n      // sass control statements\n      { include: \"@selectorname\" },\n      [\"[&\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      { include: \"@selector\" },\n      // sass: nested selectors\n      [\"[@](extend)\", { token: \"keyword\", next: \"@extendbody\" }],\n      // sass: extend other selectors\n      [\"[@](return)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }],\n      // sass: interpolation\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@variablereference\" },\n      [\"(and\\\\b|or\\\\b|not\\\\b)\", \"operator\"],\n      { include: \"@name\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"operator\"],\n      [\",\", \"delimiter\"],\n      [\"!default\", \"literal\"],\n      [\"\\\\(\", { token: \"delimiter.parenthesis\", next: \"@parenthizedterm\" }]\n    ],\n    rulevalue: [\n      { include: \"@term\" },\n      [\"!important\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@nestedproperty\" }],\n      // sass: nested properties\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    nestedproperty: [\n      [\"[*_]?@identifier@ws:\", \"attribute.name\", \"@rulevalue\"],\n      { include: \"@comments\" },\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    variabledeclaration: [\n      // sass variables\n      [\"\\\\$@identifier@ws:\", \"variable.decl\", \"@declarationbody\"]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    extendbody: [\n      { include: \"@selectorname\" },\n      [\"!optional\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    variablereference: [\n      // sass variable reference\n      [\"\\\\$@identifier\", \"variable.ref\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in reference\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }]\n      // sass var resolve\n    ],\n    variableinterpolation: [\n      { include: \"@variablereference\" },\n      [\"}\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"number.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"number\",\n        \"@pop\"\n      ]\n    ],\n    functiondeclaration: [\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@functionbody\" }]\n    ],\n    mixindeclaration: [\n      // mixin with parameters\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      // mixin without parameters\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    parameterdeclaration: [\n      [\"\\\\$@identifier@ws:\", \"variable.decl\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in declaration\n      [\",\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    includedeclaration: [\n      { include: \"@functioninvocation\" },\n      [\"@identifier\", \"meta\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }],\n      // missing semicolon\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    controlstatement: [\n      [\n        \"[@](if|else|for|while|each|media)\",\n        { token: \"keyword.flow\", next: \"@controlstatementdeclaration\" }\n      ]\n    ],\n    controlstatementdeclaration: [\n      [\"(in|from|through|if|to)\\\\b\", { token: \"keyword.flow\" }],\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    functionbody: [\n      [\"[@](return)\", { token: \"keyword\" }],\n      { include: \"@variabledeclaration\" },\n      { include: \"@term\" },\n      { include: \"@controlstatement\" },\n      [\";\", \"delimiter\"],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    functioninvocation: [[\"@identifier\\\\(\", { token: \"meta\", next: \"@functionarguments\" }]],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAgCA;AAzCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,MAAM,IAAI;AAAA,QACzB,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,UAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,IAAI;AAAA;AAAA,MAEJ,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,QACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MACpD;AAAA,MACA,WAAW;AAAA,QACT,MAAM,CAAC,EAAE,SAAS,YAAY,CAAC;AAAA,QAC/B,UAAU;AAAA,UACR,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,uBAAuB;AAAA,UAClC,EAAE,SAAS,aAAa;AAAA;AAAA,UAExB,CAAC,gBAAgB,EAAE,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAAA;AAAA,UAElE;AAAA,YACE;AAAA,YACA,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,UACnD;AAAA,UACA,CAAC,6CAA6C,EAAE,OAAO,UAAU,CAAC;AAAA;AAAA,UAElE,CAAC,0BAA0B,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,UACzE,CAAC,iBAAiB,EAAE,OAAO,WAAW,MAAM,uBAAuB,CAAC;AAAA,UACpE,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,oBAAoB,CAAC;AAAA,UAC9D,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,UACjE,EAAE,SAAS,oBAAoB;AAAA;AAAA,UAE/B,EAAE,SAAS,gBAAgB;AAAA,UAC3B,CAAC,UAAU,KAAK;AAAA;AAAA,UAEhB,CAAC,WAAW,WAAW;AAAA;AAAA,UAEvB,CAAC,OAAO,EAAE,OAAO,qBAAqB,MAAM,qBAAqB,CAAC;AAAA,UAClE,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,QAC3D;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,iDAAiD,kBAAkB,YAAY;AAAA;AAAA,UAEhF,EAAE,SAAS,YAAY;AAAA;AAAA,UAEvB,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,cAAc,CAAC;AAAA;AAAA,UAEzD,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,UAC9D,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA;AAAA,UAExD,CAAC,sCAAsC,KAAK;AAAA;AAAA,QAE9C;AAAA,QACA,mBAAmB,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC,CAAC;AAAA,QAC7F,MAAM;AAAA,UACJ,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,UACjE,EAAE,SAAS,sBAAsB;AAAA,UACjC,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,qBAAqB;AAAA,UAChC,CAAC,yBAAyB,UAAU;AAAA,UACpC,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,iCAAiC,UAAU;AAAA,UAC5C,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,YAAY,SAAS;AAAA,UACtB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,mBAAmB,CAAC;AAAA,QACtE;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,cAAc,SAAS;AAAA,UACxB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA;AAAA,UAE/D,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,QAEvC;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,wBAAwB,kBAAkB,YAAY;AAAA,UACvD,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,WAAW,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,QAC/E,QAAQ,CAAC,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,QACxE,qBAAqB;AAAA;AAAA,UAEnB,CAAC,sBAAsB,iBAAiB,kBAAkB;AAAA,QAC5D;AAAA,QACA,gBAAgB;AAAA,UACd,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,QACzC;AAAA,QACA,iBAAiB;AAAA,UACf,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,QAC1D;AAAA,QACA,iBAAiB;AAAA,UACf,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,QAEvC;AAAA,QACA,YAAY;AAAA,UACV,EAAE,SAAS,gBAAgB;AAAA,UAC3B,CAAC,aAAa,SAAS;AAAA,UACvB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,QAEvC;AAAA,QACA,mBAAmB;AAAA;AAAA,UAEjB,CAAC,kBAAkB,cAAc;AAAA,UACjC,CAAC,aAAa,UAAU;AAAA;AAAA,UAExB,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA;AAAA,QAE1D;AAAA,QACA,uBAAuB;AAAA,UACrB,EAAE,SAAS,qBAAqB;AAAA,UAChC,CAAC,KAAK,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,QACvC;AAAA,QACA,UAAU;AAAA,UACR,CAAC,UAAU,WAAW,UAAU;AAAA,UAChC,CAAC,aAAa,SAAS;AAAA,QACzB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,UAAU,WAAW,MAAM;AAAA,UAC5B,CAAC,KAAK,SAAS;AAAA,QACjB;AAAA,QACA,MAAM,CAAC,CAAC,eAAe,iBAAiB,CAAC;AAAA,QACzC,SAAS;AAAA,UACP,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,SAAS,CAAC;AAAA,UACxE,CAAC,yBAAyB,YAAY;AAAA,QACxC;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA,UACtE,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,QAC/D;AAAA,QACA,kBAAkB;AAAA;AAAA,UAEhB,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA;AAAA,UAEtE,CAAC,eAAe,MAAM;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,QAC/D;AAAA,QACA,sBAAsB;AAAA,UACpB,CAAC,sBAAsB,eAAe;AAAA,UACtC,CAAC,aAAa,UAAU;AAAA;AAAA,UAExB,CAAC,KAAK,WAAW;AAAA,UACjB,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,QACzC;AAAA,QACA,oBAAoB;AAAA,UAClB,EAAE,SAAS,sBAAsB;AAAA,UACjC,CAAC,eAAe,MAAM;AAAA,UACtB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,UAErC,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,QAC/D;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,eAAe,MAAM;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,QAC/D;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,UACzD,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,kBAAkB;AAAA,UAChB;AAAA,YACE;AAAA,YACA,EAAE,OAAO,gBAAgB,MAAM,+BAA+B;AAAA,UAChE;AAAA,QACF;AAAA,QACA,6BAA6B;AAAA,UAC3B,CAAC,8BAA8B,EAAE,OAAO,eAAe,CAAC;AAAA,UACxD,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,QAC/D;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,eAAe,EAAE,OAAO,UAAU,CAAC;AAAA,UACpC,EAAE,SAAS,uBAAuB;AAAA,UAClC,EAAE,SAAS,QAAQ;AAAA,UACnB,EAAE,SAAS,oBAAoB;AAAA,UAC/B,CAAC,KAAK,WAAW;AAAA,UACjB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,oBAAoB,CAAC,CAAC,kBAAkB,EAAE,OAAO,QAAQ,MAAM,qBAAqB,CAAC,CAAC;AAAA,QACtF,mBAAmB;AAAA,UACjB,CAAC,sBAAsB,gBAAgB;AAAA,UACvC,CAAC,OAAO,WAAW;AAAA,UACnB,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,QACzC;AAAA,QACA,SAAS;AAAA,UACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,wBAAwB,CAAC;AAAA,UACpE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,kBAAkB,CAAC;AAAA,QAChE;AAAA,QACA,sBAAsB;AAAA,UACpB,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACjD,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACjD,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
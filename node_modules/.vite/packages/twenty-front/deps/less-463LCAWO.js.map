{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/less/less.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/less/less.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".less\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  identifierPlus: \"-?-?([a-zA-Z:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@nestedJSBegin\" },\n      [\"[ \\\\t\\\\r\\\\n]+\", \"\"],\n      { include: \"@comments\" },\n      { include: \"@keyword\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      [\"[*_]?[a-zA-Z\\\\-\\\\s]+(?=:.*(;|(\\\\\\\\$)))\", \"attribute.name\", \"@attribute\"],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"tag\", next: \"@urldeclaration\" }],\n      [\"[{}()\\\\[\\\\]]\", \"@brackets\"],\n      [\"[,:;]\", \"delimiter\"],\n      [\"#@identifierPlus\", \"tag.id\"],\n      [\"&\", \"tag\"],\n      [\"\\\\.@identifierPlus(?=\\\\()\", \"tag.class\", \"@attribute\"],\n      [\"\\\\.@identifierPlus\", \"tag.class\"],\n      [\"@identifierPlus\", \"tag\"],\n      { include: \"@operators\" },\n      [\"@(@identifier(?=[:,\\\\)]))\", \"variable\", \"@attribute\"],\n      [\"@(@identifier)\", \"variable\"],\n      [\"@\", \"key\", \"@atRules\"]\n    ],\n    nestedJSBegin: [\n      [\"``\", \"delimiter.backtick\"],\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@nestedJSEnd\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ]\n    ],\n    nestedJSEnd: [\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      ]\n    ],\n    operators: [[\"[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~]\", \"operator\"]],\n    keyword: [\n      [\n        \"(@[\\\\s]*import|![\\\\s]*important|true|false|when|iscolor|isnumber|isstring|iskeyword|isurl|ispixel|ispercentage|isem|hue|saturation|lightness|alpha|lighten|darken|saturate|desaturate|fadein|fadeout|fade|spin|mix|round|ceil|floor|percentage)\\\\b\",\n        \"keyword\"\n      ]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"tag\", next: \"@pop\" }]\n    ],\n    attribute: [\n      { include: \"@nestedJSBegin\" },\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      { include: \"@keyword\" },\n      [\"[a-zA-Z\\\\-]+(?=\\\\()\", \"attribute.value\", \"@attribute\"],\n      [\">\", \"operator\", \"@pop\"],\n      [\"@identifier\", \"attribute.value\"],\n      { include: \"@operators\" },\n      [\"@(@identifier)\", \"variable\"],\n      [\"[)\\\\}]\", \"@brackets\", \"@pop\"],\n      [\"[{}()\\\\[\\\\]>]\", \"@brackets\"],\n      [\"[;]\", \"delimiter\", \"@pop\"],\n      [\"[,=:]\", \"delimiter\"],\n      [\"\\\\s\", \"\"],\n      [\".\", \"attribute.value\"]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringsEndDoubleQuote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringsEndQuote\" }]\n    ],\n    stringsEndDoubleQuote: [\n      ['\\\\\\\\\"', \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    stringsEndQuote: [\n      [\"\\\\\\\\'\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    atRules: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      [\"[()]\", \"delimiter\"],\n      [\"[\\\\{;]\", \"delimiter\", \"@pop\"],\n      [\".\", \"key\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAgCA;AAzCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,MAAM,IAAI;AAAA,QACzB,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,UAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,QACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MACpD;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,iBAAiB;AAAA,UAC5B,CAAC,iBAAiB,EAAE;AAAA,UACpB,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,0CAA0C,kBAAkB,YAAY;AAAA,UACzE,CAAC,sBAAsB,EAAE,OAAO,OAAO,MAAM,kBAAkB,CAAC;AAAA,UAChE,CAAC,gBAAgB,WAAW;AAAA,UAC5B,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,oBAAoB,QAAQ;AAAA,UAC7B,CAAC,KAAK,KAAK;AAAA,UACX,CAAC,6BAA6B,aAAa,YAAY;AAAA,UACvD,CAAC,sBAAsB,WAAW;AAAA,UAClC,CAAC,mBAAmB,KAAK;AAAA,UACzB,EAAE,SAAS,aAAa;AAAA,UACxB,CAAC,6BAA6B,YAAY,YAAY;AAAA,UACtD,CAAC,kBAAkB,UAAU;AAAA,UAC7B,CAAC,KAAK,OAAO,UAAU;AAAA,QACzB;AAAA,QACA,eAAe;AAAA,UACb,CAAC,MAAM,oBAAoB;AAAA,UAC3B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,QACA,aAAa;AAAA,UACX;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,cACN,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW,CAAC,CAAC,8BAA8B,UAAU,CAAC;AAAA,QACtD,SAAS;AAAA,UACP;AAAA,YACE;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,gBAAgB;AAAA,UACd,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,OAAO,EAAE,OAAO,OAAO,MAAM,OAAO,CAAC;AAAA,QACxC;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,iBAAiB;AAAA,UAC5B,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,uBAAuB,mBAAmB,YAAY;AAAA,UACvD,CAAC,KAAK,YAAY,MAAM;AAAA,UACxB,CAAC,eAAe,iBAAiB;AAAA,UACjC,EAAE,SAAS,aAAa;AAAA,UACxB,CAAC,kBAAkB,UAAU;AAAA,UAC7B,CAAC,UAAU,aAAa,MAAM;AAAA,UAC9B,CAAC,iBAAiB,WAAW;AAAA,UAC7B,CAAC,OAAO,aAAa,MAAM;AAAA,UAC3B,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,OAAO,EAAE;AAAA,UACV,CAAC,KAAK,iBAAiB;AAAA,QACzB;AAAA,QACA,UAAU;AAAA,UACR,CAAC,UAAU,WAAW,UAAU;AAAA,UAChC,CAAC,aAAa,SAAS;AAAA,QACzB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,UAAU,WAAW,MAAM;AAAA,UAC5B,CAAC,KAAK,SAAS;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,oCAAoC,EAAE,OAAO,0BAA0B,MAAM,SAAS,CAAC;AAAA,UACxF,CAAC,yBAAyB,qBAAqB;AAAA,QACjD;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAAA,UACrE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,mBAAmB,CAAC;AAAA,QACjE;AAAA,QACA,uBAAuB;AAAA,UACrB,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,UAAU,CAAC;AAAA,UACpD,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,UAAU,CAAC;AAAA,UACpD,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,SAAS;AAAA,UACP,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,QAAQ,WAAW;AAAA,UACpB,CAAC,UAAU,aAAa,MAAM;AAAA,UAC9B,CAAC,KAAK,KAAK;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
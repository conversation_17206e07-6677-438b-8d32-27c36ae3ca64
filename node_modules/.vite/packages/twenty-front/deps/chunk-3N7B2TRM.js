import {
  a
} from "./chunk-PEQF4OOI.js";
import {
  createBlock,
  createVNode,
  defineComponent,
  m4 as m,
  openBlock,
  ref,
  unref,
  withCtx
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/api-client/dist/components/Sidebar/Actions/EditSidebarListElement.vue.js
var C = defineComponent({
  __name: "EditSidebarListElement",
  props: {
    name: {}
  },
  emits: ["close", "edit"],
  setup(l, { emit: m2 }) {
    const r = l, n = m2, t = ref(r.name);
    return (x, e) => (openBlock(), createBlock(a, {
      onCancel: e[1] || (e[1] = (o) => n("close")),
      onSubmit: e[2] || (e[2] = (o) => n("edit", t.value))
    }, {
      default: withCtx(() => [
        createVNode(unref(m), {
          modelValue: t.value,
          "onUpdate:modelValue": e[0] || (e[0] = (o) => t.value = o),
          autofocus: ""
        }, null, 8, ["modelValue"])
      ]),
      _: 1
    }));
  }
});

export {
  C
};
//# sourceMappingURL=chunk-3N7B2TRM.js.map

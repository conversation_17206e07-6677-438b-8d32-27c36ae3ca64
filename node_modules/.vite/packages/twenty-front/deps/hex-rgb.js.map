{"version": 3, "sources": ["../../../../hex-rgb/index.js"], "sourcesContent": ["const hexCharacters = 'a-f\\\\d';\nconst match3or4Hex = `#?[${hexCharacters}]{3}[${hexCharacters}]?`;\nconst match6or8Hex = `#?[${hexCharacters}]{6}([${hexCharacters}]{2})?`;\nconst nonHexChars = new RegExp(`[^#${hexCharacters}]`, 'gi');\nconst validHexSize = new RegExp(`^${match3or4Hex}$|^${match6or8Hex}$`, 'i');\n\nexport default function hexRgb(hex, options = {}) {\n\tif (typeof hex !== 'string' || nonHexChars.test(hex) || !validHexSize.test(hex)) {\n\t\tthrow new TypeError('Expected a valid hex string');\n\t}\n\n\thex = hex.replace(/^#/, '');\n\tlet alphaFromHex = 1;\n\n\tif (hex.length === 8) {\n\t\talphaFromHex = Number.parseInt(hex.slice(6, 8), 16) / 255;\n\t\thex = hex.slice(0, 6);\n\t}\n\n\tif (hex.length === 4) {\n\t\talphaFromHex = Number.parseInt(hex.slice(3, 4).repeat(2), 16) / 255;\n\t\thex = hex.slice(0, 3);\n\t}\n\n\tif (hex.length === 3) {\n\t\thex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];\n\t}\n\n\tconst number = Number.parseInt(hex, 16);\n\tconst red = number >> 16;\n\tconst green = (number >> 8) & 255;\n\tconst blue = number & 255;\n\tconst alpha = typeof options.alpha === 'number' ? options.alpha : alphaFromHex;\n\n\tif (options.format === 'array') {\n\t\treturn [red, green, blue, alpha];\n\t}\n\n\tif (options.format === 'css') {\n\t\tconst alphaString = alpha === 1 ? '' : ` / ${Number((alpha * 100).toFixed(2))}%`;\n\t\treturn `rgb(${red} ${green} ${blue}${alphaString})`;\n\t}\n\n\treturn {red, green, blue, alpha};\n}\n"], "mappings": ";;;AAAA,IAAM,gBAAgB;AACtB,IAAM,eAAe,MAAM,aAAa,QAAQ,aAAa;AAC7D,IAAM,eAAe,MAAM,aAAa,SAAS,aAAa;AAC9D,IAAM,cAAc,IAAI,OAAO,MAAM,aAAa,KAAK,IAAI;AAC3D,IAAM,eAAe,IAAI,OAAO,IAAI,YAAY,MAAM,YAAY,KAAK,GAAG;AAE3D,SAAR,OAAwB,KAAK,UAAU,CAAC,GAAG;AACjD,MAAI,OAAO,QAAQ,YAAY,YAAY,KAAK,GAAG,KAAK,CAAC,aAAa,KAAK,GAAG,GAAG;AAChF,UAAM,IAAI,UAAU,6BAA6B;AAAA,EAClD;AAEA,QAAM,IAAI,QAAQ,MAAM,EAAE;AAC1B,MAAI,eAAe;AAEnB,MAAI,IAAI,WAAW,GAAG;AACrB,mBAAe,OAAO,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,IAAI;AACtD,UAAM,IAAI,MAAM,GAAG,CAAC;AAAA,EACrB;AAEA,MAAI,IAAI,WAAW,GAAG;AACrB,mBAAe,OAAO,SAAS,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI;AAChE,UAAM,IAAI,MAAM,GAAG,CAAC;AAAA,EACrB;AAEA,MAAI,IAAI,WAAW,GAAG;AACrB,UAAM,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,EACzD;AAEA,QAAM,SAAS,OAAO,SAAS,KAAK,EAAE;AACtC,QAAM,MAAM,UAAU;AACtB,QAAM,QAAS,UAAU,IAAK;AAC9B,QAAM,OAAO,SAAS;AACtB,QAAM,QAAQ,OAAO,QAAQ,UAAU,WAAW,QAAQ,QAAQ;AAElE,MAAI,QAAQ,WAAW,SAAS;AAC/B,WAAO,CAAC,KAAK,OAAO,MAAM,KAAK;AAAA,EAChC;AAEA,MAAI,QAAQ,WAAW,OAAO;AAC7B,UAAM,cAAc,UAAU,IAAI,KAAK,MAAM,QAAQ,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;AAC7E,WAAO,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,WAAW;AAAA,EACjD;AAEA,SAAO,EAAC,KAAK,OAAO,MAAM,MAAK;AAChC;", "names": []}
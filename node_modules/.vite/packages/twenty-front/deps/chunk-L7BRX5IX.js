import {
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  defineComponent,
  openBlock,
  resolveDynamicComponent
} from "./chunk-D5ZO4EYM.js";
import {
  __export
} from "./chunk-XPZLJQLW.js";

// node_modules/@scalar/icons/dist/icons/basic-shape-diamond.svg.js
var basic_shape_diamond_svg_exports = {};
__export(basic_shape_diamond_svg_exports, {
  default: () => i,
  render: () => l
});
var t = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l(a11, e) {
  return openBlock(), createElementBlock("svg", t, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m10.812 2.492-8.32 8.32a1.68 1.68 0 0 0 0 2.377l8.32 8.319a1.68 1.68 0 0 0 2.377 0l8.319-8.32a1.68 1.68 0 0 0 0-2.377l-8.32-8.319a1.68 1.68 0 0 0-2.377 0Z"
    }, null, -1)
  ]));
}
var i = { render: l };

// node_modules/@scalar/icons/dist/icons/basic-shape-hexagon.svg.js
var basic_shape_hexagon_svg_exports = {};
__export(basic_shape_hexagon_svg_exports, {
  default: () => c,
  render: () => l2
});
var t2 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l2(a11, e) {
  return openBlock(), createElementBlock("svg", t2, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M20.711 16.963V7.044a1.37 1.37 0 0 0-.759-1.229l-7.338-3.67a1.37 1.37 0 0 0-1.227 0l-7.34 3.662a1.37 1.37 0 0 0-.758 1.23v9.919a1.37 1.37 0 0 0 .759 1.229l7.34 3.67a1.38 1.38 0 0 0 1.229 0l7.34-3.663a1.37 1.37 0 0 0 .754-1.23Z"
    }, null, -1)
  ]));
}
var c = { render: l2 };

// node_modules/@scalar/icons/dist/icons/basic-shape-primary-circle-ellipse-round.svg.js
var basic_shape_primary_circle_ellipse_round_svg_exports = {};
__export(basic_shape_primary_circle_ellipse_round_svg_exports, {
  default: () => c2,
  render: () => l3
});
var t3 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l3(i16, e) {
  return openBlock(), createElementBlock("svg", t3, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M2 12a10 10 0 1 0 20 0 10 10 0 0 0-20 0"
    }, null, -1)
  ]));
}
var c2 = { render: l3 };

// node_modules/@scalar/icons/dist/icons/basic-shape-primary-square-rectangle.svg.js
var basic_shape_primary_square_rectangle_svg_exports = {};
__export(basic_shape_primary_square_rectangle_svg_exports, {
  default: () => c3,
  render: () => a
});
var t4 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a(l68, e) {
  return openBlock(), createElementBlock("svg", t4, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2"
    }, null, -1)
  ]));
}
var c3 = { render: a };

// node_modules/@scalar/icons/dist/icons/basic-shape-shield.svg.js
var basic_shape_shield_svg_exports = {};
__export(basic_shape_shield_svg_exports, {
  default: () => i2,
  render: () => l4
});
var t5 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l4(a11, e) {
  return openBlock(), createElementBlock("svg", t5, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M3.334 4.82v6.768a10.58 10.58 0 0 0 6.783 9.878l.926.356a2.67 2.67 0 0 0 1.914 0l.926-.356a10.58 10.58 0 0 0 6.783-9.878V4.821a1.32 1.32 0 0 0-.771-1.211A19.3 19.3 0 0 0 12 2a19.3 19.3 0 0 0-7.895 1.61 1.32 1.32 0 0 0-.771 1.21"
    }, null, -1)
  ]));
}
var i2 = { render: l4 };

// node_modules/@scalar/icons/dist/icons/computer-device-desktop-monitor.svg.js
var computer_device_desktop_monitor_svg_exports = {};
__export(computer_device_desktop_monitor_svg_exports, {
  default: () => i3,
  render: () => l5
});
var n = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l5(s27, e) {
  return openBlock(), createElementBlock("svg", n, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M3 20h18M5 4h14a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2"
    }, null, -1)
  ]));
}
var i3 = { render: l5 };

// node_modules/@scalar/icons/dist/icons/computer-device-desktop.svg.js
var computer_device_desktop_svg_exports = {};
__export(computer_device_desktop_svg_exports, {
  default: () => c4,
  render: () => l6
});
var n2 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l6(a11, e) {
  return openBlock(), createElementBlock("svg", n2, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8m-5 4h5m6-7h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-6a2 2 0 0 1 2-2"
    }, null, -1)
  ]));
}
var c4 = { render: l6 };

// node_modules/@scalar/icons/dist/icons/computer-device-laptop.svg.js
var computer_device_laptop_svg_exports = {};
__export(computer_device_laptop_svg_exports, {
  default: () => s,
  render: () => l7
});
var n3 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l7(a11, e) {
  return openBlock(), createElementBlock("svg", n3, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M20.522 14.167V6.64a2.13 2.13 0 0 0-2.13-2.13H5.608a2.13 2.13 0 0 0-2.13 2.13v7.526m17.044 0H3.478m17.044 0 1.364 4.778a1.066 1.066 0 0 1-.959 1.545H3.073a1.065 1.065 0 0 1-.959-1.545l1.364-4.778m6.855 3.333h3.334"
    }, null, -1)
  ]));
}
var s = { render: l7 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-phone-android-samsung-back.svg.js
var computer_device_mobile_phone_android_samsung_back_svg_exports = {};
__export(computer_device_mobile_phone_android_samsung_back_svg_exports, {
  default: () => c5,
  render: () => s2
});
var t6 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function s2(l68, e) {
  return openBlock(), createElementBlock("svg", t6, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M8.333 2h7.334s2.666 0 2.666 2.667v14.666s0 2.667-2.666 2.667H8.333s-2.666 0-2.666-2.667V4.667S5.667 2 8.333 2"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M9.186 4.563h1.246s.889 0 .889.889V9.88s0 .889-.889.889H9.186s-.89 0-.89-.89V5.453s0-.89.89-.89"
    }, null, -1)
  ]));
}
var c5 = { render: s2 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-phone-android-samsung.svg.js
var computer_device_mobile_phone_android_samsung_svg_exports = {};
__export(computer_device_mobile_phone_android_samsung_svg_exports, {
  default: () => u,
  render: () => t7
});
var l8 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t7(d8, e) {
  return openBlock(), createElementBlock("svg", l8, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15.667 2H8.333a2.667 2.667 0 0 0-2.666 2.667v14.666A2.667 2.667 0 0 0 8.333 22h7.334a2.667 2.667 0 0 0 2.666-2.667V4.667A2.667 2.667 0 0 0 15.667 2"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 5a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var u = { render: t7 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-phone-iphone-x-back.svg.js
var computer_device_mobile_phone_iphone_x_back_svg_exports = {};
__export(computer_device_mobile_phone_iphone_x_back_svg_exports, {
  default: () => i4,
  render: () => t8
});
var l9 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t8(c34, e) {
  return openBlock(), createElementBlock("svg", l9, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15.667 2H8.333a2.667 2.667 0 0 0-2.666 2.667v14.666A2.667 2.667 0 0 0 8.333 22h7.334a2.667 2.667 0 0 0 2.666-2.667V4.667A2.667 2.667 0 0 0 15.667 2"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M8 5a1 1 0 1 1 2 0 1 1 0 0 1-2 0m3 2a1 1 0 1 1 2 0 1 1 0 0 1-2 0M8 9a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var i4 = { render: t8 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-phone-iphone-x.svg.js
var computer_device_mobile_phone_iphone_x_svg_exports = {};
__export(computer_device_mobile_phone_iphone_x_svg_exports, {
  default: () => c6,
  render: () => l10
});
var t9 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l10(s27, e) {
  return openBlock(), createElementBlock("svg", t9, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M7.198 2h9.603s1.74 0 1.74 1.74v16.52s0 1.74-1.74 1.74H7.198s-1.74 0-1.74-1.74V3.74S5.459 2 7.199 2Z"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M14.684 4.935H9.315a.94.94 0 0 1-.932-.8L8.067 2h7.865l-.315 2.133a.94.94 0 0 1-.933.802"
    }, null, -1)
  ]));
}
var c6 = { render: l10 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-tablet-touch.svg.js
var computer_device_mobile_tablet_touch_svg_exports = {};
__export(computer_device_mobile_tablet_touch_svg_exports, {
  default: () => i5,
  render: () => l11
});
var n4 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l11(a11, e) {
  return openBlock(), createElementBlock("svg", n4, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M21 11V7a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h6m1.034-7.319a.499.499 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z"
    }, null, -1)
  ]));
}
var i5 = { render: l11 };

// node_modules/@scalar/icons/dist/icons/computer-device-mobile-tablet.svg.js
var computer_device_mobile_tablet_svg_exports = {};
__export(computer_device_mobile_tablet_svg_exports, {
  default: () => c7,
  render: () => t10
});
var n5 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t10(d8, e) {
  return openBlock(), createElementBlock("svg", n5, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15.667 22H8.333a2.667 2.667 0 0 1-2.666-2.667V4.667A2.667 2.667 0 0 1 8.333 2h7.334a2.667 2.667 0 0 1 2.666 2.667v14.666A2.667 2.667 0 0 1 15.667 22"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 19a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var c7 = { render: t10 };

// node_modules/@scalar/icons/dist/icons/computer-device-network-ethernet-cat6.svg.js
var computer_device_network_ethernet_cat6_svg_exports = {};
__export(computer_device_network_ethernet_cat6_svg_exports, {
  default: () => s3,
  render: () => l12
});
var n6 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l12(c34, e) {
  return openBlock(), createElementBlock("svg", n6, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M10 7.5v2.813M14 7.5v2.813M14 7.5h2.5a1.125 1.125 0 0 1 1.125 1.125v5.062a1.125 1.125 0 0 1-1.125 1.125h-.543c-.373 0-.73.149-.994.412l-.864.864a1.4 1.4 0 0 1-.994.412h-2.21c-.373 0-.73-.148-.994-.412l-.864-.864a1.4 1.4 0 0 0-.994-.412H7.5a1.125 1.125 0 0 1-1.125-1.125V8.625A1.125 1.125 0 0 1 7.5 7.5zM4.2 3h15.6S21 3 21 4.2v15.6s0 1.2-1.2 1.2H4.2S3 21 3 19.8V4.2S3 3 4.2 3"
    }, null, -1)
  ]));
}
var s3 = { render: l12 };

// node_modules/@scalar/icons/dist/icons/computer-device-network-lan-www.svg.js
var computer_device_network_lan_www_svg_exports = {};
__export(computer_device_network_lan_www_svg_exports, {
  default: () => s4,
  render: () => l13
});
var n7 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l13(c34, e) {
  return openBlock(), createElementBlock("svg", n7, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M22 12c0 5.523-4.477 10-10 10m10-10c0-5.523-4.477-10-10-10m10 10H2m10 10C6.477 22 2 17.523 2 12m10 10a14.5 14.5 0 0 1 0-20m0 20a14.5 14.5 0 0 0 0-20M2 12C2 6.477 6.477 2 12 2"
    }, null, -1)
  ]));
}
var s4 = { render: l13 };

// node_modules/@scalar/icons/dist/icons/computer-device-network-wifi-connection.svg.js
var computer_device_network_wifi_connection_svg_exports = {};
__export(computer_device_network_wifi_connection_svg_exports, {
  default: () => d,
  render: () => l14
});
var t11 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l14(i16, e) {
  return openBlock(), createElementBlock("svg", t11, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M2 8.82a15 15 0 0 1 20 0M5 12.859a10 10 0 0 1 14 0m-10.5 3.57a5 5 0 0 1 7 0"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 20a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var d = { render: l14 };

// node_modules/@scalar/icons/dist/icons/computer-device-network-wifi-router.svg.js
var computer_device_network_wifi_router_svg_exports = {};
__export(computer_device_network_wifi_router_svg_exports, {
  default: () => d2,
  render: () => l15
});
var n8 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l15(i16, e) {
  return openBlock(), createElementBlock("svg", n8, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M14.7 10.2v3.6m2.556-6.147a3.6 3.6 0 0 0-5.094 0m7.632-2.547a7.2 7.2 0 0 0-10.179 0M4.8 13.8h14.4a1.8 1.8 0 0 1 1.8 1.8v3.6a1.8 1.8 0 0 1-1.8 1.8H4.8A1.8 1.8 0 0 1 3 19.2v-3.6a1.8 1.8 0 0 1 1.8-1.8"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M6 17.5a1 1 0 1 1 2 0 1 1 0 0 1-2 0m4 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var d2 = { render: l15 };

// node_modules/@scalar/icons/dist/icons/ecology-science-erlenmeyer-flask.svg.js
var ecology_science_erlenmeyer_flask_svg_exports = {};
__export(ecology_science_erlenmeyer_flask_svg_exports, {
  default: () => a2,
  render: () => l16
});
var t12 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l16(c34, e) {
  return openBlock(), createElementBlock("svg", t12, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M8.5 2h7m-5.3 1v6.774a1.8 1.8 0 0 1-.19.807l-4.562 9.114A.9.9 0 0 0 6.258 21h11.484a.9.9 0 0 0 .81-1.305l-4.562-9.114a1.8 1.8 0 0 1-.19-.807V3M7.5 15.6h9"
    }, null, -1)
  ]));
}
var a2 = { render: l16 };

// node_modules/@scalar/icons/dist/icons/image-flash-lightning.svg.js
var image_flash_lightning_svg_exports = {};
__export(image_flash_lightning_svg_exports, {
  default: () => s5,
  render: () => l17
});
var r = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l17(a11, e) {
  return openBlock(), createElementBlock("svg", r, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4.802 13.8a.899.899 0 0 1-.702-1.467l8.908-9.178a.45.45 0 0 1 .774.414l-1.728 5.417A.9.9 0 0 0 12.9 10.2h6.298a.899.899 0 0 1 .702 1.467l-8.908 9.178a.45.45 0 0 1-.774-.414l1.728-5.417A.9.9 0 0 0 11.1 13.8z"
    }, null, -1)
  ]));
}
var s5 = { render: l17 };

// node_modules/@scalar/icons/dist/icons/image-picture-flower.svg.js
var image_picture_flower_svg_exports = {};
__export(image_picture_flower_svg_exports, {
  default: () => s6,
  render: () => m
});
var n9 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function m(l68, e) {
  return openBlock(), createElementBlock("svg", n9, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 9.286a2.5 2.5 0 1 1 2.5 2.5m-2.5-2.5a2.5 2.5 0 1 0-2.5 2.5m2.5-2.5v.833m2.5 1.667a2.5 2.5 0 1 1-2.5 2.5m2.5-2.5h-.833m-4.167 0a2.5 2.5 0 1 0 2.5 2.5m-2.5-2.5h.833M12 10.119c-.92 0-1.667.746-1.667 1.667M12 10.119c.92 0 1.667.746 1.667 1.667M12 14.286v-.834m0 .834V20.5m-1.667-8.714c0 .92.746 1.666 1.667 1.666m1.667-1.666c0 .92-.746 1.666-1.667 1.666M4.2 3h15.6S21 3 21 4.2v15.6s0 1.2-1.2 1.2H4.2S3 21 3 19.8V4.2S3 3 4.2 3"
    }, null, -1)
  ]));
}
var s6 = { render: m };

// node_modules/@scalar/icons/dist/icons/interface-alert-exclamation-diamond.svg.js
var interface_alert_exclamation_diamond_svg_exports = {};
__export(interface_alert_exclamation_diamond_svg_exports, {
  default: () => i6,
  render: () => t13
});
var r2 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t13(a11, e) {
  return openBlock(), createElementBlock("svg", r2, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m10.812 2.492-8.32 8.32a1.68 1.68 0 0 0 0 2.377l8.32 8.319a1.68 1.68 0 0 0 2.377 0l8.319-8.32a1.68 1.68 0 0 0 0-2.377l-8.32-8.319a1.68 1.68 0 0 0-2.377 0ZM12 8v4"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 16a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var i6 = { render: t13 };

// node_modules/@scalar/icons/dist/icons/interface-alert-exclamation-triangle-warning.svg.js
var interface_alert_exclamation_triangle_warning_svg_exports = {};
__export(interface_alert_exclamation_triangle_warning_svg_exports, {
  default: () => c8,
  render: () => t14
});
var o = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t14(i16, e) {
  return openBlock(), createElementBlock("svg", o, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 9.57v4.296m7.926 7.516H4.074c-.798 0-1.318-.84-.96-1.554L11.04 3.976a1.074 1.074 0 0 1 1.92 0l7.927 15.852a1.074 1.074 0 0 1-.96 1.554Z"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 18a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var c8 = { render: t14 };

// node_modules/@scalar/icons/dist/icons/interface-alert-information-circle.svg.js
var interface_alert_information_circle_svg_exports = {};
__export(interface_alert_information_circle_svg_exports, {
  default: () => c9,
  render: () => l18
});
var t15 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l18(i16, r14) {
  return openBlock(), createElementBlock("svg", t15, r14[0] || (r14[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-miterlimit": "10",
      d: "M12 17v-6 0a.5.5 0 0 0-.5-.5l-.5.001h-1M12 17h-2m2 0h2"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M10.75 7.5a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var c9 = { render: l18 };

// node_modules/@scalar/icons/dist/icons/interface-award-crown.svg.js
var interface_award_crown_svg_exports = {};
__export(interface_award_crown_svg_exports, {
  default: () => c10,
  render: () => l19
});
var t16 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l19(a11, e) {
  return openBlock(), createElementBlock("svg", t16, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4.999 21.002H19M11.56 3.264a.5.5 0 0 1 .877 0l2.953 5.605a1 1 0 0 0 1.516.294L21.185 5.5a.5.5 0 0 1 .798.519l-2.835 10.248a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.018 6.019a.5.5 0 0 1 .798-.52l4.277 3.665a1 1 0 0 0 1.516-.294l2.953-5.606Z"
    }, null, -1)
  ]));
}
var c10 = { render: l19 };

// node_modules/@scalar/icons/dist/icons/interface-bookmark-tag.svg.js
var interface_bookmark_tag_svg_exports = {};
__export(interface_bookmark_tag_svg_exports, {
  default: () => c11,
  render: () => t17
});
var n10 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t17(a11, e) {
  return openBlock(), createElementBlock("svg", n10, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M13.527 3.525a1.8 1.8 0 0 0-1.273-.527H5.798a1.8 1.8 0 0 0-1.8 1.8v6.456c0 .478.19.936.527 1.273l7.836 7.836a2.184 2.184 0 0 0 3.078 0l5.924-5.924a2.184 2.184 0 0 0 0-3.078z"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M8 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var c11 = { render: t17 };

// node_modules/@scalar/icons/dist/icons/interface-bookmark.svg.js
var interface_bookmark_svg_exports = {};
__export(interface_bookmark_svg_exports, {
  default: () => c12,
  render: () => l20
});
var t18 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l20(s27, e) {
  return openBlock(), createElementBlock("svg", t18, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
    }, null, -1)
  ]));
}
var c12 = { render: l20 };

// node_modules/@scalar/icons/dist/icons/interface-calendar-date-one.svg.js
var interface_calendar_date_one_svg_exports = {};
__export(interface_calendar_date_one_svg_exports, {
  default: () => c13,
  render: () => l21
});
var t19 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l21(a11, e) {
  return openBlock(), createElementBlock("svg", t19, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11 14h1v4m4-16v4M3 10h18M8 2v4M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2"
    }, null, -1)
  ]));
}
var c13 = { render: l21 };

// node_modules/@scalar/icons/dist/icons/interface-content-book-open-pages.svg.js
var interface_content_book_open_pages_svg_exports = {};
__export(interface_content_book_open_pages_svg_exports, {
  default: () => c14,
  render: () => l22
});
var r3 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l22(s27, e) {
  return openBlock(), createElementBlock("svg", r3, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M17.333 5.01c1.39-.224 2.796-.34 4.205-.346.121 0 .238.044.329.125.085.084.133.2.133.32v14a.453.453 0 0 1-.444.444C14.186 19.66 12 21.997 12 21.997m0 0V7.091m0 14.906s-2.187-2.337-9.556-2.444A.453.453 0 0 1 2 19.109v-14c0-.12.048-.236.133-.32a.5.5 0 0 1 .33-.125C9.822 4.762 12 7.091 12 7.091m0 0a5.5 5.5 0 0 1 1.458-3.05 6.9 6.9 0 0 1 3.235-2.017.53.53 0 0 1 .436.053.41.41 0 0 1 .204.356V16.31a7.27 7.27 0 0 0-3.875 2.186A5.33 5.33 0 0 0 12 21.562"
    }, null, -1)
  ]));
}
var c14 = { render: l22 };

// node_modules/@scalar/icons/dist/icons/interface-content-book-page.svg.js
var interface_content_book_page_svg_exports = {};
__export(interface_content_book_page_svg_exports, {
  default: () => c15,
  render: () => l23
});
var r4 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l23(a11, e) {
  return openBlock(), createElementBlock("svg", r4, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11 3v8l3-3 3 3V3M5 3h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2"
    }, null, -1)
  ]));
}
var c15 = { render: l23 };

// node_modules/@scalar/icons/dist/icons/interface-content-file.svg.js
var interface_content_file_svg_exports = {};
__export(interface_content_file_svg_exports, {
  default: () => s7,
  render: () => l24
});
var r5 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l24(a11, e) {
  return openBlock(), createElementBlock("svg", r5, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15 3v4a2 2 0 0 0 2 2h4m-5-6H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8z"
    }, null, -1)
  ]));
}
var s7 = { render: l24 };

// node_modules/@scalar/icons/dist/icons/interface-content-folder.svg.js
var interface_content_folder_svg_exports = {};
__export(interface_content_folder_svg_exports, {
  default: () => c16,
  render: () => l25
});
var r6 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l25(a11, e) {
  return openBlock(), createElementBlock("svg", r6, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M13 6h6a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.93a2 2 0 0 1 1.67.9l.81 1.2a2 2 0 0 0 1.69.9"
    }, null, -1)
  ]));
}
var c16 = { render: l25 };

// node_modules/@scalar/icons/dist/icons/interface-copy-clipboard.svg.js
var interface_copy_clipboard_svg_exports = {};
__export(interface_copy_clipboard_svg_exports, {
  default: () => s8,
  render: () => a3
});
var t20 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a3(l68, e) {
  return openBlock(), createElementBlock("svg", t20, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2m1-2h6a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1"
    }, null, -1)
  ]));
}
var s8 = { render: a3 };

// node_modules/@scalar/icons/dist/icons/interface-edit-attachment.svg.js
var interface_edit_attachment_svg_exports = {};
__export(interface_edit_attachment_svg_exports, {
  default: () => i7,
  render: () => l26
});
var r7 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l26(a11, e) {
  return openBlock(), createElementBlock("svg", r7, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m20.72 11.12-8.51 8.51a5.559 5.559 0 1 1-7.86-7.861l7.935-7.936a3.71 3.71 0 0 1 5.25 5.241L9.581 17.01a1.853 1.853 0 1 1-2.62-2.62l7.861-7.853"
    }, null, -1)
  ]));
}
var i7 = { render: l26 };

// node_modules/@scalar/icons/dist/icons/interface-edit-binocular.svg.js
var interface_edit_binocular_svg_exports = {};
__export(interface_edit_binocular_svg_exports, {
  default: () => c17,
  render: () => l27
});
var t21 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l27(a11, e) {
  return openBlock(), createElementBlock("svg", t21, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M10.2 16.8a3.6 3.6 0 1 1-7.2 0 3.6 3.6 0 0 1 7.2 0m0 0V5.7a2.1 2.1 0 0 0-4.125-.56L3.13 15.844m10.67.956a3.6 3.6 0 1 0 7.2 0 3.6 3.6 0 0 0-7.2 0m0 0V5.7a2.1 2.1 0 0 1 4.125-.56l2.945 10.7M10.2 9.6h3.6V12h-3.6z"
    }, null, -1)
  ]));
}
var c17 = { render: l27 };

// node_modules/@scalar/icons/dist/icons/interface-edit-magic-wand.svg.js
var interface_edit_magic_wand_svg_exports = {};
__export(interface_edit_magic_wand_svg_exports, {
  default: () => d3,
  render: () => l28
});
var r8 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l28(i16, e) {
  return openBlock(), createElementBlock("svg", r8, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15 6V4m2 2.983 1.414-1.415M18 9h2m-3 2 1.2 1.2M4 20 15 9m-1.8-1.8L12 6"
    }, null, -1)
  ]));
}
var d3 = { render: l28 };

// node_modules/@scalar/icons/dist/icons/interface-edit-tool-paint-roller.svg.js
var interface_edit_tool_paint_roller_svg_exports = {};
__export(interface_edit_tool_paint_roller_svg_exports, {
  default: () => s9,
  render: () => l29
});
var r9 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l29(a11, e) {
  return openBlock(), createElementBlock("svg", r9, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M13.8 15.6v-1.8A1.8 1.8 0 0 0 12 12H4.8A1.8 1.8 0 0 1 3 10.2V7.5a1.8 1.8 0 0 1 1.8-1.8h1.8M19.2 3H8.4a1.8 1.8 0 0 0-1.8 1.8v1.8a1.8 1.8 0 0 0 1.8 1.8h10.8A1.8 1.8 0 0 0 21 6.6V4.8A1.8 1.8 0 0 0 19.2 3m-4.5 12.6h-1.8a.9.9 0 0 0-.9.9v3.6a.9.9 0 0 0 .9.9h1.8a.9.9 0 0 0 .9-.9v-3.6a.9.9 0 0 0-.9-.9"
    }, null, -1)
  ]));
}
var s9 = { render: l29 };

// node_modules/@scalar/icons/dist/icons/interface-edit-tool-pencil.svg.js
var interface_edit_tool_pencil_svg_exports = {};
__export(interface_edit_tool_pencil_svg_exports, {
  default: () => s10,
  render: () => l30
});
var r10 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l30(i16, e) {
  return openBlock(), createElementBlock("svg", r10, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M16.494 3.913a2.56 2.56 0 0 1 1.855-.905 2.58 2.58 0 0 1 1.923.749 2.54 2.54 0 0 1 .741 1.915 2.53 2.53 0 0 1-.924 1.835l-12.13 12.13-4.943 1.348 1.348-4.942z"
    }, null, -1)
  ]));
}
var s10 = { render: l30 };

// node_modules/@scalar/icons/dist/icons/interface-favorite-award.svg.js
var interface_favorite_award_svg_exports = {};
__export(interface_favorite_award_svg_exports, {
  default: () => s11,
  render: () => l31
});
var n11 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l31(a11, e) {
  return openBlock(), createElementBlock("svg", n11, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526M18 8A6 6 0 1 1 6 8a6 6 0 0 1 12 0"
    }, null, -1)
  ]));
}
var s11 = { render: l31 };

// node_modules/@scalar/icons/dist/icons/interface-favorite-flag.svg.js
var interface_favorite_flag_svg_exports = {};
__export(interface_favorite_flag_svg_exports, {
  default: () => c18,
  render: () => l32
});
var n12 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l32(s27, e) {
  return openBlock(), createElementBlock("svg", n12, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4.8 14.7s.9-.9 3.6-.9 4.5 1.8 7.2 1.8 3.6-.9 3.6-.9V3.9s-.9.9-3.6.9S11.1 3 8.4 3s-3.6.9-3.6.9zm0 0V21"
    }, null, -1)
  ]));
}
var c18 = { render: l32 };

// node_modules/@scalar/icons/dist/icons/interface-favorite-heart.svg.js
var interface_favorite_heart_svg_exports = {};
__export(interface_favorite_heart_svg_exports, {
  default: () => s12,
  render: () => l33
});
var n13 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l33(c34, e) {
  return openBlock(), createElementBlock("svg", n13, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7z"
    }, null, -1)
  ]));
}
var s12 = { render: l33 };

// node_modules/@scalar/icons/dist/icons/interface-favorite-star.svg.js
var interface_favorite_star_svg_exports = {};
__export(interface_favorite_star_svg_exports, {
  default: () => s13,
  render: () => l34
});
var n14 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l34(a11, e) {
  return openBlock(), createElementBlock("svg", n14, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.12 2.12 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.12 2.12 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.12 2.12 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.12 2.12 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.12 2.12 0 0 0 1.597-1.16z"
    }, null, -1)
  ]));
}
var s13 = { render: l34 };

// node_modules/@scalar/icons/dist/icons/interface-favorite-stars-sparkles.svg.js
var interface_favorite_stars_sparkles_svg_exports = {};
__export(interface_favorite_stars_sparkles_svg_exports, {
  default: () => i8,
  render: () => l35
});
var n15 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l35(a11, e) {
  return openBlock(), createElementBlock("svg", n15, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4 3v4M2 5h4m14 12v2m-1-1h2m-6.937-2.5a2 2 0 0 1 1.437-1.437l6.135-1.582a.5.5 0 0 0 0-.962L15.5 9.936A2 2 0 0 1 14.063 8.5l-1.582-6.135a.5.5 0 0 0-.963 0L9.937 8.5A2 2 0 0 1 8.5 9.937l-6.135 1.581a.5.5 0 0 0 0 .964L8.5 14.063A2 2 0 0 1 9.937 15.5l1.582 6.135a.5.5 0 0 0 .963 0z"
    }, null, -1)
  ]));
}
var i8 = { render: l35 };

// node_modules/@scalar/icons/dist/icons/interface-hierarchy-flowchart.svg.js
var interface_hierarchy_flowchart_svg_exports = {};
__export(interface_hierarchy_flowchart_svg_exports, {
  default: () => s14,
  render: () => l36
});
var n16 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l36(c34, e) {
  return openBlock(), createElementBlock("svg", n16, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M7 11v4a2 2 0 0 0 2 2h4"
    }, null, -1),
    createBaseVNode("circle", {
      cx: "7",
      cy: "7",
      r: "3.25",
      stroke: "currentColor"
    }, null, -1),
    createBaseVNode("circle", {
      cx: "17",
      cy: "17",
      r: "3.25",
      stroke: "currentColor"
    }, null, -1)
  ]));
}
var s14 = { render: l36 };

// node_modules/@scalar/icons/dist/icons/interface-home-house.svg.js
var interface_home_house_svg_exports = {};
__export(interface_home_house_svg_exports, {
  default: () => c19,
  render: () => l37
});
var t22 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l37(a11, e) {
  return openBlock(), createElementBlock("svg", t22, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15 21v-6a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v6M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
    }, null, -1)
  ]));
}
var c19 = { render: l37 };

// node_modules/@scalar/icons/dist/icons/interface-hyperlink.svg.js
var interface_hyperlink_svg_exports = {};
__export(interface_hyperlink_svg_exports, {
  default: () => c20,
  render: () => l38
});
var t23 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l38(i16, e) {
  return openBlock(), createElementBlock("svg", t23, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M13.414 17.657 12 19.07A5 5 0 0 1 4.929 12l1.414-1.414m4.243-4.243L12 4.93A5 5 0 0 1 19.071 12l-1.414 1.414m-8.485 1.414 5.656-5.656"
    }, null, -1)
  ]));
}
var c20 = { render: l38 };

// node_modules/@scalar/icons/dist/icons/interface-lighting-brightness.svg.js
var interface_lighting_brightness_svg_exports = {};
__export(interface_lighting_brightness_svg_exports, {
  default: () => c21,
  render: () => l39
});
var r11 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l39(i16, e) {
  return openBlock(), createElementBlock("svg", r11, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 2v2m0 16v2M4.93 4.93l1.41 1.41m11.32 11.32 1.41 1.41M2 12h2m16 0h2M6.34 17.66l-1.41 1.41M19.07 4.93l-1.41 1.41M16 12a4 4 0 1 1-8 0 4 4 0 0 1 8 0"
    }, null, -1)
  ]));
}
var c21 = { render: l39 };

// node_modules/@scalar/icons/dist/icons/interface-lock-closed.svg.js
var interface_lock_closed_svg_exports = {};
__export(interface_lock_closed_svg_exports, {
  default: () => i9,
  render: () => t24
});
var l40 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t24(c34, e) {
  return openBlock(), createElementBlock("svg", l40, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M5.778 10h12.444c.982 0 1.778.895 1.778 2v8c0 1.105-.796 2-1.778 2H5.778C4.796 22 4 21.105 4 20v-8c0-1.105.796-2 1.778-2M7 10V7a5 5 0 1 1 10 0v3"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 16a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var i9 = { render: t24 };

// node_modules/@scalar/icons/dist/icons/interface-lock-open-unlock.svg.js
var interface_lock_open_unlock_svg_exports = {};
__export(interface_lock_open_unlock_svg_exports, {
  default: () => i10,
  render: () => t25
});
var l41 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t25(c34, e) {
  return openBlock(), createElementBlock("svg", l41, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M5.778 10h12.444c.982 0 1.778.895 1.778 2v8c0 1.105-.796 2-1.778 2H5.778C4.796 22 4 21.105 4 20v-8c0-1.105.796-2 1.778-2M7 10V7a5 5 0 1 1 10 0v3"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M11 16a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var i10 = { render: t25 };

// node_modules/@scalar/icons/dist/icons/interface-login-key.svg.js
var interface_login_key_svg_exports = {};
__export(interface_login_key_svg_exports, {
  default: () => c22,
  render: () => l42
});
var t26 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l42(i16, e) {
  return openBlock(), createElementBlock("svg", t26, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M7.5 21a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11m3.9-9.4L19 4m-3.5 3.5 3 3M22 7l-3-3"
    }, null, -1)
  ]));
}
var c22 = { render: l42 };

// node_modules/@scalar/icons/dist/icons/interface-search.svg.js
var interface_search_svg_exports = {};
__export(interface_search_svg_exports, {
  default: () => i11,
  render: () => l43
});
var t27 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l43(c34, e) {
  return openBlock(), createElementBlock("svg", t27, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16m10 2-4.35-4.35"
    }, null, -1)
  ]));
}
var i11 = { render: l43 };

// node_modules/@scalar/icons/dist/icons/interface-setting-cog.svg.js
var interface_setting_cog_svg_exports = {};
__export(interface_setting_cog_svg_exports, {
  default: () => s15,
  render: () => r12
});
var n17 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function r12(t52, e) {
  return openBlock(), createElementBlock("svg", n17, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M2 11.78v.44a2 2 0 0 0 2 2h.18a2 2 0 0 1 1.73 1l.25.43a2 2 0 0 1 0 2l-.08.15a2 2 0 0 0 .73 2.73l.38.22a2 2 0 0 0 2.73-.73l.1-.15a2 2 0 0 1 1.72-1h.51a2 2 0 0 1 1.74 1l.09.15a2 2 0 0 0 2.73.73l.38-.22a2 2 0 0 0 .73-2.73l-.08-.15a2 2 0 0 1 0-2l.25-.43a2 2 0 0 1 1.73-1H20a2 2 0 0 0 2-2v-.44a2 2 0 0 0-2-2h-.18a2 2 0 0 1-1.73-1l-.25-.43a2 2 0 0 1 0-2l.08-.15a2 2 0 0 0-.73-2.73l-.39-.22a2 2 0 0 0-2.73.73l-.08.15a2 2 0 0 1-1.74 1h-.5a2 2 0 0 1-1.74-1l-.09-.15a2 2 0 0 0-2.73-.73l-.38.22a2 2 0 0 0-.73 2.73l.08.15a2 2 0 0 1 0 2l-.25.43a2 2 0 0 1-1.73 1H4a2 2 0 0 0-2 2"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M15 12a3 3 0 1 0-6 0 3 3 0 0 0 6 0"
    }, null, -1)
  ]));
}
var s15 = { render: r12 };

// node_modules/@scalar/icons/dist/icons/interface-share-megaphone-bullhorn.svg.js
var interface_share_megaphone_bullhorn_svg_exports = {};
__export(interface_share_megaphone_bullhorn_svg_exports, {
  default: () => c23,
  render: () => l44
});
var t28 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l44(a11, e) {
  return openBlock(), createElementBlock("svg", t28, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M16 9a5 5 0 0 1 0 6m3.364 3.364a9 9 0 0 0 0-12.728M11 4.702a.706.706 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z"
    }, null, -1)
  ]));
}
var c23 = { render: l44 };

// node_modules/@scalar/icons/dist/icons/interface-share-rocket.svg.js
var interface_share_rocket_svg_exports = {};
__export(interface_share_rocket_svg_exports, {
  default: () => a4,
  render: () => l45
});
var n18 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l45(c34, e) {
  return openBlock(), createElementBlock("svg", n18, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11.566 18.582v2.778s3.367-.611 4.445-2.222c.58-.87.599-2.196.465-3.334m-10.698-3.01H3s.611-3.367 2.222-4.445c.87-.58 2.196-.6 3.334-.465m-.82 8.883-1.571-1.571-.625-.625a20.2 20.2 0 0 1 1.837-4.667 10.76 10.76 0 0 1 5.199-4.825c2.26-1.003 4.914-1.314 7.712-.903.486 3.46.349 9.4-5.665 12.92a20.6 20.6 0 0 1-4.73 1.828l-.586-.585zm0 0L6.165 18.34"
    }, null, -1)
  ]));
}
var a4 = { render: l45 };

// node_modules/@scalar/icons/dist/icons/interface-share-satellite.svg.js
var interface_share_satellite_svg_exports = {};
__export(interface_share_satellite_svg_exports, {
  default: () => s16,
  render: () => l46
});
var n19 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l46(a11, e) {
  return openBlock(), createElementBlock("svg", n19, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m9 15 3-3m5 1a6 6 0 0 0-6-6m10 6A10 10 0 0 0 11 3m-7 7a7.31 7.31 0 0 0 10 10z"
    }, null, -1)
  ]));
}
var s16 = { render: l46 };

// node_modules/@scalar/icons/dist/icons/interface-share-space-ship.svg.js
var interface_share_space_ship_svg_exports = {};
__export(interface_share_space_ship_svg_exports, {
  default: () => c24,
  render: () => l47
});
var t29 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l47(a11, e) {
  return openBlock(), createElementBlock("svg", t29, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4 3v4M2 5h4m14 12v2m-1-1h2m-10.582-5.578L5.368 13.5a.763.763 0 0 1-.906-.574l-.439-1.757a.875.875 0 0 1 .565-1.034l11.036-3.629m-2.35 5.307 3.541-.755m-1.546 8.317L12.73 14.3M7.225 9.268l.91 3.642m.596 6.465 2.537-5.075m4.397-7.233a1.635 1.635 0 0 1 1.19-1.982l.89-.222a.817.817 0 0 1 .99.594l1.239 4.953a.82.82 0 0 1-.594.99l-.891.223a1.634 1.634 0 0 1-1.982-1.189zm-2.03 5.77a1.635 1.635 0 1 1-3.27 0 1.635 1.635 0 0 1 3.27 0"
    }, null, -1)
  ]));
}
var c24 = { render: l47 };

// node_modules/@scalar/icons/dist/icons/interface-share.svg.js
var interface_share_svg_exports = {};
__export(interface_share_svg_exports, {
  default: () => c25,
  render: () => l48
});
var t30 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l48(a11, e) {
  return openBlock(), createElementBlock("svg", t30, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m7.59 13.51 6.83 3.98m-.01-10.98-6.82 3.98M20 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0M8 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0m12 7a3 3 0 1 1-6 0 3 3 0 0 1 6 0"
    }, null, -1)
  ]));
}
var c25 = { render: l48 };

// node_modules/@scalar/icons/dist/icons/interface-signal-square.svg.js
var interface_signal_square_svg_exports = {};
__export(interface_signal_square_svg_exports, {
  default: () => s17,
  render: () => l49
});
var t31 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l49(a11, e) {
  return openBlock(), createElementBlock("svg", t31, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M18 12h-1.488a1.2 1.2 0 0 0-1.158.876l-1.41 5.016a.15.15 0 0 1-.288 0L10.344 6.108a.15.15 0 0 0-.288 0l-1.41 5.016A1.2 1.2 0 0 1 7.494 12H6"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2"
    }, null, -1)
  ]));
}
var s17 = { render: l49 };

// node_modules/@scalar/icons/dist/icons/interface-time-clock-circle.svg.js
var interface_time_clock_circle_svg_exports = {};
__export(interface_time_clock_circle_svg_exports, {
  default: () => s18,
  render: () => l50
});
var t32 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l50(c34, e) {
  return openBlock(), createElementBlock("svg", t32, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 6v6l4 2M2 12a10 10 0 1 0 20 0 10 10 0 0 0-20 0"
    }, null, -1)
  ]));
}
var s18 = { render: l50 };

// node_modules/@scalar/icons/dist/icons/interface-time-hour-glass.svg.js
var interface_time_hour_glass_svg_exports = {};
__export(interface_time_hour_glass_svg_exports, {
  default: () => s19,
  render: () => l51
});
var t33 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l51(a11, e) {
  return openBlock(), createElementBlock("svg", t33, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M17.263 5.71v.181c0 .558-.222 1.094-.617 1.489l-3.157 3.158a2.105 2.105 0 0 1-2.978 0L7.353 7.38a2.1 2.1 0 0 1-.616-1.489v-.18m10.526 0V3.605A2.105 2.105 0 0 0 15.158 1.5H8.842a2.105 2.105 0 0 0-2.105 2.105v2.106m10.526 0H6.737m0 15.789h10.526m-10.526 0 3.948-3.158a2.105 2.105 0 0 1 2.63 0l3.948 3.158m-10.526 0v-4.391c0-.559.222-1.094.616-1.489l3.158-3.158a2.105 2.105 0 0 1 2.978 0l3.157 3.158c.395.395.617.93.617 1.489V21.5"
    }, null, -1)
  ]));
}
var s19 = { render: l51 };

// node_modules/@scalar/icons/dist/icons/interface-users-multiple.svg.js
var interface_users_multiple_svg_exports = {};
__export(interface_users_multiple_svg_exports, {
  default: () => i12,
  render: () => l52
});
var n20 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l52(s27, e) {
  return openBlock(), createElementBlock("svg", n20, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2m20 0v-2a4 4 0 0 0-3-3.87m-3-12a4 4 0 0 1 0 7.75M13 7a4 4 0 1 1-8 0 4 4 0 0 1 8 0"
    }, null, -1)
  ]));
}
var i12 = { render: l52 };

// node_modules/@scalar/icons/dist/icons/interface-weather-moon.svg.js
var interface_weather_moon_svg_exports = {};
__export(interface_weather_moon_svg_exports, {
  default: () => c26,
  render: () => l53
});
var t34 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l53(a11, e) {
  return openBlock(), createElementBlock("svg", t34, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 2a7.071 7.071 0 1 0 10 10M12 2a10 10 0 0 0-7.071 17.071M12 2a10 10 0 0 0-7.071 17.071M22 12a10 10 0 0 1-17.071 7.071M22 12a10 10 0 0 1-17.071 7.071"
    }, null, -1)
  ]));
}
var c26 = { render: l53 };

// node_modules/@scalar/icons/dist/icons/mail-chat-bubble-square.svg.js
var mail_chat_bubble_square_svg_exports = {};
__export(mail_chat_bubble_square_svg_exports, {
  default: () => i13,
  render: () => l54
});
var n21 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l54(a11, e) {
  return openBlock(), createElementBlock("svg", n21, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
    }, null, -1)
  ]));
}
var i13 = { render: l54 };

// node_modules/@scalar/icons/dist/icons/mail-send-email-paper-airplane.svg.js
var mail_send_email_paper_airplane_svg_exports = {};
__export(mail_send_email_paper_airplane_svg_exports, {
  default: () => s20,
  render: () => l55
});
var t35 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l55(a11, e) {
  return openBlock(), createElementBlock("svg", t35, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M11.023 12.976a1.8 1.8 0 0 0-.603-.397L3.284 9.718a.45.45 0 0 1 .021-.843l17.098-5.85a.446.446 0 0 1 .572.572l-5.85 17.098a.45.45 0 0 1-.843.021l-2.861-7.138a1.8 1.8 0 0 0-.398-.601Zm0 0 9.845-9.843"
    }, null, -1)
  ]));
}
var s20 = { render: l55 };

// node_modules/@scalar/icons/dist/icons/mail-send-envelope.svg.js
var mail_send_envelope_svg_exports = {};
__export(mail_send_envelope_svg_exports, {
  default: () => d4,
  render: () => l56
});
var t36 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l56(a11, e) {
  return openBlock(), createElementBlock("svg", t36, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7m2-3h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2"
    }, null, -1)
  ]));
}
var d4 = { render: l56 };

// node_modules/@scalar/icons/dist/icons/money-cashier-receipt.svg.js
var money_cashier_receipt_svg_exports = {};
__export(money_cashier_receipt_svg_exports, {
  default: () => i14,
  render: () => a5
});
var t37 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a5(l68, e) {
  return openBlock(), createElementBlock("svg", t37, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M9 12h5M9 8h5m-9 9V5a2 2 0 0 1 2-2h13m0 0a2 2 0 0 0-2 2v14a2 2 0 0 1-2 2m4-18a2 2 0 0 1 2 2v2a1 1 0 0 1-1 1h-3m-2 13H4a2 2 0 0 1-2-2v-1a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v1a2 2 0 0 0 2 2"
    }, null, -1)
  ]));
}
var i14 = { render: a5 };

// node_modules/@scalar/icons/dist/icons/money-currency-dollar-pay.svg.js
var money_currency_dollar_pay_svg_exports = {};
__export(money_currency_dollar_pay_svg_exports, {
  default: () => c27,
  render: () => l57
});
var t38 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l57(s27, e) {
  return openBlock(), createElementBlock("svg", t38, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 2v20m5-17H9.5a3.5 3.5 0 1 0 0 7h5a3.5 3.5 0 1 1 0 7H6"
    }, null, -1)
  ]));
}
var c27 = { render: l57 };

// node_modules/@scalar/icons/dist/icons/money-graph-arrow-increase.svg.js
var money_graph_arrow_increase_svg_exports = {};
__export(money_graph_arrow_increase_svg_exports, {
  default: () => d5,
  render: () => l58
});
var t39 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l58(s27, e) {
  return openBlock(), createElementBlock("svg", t39, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m22 7-8.5 8.5-5-5L2 17M22 7h-6m6 0v6"
    }, null, -1)
  ]));
}
var d5 = { render: l58 };

// node_modules/@scalar/icons/dist/icons/money-graph-bar-chart-increase.svg.js
var money_graph_bar_chart_increase_svg_exports = {};
__export(money_graph_bar_chart_increase_svg_exports, {
  default: () => c28,
  render: () => a6
});
var t40 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a6(l68, e) {
  return openBlock(), createElementBlock("svg", t40, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M8 17v-7m4 7v-4m4 4V8M5 21h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2"
    }, null, -1)
  ]));
}
var c28 = { render: a6 };

// node_modules/@scalar/icons/dist/icons/nature-ecology-leaf.svg.js
var nature_ecology_leaf_svg_exports = {};
__export(nature_ecology_leaf_svg_exports, {
  default: () => a7,
  render: () => l59
});
var t41 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l59(c34, e) {
  return openBlock(), createElementBlock("svg", t41, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M3.011 20.99c1.124-.887 2.04-1.775 3.506-1.977 1.268 0 2.49.481 3.416 1.348a3.595 3.595 0 0 0 5.561-2.115 3.146 3.146 0 0 0 3.317-4.599 3.148 3.148 0 0 0 1.234-5.241 3.145 3.145 0 1 0-4.45-4.45 3.147 3.147 0 0 0-5.24 1.234 3.146 3.146 0 0 0-4.6 3.317 3.596 3.596 0 0 0-2.11 5.533c.714 1.035 1.532 2.197 1.344 3.444-.247 1.477-1.066 2.366-1.978 3.506m0 0L16.494 7.507"
    }, null, -1)
  ]));
}
var a7 = { render: l59 };

// node_modules/@scalar/icons/dist/icons/phone-telephone.svg.js
var phone_telephone_svg_exports = {};
__export(phone_telephone_svg_exports, {
  default: () => c29,
  render: () => l60
});
var t42 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l60(a11, e) {
  return openBlock(), createElementBlock("svg", t42, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M21.038 16.44v2.71a1.81 1.81 0 0 1-1.97 1.807 17.9 17.9 0 0 1-7.794-2.773 17.6 17.6 0 0 1-5.42-5.42 17.9 17.9 0 0 1-2.773-7.83 1.806 1.806 0 0 1 1.798-1.97h2.71a1.806 1.806 0 0 1 1.806 1.554 11.6 11.6 0 0 0 .632 2.538 1.8 1.8 0 0 1-.406 1.906l-1.147 1.147a14.45 14.45 0 0 0 5.42 5.42l1.146-1.148a1.81 1.81 0 0 1 1.906-.406c.82.305 1.671.518 2.538.632a1.807 1.807 0 0 1 1.554 1.833"
    }, null, -1)
  ]));
}
var c29 = { render: l60 };

// node_modules/@scalar/icons/dist/icons/programming-bug.svg.js
var programming_bug_svg_exports = {};
__export(programming_bug_svg_exports, {
  default: () => s21,
  render: () => c30
});
var t43 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function c30(m3, e) {
  return openBlock(), createElementBlock("svg", t43, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m8.21 2.5 1.782 1.781m4.016 0L15.79 2.5M9.158 7.36v-.947a2.845 2.845 0 1 1 5.684 0v.947M12 19.553c-3.126 0-5.684-2.558-5.684-5.685v-2.842a3.79 3.79 0 0 1 3.79-3.79h3.789a3.79 3.79 0 0 1 3.79 3.79v2.842c0 3.127-2.559 5.685-5.685 5.685m0 0v-8.527M6.818 9.132c-1.829-.19-3.344-1.8-3.344-3.79m2.842 7.58h-3.79m.948 7.578c0-1.99 1.61-3.695 3.6-3.79M20.498 5.342c0 1.99-1.516 3.6-3.316 3.79m4.292 3.79h-3.79m-.758 3.788c1.99.095 3.6 1.8 3.6 3.79"
    }, null, -1)
  ]));
}
var s21 = { render: c30 };

// node_modules/@scalar/icons/dist/icons/programming-cloud.svg.js
var programming_cloud_svg_exports = {};
__export(programming_cloud_svg_exports, {
  default: () => i15,
  render: () => l61
});
var t44 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l61(s27, e) {
  return openBlock(), createElementBlock("svg", t44, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M17.662 18.661a4.33 4.33 0 1 0-2.455-7.891 6.662 6.662 0 1 0-6.537 7.89h8.993Z"
    }, null, -1)
  ]));
}
var i15 = { render: l61 };

// node_modules/@scalar/icons/dist/icons/programming-computer-database-server.svg.js
var programming_computer_database_server_svg_exports = {};
__export(programming_computer_database_server_svg_exports, {
  default: () => u2,
  render: () => l62
});
var t45 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l62(a11, r14) {
  return openBlock(), createElementBlock("svg", t45, r14[0] || (r14[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M20 12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2m16 0H4m16 0a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      d: "M18 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2m0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2"
    }, null, -1),
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M6 8h4m-4 8h4"
    }, null, -1)
  ]));
}
var u2 = { render: l62 };

// node_modules/@scalar/icons/dist/icons/programming-computer-database.svg.js
var programming_computer_database_svg_exports = {};
__export(programming_computer_database_svg_exports, {
  default: () => m2,
  render: () => s22
});
var n22 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function s22(l68, e) {
  return openBlock(), createElementBlock("svg", n22, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M3 6.601c0 .955.948 1.87 2.636 2.546S9.613 10.2 12 10.2s4.676-.38 6.364-1.054C20.052 8.47 21 7.556 21 6.6m-18 0c0-.955.948-1.87 2.636-2.546C7.324 3.38 9.613 3.001 12 3.001s4.676.38 6.364 1.054C20.052 4.731 21 5.646 21 6.601m-18 0v5.4m18-5.4v5.4M3 12c0 1.987 4.03 3.6 9 3.6s9-1.612 9-3.6M3 12v5.4c0 1.987 4.03 3.6 9 3.6s9-1.613 9-3.6V12"
    }, null, -1)
  ]));
}
var m2 = { render: s22 };

// node_modules/@scalar/icons/dist/icons/programming-module-four-layout.svg.js
var programming_module_four_layout_svg_exports = {};
__export(programming_module_four_layout_svg_exports, {
  default: () => u3,
  render: () => t46
});
var n23 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t46(l68, e) {
  return openBlock(), createElementBlock("svg", n23, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M9 3H4a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1m11 0h-5a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1m0 11h-5a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1M9 14H4a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1"
    }, null, -1)
  ]));
}
var u3 = { render: t46 };

// node_modules/@scalar/icons/dist/icons/programming-module-three.svg.js
var programming_module_three_svg_exports = {};
__export(programming_module_three_svg_exports, {
  default: () => d6,
  render: () => a8
});
var t47 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a8(l68, e) {
  return openBlock(), createElementBlock("svg", t47, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M20 14h-5a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1M9 14H4a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1m5-11H9a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1"
    }, null, -1)
  ]));
}
var d6 = { render: a8 };

// node_modules/@scalar/icons/dist/icons/programming-module.svg.js
var programming_module_svg_exports = {};
__export(programming_module_svg_exports, {
  default: () => s23,
  render: () => l63
});
var t48 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l63(a11, e) {
  return openBlock(), createElementBlock("svg", t48, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m3.3 7 8.7 5m0 0 8.7-5M12 12v10m9-14a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
    }, null, -1)
  ]));
}
var s23 = { render: l63 };

// node_modules/@scalar/icons/dist/icons/programming-script-code.svg.js
var programming_script_code_svg_exports = {};
__export(programming_script_code_svg_exports, {
  default: () => s24,
  render: () => l64
});
var t49 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l64(c34, e) {
  return openBlock(), createElementBlock("svg", t49, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "m7 14 2.5-2.5L7 9m5 7.5h5M5 4h14c1.105 0 2 .796 2 1.778v12.444c0 .982-.895 1.778-2 1.778H5c-1.105 0-2-.796-2-1.778V5.778C3 4.796 3.895 4 5 4"
    }, null, -1)
  ]));
}
var s24 = { render: l64 };

// node_modules/@scalar/icons/dist/icons/shopping-cart.svg.js
var shopping_cart_svg_exports = {};
__export(shopping_cart_svg_exports, {
  default: () => s25,
  render: () => t50
});
var n24 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function t50(d8, e) {
  return openBlock(), createElementBlock("svg", n24, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      "stroke-width": "1.347",
      d: "M2.02 3.015h1.796l2.39 11.156a1.8 1.8 0 0 0 1.796 1.42h8.784a1.796 1.796 0 0 0 1.752-1.411l1.482-6.674H4.777"
    }, null, -1),
    createBaseVNode("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      d: "M7 20a1 1 0 1 1 2 0 1 1 0 0 1-2 0m9 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0",
      "clip-rule": "evenodd"
    }, null, -1)
  ]));
}
var s25 = { render: t50 };

// node_modules/@scalar/icons/dist/icons/shopping-gift-present.svg.js
var shopping_gift_present_svg_exports = {};
__export(shopping_gift_present_svg_exports, {
  default: () => c31,
  render: () => a9
});
var r13 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function a9(l68, e) {
  return openBlock(), createElementBlock("svg", r13, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M12 8v13m0-13c-.362-1.49-.985-2.765-1.787-3.657C9.41 3.451 8.465 2.983 7.5 3a2.5 2.5 0 1 0 0 5M12 8c.362-1.49.985-2.765 1.787-3.657.803-.892 1.748-1.36 2.713-1.343a2.5 2.5 0 0 1 0 5m2.5 4v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7M4 8h16a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1"
    }, null, -1)
  ]));
}
var c31 = { render: a9 };

// node_modules/@scalar/icons/dist/icons/shopping-shipping-box-parcel-package.svg.js
var shopping_shipping_box_parcel_package_svg_exports = {};
__export(shopping_shipping_box_parcel_package_svg_exports, {
  default: () => s26,
  render: () => l65
});
var t51 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l65(a11, e) {
  return openBlock(), createElementBlock("svg", t51, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M3 9h18M3 9v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9M3 9l2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9m-9-6v6"
    }, null, -1)
  ]));
}
var s26 = { render: l65 };

// node_modules/@scalar/icons/dist/icons/tag-new-circle.svg.js
var tag_new_circle_svg_exports = {};
__export(tag_new_circle_svg_exports, {
  default: () => c32,
  render: () => l66
});
var n25 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l66(a11, e) {
  return openBlock(), createElementBlock("svg", n25, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M4.929 19.071a10 10 0 0 0 14.142 0m0-14.142a10 10 0 0 0-14.142 0M4 15.5v-7l2.8 7v-7m5.6 7H11a1.4 1.4 0 0 1-1.4-1.4V9.9A1.4 1.4 0 0 1 11 8.5h1.4m-2.8 4.2h2.8m2.8-4.2v1.883a8.4 8.4 0 0 0 .601 3.119L16.6 15.5l1.4-4.9 1.4 4.9.8-1.998a8.4 8.4 0 0 0 .6-3.12V8.5"
    }, null, -1)
  ]));
}
var c32 = { render: l66 };

// node_modules/@scalar/icons/dist/icons/travel-map-earth-globe.svg.js
var travel_map_earth_globe_svg_exports = {};
__export(travel_map_earth_globe_svg_exports, {
  default: () => c33,
  render: () => l67
});
var n26 = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 24 24"
};
function l67(a11, e) {
  return openBlock(), createElementBlock("svg", n26, e[0] || (e[0] = [
    createBaseVNode("path", {
      stroke: "currentColor",
      "stroke-linecap": "round",
      "stroke-linejoin": "round",
      d: "M7 20.5 8 14h1.08a1.48 1.48 0 0 0 1.18-.58 1.44 1.44 0 0 0 .27-1.28l-.75-3A1.49 1.49 0 0 0 8.33 8H3m16-3h-3.753a1.49 1.49 0 0 0-1.45 1.14l-.75 3a1.44 1.44 0 0 0 .28 1.28 1.48 1.48 0 0 0 1.18.58h1.58l.78 4.75a1.5 1.5 0 0 0 1.46 1.25h2M2 12a10 10 0 1 0 20 0 10 10 0 0 0-20 0"
    }, null, -1)
  ]));
}
var c33 = { render: l67 };

// node_modules/@scalar/icons/dist/icons.js
var Re = Object.assign({
  "./icons/basic-shape-diamond.svg": basic_shape_diamond_svg_exports,
  "./icons/basic-shape-hexagon.svg": basic_shape_hexagon_svg_exports,
  "./icons/basic-shape-primary-circle-ellipse-round.svg": basic_shape_primary_circle_ellipse_round_svg_exports,
  "./icons/basic-shape-primary-square-rectangle.svg": basic_shape_primary_square_rectangle_svg_exports,
  "./icons/basic-shape-shield.svg": basic_shape_shield_svg_exports,
  "./icons/computer-device-desktop-monitor.svg": computer_device_desktop_monitor_svg_exports,
  "./icons/computer-device-desktop.svg": computer_device_desktop_svg_exports,
  "./icons/computer-device-laptop.svg": computer_device_laptop_svg_exports,
  "./icons/computer-device-mobile-phone-android-samsung-back.svg": computer_device_mobile_phone_android_samsung_back_svg_exports,
  "./icons/computer-device-mobile-phone-android-samsung.svg": computer_device_mobile_phone_android_samsung_svg_exports,
  "./icons/computer-device-mobile-phone-iphone-x-back.svg": computer_device_mobile_phone_iphone_x_back_svg_exports,
  "./icons/computer-device-mobile-phone-iphone-x.svg": computer_device_mobile_phone_iphone_x_svg_exports,
  "./icons/computer-device-mobile-tablet-touch.svg": computer_device_mobile_tablet_touch_svg_exports,
  "./icons/computer-device-mobile-tablet.svg": computer_device_mobile_tablet_svg_exports,
  "./icons/computer-device-network-ethernet-cat6.svg": computer_device_network_ethernet_cat6_svg_exports,
  "./icons/computer-device-network-lan-www.svg": computer_device_network_lan_www_svg_exports,
  "./icons/computer-device-network-wifi-connection.svg": computer_device_network_wifi_connection_svg_exports,
  "./icons/computer-device-network-wifi-router.svg": computer_device_network_wifi_router_svg_exports,
  "./icons/ecology-science-erlenmeyer-flask.svg": ecology_science_erlenmeyer_flask_svg_exports,
  "./icons/image-flash-lightning.svg": image_flash_lightning_svg_exports,
  "./icons/image-picture-flower.svg": image_picture_flower_svg_exports,
  "./icons/interface-alert-exclamation-diamond.svg": interface_alert_exclamation_diamond_svg_exports,
  "./icons/interface-alert-exclamation-triangle-warning.svg": interface_alert_exclamation_triangle_warning_svg_exports,
  "./icons/interface-alert-information-circle.svg": interface_alert_information_circle_svg_exports,
  "./icons/interface-award-crown.svg": interface_award_crown_svg_exports,
  "./icons/interface-bookmark-tag.svg": interface_bookmark_tag_svg_exports,
  "./icons/interface-bookmark.svg": interface_bookmark_svg_exports,
  "./icons/interface-calendar-date-one.svg": interface_calendar_date_one_svg_exports,
  "./icons/interface-content-book-open-pages.svg": interface_content_book_open_pages_svg_exports,
  "./icons/interface-content-book-page.svg": interface_content_book_page_svg_exports,
  "./icons/interface-content-file.svg": interface_content_file_svg_exports,
  "./icons/interface-content-folder.svg": interface_content_folder_svg_exports,
  "./icons/interface-copy-clipboard.svg": interface_copy_clipboard_svg_exports,
  "./icons/interface-edit-attachment.svg": interface_edit_attachment_svg_exports,
  "./icons/interface-edit-binocular.svg": interface_edit_binocular_svg_exports,
  "./icons/interface-edit-magic-wand.svg": interface_edit_magic_wand_svg_exports,
  "./icons/interface-edit-tool-paint-roller.svg": interface_edit_tool_paint_roller_svg_exports,
  "./icons/interface-edit-tool-pencil.svg": interface_edit_tool_pencil_svg_exports,
  "./icons/interface-favorite-award.svg": interface_favorite_award_svg_exports,
  "./icons/interface-favorite-flag.svg": interface_favorite_flag_svg_exports,
  "./icons/interface-favorite-heart.svg": interface_favorite_heart_svg_exports,
  "./icons/interface-favorite-star.svg": interface_favorite_star_svg_exports,
  "./icons/interface-favorite-stars-sparkles.svg": interface_favorite_stars_sparkles_svg_exports,
  "./icons/interface-hierarchy-flowchart.svg": interface_hierarchy_flowchart_svg_exports,
  "./icons/interface-home-house.svg": interface_home_house_svg_exports,
  "./icons/interface-hyperlink.svg": interface_hyperlink_svg_exports,
  "./icons/interface-lighting-brightness.svg": interface_lighting_brightness_svg_exports,
  "./icons/interface-lock-closed.svg": interface_lock_closed_svg_exports,
  "./icons/interface-lock-open-unlock.svg": interface_lock_open_unlock_svg_exports,
  "./icons/interface-login-key.svg": interface_login_key_svg_exports,
  "./icons/interface-search.svg": interface_search_svg_exports,
  "./icons/interface-setting-cog.svg": interface_setting_cog_svg_exports,
  "./icons/interface-share-megaphone-bullhorn.svg": interface_share_megaphone_bullhorn_svg_exports,
  "./icons/interface-share-rocket.svg": interface_share_rocket_svg_exports,
  "./icons/interface-share-satellite.svg": interface_share_satellite_svg_exports,
  "./icons/interface-share-space-ship.svg": interface_share_space_ship_svg_exports,
  "./icons/interface-share.svg": interface_share_svg_exports,
  "./icons/interface-signal-square.svg": interface_signal_square_svg_exports,
  "./icons/interface-time-clock-circle.svg": interface_time_clock_circle_svg_exports,
  "./icons/interface-time-hour-glass.svg": interface_time_hour_glass_svg_exports,
  "./icons/interface-users-multiple.svg": interface_users_multiple_svg_exports,
  "./icons/interface-weather-moon.svg": interface_weather_moon_svg_exports,
  "./icons/mail-chat-bubble-square.svg": mail_chat_bubble_square_svg_exports,
  "./icons/mail-send-email-paper-airplane.svg": mail_send_email_paper_airplane_svg_exports,
  "./icons/mail-send-envelope.svg": mail_send_envelope_svg_exports,
  "./icons/money-cashier-receipt.svg": money_cashier_receipt_svg_exports,
  "./icons/money-currency-dollar-pay.svg": money_currency_dollar_pay_svg_exports,
  "./icons/money-graph-arrow-increase.svg": money_graph_arrow_increase_svg_exports,
  "./icons/money-graph-bar-chart-increase.svg": money_graph_bar_chart_increase_svg_exports,
  "./icons/nature-ecology-leaf.svg": nature_ecology_leaf_svg_exports,
  "./icons/phone-telephone.svg": phone_telephone_svg_exports,
  "./icons/programming-bug.svg": programming_bug_svg_exports,
  "./icons/programming-cloud.svg": programming_cloud_svg_exports,
  "./icons/programming-computer-database-server.svg": programming_computer_database_server_svg_exports,
  "./icons/programming-computer-database.svg": programming_computer_database_svg_exports,
  "./icons/programming-module-four-layout.svg": programming_module_four_layout_svg_exports,
  "./icons/programming-module-three.svg": programming_module_three_svg_exports,
  "./icons/programming-module.svg": programming_module_svg_exports,
  "./icons/programming-script-code.svg": programming_script_code_svg_exports,
  "./icons/shopping-cart.svg": shopping_cart_svg_exports,
  "./icons/shopping-gift-present.svg": shopping_gift_present_svg_exports,
  "./icons/shopping-shipping-box-parcel-package.svg": shopping_shipping_box_parcel_package_svg_exports,
  "./icons/tag-new-circle.svg": tag_new_circle_svg_exports,
  "./icons/travel-map-earth-globe.svg": travel_map_earth_globe_svg_exports
});
function We(o2) {
  const r14 = Object.entries(o2).map(([e, s27]) => {
    const i16 = e.replace("./icons/", "").replace(".svg", "");
    return {
      icon: {
        // Prefix the src with the group so that the final flat icon map has unique keys
        src: i16,
        title: i16.replaceAll("-", " "),
        tags: []
      },
      rawData: s27
    };
  }), t52 = r14.map((e) => e.icon), n27 = Object.fromEntries(r14.map((e) => [e.icon.src, e.rawData]));
  return {
    iconDefinitionList: t52,
    iconDataMap: n27
  };
}
var a10 = We(Re);
var je = a10.iconDefinitionList;
var Ge = a10.iconDataMap;
var Ue = (o2) => Ge[o2];

// node_modules/@scalar/icons/dist/LibraryIcon.vue.js
var d7 = defineComponent({
  __name: "LibraryIcon",
  props: {
    src: {}
  },
  setup(o2) {
    const r14 = o2, e = computed(() => Ue(r14.src));
    return (u4, i16) => e.value ? (openBlock(), createBlock(resolveDynamicComponent(e.value), { key: 0 })) : createCommentVNode("", true);
  }
});

export {
  je,
  d7 as d
};
//# sourceMappingURL=chunk-L7BRX5IX.js.map

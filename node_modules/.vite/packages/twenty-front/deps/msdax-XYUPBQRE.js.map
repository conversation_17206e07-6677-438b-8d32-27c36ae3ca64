{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/msdax/msdax.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/msdax/msdax.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"{\", \"}\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".msdax\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.brackets\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // Query keywords\n    \"VAR\",\n    \"RETURN\",\n    \"NOT\",\n    \"EVALUATE\",\n    \"DATATABLE\",\n    \"ORDER\",\n    \"BY\",\n    \"START\",\n    \"AT\",\n    \"DEFINE\",\n    \"MEASURE\",\n    \"ASC\",\n    \"DESC\",\n    \"IN\",\n    // Datatable types\n    \"BOOLEAN\",\n    \"DOUBLE\",\n    \"INTEGER\",\n    \"DATETIME\",\n    \"CURRENCY\",\n    \"STRING\"\n  ],\n  functions: [\n    // Relational\n    \"CLOSINGBALANCEMONTH\",\n    \"CLOSINGBALANCEQUARTER\",\n    \"CLOSINGBALANCEYEAR\",\n    \"DATEADD\",\n    \"DATESBETWEEN\",\n    \"DATESINPERIOD\",\n    \"DATESMTD\",\n    \"DATESQTD\",\n    \"DATESYTD\",\n    \"ENDOFMONTH\",\n    \"ENDOFQUARTER\",\n    \"ENDOFYEAR\",\n    \"FIRSTDATE\",\n    \"FIRSTNONBLANK\",\n    \"LASTDATE\",\n    \"LASTNONBLANK\",\n    \"NEXTDAY\",\n    \"NEXTMONTH\",\n    \"NEXTQUARTER\",\n    \"NEXTYEAR\",\n    \"OPENINGBALANCEMONTH\",\n    \"OPENINGBALANCEQUARTER\",\n    \"OPENINGBALANCEYEAR\",\n    \"PARALLELPERIOD\",\n    \"PREVIOUSDAY\",\n    \"PREVIOUSMONTH\",\n    \"PREVIOUSQUARTER\",\n    \"PREVIOUSYEAR\",\n    \"SAMEPERIODLASTYEAR\",\n    \"STARTOFMONTH\",\n    \"STARTOFQUARTER\",\n    \"STARTOFYEAR\",\n    \"TOTALMTD\",\n    \"TOTALQTD\",\n    \"TOTALYTD\",\n    \"ADDCOLUMNS\",\n    \"ADDMISSINGITEMS\",\n    \"ALL\",\n    \"ALLEXCEPT\",\n    \"ALLNOBLANKROW\",\n    \"ALLSELECTED\",\n    \"CALCULATE\",\n    \"CALCULATETABLE\",\n    \"CALENDAR\",\n    \"CALENDARAUTO\",\n    \"CROSSFILTER\",\n    \"CROSSJOIN\",\n    \"CURRENTGROUP\",\n    \"DATATABLE\",\n    \"DETAILROWS\",\n    \"DISTINCT\",\n    \"EARLIER\",\n    \"EARLIEST\",\n    \"EXCEPT\",\n    \"FILTER\",\n    \"FILTERS\",\n    \"GENERATE\",\n    \"GENERATEALL\",\n    \"GROUPBY\",\n    \"IGNORE\",\n    \"INTERSECT\",\n    \"ISONORAFTER\",\n    \"KEEPFILTERS\",\n    \"LOOKUPVALUE\",\n    \"NATURALINNERJOIN\",\n    \"NATURALLEFTOUTERJOIN\",\n    \"RELATED\",\n    \"RELATEDTABLE\",\n    \"ROLLUP\",\n    \"ROLLUPADDISSUBTOTAL\",\n    \"ROLLUPGROUP\",\n    \"ROLLUPISSUBTOTAL\",\n    \"ROW\",\n    \"SAMPLE\",\n    \"SELECTCOLUMNS\",\n    \"SUBSTITUTEWITHINDEX\",\n    \"SUMMARIZE\",\n    \"SUMMARIZECOLUMNS\",\n    \"TOPN\",\n    \"TREATAS\",\n    \"UNION\",\n    \"USERELATIONSHIP\",\n    \"VALUES\",\n    \"SUM\",\n    \"SUMX\",\n    \"PATH\",\n    \"PATHCONTAINS\",\n    \"PATHITEM\",\n    \"PATHITEMREVERSE\",\n    \"PATHLENGTH\",\n    \"AVERAGE\",\n    \"AVERAGEA\",\n    \"AVERAGEX\",\n    \"COUNT\",\n    \"COUNTA\",\n    \"COUNTAX\",\n    \"COUNTBLANK\",\n    \"COUNTROWS\",\n    \"COUNTX\",\n    \"DISTINCTCOUNT\",\n    \"DIVIDE\",\n    \"GEOMEAN\",\n    \"GEOMEANX\",\n    \"MAX\",\n    \"MAXA\",\n    \"MAXX\",\n    \"MEDIAN\",\n    \"MEDIANX\",\n    \"MIN\",\n    \"MINA\",\n    \"MINX\",\n    \"PERCENTILE.EXC\",\n    \"PERCENTILE.INC\",\n    \"PERCENTILEX.EXC\",\n    \"PERCENTILEX.INC\",\n    \"PRODUCT\",\n    \"PRODUCTX\",\n    \"RANK.EQ\",\n    \"RANKX\",\n    \"STDEV.P\",\n    \"STDEV.S\",\n    \"STDEVX.P\",\n    \"STDEVX.S\",\n    \"VAR.P\",\n    \"VAR.S\",\n    \"VARX.P\",\n    \"VARX.S\",\n    \"XIRR\",\n    \"XNPV\",\n    // Scalar\n    \"DATE\",\n    \"DATEDIFF\",\n    \"DATEVALUE\",\n    \"DAY\",\n    \"EDATE\",\n    \"EOMONTH\",\n    \"HOUR\",\n    \"MINUTE\",\n    \"MONTH\",\n    \"NOW\",\n    \"SECOND\",\n    \"TIME\",\n    \"TIMEVALUE\",\n    \"TODAY\",\n    \"WEEKDAY\",\n    \"WEEKNUM\",\n    \"YEAR\",\n    \"YEARFRAC\",\n    \"CONTAINS\",\n    \"CONTAINSROW\",\n    \"CUSTOMDATA\",\n    \"ERROR\",\n    \"HASONEFILTER\",\n    \"HASONEVALUE\",\n    \"ISBLANK\",\n    \"ISCROSSFILTERED\",\n    \"ISEMPTY\",\n    \"ISERROR\",\n    \"ISEVEN\",\n    \"ISFILTERED\",\n    \"ISLOGICAL\",\n    \"ISNONTEXT\",\n    \"ISNUMBER\",\n    \"ISODD\",\n    \"ISSUBTOTAL\",\n    \"ISTEXT\",\n    \"USERNAME\",\n    \"USERPRINCIPALNAME\",\n    \"AND\",\n    \"FALSE\",\n    \"IF\",\n    \"IFERROR\",\n    \"NOT\",\n    \"OR\",\n    \"SWITCH\",\n    \"TRUE\",\n    \"ABS\",\n    \"ACOS\",\n    \"ACOSH\",\n    \"ACOT\",\n    \"ACOTH\",\n    \"ASIN\",\n    \"ASINH\",\n    \"ATAN\",\n    \"ATANH\",\n    \"BETA.DIST\",\n    \"BETA.INV\",\n    \"CEILING\",\n    \"CHISQ.DIST\",\n    \"CHISQ.DIST.RT\",\n    \"CHISQ.INV\",\n    \"CHISQ.INV.RT\",\n    \"COMBIN\",\n    \"COMBINA\",\n    \"CONFIDENCE.NORM\",\n    \"CONFIDENCE.T\",\n    \"COS\",\n    \"COSH\",\n    \"COT\",\n    \"COTH\",\n    \"CURRENCY\",\n    \"DEGREES\",\n    \"EVEN\",\n    \"EXP\",\n    \"EXPON.DIST\",\n    \"FACT\",\n    \"FLOOR\",\n    \"GCD\",\n    \"INT\",\n    \"ISO.CEILING\",\n    \"LCM\",\n    \"LN\",\n    \"LOG\",\n    \"LOG10\",\n    \"MOD\",\n    \"MROUND\",\n    \"ODD\",\n    \"PERMUT\",\n    \"PI\",\n    \"POISSON.DIST\",\n    \"POWER\",\n    \"QUOTIENT\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDBETWEEN\",\n    \"ROUND\",\n    \"ROUNDDOWN\",\n    \"ROUNDUP\",\n    \"SIGN\",\n    \"SIN\",\n    \"SINH\",\n    \"SQRT\",\n    \"SQRTPI\",\n    \"TAN\",\n    \"TANH\",\n    \"TRUNC\",\n    \"BLANK\",\n    \"CONCATENATE\",\n    \"CONCATENATEX\",\n    \"EXACT\",\n    \"FIND\",\n    \"FIXED\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"MID\",\n    \"REPLACE\",\n    \"REPT\",\n    \"RIGHT\",\n    \"SEARCH\",\n    \"SUBSTITUTE\",\n    \"TRIM\",\n    \"UNICHAR\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"VALUE\"\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[({})]/, \"@brackets\"],\n      [\n        /[a-z_][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@functions\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/\\/\\/+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N\"/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string\"],\n      [/\"/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/'/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^']+/, \"identifier\"],\n      [/''/, \"identifier\"],\n      [/'/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAkBA;AA3BJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,qBAAqB;AAAA,QACrD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MAC1D;AAAA,MACA,UAAU;AAAA;AAAA,QAER;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA;AAAA,QAET;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,sBAAsB;AAAA,UACjC,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,UAAU,WAAW;AAAA,UACtB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,oBAAoB,UAAU;AAAA,QACjC;AAAA,QACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,QAC7B,UAAU;AAAA,UACR,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,QACvD;AAAA,QACA,SAAS;AAAA,UACP,CAAC,UAAU,SAAS;AAAA,UACpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,UACjD,CAAC,KAAK,SAAS;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,qBAAqB,QAAQ;AAAA,UAC9B,CAAC,uBAAuB,QAAQ;AAAA,UAChC,CAAC,2CAA2C,QAAQ;AAAA,QACtD;AAAA,QACA,SAAS;AAAA,UACP,CAAC,MAAM,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,UAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,QAC5C;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,MAAM,QAAQ;AAAA,UACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,QACzC;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,MAAM,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,UAClE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC;AAAA,QAChE;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,UAAU,YAAY;AAAA,UACvB,CAAC,MAAM,YAAY;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,QACnD;AAAA,QACA,kBAAkB;AAAA,UAChB,CAAC,SAAS,YAAY;AAAA,UACtB,CAAC,MAAM,YAAY;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
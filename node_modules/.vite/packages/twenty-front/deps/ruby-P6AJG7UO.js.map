{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/ruby/ruby.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ruby/ruby.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"=begin\", \"=end\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\n      `^\\\\s*((begin|class|(private|protected)\\\\s+def|def|else|elsif|ensure|for|if|module|rescue|unless|until|when|while|case)|([^#]*\\\\sdo\\\\b)|([^#]*=\\\\s*(case|if|unless)))\\\\b([^#\\\\{;]|(\"|'|/).*\\\\4)*(#.*)?$`\n    ),\n    decreaseIndentPattern: new RegExp(\n      \"^\\\\s*([}\\\\]]([,)]?\\\\s*(#|$)|\\\\.[a-zA-Z_]\\\\w*\\\\b)|(end|rescue|ensure|else|elsif|when)\\\\b)\"\n    )\n  }\n};\nvar language = {\n  tokenPostfix: \".ruby\",\n  keywords: [\n    \"__LINE__\",\n    \"__ENCODING__\",\n    \"__FILE__\",\n    \"BEGIN\",\n    \"END\",\n    \"alias\",\n    \"and\",\n    \"begin\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"def\",\n    \"defined?\",\n    \"do\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"ensure\",\n    \"for\",\n    \"false\",\n    \"if\",\n    \"in\",\n    \"module\",\n    \"next\",\n    \"nil\",\n    \"not\",\n    \"or\",\n    \"redo\",\n    \"rescue\",\n    \"retry\",\n    \"return\",\n    \"self\",\n    \"super\",\n    \"then\",\n    \"true\",\n    \"undef\",\n    \"unless\",\n    \"until\",\n    \"when\",\n    \"while\",\n    \"yield\"\n  ],\n  keywordops: [\"::\", \"..\", \"...\", \"?\", \":\", \"=>\"],\n  builtins: [\n    \"require\",\n    \"public\",\n    \"private\",\n    \"include\",\n    \"extend\",\n    \"attr_reader\",\n    \"protected\",\n    \"private_class_method\",\n    \"protected_class_method\",\n    \"new\"\n  ],\n  // these are closed by 'end' (if, while and until are handled separately)\n  declarations: [\n    \"module\",\n    \"class\",\n    \"def\",\n    \"case\",\n    \"do\",\n    \"begin\",\n    \"for\",\n    \"if\",\n    \"while\",\n    \"until\",\n    \"unless\"\n  ],\n  linedecls: [\"def\", \"case\", \"do\", \"begin\", \"for\", \"if\", \"while\", \"until\", \"unless\"],\n  operators: [\n    \"^\",\n    \"&\",\n    \"|\",\n    \"<=>\",\n    \"==\",\n    \"===\",\n    \"!~\",\n    \"=~\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"<<\",\n    \">>\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"**\",\n    \"~\",\n    \"+@\",\n    \"-@\",\n    \"[]\",\n    \"[]=\",\n    \"`\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \"&=\",\n    \"&&=\",\n    \"||=\",\n    \"|=\"\n  ],\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%\\.]+/,\n  // escape sequences\n  escape: /(?:[abefnrstv\\\\\"'\\n\\r]|[0-7]{1,3}|x[0-9A-Fa-f]{1,2}|u[0-9A-Fa-f]{4})/,\n  escapes: /\\\\(?:C\\-(@escape|.)|c(@escape|.)|@escape)/,\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  delim: /[^a-zA-Z0-9\\s\\n\\r]/,\n  heredelim: /(?:\\w+|'[^']*'|\"[^\"]*\"|`[^`]*`)/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[AzZbBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    // Main entry.\n    // root.<decl> where decl is the current opening declaration (like 'class')\n    root: [\n      // identifiers and keywords\n      // most complexity here is due to matching 'end' correctly with declarations.\n      // We distinguish a declaration that comes first on a line, versus declarations further on a line (which are most likey modifiers)\n      [\n        /^(\\s*)([a-z_]\\w*[!?=]?)/,\n        [\n          \"white\",\n          {\n            cases: {\n              \"for|until|while\": {\n                token: \"keyword.$2\",\n                next: \"@dodecl.$2\"\n              },\n              \"@declarations\": {\n                token: \"keyword.$2\",\n                next: \"@root.$2\"\n              },\n              end: { token: \"keyword.$S2\", next: \"@pop\" },\n              \"@keywords\": \"keyword\",\n              \"@builtins\": \"predefined\",\n              \"@default\": \"identifier\"\n            }\n          }\n        ]\n      ],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            \"if|unless|while|until\": {\n              token: \"keyword.$0x\",\n              next: \"@modifier.$0x\"\n            },\n            for: { token: \"keyword.$2\", next: \"@dodecl.$2\" },\n            \"@linedecls\": { token: \"keyword.$0\", next: \"@root.$0\" },\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w]*[!?=]?/, \"constructor.identifier\"],\n      // constant\n      [/\\$[\\w]*/, \"global.constant\"],\n      // global\n      [/@[\\w]*/, \"namespace.instance.identifier\"],\n      // instance\n      [/@@@[\\w]*/, \"namespace.class.identifier\"],\n      // class\n      // here document\n      [/<<[-~](@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/[ \\t\\r\\n]+<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/^<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      // whitespace\n      { include: \"@whitespace\" },\n      // strings\n      [/\"/, { token: \"string.d.delim\", next: '@dstring.d.\"' }],\n      [/'/, { token: \"string.sq.delim\", next: \"@sstring.sq\" }],\n      // % literals. For efficiency, rematch in the 'pstring' state\n      [/%([rsqxwW]|Q?)/, { token: \"@rematch\", next: \"pstring\" }],\n      // commands and symbols\n      [/`/, { token: \"string.x.delim\", next: \"@dstring.x.`\" }],\n      [/:(\\w|[$@])\\w*[!?=]?/, \"string.s\"],\n      [/:\"/, { token: \"string.s.delim\", next: '@dstring.s.\"' }],\n      [/:'/, { token: \"string.s.delim\", next: \"@sstring.s\" }],\n      // regular expressions. Lookahead for a (not escaped) closing forwardslash on the same line\n      [/\\/(?=(\\\\\\/|[^\\/\\n])+\\/)/, { token: \"regexp.delim\", next: \"@regexp\" }],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@keywordops\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[;,]/, \"delimiter\"],\n      // numbers\n      [/0[xX][0-9a-fA-F](_?[0-9a-fA-F])*/, \"number.hex\"],\n      [/0[_oO][0-7](_?[0-7])*/, \"number.octal\"],\n      [/0[bB][01](_?[01])*/, \"number.binary\"],\n      [/0[dD]@decpart/, \"number\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)/,\n        {\n          cases: {\n            $1: \"number.float\",\n            \"@default\": \"number\"\n          }\n        }\n      ]\n    ],\n    // used to not treat a 'do' as a block opener if it occurs on the same\n    // line as a 'do' statement: 'while|until|for'\n    // dodecl.<decl> where decl is the declarations started, like 'while'\n    dodecl: [\n      [/^/, { token: \"\", switchTo: \"@root.$S2\" }],\n      // get out of do-skipping mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            do: { token: \"keyword\", switchTo: \"@root.$S2\" },\n            // do on same line: not an open bracket here\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration on same line: rematch\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // used to prevent potential modifiers ('if|until|while|unless') to match\n    // with 'end' keywords.\n    // modifier.<decl>x where decl is the declaration starter, like 'if'\n    modifier: [\n      [/^/, \"\", \"@pop\"],\n      // it was a modifier: get out of modifier mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            \"then|else|elsif|do\": {\n              token: \"keyword\",\n              switchTo: \"@root.$S2\"\n            },\n            // real declaration and not a modifier\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration => not a modifier\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // single quote strings (also used for symbols)\n    // sstring.<kind>  where kind is 'sq' (single quote) or 's' (symbol)\n    sstring: [\n      [/[^\\\\']+/, \"string.$S2\"],\n      [/\\\\\\\\|\\\\'|\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.invalid\"],\n      [/'/, { token: \"string.$S2.delim\", next: \"@pop\" }]\n    ],\n    // double quoted \"string\".\n    // dstring.<kind>.<delim> where kind is 'd' (double quoted), 'x' (command), or 's' (symbol)\n    // and delim is the ending delimiter (\" or `)\n    dstring: [\n      [/[^\\\\`\"#]+/, \"string.$S2\"],\n      [/#/, \"string.$S2.escape\", \"@interpolated\"],\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/@escapes/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape.invalid\"],\n      [\n        /[`\"]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // literal documents\n    // heredoc.<close> where close is the closing delimiter\n    heredoc: [\n      [\n        /^(\\s*)(@heredelim)$/,\n        {\n          cases: {\n            \"$2==$S2\": [\"string.heredoc\", { token: \"string.heredoc.delimiter\", next: \"@pop\" }],\n            \"@default\": [\"string.heredoc\", \"string.heredoc\"]\n          }\n        }\n      ],\n      [/.*/, \"string.heredoc\"]\n    ],\n    // interpolated sequence\n    interpolated: [\n      [/\\$\\w*/, \"global.constant\", \"@pop\"],\n      [/@\\w*/, \"namespace.class.identifier\", \"@pop\"],\n      [/@@@\\w*/, \"namespace.instance.identifier\", \"@pop\"],\n      [\n        /[{]/,\n        {\n          token: \"string.escape.curly\",\n          switchTo: \"@interpolated_compound\"\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n      // just a # is interpreted as a #\n    ],\n    // any code\n    interpolated_compound: [\n      [/[}]/, { token: \"string.escape.curly\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ],\n    // %r quoted regexp\n    // pregexp.<open>.<close> where open/close are the open/close delimiter\n    pregexp: [\n      { include: \"@whitespace\" },\n      // turns out that you can quote using regex control characters, aargh!\n      // for example; %r|kgjgaj| is ok (even though | is used for alternation)\n      // so, we need to match those first\n      [\n        /[^\\(\\{\\[\\\\]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"regexp.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"~[)}\\\\]]\": \"@brackets.regexp.escape.control\",\n            \"~@regexpctl\": \"regexp.escape.control\",\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexcontrol\" }\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      { include: \"@regexcontrol\" },\n      [/[^\\\\\\/]/, \"regexp\"],\n      [\"/[ixmp]*\", { token: \"regexp.delim\" }, \"@pop\"]\n    ],\n    regexcontrol: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\n          \"@brackets.regexp.escape.control\",\n          \"regexp.escape.control\",\n          \"@brackets.regexp.escape.control\"\n        ]\n      ],\n      [\n        /(\\[)(\\^?)/,\n        [\"@brackets.regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?[:=!])/, [\"@brackets.regexp.escape.control\", \"regexp.escape.control\"]],\n      [/\\(\\?#/, { token: \"regexp.escape.control\", next: \"@regexpcomment\" }],\n      [/[()]/, \"@brackets.regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/#/, \"regexp.escape\", \"@interpolated\"]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [/\\]/, \"@brackets.regexp.escape.control\", \"@pop\"]\n    ],\n    regexpcomment: [\n      [/[^)]+/, \"comment\"],\n      [/\\)/, { token: \"regexp.escape.control\", next: \"@pop\" }]\n    ],\n    // % quoted strings\n    // A bit repetitive since we need to often special case the kind of ending delimiter\n    pstring: [\n      [/%([qws])\\(/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.(.)\" }],\n      [/%([qws])\\[/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.[.]\" }],\n      [/%([qws])\\{/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.{.}\" }],\n      [/%([qws])</, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.<.>\" }],\n      [/%([qws])(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.$2.$2\" }],\n      [/%r\\(/, { token: \"regexp.delim\", switchTo: \"@pregexp.(.)\" }],\n      [/%r\\[/, { token: \"regexp.delim\", switchTo: \"@pregexp.[.]\" }],\n      [/%r\\{/, { token: \"regexp.delim\", switchTo: \"@pregexp.{.}\" }],\n      [/%r</, { token: \"regexp.delim\", switchTo: \"@pregexp.<.>\" }],\n      [/%r(@delim)/, { token: \"regexp.delim\", switchTo: \"@pregexp.$1.$1\" }],\n      [/%(x|W|Q?)\\(/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.(.)\" }],\n      [/%(x|W|Q?)\\[/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.[.]\" }],\n      [/%(x|W|Q?)\\{/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.{.}\" }],\n      [/%(x|W|Q?)</, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.<.>\" }],\n      [/%(x|W|Q?)(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.$2.$2\" }],\n      [/%([rqwsxW]|Q?)./, { token: \"invalid\", next: \"@pop\" }],\n      // recover\n      [/./, { token: \"invalid\", next: \"@pop\" }]\n      // recover\n    ],\n    // non-expanded quoted string.\n    // qstring.<kind>.<open>.<close>\n    //  kind = q|w|s  (single quote, array, symbol)\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S4\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // expanded quoted string.\n    // qqstring.<kind>.<open>.<close>\n    //  kind = Q|W|x  (double quote, array, command)\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [[/#/, \"string.$S2.escape\", \"@interpolated\"], { include: \"@qstring\" }],\n    // whitespace & comments\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*=begin\\b/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^=]+/, \"comment\"],\n      [/^\\s*=begin\\b/, \"comment.invalid\"],\n      // nested comment\n      [/^\\s*=end\\b.*/, \"comment\", \"@pop\"],\n      [/[=]/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAiCA;AA1CJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,UAAU,MAAM;AAAA,MACjC;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,uBAAuB,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,QACA,uBAAuB,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY,CAAC,MAAM,MAAM,OAAO,KAAK,KAAK,IAAI;AAAA,MAC9C,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,CAAC,OAAO,QAAQ,MAAM,SAAS,OAAO,MAAM,SAAS,SAAS,QAAQ;AAAA,MACjF,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACrD;AAAA;AAAA,MAEA,SAAS;AAAA;AAAA,MAET,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAEX,WAAW;AAAA;AAAA;AAAA,QAGT,MAAM;AAAA;AAAA;AAAA;AAAA,UAIJ;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,mBAAmB;AAAA,oBACjB,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR;AAAA,kBACA,iBAAiB;AAAA,oBACf,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR;AAAA,kBACA,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,kBAC1C,aAAa;AAAA,kBACb,aAAa;AAAA,kBACb,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,yBAAyB;AAAA,kBACvB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,KAAK,EAAE,OAAO,cAAc,MAAM,aAAa;AAAA,gBAC/C,cAAc,EAAE,OAAO,cAAc,MAAM,WAAW;AAAA,gBACtD,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,gBAC1C,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,oBAAoB,wBAAwB;AAAA;AAAA,UAE7C,CAAC,WAAW,iBAAiB;AAAA;AAAA,UAE7B,CAAC,UAAU,+BAA+B;AAAA;AAAA,UAE1C,CAAC,YAAY,4BAA4B;AAAA;AAAA;AAAA,UAGzC,CAAC,wBAAwB,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA,UACnF,CAAC,8BAA8B,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA,UACzF,CAAC,qBAAqB,EAAE,OAAO,4BAA4B,MAAM,cAAc,CAAC;AAAA;AAAA,UAEhF,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,KAAK,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,UACvD,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,cAAc,CAAC;AAAA;AAAA,UAEvD,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC;AAAA;AAAA,UAEzD,CAAC,KAAK,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,UACvD,CAAC,uBAAuB,UAAU;AAAA,UAClC,CAAC,MAAM,EAAE,OAAO,kBAAkB,MAAM,eAAe,CAAC;AAAA,UACxD,CAAC,MAAM,EAAE,OAAO,kBAAkB,MAAM,aAAa,CAAC;AAAA;AAAA,UAEtD,CAAC,2BAA2B,EAAE,OAAO,gBAAgB,MAAM,UAAU,CAAC;AAAA;AAAA,UAEtE,CAAC,cAAc,WAAW;AAAA,UAC1B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,eAAe;AAAA,gBACf,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,WAAW;AAAA;AAAA,UAEpB,CAAC,oCAAoC,YAAY;AAAA,UACjD,CAAC,yBAAyB,cAAc;AAAA,UACxC,CAAC,sBAAsB,eAAe;AAAA,UACtC,CAAC,iBAAiB,QAAQ;AAAA,UAC1B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,IAAI;AAAA,gBACJ,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAIA,QAAQ;AAAA,UACN,CAAC,KAAK,EAAE,OAAO,IAAI,UAAU,YAAY,CAAC;AAAA;AAAA,UAE1C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA;AAAA,gBAE1C,IAAI,EAAE,OAAO,WAAW,UAAU,YAAY;AAAA;AAAA,gBAE9C,cAAc;AAAA,kBACZ,OAAO;AAAA,kBACP,UAAU;AAAA,gBACZ;AAAA;AAAA,gBAEA,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA;AAAA;AAAA;AAAA,QAIA,UAAU;AAAA,UACR,CAAC,KAAK,IAAI,MAAM;AAAA;AAAA,UAEhB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,KAAK,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA;AAAA,gBAE1C,sBAAsB;AAAA,kBACpB,OAAO;AAAA,kBACP,UAAU;AAAA,gBACZ;AAAA;AAAA,gBAEA,cAAc;AAAA,kBACZ,OAAO;AAAA,kBACP,UAAU;AAAA,gBACZ;AAAA;AAAA,gBAEA,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,CAAC,WAAW,YAAY;AAAA,UACxB,CAAC,gBAAgB,mBAAmB;AAAA,UACpC,CAAC,OAAO,oBAAoB;AAAA,UAC5B,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,QACnD;AAAA;AAAA;AAAA;AAAA,QAIA,SAAS;AAAA,UACP,CAAC,aAAa,YAAY;AAAA,UAC1B,CAAC,KAAK,qBAAqB,eAAe;AAAA,UAC1C,CAAC,OAAO,mBAAmB;AAAA,UAC3B,CAAC,YAAY,mBAAmB;AAAA,UAChC,CAAC,OAAO,2BAA2B;AAAA,UACnC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA,QAGA,SAAS;AAAA,UACP;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,CAAC,kBAAkB,EAAE,OAAO,4BAA4B,MAAM,OAAO,CAAC;AAAA,gBACjF,YAAY,CAAC,kBAAkB,gBAAgB;AAAA,cACjD;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,gBAAgB;AAAA,QACzB;AAAA;AAAA,QAEA,cAAc;AAAA,UACZ,CAAC,SAAS,mBAAmB,MAAM;AAAA,UACnC,CAAC,QAAQ,8BAA8B,MAAM;AAAA,UAC7C,CAAC,UAAU,iCAAiC,MAAM;AAAA,UAClD;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,IAAI,IAAI,MAAM;AAAA;AAAA,QAEjB;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB,CAAC,OAAO,EAAE,OAAO,uBAAuB,MAAM,OAAO,CAAC;AAAA,UACtD,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,EAAE,SAAS,cAAc;AAAA;AAAA;AAAA;AAAA,UAIzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,gBAAgB,MAAM,OAAO;AAAA,gBACjD,WAAW,EAAE,OAAO,gBAAgB,MAAM,QAAQ;AAAA;AAAA,gBAElD,YAAY;AAAA,gBACZ,eAAe;AAAA,gBACf,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,gBAAgB;AAAA,QAC7B;AAAA;AAAA,QAEA,QAAQ;AAAA,UACN,EAAE,SAAS,gBAAgB;AAAA,UAC3B,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,EAAE,OAAO,eAAe,GAAG,MAAM;AAAA,QAChD;AAAA,QACA,cAAc;AAAA,UACZ;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,mCAAmC,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,UAC7F;AAAA,UACA,CAAC,iBAAiB,CAAC,mCAAmC,uBAAuB,CAAC;AAAA,UAC9E,CAAC,SAAS,EAAE,OAAO,yBAAyB,MAAM,iBAAiB,CAAC;AAAA,UACpE,CAAC,QAAQ,iCAAiC;AAAA,UAC1C,CAAC,cAAc,uBAAuB;AAAA,UACtC,CAAC,OAAO,eAAe;AAAA,UACvB,CAAC,cAAc,eAAe;AAAA,UAC9B,CAAC,QAAQ,gBAAgB;AAAA,UACzB,CAAC,KAAK,iBAAiB,eAAe;AAAA,QACxC;AAAA,QACA,YAAY;AAAA,UACV,CAAC,KAAK,uBAAuB;AAAA,UAC7B,CAAC,MAAM,gBAAgB;AAAA,UACvB,CAAC,OAAO,eAAe;AAAA,UACvB,CAAC,cAAc,eAAe;AAAA,UAC9B,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,MAAM,mCAAmC,MAAM;AAAA,QAClD;AAAA,QACA,eAAe;AAAA,UACb,CAAC,SAAS,SAAS;AAAA,UACnB,CAAC,MAAM,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,QACzD;AAAA;AAAA;AAAA,QAGA,SAAS;AAAA,UACP,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,UACxE,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,UACxE,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,UACxE,CAAC,aAAa,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,UACvE,CAAC,oBAAoB,EAAE,OAAO,mBAAmB,UAAU,oBAAoB,CAAC;AAAA,UAChF,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,UAC5D,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,UAC5D,CAAC,QAAQ,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,UAC5D,CAAC,OAAO,EAAE,OAAO,gBAAgB,UAAU,eAAe,CAAC;AAAA,UAC3D,CAAC,cAAc,EAAE,OAAO,gBAAgB,UAAU,iBAAiB,CAAC;AAAA,UACpE,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,UAC1E,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,UAC1E,CAAC,eAAe,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,UAC1E,CAAC,cAAc,EAAE,OAAO,mBAAmB,UAAU,mBAAmB,CAAC;AAAA,UACzE,CAAC,qBAAqB,EAAE,OAAO,mBAAmB,UAAU,qBAAqB,CAAC;AAAA,UAClF,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA;AAAA,UAEtD,CAAC,KAAK,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA;AAAA,QAE1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,SAAS;AAAA,UACP,CAAC,OAAO,mBAAmB;AAAA,UAC3B,CAAC,OAAO,mBAAmB;AAAA,UAC3B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,WAAW,EAAE,OAAO,oBAAoB,MAAM,QAAQ;AAAA;AAAA,gBAEtD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,UAAU,CAAC,CAAC,KAAK,qBAAqB,eAAe,GAAG,EAAE,SAAS,WAAW,CAAC;AAAA;AAAA,QAE/E,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,gBAAgB,WAAW,UAAU;AAAA,UACtC,CAAC,QAAQ,SAAS;AAAA,QACpB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,SAAS,SAAS;AAAA,UACnB,CAAC,gBAAgB,iBAAiB;AAAA;AAAA,UAElC,CAAC,gBAAgB,WAAW,MAAM;AAAA,UAClC,CAAC,OAAO,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
import {
  Button$1,
  ButtonGroup,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  Dialog,
  ExecuteButton,
  GraphiQLProvider,
  HeaderEditor,
  KeyboardShortcutIcon,
  MergeIcon,
  PlusIcon,
  PrettifyIcon,
  QueryEditor,
  ReloadIcon,
  ResponseEditor,
  SettingsIcon,
  Spinner,
  Tab,
  Tabs,
  ToolbarButton,
  Tooltip,
  UnStyledButton,
  VariableEditor,
  useCopyQuery,
  useDragResize,
  useEditorContext,
  useExecutionContext,
  useMergeQuery,
  usePluginContext,
  usePrettifyEditors,
  useSchemaContext,
  useStorageContext,
  useTheme
} from "./chunk-35WFSX3X.js";
import "./chunk-FBZEYJ3E.js";
import "./chunk-RCUOUUBT.js";
import "./chunk-4GMML6L6.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-HU3QXZQW.js";
import "./chunk-PGOGSL6Q.js";
import "./chunk-C6RYHFZP.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-VZQMTKPA.js";
import "./chunk-I7RZVVY3.js";
import "./chunk-I4HVGP7M.js";
import "./chunk-VV4ISGQI.js";
import {
  require_react
} from "./chunk-LABDTKBP.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/graphiql/esm/components/GraphiQL.js
var import_react = __toESM(require_react());
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var __read = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
};
var majorVersion = parseInt(import_react.default.version.slice(0, 2), 10);
if (majorVersion < 16) {
  throw new Error([
    "GraphiQL 0.18.0 and after is not compatible with React 15 or below.",
    "If you are using a CDN source (jsdelivr, unpkg, etc), follow this example:",
    "https://github.com/graphql/graphiql/blob/master/examples/graphiql-cdn/index.html#L49"
  ].join("\n"));
}
function GraphiQL(_a) {
  var _b;
  var dangerouslyAssumeSchemaIsValid = _a.dangerouslyAssumeSchemaIsValid, defaultQuery = _a.defaultQuery, defaultTabs = _a.defaultTabs, externalFragments = _a.externalFragments, fetcher = _a.fetcher, getDefaultFieldNames = _a.getDefaultFieldNames, headers = _a.headers, inputValueDeprecation = _a.inputValueDeprecation, introspectionQueryName = _a.introspectionQueryName, maxHistoryLength = _a.maxHistoryLength, onEditOperationName = _a.onEditOperationName, onSchemaChange = _a.onSchemaChange, onTabChange = _a.onTabChange, onTogglePluginVisibility = _a.onTogglePluginVisibility, operationName = _a.operationName, plugins = _a.plugins, query = _a.query, response = _a.response, schema = _a.schema, schemaDescription = _a.schemaDescription, shouldPersistHeaders = _a.shouldPersistHeaders, storage = _a.storage, validationRules = _a.validationRules, variables = _a.variables, visiblePlugin = _a.visiblePlugin, defaultHeaders = _a.defaultHeaders, props = __rest(_a, ["dangerouslyAssumeSchemaIsValid", "defaultQuery", "defaultTabs", "externalFragments", "fetcher", "getDefaultFieldNames", "headers", "inputValueDeprecation", "introspectionQueryName", "maxHistoryLength", "onEditOperationName", "onSchemaChange", "onTabChange", "onTogglePluginVisibility", "operationName", "plugins", "query", "response", "schema", "schemaDescription", "shouldPersistHeaders", "storage", "validationRules", "variables", "visiblePlugin", "defaultHeaders"]);
  if (typeof fetcher !== "function") {
    throw new TypeError("The `GraphiQL` component requires a `fetcher` function to be passed as prop.");
  }
  return import_react.default.createElement(
    GraphiQLProvider,
    { getDefaultFieldNames, dangerouslyAssumeSchemaIsValid, defaultQuery, defaultHeaders, defaultTabs, externalFragments, fetcher, headers, inputValueDeprecation, introspectionQueryName, maxHistoryLength, onEditOperationName, onSchemaChange, onTabChange, onTogglePluginVisibility, plugins, visiblePlugin, operationName, query, response, schema, schemaDescription, shouldPersistHeaders, storage, validationRules, variables },
    import_react.default.createElement(GraphiQLInterface, __assign({ showPersistHeadersSettings: shouldPersistHeaders !== false, disableTabs: (_b = props.disableTabs) !== null && _b !== void 0 ? _b : false, forcedTheme: props.forcedTheme }, props))
  );
}
GraphiQL.Logo = GraphiQLLogo;
GraphiQL.Toolbar = GraphiQLToolbar;
GraphiQL.Footer = GraphiQLFooter;
var THEMES = ["light", "dark", "system"];
function GraphiQLInterface(props) {
  var _a, _b, _c, _d;
  var isHeadersEditorEnabled = (_a = props.isHeadersEditorEnabled) !== null && _a !== void 0 ? _a : true;
  var editorContext = useEditorContext({ nonNull: true });
  var executionContext = useExecutionContext({ nonNull: true });
  var schemaContext = useSchemaContext({ nonNull: true });
  var storageContext = useStorageContext();
  var pluginContext = usePluginContext();
  var forcedTheme = (0, import_react.useMemo)(function() {
    return props.forcedTheme && THEMES.includes(props.forcedTheme) ? props.forcedTheme : void 0;
  }, [props.forcedTheme]);
  var copy = useCopyQuery({ onCopyQuery: props.onCopyQuery });
  var merge = useMergeQuery();
  var prettify = usePrettifyEditors();
  var _e = useTheme(), theme = _e.theme, setTheme = _e.setTheme;
  (0, import_react.useEffect)(function() {
    if (forcedTheme === "system") {
      setTheme(null);
    } else if (forcedTheme === "light" || forcedTheme === "dark") {
      setTheme(forcedTheme);
    }
  }, [forcedTheme, setTheme]);
  var PluginContent = (_b = pluginContext === null || pluginContext === void 0 ? void 0 : pluginContext.visiblePlugin) === null || _b === void 0 ? void 0 : _b.content;
  var pluginResize = useDragResize({
    defaultSizeRelation: 1 / 3,
    direction: "horizontal",
    initiallyHidden: (pluginContext === null || pluginContext === void 0 ? void 0 : pluginContext.visiblePlugin) ? void 0 : "first",
    onHiddenElementChange: function(resizableElement) {
      if (resizableElement === "first") {
        pluginContext === null || pluginContext === void 0 ? void 0 : pluginContext.setVisiblePlugin(null);
      }
    },
    sizeThresholdSecond: 200,
    storageKey: "docExplorerFlex"
  });
  var editorResize = useDragResize({
    direction: "horizontal",
    storageKey: "editorFlex"
  });
  var editorToolsResize = useDragResize({
    defaultSizeRelation: 3,
    direction: "vertical",
    initiallyHidden: function() {
      if (props.defaultEditorToolsVisibility === "variables" || props.defaultEditorToolsVisibility === "headers") {
        return;
      }
      if (typeof props.defaultEditorToolsVisibility === "boolean") {
        return props.defaultEditorToolsVisibility ? void 0 : "second";
      }
      return editorContext.initialVariables || editorContext.initialHeaders ? void 0 : "second";
    }(),
    sizeThresholdSecond: 60,
    storageKey: "secondaryEditorFlex"
  });
  var _f = __read((0, import_react.useState)(function() {
    if (props.defaultEditorToolsVisibility === "variables" || props.defaultEditorToolsVisibility === "headers") {
      return props.defaultEditorToolsVisibility;
    }
    return !editorContext.initialVariables && editorContext.initialHeaders && isHeadersEditorEnabled ? "headers" : "variables";
  }), 2), activeSecondaryEditor = _f[0], setActiveSecondaryEditor = _f[1];
  var _g = __read((0, import_react.useState)(null), 2), showDialog = _g[0], setShowDialog = _g[1];
  var _h = __read((0, import_react.useState)(null), 2), clearStorageStatus = _h[0], setClearStorageStatus = _h[1];
  var children = import_react.default.Children.toArray(props.children);
  var logo = children.find(function(child) {
    return isChildComponentType(child, GraphiQL.Logo);
  }) || import_react.default.createElement(GraphiQL.Logo, null);
  var toolbar = children.find(function(child) {
    return isChildComponentType(child, GraphiQL.Toolbar);
  }) || import_react.default.createElement(
    import_react.default.Fragment,
    null,
    import_react.default.createElement(
      ToolbarButton,
      { onClick: prettify, label: "Prettify query (Shift-Ctrl-P)" },
      import_react.default.createElement(PrettifyIcon, { className: "graphiql-toolbar-icon", "aria-hidden": "true" })
    ),
    import_react.default.createElement(
      ToolbarButton,
      { onClick: merge, label: "Merge fragments into query (Shift-Ctrl-M)" },
      import_react.default.createElement(MergeIcon, { className: "graphiql-toolbar-icon", "aria-hidden": "true" })
    ),
    import_react.default.createElement(
      ToolbarButton,
      { onClick: copy, label: "Copy query (Shift-Ctrl-C)" },
      import_react.default.createElement(CopyIcon, { className: "graphiql-toolbar-icon", "aria-hidden": "true" })
    ),
    (_c = props.toolbar) === null || _c === void 0 ? void 0 : _c.additionalContent,
    ((_d = props.toolbar) === null || _d === void 0 ? void 0 : _d.additionalComponent) && import_react.default.createElement(props.toolbar.additionalComponent, null)
  );
  var footer = children.find(function(child) {
    return isChildComponentType(child, GraphiQL.Footer);
  });
  var onClickReference = (0, import_react.useCallback)(function() {
    if (pluginResize.hiddenElement === "first") {
      pluginResize.setHiddenElement(null);
    }
  }, [pluginResize]);
  var handleClearData = (0, import_react.useCallback)(function() {
    try {
      storageContext === null || storageContext === void 0 ? void 0 : storageContext.clear();
      setClearStorageStatus("success");
    } catch (_a2) {
      setClearStorageStatus("error");
    }
  }, [storageContext]);
  var handlePersistHeaders = (0, import_react.useCallback)(function(event) {
    editorContext.setShouldPersistHeaders(event.currentTarget.dataset.value === "true");
  }, [editorContext]);
  var handleChangeTheme = (0, import_react.useCallback)(function(event) {
    var selectedTheme = event.currentTarget.dataset.theme;
    setTheme(selectedTheme || null);
  }, [setTheme]);
  var handleAddTab = editorContext.addTab;
  var handleRefetchSchema = schemaContext.introspect;
  var handleReorder = editorContext.moveTab;
  var handleShowDialog = (0, import_react.useCallback)(function(event) {
    setShowDialog(event.currentTarget.dataset.value);
  }, []);
  var handlePluginClick = (0, import_react.useCallback)(function(e) {
    var context = pluginContext;
    var pluginIndex = Number(e.currentTarget.dataset.index);
    var plugin = context.plugins.find(function(_, index) {
      return pluginIndex === index;
    });
    var isVisible = plugin === context.visiblePlugin;
    if (isVisible) {
      context.setVisiblePlugin(null);
      pluginResize.setHiddenElement("first");
    } else {
      context.setVisiblePlugin(plugin);
      pluginResize.setHiddenElement(null);
    }
  }, [pluginContext, pluginResize]);
  var handleToolsTabClick = (0, import_react.useCallback)(function(event) {
    if (editorToolsResize.hiddenElement === "second") {
      editorToolsResize.setHiddenElement(null);
    }
    setActiveSecondaryEditor(event.currentTarget.dataset.name);
  }, [editorToolsResize]);
  var toggleEditorTools = (0, import_react.useCallback)(function() {
    editorToolsResize.setHiddenElement(editorToolsResize.hiddenElement === "second" ? null : "second");
  }, [editorToolsResize]);
  var handleOpenShortKeysDialog = (0, import_react.useCallback)(function(isOpen) {
    if (!isOpen) {
      setShowDialog(null);
    }
  }, []);
  var handleOpenSettingsDialog = (0, import_react.useCallback)(function(isOpen) {
    if (!isOpen) {
      setShowDialog(null);
      setClearStorageStatus(null);
    }
  }, []);
  var addTab = import_react.default.createElement(
    Tooltip,
    { label: "Add tab" },
    import_react.default.createElement(
      UnStyledButton,
      { type: "button", className: "graphiql-tab-add", onClick: handleAddTab, "aria-label": "Add tab" },
      import_react.default.createElement(PlusIcon, { "aria-hidden": "true" })
    )
  );
  var className = props.className ? " ".concat(props.className) : "";
  return import_react.default.createElement(
    Tooltip.Provider,
    null,
    import_react.default.createElement(
      "div",
      { "data-testid": "graphiql-container", className: "graphiql-container".concat(className) },
      import_react.default.createElement(
        "div",
        { className: "graphiql-sidebar" },
        import_react.default.createElement("div", { className: "graphiql-sidebar-section" }, pluginContext === null || pluginContext === void 0 ? void 0 : pluginContext.plugins.map(function(plugin, index) {
          var isVisible = plugin === pluginContext.visiblePlugin;
          var label = "".concat(isVisible ? "Hide" : "Show", " ").concat(plugin.title);
          var Icon = plugin.icon;
          return import_react.default.createElement(
            Tooltip,
            { key: plugin.title, label },
            import_react.default.createElement(
              UnStyledButton,
              { type: "button", className: isVisible ? "active" : "", onClick: handlePluginClick, "data-index": index, "aria-label": label },
              import_react.default.createElement(Icon, { "aria-hidden": "true" })
            )
          );
        })),
        import_react.default.createElement(
          "div",
          { className: "graphiql-sidebar-section" },
          import_react.default.createElement(
            Tooltip,
            { label: "Re-fetch GraphQL schema" },
            import_react.default.createElement(
              UnStyledButton,
              { type: "button", disabled: schemaContext.isFetching, onClick: handleRefetchSchema, "aria-label": "Re-fetch GraphQL schema" },
              import_react.default.createElement(ReloadIcon, { className: schemaContext.isFetching ? "graphiql-spin" : "", "aria-hidden": "true" })
            )
          ),
          import_react.default.createElement(
            Tooltip,
            { label: "Open short keys dialog" },
            import_react.default.createElement(
              UnStyledButton,
              { type: "button", "data-value": "short-keys", onClick: handleShowDialog, "aria-label": "Open short keys dialog" },
              import_react.default.createElement(KeyboardShortcutIcon, { "aria-hidden": "true" })
            )
          ),
          import_react.default.createElement(
            Tooltip,
            { label: "Open settings dialog" },
            import_react.default.createElement(
              UnStyledButton,
              { type: "button", "data-value": "settings", onClick: handleShowDialog, "aria-label": "Open settings dialog" },
              import_react.default.createElement(SettingsIcon, { "aria-hidden": "true" })
            )
          )
        )
      ),
      import_react.default.createElement(
        "div",
        { className: "graphiql-main" },
        import_react.default.createElement(
          "div",
          { ref: pluginResize.firstRef, style: {
            minWidth: "200px"
          } },
          import_react.default.createElement("div", { className: "graphiql-plugin" }, PluginContent ? import_react.default.createElement(PluginContent, null) : null)
        ),
        (pluginContext === null || pluginContext === void 0 ? void 0 : pluginContext.visiblePlugin) && import_react.default.createElement("div", { className: "graphiql-horizontal-drag-bar", ref: pluginResize.dragBarRef }),
        import_react.default.createElement(
          "div",
          { ref: pluginResize.secondRef, className: "graphiql-sessions" },
          import_react.default.createElement(
            "div",
            { className: "graphiql-session-header" },
            !props.disableTabs && import_react.default.createElement(Tabs, { values: editorContext.tabs, onReorder: handleReorder, "aria-label": "Select active operation" }, editorContext.tabs.length > 1 && import_react.default.createElement(
              import_react.default.Fragment,
              null,
              editorContext.tabs.map(function(tab, index) {
                return import_react.default.createElement(
                  Tab,
                  { key: tab.id, value: tab, isActive: index === editorContext.activeTabIndex },
                  import_react.default.createElement(Tab.Button, { "aria-controls": "graphiql-session", id: "graphiql-session-tab-".concat(index), onClick: function() {
                    executionContext.stop();
                    editorContext.changeTab(index);
                  } }, tab.title),
                  import_react.default.createElement(Tab.Close, { onClick: function() {
                    if (editorContext.activeTabIndex === index) {
                      executionContext.stop();
                    }
                    editorContext.closeTab(index);
                  } })
                );
              }),
              addTab
            )),
            import_react.default.createElement(
              "div",
              { className: "graphiql-session-header-right" },
              editorContext.tabs.length === 1 && addTab,
              logo
            )
          ),
          import_react.default.createElement(
            "div",
            { role: "tabpanel", id: "graphiql-session", className: "graphiql-session", "aria-labelledby": "graphiql-session-tab-".concat(editorContext.activeTabIndex) },
            import_react.default.createElement(
              "div",
              { ref: editorResize.firstRef },
              import_react.default.createElement(
                "div",
                { className: "graphiql-editors".concat(editorContext.tabs.length === 1 ? " full-height" : "") },
                import_react.default.createElement(
                  "div",
                  { ref: editorToolsResize.firstRef },
                  import_react.default.createElement(
                    "section",
                    { className: "graphiql-query-editor", "aria-label": "Query Editor" },
                    import_react.default.createElement(QueryEditor, { editorTheme: props.editorTheme, keyMap: props.keyMap, onClickReference, onCopyQuery: props.onCopyQuery, onEdit: props.onEditQuery, readOnly: props.readOnly }),
                    import_react.default.createElement(
                      "div",
                      { className: "graphiql-toolbar", role: "toolbar", "aria-label": "Editor Commands" },
                      import_react.default.createElement(ExecuteButton, null),
                      toolbar
                    )
                  )
                ),
                import_react.default.createElement(
                  "div",
                  { ref: editorToolsResize.dragBarRef },
                  import_react.default.createElement(
                    "div",
                    { className: "graphiql-editor-tools" },
                    import_react.default.createElement(UnStyledButton, { type: "button", className: activeSecondaryEditor === "variables" && editorToolsResize.hiddenElement !== "second" ? "active" : "", onClick: handleToolsTabClick, "data-name": "variables" }, "Variables"),
                    isHeadersEditorEnabled && import_react.default.createElement(UnStyledButton, { type: "button", className: activeSecondaryEditor === "headers" && editorToolsResize.hiddenElement !== "second" ? "active" : "", onClick: handleToolsTabClick, "data-name": "headers" }, "Headers"),
                    import_react.default.createElement(
                      Tooltip,
                      { label: editorToolsResize.hiddenElement === "second" ? "Show editor tools" : "Hide editor tools" },
                      import_react.default.createElement(UnStyledButton, { type: "button", onClick: toggleEditorTools, "aria-label": editorToolsResize.hiddenElement === "second" ? "Show editor tools" : "Hide editor tools", className: "graphiql-toggle-editor-tools" }, editorToolsResize.hiddenElement === "second" ? import_react.default.createElement(ChevronUpIcon, { className: "graphiql-chevron-icon", "aria-hidden": "true" }) : import_react.default.createElement(ChevronDownIcon, { className: "graphiql-chevron-icon", "aria-hidden": "true" }))
                    )
                  )
                ),
                import_react.default.createElement(
                  "div",
                  { ref: editorToolsResize.secondRef },
                  import_react.default.createElement(
                    "section",
                    { className: "graphiql-editor-tool", "aria-label": activeSecondaryEditor === "variables" ? "Variables" : "Headers" },
                    import_react.default.createElement(VariableEditor, { editorTheme: props.editorTheme, isHidden: activeSecondaryEditor !== "variables", keyMap: props.keyMap, onEdit: props.onEditVariables, onClickReference, readOnly: props.readOnly }),
                    isHeadersEditorEnabled && import_react.default.createElement(HeaderEditor, { editorTheme: props.editorTheme, isHidden: activeSecondaryEditor !== "headers", keyMap: props.keyMap, onEdit: props.onEditHeaders, readOnly: props.readOnly })
                  )
                )
              )
            ),
            import_react.default.createElement("div", { className: "graphiql-horizontal-drag-bar", ref: editorResize.dragBarRef }),
            import_react.default.createElement(
              "div",
              { ref: editorResize.secondRef },
              import_react.default.createElement(
                "div",
                { className: "graphiql-response" },
                executionContext.isFetching ? import_react.default.createElement(Spinner, null) : null,
                import_react.default.createElement(ResponseEditor, { editorTheme: props.editorTheme, responseTooltip: props.responseTooltip, keyMap: props.keyMap }),
                footer
              )
            )
          )
        )
      ),
      import_react.default.createElement(
        Dialog,
        { open: showDialog === "short-keys", onOpenChange: handleOpenShortKeysDialog },
        import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-header" },
          import_react.default.createElement(Dialog.Title, { className: "graphiql-dialog-title" }, "Short Keys"),
          import_react.default.createElement(Dialog.Close, null)
        ),
        import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-section" },
          import_react.default.createElement(ShortKeys, { keyMap: props.keyMap || "sublime" })
        )
      ),
      import_react.default.createElement(
        Dialog,
        { open: showDialog === "settings", onOpenChange: handleOpenSettingsDialog },
        import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-header" },
          import_react.default.createElement(Dialog.Title, { className: "graphiql-dialog-title" }, "Settings"),
          import_react.default.createElement(Dialog.Close, null)
        ),
        props.showPersistHeadersSettings ? import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-section" },
          import_react.default.createElement(
            "div",
            null,
            import_react.default.createElement("div", { className: "graphiql-dialog-section-title" }, "Persist headers"),
            import_react.default.createElement(
              "div",
              { className: "graphiql-dialog-section-caption" },
              "Save headers upon reloading.",
              " ",
              import_react.default.createElement("span", { className: "graphiql-warning-text" }, "Only enable if you trust this device.")
            )
          ),
          import_react.default.createElement(
            ButtonGroup,
            null,
            import_react.default.createElement(Button$1, { type: "button", id: "enable-persist-headers", className: editorContext.shouldPersistHeaders ? "active" : "", "data-value": "true", onClick: handlePersistHeaders }, "On"),
            import_react.default.createElement(Button$1, { type: "button", id: "disable-persist-headers", className: editorContext.shouldPersistHeaders ? "" : "active", onClick: handlePersistHeaders }, "Off")
          )
        ) : null,
        !forcedTheme && import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-section" },
          import_react.default.createElement(
            "div",
            null,
            import_react.default.createElement("div", { className: "graphiql-dialog-section-title" }, "Theme"),
            import_react.default.createElement("div", { className: "graphiql-dialog-section-caption" }, "Adjust how the interface appears.")
          ),
          import_react.default.createElement(
            ButtonGroup,
            null,
            import_react.default.createElement(Button$1, { type: "button", className: theme === null ? "active" : "", onClick: handleChangeTheme }, "System"),
            import_react.default.createElement(Button$1, { type: "button", className: theme === "light" ? "active" : "", "data-theme": "light", onClick: handleChangeTheme }, "Light"),
            import_react.default.createElement(Button$1, { type: "button", className: theme === "dark" ? "active" : "", "data-theme": "dark", onClick: handleChangeTheme }, "Dark")
          )
        ),
        storageContext ? import_react.default.createElement(
          "div",
          { className: "graphiql-dialog-section" },
          import_react.default.createElement(
            "div",
            null,
            import_react.default.createElement("div", { className: "graphiql-dialog-section-title" }, "Clear storage"),
            import_react.default.createElement("div", { className: "graphiql-dialog-section-caption" }, "Remove all locally stored data and start fresh.")
          ),
          import_react.default.createElement(Button$1, { type: "button", state: clearStorageStatus || void 0, disabled: clearStorageStatus === "success", onClick: handleClearData }, {
            success: "Cleared data",
            error: "Failed"
          }[clearStorageStatus] || "Clear data")
        ) : null
      )
    )
  );
}
var modifier = typeof window !== "undefined" && window.navigator.platform.toLowerCase().indexOf("mac") === 0 ? "Cmd" : "Ctrl";
var SHORT_KEYS = Object.entries({
  "Search in editor": [modifier, "F"],
  "Search in documentation": [modifier, "K"],
  "Execute query": [modifier, "Enter"],
  "Prettify editors": ["Ctrl", "Shift", "P"],
  "Merge fragments definitions into operation definition": [
    "Ctrl",
    "Shift",
    "M"
  ],
  "Copy query": ["Ctrl", "Shift", "C"],
  "Re-fetch schema using introspection": ["Ctrl", "Shift", "R"]
});
function ShortKeys(_a) {
  var keyMap = _a.keyMap;
  return import_react.default.createElement(
    "div",
    null,
    import_react.default.createElement(
      "table",
      { className: "graphiql-table" },
      import_react.default.createElement(
        "thead",
        null,
        import_react.default.createElement(
          "tr",
          null,
          import_react.default.createElement("th", null, "Short Key"),
          import_react.default.createElement("th", null, "Function")
        )
      ),
      import_react.default.createElement("tbody", null, SHORT_KEYS.map(function(_a2) {
        var _b = __read(_a2, 2), title = _b[0], keys = _b[1];
        return import_react.default.createElement(
          "tr",
          { key: title },
          import_react.default.createElement("td", null, keys.map(function(key, index, array) {
            return import_react.default.createElement(
              import_react.Fragment,
              { key },
              import_react.default.createElement("code", { className: "graphiql-key" }, key),
              index !== array.length - 1 && " + "
            );
          })),
          import_react.default.createElement("td", null, title)
        );
      }))
    ),
    import_react.default.createElement(
      "p",
      null,
      "The editors use",
      " ",
      import_react.default.createElement("a", { href: "https://codemirror.net/5/doc/manual.html#keymaps", target: "_blank", rel: "noopener noreferrer" }, "CodeMirror Key Maps"),
      " ",
      "that add more short keys. This instance of Graph",
      import_react.default.createElement("em", null, "i"),
      "QL uses",
      " ",
      import_react.default.createElement("code", null, keyMap),
      "."
    )
  );
}
function GraphiQLLogo(props) {
  return import_react.default.createElement("div", { className: "graphiql-logo" }, props.children || import_react.default.createElement(
    "a",
    { className: "graphiql-logo-link", href: "https://github.com/graphql/graphiql", target: "_blank", rel: "noreferrer" },
    "Graph",
    import_react.default.createElement("em", null, "i"),
    "QL"
  ));
}
GraphiQLLogo.displayName = "GraphiQLLogo";
function GraphiQLToolbar(props) {
  return import_react.default.createElement(import_react.default.Fragment, null, props.children);
}
GraphiQLToolbar.displayName = "GraphiQLToolbar";
function GraphiQLFooter(props) {
  return import_react.default.createElement("div", { className: "graphiql-footer" }, props.children);
}
GraphiQLFooter.displayName = "GraphiQLFooter";
function isChildComponentType(child, component) {
  var _a;
  if (((_a = child === null || child === void 0 ? void 0 : child.type) === null || _a === void 0 ? void 0 : _a.displayName) && child.type.displayName === component.displayName) {
    return true;
  }
  return child.type === component;
}
export {
  GraphiQL,
  GraphiQLInterface,
  GraphiQLProvider,
  GraphiQL as default
};
//# sourceMappingURL=graphiql.js.map

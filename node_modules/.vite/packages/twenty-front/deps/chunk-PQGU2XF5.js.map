{"version": 3, "sources": ["../../../../@apollo/src/link/utils/fromError.ts", "../../../../@apollo/src/link/utils/toPromise.ts", "../../../../@apollo/src/link/utils/fromPromise.ts", "../../../../@apollo/src/link/utils/throwServerError.ts", "../../../../@apollo/src/link/utils/validateOperation.ts", "../../../../@apollo/src/link/utils/createOperation.ts", "../../../../@apollo/src/link/utils/transformOperation.ts", "../../../../@apollo/src/link/utils/filterOperationVariables.ts", "../../../../@apollo/src/link/utils/index.ts", "../../../../@apollo/src/link/core/ApolloLink.ts", "../../../../@apollo/src/link/core/empty.ts", "../../../../@apollo/src/link/core/from.ts", "../../../../@apollo/src/link/core/split.ts", "../../../../@apollo/src/link/core/concat.ts", "../../../../@apollo/src/link/core/execute.ts", "../../../../@apollo/client/link/core/types.js", "../../../../@apollo/src/link/core/index.ts"], "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\n\nexport function fromError<T>(errorValue: any): Observable<T> {\n  return new Observable<T>((observer) => {\n    observer.error(errorValue);\n  });\n}\n", "import { invariant } from \"../../utilities/globals/index.js\";\nimport type { Observable } from \"../../utilities/index.js\";\n\nexport function toPromise<R>(observable: Observable<R>): Promise<R> {\n  let completed = false;\n  return new Promise<R>((resolve, reject) => {\n    observable.subscribe({\n      next: (data) => {\n        if (completed) {\n          invariant.warn(\n            `Promise Wrapper does not support multiple results from Observable`\n          );\n        } else {\n          completed = true;\n          resolve(data);\n        }\n      },\n      error: reject,\n    });\n  });\n}\n", "import { Observable } from \"../../utilities/index.js\";\n\nexport function fromPromise<T>(promise: Promise<T>): Observable<T> {\n  return new Observable<T>((observer) => {\n    promise\n      .then((value: T) => {\n        observer.next(value);\n        observer.complete();\n      })\n      .catch(observer.error.bind(observer));\n  });\n}\n", "export type ServerError = Error & {\n  response: Response;\n  result: Record<string, any> | string;\n  statusCode: number;\n};\n\nexport const throwServerError = (\n  response: Response,\n  result: any,\n  message: string\n) => {\n  const error = new Error(message) as ServerError;\n  error.name = \"ServerError\";\n  error.response = response;\n  error.statusCode = response.status;\n  error.result = result;\n  throw error;\n};\n", "import { newInvariantError } from \"../../utilities/globals/index.js\";\nimport type { GraphQLRequest } from \"../core/index.js\";\n\nexport function validateOperation(operation: GraphQLRequest): GraphQLRequest {\n  const OPERATION_FIELDS = [\n    \"query\",\n    \"operationName\",\n    \"variables\",\n    \"extensions\",\n    \"context\",\n  ];\n  for (let key of Object.keys(operation)) {\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(`illegal argument: %s`, key);\n    }\n  }\n\n  return operation;\n}\n", "import type { GraphQLRequest, Operation } from \"../core/index.js\";\n\nexport function createOperation(\n  starting: any,\n  operation: GraphQLRequest\n): Operation {\n  let context = { ...starting };\n  const setContext: Operation[\"setContext\"] = (next) => {\n    if (typeof next === \"function\") {\n      context = { ...context, ...next(context) };\n    } else {\n      context = { ...context, ...next };\n    }\n  };\n  const getContext: Operation[\"getContext\"] = () => ({ ...context });\n\n  Object.defineProperty(operation, \"setContext\", {\n    enumerable: false,\n    value: setContext,\n  });\n\n  Object.defineProperty(operation, \"getContext\", {\n    enumerable: false,\n    value: getContext,\n  });\n\n  return operation as Operation;\n}\n", "import type { GraphQLRequest, Operation } from \"../core/index.js\";\nimport { getOperationName } from \"../../utilities/index.js\";\n\nexport function transformOperation(operation: GraphQLRequest): GraphQLRequest {\n  const transformedOperation: GraphQLRequest = {\n    variables: operation.variables || {},\n    extensions: operation.extensions || {},\n    operationName: operation.operationName,\n    query: operation.query,\n  };\n\n  // Best guess at an operation name\n  if (!transformedOperation.operationName) {\n    transformedOperation.operationName =\n      typeof transformedOperation.query !== \"string\" ?\n        getOperationName(transformedOperation.query) || undefined\n      : \"\";\n  }\n\n  return transformedOperation as Operation;\n}\n", "import type { VariableDefinitionNode, DocumentNode } from \"graphql\";\nimport { visit } from \"graphql\";\n\nexport function filterOperationVariables(\n  variables: Record<string, any>,\n  query: DocumentNode\n) {\n  const result = { ...variables };\n  const unusedNames = new Set(Object.keys(variables));\n  visit(query, {\n    Variable(node, _key, parent) {\n      // A variable type definition at the top level of a query is not\n      // enough to silence server-side errors about the variable being\n      // unused, so variable definitions do not count as usage.\n      // https://spec.graphql.org/draft/#sec-All-Variables-Used\n      if (\n        parent &&\n        (parent as VariableDefinitionNode).kind !== \"VariableDefinition\"\n      ) {\n        unusedNames.delete(node.name.value);\n      }\n    },\n  });\n  unusedNames.forEach((name) => {\n    delete result![name];\n  });\n  return result;\n}\n", "import \"../../utilities/globals/index.js\";\n\nexport { fromError } from \"./fromError.js\";\nexport { toPromise } from \"./toPromise.js\";\nexport { fromPromise } from \"./fromPromise.js\";\nexport type { ServerError } from \"./throwServerError.js\";\nexport { throwServerError } from \"./throwServerError.js\";\nexport { validateOperation } from \"./validateOperation.js\";\nexport { createOperation } from \"./createOperation.js\";\nexport { transformOperation } from \"./transformOperation.js\";\nexport { filterOperationVariables } from \"./filterOperationVariables.js\";\n", "import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\n\nimport type { Observer } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type {\n  NextLink,\n  Operation,\n  RequestHandler,\n  FetchResult,\n  GraphQLRequest,\n} from \"./types.js\";\nimport {\n  validateOperation,\n  createOperation,\n  transformOperation,\n} from \"../utils/index.js\";\n\nfunction passthrough(op: Operation, forward: NextLink) {\n  return (forward ? forward(op) : Observable.of()) as Observable<FetchResult>;\n}\n\nfunction toLink(handler: RequestHandler | ApolloLink) {\n  return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\n\nfunction isTerminating(link: ApolloLink): boolean {\n  return link.request.length <= 1;\n}\n\nexport class ApolloLink {\n  public static empty(): ApolloLink {\n    return new ApolloLink(() => Observable.of());\n  }\n\n  public static from(links: (ApolloLink | RequestHandler)[]): ApolloLink {\n    if (links.length === 0) return ApolloLink.empty();\n    return links.map(toLink).reduce((x, y) => x.concat(y)) as ApolloLink;\n  }\n\n  public static split(\n    test: (op: Operation) => boolean,\n    left: ApolloLink | RequestHandler,\n    right?: ApolloLink | RequestHandler\n  ): ApolloLink {\n    const leftLink = toLink(left);\n    const rightLink = toLink(right || new ApolloLink(passthrough));\n\n    let ret: ApolloLink;\n    if (isTerminating(leftLink) && isTerminating(rightLink)) {\n      ret = new ApolloLink((operation) => {\n        return test(operation) ?\n            leftLink.request(operation) || Observable.of()\n          : rightLink.request(operation) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink((operation, forward) => {\n        return test(operation) ?\n            leftLink.request(operation, forward) || Observable.of()\n          : rightLink.request(operation, forward) || Observable.of();\n      });\n    }\n    return Object.assign(ret, { left: leftLink, right: rightLink });\n  }\n\n  public static execute(\n    link: ApolloLink,\n    operation: GraphQLRequest\n  ): Observable<FetchResult> {\n    return (\n      link.request(\n        createOperation(\n          operation.context,\n          transformOperation(validateOperation(operation))\n        )\n      ) || Observable.of()\n    );\n  }\n\n  public static concat(\n    first: ApolloLink | RequestHandler,\n    second: ApolloLink | RequestHandler\n  ) {\n    const firstLink = toLink(first);\n    if (isTerminating(firstLink)) {\n      invariant.warn(\n        `You are calling concat on a terminating link, which will have no effect %o`,\n        firstLink\n      );\n      return firstLink;\n    }\n    const nextLink = toLink(second);\n\n    let ret: ApolloLink;\n    if (isTerminating(nextLink)) {\n      ret = new ApolloLink(\n        (operation) =>\n          firstLink.request(\n            operation,\n            (op) => nextLink.request(op) || Observable.of()\n          ) || Observable.of()\n      );\n    } else {\n      ret = new ApolloLink((operation, forward) => {\n        return (\n          firstLink.request(operation, (op) => {\n            return nextLink.request(op, forward) || Observable.of();\n          }) || Observable.of()\n        );\n      });\n    }\n    return Object.assign(ret, { left: firstLink, right: nextLink });\n  }\n\n  constructor(request?: RequestHandler) {\n    if (request) this.request = request;\n  }\n\n  public split(\n    test: (op: Operation) => boolean,\n    left: ApolloLink | RequestHandler,\n    right?: ApolloLink | RequestHandler\n  ): ApolloLink {\n    return this.concat(\n      ApolloLink.split(test, left, right || new ApolloLink(passthrough))\n    );\n  }\n\n  public concat(next: ApolloLink | RequestHandler): ApolloLink {\n    return ApolloLink.concat(this, next);\n  }\n\n  public request(\n    operation: Operation,\n    forward?: NextLink\n  ): Observable<FetchResult> | null {\n    throw newInvariantError(\"request is not implemented\");\n  }\n\n  protected onError(\n    error: any,\n    observer?: Observer<FetchResult>\n  ): false | void {\n    if (observer && observer.error) {\n      observer.error(error);\n      // Returning false indicates that observer.error does not need to be\n      // called again, since it was already called (on the previous line).\n      // Calling observer.error again would not cause any real problems,\n      // since only the first call matters, but custom onError functions\n      // might have other reasons for wanting to prevent the default\n      // behavior by returning false.\n      return false;\n    }\n    // Throw errors will be passed to observer.error.\n    throw error;\n  }\n\n  public setOnError(fn: ApolloLink[\"onError\"]): this {\n    this.onError = fn;\n    return this;\n  }\n\n  /**\n   * @internal\n   * Used to iterate through all links that are concatenations or `split` links.\n   */\n  readonly left?: ApolloLink;\n  /**\n   * @internal\n   * Used to iterate through all links that are concatenations or `split` links.\n   */\n  readonly right?: ApolloLink;\n\n  /**\n   * @internal\n   * Can be provided by a link that has an internal cache to report it's memory details.\n   */\n  getMemoryInternals?: () => unknown;\n}\n", "import { ApolloLink } from \"./ApolloLink.js\";\n\nexport const empty = ApolloLink.empty;\n", "import { ApolloLink } from \"./ApolloLink.js\";\n\nexport const from = ApolloLink.from;\n", "import { ApolloLink } from \"./ApolloLink.js\";\n\nexport const split = ApolloLink.split;\n", "import { ApolloLink } from \"./ApolloLink.js\";\n\nexport const concat = ApolloLink.concat;\n", "import { ApolloLink } from \"./ApolloLink.js\";\n\nexport const execute = ApolloLink.execute;\n", "export {};\n//# sourceMappingURL=types.js.map", "import \"../../utilities/globals/index.js\";\n\nexport { empty } from \"./empty.js\";\nexport { from } from \"./from.js\";\nexport { split } from \"./split.js\";\nexport { concat } from \"./concat.js\";\nexport { execute } from \"./execute.js\";\nexport { ApolloLink } from \"./ApolloLink.js\";\n\nexport * from \"./types.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEM,SAAU,UAAa,YAAe;AAC1C,SAAO,IAAI,WAAc,SAAC,UAAQ;AAChC,aAAS,MAAM,UAAU;EAC3B,CAAC;AACH;AANA;;;;;;;ACGM,SAAU,UAAa,YAAyB;AACpD,MAAI,YAAY;AAChB,SAAO,IAAI,QAAW,SAAC,SAAS,QAAM;AACpC,eAAW,UAAU;MACnB,MAAM,SAAC,MAAI;AACT,YAAI,WAAW;AACb,qBAAU,YACR,SAAA,UAAA,KAAA,EAAA;QAEJ,OAAO;AACL,sBAAY;AACZ,kBAAQ,IAAI;QACd;MACF;MACA,OAAO;KACR;EACH,CAAC;AACH;AApBA;;;;;;;ACEM,SAAU,YAAe,SAAmB;AAChD,SAAO,IAAI,WAAc,SAAC,UAAQ;AAChC,YACG,KAAK,SAAC,OAAQ;AACb,eAAS,KAAK,KAAK;AACnB,eAAS,SAAQ;IACnB,CAAC,EACA,MAAM,SAAS,MAAM,KAAK,QAAQ,CAAC;EACxC,CAAC;AACH;AAXA;;;;;;;ACMA,IAAa;AAAb;;AAAO,IAAM,mBAAmB,SAC9B,UACA,QACA,SAAe;AAEf,UAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,YAAM,OAAO;AACb,YAAM,WAAW;AACjB,YAAM,aAAa,SAAS;AAC5B,YAAM,SAAS;AACf,YAAM;IACR;;;;;ACdM,SAAU,kBAAkB,WAAyB;AACzD,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;;AAEF,WAAgB,KAAA,GAAA,KAAA,OAAO,KAAK,SAAS,GAArB,KAAA,GAAA,QAAA,MAAwB;AAAnC,QAAI,MAAG,GAAA,EAAA;AACV,QAAI,iBAAiB,QAAQ,GAAG,IAAI,GAAG;AACrC,YAAM,kBAAkB,IAAA,GAAA;IAC1B;EACF;AAEA,SAAO;AACT;AAlBA;;;;;;;ACEM,SAAU,gBACd,UACA,WAAyB;AAEzB,MAAI,UAAO,SAAA,CAAA,GAAQ,QAAQ;AAC3B,MAAM,aAAsC,SAAC,MAAI;AAC/C,QAAI,OAAO,SAAS,YAAY;AAC9B,gBAAO,SAAA,SAAA,CAAA,GAAQ,OAAO,GAAK,KAAK,OAAO,CAAC;IAC1C,OAAO;AACL,gBAAO,SAAA,SAAA,CAAA,GAAQ,OAAO,GAAK,IAAI;IACjC;EACF;AACA,MAAM,aAAsC,WAAA;AAAM,WAAA,SAAA,CAAA,GAAM,OAAO;EAAb;AAElD,SAAO,eAAe,WAAW,cAAc;IAC7C,YAAY;IACZ,OAAO;GACR;AAED,SAAO,eAAe,WAAW,cAAc;IAC7C,YAAY;IACZ,OAAO;GACR;AAED,SAAO;AACT;;;;;;;;ACxBM,SAAU,mBAAmB,WAAyB;AAC1D,MAAM,uBAAuC;IAC3C,WAAW,UAAU,aAAa,CAAA;IAClC,YAAY,UAAU,cAAc,CAAA;IACpC,eAAe,UAAU;IACzB,OAAO,UAAU;;AAInB,MAAI,CAAC,qBAAqB,eAAe;AACvC,yBAAqB,gBACnB,OAAO,qBAAqB,UAAU,WACpC,iBAAiB,qBAAqB,KAAK,KAAK,SAChD;EACN;AAEA,SAAO;AACT;AAnBA;;;;;;;ACEM,SAAU,yBACd,WACA,OAAmB;AAEnB,MAAM,SAAM,SAAA,CAAA,GAAQ,SAAS;AAC7B,MAAM,cAAc,IAAI,IAAI,OAAO,KAAK,SAAS,CAAC;AAClD,QAAM,OAAO;IACX,UAAQ,SAAC,MAAM,MAAM,QAAM;AAKzB,UACE,UACC,OAAkC,SAAS,sBAC5C;AACA,oBAAY,OAAO,KAAK,KAAK,KAAK;MACpC;IACF;GACD;AACD,cAAY,QAAQ,SAAC,MAAI;AACvB,WAAO,OAAQ,IAAI;EACrB,CAAC;AACD,SAAO;AACT;;;;AA1BA;;;;;ACDA;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;ACOA,SAAS,YAAY,IAAe,SAAiB;AACnD,SAAQ,UAAU,QAAQ,EAAE,IAAI,WAAW,GAAE;AAC/C;AAEA,SAAS,OAAO,SAAoC;AAClD,SAAO,OAAO,YAAY,aAAa,IAAI,WAAW,OAAO,IAAI;AACnE;AAEA,SAAS,cAAc,MAAgB;AACrC,SAAO,KAAK,QAAQ,UAAU;AAChC;AA3BA,IA6BA;AA7BA;;;AAGA;AAQA;AAkBA,IAAA;IAAA,WAAA;AAoFE,eAAAA,YAAY,SAAwB;AAClC,YAAI;AAAS,eAAK,UAAU;MAC9B;AArFc,MAAAA,YAAA,QAAd,WAAA;AACE,eAAO,IAAIA,YAAW,WAAA;AAAM,iBAAA,WAAW,GAAE;QAAb,CAAe;MAC7C;AAEc,MAAAA,YAAA,OAAd,SAAmB,OAAsC;AACvD,YAAI,MAAM,WAAW;AAAG,iBAAOA,YAAW,MAAK;AAC/C,eAAO,MAAM,IAAI,MAAM,EAAE,OAAO,SAAC,GAAG,GAAC;AAAK,iBAAA,EAAE,OAAO,CAAC;QAAV,CAAW;MACvD;AAEc,MAAAA,YAAA,QAAd,SACE,MACA,MACA,OAAmC;AAEnC,YAAM,WAAW,OAAO,IAAI;AAC5B,YAAM,YAAY,OAAO,SAAS,IAAIA,YAAW,WAAW,CAAC;AAE7D,YAAI;AACJ,YAAI,cAAc,QAAQ,KAAK,cAAc,SAAS,GAAG;AACvD,gBAAM,IAAIA,YAAW,SAAC,WAAS;AAC7B,mBAAO,KAAK,SAAS,IACjB,SAAS,QAAQ,SAAS,KAAK,WAAW,GAAE,IAC5C,UAAU,QAAQ,SAAS,KAAK,WAAW,GAAE;UACnD,CAAC;QACH,OAAO;AACL,gBAAM,IAAIA,YAAW,SAAC,WAAW,SAAO;AACtC,mBAAO,KAAK,SAAS,IACjB,SAAS,QAAQ,WAAW,OAAO,KAAK,WAAW,GAAE,IACrD,UAAU,QAAQ,WAAW,OAAO,KAAK,WAAW,GAAE;UAC5D,CAAC;QACH;AACA,eAAO,OAAO,OAAO,KAAK,EAAE,MAAM,UAAU,OAAO,UAAS,CAAE;MAChE;AAEc,MAAAA,YAAA,UAAd,SACE,MACA,WAAyB;AAEzB,eACE,KAAK,QACH,gBACE,UAAU,SACV,mBAAmB,kBAAkB,SAAS,CAAC,CAAC,CACjD,KACE,WAAW,GAAE;MAEtB;AAEc,MAAAA,YAAA,SAAd,SACE,OACA,QAAmC;AAEnC,YAAM,YAAY,OAAO,KAAK;AAC9B,YAAI,cAAc,SAAS,GAAG;AAC5B,qBAAU,YACR,SAAA,UAAA,KAAA,IAAA,SAAA;AAGF,iBAAO;QACT;AACA,YAAM,WAAW,OAAO,MAAM;AAE9B,YAAI;AACJ,YAAI,cAAc,QAAQ,GAAG;AAC3B,gBAAM,IAAIA,YACR,SAAC,WAAS;AACR,mBAAA,UAAU,QACR,WACA,SAAC,IAAE;AAAK,qBAAA,SAAS,QAAQ,EAAE,KAAK,WAAW,GAAE;YAArC,CAAuC,KAC5C,WAAW,GAAE;UAHlB,CAGoB;QAE1B,OAAO;AACL,gBAAM,IAAIA,YAAW,SAAC,WAAW,SAAO;AACtC,mBACE,UAAU,QAAQ,WAAW,SAAC,IAAE;AAC9B,qBAAO,SAAS,QAAQ,IAAI,OAAO,KAAK,WAAW,GAAE;YACvD,CAAC,KAAK,WAAW,GAAE;UAEvB,CAAC;QACH;AACA,eAAO,OAAO,OAAO,KAAK,EAAE,MAAM,WAAW,OAAO,SAAQ,CAAE;MAChE;AAMO,MAAAA,YAAA,UAAA,QAAP,SACE,MACA,MACA,OAAmC;AAEnC,eAAO,KAAK,OACVA,YAAW,MAAM,MAAM,MAAM,SAAS,IAAIA,YAAW,WAAW,CAAC,CAAC;MAEtE;AAEO,MAAAA,YAAA,UAAA,SAAP,SAAc,MAAiC;AAC7C,eAAOA,YAAW,OAAO,MAAM,IAAI;MACrC;AAEO,MAAAA,YAAA,UAAA,UAAP,SACE,WACA,SAAkB;AAElB,cAAM,kBAAkB,EAAA;MAC1B;AAEU,MAAAA,YAAA,UAAA,UAAV,SACE,OACA,UAAgC;AAEhC,YAAI,YAAY,SAAS,OAAO;AAC9B,mBAAS,MAAM,KAAK;AAOpB,iBAAO;QACT;AAEA,cAAM;MACR;AAEO,MAAAA,YAAA,UAAA,aAAP,SAAkB,IAAyB;AACzC,aAAK,UAAU;AACf,eAAO;MACT;AAkBF,aAAAA;IAAA,EApJA;;;;;AC7BA,IAEa;AAFb;;;AAEO,IAAM,QAAQ,WAAW;;;;;ACFhC,IAEa;AAFb;;;AAEO,IAAM,OAAO,WAAW;;;;;ACF/B,IAEa;AAFb;;;AAEO,IAAM,QAAQ,WAAW;;;;;ACFhC,IAEa;AAFb;;;AAEO,IAAM,SAAS,WAAW;;;;;ACFjC,IAEa;AAFb;;;AAEO,IAAM,UAAU,WAAW;;;;;ACFlC;AAAA;AAAA;AAAA;;;ACAA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;", "names": ["ApolloLink"]}
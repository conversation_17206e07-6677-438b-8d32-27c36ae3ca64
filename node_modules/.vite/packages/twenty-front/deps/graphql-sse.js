import {
  execute,
  getOperationAST,
  init_graphql,
  parse,
  subscribe,
  validate
} from "./chunk-I7RZVVY3.js";
import "./chunk-XPZLJQLW.js";

// node_modules/graphql-sse/lib/utils.mjs
function isObject(val) {
  return typeof val === "object" && val !== null;
}

// node_modules/graphql-sse/lib/common.mjs
var TOKEN_HEADER_KEY = "x-graphql-event-stream-token";
var TOKEN_QUERY_KEY = "token";
function validateStreamEvent(e) {
  e = e;
  if (e !== "next" && e !== "complete")
    throw new Error(`Invalid stream event "${e}"`);
  return e;
}
function print(msg) {
  let str = `event: ${msg.event}
data:`;
  if (msg.data) {
    str += " ";
    str += JSON.stringify(msg.data);
  }
  str += "\n\n";
  return str;
}
function parseStreamData(e, data) {
  if (data) {
    try {
      data = JSON.parse(data);
    } catch {
      throw new Error("Invalid stream data");
    }
  }
  if (e === "next" && !data)
    throw new Error('Stream data must be an object for "next" events');
  return data || null;
}
function isAsyncIterable(val) {
  return typeof Object(val)[Symbol.asyncIterator] === "function";
}
function isAsyncGenerator(val) {
  return isObject(val) && typeof Object(val)[Symbol.asyncIterator] === "function" && typeof val.return === "function" && typeof val.throw === "function" && typeof val.next === "function";
}

// node_modules/graphql-sse/lib/handler.mjs
init_graphql();
function createHandler(options) {
  const { validate: validate2 = validate, execute: execute2 = execute, subscribe: subscribe2 = subscribe, schema, authenticate = function extractOrCreateStreamToken(req) {
    var _a;
    const headerToken = req.headers.get(TOKEN_HEADER_KEY);
    if (headerToken)
      return Array.isArray(headerToken) ? headerToken.join("") : headerToken;
    const urlToken = new URL((_a = req.url) !== null && _a !== void 0 ? _a : "", "http://localhost/").searchParams.get(TOKEN_QUERY_KEY);
    if (urlToken)
      return urlToken;
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0, v = c == "x" ? r : r & 3 | 8;
      return v.toString(16);
    });
  }, onConnect, context, onSubscribe, onOperation, onNext, onComplete } = options;
  const streams = {};
  function createStream(token) {
    const ops = {};
    let pinger;
    const msgs = (() => {
      const pending = [];
      const deferred = {
        done: false,
        error: null,
        resolve: () => {
        }
      };
      async function dispose() {
        clearInterval(pinger);
        if (typeof token === "string")
          delete streams[token];
        for (const op of Object.values(ops)) {
          if (isAsyncGenerator(op)) {
            await op.return(void 0);
          }
        }
      }
      const iterator = async function* iterator2() {
        for (; ; ) {
          if (!pending.length) {
            await new Promise((resolve) => deferred.resolve = resolve);
          }
          while (pending.length) {
            yield pending.shift();
          }
          if (deferred.error) {
            throw deferred.error;
          }
          if (deferred.done) {
            return;
          }
        }
      }();
      iterator.throw = async (err) => {
        if (!deferred.done) {
          deferred.done = true;
          deferred.error = err;
          deferred.resolve();
          await dispose();
        }
        return { done: true, value: void 0 };
      };
      iterator.return = async () => {
        if (!deferred.done) {
          deferred.done = true;
          deferred.resolve();
          await dispose();
        }
        return { done: true, value: void 0 };
      };
      return {
        next(msg) {
          pending.push(msg);
          deferred.resolve();
        },
        iterator
      };
    })();
    let subscribed = false;
    return {
      get open() {
        return subscribed;
      },
      ops,
      subscribe() {
        subscribed = true;
        msgs.next(":\n\n");
        pinger = setInterval(() => msgs.next(":\n\n"), 12e3);
        return msgs.iterator;
      },
      from(ctx, req, result, opId) {
        (async () => {
          if (isAsyncIterable(result)) {
            for await (let part of result) {
              const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, req, part));
              if (maybeResult)
                part = maybeResult;
              msgs.next(print({
                event: "next",
                data: opId ? {
                  id: opId,
                  payload: part
                } : part
              }));
            }
          } else {
            const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, req, result));
            if (maybeResult)
              result = maybeResult;
            msgs.next(print({
              event: "next",
              data: opId ? {
                id: opId,
                payload: result
              } : result
            }));
          }
          msgs.next(print({
            event: "complete",
            data: opId ? { id: opId } : null
          }));
          await (onComplete === null || onComplete === void 0 ? void 0 : onComplete(ctx, req));
          if (!opId) {
            await msgs.iterator.return();
          } else {
            delete ops[opId];
          }
        })().catch(msgs.iterator.throw);
      }
    };
  }
  async function prepare(req, params) {
    let args;
    const onSubscribeResult = await (onSubscribe === null || onSubscribe === void 0 ? void 0 : onSubscribe(req, params));
    if (isResponse(onSubscribeResult))
      return onSubscribeResult;
    else if (isExecutionResult(onSubscribeResult) || isAsyncIterable(onSubscribeResult))
      return {
        // even if the result is already available, use
        // context because onNext and onComplete needs it
        ctx: typeof context === "function" ? await context(req, params) : context,
        perform() {
          return onSubscribeResult;
        }
      };
    else if (onSubscribeResult)
      args = onSubscribeResult;
    else {
      if (!schema)
        throw new Error("The GraphQL schema is not provided");
      const { operationName, variables } = params;
      let query;
      try {
        query = parse(params.query);
      } catch (err) {
        return [
          JSON.stringify({
            errors: [
              err instanceof Error ? {
                message: err.message
                // TODO: stack might leak sensitive information
                // stack: err.stack,
              } : err
            ]
          }),
          {
            status: 400,
            statusText: "Bad Request",
            headers: { "content-type": "application/json; charset=utf-8" }
          }
        ];
      }
      const argsWithoutSchema = {
        operationName,
        document: query,
        variableValues: variables,
        contextValue: typeof context === "function" ? await context(req, params) : context
      };
      args = {
        ...argsWithoutSchema,
        schema: typeof schema === "function" ? await schema(req, argsWithoutSchema) : schema
      };
    }
    let operation;
    try {
      const ast = getOperationAST(args.document, args.operationName);
      if (!ast)
        throw null;
      operation = ast.operation;
    } catch {
      return [
        JSON.stringify({
          errors: [{ message: "Unable to detect operation AST" }]
        }),
        {
          status: 400,
          statusText: "Bad Request",
          headers: { "content-type": "application/json; charset=utf-8" }
        }
      ];
    }
    if (operation === "mutation" && req.method === "GET") {
      return [
        JSON.stringify({
          errors: [{ message: "Cannot perform mutations over GET" }]
        }),
        {
          status: 405,
          statusText: "Method Not Allowed",
          headers: {
            allow: "POST",
            "content-type": "application/json; charset=utf-8"
          }
        }
      ];
    }
    const validationErrs = validate2(args.schema, args.document);
    if (validationErrs.length) {
      if (req.headers.get("accept") === "text/event-stream") {
        return {
          ctx: args.contextValue,
          perform() {
            return { errors: validationErrs };
          }
        };
      }
      return [
        JSON.stringify({ errors: validationErrs }),
        {
          status: 400,
          statusText: "Bad Request",
          headers: { "content-type": "application/json; charset=utf-8" }
        }
      ];
    }
    return {
      ctx: args.contextValue,
      async perform() {
        const result = await (operation === "subscription" ? subscribe2(args) : execute2(args));
        const maybeResult = await (onOperation === null || onOperation === void 0 ? void 0 : onOperation(args.contextValue, req, args, result));
        if (maybeResult)
          return maybeResult;
        return result;
      }
    };
  }
  return async function handler(req) {
    var _a, _b, _c;
    const token = await authenticate(req);
    if (isResponse(token))
      return token;
    const accept = req.headers.get("accept") || "*/*";
    const stream = typeof token === "string" ? streams[token] : null;
    if (accept === "text/event-stream") {
      const maybeResponse = await (onConnect === null || onConnect === void 0 ? void 0 : onConnect(req));
      if (isResponse(maybeResponse))
        return maybeResponse;
      if (!stream) {
        const paramsOrResponse2 = await parseReq(req);
        if (isResponse(paramsOrResponse2))
          return paramsOrResponse2;
        const params2 = paramsOrResponse2;
        const distinctStream = createStream(null);
        distinctStream.ops[""] = null;
        const prepared2 = await prepare(req, params2);
        if (isResponse(prepared2))
          return prepared2;
        const result2 = await prepared2.perform();
        if (isAsyncIterable(result2))
          distinctStream.ops[""] = result2;
        distinctStream.from(prepared2.ctx, req, result2, null);
        return [
          distinctStream.subscribe(),
          {
            status: 200,
            statusText: "OK",
            headers: {
              connection: "keep-alive",
              "cache-control": "no-cache",
              "content-encoding": "none",
              "content-type": "text/event-stream; charset=utf-8"
            }
          }
        ];
      }
      if (stream.open) {
        return [
          JSON.stringify({ errors: [{ message: "Stream already open" }] }),
          {
            status: 409,
            statusText: "Conflict",
            headers: {
              "content-type": "application/json; charset=utf-8"
            }
          }
        ];
      }
      return [
        stream.subscribe(),
        {
          status: 200,
          statusText: "OK",
          headers: {
            connection: "keep-alive",
            "cache-control": "no-cache",
            "content-encoding": "none",
            "content-type": "text/event-stream; charset=utf-8"
          }
        }
      ];
    }
    if (typeof token !== "string") {
      return [null, { status: 404, statusText: "Not Found" }];
    }
    if (req.method === "PUT") {
      if (!["*/*", "text/plain"].includes(accept)) {
        return [null, { status: 406, statusText: "Not Acceptable" }];
      }
      if (stream) {
        return [
          JSON.stringify({
            errors: [{ message: "Stream already registered" }]
          }),
          {
            status: 409,
            statusText: "Conflict",
            headers: {
              "content-type": "application/json; charset=utf-8"
            }
          }
        ];
      }
      streams[token] = createStream(token);
      return [
        token,
        {
          status: 201,
          statusText: "Created",
          headers: {
            "content-type": "text/plain; charset=utf-8"
          }
        }
      ];
    } else if (req.method === "DELETE") {
      if (!stream) {
        return [
          JSON.stringify({
            errors: [{ message: "Stream not found" }]
          }),
          {
            status: 404,
            statusText: "Not Found",
            headers: {
              "content-type": "application/json; charset=utf-8"
            }
          }
        ];
      }
      const opId2 = new URL((_a = req.url) !== null && _a !== void 0 ? _a : "", "http://localhost/").searchParams.get("operationId");
      if (!opId2) {
        return [
          JSON.stringify({
            errors: [{ message: "Operation ID is missing" }]
          }),
          {
            status: 400,
            statusText: "Bad Request",
            headers: {
              "content-type": "application/json; charset=utf-8"
            }
          }
        ];
      }
      const op = stream.ops[opId2];
      if (isAsyncGenerator(op))
        op.return(void 0);
      delete stream.ops[opId2];
      return [
        null,
        {
          status: 200,
          statusText: "OK"
        }
      ];
    } else if (req.method !== "GET" && req.method !== "POST") {
      return [
        null,
        {
          status: 405,
          statusText: "Method Not Allowed",
          headers: {
            allow: "GET, POST, PUT, DELETE"
          }
        }
      ];
    } else if (!stream) {
      return [
        JSON.stringify({
          errors: [{ message: "Stream not found" }]
        }),
        {
          status: 404,
          statusText: "Not Found",
          headers: {
            "content-type": "application/json; charset=utf-8"
          }
        }
      ];
    }
    if (!["*/*", "application/*", "application/json"].includes(accept)) {
      return [
        null,
        {
          status: 406,
          statusText: "Not Acceptable"
        }
      ];
    }
    const paramsOrResponse = await parseReq(req);
    if (isResponse(paramsOrResponse))
      return paramsOrResponse;
    const params = paramsOrResponse;
    const opId = String((_c = (_b = params.extensions) === null || _b === void 0 ? void 0 : _b.operationId) !== null && _c !== void 0 ? _c : "");
    if (!opId) {
      return [
        JSON.stringify({
          errors: [{ message: "Operation ID is missing" }]
        }),
        {
          status: 400,
          statusText: "Bad Request",
          headers: {
            "content-type": "application/json; charset=utf-8"
          }
        }
      ];
    }
    if (opId in stream.ops) {
      return [
        JSON.stringify({
          errors: [{ message: "Operation with ID already exists" }]
        }),
        {
          status: 409,
          statusText: "Conflict",
          headers: {
            "content-type": "application/json; charset=utf-8"
          }
        }
      ];
    }
    stream.ops[opId] = null;
    const prepared = await prepare(req, params);
    if (isResponse(prepared))
      return prepared;
    if (!(opId in stream.ops)) {
      return [
        null,
        {
          status: 204,
          statusText: "No Content"
        }
      ];
    }
    const result = await prepared.perform();
    if (!(opId in stream.ops)) {
      if (isAsyncGenerator(result))
        result.return(void 0);
      if (!(opId in stream.ops)) {
        return [
          null,
          {
            status: 204,
            statusText: "No Content"
          }
        ];
      }
    }
    if (isAsyncIterable(result))
      stream.ops[opId] = result;
    stream.from(prepared.ctx, req, result, opId);
    return [null, { status: 202, statusText: "Accepted" }];
  };
}
async function parseReq(req) {
  var _a, _b, _c;
  const params = {};
  try {
    switch (true) {
      case req.method === "GET": {
        try {
          const [, search] = req.url.split("?");
          const searchParams = new URLSearchParams(search);
          params.operationName = (_a = searchParams.get("operationName")) !== null && _a !== void 0 ? _a : void 0;
          params.query = (_b = searchParams.get("query")) !== null && _b !== void 0 ? _b : void 0;
          const variables = searchParams.get("variables");
          if (variables)
            params.variables = JSON.parse(variables);
          const extensions = searchParams.get("extensions");
          if (extensions)
            params.extensions = JSON.parse(extensions);
        } catch {
          throw new Error("Unparsable URL");
        }
        break;
      }
      case (req.method === "POST" && ((_c = req.headers.get("content-type")) === null || _c === void 0 ? void 0 : _c.includes("application/json"))): {
        if (!req.body) {
          throw new Error("Missing body");
        }
        const body = typeof req.body === "function" ? await req.body() : req.body;
        const data = typeof body === "string" ? JSON.parse(body) : body;
        if (!isObject(data)) {
          throw new Error("JSON body must be an object");
        }
        params.operationName = data.operationName;
        params.query = data.query;
        params.variables = data.variables;
        params.extensions = data.extensions;
        break;
      }
      default:
        return [
          null,
          {
            status: 415,
            statusText: "Unsupported Media Type"
          }
        ];
    }
    if (params.query == null)
      throw new Error("Missing query");
    if (typeof params.query !== "string")
      throw new Error("Invalid query");
    if (params.variables != null && (typeof params.variables !== "object" || Array.isArray(params.variables))) {
      throw new Error("Invalid variables");
    }
    if (params.extensions != null && (typeof params.extensions !== "object" || Array.isArray(params.extensions))) {
      throw new Error("Invalid extensions");
    }
    return params;
  } catch (err) {
    return [
      JSON.stringify({
        errors: [
          err instanceof Error ? {
            message: err.message
            // TODO: stack might leak sensitive information
            // stack: err.stack,
          } : err
        ]
      }),
      {
        status: 400,
        statusText: "Bad Request",
        headers: { "content-type": "application/json; charset=utf-8" }
      }
    ];
  }
}
function isResponse(val) {
  return Array.isArray(val);
}
function isExecutionResult(val) {
  return isObject(val) && ("data" in val || "data" in val && val.data == null && "errors" in val);
}

// node_modules/graphql-sse/lib/parser.mjs
var ControlChars;
(function(ControlChars2) {
  ControlChars2[ControlChars2["NewLine"] = 10] = "NewLine";
  ControlChars2[ControlChars2["CarriageReturn"] = 13] = "CarriageReturn";
  ControlChars2[ControlChars2["Space"] = 32] = "Space";
  ControlChars2[ControlChars2["Colon"] = 58] = "Colon";
})(ControlChars || (ControlChars = {}));
function createParser() {
  let buffer;
  let position;
  let fieldLength;
  let discardTrailingNewline = false;
  let message = { event: "", data: "" };
  let pending = [];
  const decoder = new TextDecoder();
  return function parse2(chunk) {
    if (buffer === void 0) {
      buffer = chunk;
      position = 0;
      fieldLength = -1;
    } else {
      const concat = new Uint8Array(buffer.length + chunk.length);
      concat.set(buffer);
      concat.set(chunk, buffer.length);
      buffer = concat;
    }
    const bufLength = buffer.length;
    let lineStart = 0;
    while (position < bufLength) {
      if (discardTrailingNewline) {
        if (buffer[position] === ControlChars.NewLine) {
          lineStart = ++position;
        }
        discardTrailingNewline = false;
      }
      let lineEnd = -1;
      for (; position < bufLength && lineEnd === -1; ++position) {
        switch (buffer[position]) {
          case ControlChars.Colon:
            if (fieldLength === -1) {
              fieldLength = position - lineStart;
            }
            break;
          // \r case below should fallthrough to \n:
          case ControlChars.CarriageReturn:
            discardTrailingNewline = true;
          // eslint-disable-next-line no-fallthrough
          case ControlChars.NewLine:
            lineEnd = position;
            break;
        }
      }
      if (lineEnd === -1) {
        break;
      } else if (lineStart === lineEnd) {
        if (message.event || message.data) {
          if (!message.event)
            throw new Error("Missing message event");
          const event = validateStreamEvent(message.event);
          const data = parseStreamData(event, message.data);
          pending.push({
            event,
            data
          });
          message = { event: "", data: "" };
        }
      } else if (fieldLength > 0) {
        const line = buffer.subarray(lineStart, lineEnd);
        const field = decoder.decode(line.subarray(0, fieldLength));
        const valueOffset = fieldLength + (line[fieldLength + 1] === ControlChars.Space ? 2 : 1);
        const value = decoder.decode(line.subarray(valueOffset));
        switch (field) {
          case "event":
            message.event = value;
            break;
          case "data":
            message.data = message.data ? message.data + "\n" + value : value;
            break;
        }
      }
      lineStart = position;
      fieldLength = -1;
    }
    if (lineStart === bufLength) {
      buffer = void 0;
      const messages = [...pending];
      pending = [];
      return messages;
    } else if (lineStart !== 0) {
      buffer = buffer.subarray(lineStart);
      position -= lineStart;
    }
  };
}

// node_modules/graphql-sse/lib/client.mjs
function createClient(options) {
  const {
    singleConnection = false,
    lazy = true,
    lazyCloseTimeout = 0,
    onNonLazyError = console.error,
    /**
     * Generates a v4 UUID to be used as the ID using `Math`
     * as the random number generator. Supply your own generator
     * in case you need more uniqueness.
     *
     * Reference: https://gist.github.com/jed/982883
     */
    generateID = function generateUUID() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0, v = c == "x" ? r : r & 3 | 8;
        return v.toString(16);
      });
    },
    retryAttempts = 5,
    retry = async function randomisedExponentialBackoff(retries2) {
      let retryDelay = 1e3;
      for (let i = 0; i < retries2; i++) {
        retryDelay *= 2;
      }
      await new Promise((resolve) => setTimeout(resolve, retryDelay + // add random timeout from 300ms to 3s
      Math.floor(Math.random() * (3e3 - 300) + 300)));
    },
    credentials = "same-origin",
    referrer,
    referrerPolicy,
    onMessage,
    on: clientOn
  } = options;
  const fetchFn = options.fetchFn || fetch;
  const AbortControllerImpl = options.abortControllerImpl || AbortController;
  const client = /* @__PURE__ */ (() => {
    let disposed = false;
    const listeners = [];
    return {
      get disposed() {
        return disposed;
      },
      onDispose(cb) {
        if (disposed) {
          setTimeout(() => cb(), 0);
          return () => {
          };
        }
        listeners.push(cb);
        return () => {
          listeners.splice(listeners.indexOf(cb), 1);
        };
      },
      dispose() {
        if (disposed)
          return;
        disposed = true;
        for (const listener of [...listeners]) {
          listener();
        }
      }
    };
  })();
  let connCtrl, conn, locks = 0, retryingErr = null, retries = 0;
  async function getOrConnect() {
    try {
      if (client.disposed)
        throw new Error("Client has been disposed");
      return await (conn !== null && conn !== void 0 ? conn : conn = (async () => {
        var _a, _b, _c;
        if (retryingErr) {
          await retry(retries);
          if (connCtrl.signal.aborted)
            throw new Error("Connection aborted by the client");
          retries++;
        }
        (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connecting) === null || _a === void 0 ? void 0 : _a.call(clientOn, !!retryingErr);
        connCtrl = new AbortControllerImpl();
        const unlistenDispose = client.onDispose(() => connCtrl.abort());
        connCtrl.signal.addEventListener("abort", () => {
          unlistenDispose();
          conn = void 0;
        });
        const url = typeof options.url === "function" ? await options.url() : options.url;
        if (connCtrl.signal.aborted)
          throw new Error("Connection aborted by the client");
        const headers = typeof options.headers === "function" ? await options.headers() : (_b = options.headers) !== null && _b !== void 0 ? _b : {};
        if (connCtrl.signal.aborted)
          throw new Error("Connection aborted by the client");
        let res;
        try {
          res = await fetchFn(url, {
            signal: connCtrl.signal,
            method: "PUT",
            credentials,
            referrer,
            referrerPolicy,
            headers
          });
        } catch (err) {
          throw new NetworkError(err);
        }
        if (res.status !== 201)
          throw new NetworkError(res);
        const token = await res.text();
        headers[TOKEN_HEADER_KEY] = token;
        const connected = await connect({
          signal: connCtrl.signal,
          headers,
          credentials,
          referrer,
          referrerPolicy,
          url,
          fetchFn,
          onMessage: (msg) => {
            var _a2;
            (_a2 = clientOn === null || clientOn === void 0 ? void 0 : clientOn.message) === null || _a2 === void 0 ? void 0 : _a2.call(clientOn, msg);
            onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg);
          }
        });
        (_c = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connected) === null || _c === void 0 ? void 0 : _c.call(clientOn, !!retryingErr);
        connected.waitForThrow().catch(() => conn = void 0);
        return connected;
      })());
    } catch (err) {
      conn = void 0;
      throw err;
    }
  }
  if (singleConnection && !lazy) {
    (async () => {
      locks++;
      for (; ; ) {
        try {
          const { waitForThrow } = await getOrConnect();
          await waitForThrow();
        } catch (err) {
          if (client.disposed)
            return;
          if (!(err instanceof NetworkError))
            return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(err);
          conn = void 0;
          if (!retryAttempts || retries >= retryAttempts)
            return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(err);
          retryingErr = err;
        }
      }
    })();
  }
  function subscribe2(request, sink, on) {
    if (!singleConnection) {
      const control2 = new AbortControllerImpl();
      const unlisten2 = client.onDispose(() => {
        unlisten2();
        control2.abort();
      });
      (async () => {
        var _a, _b, _c, _d, _e;
        let retryingErr2 = null, retries2 = 0;
        for (; ; ) {
          try {
            if (retryingErr2) {
              await retry(retries2);
              if (control2.signal.aborted)
                throw new Error("Connection aborted by the client");
              retries2++;
            }
            (_a = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connecting) === null || _a === void 0 ? void 0 : _a.call(clientOn, !!retryingErr2);
            (_b = on === null || on === void 0 ? void 0 : on.connecting) === null || _b === void 0 ? void 0 : _b.call(on, !!retryingErr2);
            const url = typeof options.url === "function" ? await options.url() : options.url;
            if (control2.signal.aborted)
              throw new Error("Connection aborted by the client");
            const headers = typeof options.headers === "function" ? await options.headers() : (_c = options.headers) !== null && _c !== void 0 ? _c : {};
            if (control2.signal.aborted)
              throw new Error("Connection aborted by the client");
            const { getResults } = await connect({
              signal: control2.signal,
              headers: {
                ...headers,
                "content-type": "application/json; charset=utf-8"
              },
              credentials,
              referrer,
              referrerPolicy,
              url,
              body: JSON.stringify(request),
              fetchFn,
              onMessage: (msg) => {
                var _a2, _b2;
                (_a2 = clientOn === null || clientOn === void 0 ? void 0 : clientOn.message) === null || _a2 === void 0 ? void 0 : _a2.call(clientOn, msg);
                (_b2 = on === null || on === void 0 ? void 0 : on.message) === null || _b2 === void 0 ? void 0 : _b2.call(on, msg);
                onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg);
              }
            });
            (_d = clientOn === null || clientOn === void 0 ? void 0 : clientOn.connected) === null || _d === void 0 ? void 0 : _d.call(clientOn, !!retryingErr2);
            (_e = on === null || on === void 0 ? void 0 : on.connected) === null || _e === void 0 ? void 0 : _e.call(on, !!retryingErr2);
            for await (const result of getResults()) {
              retryingErr2 = null;
              retries2 = 0;
              sink.next(result);
            }
            return control2.abort();
          } catch (err) {
            if (control2.signal.aborted)
              return;
            if (!(err instanceof NetworkError))
              throw err;
            if (!retryAttempts || retries2 >= retryAttempts)
              throw err;
            retryingErr2 = err;
          }
        }
      })().then(() => sink.complete()).catch((err) => sink.error(err));
      return () => control2.abort();
    }
    locks++;
    const control = new AbortControllerImpl();
    const unlisten = client.onDispose(() => {
      unlisten();
      control.abort();
    });
    (async () => {
      const operationId = generateID();
      request = {
        ...request,
        extensions: { ...request.extensions, operationId }
      };
      let complete = null;
      for (; ; ) {
        complete = null;
        try {
          const { url, headers, getResults } = await getOrConnect();
          let res;
          try {
            res = await fetchFn(url, {
              signal: control.signal,
              method: "POST",
              credentials,
              referrer,
              referrerPolicy,
              headers: {
                ...headers,
                "content-type": "application/json; charset=utf-8"
              },
              body: JSON.stringify(request)
            });
          } catch (err) {
            throw new NetworkError(err);
          }
          if (res.status !== 202)
            throw new NetworkError(res);
          complete = async () => {
            let res2;
            try {
              const control2 = new AbortControllerImpl();
              const unlisten2 = client.onDispose(() => {
                unlisten2();
                control2.abort();
              });
              res2 = await fetchFn(url + "?operationId=" + operationId, {
                signal: control2.signal,
                method: "DELETE",
                credentials,
                referrer,
                referrerPolicy,
                headers
              });
            } catch (err) {
              throw new NetworkError(err);
            }
            if (res2.status !== 200)
              throw new NetworkError(res2);
          };
          for await (const result of getResults({
            signal: control.signal,
            operationId
          })) {
            retryingErr = null;
            retries = 0;
            sink.next(result);
          }
          complete = null;
          return control.abort();
        } catch (err) {
          if (control.signal.aborted)
            return await (complete === null || complete === void 0 ? void 0 : complete());
          if (!(err instanceof NetworkError)) {
            control.abort();
            throw err;
          }
          if (lazy) {
            conn = void 0;
          }
          if (!retryAttempts || retries >= retryAttempts) {
            control.abort();
            throw err;
          }
          retryingErr = err;
        } finally {
          if (control.signal.aborted && --locks === 0) {
            if (isFinite(lazyCloseTimeout) && lazyCloseTimeout > 0) {
              setTimeout(() => {
                if (!locks)
                  connCtrl.abort();
              }, lazyCloseTimeout);
            } else {
              connCtrl.abort();
            }
          }
        }
      }
    })().then(() => sink.complete()).catch((err) => sink.error(err));
    return () => control.abort();
  }
  return {
    subscribe: subscribe2,
    iterate(request, on) {
      const pending = [];
      const deferred = {
        done: false,
        error: null,
        resolve: () => {
        }
      };
      const dispose = subscribe2(request, {
        next(val) {
          pending.push(val);
          deferred.resolve();
        },
        error(err) {
          deferred.done = true;
          deferred.error = err;
          deferred.resolve();
        },
        complete() {
          deferred.done = true;
          deferred.resolve();
        }
      }, on);
      const iterator = async function* iterator2() {
        for (; ; ) {
          if (!pending.length) {
            await new Promise((resolve) => deferred.resolve = resolve);
          }
          while (pending.length) {
            yield pending.shift();
          }
          if (deferred.error) {
            throw deferred.error;
          }
          if (deferred.done) {
            return;
          }
        }
      }();
      iterator.throw = async (err) => {
        if (!deferred.done) {
          deferred.done = true;
          deferred.error = err;
          deferred.resolve();
        }
        return { done: true, value: void 0 };
      };
      iterator.return = async () => {
        dispose();
        return { done: true, value: void 0 };
      };
      return iterator;
    },
    dispose() {
      client.dispose();
    }
  };
}
var NetworkError = class extends Error {
  constructor(msgOrErrOrResponse) {
    let message, response;
    if (isResponseLike(msgOrErrOrResponse)) {
      response = msgOrErrOrResponse;
      message = "Server responded with " + msgOrErrOrResponse.status + ": " + msgOrErrOrResponse.statusText;
    } else if (msgOrErrOrResponse instanceof Error)
      message = msgOrErrOrResponse.message;
    else
      message = String(msgOrErrOrResponse);
    super(message);
    this.name = this.constructor.name;
    this.response = response;
  }
};
function isResponseLike(val) {
  return isObject(val) && typeof val["ok"] === "boolean" && typeof val["status"] === "number" && typeof val["statusText"] === "string";
}
async function connect(options) {
  const { signal, url, credentials, headers, body, referrer, referrerPolicy, fetchFn, onMessage } = options;
  const waiting = {};
  const queue = {};
  let res;
  try {
    res = await fetchFn(url, {
      signal,
      method: body ? "POST" : "GET",
      credentials,
      referrer,
      referrerPolicy,
      headers: {
        ...headers,
        accept: "text/event-stream"
      },
      body
    });
  } catch (err) {
    throw new NetworkError(err);
  }
  if (!res.ok)
    throw new NetworkError(res);
  if (!res.body)
    throw new Error("Missing response body");
  let error = null;
  let waitingForThrow;
  (async () => {
    var _a;
    try {
      const parse2 = createParser();
      for await (const chunk of toAsyncIterable(res.body)) {
        if (typeof chunk === "string")
          throw error = new Error(`Unexpected string chunk "${chunk}"`);
        let msgs;
        try {
          msgs = parse2(chunk);
        } catch (err) {
          throw error = err;
        }
        if (!msgs)
          continue;
        for (const msg of msgs) {
          try {
            onMessage === null || onMessage === void 0 ? void 0 : onMessage(msg);
          } catch (err) {
            throw error = err;
          }
          const operationId = msg.data && "id" in msg.data ? msg.data.id : "";
          if (!(operationId in queue))
            queue[operationId] = [];
          switch (msg.event) {
            case "next":
              if (operationId)
                queue[operationId].push(msg.data.payload);
              else
                queue[operationId].push(msg.data);
              break;
            case "complete":
              queue[operationId].push("complete");
              break;
            default:
              throw error = new Error(`Unexpected message event "${msg.event}"`);
          }
          (_a = waiting[operationId]) === null || _a === void 0 ? void 0 : _a.proceed();
        }
      }
      if (Object.keys(waiting).length) {
        throw new Error("Connection closed while having active streams");
      }
    } catch (err) {
      if (!error && Object.keys(waiting).length) {
        error = new NetworkError(err);
      } else {
        error = err;
      }
      waitingForThrow === null || waitingForThrow === void 0 ? void 0 : waitingForThrow(error);
    } finally {
      Object.values(waiting).forEach(({ proceed }) => proceed());
    }
  })();
  return {
    url,
    headers,
    waitForThrow: () => new Promise((_, reject) => {
      if (error)
        return reject(error);
      waitingForThrow = reject;
    }),
    async *getResults(options2) {
      var _a;
      const { signal: signal2, operationId = "" } = options2 !== null && options2 !== void 0 ? options2 : {};
      try {
        for (; ; ) {
          while ((_a = queue[operationId]) === null || _a === void 0 ? void 0 : _a.length) {
            const result = queue[operationId].shift();
            if (result === "complete")
              return;
            yield result;
          }
          if (error)
            throw error;
          if (signal2 === null || signal2 === void 0 ? void 0 : signal2.aborted)
            throw new Error("Getting results aborted by the client");
          await new Promise((resolve) => {
            const proceed = () => {
              signal2 === null || signal2 === void 0 ? void 0 : signal2.removeEventListener("abort", proceed);
              delete waiting[operationId];
              resolve();
            };
            signal2 === null || signal2 === void 0 ? void 0 : signal2.addEventListener("abort", proceed);
            waiting[operationId] = { proceed };
          });
        }
      } finally {
        delete queue[operationId];
      }
    }
  };
}
function toAsyncIterable(val) {
  if (typeof Object(val)[Symbol.asyncIterator] === "function") {
    val = val;
    return val;
  }
  return async function* () {
    const reader = val.getReader();
    let result;
    do {
      result = await reader.read();
      if (result.value !== void 0)
        yield result.value;
    } while (!result.done);
  }();
}
export {
  NetworkError,
  TOKEN_HEADER_KEY,
  TOKEN_QUERY_KEY,
  createClient,
  createHandler,
  isAsyncGenerator,
  isAsyncIterable,
  isExecutionResult,
  parseStreamData,
  print,
  validateStreamEvent
};
//# sourceMappingURL=graphql-sse.js.map

{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/coffee/coffee.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/coffee/coffee.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".coffee\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \"and\",\n    \"or\",\n    \"is\",\n    \"isnt\",\n    \"not\",\n    \"on\",\n    \"yes\",\n    \"@\",\n    \"no\",\n    \"off\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"this\",\n    \"new\",\n    \"delete\",\n    \"typeof\",\n    \"in\",\n    \"instanceof\",\n    \"return\",\n    \"throw\",\n    \"break\",\n    \"continue\",\n    \"debugger\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"for\",\n    \"while\",\n    \"do\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"class\",\n    \"extends\",\n    \"super\",\n    \"undefined\",\n    \"then\",\n    \"unless\",\n    \"until\",\n    \"loop\",\n    \"of\",\n    \"by\",\n    \"when\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?&%|+\\-*\\/\\^\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\@[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/###/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\()(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\=)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\[)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\!)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\&)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\|)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\?)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\{)(\\s*)(@regEx)/, [\"@brackets\", \"\", \"regexp\"]],\n      [/(\\;)(\\s*)(@regEx)/, [\"\", \"\", \"regexp\"]],\n      // delimiters\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/###/, \"comment\", \"@pop\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAgCA;AAzCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,OAAO,KAAK;AAAA,QAC3B,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,UACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MAC1D;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA,MACT,SAAS;AAAA;AAAA,MAET,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,kBAAkB,qBAAqB;AAAA,UACxC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,MAAM;AAAA,gBACN,aAAa,EAAE,OAAO,aAAa;AAAA,gBACnC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,cAAc,EAAE;AAAA;AAAA,UAEjB,CAAC,OAAO,WAAW,UAAU;AAAA,UAC7B,CAAC,QAAQ,SAAS;AAAA;AAAA,UAElB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,cAAc,CAAC;AAAA,UAChD,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC;AAAA,UACjC,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,UACjD,CAAC,qBAAqB,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA;AAAA,UAExC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,2BAA2B;AAAA,kBACzB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,UAExB,CAAC,uBAAuB,cAAc;AAAA,UACtC,CAAC,4BAA4B,cAAc;AAAA,UAC3C,CAAC,qBAAqB,YAAY;AAAA,UAClC,CAAC,iBAAiB,cAAc;AAAA,UAChC,CAAC,OAAO,QAAQ;AAAA;AAAA,UAEhB,CAAC,QAAQ,WAAW;AAAA;AAAA,UAEpB,CAAC,OAAO,UAAU,iBAAiB;AAAA,UACnC,CAAC,OAAO,UAAU,iBAAiB;AAAA,UACnC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ;AAAA,gBACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ;AAAA,gBACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,cAAc,QAAQ;AAAA,UACvB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,MAAM,uBAAuB;AAAA,UAC9B,CAAC,MAAM,uBAAuB;AAAA,UAC9B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,UAAU;AAAA,kBACR,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,YAAY;AAAA,UACV;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,MAAM,uBAAuB;AAAA,UAC9B,CAAC,MAAM,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,UACjE,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,SAAS,SAAS;AAAA,UACnB,CAAC,OAAO,WAAW,MAAM;AAAA,UACzB,CAAC,KAAK,SAAS;AAAA,QACjB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,QAAQ,SAAS;AAAA,UAClB,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UAC/C,CAAC,MAAM,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../graphql-tag/src/index.ts"], "sourcesContent": ["import { parse } from 'graphql';\n\nimport {\n  DocumentNode,\n  DefinitionNode,\n  Location,\n} from 'graphql/language/ast';\n\n// A map docString -> graphql document\nconst docCache = new Map<string, DocumentNode>();\n\n// A map fragmentName -> [normalized source]\nconst fragmentSourceMap = new Map<string, Set<string>>();\n\nlet printFragmentWarnings = true;\nlet experimentalFragmentVariables = false;\n\n// Strip insignificant whitespace\n// Note that this could do a lot more, such as reorder fields etc.\nfunction normalize(string: string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\n\nfunction cacheKeyFromLoc(loc: Location) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\n\n// Take a unstripped parsed document (query/mutation or even fragment), and\n// check all fragment definitions, checking for name->source uniqueness.\n// We also want to make sure only unique fragments exist in the document.\nfunction processFragments(ast: DocumentNode) {\n  const seenKeys = new Set<string>();\n  const definitions: DefinitionNode[] = [];\n\n  ast.definitions.forEach(fragmentDefinition => {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc!);\n\n      // We know something about this fragment\n      let sourceKeySet = fragmentSourceMap.get(fragmentName)!;\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        // this is a problem because the app developer is trying to register another fragment with\n        // the same name as one previously registered. So, we tell them about it.\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n            + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n            + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n      }\n\n      sourceKeySet.add(sourceKey);\n\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n\n  return {\n    ...ast,\n    definitions,\n  };\n}\n\nfunction stripLoc(doc: DocumentNode) {\n  const workSet = new Set<Record<string, any>>(doc.definitions);\n\n  workSet.forEach(node => {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(key => {\n      const value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n\n  const loc = doc.loc as Record<string, any>;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n\n  return doc;\n}\n\nfunction parseDocument(source: string) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    const parsed = parse(source, {\n      experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables,\n    } as any);\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(\n      cacheKey,\n      // check that all \"new\" fragments inside the documents are consistent with\n      // existing fragments of the same name\n      stripLoc(processFragments(parsed)),\n    );\n  }\n  return docCache.get(cacheKey)!;\n}\n\n// XXX This should eventually disallow arbitrary string interpolation, like Relay does\nexport function gql(\n  literals: string | readonly string[],\n  ...args: any[]\n) {\n\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n\n  let result = literals[0];\n\n  args.forEach((arg, i) => {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n\n  return parseDocument(result);\n}\n\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\n\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\n\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\n\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\n\nconst extras = {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n};\n\nexport namespace gql {\n  export const {\n    gql,\n    resetCaches,\n    disableFragmentWarnings,\n    enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables,\n  } = extras;\n}\n\ngql.default = gql;\n\nexport default gql;\n"], "mappings": ";;;;;;;;;;;;;AAmBA,SAAS,UAAU,QAAc;AAC/B,SAAO,OAAO,QAAQ,WAAW,GAAG,EAAE,KAAI;AAC5C;AAEA,SAAS,gBAAgB,KAAa;AACpC,SAAO,UAAU,IAAI,OAAO,KAAK,UAAU,IAAI,OAAO,IAAI,GAAG,CAAC;AAChE;AAKA,SAAS,iBAAiB,KAAiB;AACzC,MAAM,WAAW,oBAAI,IAAG;AACxB,MAAM,cAAgC,CAAA;AAEtC,MAAI,YAAY,QAAQ,SAAA,oBAAkB;AACxC,QAAI,mBAAmB,SAAS,sBAAsB;AACpD,UAAI,eAAe,mBAAmB,KAAK;AAC3C,UAAI,YAAY,gBAAgB,mBAAmB,GAAI;AAGvD,UAAI,eAAe,kBAAkB,IAAI,YAAY;AACrD,UAAI,gBAAgB,CAAC,aAAa,IAAI,SAAS,GAAG;AAGhD,YAAI,uBAAuB;AACzB,kBAAQ,KAAK,iCAAiC,eAAe,+LAEqB;;iBAE3E,CAAC,cAAc;AACxB,0BAAkB,IAAI,cAAc,eAAe,oBAAI,KAAG;;AAG5D,mBAAa,IAAI,SAAS;AAE1B,UAAI,CAAC,SAAS,IAAI,SAAS,GAAG;AAC5B,iBAAS,IAAI,SAAS;AACtB,oBAAY,KAAK,kBAAkB;;WAEhC;AACL,kBAAY,KAAK,kBAAkB;;EAEvC,CAAC;AAED,SAAA,SAAA,SAAA,CAAA,GACK,GAAG,GAAA,EACN,YAAW,CAAA;AAEf;AAEA,SAAS,SAAS,KAAiB;AACjC,MAAM,UAAU,IAAI,IAAyB,IAAI,WAAW;AAE5D,UAAQ,QAAQ,SAAA,MAAI;AAClB,QAAI,KAAK;AAAK,aAAO,KAAK;AAC1B,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAA,KAAG;AAC3B,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,IAAI,KAAK;;IAErB,CAAC;EACH,CAAC;AAED,MAAM,MAAM,IAAI;AAChB,MAAI,KAAK;AACP,WAAO,IAAI;AACX,WAAO,IAAI;;AAGb,SAAO;AACT;AAEA,SAAS,cAAc,QAAc;AACnC,MAAI,WAAW,UAAU,MAAM;AAC/B,MAAI,CAAC,SAAS,IAAI,QAAQ,GAAG;AAC3B,QAAM,SAAS,MAAM,QAAQ;MAC3B;MACA,8BAA8B;KACxB;AACR,QAAI,CAAC,UAAU,OAAO,SAAS,YAAY;AACzC,YAAM,IAAI,MAAM,+BAA+B;;AAEjD,aAAS,IACP,UAGA,SAAS,iBAAiB,MAAM,CAAC,CAAC;;AAGtC,SAAO,SAAS,IAAI,QAAQ;AAC9B;AAGM,SAAU,IACd,UAAoC;AACpC,MAAA,OAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,SAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAGA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,CAAC,QAAQ;;AAGtB,MAAI,SAAS,SAAS,CAAC;AAEvB,OAAK,QAAQ,SAAC,KAAK,GAAC;AAClB,QAAI,OAAO,IAAI,SAAS,YAAY;AAClC,gBAAU,IAAI,IAAI,OAAO;WACpB;AACL,gBAAU;;AAEZ,cAAU,SAAS,IAAI,CAAC;EAC1B,CAAC;AAED,SAAO,cAAc,MAAM;AAC7B;AAEM,SAAU,cAAW;AACzB,WAAS,MAAK;AACd,oBAAkB,MAAK;AACzB;AAEM,SAAU,0BAAuB;AACrC,0BAAwB;AAC1B;AAEM,SAAU,sCAAmC;AACjD,kCAAgC;AAClC;AAEM,SAAU,uCAAoC;AAClD,kCAAgC;AAClC;IA9IM,UAGA,mBAEF,uBACA,+BA0IE,QAoBN;;;;AA7KA;AASA,IAAM,WAAW,oBAAI,IAAG;AAGxB,IAAM,oBAAoB,oBAAI,IAAG;AAEjC,IAAI,wBAAwB;AAC5B,IAAI,gCAAgC;AA0IpC,IAAM,SAAS;MACb;MACA;MACA;MACA;MACA;;AAGF,KAAA,SAAiB,OAAG;AAEhB,YAAA,MAKE,OAAM,KAJR,MAAA,cAIE,OAAM,aAHR,MAAA,0BAGE,OAAM,yBAFR,MAAA,sCAEE,OAAM,qCADR,MAAA,uCACE,OAAM;IACZ,GARiB,QAAA,MAAG,CAAA,EAAA;AAUpB,QAAI,SAAO,IAAG;AAEd,IAAA,cAAe;;;", "names": []}
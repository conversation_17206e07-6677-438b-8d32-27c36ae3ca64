{"version": 3, "sources": ["../../../../lodash.identity/index.js"], "sourcesContent": ["/**\n * lodash 3.0.0 (Custom Build) <https://lodash.com/>\n * Build: `lodash modern modularize exports=\"npm\" -o ./`\n * Copyright 2012-2015 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.7.0 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2015 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/**\n * This method returns the first argument provided to it.\n *\n * @static\n * @memberOf _\n * @category Utility\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'user': 'fred' };\n * _.identity(object) === object;\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\nmodule.exports = identity;\n"], "mappings": ";;;;;AAAA;AAAA;AAuBA,aAAS,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/DataTable/DataTable.vue.js", "../../../../@scalar/api-client/dist/components/DataTable/DataTableRow.vue.js", "../../../../@scalar/api-client/dist/components/DataTable/DataTableCell.vue.js", "../../../../@scalar/api-client/dist/components/DataTable/DataTableInput.vue2.js", "../../../../@scalar/api-client/dist/components/DataTable/DataTableInput.vue.js"], "sourcesContent": ["import { defineComponent as t, openBlock as e, createElementBlock as l, normalizeProps as n, guardReactiveProps as m, unref as c, createElementVNode as i, normalizeClass as p, normalizeStyle as u, renderSlot as s, createCommentVNode as d } from \"vue\";\nimport { useBindCx as f } from \"@scalar/components\";\nconst b = {\n  key: 0,\n  class: \"sr-only\"\n}, g = /* @__PURE__ */ t({\n  __name: \"DataTable\",\n  props: {\n    columns: {},\n    scroll: { type: Boolean }\n  },\n  setup(v) {\n    const { cx: a } = f();\n    return (o, w) => (e(), l(\"div\", n(m(\n      c(a)(\n        o.scroll ? \"overflow-x-auto custom-scroll\" : \"overflow-visible\",\n        \"scalar-data-table bg-b-1\"\n      )\n    )), [\n      i(\"table\", {\n        class: p([\"mb-0 grid min-h-8 auto-rows-auto\", { \"w-max min-w-full\": o.scroll }]),\n        style: u({\n          gridTemplateColumns: o.columns.map((r) => r || \"1fr\").join(\" \")\n        })\n      }, [\n        o.$slots.caption ? (e(), l(\"caption\", b, [\n          s(o.$slots, \"caption\")\n        ])) : d(\"\", !0),\n        s(o.$slots, \"default\")\n      ], 6)\n    ], 16));\n  }\n});\nexport {\n  g as default\n};\n", "import { openBlock as e, createElementBlock as o, renderSlot as r } from \"vue\";\nimport n from \"../../_virtual/_plugin-vue_export-helper.js\";\nconst c = {}, s = { class: \"group contents w-fit min-w-full\" };\nfunction l(t, a) {\n  return e(), o(\"tr\", s, [\n    r(t.$slots, \"default\")\n  ]);\n}\nconst i = /* @__PURE__ */ n(c, [[\"render\", l]]);\nexport {\n  i as default\n};\n", "import { defineComponent as t, openBlock as o, createBlock as a, resolveDynamicComponent as l, mergeProps as n, unref as s, withCtx as p, renderSlot as d } from \"vue\";\nimport { useBindCx as m } from \"@scalar/components\";\nconst f = /* @__PURE__ */ t({\n  inheritAttrs: !1,\n  __name: \"DataTableCell\",\n  props: {\n    is: { default: \"td\" }\n  },\n  setup(b) {\n    const { cx: r } = m();\n    return (e, i) => (o(), a(l(e.is), n(\n      s(r)(\n        \"box-content max-h-8 min-h-8 min-w-8 border-l-0 border-t border-b-0 border-r flex text-sm last:border-r-0 group-last:border-b-transparent p-0 m-0 relative\"\n      ),\n      {\n        class: \"group-[.alert]:bg-b-alert group-[.error]:bg-b-danger\",\n        role: \"cell\"\n      }\n    ), {\n      default: p(() => [\n        d(e.$slots, \"default\")\n      ]),\n      _: 3\n    }, 16));\n  }\n});\nexport {\n  f as default\n};\n", "import { defineComponent as b, ref as i, computed as C, openBlock as t, createBlock as u, normalizeClass as g, withCtx as B, createElementBlock as s, renderSlot as p, createTextVNode as $, createCommentVNode as d, createElementVNode as h, Fragment as O, mergeProps as c, unref as q } from \"vue\";\nimport { ScalarIconButton as I } from \"@scalar/components\";\nimport A from \"../CodeInput/CodeInput.vue.js\";\nimport E from \"./DataTableCell.vue.js\";\nimport F from \"./DataTableInputSelect.vue.js\";\nconst S = [\"for\"], T = { class: \"row-1 overflow-x-auto\" }, N = [\"readOnly\", \"type\", \"value\"], P = {\n  key: 2,\n  class: \"scalar-input-required centered-y text-xxs text-c-3 bg-b-1 absolute right-2 pt-px opacity-100 shadow-[-8px_0_4px_var(--scalar-background-1)] transition-opacity duration-150 peer-has-[:focus-visible]:opacity-0\"\n}, x = {\n  key: 1,\n  class: \"centered-y text-orange absolute right-7 text-xs\"\n}, R = /* @__PURE__ */ b({\n  inheritAttrs: !1,\n  __name: \"DataTableInput\",\n  props: {\n    id: {},\n    type: {},\n    containerClass: {},\n    required: { type: Boolean, default: !1 },\n    modelValue: {},\n    canAddCustomEnumValue: { type: Boolean, default: !0 },\n    readOnly: { type: Boolean, default: !1 },\n    enum: {},\n    min: {},\n    max: {},\n    environment: {},\n    envVariables: {},\n    workspace: {}\n  },\n  emits: [\"update:modelValue\", \"inputFocus\", \"inputBlur\", \"selectVariable\"],\n  setup(v, { emit: y }) {\n    const a = v, r = y, n = i(!0), V = i(!1), m = i(null), w = () => {\n      V.value || r(\"inputBlur\");\n    }, f = C(\n      () => a.type === \"password\" ? n.value ? \"password\" : \"text\" : a.type ?? \"text\"\n    ), k = () => {\n      var e, l;\n      !((e = a.enum) != null && e.length) && !a.readOnly && ((l = m.value) == null || l.focus());\n    };\n    return (e, l) => (t(), u(E, {\n      class: g([\"row relative\", e.containerClass])\n    }, {\n      default: B(() => [\n        e.$slots.default ? (t(), s(\"div\", {\n          key: 0,\n          class: \"text-c-1 flex items-center pl-3 pr-0\",\n          for: e.id ?? \"\",\n          onClick: k\n        }, [\n          p(e.$slots, \"default\", {}, void 0, !0),\n          l[5] || (l[5] = $(\": \"))\n        ], 8, S)) : d(\"\", !0),\n        h(\"div\", T, [\n          a.enum && a.enum.length ? (t(), u(F, {\n            key: 0,\n            canAddCustomValue: a.canAddCustomEnumValue,\n            modelValue: a.modelValue,\n            value: a.enum,\n            \"onUpdate:modelValue\": l[0] || (l[0] = (o) => r(\"update:modelValue\", o))\n          }, null, 8, [\"canAddCustomValue\", \"modelValue\", \"value\"])) : (t(), s(O, { key: 1 }, [\n            n.value && e.type === \"password\" ? (t(), s(\"input\", c({ key: 0 }, e.id ? { ...e.$attrs, id: e.id } : e.$attrs, {\n              autocomplete: \"off\",\n              class: \"text-c-1 disabled:text-c-2 py-1.25 peer w-full min-w-0 border-none px-2 -outline-offset-2\",\n              \"data-1p-ignore\": \"\",\n              readOnly: e.readOnly,\n              spellcheck: \"false\",\n              type: f.value,\n              value: e.modelValue,\n              onInput: l[1] || (l[1] = (o) => r(\n                \"update:modelValue\",\n                o.target.value ?? \"\"\n              ))\n            }), null, 16, N)) : (t(), u(A, c({\n              key: 1,\n              ref_key: \"codeInput\",\n              ref: m\n            }, e.$attrs, {\n              id: e.id,\n              class: \"text-c-1 disabled:text-c-2 peer w-full min-w-0 border-none\",\n              disableCloseBrackets: \"\",\n              disableTabIndent: \"\",\n              envVariables: e.envVariables,\n              environment: e.environment,\n              max: e.max,\n              min: e.min,\n              modelValue: e.modelValue ?? \"\",\n              readOnly: e.readOnly,\n              required: !!e.required,\n              spellcheck: \"false\",\n              type: f.value,\n              workspace: e.workspace,\n              onBlur: w,\n              onFocus: l[2] || (l[2] = (o) => r(\"inputFocus\")),\n              \"onUpdate:modelValue\": l[3] || (l[3] = (o) => r(\"update:modelValue\", o))\n            }), null, 16, [\"id\", \"envVariables\", \"environment\", \"max\", \"min\", \"modelValue\", \"readOnly\", \"required\", \"type\", \"workspace\"])),\n            e.required ? (t(), s(\"div\", P, \" Required \")) : d(\"\", !0)\n          ], 64))\n        ]),\n        e.$slots.warning ? (t(), s(\"div\", x, [\n          p(e.$slots, \"warning\", {}, void 0, !0)\n        ])) : d(\"\", !0),\n        p(e.$slots, \"icon\", {}, void 0, !0),\n        e.type === \"password\" ? (t(), u(q(I), {\n          key: 2,\n          class: \"-ml-.5 mr-0.75 h-6 w-6 self-center p-1.5\",\n          icon: n.value ? \"Show\" : \"Hide\",\n          label: n.value ? \"Show Password\" : \"Hide Password\",\n          onClick: l[4] || (l[4] = (o) => n.value = !n.value)\n        }, null, 8, [\"icon\", \"label\"])) : d(\"\", !0)\n      ]),\n      _: 3\n    }, 8, [\"class\"]));\n  }\n});\nexport {\n  R as default\n};\n", "import t from \"./DataTableInput.vue2.js\";\n/* empty css                    */\nimport a from \"../../_virtual/_plugin-vue_export-helper.js\";\nconst m = /* @__PURE__ */ a(t, [[\"__scopeId\", \"data-v-921a6c09\"]]);\nexport {\n  m as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAI;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,SAAS,CAAC;AAAA,IACV,QAAQ,EAAE,MAAM,QAAQ;AAAA,EAC1B;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAE,IAAI,EAAE,IAAI,EAAE;AACpB,WAAO,CAAC,GAAGA,QAAO,UAAE,GAAG,mBAAE,OAAO,eAAE;AAAA,MAChC,MAAE,CAAC;AAAA,QACD,EAAE,SAAS,kCAAkC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF,CAAC,GAAG;AAAA,MACF,gBAAE,SAAS;AAAA,QACT,OAAO,eAAE,CAAC,oCAAoC,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;AAAA,QAC/E,OAAO,eAAE;AAAA,UACP,qBAAqB,EAAE,QAAQ,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;AAAA,QAChE,CAAC;AAAA,MACH,GAAG;AAAA,QACD,EAAE,OAAO,WAAW,UAAE,GAAG,mBAAE,WAAW,GAAG;AAAA,UACvC,WAAE,EAAE,QAAQ,SAAS;AAAA,QACvB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACd,WAAE,EAAE,QAAQ,SAAS;AAAA,MACvB,GAAG,CAAC;AAAA,IACN,GAAG,EAAE;AAAA,EACP;AACF,CAAC;;;AC9BD,IAAM,IAAI,CAAC;AAAX,IAAcC,KAAI,EAAE,OAAO,kCAAkC;AAC7D,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,UAAE,GAAG,mBAAE,MAAMA,IAAG;AAAA,IACrB,WAAE,EAAE,QAAQ,SAAS;AAAA,EACvB,CAAC;AACH;AACA,IAAMC,KAAoB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;;;ACN9C,IAAM,IAAoB,gBAAE;AAAA,EAC1B,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,IAAI,EAAE,SAAS,KAAK;AAAA,EACtB;AAAA,EACA,MAAMC,IAAG;AACP,UAAM,EAAE,IAAI,EAAE,IAAI,EAAE;AACpB,WAAO,CAAC,GAAGC,QAAO,UAAE,GAAG,YAAE,wBAAE,EAAE,EAAE,GAAG;AAAA,MAChC,MAAE,CAAC;AAAA,QACD;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,WAAE,EAAE,QAAQ,SAAS;AAAA,MACvB,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,EAAE;AAAA,EACP;AACF,CAAC;;;ACpBD,IAAM,IAAI,CAAC,KAAK;AAAhB,IAAmB,IAAI,EAAE,OAAO,wBAAwB;AAAxD,IAA2D,IAAI,CAAC,YAAY,QAAQ,OAAO;AAA3F,IAA8F,IAAI;AAAA,EAChG,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMG,IAAoB,gBAAE;AAAA,EACvB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,IAAI,CAAC;AAAA,IACL,MAAM,CAAC;AAAA,IACP,gBAAgB,CAAC;AAAA,IACjB,UAAU,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA,IACvC,YAAY,CAAC;AAAA,IACb,uBAAuB,EAAE,MAAM,SAAS,SAAS,KAAG;AAAA,IACpD,UAAU,EAAE,MAAM,SAAS,SAAS,MAAG;AAAA,IACvC,MAAM,CAAC;AAAA,IACP,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,WAAW,CAAC;AAAA,EACd;AAAA,EACA,OAAO,CAAC,qBAAqB,cAAc,aAAa,gBAAgB;AAAA,EACxE,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,GAAG,IAAI,IAAE,IAAE,GAAG,IAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,IAAI,GAAGC,KAAI,MAAM;AAC/D,QAAE,SAAS,EAAE,WAAW;AAAA,IAC1B,GAAGC,KAAI;AAAA,MACL,MAAM,EAAE,SAAS,aAAa,EAAE,QAAQ,aAAa,SAAS,EAAE,QAAQ;AAAA,IAC1E,GAAG,IAAI,MAAM;AACX,UAAI,GAAGC;AACP,SAAG,IAAI,EAAE,SAAS,QAAQ,EAAE,WAAW,CAAC,EAAE,cAAcA,KAAIH,GAAE,UAAU,QAAQG,GAAE,MAAM;AAAA,IAC1F;AACA,WAAO,CAAC,GAAGA,QAAO,UAAE,GAAG,YAAE,GAAG;AAAA,MAC1B,OAAO,eAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC;AAAA,IAC7C,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,EAAE,OAAO,WAAW,UAAE,GAAG,mBAAE,OAAO;AAAA,UAChC,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK,EAAE,MAAM;AAAA,UACb,SAAS;AAAA,QACX,GAAG;AAAA,UACD,WAAE,EAAE,QAAQ,WAAW,CAAC,GAAG,QAAQ,IAAE;AAAA,UACrCA,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,gBAAE,IAAI;AAAA,QACxB,GAAG,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACpB,gBAAE,OAAO,GAAG;AAAA,UACV,EAAE,QAAQ,EAAE,KAAK,UAAU,UAAE,GAAG,YAAE,GAAG;AAAA,YACnC,KAAK;AAAA,YACL,mBAAmB,EAAE;AAAA,YACrB,YAAY,EAAE;AAAA,YACd,OAAO,EAAE;AAAA,YACT,uBAAuBA,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAM,EAAE,qBAAqB,CAAC;AAAA,UACxE,GAAG,MAAM,GAAG,CAAC,qBAAqB,cAAc,OAAO,CAAC,MAAM,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,YAClF,EAAE,SAAS,EAAE,SAAS,cAAc,UAAE,GAAG,mBAAE,SAAS,WAAE,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,IAAI,EAAE,GAAG,IAAI,EAAE,QAAQ;AAAA,cAC7G,cAAc;AAAA,cACd,OAAO;AAAA,cACP,kBAAkB;AAAA,cAClB,UAAU,EAAE;AAAA,cACZ,YAAY;AAAA,cACZ,MAAMD,GAAE;AAAA,cACR,OAAO,EAAE;AAAA,cACT,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAM;AAAA,gBAC9B;AAAA,gBACA,EAAE,OAAO,SAAS;AAAA,cACpB;AAAA,YACF,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,UAAE,GAAG,YAAE,GAAG,WAAE;AAAA,cAC/B,KAAK;AAAA,cACL,SAAS;AAAA,cACT,KAAKH;AAAA,YACP,GAAG,EAAE,QAAQ;AAAA,cACX,IAAI,EAAE;AAAA,cACN,OAAO;AAAA,cACP,sBAAsB;AAAA,cACtB,kBAAkB;AAAA,cAClB,cAAc,EAAE;AAAA,cAChB,aAAa,EAAE;AAAA,cACf,KAAK,EAAE;AAAA,cACP,KAAK,EAAE;AAAA,cACP,YAAY,EAAE,cAAc;AAAA,cAC5B,UAAU,EAAE;AAAA,cACZ,UAAU,CAAC,CAAC,EAAE;AAAA,cACd,YAAY;AAAA,cACZ,MAAME,GAAE;AAAA,cACR,WAAW,EAAE;AAAA,cACb,QAAQD;AAAA,cACR,SAASE,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY;AAAA,cAC9C,uBAAuBA,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAM,EAAE,qBAAqB,CAAC;AAAA,YACxE,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,gBAAgB,eAAe,OAAO,OAAO,cAAc,YAAY,YAAY,QAAQ,WAAW,CAAC;AAAA,YAC5H,EAAE,YAAY,UAAE,GAAG,mBAAE,OAAO,GAAG,YAAY,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC1D,GAAG,EAAE;AAAA,QACP,CAAC;AAAA,QACD,EAAE,OAAO,WAAW,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,UACnC,WAAE,EAAE,QAAQ,WAAW,CAAC,GAAG,QAAQ,IAAE;AAAA,QACvC,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACd,WAAE,EAAE,QAAQ,QAAQ,CAAC,GAAG,QAAQ,IAAE;AAAA,QAClC,EAAE,SAAS,cAAc,UAAE,GAAG,YAAE,MAAE,CAAC,GAAG;AAAA,UACpC,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM,EAAE,QAAQ,SAAS;AAAA,UACzB,OAAO,EAAE,QAAQ,kBAAkB;AAAA,UACnC,SAASA,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;AAAA,QAC/C,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAC5C,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,OAAO,CAAC;AAAA,EACjB;AACF,CAAC;;;AC9GD,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;", "names": ["w", "s", "i", "b", "i", "m", "w", "f", "l"]}
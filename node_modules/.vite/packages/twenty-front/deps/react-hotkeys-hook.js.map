{"version": 3, "sources": ["../../../../react-hotkeys-hook/src/parseHotkeys.ts", "../../../../react-hotkeys-hook/src/isHotkeyPressed.ts", "../../../../react-hotkeys-hook/src/validators.ts", "../../../../react-hotkeys-hook/src/BoundHotkeysProxyProvider.tsx", "../../../../react-hotkeys-hook/src/deepEqual.ts", "../../../../react-hotkeys-hook/src/HotkeysProvider.tsx", "../../../../react-hotkeys-hook/src/useDeepEqualMemo.ts", "../../../../react-hotkeys-hook/src/useHotkeys.ts", "../../../../react-hotkeys-hook/src/useRecordHotkeys.ts"], "sourcesContent": ["import { Hotkey, KeyboardModifiers } from './types'\n\nconst reservedModifierKeywords = ['shift', 'alt', 'meta', 'mod', 'ctrl']\n\nconst mappedKeys: Record<string, string> = {\n  esc: 'escape',\n  return: 'enter',\n  '.': 'period',\n  ',': 'comma',\n  '-': 'slash',\n  ' ': 'space',\n  '`': 'backquote',\n  '#': 'backslash',\n  '+': 'bracketright',\n  ShiftLeft: 'shift',\n  ShiftRight: 'shift',\n  AltLeft: 'alt',\n  AltRight: 'alt',\n  MetaLeft: 'meta',\n  MetaRight: 'meta',\n  OSLeft: 'meta',\n  OSRight: 'meta',\n  ControlLeft: 'ctrl',\n  ControlRight: 'ctrl',\n}\n\nexport function mapKey(key: string): string {\n  return (mappedKeys[key] || key)\n    .trim()\n    .toLowerCase()\n    .replace(/key|digit|numpad|arrow/, '')\n}\n\nexport function isHotkeyModifier(key: string) {\n  return reservedModifierKeywords.includes(key)\n}\n\nexport function parseKeysHookInput(keys: string, splitKey = ','): string[] {\n  return keys.split(splitKey)\n}\n\nexport function parseHotkey(hotkey: string, combinationKey = '+', description?: string): Hotkey {\n  const keys = hotkey\n    .toLocaleLowerCase()\n    .split(combinationKey)\n    .map((k) => mapKey(k))\n\n  const modifiers: KeyboardModifiers = {\n    alt: keys.includes('alt'),\n    ctrl: keys.includes('ctrl') || keys.includes('control'),\n    shift: keys.includes('shift'),\n    meta: keys.includes('meta'),\n    mod: keys.includes('mod'),\n  }\n\n  const singleCharKeys = keys.filter((k) => !reservedModifierKeywords.includes(k))\n\n  return {\n    ...modifiers,\n    keys: singleCharKeys,\n    description,\n  }\n}\n", "import { isHotkeyModifier, mapKey } from './parseHotkeys'\n;(() => {\n  if (typeof document !== 'undefined') {\n    document.addEventListener('keydown', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n\n    document.addEventListener('keyup', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n  }\n\n  if (typeof window !== 'undefined') {\n    window.addEventListener('blur', () => {\n      currentlyPressedKeys.clear()\n    })\n  }\n})()\n\nconst currentlyPressedKeys: Set<string> = new Set<string>()\n\n// https://github.com/microsoft/TypeScript/issues/17002\nexport function isReadonlyArray(value: unknown): value is readonly unknown[] {\n  return Array.isArray(value)\n}\n\nexport function isHotkeyPressed(key: string | readonly string[], splitKey = ','): boolean {\n  const hotkeyArray = isReadonlyArray(key) ? key : key.split(splitKey)\n\n  return hotkeyArray.every((hotkey) => currentlyPressedKeys.has(hotkey.trim().toLowerCase()))\n}\n\nexport function pushToCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (currentlyPressedKeys.has('meta')) {\n    currentlyPressedKeys.forEach((key) => !isHotkeyModifier(key) && currentlyPressedKeys.delete(key.toLowerCase()))\n  }\n\n  hotkeyArray.forEach((hotkey) => currentlyPressedKeys.add(hotkey.toLowerCase()))\n}\n\nexport function removeFromCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (key === 'meta') {\n    currentlyPressedKeys.clear()\n  } else {\n    hotkeyArray.forEach((hotkey) => currentlyPressedKeys.delete(hotkey.toLowerCase()))\n  }\n}\n", "import { FormT<PERSON><PERSON>, Hotkey, Scopes, Trigger } from './types'\nimport { isHotkeyPressed, isReadonlyArray } from './isHotkeyPressed'\nimport { mapKey } from './parseHotkeys'\n\nexport function maybePreventDefault(e: KeyboardEvent, hotkey: Hotkey, preventDefault?: Trigger): void {\n  if ((typeof preventDefault === 'function' && preventDefault(e, hotkey)) || preventDefault === true) {\n    e.preventDefault()\n  }\n}\n\nexport function isHotkeyEnabled(e: KeyboardEvent, hotkey: Hotkey, enabled?: Trigger): boolean {\n  if (typeof enabled === 'function') {\n    return enabled(e, hotkey)\n  }\n\n  return enabled === true || enabled === undefined\n}\n\nexport function isKeyboardEventTriggeredByInput(ev: KeyboardEvent): boolean {\n  return isHotkeyEnabledOnTag(ev, ['input', 'textarea', 'select'])\n}\n\nexport function isHotkeyEnabledOnTag(\n  { target }: KeyboardEvent,\n  enabledOnTags: readonly FormTags[] | boolean = false\n): boolean {\n  const targetTagName = target && (target as HTMLElement).tagName\n\n  if (isReadonlyArray(enabledOnTags)) {\n    return Boolean(\n      targetTagName && enabledOnTags && enabledOnTags.some((tag) => tag.toLowerCase() === targetTagName.toLowerCase())\n    )\n  }\n\n  return Boolean(targetTagName && enabledOnTags && enabledOnTags === true)\n}\n\nexport function isScopeActive(activeScopes: string[], scopes?: Scopes): boolean {\n  if (activeScopes.length === 0 && scopes) {\n    console.warn(\n      'A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'\n    )\n\n    return true\n  }\n\n  if (!scopes) {\n    return true\n  }\n\n  return activeScopes.some((scope) => scopes.includes(scope)) || activeScopes.includes('*')\n}\n\nexport const isHotkeyMatchingKeyboardEvent = (e: KeyboardEvent, hotkey: Hotkey, ignoreModifiers = false): boolean => {\n  const { alt, meta, mod, shift, ctrl, keys } = hotkey\n  const { key: pressedKeyUppercase, code, ctrlKey, metaKey, shiftKey, altKey } = e\n\n  const keyCode = mapKey(code)\n  const pressedKey = pressedKeyUppercase.toLowerCase()\n\n  if (!keys?.includes(keyCode) && !keys?.includes(pressedKey) && !['ctrl', 'control', 'unknown', 'meta', 'alt', 'shift', 'os'].includes(keyCode)) {\n    return false\n  }\n\n  if (!ignoreModifiers) {\n    // We check the pressed keys for compatibility with the keyup event. In keyup events the modifier flags are not set.\n    if (alt === !altKey && pressedKey !== 'alt') {\n      return false\n    }\n\n    if (shift === !shiftKey && pressedKey !== 'shift') {\n      return false\n    }\n\n    // Mod is a special key name that is checking for meta on macOS and ctrl on other platforms\n    if (mod) {\n      if (!metaKey && !ctrlKey) {\n        return false\n      }\n    } else {\n      if (meta === !metaKey && pressedKey !== 'meta' && pressedKey !== 'os') {\n        return false\n      }\n\n      if (ctrl === !ctrlKey && pressedKey !== 'ctrl' && pressedKey !== 'control') {\n        return false\n      }\n    }\n  }\n\n  // All modifiers are correct, now check the key\n  // If the key is set, we check for the key\n  if (keys && keys.length === 1 && (keys.includes(pressedKey) || keys.includes(keyCode))) {\n    return true\n  } else if (keys) {\n    // Check if all keys are present in pressedDownKeys set\n    return isHotkeyPressed(keys)\n  } else if (!keys) {\n    // If the key is not set, we only listen for modifiers, that check went alright, so we return true\n    return true\n  }\n\n  // There is nothing that matches.\n  return false\n}\n", "import { createContext, ReactNode, useContext } from 'react'\nimport { Hotkey } from './types'\n\ntype BoundHotkeysProxyProviderType = {\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nconst BoundHotkeysProxyProvider = createContext<BoundHotkeysProxyProviderType | undefined>(undefined)\n\nexport const useBoundHotkeysProxy = () => {\n  return useContext(BoundHotkeysProxyProvider)\n}\n\ninterface Props {\n  children: ReactNode\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nexport default function BoundHotkeysProxyProviderProvider({ addHotkey, removeHotkey, children }: Props) {\n  return (\n    <BoundHotkeysProxyProvider.Provider value={{ addHotkey, removeHotkey }}>\n      {children}\n    </BoundHotkeysProxyProvider.Provider>\n  )\n}\n", "export default function deepEqual(x: any, y: any): boolean {\n  //@ts-ignore\n  return x && y && typeof x === 'object' && typeof y === 'object'\n    ? Object.keys(x).length === Object.keys(y).length &&\n        //@ts-ignore\n        Object.keys(x).reduce((isEqual, key) => isEqual && deepEqual(x[key], y[key]), true)\n    : x === y\n}\n", "import { Hotkey } from './types'\nimport { createContext, ReactNode, useState, useContext, useCallback } from 'react'\nimport BoundHotkeysProxyProviderProvider from './BoundHotkeysProxyProvider'\nimport deepEqual from './deepEqual'\n\nexport type HotkeysContextType = {\n  hotkeys: ReadonlyArray<Hotkey>\n  enabledScopes: string[]\n  toggleScope: (scope: string) => void\n  enableScope: (scope: string) => void\n  disableScope: (scope: string) => void\n}\n\n// The context is only needed for special features like global scoping, so we use a graceful default fallback\nconst HotkeysContext = createContext<HotkeysContextType>({\n  hotkeys: [],\n  enabledScopes: [], // This array has to be empty instead of containing '*' as default, to check if the provider is set or not\n  toggleScope: () => {},\n  enableScope: () => {},\n  disableScope: () => {},\n})\n\nexport const useHotkeysContext = () => {\n  return useContext(HotkeysContext)\n}\n\ninterface Props {\n  initiallyActiveScopes?: string[]\n  children: ReactNode\n}\n\nexport const HotkeysProvider = ({ initiallyActiveScopes = ['*'], children }: Props) => {\n  const [internalActiveScopes, setInternalActiveScopes] = useState(\n    initiallyActiveScopes?.length > 0 ? initiallyActiveScopes : ['*']\n  )\n  const [boundHotkeys, setBoundHotkeys] = useState<Hotkey[]>([])\n\n  const enableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes('*')) {\n        return [scope]\n      }\n\n      return Array.from(new Set([...prev, scope]))\n    })\n  }, [])\n\n  const disableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.filter((s) => s !== scope).length === 0) {\n        return ['*']\n      } else {\n        return prev.filter((s) => s !== scope)\n      }\n    })\n  }, [])\n\n  const toggleScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes(scope)) {\n        if (prev.filter((s) => s !== scope).length === 0) {\n          return ['*']\n        } else {\n          return prev.filter((s) => s !== scope)\n        }\n      } else {\n        if (prev.includes('*')) {\n          return [scope]\n        }\n\n        return Array.from(new Set([...prev, scope]))\n      }\n    })\n  }, [])\n\n  const addBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => [...prev, hotkey])\n  }, [])\n\n  const removeBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => prev.filter((h) => !deepEqual(h, hotkey)))\n  }, [])\n\n  return (\n    <HotkeysContext.Provider\n      value={{ enabledScopes: internalActiveScopes, hotkeys: boundHotkeys, enableScope, disableScope, toggleScope }}\n    >\n      <BoundHotkeysProxyProviderProvider addHotkey={addBoundHotkey} removeHotkey={removeBoundHotkey}>\n        {children}\n      </BoundHotkeysProxyProviderProvider>\n    </HotkeysContext.Provider>\n  )\n}\n", "import { useRef } from 'react'\nimport deepEqual from './deepEqual'\n\nexport default function useDeepEqualMemo<T>(value: T) {\n  const ref = useRef<T | undefined>(undefined)\n\n  if (!deepEqual(ref.current, value)) {\n    ref.current = value\n  }\n\n  return ref.current\n}\n", "import { HotkeyCallback, Keys, Options, OptionsOrDependencyArray, RefType } from './types'\nimport { DependencyList, useCallback, useEffect, useLayoutEffect, useRef } from 'react'\nimport { mapKey, parseHotkey, parseKeysHookInput } from './parseHotkeys'\nimport {\n  isHotkeyEnabled,\n  isHotkeyEnabledOnTag,\n  isHotkeyMatchingKeyboardEvent,\n  isKeyboardEventTriggeredByInput,\n  isScopeActive,\n  maybePreventDefault,\n} from './validators'\nimport { useHotkeysContext } from './HotkeysProvider'\nimport { useBoundHotkeysProxy } from './BoundHotkeysProxyProvider'\nimport useDeepEqualMemo from './useDeepEqualMemo'\nimport { isReadonlyArray, pushToCurrentlyPressedKeys, removeFromCurrentlyPressedKeys } from './isHotkeyPressed'\n\nconst stopPropagation = (e: KeyboardEvent): void => {\n  e.stopPropagation()\n  e.preventDefault()\n  e.stopImmediatePropagation()\n}\n\nconst useSafeLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport default function useHotkeys<T extends HTMLElement>(\n  keys: Keys,\n  callback: HotkeyCallback,\n  options?: OptionsOrDependencyArray,\n  dependencies?: OptionsOrDependencyArray\n) {\n  const ref = useRef<RefType<T>>(null)\n  const hasTriggeredRef = useRef(false)\n\n  const _options: Options | undefined = !(options instanceof Array)\n    ? (options as Options)\n    : !(dependencies instanceof Array)\n    ? (dependencies as Options)\n    : undefined\n  const _keys: string = isReadonlyArray(keys) ? keys.join(_options?.splitKey) : keys\n  const _deps: DependencyList | undefined =\n    options instanceof Array ? options : dependencies instanceof Array ? dependencies : undefined\n\n  const memoisedCB = useCallback(callback, _deps ?? [])\n  const cbRef = useRef<HotkeyCallback>(memoisedCB)\n\n  if (_deps) {\n    cbRef.current = memoisedCB\n  } else {\n    cbRef.current = callback\n  }\n\n  const memoisedOptions = useDeepEqualMemo(_options)\n\n  const { enabledScopes } = useHotkeysContext()\n  const proxy = useBoundHotkeysProxy()\n\n  useSafeLayoutEffect(() => {\n    if (memoisedOptions?.enabled === false || !isScopeActive(enabledScopes, memoisedOptions?.scopes)) {\n      return\n    }\n\n    const listener = (e: KeyboardEvent, isKeyUp = false) => {\n      if (isKeyboardEventTriggeredByInput(e) && !isHotkeyEnabledOnTag(e, memoisedOptions?.enableOnFormTags)) {\n        return\n      }\n\n      // TODO: SINCE THE EVENT IS NOW ATTACHED TO THE REF, THE ACTIVE ELEMENT CAN NEVER BE INSIDE THE REF. THE HOTKEY ONLY TRIGGERS IF THE\n      // REF IS THE ACTIVE ELEMENT. THIS IS A PROBLEM SINCE FOCUSED SUB COMPONENTS WON'T TRIGGER THE HOTKEY.\n      if (ref.current !== null) {\n        const rootNode = ref.current.getRootNode()\n        if (\n          (rootNode instanceof Document || rootNode instanceof ShadowRoot) &&\n          rootNode.activeElement !== ref.current &&\n          !ref.current.contains(rootNode.activeElement)\n        ) {\n          stopPropagation(e)\n          return\n        }\n      }\n\n      if ((e.target as HTMLElement)?.isContentEditable && !memoisedOptions?.enableOnContentEditable) {\n        return\n      }\n\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) => {\n        const hotkey = parseHotkey(key, memoisedOptions?.combinationKey)\n\n        if (isHotkeyMatchingKeyboardEvent(e, hotkey, memoisedOptions?.ignoreModifiers) || hotkey.keys?.includes('*')) {\n          if (memoisedOptions?.ignoreEventWhen?.(e)) {\n            return\n          }\n\n          if (isKeyUp && hasTriggeredRef.current) {\n            return\n          }\n\n          maybePreventDefault(e, hotkey, memoisedOptions?.preventDefault)\n\n          if (!isHotkeyEnabled(e, hotkey, memoisedOptions?.enabled)) {\n            stopPropagation(e)\n\n            return\n          }\n\n          // Execute the user callback for that hotkey\n          cbRef.current(e, hotkey)\n\n          if (!isKeyUp) {\n            hasTriggeredRef.current = true\n          }\n        }\n      })\n    }\n\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys(mapKey(event.code))\n\n      if ((memoisedOptions?.keydown === undefined && memoisedOptions?.keyup !== true) || memoisedOptions?.keydown) {\n        listener(event)\n      }\n    }\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys(mapKey(event.code))\n\n      hasTriggeredRef.current = false\n\n      if (memoisedOptions?.keyup) {\n        listener(event, true)\n      }\n    }\n\n    const domNode = ref.current || _options?.document || document\n\n    // @ts-ignore\n    domNode.addEventListener('keyup', handleKeyUp)\n    // @ts-ignore\n    domNode.addEventListener('keydown', handleKeyDown)\n\n    if (proxy) {\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n        proxy.addHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n      )\n    }\n\n    return () => {\n      // @ts-ignore\n      domNode.removeEventListener('keyup', handleKeyUp)\n      // @ts-ignore\n      domNode.removeEventListener('keydown', handleKeyDown)\n\n      if (proxy) {\n        parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n          proxy.removeHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n        )\n      }\n    }\n  }, [_keys, memoisedOptions, enabledScopes])\n\n  return ref\n}\n", "import { useCallback, useState } from 'react'\nimport { mapKey } from './parseHotkeys'\n\nexport default function useRecordHotkeys() {\n  const [keys, setKeys] = useState(new Set<string>())\n  const [isRecording, setIsRecording] = useState(false)\n\n  const handler = useCallback((event: KeyboardEvent) => {\n    if (event.key === undefined) {\n      // Synthetic event (e.g., Chrome autofill).  Ignore.\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    setKeys((prev) => {\n      const newKeys = new Set(prev)\n\n      newKeys.add(mapKey(event.code))\n\n      return newKeys\n    })\n  }, [])\n\n  const stop = useCallback(() => {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener('keydown', handler)\n\n      setIsRecording(false)\n    }\n  }, [handler])\n\n  const start = useCallback(() => {\n    setKeys(new Set<string>())\n\n    if (typeof document !== 'undefined') {\n      stop()\n\n      document.addEventListener('keydown', handler)\n\n      setIsRecording(true)\n    }\n  }, [handler, stop])\n\n  const resetKeys = useCallback(() => {\n    setKeys(new Set<string>())\n  }, [])\n\n  return [keys, { start, stop, resetKeys, isRecording }] as const\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,2BAA2B,CAAC,SAAS,OAAO,QAAQ,OAAO,MAAM;AAEvE,IAAMC,aAAqC;EACzCC,KAAK;EACL,UAAQ;EACR,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACLC,WAAW;EACXC,YAAY;EACZC,SAAS;EACTC,UAAU;EACVC,UAAU;EACVC,WAAW;EACXC,QAAQ;EACRC,SAAS;EACTC,aAAa;EACbC,cAAc;;SAGAC,OAAOC,KAAW;AAChC,UAAQb,WAAWa,GAAG,KAAKA,KACxBC,KAAI,EACJC,YAAW,EACXC,QAAQ,0BAA0B,EAAE;AACzC;SAEgBC,iBAAiBJ,KAAW;AAC1C,SAAOd,yBAAyBmB,SAASL,GAAG;AAC9C;SAEgBM,mBAAmBC,MAAcC,UAAQ;MAARA,aAAQ,QAAA;AAARA,eAAW;;AAC1D,SAAOD,KAAKE,MAAMD,QAAQ;AAC5B;SAEgBE,YAAYC,QAAgBC,gBAAsBC,aAAoB;MAA1CD,mBAAc,QAAA;AAAdA,qBAAiB;;AAC3D,MAAML,OAAOI,OACVG,kBAAiB,EACjBL,MAAMG,cAAc,EACpBG,IAAI,SAACC,GAAC;AAAA,WAAKjB,OAAOiB,CAAC;;AAEtB,MAAMC,YAA+B;IACnCC,KAAKX,KAAKF,SAAS,KAAK;IACxBc,MAAMZ,KAAKF,SAAS,MAAM,KAAKE,KAAKF,SAAS,SAAS;IACtDe,OAAOb,KAAKF,SAAS,OAAO;IAC5BgB,MAAMd,KAAKF,SAAS,MAAM;IAC1BiB,KAAKf,KAAKF,SAAS,KAAK;;AAG1B,MAAMkB,iBAAiBhB,KAAKiB,OAAO,SAACR,GAAC;AAAA,WAAK,CAAC9B,yBAAyBmB,SAASW,CAAC;;AAE9E,SAAAS,SAAA,CAAA,GACKR,WAAS;IACZV,MAAMgB;IACNV;;AAEJ;CC7DE,WAAA;AACA,MAAI,OAAOa,aAAa,aAAa;AACnCA,aAASC,iBAAiB,WAAW,SAACC,GAAC;AACrC,UAAIA,EAAE5B,QAAQ6B,QAAW;AAEvB;;AAGFC,iCAA2B,CAAC/B,OAAO6B,EAAE5B,GAAG,GAAGD,OAAO6B,EAAEG,IAAI,CAAC,CAAC;KAC3D;AAEDL,aAASC,iBAAiB,SAAS,SAACC,GAAC;AACnC,UAAIA,EAAE5B,QAAQ6B,QAAW;AAEvB;;AAGFG,qCAA+B,CAACjC,OAAO6B,EAAE5B,GAAG,GAAGD,OAAO6B,EAAEG,IAAI,CAAC,CAAC;KAC/D;;AAGH,MAAI,OAAOE,WAAW,aAAa;AACjCA,WAAON,iBAAiB,QAAQ,WAAA;AAC9BO,2BAAqBC,MAAK;KAC3B;;AAEL,GAAC;AAED,IAAMD,uBAAoC,oBAAIE,IAAG;AAGjD,SAAgBC,gBAAgBC,OAAc;AAC5C,SAAOC,MAAMC,QAAQF,KAAK;AAC5B;AAEA,SAAgBG,gBAAgBzC,KAAiCQ,UAAQ;MAARA,aAAQ,QAAA;AAARA,eAAW;;AAC1E,MAAMkC,cAAcL,gBAAgBrC,GAAG,IAAIA,MAAMA,IAAIS,MAAMD,QAAQ;AAEnE,SAAOkC,YAAYC,MAAM,SAAChC,QAAM;AAAA,WAAKuB,qBAAqBU,IAAIjC,OAAOV,KAAI,EAAGC,YAAW,CAAE;;AAC3F;AAEA,SAAgB4B,2BAA2B9B,KAAsB;AAC/D,MAAM0C,cAAcH,MAAMC,QAAQxC,GAAG,IAAIA,MAAM,CAACA,GAAG;AAOnD,MAAIkC,qBAAqBU,IAAI,MAAM,GAAG;AACpCV,yBAAqBW,QAAQ,SAAC7C,MAAG;AAAA,aAAK,CAACI,iBAAiBJ,IAAG,KAAKkC,qBAAoB,QAAA,EAAQlC,KAAIE,YAAW,CAAE;;;AAG/GwC,cAAYG,QAAQ,SAAClC,QAAM;AAAA,WAAKuB,qBAAqBY,IAAInC,OAAOT,YAAW,CAAE;;AAC/E;AAEA,SAAgB8B,+BAA+BhC,KAAsB;AACnE,MAAM0C,cAAcH,MAAMC,QAAQxC,GAAG,IAAIA,MAAM,CAACA,GAAG;AAOnD,MAAIA,QAAQ,QAAQ;AAClBkC,yBAAqBC,MAAK;SACrB;AACLO,gBAAYG,QAAQ,SAAClC,QAAM;AAAA,aAAKuB,qBAAoB,QAAA,EAAQvB,OAAOT,YAAW,CAAE;;;AAEpF;SClEgB6C,oBAAoBnB,GAAkBjB,QAAgBqC,gBAAwB;AAC5F,MAAK,OAAOA,mBAAmB,cAAcA,eAAepB,GAAGjB,MAAM,KAAMqC,mBAAmB,MAAM;AAClGpB,MAAEoB,eAAc;;AAEpB;AAEA,SAAgBC,gBAAgBrB,GAAkBjB,QAAgBuC,SAAiB;AACjF,MAAI,OAAOA,YAAY,YAAY;AACjC,WAAOA,QAAQtB,GAAGjB,MAAM;;AAG1B,SAAOuC,YAAY,QAAQA,YAAYrB;AACzC;AAEA,SAAgBsB,gCAAgCC,IAAiB;AAC/D,SAAOC,qBAAqBD,IAAI,CAAC,SAAS,YAAY,QAAQ,CAAC;AACjE;AAEA,SAAgBC,qBAAoBC,MAElCC,eAAAA;MADEC,SAAMF,KAANE;AAAM,MACRD,kBAAAA,QAAAA;AAAAA,oBAA+C;;AAE/C,MAAME,gBAAgBD,UAAWA,OAAuBE;AAExD,MAAIrB,gBAAgBkB,aAAa,GAAG;AAClC,WAAOI,QACLF,iBAAiBF,iBAAiBA,cAAcK,KAAK,SAACC,KAAG;AAAA,aAAKA,IAAI3D,YAAW,MAAOuD,cAAcvD,YAAW;MAAG;;AAIpH,SAAOyD,QAAQF,iBAAiBF,iBAAiBA,kBAAkB,IAAI;AACzE;AAEA,SAAgBO,cAAcC,cAAwBC,QAAe;AACnE,MAAID,aAAaE,WAAW,KAAKD,QAAQ;AACvCE,YAAQC,KACN,2KAA2K;AAG7K,WAAO;;AAGT,MAAI,CAACH,QAAQ;AACX,WAAO;;AAGT,SAAOD,aAAaH,KAAK,SAACQ,OAAK;AAAA,WAAKJ,OAAO3D,SAAS+D,KAAK;QAAML,aAAa1D,SAAS,GAAG;AAC1F;AAEO,IAAMgE,gCAAgC,SAAhCA,+BAAiCzC,GAAkBjB,QAAgB2D,iBAAe;MAAfA,oBAAe,QAAA;AAAfA,sBAAkB;;AAChG,MAAQpD,MAAsCP,OAAtCO,KAAKG,OAAiCV,OAAjCU,MAAMC,MAA2BX,OAA3BW,KAAKF,QAAsBT,OAAtBS,OAAOD,OAAeR,OAAfQ,MAAMZ,OAASI,OAATJ;AACrC,MAAagE,sBAAkE3C,EAAvE5B,KAA0B+B,OAA6CH,EAA7CG,MAAMyC,UAAuC5C,EAAvC4C,SAASC,UAA8B7C,EAA9B6C,SAASC,WAAqB9C,EAArB8C,UAAUC,SAAW/C,EAAX+C;AAEpE,MAAMC,UAAU7E,OAAOgC,IAAI;AAC3B,MAAM8C,aAAaN,oBAAoBrE,YAAW;AAElD,MAAI,EAACK,QAAI,QAAJA,KAAMF,SAASuE,OAAO,MAAK,EAACrE,QAAI,QAAJA,KAAMF,SAASwE,UAAU,MAAK,CAAC,CAAC,QAAQ,WAAW,WAAW,QAAQ,OAAO,SAAS,IAAI,EAAExE,SAASuE,OAAO,GAAG;AAC9I,WAAO;;AAGT,MAAI,CAACN,iBAAiB;AAEpB,QAAIpD,QAAQ,CAACyD,UAAUE,eAAe,OAAO;AAC3C,aAAO;;AAGT,QAAIzD,UAAU,CAACsD,YAAYG,eAAe,SAAS;AACjD,aAAO;;AAIT,QAAIvD,KAAK;AACP,UAAI,CAACmD,WAAW,CAACD,SAAS;AACxB,eAAO;;WAEJ;AACL,UAAInD,SAAS,CAACoD,WAAWI,eAAe,UAAUA,eAAe,MAAM;AACrE,eAAO;;AAGT,UAAI1D,SAAS,CAACqD,WAAWK,eAAe,UAAUA,eAAe,WAAW;AAC1E,eAAO;;;;AAOb,MAAItE,QAAQA,KAAK0D,WAAW,MAAM1D,KAAKF,SAASwE,UAAU,KAAKtE,KAAKF,SAASuE,OAAO,IAAI;AACtF,WAAO;aACErE,MAAM;AAEf,WAAOkC,gBAAgBlC,IAAI;aAClB,CAACA,MAAM;AAEhB,WAAO;;AAIT,SAAO;AACT;AChGA,IAAMuE,gCAA4BC,4BAAyDlD,MAAS;AAE7F,IAAMmD,uBAAuB,SAAvBA,wBAAoBA;AAC/B,aAAOC,yBAAWH,yBAAyB;AAC7C;AAQA,SAAwBI,kCAAiC5B,MAAA;MAAG6B,YAAS7B,KAAT6B,WAAWC,eAAY9B,KAAZ8B,cAAcC,WAAQ/B,KAAR+B;AACnF,aACEC,wBAACR,0BAA0BS,UAAQ;IAACjD,OAAO;MAAE6C;MAAWC;;IAAeC;GAEnC;AAExC;SC1BwBG,UAAUC,GAAQC,GAAM;AAE9C,SAAOD,KAAKC,KAAK,OAAOD,MAAM,YAAY,OAAOC,MAAM,WACnDC,OAAOpF,KAAKkF,CAAC,EAAExB,WAAW0B,OAAOpF,KAAKmF,CAAC,EAAEzB;EAEvC0B,OAAOpF,KAAKkF,CAAC,EAAEG,OAAO,SAACC,SAAS7F,KAAG;AAAA,WAAK6F,WAAWL,UAAUC,EAAEzF,GAAG,GAAG0F,EAAE1F,GAAG,CAAC;KAAG,IAAI,IACpFyF,MAAMC;AACZ;ACOA,IAAMI,qBAAiBf,4BAAkC;EACvDgB,SAAS,CAAA;EACTC,eAAe,CAAA;EACfC,aAAa,SAAAA,cAAAA;EAAAA;EACbC,aAAa,SAAAA,cAAAA;EAAAA;EACbC,cAAc,SAAAA,eAAAA;EAAAA;CACf;AAED,IAAaC,oBAAoB,SAApBA,qBAAiBA;AAC5B,aAAOnB,yBAAWa,cAAc;AAClC;AAOA,IAAaO,kBAAkB,SAAlBA,iBAAe/C,MAAA;mCAAMgD,uBAAAA,wBAAqBC,0BAAA,SAAG,CAAC,GAAG,IAACA,uBAAElB,WAAQ/B,KAAR+B;AAC/D,MAAAmB,gBAAwDC,wBACtDH,yBAAqB,OAAA,SAArBA,sBAAuBrC,UAAS,IAAIqC,wBAAwB,CAAC,GAAG,CAAC,GAD5DI,uBAAoBF,UAAA,CAAA,GAAEG,0BAAuBH,UAAA,CAAA;AAGpD,MAAAI,iBAAwCH,uBAAmB,CAAA,CAAE,GAAtDI,eAAYD,WAAA,CAAA,GAAEE,kBAAeF,WAAA,CAAA;AAEpC,MAAMV,mBAAca,0BAAY,SAAC3C,OAAa;AAC5CuC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAK3G,SAAS,GAAG,GAAG;AACtB,eAAO,CAAC+D,KAAK;;AAGf,aAAO7B,MAAM0E,KAAK,IAAI7E,IAAG,CAAA,EAAA8E,OAAKF,MAAI,CAAE5C,KAAK,CAAA,CAAC,CAAC;KAC5C;KACA,CAAA,CAAE;AAEL,MAAM+B,oBAAeY,0BAAY,SAAC3C,OAAa;AAC7CuC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAKxF,OAAO,SAAC2F,GAAC;AAAA,eAAKA,MAAM/C;SAAOH,WAAW,GAAG;AAChD,eAAO,CAAC,GAAG;aACN;AACL,eAAO+C,KAAKxF,OAAO,SAAC2F,GAAC;AAAA,iBAAKA,MAAM/C;;;KAEnC;KACA,CAAA,CAAE;AAEL,MAAM6B,mBAAcc,0BAAY,SAAC3C,OAAa;AAC5CuC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAK3G,SAAS+D,KAAK,GAAG;AACxB,YAAI4C,KAAKxF,OAAO,SAAC2F,GAAC;AAAA,iBAAKA,MAAM/C;WAAOH,WAAW,GAAG;AAChD,iBAAO,CAAC,GAAG;eACN;AACL,iBAAO+C,KAAKxF,OAAO,SAAC2F,GAAC;AAAA,mBAAKA,MAAM/C;;;aAE7B;AACL,YAAI4C,KAAK3G,SAAS,GAAG,GAAG;AACtB,iBAAO,CAAC+D,KAAK;;AAGf,eAAO7B,MAAM0E,KAAK,IAAI7E,IAAG,CAAA,EAAA8E,OAAKF,MAAI,CAAE5C,KAAK,CAAA,CAAC,CAAC;;KAE9C;KACA,CAAA,CAAE;AAEL,MAAMgD,qBAAiBL,0BAAY,SAACpG,QAAc;AAChDmG,oBAAgB,SAACE,MAAI;AAAA,aAAA,CAAA,EAAAE,OAASF,MAAI,CAAErG,MAAM,CAAA;KAAC;KAC1C,CAAA,CAAE;AAEL,MAAM0G,wBAAoBN,0BAAY,SAACpG,QAAc;AACnDmG,oBAAgB,SAACE,MAAI;AAAA,aAAKA,KAAKxF,OAAO,SAAC8F,GAAC;AAAA,eAAK,CAAC9B,UAAU8B,GAAG3G,MAAM;;;KAChE,CAAA,CAAE;AAEL,aACE2E,wBAACQ,eAAeP,UAAQ;IACtBjD,OAAO;MAAE0D,eAAeU;MAAsBX,SAASc;MAAcX,aAAAA;MAAaC,cAAAA;MAAcF,aAAAA;;IAAcZ,cAE9GC,wBAACJ,mCAAiC;MAACC,WAAWiC;MAAgBhC,cAAciC;MAAkBhC;KAE3D;GACZ;AAE7B;SCzFwBkC,iBAAoBjF,OAAQ;AAClD,MAAMkF,UAAMC,qBAAsB5F,MAAS;AAE3C,MAAI,CAAC2D,UAAUgC,IAAIE,SAASpF,KAAK,GAAG;AAClCkF,QAAIE,UAAUpF;;AAGhB,SAAOkF,IAAIE;AACb;ACKA,IAAMC,kBAAkB,SAAlBA,iBAAmB/F,GAAgB;AACvCA,IAAE+F,gBAAe;AACjB/F,IAAEoB,eAAc;AAChBpB,IAAEgG,yBAAwB;AAC5B;AAEA,IAAMC,sBAAsB,OAAO5F,WAAW,cAAc6F,+BAAkBC;AAE9E,SAAwBC,WACtBzH,MACA0H,UACAC,SACAC,cAAuC;AAEvC,MAAMX,UAAMC,qBAAmB,IAAI;AACnC,MAAMW,sBAAkBX,qBAAO,KAAK;AAEpC,MAAMY,WAAgC,EAAEH,mBAAmB3F,SACtD2F,UACD,EAAEC,wBAAwB5F,SACzB4F,eACDtG;AACJ,MAAMyG,QAAgBjG,gBAAgB9B,IAAI,IAAIA,KAAKgI,KAAKF,YAAQ,OAAA,SAARA,SAAU7H,QAAQ,IAAID;AAC9E,MAAMiI,QACJN,mBAAmB3F,QAAQ2F,UAAUC,wBAAwB5F,QAAQ4F,eAAetG;AAEtF,MAAM4G,iBAAa1B,0BAAYkB,UAAUO,SAAK,OAALA,QAAS,CAAA,CAAE;AACpD,MAAME,YAAQjB,qBAAuBgB,UAAU;AAE/C,MAAID,OAAO;AACTE,UAAMhB,UAAUe;SACX;AACLC,UAAMhB,UAAUO;;AAGlB,MAAMU,kBAAkBpB,iBAAiBc,QAAQ;AAEjD,MAAAO,qBAA0BxC,kBAAiB,GAAnCJ,gBAAa4C,mBAAb5C;AACR,MAAM6C,QAAQ7D,qBAAoB;AAElC6C,sBAAoB,WAAA;AAClB,SAAIc,mBAAe,OAAA,SAAfA,gBAAiBzF,aAAY,SAAS,CAACY,cAAckC,eAAe2C,mBAAe,OAAA,SAAfA,gBAAiB3E,MAAM,GAAG;AAChG;;AAGF,QAAM8E,WAAW,SAAXA,UAAYlH,GAAkBmH,SAAO;;UAAPA,YAAO,QAAA;AAAPA,kBAAU;;AAC5C,UAAI5F,gCAAgCvB,CAAC,KAAK,CAACyB,qBAAqBzB,GAAG+G,mBAAe,OAAA,SAAfA,gBAAiBK,gBAAgB,GAAG;AACrG;;AAKF,UAAIxB,IAAIE,YAAY,MAAM;AACxB,YAAMuB,WAAWzB,IAAIE,QAAQwB,YAAW;AACxC,aACGD,oBAAoBE,YAAYF,oBAAoBG,eACrDH,SAASI,kBAAkB7B,IAAIE,WAC/B,CAACF,IAAIE,QAAQ4B,SAASL,SAASI,aAAa,GAC5C;AACA1B,0BAAgB/F,CAAC;AACjB;;;AAIJ,WAAK2H,YAAA3H,EAAE4B,WAAsB,QAAxB+F,UAA0BC,qBAAqB,EAACb,mBAAe,QAAfA,gBAAiBc,0BAAyB;AAC7F;;AAGFnJ,yBAAmBgI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiBnI,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;;AAC/D,YAAMW,SAASD,YAAYV,KAAK2I,mBAAe,OAAA,SAAfA,gBAAiB/H,cAAc;AAE/D,YAAIyD,8BAA8BzC,GAAGjB,QAAQgI,mBAAe,OAAA,SAAfA,gBAAiBrE,eAAe,MAACoF,eAAI/I,OAAOJ,SAAI,QAAXmJ,aAAarJ,SAAS,GAAG,GAAG;AAC5G,cAAIsI,mBAAe,QAAfA,gBAAiBgB,mBAAe,QAAhChB,gBAAiBgB,gBAAkB/H,CAAC,GAAG;AACzC;;AAGF,cAAImH,WAAWX,gBAAgBV,SAAS;AACtC;;AAGF3E,8BAAoBnB,GAAGjB,QAAQgI,mBAAe,OAAA,SAAfA,gBAAiB3F,cAAc;AAE9D,cAAI,CAACC,gBAAgBrB,GAAGjB,QAAQgI,mBAAe,OAAA,SAAfA,gBAAiBzF,OAAO,GAAG;AACzDyE,4BAAgB/F,CAAC;AAEjB;;AAIF8G,gBAAMhB,QAAQ9F,GAAGjB,MAAM;AAEvB,cAAI,CAACoI,SAAS;AACZX,4BAAgBV,UAAU;;;OAG/B;;AAGH,QAAMkC,gBAAgB,SAAhBA,eAAiBC,OAAoB;AACzC,UAAIA,MAAM7J,QAAQ6B,QAAW;AAE3B;;AAGFC,iCAA2B/B,OAAO8J,MAAM9H,IAAI,CAAC;AAE7C,WAAK4G,mBAAe,OAAA,SAAfA,gBAAiBmB,aAAYjI,WAAa8G,mBAAe,OAAA,SAAfA,gBAAiBoB,WAAU,QAASpB,mBAAe,QAAfA,gBAAiBmB,SAAS;AAC3GhB,iBAASe,KAAK;;;AAIlB,QAAMG,cAAc,SAAdA,aAAeH,OAAoB;AACvC,UAAIA,MAAM7J,QAAQ6B,QAAW;AAE3B;;AAGFG,qCAA+BjC,OAAO8J,MAAM9H,IAAI,CAAC;AAEjDqG,sBAAgBV,UAAU;AAE1B,UAAIiB,mBAAe,QAAfA,gBAAiBoB,OAAO;AAC1BjB,iBAASe,OAAO,IAAI;;;AAIxB,QAAMI,UAAUzC,IAAIE,YAAWW,YAAQ,OAAA,SAARA,SAAU3G,aAAYA;AAGrDuI,YAAQtI,iBAAiB,SAASqI,WAAW;AAE7CC,YAAQtI,iBAAiB,WAAWiI,aAAa;AAEjD,QAAIf,OAAO;AACTvI,yBAAmBgI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiBnI,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;AAAA,eAC/D6I,MAAM1D,UAAUzE,YAAYV,KAAK2I,mBAAe,OAAA,SAAfA,gBAAiB/H,gBAAgB+H,mBAAe,OAAA,SAAfA,gBAAiB9H,WAAW,CAAC;;;AAInG,WAAO,WAAA;AAELoJ,cAAQC,oBAAoB,SAASF,WAAW;AAEhDC,cAAQC,oBAAoB,WAAWN,aAAa;AAEpD,UAAIf,OAAO;AACTvI,2BAAmBgI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiBnI,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;AAAA,iBAC/D6I,MAAMzD,aAAa1E,YAAYV,KAAK2I,mBAAe,OAAA,SAAfA,gBAAiB/H,gBAAgB+H,mBAAe,OAAA,SAAfA,gBAAiB9H,WAAW,CAAC;;;;KAIvG,CAACyH,OAAOK,iBAAiB3C,aAAa,CAAC;AAE1C,SAAOwB;AACT;SCvKwB2C,mBAAgBA;AACtC,MAAA3D,gBAAwBC,uBAAS,oBAAIrE,IAAG,CAAU,GAA3C7B,OAAIiG,UAAA,CAAA,GAAE4D,UAAO5D,UAAA,CAAA;AACpB,MAAAI,iBAAsCH,uBAAS,KAAK,GAA7C4D,cAAWzD,WAAA,CAAA,GAAE0D,iBAAc1D,WAAA,CAAA;AAElC,MAAM2D,cAAUxD,0BAAY,SAAC8C,OAAoB;AAC/C,QAAIA,MAAM7J,QAAQ6B,QAAW;AAE3B;;AAGFgI,UAAM7G,eAAc;AACpB6G,UAAMlC,gBAAe;AAErByC,YAAQ,SAACpD,MAAI;AACX,UAAMwD,UAAU,IAAIpI,IAAI4E,IAAI;AAE5BwD,cAAQ1H,IAAI/C,OAAO8J,MAAM9H,IAAI,CAAC;AAE9B,aAAOyI;KACR;KACA,CAAA,CAAE;AAEL,MAAMC,WAAO1D,0BAAY,WAAA;AACvB,QAAI,OAAOrF,aAAa,aAAa;AACnCA,eAASwI,oBAAoB,WAAWK,OAAO;AAE/CD,qBAAe,KAAK;;KAErB,CAACC,OAAO,CAAC;AAEZ,MAAMG,YAAQ3D,0BAAY,WAAA;AACxBqD,YAAQ,oBAAIhI,IAAG,CAAU;AAEzB,QAAI,OAAOV,aAAa,aAAa;AACnC+I,WAAI;AAEJ/I,eAASC,iBAAiB,WAAW4I,OAAO;AAE5CD,qBAAe,IAAI;;KAEpB,CAACC,SAASE,IAAI,CAAC;AAElB,MAAME,gBAAY5D,0BAAY,WAAA;AAC5BqD,YAAQ,oBAAIhI,IAAG,CAAU;KACxB,CAAA,CAAE;AAEL,SAAO,CAAC7B,MAAM;IAAEmK;IAAOD;IAAME;IAAWN;GAAa;AACvD;", "names": ["reservedModifierKeywords", "mapped<PERSON>eys", "esc", "ShiftLeft", "ShiftRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "OSLeft", "OSRight", "ControlLeft", "ControlRight", "mapKey", "key", "trim", "toLowerCase", "replace", "isHotkeyModifier", "includes", "parseKeysHookInput", "keys", "splitKey", "split", "parseHotkey", "hotkey", "combinationKey", "description", "toLocaleLowerCase", "map", "k", "modifiers", "alt", "ctrl", "shift", "meta", "mod", "singleCharKeys", "filter", "_extends", "document", "addEventListener", "e", "undefined", "pushToCurrentlyPressedKeys", "code", "removeFromCurrentlyPressedKeys", "window", "currentlyPressedKeys", "clear", "Set", "isReadonlyArray", "value", "Array", "isArray", "isHotkeyPressed", "hotkeyArray", "every", "has", "for<PERSON>ach", "add", "maybePreventDefault", "preventDefault", "isHotkeyEnabled", "enabled", "isKeyboardEventTriggeredByInput", "ev", "isHotkeyEnabledOnTag", "_ref", "enabledOnTags", "target", "targetTagName", "tagName", "Boolean", "some", "tag", "isScopeActive", "activeScopes", "scopes", "length", "console", "warn", "scope", "isHotkeyMatchingKeyboardEvent", "ignoreModifiers", "pressedKeyUppercase", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "keyCode", "<PERSON><PERSON><PERSON>", "BoundHotkeysProxyProvider", "createContext", "useBoundHotkeysProxy", "useContext", "BoundHotkeysProxyProviderProvider", "addHotkey", "removeHotkey", "children", "_jsx", "Provider", "deepEqual", "x", "y", "Object", "reduce", "isEqual", "HotkeysContext", "hotkeys", "enabledScopes", "toggleScope", "enableScope", "disableScope", "useHotkeysContext", "HotkeysProvider", "initiallyActiveScopes", "_ref$initiallyActiveS", "_useState", "useState", "internalActiveScopes", "setInternalActiveScopes", "_useState2", "boundHotkeys", "setBoundHotkeys", "useCallback", "prev", "from", "concat", "s", "addBoundHotkey", "removeBoundHotkey", "h", "useDeepEqualMemo", "ref", "useRef", "current", "stopPropagation", "stopImmediatePropagation", "useSafeLayoutEffect", "useLayoutEffect", "useEffect", "useHotkeys", "callback", "options", "dependencies", "hasTriggeredRef", "_options", "_keys", "join", "_deps", "memoisedCB", "cbRef", "memoisedOptions", "_useHotkeysContext", "proxy", "listener", "isKeyUp", "enableOnFormTags", "rootNode", "getRootNode", "Document", "ShadowRoot", "activeElement", "contains", "_e$target", "isContentEditable", "enableOnContentEditable", "_hotkey$keys", "ignoreEventWhen", "handleKeyDown", "event", "keydown", "keyup", "handleKeyUp", "domNode", "removeEventListener", "useRecordHotkeys", "set<PERSON><PERSON><PERSON>", "isRecording", "setIsRecording", "handler", "newKeys", "stop", "start", "resetKeys"]}
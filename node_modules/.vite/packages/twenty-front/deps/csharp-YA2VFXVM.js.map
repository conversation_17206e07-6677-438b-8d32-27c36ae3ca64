{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/csharp/csharp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csharp/csharp.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cs\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"extern\",\n    \"alias\",\n    \"using\",\n    \"bool\",\n    \"decimal\",\n    \"sbyte\",\n    \"byte\",\n    \"short\",\n    \"ushort\",\n    \"int\",\n    \"uint\",\n    \"long\",\n    \"ulong\",\n    \"char\",\n    \"float\",\n    \"double\",\n    \"object\",\n    \"dynamic\",\n    \"string\",\n    \"assembly\",\n    \"is\",\n    \"as\",\n    \"ref\",\n    \"out\",\n    \"this\",\n    \"base\",\n    \"new\",\n    \"typeof\",\n    \"void\",\n    \"checked\",\n    \"unchecked\",\n    \"default\",\n    \"delegate\",\n    \"var\",\n    \"const\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"case\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"foreach\",\n    \"in\",\n    \"break\",\n    \"continue\",\n    \"goto\",\n    \"return\",\n    \"throw\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"lock\",\n    \"yield\",\n    \"from\",\n    \"let\",\n    \"where\",\n    \"join\",\n    \"on\",\n    \"equals\",\n    \"into\",\n    \"orderby\",\n    \"ascending\",\n    \"descending\",\n    \"select\",\n    \"group\",\n    \"by\",\n    \"namespace\",\n    \"partial\",\n    \"class\",\n    \"field\",\n    \"event\",\n    \"method\",\n    \"param\",\n    \"public\",\n    \"protected\",\n    \"internal\",\n    \"private\",\n    \"abstract\",\n    \"sealed\",\n    \"static\",\n    \"struct\",\n    \"readonly\",\n    \"volatile\",\n    \"virtual\",\n    \"override\",\n    \"params\",\n    \"get\",\n    \"set\",\n    \"add\",\n    \"remove\",\n    \"operator\",\n    \"true\",\n    \"false\",\n    \"implicit\",\n    \"explicit\",\n    \"interface\",\n    \"enum\",\n    \"null\",\n    \"async\",\n    \"await\",\n    \"fixed\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"unsafe\",\n    \"nameof\",\n    \"when\"\n  ],\n  namespaceFollows: [\"namespace\", \"using\"],\n  parenFollows: [\"if\", \"for\", \"while\", \"switch\", \"foreach\", \"using\", \"catch\", \"when\"],\n  operators: [\n    \"=\",\n    \"??\",\n    \"||\",\n    \"&&\",\n    \"|\",\n    \"^\",\n    \"&\",\n    \"==\",\n    \"!=\",\n    \"<=\",\n    \">=\",\n    \"<<\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"!\",\n    \"~\",\n    \"++\",\n    \"--\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>=\",\n    \">>\",\n    \"=>\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // escape sequences\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /\\@?[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@keywords\": {\n              token: \"keyword.$0\",\n              next: \"@qualified\"\n            },\n            \"@default\": { token: \"identifier\", next: \"@qualified\" }\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"$S2==litinterpstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?[fFdD]?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01_]+/, \"number.hex\"],\n      // binary: use same theme style as hex\n      [/[0-9_]+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", next: \"@string\" }],\n      [/\\$\\@\"/, { token: \"string.quote\", next: \"@litinterpstring\" }],\n      [/\\@\"/, { token: \"string.quote\", next: \"@litstring\" }],\n      [/\\$\"/, { token: \"string.quote\", next: \"@interpolatedstring\" }],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    qualified: [\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/\\./, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Z]\\w*/, \"namespace\"],\n      [/[\\.=]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/,    'comment', '@push' ],    // no nested comments :-(\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litstring: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litinterpstring: [\n      [/[^\"{]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.litinterpstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    interpolatedstring: [\n      [/[^\\\\\"{]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^[ \\t\\v\\f]*#((r)|(load))(?=\\s)/, \"directive.csx\"],\n      [/^[ \\t\\v\\f]*#\\w.*$/, \"namespace.cpp\"],\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAiCA;AA1CJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,UACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MACpD;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB,CAAC,aAAa,OAAO;AAAA,MACvC,cAAc,CAAC,MAAM,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,MAAM;AAAA,MAClF,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,qBAAqB;AAAA,kBACnB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,aAAa;AAAA,kBACX,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,YAAY,EAAE,OAAO,cAAc,MAAM,aAAa;AAAA,cACxD;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,2BAA2B;AAAA,kBACzB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,wBAAwB;AAAA,kBACtB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,oBAAoB,WAAW;AAAA,UAChC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,2CAA2C,cAAc;AAAA,UAC1D,CAAC,sBAAsB,YAAY;AAAA,UACnC,CAAC,eAAe,YAAY;AAAA;AAAA,UAE5B,CAAC,WAAW,QAAQ;AAAA;AAAA,UAEpB,CAAC,SAAS,WAAW;AAAA;AAAA,UAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,UAAU,CAAC;AAAA,UAChD,CAAC,SAAS,EAAE,OAAO,gBAAgB,MAAM,mBAAmB,CAAC;AAAA,UAC7D,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,UACrD,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,sBAAsB,CAAC;AAAA;AAAA,UAE9D,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,UAC1D,CAAC,KAAK,gBAAgB;AAAA,QACxB;AAAA,QACA,WAAW;AAAA,UACT;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa,EAAE,OAAO,aAAa;AAAA,gBACnC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,MAAM,WAAW;AAAA,UAClB,CAAC,IAAI,IAAI,MAAM;AAAA,QACjB;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,YAAY,WAAW;AAAA,UACxB,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,IAAI,IAAI,MAAM;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA;AAAA,UAErB,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,QAC/C;AAAA,QACA,WAAW;AAAA,UACT,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,QAC/C;AAAA,QACA,iBAAiB;AAAA,UACf,CAAC,UAAU,QAAQ;AAAA,UACnB,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAAA,UAC7D,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,QAC/C;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,MAAM,eAAe;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,UAChE,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,QAC/C;AAAA,QACA,YAAY;AAAA,UACV,CAAC,kCAAkC,eAAe;AAAA,UAClD,CAAC,qBAAqB,eAAe;AAAA,UACrC,CAAC,kBAAkB,EAAE;AAAA,UACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
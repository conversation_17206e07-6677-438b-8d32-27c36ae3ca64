import {
  require_classnames
} from "./chunk-XN5C6TTX.js";
import {
  arrow,
  autoUpdate,
  computePosition,
  flip,
  offset,
  shift
} from "./chunk-YAGSMJYR.js";
import {
  require_react
} from "./chunk-LABDTKBP.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/react-tooltip/dist/react-tooltip.min.mjs
var import_react = __toESM(require_react(), 1);
var import_classnames = __toESM(require_classnames(), 1);
var h = "react-tooltip-core-styles";
var w = "react-tooltip-base-styles";
var b = { core: false, base: false };
function S({ css: e2, id: t2 = w, type: o2 = "base", ref: l2 }) {
  var r2, n2;
  if (!e2 || "undefined" == typeof document || b[o2]) return;
  if ("core" === o2 && "undefined" != typeof process && (null === (r2 = null === process || void 0 === process ? void 0 : process.env) || void 0 === r2 ? void 0 : r2.REACT_TOOLTIP_DISABLE_CORE_STYLES)) return;
  if ("base" !== o2 && "undefined" != typeof process && (null === (n2 = null === process || void 0 === process ? void 0 : process.env) || void 0 === n2 ? void 0 : n2.REACT_TOOLTIP_DISABLE_BASE_STYLES)) return;
  "core" === o2 && (t2 = h), l2 || (l2 = {});
  const { insertAt: i2 } = l2;
  if (document.getElementById(t2)) return;
  const c2 = document.head || document.getElementsByTagName("head")[0], s2 = document.createElement("style");
  s2.id = t2, s2.type = "text/css", "top" === i2 && c2.firstChild ? c2.insertBefore(s2, c2.firstChild) : c2.appendChild(s2), s2.styleSheet ? s2.styleSheet.cssText = e2 : s2.appendChild(document.createTextNode(e2)), b[o2] = true;
}
function g({ type: e2 = "base", id: t2 = w } = {}) {
  if (!b[e2]) return;
  "core" === e2 && (t2 = h);
  const o2 = document.getElementById(t2);
  "style" === (null == o2 ? void 0 : o2.tagName) ? null == o2 || o2.remove() : console.warn(`[react-tooltip] Failed to remove 'style' element with id '${t2}'. Call \`injectStyle()\` first`), b[e2] = false;
}
var E = async ({ elementReference: e2 = null, tooltipReference: t2 = null, tooltipArrowReference: o2 = null, place: l2 = "top", offset: r2 = 10, strategy: n2 = "absolute", middlewares: i2 = [offset(Number(r2)), flip({ fallbackAxisSideDirection: "start" }), shift({ padding: 5 })], border: c2 }) => {
  if (!e2) return { tooltipStyles: {}, tooltipArrowStyles: {}, place: l2 };
  if (null === t2) return { tooltipStyles: {}, tooltipArrowStyles: {}, place: l2 };
  const s2 = i2;
  return o2 ? (s2.push(arrow({ element: o2, padding: 5 })), computePosition(e2, t2, { placement: l2, strategy: n2, middleware: s2 }).then(({ x: e3, y: t3, placement: o3, middlewareData: l3 }) => {
    var r3, n3;
    const i3 = { left: `${e3}px`, top: `${t3}px`, border: c2 }, { x: s3, y: a2 } = null !== (r3 = l3.arrow) && void 0 !== r3 ? r3 : { x: 0, y: 0 }, u = null !== (n3 = { top: "bottom", right: "left", bottom: "top", left: "right" }[o3.split("-")[0]]) && void 0 !== n3 ? n3 : "bottom", d = c2 && { borderBottom: c2, borderRight: c2 };
    let p = 0;
    if (c2) {
      const e4 = `${c2}`.match(/(\d+)px/);
      p = (null == e4 ? void 0 : e4[1]) ? Number(e4[1]) : 1;
    }
    return { tooltipStyles: i3, tooltipArrowStyles: { left: null != s3 ? `${s3}px` : "", top: null != a2 ? `${a2}px` : "", right: "", bottom: "", ...d, [u]: `-${4 + p}px` }, place: o3 };
  })) : computePosition(e2, t2, { placement: "bottom", strategy: n2, middleware: s2 }).then(({ x: e3, y: t3, placement: o3 }) => ({ tooltipStyles: { left: `${e3}px`, top: `${t3}px` }, tooltipArrowStyles: {}, place: o3 }));
};
var A = (e2, t2) => !("CSS" in window && "supports" in window.CSS) || window.CSS.supports(e2, t2);
var _ = (e2, t2, o2) => {
  let l2 = null;
  const r2 = function(...r3) {
    const n2 = () => {
      l2 = null, o2 || e2.apply(this, r3);
    };
    o2 && !l2 && (e2.apply(this, r3), l2 = setTimeout(n2, t2)), o2 || (l2 && clearTimeout(l2), l2 = setTimeout(n2, t2));
  };
  return r2.cancel = () => {
    l2 && (clearTimeout(l2), l2 = null);
  }, r2;
};
var O = (e2) => null !== e2 && !Array.isArray(e2) && "object" == typeof e2;
var k = (e2, t2) => {
  if (e2 === t2) return true;
  if (Array.isArray(e2) && Array.isArray(t2)) return e2.length === t2.length && e2.every((e3, o3) => k(e3, t2[o3]));
  if (Array.isArray(e2) !== Array.isArray(t2)) return false;
  if (!O(e2) || !O(t2)) return e2 === t2;
  const o2 = Object.keys(e2), l2 = Object.keys(t2);
  return o2.length === l2.length && o2.every((o3) => k(e2[o3], t2[o3]));
};
var T = (e2) => {
  if (!(e2 instanceof HTMLElement || e2 instanceof SVGElement)) return false;
  const t2 = getComputedStyle(e2);
  return ["overflow", "overflow-x", "overflow-y"].some((e3) => {
    const o2 = t2.getPropertyValue(e3);
    return "auto" === o2 || "scroll" === o2;
  });
};
var L = (e2) => {
  if (!e2) return null;
  let t2 = e2.parentElement;
  for (; t2; ) {
    if (T(t2)) return t2;
    t2 = t2.parentElement;
  }
  return document.scrollingElement || document.documentElement;
};
var C = "undefined" != typeof window ? import_react.useLayoutEffect : import_react.useEffect;
var R = (e2) => {
  e2.current && (clearTimeout(e2.current), e2.current = null);
};
var x = "DEFAULT_TOOLTIP_ID";
var N = { anchorRefs: /* @__PURE__ */ new Set(), activeAnchor: { current: null }, attach: () => {
}, detach: () => {
}, setActiveAnchor: () => {
} };
var $ = (0, import_react.createContext)({ getTooltipData: () => N });
var I = ({ children: t2 }) => {
  const [o2, l2] = (0, import_react.useState)({ [x]: /* @__PURE__ */ new Set() }), [c2, s2] = (0, import_react.useState)({ [x]: { current: null } }), a2 = (e2, ...t3) => {
    l2((o3) => {
      var l3;
      const r2 = null !== (l3 = o3[e2]) && void 0 !== l3 ? l3 : /* @__PURE__ */ new Set();
      return t3.forEach((e3) => r2.add(e3)), { ...o3, [e2]: new Set(r2) };
    });
  }, u = (e2, ...t3) => {
    l2((o3) => {
      const l3 = o3[e2];
      return l3 ? (t3.forEach((e3) => l3.delete(e3)), { ...o3 }) : o3;
    });
  }, d = (0, import_react.useCallback)((e2 = x) => {
    var t3, l3;
    return { anchorRefs: null !== (t3 = o2[e2]) && void 0 !== t3 ? t3 : /* @__PURE__ */ new Set(), activeAnchor: null !== (l3 = c2[e2]) && void 0 !== l3 ? l3 : { current: null }, attach: (...t4) => a2(e2, ...t4), detach: (...t4) => u(e2, ...t4), setActiveAnchor: (t4) => ((e3, t5) => {
      s2((o3) => {
        var l4;
        return (null === (l4 = o3[e3]) || void 0 === l4 ? void 0 : l4.current) === t5.current ? o3 : { ...o3, [e3]: t5 };
      });
    })(e2, t4) };
  }, [o2, c2, a2, u]), p = (0, import_react.useMemo)(() => ({ getTooltipData: d }), [d]);
  return import_react.default.createElement($.Provider, { value: p }, t2);
};
function j(e2 = x) {
  return (0, import_react.useContext)($).getTooltipData(e2);
}
var B = ({ tooltipId: t2, children: l2, className: r2, place: n2, content: i2, html: c2, variant: a2, offset: u, wrapper: d, events: p, positionStrategy: v, delayShow: m, delayHide: f }) => {
  const { attach: h2, detach: w2 } = j(t2), b2 = (0, import_react.useRef)(null);
  return (0, import_react.useEffect)(() => (h2(b2), () => {
    w2(b2);
  }), []), import_react.default.createElement("span", { ref: b2, className: (0, import_classnames.default)("react-tooltip-wrapper", r2), "data-tooltip-place": n2, "data-tooltip-content": i2, "data-tooltip-html": c2, "data-tooltip-variant": a2, "data-tooltip-offset": u, "data-tooltip-wrapper": d, "data-tooltip-events": p, "data-tooltip-position-strategy": v, "data-tooltip-delay-show": m, "data-tooltip-delay-hide": f }, l2);
};
var z = { tooltip: "core-styles-module_tooltip__3vRRp", fixed: "core-styles-module_fixed__pcSol", arrow: "core-styles-module_arrow__cvMwQ", noArrow: "core-styles-module_noArrow__xock6", clickable: "core-styles-module_clickable__ZuTTB", show: "core-styles-module_show__Nt9eE", closing: "core-styles-module_closing__sGnxF" };
var D = { tooltip: "styles-module_tooltip__mnnfp", arrow: "styles-module_arrow__K0L3T", dark: "styles-module_dark__xNqje", light: "styles-module_light__Z6W-X", success: "styles-module_success__A2AKt", warning: "styles-module_warning__SCK0X", error: "styles-module_error__JvumD", info: "styles-module_info__BWdHW" };
var q = ({ forwardRef: t2, id: l2, className: i2, classNameArrow: c2, variant: u = "dark", anchorId: d, anchorSelect: p, place: v = "top", offset: m = 10, events: h2 = ["hover"], openOnClick: w2 = false, positionStrategy: b2 = "absolute", middlewares: S2, wrapper: g2, delayShow: A2 = 0, delayHide: O2 = 0, float: T2 = false, hidden: x2 = false, noArrow: N2 = false, clickable: $2 = false, closeOnEsc: I2 = false, closeOnScroll: B2 = false, closeOnResize: q2 = false, openEvents: H2, closeEvents: M2, globalCloseEvents: W, imperativeModeOnly: P, style: V, position: F, afterShow: K, afterHide: U, disableTooltip: X, content: Y, contentWrapperRef: G, isOpen: Z, defaultIsOpen: J = false, setIsOpen: Q, activeAnchor: ee, setActiveAnchor: te, border: oe, opacity: le, arrowColor: re, role: ne = "tooltip" }) => {
  var ie;
  const ce = (0, import_react.useRef)(null), se = (0, import_react.useRef)(null), ae = (0, import_react.useRef)(null), ue = (0, import_react.useRef)(null), de = (0, import_react.useRef)(null), [pe, ve] = (0, import_react.useState)({ tooltipStyles: {}, tooltipArrowStyles: {}, place: v }), [me, fe] = (0, import_react.useState)(false), [ye, he] = (0, import_react.useState)(false), [we, be] = (0, import_react.useState)(null), Se = (0, import_react.useRef)(false), ge = (0, import_react.useRef)(null), { anchorRefs: Ee, setActiveAnchor: Ae } = j(l2), _e = (0, import_react.useRef)(false), [Oe, ke] = (0, import_react.useState)([]), Te = (0, import_react.useRef)(false), Le = w2 || h2.includes("click"), Ce = Le || (null == H2 ? void 0 : H2.click) || (null == H2 ? void 0 : H2.dblclick) || (null == H2 ? void 0 : H2.mousedown), Re = H2 ? { ...H2 } : { mouseover: true, focus: true, mouseenter: false, click: false, dblclick: false, mousedown: false };
  !H2 && Le && Object.assign(Re, { mouseenter: false, focus: false, mouseover: false, click: true });
  const xe = M2 ? { ...M2 } : { mouseout: true, blur: true, mouseleave: false, click: false, dblclick: false, mouseup: false };
  !M2 && Le && Object.assign(xe, { mouseleave: false, blur: false, mouseout: false });
  const Ne = W ? { ...W } : { escape: I2 || false, scroll: B2 || false, resize: q2 || false, clickOutsideAnchor: Ce || false };
  P && (Object.assign(Re, { mouseenter: false, focus: false, click: false, dblclick: false, mousedown: false }), Object.assign(xe, { mouseleave: false, blur: false, click: false, dblclick: false, mouseup: false }), Object.assign(Ne, { escape: false, scroll: false, resize: false, clickOutsideAnchor: false })), C(() => (Te.current = true, () => {
    Te.current = false;
  }), []);
  const $e = (e2) => {
    Te.current && (e2 && he(true), setTimeout(() => {
      Te.current && (null == Q || Q(e2), void 0 === Z && fe(e2));
    }, 10));
  };
  (0, import_react.useEffect)(() => {
    if (void 0 === Z) return () => null;
    Z && he(true);
    const e2 = setTimeout(() => {
      fe(Z);
    }, 10);
    return () => {
      clearTimeout(e2);
    };
  }, [Z]), (0, import_react.useEffect)(() => {
    if (me !== Se.current) if (R(de), Se.current = me, me) null == K || K();
    else {
      const e2 = ((e3) => {
        const t3 = e3.match(/^([\d.]+)(ms|s)$/);
        if (!t3) return 0;
        const [, o2, l3] = t3;
        return Number(o2) * ("ms" === l3 ? 1 : 1e3);
      })(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));
      de.current = setTimeout(() => {
        he(false), be(null), null == U || U();
      }, e2 + 25);
    }
  }, [me]);
  const Ie = (e2) => {
    ve((t3) => k(t3, e2) ? t3 : e2);
  }, je = (e2 = A2) => {
    R(ae), ye ? $e(true) : ae.current = setTimeout(() => {
      $e(true);
    }, e2);
  }, Be = (e2 = O2) => {
    R(ue), ue.current = setTimeout(() => {
      _e.current || $e(false);
    }, e2);
  }, ze = (e2) => {
    var t3;
    if (!e2) return;
    const o2 = null !== (t3 = e2.currentTarget) && void 0 !== t3 ? t3 : e2.target;
    if (!(null == o2 ? void 0 : o2.isConnected)) return te(null), void Ae({ current: null });
    A2 ? je() : $e(true), te(o2), Ae({ current: o2 }), R(ue);
  }, De = () => {
    $2 ? Be(O2 || 100) : O2 ? Be() : $e(false), R(ae);
  }, qe = ({ x: e2, y: t3 }) => {
    var o2;
    const l3 = { getBoundingClientRect: () => ({ x: e2, y: t3, width: 0, height: 0, top: t3, left: e2, right: e2, bottom: t3 }) };
    E({ place: null !== (o2 = null == we ? void 0 : we.place) && void 0 !== o2 ? o2 : v, offset: m, elementReference: l3, tooltipReference: ce.current, tooltipArrowReference: se.current, strategy: b2, middlewares: S2, border: oe }).then((e3) => {
      Ie(e3);
    });
  }, He = (e2) => {
    if (!e2) return;
    const t3 = e2, o2 = { x: t3.clientX, y: t3.clientY };
    qe(o2), ge.current = o2;
  }, Me = (e2) => {
    var t3;
    if (!me) return;
    const o2 = e2.target;
    if (!o2.isConnected) return;
    if (null === (t3 = ce.current) || void 0 === t3 ? void 0 : t3.contains(o2)) return;
    [document.querySelector(`[id='${d}']`), ...Oe].some((e3) => null == e3 ? void 0 : e3.contains(o2)) || ($e(false), R(ae));
  }, We = _(ze, 50, true), Pe = _(De, 50, true), Ve = (e2) => {
    Pe.cancel(), We(e2);
  }, Fe = () => {
    We.cancel(), Pe();
  }, Ke = (0, import_react.useCallback)(() => {
    var e2, t3;
    const o2 = null !== (e2 = null == we ? void 0 : we.position) && void 0 !== e2 ? e2 : F;
    o2 ? qe(o2) : T2 ? ge.current && qe(ge.current) : (null == ee ? void 0 : ee.isConnected) && E({ place: null !== (t3 = null == we ? void 0 : we.place) && void 0 !== t3 ? t3 : v, offset: m, elementReference: ee, tooltipReference: ce.current, tooltipArrowReference: se.current, strategy: b2, middlewares: S2, border: oe }).then((e3) => {
      Te.current && Ie(e3);
    });
  }, [me, ee, Y, V, v, null == we ? void 0 : we.place, m, b2, F, null == we ? void 0 : we.position, T2]);
  (0, import_react.useEffect)(() => {
    var e2, t3;
    const o2 = new Set(Ee);
    Oe.forEach((e3) => {
      (null == X ? void 0 : X(e3)) || o2.add({ current: e3 });
    });
    const l3 = document.querySelector(`[id='${d}']`);
    l3 && !(null == X ? void 0 : X(l3)) && o2.add({ current: l3 });
    const r2 = () => {
      $e(false);
    }, n2 = L(ee), i3 = L(ce.current);
    Ne.scroll && (window.addEventListener("scroll", r2), null == n2 || n2.addEventListener("scroll", r2), null == i3 || i3.addEventListener("scroll", r2));
    let c3 = null;
    Ne.resize ? window.addEventListener("resize", r2) : ee && ce.current && (c3 = autoUpdate(ee, ce.current, Ke, { ancestorResize: true, elementResize: true, layoutShift: true }));
    const s2 = (e3) => {
      "Escape" === e3.key && $e(false);
    };
    Ne.escape && window.addEventListener("keydown", s2), Ne.clickOutsideAnchor && window.addEventListener("click", Me);
    const a2 = [], u2 = (e3) => {
      me && (null == e3 ? void 0 : e3.target) === ee || ze(e3);
    }, p2 = (e3) => {
      me && (null == e3 ? void 0 : e3.target) === ee && De();
    }, v2 = ["mouseover", "mouseout", "mouseenter", "mouseleave", "focus", "blur"], m2 = ["click", "dblclick", "mousedown", "mouseup"];
    Object.entries(Re).forEach(([e3, t4]) => {
      t4 && (v2.includes(e3) ? a2.push({ event: e3, listener: Ve }) : m2.includes(e3) && a2.push({ event: e3, listener: u2 }));
    }), Object.entries(xe).forEach(([e3, t4]) => {
      t4 && (v2.includes(e3) ? a2.push({ event: e3, listener: Fe }) : m2.includes(e3) && a2.push({ event: e3, listener: p2 }));
    }), T2 && a2.push({ event: "pointermove", listener: He });
    const y2 = () => {
      _e.current = true;
    }, h3 = () => {
      _e.current = false, De();
    };
    return $2 && !Ce && (null === (e2 = ce.current) || void 0 === e2 || e2.addEventListener("mouseenter", y2), null === (t3 = ce.current) || void 0 === t3 || t3.addEventListener("mouseleave", h3)), a2.forEach(({ event: e3, listener: t4 }) => {
      o2.forEach((o3) => {
        var l4;
        null === (l4 = o3.current) || void 0 === l4 || l4.addEventListener(e3, t4);
      });
    }), () => {
      var e3, t4;
      Ne.scroll && (window.removeEventListener("scroll", r2), null == n2 || n2.removeEventListener("scroll", r2), null == i3 || i3.removeEventListener("scroll", r2)), Ne.resize ? window.removeEventListener("resize", r2) : null == c3 || c3(), Ne.clickOutsideAnchor && window.removeEventListener("click", Me), Ne.escape && window.removeEventListener("keydown", s2), $2 && !Ce && (null === (e3 = ce.current) || void 0 === e3 || e3.removeEventListener("mouseenter", y2), null === (t4 = ce.current) || void 0 === t4 || t4.removeEventListener("mouseleave", h3)), a2.forEach(({ event: e4, listener: t5 }) => {
        o2.forEach((o3) => {
          var l4;
          null === (l4 = o3.current) || void 0 === l4 || l4.removeEventListener(e4, t5);
        });
      });
    };
  }, [ee, Ke, ye, Ee, Oe, H2, M2, W, Le, A2, O2]), (0, import_react.useEffect)(() => {
    var e2, t3;
    let o2 = null !== (t3 = null !== (e2 = null == we ? void 0 : we.anchorSelect) && void 0 !== e2 ? e2 : p) && void 0 !== t3 ? t3 : "";
    !o2 && l2 && (o2 = `[data-tooltip-id='${l2.replace(/'/g, "\\'")}']`);
    const r2 = new MutationObserver((e3) => {
      const t4 = [], r3 = [];
      e3.forEach((e4) => {
        if ("attributes" === e4.type && "data-tooltip-id" === e4.attributeName) {
          e4.target.getAttribute("data-tooltip-id") === l2 ? t4.push(e4.target) : e4.oldValue === l2 && r3.push(e4.target);
        }
        if ("childList" === e4.type) {
          if (ee) {
            const t5 = [...e4.removedNodes].filter((e5) => 1 === e5.nodeType);
            if (o2) try {
              r3.push(...t5.filter((e5) => e5.matches(o2))), r3.push(...t5.flatMap((e5) => [...e5.querySelectorAll(o2)]));
            } catch (e5) {
            }
            t5.some((e5) => {
              var t6;
              return !!(null === (t6 = null == e5 ? void 0 : e5.contains) || void 0 === t6 ? void 0 : t6.call(e5, ee)) && (he(false), $e(false), te(null), R(ae), R(ue), true);
            });
          }
          if (o2) try {
            const l3 = [...e4.addedNodes].filter((e5) => 1 === e5.nodeType);
            t4.push(...l3.filter((e5) => e5.matches(o2))), t4.push(...l3.flatMap((e5) => [...e5.querySelectorAll(o2)]));
          } catch (e5) {
          }
        }
      }), (t4.length || r3.length) && ke((e4) => [...e4.filter((e5) => !r3.includes(e5)), ...t4]);
    });
    return r2.observe(document.body, { childList: true, subtree: true, attributes: true, attributeFilter: ["data-tooltip-id"], attributeOldValue: true }), () => {
      r2.disconnect();
    };
  }, [l2, p, null == we ? void 0 : we.anchorSelect, ee]), (0, import_react.useEffect)(() => {
    Ke();
  }, [Ke]), (0, import_react.useEffect)(() => {
    if (!(null == G ? void 0 : G.current)) return () => null;
    const e2 = new ResizeObserver(() => {
      setTimeout(() => Ke());
    });
    return e2.observe(G.current), () => {
      e2.disconnect();
    };
  }, [Y, null == G ? void 0 : G.current]), (0, import_react.useEffect)(() => {
    var e2;
    const t3 = document.querySelector(`[id='${d}']`), o2 = [...Oe, t3];
    ee && o2.includes(ee) || te(null !== (e2 = Oe[0]) && void 0 !== e2 ? e2 : t3);
  }, [d, Oe, ee]), (0, import_react.useEffect)(() => (J && $e(true), () => {
    R(ae), R(ue);
  }), []), (0, import_react.useEffect)(() => {
    var e2;
    let t3 = null !== (e2 = null == we ? void 0 : we.anchorSelect) && void 0 !== e2 ? e2 : p;
    if (!t3 && l2 && (t3 = `[data-tooltip-id='${l2.replace(/'/g, "\\'")}']`), t3) try {
      const e3 = Array.from(document.querySelectorAll(t3));
      ke(e3);
    } catch (e3) {
      ke([]);
    }
  }, [l2, p, null == we ? void 0 : we.anchorSelect]), (0, import_react.useEffect)(() => {
    ae.current && (R(ae), je(A2));
  }, [A2]);
  const Ue = null !== (ie = null == we ? void 0 : we.content) && void 0 !== ie ? ie : Y, Xe = me && Object.keys(pe.tooltipStyles).length > 0;
  return (0, import_react.useImperativeHandle)(t2, () => ({ open: (e2) => {
    if (null == e2 ? void 0 : e2.anchorSelect) try {
      document.querySelector(e2.anchorSelect);
    } catch (t3) {
      return void console.warn(`[react-tooltip] "${e2.anchorSelect}" is not a valid CSS selector`);
    }
    be(null != e2 ? e2 : null), (null == e2 ? void 0 : e2.delay) ? je(e2.delay) : $e(true);
  }, close: (e2) => {
    (null == e2 ? void 0 : e2.delay) ? Be(e2.delay) : $e(false);
  }, activeAnchor: ee, place: pe.place, isOpen: Boolean(ye && !x2 && Ue && Xe) })), ye && !x2 && Ue ? import_react.default.createElement(g2, { id: l2, role: ne, className: (0, import_classnames.default)("react-tooltip", z.tooltip, D.tooltip, D[u], i2, `react-tooltip__place-${pe.place}`, z[Xe ? "show" : "closing"], Xe ? "react-tooltip__show" : "react-tooltip__closing", "fixed" === b2 && z.fixed, $2 && z.clickable), onTransitionEnd: (e2) => {
    R(de), me || "opacity" !== e2.propertyName || (he(false), be(null), null == U || U());
  }, style: { ...V, ...pe.tooltipStyles, opacity: void 0 !== le && Xe ? le : void 0 }, ref: ce }, Ue, import_react.default.createElement(g2, { className: (0, import_classnames.default)("react-tooltip-arrow", z.arrow, D.arrow, c2, N2 && z.noArrow), style: { ...pe.tooltipArrowStyles, background: re ? `linear-gradient(to right bottom, transparent 50%, ${re} 50%)` : void 0 }, ref: se })) : null;
};
var H = ({ content: t2 }) => import_react.default.createElement("span", { dangerouslySetInnerHTML: { __html: t2 } });
var M = import_react.default.forwardRef(({ id: t2, anchorId: l2, anchorSelect: n2, content: i2, html: c2, render: a2, className: u, classNameArrow: d, variant: p = "dark", place: v = "top", offset: m = 10, wrapper: f = "div", children: h2 = null, events: w2 = ["hover"], openOnClick: b2 = false, positionStrategy: S2 = "absolute", middlewares: g2, delayShow: E2 = 0, delayHide: _2 = 0, float: O2 = false, hidden: k2 = false, noArrow: T2 = false, clickable: L2 = false, closeOnEsc: C2 = false, closeOnScroll: R2 = false, closeOnResize: x2 = false, openEvents: N2, closeEvents: $2, globalCloseEvents: I2, imperativeModeOnly: B2 = false, style: z2, position: D2, isOpen: M2, defaultIsOpen: W = false, disableStyleInjection: P = false, border: V, opacity: F, arrowColor: K, setIsOpen: U, afterShow: X, afterHide: Y, disableTooltip: G, role: Z = "tooltip" }, J) => {
  const [Q, ee] = (0, import_react.useState)(i2), [te, oe] = (0, import_react.useState)(c2), [le, re] = (0, import_react.useState)(v), [ne, ie] = (0, import_react.useState)(p), [ce, se] = (0, import_react.useState)(m), [ae, ue] = (0, import_react.useState)(E2), [de, pe] = (0, import_react.useState)(_2), [ve, me] = (0, import_react.useState)(O2), [fe, ye] = (0, import_react.useState)(k2), [he, we] = (0, import_react.useState)(f), [be, Se] = (0, import_react.useState)(w2), [ge, Ee] = (0, import_react.useState)(S2), [Ae, _e] = (0, import_react.useState)(null), [Oe, ke] = (0, import_react.useState)(null), Te = (0, import_react.useRef)(P), { anchorRefs: Le, activeAnchor: Ce } = j(t2), Re = (e2) => null == e2 ? void 0 : e2.getAttributeNames().reduce((t3, o2) => {
    var l3;
    if (o2.startsWith("data-tooltip-")) {
      t3[o2.replace(/^data-tooltip-/, "")] = null !== (l3 = null == e2 ? void 0 : e2.getAttribute(o2)) && void 0 !== l3 ? l3 : null;
    }
    return t3;
  }, {}), xe = (e2) => {
    const t3 = { place: (e3) => {
      var t4;
      re(null !== (t4 = e3) && void 0 !== t4 ? t4 : v);
    }, content: (e3) => {
      ee(null != e3 ? e3 : i2);
    }, html: (e3) => {
      oe(null != e3 ? e3 : c2);
    }, variant: (e3) => {
      var t4;
      ie(null !== (t4 = e3) && void 0 !== t4 ? t4 : p);
    }, offset: (e3) => {
      se(null === e3 ? m : Number(e3));
    }, wrapper: (e3) => {
      var t4;
      we(null !== (t4 = e3) && void 0 !== t4 ? t4 : f);
    }, events: (e3) => {
      const t4 = null == e3 ? void 0 : e3.split(" ");
      Se(null != t4 ? t4 : w2);
    }, "position-strategy": (e3) => {
      var t4;
      Ee(null !== (t4 = e3) && void 0 !== t4 ? t4 : S2);
    }, "delay-show": (e3) => {
      ue(null === e3 ? E2 : Number(e3));
    }, "delay-hide": (e3) => {
      pe(null === e3 ? _2 : Number(e3));
    }, float: (e3) => {
      me(null === e3 ? O2 : "true" === e3);
    }, hidden: (e3) => {
      ye(null === e3 ? k2 : "true" === e3);
    }, "class-name": (e3) => {
      _e(e3);
    } };
    Object.values(t3).forEach((e3) => e3(null)), Object.entries(e2).forEach(([e3, o2]) => {
      var l3;
      null === (l3 = t3[e3]) || void 0 === l3 || l3.call(t3, o2);
    });
  };
  (0, import_react.useEffect)(() => {
    ee(i2);
  }, [i2]), (0, import_react.useEffect)(() => {
    oe(c2);
  }, [c2]), (0, import_react.useEffect)(() => {
    re(v);
  }, [v]), (0, import_react.useEffect)(() => {
    ie(p);
  }, [p]), (0, import_react.useEffect)(() => {
    se(m);
  }, [m]), (0, import_react.useEffect)(() => {
    ue(E2);
  }, [E2]), (0, import_react.useEffect)(() => {
    pe(_2);
  }, [_2]), (0, import_react.useEffect)(() => {
    me(O2);
  }, [O2]), (0, import_react.useEffect)(() => {
    ye(k2);
  }, [k2]), (0, import_react.useEffect)(() => {
    Ee(S2);
  }, [S2]), (0, import_react.useEffect)(() => {
    Te.current !== P && console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.");
  }, [P]), (0, import_react.useEffect)(() => {
    "undefined" != typeof window && window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles", { detail: { disableCore: "core" === P, disableBase: P } }));
  }, []), (0, import_react.useEffect)(() => {
    var e2;
    const o2 = new Set(Le);
    let r2 = n2;
    if (!r2 && t2 && (r2 = `[data-tooltip-id='${t2.replace(/'/g, "\\'")}']`), r2) try {
      document.querySelectorAll(r2).forEach((e3) => {
        o2.add({ current: e3 });
      });
    } catch (e3) {
      console.warn(`[react-tooltip] "${r2}" is not a valid CSS selector`);
    }
    const i3 = document.querySelector(`[id='${l2}']`);
    if (i3 && o2.add({ current: i3 }), !o2.size) return () => null;
    const c3 = null !== (e2 = null != Oe ? Oe : i3) && void 0 !== e2 ? e2 : Ce.current, s2 = new MutationObserver((e3) => {
      e3.forEach((e4) => {
        var t3;
        if (!c3 || "attributes" !== e4.type || !(null === (t3 = e4.attributeName) || void 0 === t3 ? void 0 : t3.startsWith("data-tooltip-"))) return;
        const o3 = Re(c3);
        xe(o3);
      });
    }), a3 = { attributes: true, childList: false, subtree: false };
    if (c3) {
      const e3 = Re(c3);
      xe(e3), s2.observe(c3, a3);
    }
    return () => {
      s2.disconnect();
    };
  }, [Le, Ce, Oe, l2, n2]), (0, import_react.useEffect)(() => {
    (null == z2 ? void 0 : z2.border) && console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."), V && !A("border", `${V}`) && console.warn(`[react-tooltip] "${V}" is not a valid \`border\`.`), (null == z2 ? void 0 : z2.opacity) && console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."), F && !A("opacity", `${F}`) && console.warn(`[react-tooltip] "${F}" is not a valid \`opacity\`.`);
  }, []);
  let Ne = h2;
  const $e = (0, import_react.useRef)(null);
  if (a2) {
    const t3 = a2({ content: (null == Oe ? void 0 : Oe.getAttribute("data-tooltip-content")) || Q || null, activeAnchor: Oe });
    Ne = t3 ? import_react.default.createElement("div", { ref: $e, className: "react-tooltip-content-wrapper" }, t3) : null;
  } else Q && (Ne = Q);
  te && (Ne = import_react.default.createElement(H, { content: te }));
  const Ie = { forwardRef: J, id: t2, anchorId: l2, anchorSelect: n2, className: (0, import_classnames.default)(u, Ae), classNameArrow: d, content: Ne, contentWrapperRef: $e, place: le, variant: ne, offset: ce, wrapper: he, events: be, openOnClick: b2, positionStrategy: ge, middlewares: g2, delayShow: ae, delayHide: de, float: ve, hidden: fe, noArrow: T2, clickable: L2, closeOnEsc: C2, closeOnScroll: R2, closeOnResize: x2, openEvents: N2, closeEvents: $2, globalCloseEvents: I2, imperativeModeOnly: B2, style: z2, position: D2, isOpen: M2, defaultIsOpen: W, border: V, opacity: F, arrowColor: K, setIsOpen: U, afterShow: X, afterHide: Y, disableTooltip: G, activeAnchor: Oe, setActiveAnchor: (e2) => ke(e2), role: Z };
  return import_react.default.createElement(q, { ...Ie });
});
"undefined" != typeof window && window.addEventListener("react-tooltip-inject-styles", (e2) => {
  e2.detail.disableCore || S({ css: `:root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}`, type: "core" }), e2.detail.disableBase || S({ css: `
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`, type: "base" });
});
export {
  M as Tooltip,
  I as TooltipProvider,
  B as TooltipWrapper,
  g as removeStyle
};
/*! Bundled license information:

react-tooltip/dist/react-tooltip.min.mjs:
  (*
  * React Tooltip
  * {@link https://github.com/ReactTooltip/react-tooltip}
  * @copyright ReactTooltip Team
  * @license MIT
  *)
*/
//# sourceMappingURL=react-tooltip.js.map

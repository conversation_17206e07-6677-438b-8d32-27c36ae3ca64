{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/tcl/tcl.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/tcl/tcl.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  tokenPostfix: \".tcl\",\n  specialFunctions: [\n    \"set\",\n    \"unset\",\n    \"rename\",\n    \"variable\",\n    \"proc\",\n    \"coroutine\",\n    \"foreach\",\n    \"incr\",\n    \"append\",\n    \"lappend\",\n    \"linsert\",\n    \"lreplace\"\n  ],\n  mainFunctions: [\n    \"if\",\n    \"then\",\n    \"elseif\",\n    \"else\",\n    \"case\",\n    \"switch\",\n    \"while\",\n    \"for\",\n    \"break\",\n    \"continue\",\n    \"return\",\n    \"package\",\n    \"namespace\",\n    \"catch\",\n    \"exit\",\n    \"eval\",\n    \"expr\",\n    \"uplevel\",\n    \"upvar\"\n  ],\n  builtinFunctions: [\n    \"file\",\n    \"info\",\n    \"concat\",\n    \"join\",\n    \"lindex\",\n    \"list\",\n    \"llength\",\n    \"lrange\",\n    \"lsearch\",\n    \"lsort\",\n    \"split\",\n    \"array\",\n    \"parray\",\n    \"binary\",\n    \"format\",\n    \"regexp\",\n    \"regsub\",\n    \"scan\",\n    \"string\",\n    \"subst\",\n    \"dict\",\n    \"cd\",\n    \"clock\",\n    \"exec\",\n    \"glob\",\n    \"pid\",\n    \"pwd\",\n    \"close\",\n    \"eof\",\n    \"fblocked\",\n    \"fconfigure\",\n    \"fcopy\",\n    \"fileevent\",\n    \"flush\",\n    \"gets\",\n    \"open\",\n    \"puts\",\n    \"read\",\n    \"seek\",\n    \"socket\",\n    \"tell\",\n    \"interp\",\n    \"after\",\n    \"auto_execok\",\n    \"auto_load\",\n    \"auto_mkindex\",\n    \"auto_reset\",\n    \"bgerror\",\n    \"error\",\n    \"global\",\n    \"history\",\n    \"load\",\n    \"source\",\n    \"time\",\n    \"trace\",\n    \"unknown\",\n    \"unset\",\n    \"update\",\n    \"vwait\",\n    \"winfo\",\n    \"wm\",\n    \"bind\",\n    \"event\",\n    \"pack\",\n    \"place\",\n    \"grid\",\n    \"font\",\n    \"bell\",\n    \"clipboard\",\n    \"destroy\",\n    \"focus\",\n    \"grab\",\n    \"lower\",\n    \"option\",\n    \"raise\",\n    \"selection\",\n    \"send\",\n    \"tk\",\n    \"tkwait\",\n    \"tk_bisque\",\n    \"tk_focusNext\",\n    \"tk_focusPrev\",\n    \"tk_focusFollowsMouse\",\n    \"tk_popup\",\n    \"tk_setPalette\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@specialFunctions\": {\n              token: \"keyword.flow\",\n              next: \"@specialFunc\"\n            },\n            \"@mainFunctions\": \"keyword\",\n            \"@builtinFunctions\": \"variable\",\n            \"@default\": \"operator.scss\"\n          }\n        }\n      ],\n      [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, \"metatag\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"operator\"],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/\\.(?!\\d|\\.)[\\w\\-]*/, \"operator.sql\"],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number\"],\n      [/\\d+/, \"number\"],\n      // delimiter\n      [/;/, \"delimiter\"],\n      // strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@dstring\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }]\n    ],\n    dstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    sstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#.*\\\\$/, { token: \"comment\", next: \"@newlineComment\" }],\n      [/#.*(?!\\\\)$/, \"comment\"]\n    ],\n    newlineComment: [\n      [/.*\\\\$/, \"comment\"],\n      [/.*(?!\\\\)$/, { token: \"comment\", next: \"@pop\" }]\n    ],\n    nestedVariable: [\n      [/[^\\{\\}\\$]+/, \"type.identifier\"],\n      [/\\}/, { token: \"identifier\", next: \"@pop\" }]\n    ],\n    nestedCall: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\]/, { token: \"@brackets\", next: \"@pop\" }],\n      { include: \"root\" }\n    ],\n    specialFunc: [\n      [/\"/, { token: \"string\", next: \"@dstring\" }],\n      [/'/, { token: \"string\", next: \"@sstring\" }],\n      [/\\S+/, { token: \"type\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAqBA;AA9BJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,kBAAkB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAS;AAAA,MACT,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,MACrD;AAAA,MACA,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,qBAAqB;AAAA,kBACnB,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,kBAAkB;AAAA,gBAClB,qBAAqB;AAAA,gBACrB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,2BAA2B,SAAS;AAAA;AAAA,UAErC,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,YAAY,UAAU;AAAA,UACvB,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,UACnE,CAAC,cAAc,iBAAiB;AAAA,UAChC,CAAC,sBAAsB,cAAc;AAAA;AAAA,UAErC,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,OAAO,QAAQ;AAAA;AAAA,UAEhB,CAAC,KAAK,WAAW;AAAA;AAAA,UAEjB,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,UACnE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,QACrE;AAAA,QACA,SAAS;AAAA,UACP,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,UAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,UACnE,CAAC,cAAc,iBAAiB;AAAA,UAChC,CAAC,gBAAgB,QAAQ;AAAA,UACzB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,SAAS;AAAA,UACP,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,UAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,UACnE,CAAC,cAAc,iBAAiB;AAAA,UAChC,CAAC,gBAAgB,QAAQ;AAAA,UACzB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,OAAO;AAAA,UACtB,CAAC,UAAU,EAAE,OAAO,WAAW,MAAM,kBAAkB,CAAC;AAAA,UACxD,CAAC,cAAc,SAAS;AAAA,QAC1B;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,SAAS,SAAS;AAAA,UACnB,CAAC,aAAa,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,QAClD;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,cAAc,iBAAiB;AAAA,UAChC,CAAC,MAAM,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,QAC9C;AAAA,QACA,YAAY;AAAA,UACV,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,UAClD,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,UAC3C,EAAE,SAAS,OAAO;AAAA,QACpB;AAAA,QACA,aAAa;AAAA,UACX,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,UAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,UAC3C,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
import {
  cookieSchema
} from "./chunk-ERAQ5RRJ.js";
import {
  REQUEST_METHODS,
  f,
  getHttpMethodInfo,
  isDefined,
  s,
  shouldUseProxy
} from "./chunk-K3FRCNXE.js";
import {
  P,
  computed,
  createBaseVNode,
  createBlock,
  createElementBlock,
  defineComponent,
  i,
  normalizeClass,
  openBlock,
  r,
  toDisplayString,
  unref,
  withCtx
} from "./chunk-D5ZO4EYM.js";
import {
  __commonJS
} from "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/utils.js
var require_utils = __commonJS({
  "node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/utils.js"(exports) {
    "use strict";
    exports.removeLeadingAndTrailingHTTPWhitespace = (string) => {
      return string.replace(/^[ \t\n\r]+/u, "").replace(/[ \t\n\r]+$/u, "");
    };
    exports.removeTrailingHTTPWhitespace = (string) => {
      return string.replace(/[ \t\n\r]+$/u, "");
    };
    exports.isHTTPWhitespaceChar = (char) => {
      return char === " " || char === "	" || char === "\n" || char === "\r";
    };
    exports.solelyContainsHTTPTokenCodePoints = (string) => {
      return /^[-!#$%&'*+.^_`|~A-Za-z0-9]*$/u.test(string);
    };
    exports.soleyContainsHTTPQuotedStringTokenCodePoints = (string) => {
      return /^[\t\u0020-\u007E\u0080-\u00FF]*$/u.test(string);
    };
    exports.asciiLowercase = (string) => {
      return string.replace(/[A-Z]/ug, (l2) => l2.toLowerCase());
    };
    exports.collectAnHTTPQuotedString = (input, position) => {
      let value = "";
      position++;
      while (true) {
        while (position < input.length && input[position] !== '"' && input[position] !== "\\") {
          value += input[position];
          ++position;
        }
        if (position >= input.length) {
          break;
        }
        const quoteOrBackslash = input[position];
        ++position;
        if (quoteOrBackslash === "\\") {
          if (position >= input.length) {
            value += "\\";
            break;
          }
          value += input[position];
          ++position;
        } else {
          break;
        }
      }
      return [value, position];
    };
  }
});

// node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/mime-type-parameters.js
var require_mime_type_parameters = __commonJS({
  "node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/mime-type-parameters.js"(exports, module) {
    "use strict";
    var {
      asciiLowercase,
      solelyContainsHTTPTokenCodePoints,
      soleyContainsHTTPQuotedStringTokenCodePoints
    } = require_utils();
    module.exports = class MIMETypeParameters {
      constructor(map) {
        this._map = map;
      }
      get size() {
        return this._map.size;
      }
      get(name) {
        name = asciiLowercase(String(name));
        return this._map.get(name);
      }
      has(name) {
        name = asciiLowercase(String(name));
        return this._map.has(name);
      }
      set(name, value) {
        name = asciiLowercase(String(name));
        value = String(value);
        if (!solelyContainsHTTPTokenCodePoints(name)) {
          throw new Error(`Invalid MIME type parameter name "${name}": only HTTP token code points are valid.`);
        }
        if (!soleyContainsHTTPQuotedStringTokenCodePoints(value)) {
          throw new Error(`Invalid MIME type parameter value "${value}": only HTTP quoted-string token code points are valid.`);
        }
        return this._map.set(name, value);
      }
      clear() {
        this._map.clear();
      }
      delete(name) {
        name = asciiLowercase(String(name));
        return this._map.delete(name);
      }
      forEach(callbackFn, thisArg) {
        this._map.forEach(callbackFn, thisArg);
      }
      keys() {
        return this._map.keys();
      }
      values() {
        return this._map.values();
      }
      entries() {
        return this._map.entries();
      }
      [Symbol.iterator]() {
        return this._map[Symbol.iterator]();
      }
    };
  }
});

// node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/parser.js
var require_parser = __commonJS({
  "node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/parser.js"(exports, module) {
    "use strict";
    var {
      removeLeadingAndTrailingHTTPWhitespace,
      removeTrailingHTTPWhitespace,
      isHTTPWhitespaceChar,
      solelyContainsHTTPTokenCodePoints,
      soleyContainsHTTPQuotedStringTokenCodePoints,
      asciiLowercase,
      collectAnHTTPQuotedString
    } = require_utils();
    module.exports = (input) => {
      input = removeLeadingAndTrailingHTTPWhitespace(input);
      let position = 0;
      let type = "";
      while (position < input.length && input[position] !== "/") {
        type += input[position];
        ++position;
      }
      if (type.length === 0 || !solelyContainsHTTPTokenCodePoints(type)) {
        return null;
      }
      if (position >= input.length) {
        return null;
      }
      ++position;
      let subtype = "";
      while (position < input.length && input[position] !== ";") {
        subtype += input[position];
        ++position;
      }
      subtype = removeTrailingHTTPWhitespace(subtype);
      if (subtype.length === 0 || !solelyContainsHTTPTokenCodePoints(subtype)) {
        return null;
      }
      const mimeType = {
        type: asciiLowercase(type),
        subtype: asciiLowercase(subtype),
        parameters: /* @__PURE__ */ new Map()
      };
      while (position < input.length) {
        ++position;
        while (isHTTPWhitespaceChar(input[position])) {
          ++position;
        }
        let parameterName = "";
        while (position < input.length && input[position] !== ";" && input[position] !== "=") {
          parameterName += input[position];
          ++position;
        }
        parameterName = asciiLowercase(parameterName);
        if (position < input.length) {
          if (input[position] === ";") {
            continue;
          }
          ++position;
        }
        let parameterValue = null;
        if (input[position] === '"') {
          [parameterValue, position] = collectAnHTTPQuotedString(input, position);
          while (position < input.length && input[position] !== ";") {
            ++position;
          }
        } else {
          parameterValue = "";
          while (position < input.length && input[position] !== ";") {
            parameterValue += input[position];
            ++position;
          }
          parameterValue = removeTrailingHTTPWhitespace(parameterValue);
          if (parameterValue === "") {
            continue;
          }
        }
        if (parameterName.length > 0 && solelyContainsHTTPTokenCodePoints(parameterName) && soleyContainsHTTPQuotedStringTokenCodePoints(parameterValue) && !mimeType.parameters.has(parameterName)) {
          mimeType.parameters.set(parameterName, parameterValue);
        }
      }
      return mimeType;
    };
  }
});

// node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/serializer.js
var require_serializer = __commonJS({
  "node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/serializer.js"(exports, module) {
    "use strict";
    var { solelyContainsHTTPTokenCodePoints } = require_utils();
    module.exports = (mimeType) => {
      let serialization = `${mimeType.type}/${mimeType.subtype}`;
      if (mimeType.parameters.size === 0) {
        return serialization;
      }
      for (let [name, value] of mimeType.parameters) {
        serialization += ";";
        serialization += name;
        serialization += "=";
        if (!solelyContainsHTTPTokenCodePoints(value) || value.length === 0) {
          value = value.replace(/(["\\])/ug, "\\$1");
          value = `"${value}"`;
        }
        serialization += value;
      }
      return serialization;
    };
  }
});

// node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/mime-type.js
var require_mime_type = __commonJS({
  "node_modules/@scalar/api-client/node_modules/whatwg-mimetype/lib/mime-type.js"(exports, module) {
    "use strict";
    var MIMETypeParameters = require_mime_type_parameters();
    var parse = require_parser();
    var serialize = require_serializer();
    var {
      asciiLowercase,
      solelyContainsHTTPTokenCodePoints
    } = require_utils();
    module.exports = class MIMEType {
      constructor(string) {
        string = String(string);
        const result = parse(string);
        if (result === null) {
          throw new Error(`Could not parse MIME type string "${string}"`);
        }
        this._type = result.type;
        this._subtype = result.subtype;
        this._parameters = new MIMETypeParameters(result.parameters);
      }
      static parse(string) {
        try {
          return new this(string);
        } catch (e2) {
          return null;
        }
      }
      get essence() {
        return `${this.type}/${this.subtype}`;
      }
      get type() {
        return this._type;
      }
      set type(value) {
        value = asciiLowercase(String(value));
        if (value.length === 0) {
          throw new Error("Invalid type: must be a non-empty string");
        }
        if (!solelyContainsHTTPTokenCodePoints(value)) {
          throw new Error(`Invalid type ${value}: must contain only HTTP token code points`);
        }
        this._type = value;
      }
      get subtype() {
        return this._subtype;
      }
      set subtype(value) {
        value = asciiLowercase(String(value));
        if (value.length === 0) {
          throw new Error("Invalid subtype: must be a non-empty string");
        }
        if (!solelyContainsHTTPTokenCodePoints(value)) {
          throw new Error(`Invalid subtype ${value}: must contain only HTTP token code points`);
        }
        this._subtype = value;
      }
      get parameters() {
        return this._parameters;
      }
      toString() {
        return serialize(this);
      }
      isJavaScript({ prohibitParameters = false } = {}) {
        switch (this._type) {
          case "text": {
            switch (this._subtype) {
              case "ecmascript":
              case "javascript":
              case "javascript1.0":
              case "javascript1.1":
              case "javascript1.2":
              case "javascript1.3":
              case "javascript1.4":
              case "javascript1.5":
              case "jscript":
              case "livescript":
              case "x-ecmascript":
              case "x-javascript": {
                return !prohibitParameters || this._parameters.size === 0;
              }
              default: {
                return false;
              }
            }
          }
          case "application": {
            switch (this._subtype) {
              case "ecmascript":
              case "javascript":
              case "x-ecmascript":
              case "x-javascript": {
                return !prohibitParameters || this._parameters.size === 0;
              }
              default: {
                return false;
              }
            }
          }
          default: {
            return false;
          }
        }
      }
      isXML() {
        return this._subtype === "xml" && (this._type === "text" || this._type === "application") || this._subtype.endsWith("+xml");
      }
      isHTML() {
        return this._subtype === "html" && this._type === "text";
      }
    };
  }
});

// node_modules/@scalar/api-client/dist/libs/send-request/set-request-cookies.js
var l = "/";
function C({
  example: a,
  env: t,
  globalCookies: e2,
  serverUrl: o,
  proxyUrl: r2
}) {
  const n = [], h = shouldUseProxy(r2, o), i2 = W(
    h ? r2 : o ?? "http://localhost"
  );
  return e2.forEach((s2) => {
    const { name: c, value: u, domain: m, ...d } = s2;
    !k(o, m) || !c || n.push(
      cookieSchema.parse({
        name: c,
        value: u,
        domain: m,
        path: d.path
      })
    );
  }), a.parameters.cookies.forEach((s2) => {
    !s2.enabled || !s2.key || n.push(
      cookieSchema.parse({
        name: s2.key,
        value: f(s2.value, t),
        domain: i2,
        path: l
      })
    );
  }), {
    cookieParams: n
  };
}
var W = (a) => {
  const t = new URL(a.startsWith("http") ? a : `http://${a}`).hostname;
  return t.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/) || t.match(/^[a-fA-F0-9:]+$/) || t.startsWith(".") ? t : `.${t}`;
};
var k = (a, t) => {
  if (!a || !t) return true;
  try {
    const e2 = a.startsWith("http") ? a : `http://${a}`, o = new URL(e2).hostname, r2 = !t, n = t === o, h = t.startsWith(".") && t === `.${o}`, i2 = t.startsWith(".") && (o == null ? void 0 : o.endsWith(t));
    return r2 || n || i2 || h;
  } catch {
    return false;
  }
};
var D = (a, t) => {
  const e2 = a.map((o) => `${o.name}=${o.value}`).join("; ");
  return t ? `${t}; ${e2}`.trim() : e2.trim();
};

// node_modules/@scalar/api-client/dist/libs/send-request/build-request-security.js
var $ = (l2 = [], i2 = {}, s2 = "") => {
  const t = {}, e2 = [], f2 = new URLSearchParams();
  return l2.forEach((a) => {
    var p;
    if (a.type === "apiKey") {
      const o = f(a.value, i2) || s2;
      a.in === "header" && (t[a.name] = o), a.in === "query" && f2.append(a.name, o), a.in === "cookie" && e2.push(
        cookieSchema.parse({
          uid: a.uid,
          name: a.name,
          value: o,
          path: "/"
        })
      );
    }
    if (a.type === "http")
      if (a.scheme === "basic") {
        const o = f(a.username, i2), u = f(a.password, i2), r2 = `${o}:${u}`;
        t.Authorization = `Basic ${r2 === ":" ? "username:password" : btoa(r2)}`;
      } else {
        const o = f(a.token, i2);
        t.Authorization = `Bearer ${o || s2}`;
      }
    if (a.type === "oauth2") {
      const u = (p = Object.values(a.flows).filter(isDefined).find((r2) => r2.token)) == null ? void 0 : p.token;
      t.Authorization = `Bearer ${u || s2}`;
    }
  }), { headers: t, cookies: e2, urlParams: f2 };
};

// node_modules/@scalar/api-client/dist/components/HttpMethod/HttpMethod.vue2.js
var H = defineComponent({
  __name: "HttpMethod",
  props: {
    isSquare: { type: Boolean, default: false },
    method: {},
    isEditable: { type: Boolean, default: false }
  },
  emits: ["change"],
  setup(b, { emit: h }) {
    const r2 = b, v = h, o = computed(() => getHttpMethodInfo(r2.method)), n = Object.entries(REQUEST_METHODS).map(([e2]) => ({
      id: e2,
      label: e2.toUpperCase(),
      color: getHttpMethodInfo(e2).color
    })), i2 = computed({
      get: () => n.find(({ id: e2 }) => e2 === r2.method),
      set: (e2) => (e2 == null ? void 0 : e2.id) && v("change", e2.id)
    }), d = i({
      base: "text-center font-code text-3xs justify-center items-center flex",
      variants: {
        isSquare: {
          true: "px-2.5 whitespace-nowrap font-bold border-r h-fit m-auto",
          false: "rounded-full"
        },
        isEditable: {
          true: "http-bg-gradient rounded-md border-1/2 border-r-1/2",
          false: "cursor-auto"
        }
      }
    }), E = computed(() => o.value.short);
    return (e2, u) => e2.isEditable ? (openBlock(), createBlock(unref(P), {
      key: 0,
      modelValue: i2.value,
      "onUpdate:modelValue": u[0] || (u[0] = (S) => i2.value = S),
      class: "font-code scalar-client mt-1 text-sm",
      options: unref(n)
    }, {
      default: withCtx(() => [
        createBaseVNode("div", {
          class: normalizeClass(["h-full", { "pointer-events-none": !e2.isEditable }])
        }, [
          createBaseVNode("button", {
            class: normalizeClass(["relative h-full", unref(r)(unref(d)({ isSquare: e2.isSquare, isEditable: e2.isEditable }), o.value.color)]),
            type: "button"
          }, [
            createBaseVNode("span", null, toDisplayString(E.value), 1)
          ], 2)
        ], 2)
      ]),
      _: 1
    }, 8, ["modelValue", "options"])) : (openBlock(), createElementBlock("div", {
      key: 1,
      class: normalizeClass(["relative gap-1 whitespace-nowrap", unref(r)(unref(d)({ isSquare: e2.isSquare, isEditable: e2.isEditable }), o.value.color)]),
      type: "button"
    }, toDisplayString(o.value.short), 3));
  }
});

// node_modules/@scalar/api-client/dist/components/HttpMethod/HttpMethod.vue.js
var e = s(H, [["__scopeId", "data-v-73e8dbd2"]]);

export {
  require_mime_type,
  e,
  C,
  k,
  D,
  $
};
//# sourceMappingURL=chunk-D7JDSFLT.js.map

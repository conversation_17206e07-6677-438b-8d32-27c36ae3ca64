{"version": 3, "sources": ["../../../../@graphiql/react/dist/codemirror.es.js"], "sourcesContent": ["import { r as requireCodemirror, g as getDefaultExportFromCjs } from \"./codemirror.es2.js\";\nfunction _mergeNamespaces(n, m) {\n  for (var i = 0; i < m.length; i++) {\n    const e = m[i];\n    if (typeof e !== \"string\" && !Array.isArray(e)) {\n      for (const k in e) {\n        if (k !== \"default\" && !(k in n)) {\n          const d = Object.getOwnPropertyDescriptor(e, k);\n          if (d) {\n            Object.defineProperty(n, k, d.get ? d : {\n              enumerable: true,\n              get: () => e[k]\n            });\n          }\n        }\n      }\n    }\n  }\n  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: \"Module\" }));\n}\nvar codemirrorExports = requireCodemirror();\nconst CodeMirror = /* @__PURE__ */ getDefaultExportFromCjs(codemirrorExports);\nconst codemirror = /* @__PURE__ */ _mergeNamespaces({\n  __proto__: null,\n  default: CodeMirror\n}, [codemirrorExports]);\nexport {\n  CodeMirror as C,\n  codemirror as c\n};\n//# sourceMappingURL=codemirror.es.js.map\n"], "mappings": ";;;;;;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAM,IAAI,EAAE,CAAC;AACb,QAAI,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC9C,iBAAW,KAAK,GAAG;AACjB,YAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AAChC,gBAAM,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC9C,cAAI,GAAG;AACL,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACtC,YAAY;AAAA,cACZ,KAAK,MAAM,EAAE,CAAC;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,CAAC;AACxF;AACA,IAAI,oBAAoB,kBAAkB;AAC1C,IAAM,aAA6B,wBAAwB,iBAAiB;AAC5E,IAAM,aAA6B,iBAAiB;AAAA,EAClD,WAAW;AAAA,EACX,SAAS;AACX,GAAG,CAAC,iBAAiB,CAAC;", "names": []}
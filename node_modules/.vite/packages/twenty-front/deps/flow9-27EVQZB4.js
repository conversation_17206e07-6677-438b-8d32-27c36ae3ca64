import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/flow9/flow9.js
var conf, language;
var init_flow9 = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/flow9/flow9.js"() {
    conf = {
      comments: {
        blockComment: ["/*", "*/"],
        lineComment: "//"
      },
      brackets: [
        ["{", "}"],
        ["[", "]"],
        ["(", ")"]
      ],
      autoClosingPairs: [
        { open: "{", close: "}", notIn: ["string"] },
        { open: "[", close: "]", notIn: ["string"] },
        { open: "(", close: ")", notIn: ["string"] },
        { open: '"', close: '"', notIn: ["string"] },
        { open: "'", close: "'", notIn: ["string"] }
      ],
      surroundingPairs: [
        { open: "{", close: "}" },
        { open: "[", close: "]" },
        { open: "(", close: ")" },
        { open: '"', close: '"' },
        { open: "'", close: "'" },
        { open: "<", close: ">" }
      ]
    };
    language = {
      defaultToken: "",
      tokenPostfix: ".flow",
      keywords: [
        "import",
        "require",
        "export",
        "forbid",
        "native",
        "if",
        "else",
        "cast",
        "unsafe",
        "switch",
        "default"
      ],
      types: [
        "io",
        "mutable",
        "bool",
        "int",
        "double",
        "string",
        "flow",
        "void",
        "ref",
        "true",
        "false",
        "with"
      ],
      operators: [
        "=",
        ">",
        "<",
        "<=",
        ">=",
        "==",
        "!",
        "!=",
        ":=",
        "::=",
        "&&",
        "||",
        "+",
        "-",
        "*",
        "/",
        "@",
        "&",
        "%",
        ":",
        "->",
        "\\",
        "$",
        "??",
        "^"
      ],
      symbols: /[@$=><!~?:&|+\-*\\\/\^%]+/,
      escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
      // The main tokenizer for our languages
      tokenizer: {
        root: [
          // identifiers and keywords
          [
            /[a-zA-Z_]\w*/,
            {
              cases: {
                "@keywords": "keyword",
                "@types": "type",
                "@default": "identifier"
              }
            }
          ],
          // whitespace
          { include: "@whitespace" },
          // delimiters and operators
          [/[{}()\[\]]/, "delimiter"],
          [/[<>](?!@symbols)/, "delimiter"],
          [
            /@symbols/,
            {
              cases: {
                "@operators": "delimiter",
                "@default": ""
              }
            }
          ],
          // numbers
          [/((0(x|X)[0-9a-fA-F]*)|(([0-9]+\.?[0-9]*)|(\.[0-9]+))((e|E)(\+|-)?[0-9]+)?)/, "number"],
          // delimiter: after number because of .\d floats
          [/[;,.]/, "delimiter"],
          // strings
          [/"([^"\\]|\\.)*$/, "string.invalid"],
          [/"/, "string", "@string"]
        ],
        whitespace: [
          [/[ \t\r\n]+/, ""],
          [/\/\*/, "comment", "@comment"],
          [/\/\/.*$/, "comment"]
        ],
        comment: [
          [/[^\/*]+/, "comment"],
          [/\*\//, "comment", "@pop"],
          [/[\/*]/, "comment"]
        ],
        string: [
          [/[^\\"]+/, "string"],
          [/@escapes/, "string.escape"],
          [/\\./, "string.escape.invalid"],
          [/"/, "string", "@pop"]
        ]
      }
    };
  }
});
init_flow9();
export {
  conf,
  language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/flow9/flow9.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=flow9-27EVQZB4.js.map

{"version": 3, "sources": ["../../../../ts-invariant/src/invariant.ts", "../../../../@apollo/src/utilities/globals/maybe.ts", "../../../../@apollo/src/utilities/common/makeUniqueId.ts", "../../../../@apollo/src/utilities/common/stringifyForDisplay.ts", "../../../../@apollo/src/version.ts", "../../../../@apollo/src/utilities/globals/global.ts", "../../../../@apollo/src/utilities/globals/invariantWrappers.ts", "../../../../@apollo/src/utilities/globals/index.ts"], "sourcesContent": ["const genericMessage = \"Invariant Violation\";\nconst {\n  setPrototypeOf = function (obj: any, proto: any) {\n    obj.__proto__ = proto;\n    return obj;\n  },\n} = Object as any;\n\nexport class InvariantError extends Error {\n  framesToPop = 1;\n  name = genericMessage;\n  constructor(message: string | number = genericMessage) {\n    super(\n      typeof message === \"number\"\n        ? `${genericMessage}: ${message} (see https://github.com/apollographql/invariant-packages)`\n        : message\n    );\n    setPrototypeOf(this, InvariantError.prototype);\n  }\n}\n\nexport function invariant(\n  condition: any,\n  message?: string | number,\n): asserts condition {\n  if (!condition) {\n    throw new InvariantError(message);\n  }\n}\n\nconst verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"] as const;\nexport type VerbosityLevel = (typeof verbosityLevels)[number];\nexport type ConsoleMethodName = Exclude<VerbosityLevel, \"silent\">;\nlet verbosityLevel = verbosityLevels.indexOf(\"log\");\n\nfunction wrapConsoleMethod<M extends ConsoleMethodName>(name: M) {\n  return function () {\n    if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n      // Default to console.log if this host environment happens not to provide\n      // all the console.* methods we need.\n      const method = console[name] || console.log;\n      return method.apply(console, arguments as any);\n    }\n  } as (typeof console)[M];\n}\n\nexport namespace invariant {\n  export const debug = wrapConsoleMethod(\"debug\");\n  export const log = wrapConsoleMethod(\"log\");\n  export const warn = wrapConsoleMethod(\"warn\");\n  export const error = wrapConsoleMethod(\"error\");\n}\n\nexport function setVerbosity(level: VerbosityLevel): VerbosityLevel {\n  const old = verbosityLevels[verbosityLevel];\n  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n  return old;\n}\n\nexport default invariant;\n", "export function maybe<T>(thunk: () => T): T | undefined {\n  try {\n    return thunk();\n  } catch {}\n}\n", "const prefixCounts = new Map<string, number>();\n\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix: string) {\n  const count = prefixCounts.get(prefix) || 1;\n  prefixCounts.set(prefix, count + 1);\n  return `${prefix}:${count}:${Math.random().toString(36).slice(2)}`;\n}\n", "import { makeUniqueId } from \"./makeUniqueId.js\";\n\nexport function stringifyForDisplay(value: any, space = 0): string {\n  const undefId = makeUniqueId(\"stringifyForDisplay\");\n  return JSON.stringify(\n    value,\n    (key, value) => {\n      return value === void 0 ? undefId : value;\n    },\n    space\n  )\n    .split(JSON.stringify(undefId))\n    .join(\"<undefined>\");\n}\n", "export const version = \"local\";\n", "import { maybe } from \"./maybe.js\";\n\ndeclare global {\n  const __DEV__: boolean; // will be removed in `dist` by the `postprocessDist` script\n  interface Window {\n    __DEV__?: boolean;\n  }\n}\n\nexport default (maybe(() => globalThis) ||\n  maybe(() => window) ||\n  maybe(() => self) ||\n  maybe(() => global) ||\n  // We don't expect the Function constructor ever to be invoked at runtime, as\n  // long as at least one of globalThis, window, self, or global is defined, so\n  // we are under no obligation to make it easy for static analysis tools to\n  // detect syntactic usage of the Function constructor. If you think you can\n  // improve your static analysis to detect this obfuscation, think again. This\n  // is an arms race you cannot win, at least not in JavaScript.\n  maybe(function () {\n    return maybe.constructor(\"return this\")();\n  })) as typeof globalThis & Window;\n", "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport type { ErrorCodes } from \"../../invariantErrorCodes.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\n\nfunction wrap(fn: (msg?: string, ...args: any[]) => void) {\n  return function (message?: string | number, ...args: any[]) {\n    if (typeof message === \"number\") {\n      const arg0 = message;\n      message = getHandledErrorMsg(arg0);\n      if (!message) {\n        message = getFallbackErrorMsg(arg0, args);\n        args = [];\n      }\n    }\n    fn(...[message].concat(args));\n  };\n}\n\ntype LogFunction = {\n  /**\n   * Logs a `$level` message if the user used `ts-invariant`'s `setVerbosity` to set\n   * a verbosity level of `$level` or lower. (defaults to `\"log\"`).\n   *\n   * The user will either be presented with a link to the documentation for the message,\n   * or they can use the `loadDevMessages` to add the message strings to the bundle.\n   * The documentation will display the message without argument substitution.\n   * Instead, the arguments will be printed on the console after the link.\n   *\n   * `message` can only be a string, a concatenation of strings, or a ternary statement\n   * that results in a string. This will be enforced on build, where the message will\n   * be replaced with a message number.\n   *\n   * String substitutions like %s, %o, %d or %f are supported.\n   */\n  (message?: any, ...optionalParams: unknown[]): void;\n};\n\ntype WrappedInvariant = {\n  /**\n   * Throws and InvariantError with the given message if the condition is false.\n   *\n   * `message` can only be a string, a concatenation of strings, or a ternary statement\n   * that results in a string. This will be enforced on build, where the message will\n   * be replaced with a message number.\n   *\n   * The user will either be presented with a link to the documentation for the message,\n   * or they can use the `loadErrorMessages` to add the message strings to the bundle.\n   * The documentation will display the message with the arguments substituted.\n   *\n   * String substitutions with %s are supported and will also return\n   * pretty-stringified objects.\n   * Excess `optionalParams` will be swallowed.\n   */\n  (\n    condition: any,\n    message?: string | number,\n    ...optionalParams: unknown[]\n  ): asserts condition;\n\n  debug: LogFunction;\n  log: LogFunction;\n  warn: LogFunction;\n  error: LogFunction;\n};\nconst invariant: WrappedInvariant = Object.assign(\n  function invariant(\n    condition: any,\n    message?: string | number,\n    ...args: unknown[]\n  ): asserts condition {\n    if (!condition) {\n      originalInvariant(\n        condition,\n        getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args)\n      );\n    }\n  },\n  {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n  }\n);\n\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(\n  message?: string | number,\n  ...optionalParams: unknown[]\n) {\n  return new InvariantError(\n    getHandledErrorMsg(message, optionalParams) ||\n      getFallbackErrorMsg(message, optionalParams)\n  );\n}\n\nconst ApolloErrorMessageHandler = Symbol.for(\n  \"ApolloErrorMessageHandler_\" + version\n);\ndeclare global {\n  interface Window {\n    [ApolloErrorMessageHandler]?: {\n      (message: string | number, args: string[]): string | undefined;\n    } & ErrorCodes;\n  }\n}\n\nfunction stringify(arg: any) {\n  if (typeof arg == \"string\") {\n    return arg;\n  }\n\n  try {\n    return stringifyForDisplay(arg, 2).slice(0, 1000);\n  } catch {\n    return \"<non-serializable>\";\n  }\n}\n\nfunction getHandledErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return (\n    global[ApolloErrorMessageHandler] &&\n    global[ApolloErrorMessageHandler](message, messageArgs.map(stringify))\n  );\n}\n\nfunction getFallbackErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return `An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#${encodeURIComponent(\n    JSON.stringify({\n      version,\n      message,\n      args: messageArgs.map(stringify),\n    })\n  )}`;\n}\n\nexport {\n  invariant,\n  InvariantError,\n  newInvariantError,\n  ApolloErrorMessageHandler,\n};\n", "import {\n  invariant,\n  newInvariantError,\n  InvariantError,\n} from \"./invariantWrappers.js\";\n\nexport { maybe } from \"./maybe.js\";\nexport { default as global } from \"./global.js\";\nexport { invariant, newInvariantError, InvariantError };\n\n/**\n * @deprecated we do not use this internally anymore,\n * it is just exported for backwards compatibility\n */\n// this file is extempt from automatic `__DEV__` replacement\n// so we have to write it out here\n// @ts-ignore\nexport const DEV = globalThis.__DEV__ !== false;\nexport { DEV as __DEV__ };\n"], "mappings": ";;;;;;;;;AAqBM,SAAU,UACd,WACA,SAAyB;AAEzB,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,eAAe,OAAO;;AAEpC;AAOA,SAAS,kBAA+C,MAAO;AAC7D,SAAO,WAAA;AACL,QAAI,gBAAgB,QAAQ,IAAI,KAAK,gBAAgB;AAGnD,UAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ;AACxC,aAAO,OAAO,MAAM,SAAS,SAAgB;;EAEjD;AACF;AASM,SAAU,aAAa,OAAqB;AAChD,MAAM,MAAM,gBAAgB,cAAc;AAC1C,mBAAiB,KAAK,IAAI,GAAG,gBAAgB,QAAQ,KAAK,CAAC;AAC3D,SAAO;AACT;IAzDM,gBAEJ,IAAA,gBAMF,gBAsBM,iBAGF;;;;AAjCJ,IAAM,iBAAiB;AAErB,IAAA,KAIE,OAAa;AAJf,IAAA,iBAAc,OAAA,SAAG,SAAU,KAAU,OAAU;AAC7C,UAAI,YAAY;AAChB,aAAO;IACT,IAAC;AAGH,IAAA;IAAA,SAAA,QAAA;AAAoC,gBAAAA,iBAAA,MAAA;AAGlC,eAAAA,gBAAY,SAAyC;AAAzC,YAAA,YAAA,QAAA;AAAA,oBAAA;QAAyC;AAArD,YAAA,QACE,OAAA,KAAA,MACE,OAAO,YAAY,WACZ,iBAAc,OAAK,UAAO,+DAC7B,OAAO,KACZ;AAPH,cAAA,cAAc;AACd,cAAA,OAAO;AAOL,uBAAe,OAAMA,gBAAe,SAAS;;MAC/C;AACF,aAAAA;IAAA,EAXoC,KAAK;AAsBzC,IAAM,kBAAkB,CAAC,SAAS,OAAO,QAAQ,SAAS,QAAQ;AAGlE,IAAI,iBAAiB,gBAAgB,QAAQ,KAAK;AAalD,KAAA,SAAiBC,YAAS;AACX,MAAAA,WAAA,QAAQ,kBAAkB,OAAO;AACjC,MAAAA,WAAA,MAAM,kBAAkB,KAAK;AAC7B,MAAAA,WAAA,OAAO,kBAAkB,MAAM;AAC/B,MAAAA,WAAA,QAAQ,kBAAkB,OAAO;IAChD,GALiB,cAAA,YAAS,CAAA,EAAA;;;;;AC9CpB,SAAU,MAAS,OAAc;AACrC,MAAI;AACF,WAAO,MAAK;EACd,SAAEC,KAAM;EAAC;AACX;AAJA;;;;;;ACIM,SAAU,aAAa,QAAc;AACzC,MAAM,QAAQ,aAAa,IAAI,MAAM,KAAK;AAC1C,eAAa,IAAI,QAAQ,QAAQ,CAAC;AAClC,SAAO,GAAA,OAAG,QAAM,GAAA,EAAA,OAAI,OAAK,GAAA,EAAA,OAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAClE;AARA,IAAM;AAAN;;IAAM,eAAe,oBAAI,IAAG;;;;;ACEtB,SAAU,oBAAoB,OAAY,OAAS;AAAT,MAAA,UAAA,QAAA;AAAA,YAAA;EAAS;AACvD,MAAM,UAAU,aAAa,qBAAqB;AAClD,SAAO,KAAK,UACV,OACA,SAAC,KAAKC,QAAK;AACT,WAAOA,WAAU,SAAS,UAAUA;EACtC,GACA,KAAK,EAEJ,MAAM,KAAK,UAAU,OAAO,CAAC,EAC7B,KAAK,aAAa;AACvB;AAbA;;;;;;;ACAA,IAAa;AAAb;;AAAO,IAAM,UAAU;;;;;ACAvB,IASA;AATA;;;AASA,IAAA,iBAAgB,MAAM,WAAA;AAAM,aAAA;IAAA,CAAU,KACpC,MAAM,WAAA;AAAM,aAAA;IAAA,CAAM,KAClB,MAAM,WAAA;AAAM,aAAA;IAAA,CAAI,KAChB,MAAM,WAAA;AAAM,aAAA;IAAA,CAAM;;;;;;UAMlB,WAAA;AACA,aAAM,MAAA,YAAA,aAAA,EAAA;;;;;;ACbR,SAAS,KAAK,IAA0C;AACtD,SAAO,SAAU,SAAyB;AAAE,QAAA,OAAA,CAAA;aAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAc;AAAd,WAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAC1C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,OAAO;AACb,gBAAU,mBAAmB,IAAI;AACjC,UAAI,CAAC,SAAS;AACZ,kBAAU,oBAAoB,MAAM,IAAI;AACxC,eAAO,CAAA;MACT;IACF;AACA,OAAE,MAAA,QAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;EAC9B;AACF;AA+EA,SAAS,kBACP,SAAyB;AACzB,MAAA,iBAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAA4B;AAA5B,mBAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAEA,SAAO,IAAI,eACT,mBAAmB,SAAS,cAAc,KACxC,oBAAoB,SAAS,cAAc,CAAC;AAElD;AAaA,SAAS,UAAU,KAAQ;AACzB,MAAI,OAAO,OAAO,UAAU;AAC1B,WAAO;EACT;AAEA,MAAI;AACF,WAAO,oBAAoB,KAAK,CAAC,EAAE,MAAM,GAAG,GAAI;EAClD,SAAEC,KAAM;AACN,WAAO;EACT;AACF;AAEA,SAAS,mBACP,SACA,aAA2B;AAA3B,MAAA,gBAAA,QAAA;AAAA,kBAAA,CAAA;EAA2B;AAE3B,MAAI,CAAC;AAAS;AACd,SACE,eAAO,yBAAyB,KAChC,eAAO,yBAAyB,EAAE,SAAS,YAAY,IAAI,SAAS,CAAC;AAEzE;AAEA,SAAS,oBACP,SACA,aAA2B;AAA3B,MAAA,gBAAA,QAAA;AAAA,kBAAA,CAAA;EAA2B;AAE3B,MAAI,CAAC;AAAS;AACd,SAAO,+FAAA,OAA+F,mBACpG,KAAK,UAAU;IACb;IACA;IACA,MAAM,YAAY,IAAI,SAAS;GAChC,CAAC,CACH;AACH;AAzJA,IAkEMC,YAyCA;AA3GN;;;AACA;AACA;AAEA;AA8DA,IAAMA,aAA8B,OAAO,OACzC,SAASA,WACP,WACA,SAAyB;AACzB,UAAA,OAAA,CAAA;eAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,aAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAEA,UAAI,CAAC,WAAW;AACd,kBACE,WACA,mBAAmB,SAAS,IAAI,KAAK,oBAAoB,SAAS,IAAI,CAAC;MAE3E;IACF,GACA;MACE,OAAO,KAAK,UAAkB,KAAK;MACnC,KAAK,KAAK,UAAkB,GAAG;MAC/B,MAAM,KAAK,UAAkB,IAAI;MACjC,OAAO,KAAK,UAAkB,KAAK;KACpC;AAuBH,IAAM,4BAA4B,OAAO,IACvC,+BAA+B,OAAO;;;;;AC5GxC,IAiBa;AAjBb;;;AAMA;AACA;AAUO,IAAM,MAAM,WAAW,YAAY;;;", "names": ["InvariantError", "invariant", "_a", "value", "_a", "invariant"]}
{"version": 3, "sources": ["../../../../@scalar/oas-utils/dist/entities/cookie/cookie.js"], "sourcesContent": ["import { z } from 'zod';\nimport { nanoidSchema } from '../shared/utility.js';\n\nconst cookieSchema = z.object({\n    uid: nanoidSchema.brand(),\n    /**  Defines the cookie name and its value. A cookie definition begins with a name-value pair.  */\n    name: z.string().default(''),\n    value: z.string().default(''),\n    /** Defines the host to which the cookie will be sent. */\n    domain: z.string().optional(),\n    /** Indicates the path that must exist in the requested URL for the browser to send the Cookie header. */\n    path: z.string().optional(),\n});\n\nexport { cookieSchema };\n"], "mappings": ";;;;;;AAGA,IAAM,eAAe,EAAE,OAAO;AAAA,EAC1B,KAAK,aAAa,MAAM;AAAA;AAAA,EAExB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;AAAA,EAC3B,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;AAAA;AAAA,EAE5B,QAAQ,EAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAE5B,MAAM,EAAE,OAAO,EAAE,SAAS;AAC9B,CAAC;", "names": []}
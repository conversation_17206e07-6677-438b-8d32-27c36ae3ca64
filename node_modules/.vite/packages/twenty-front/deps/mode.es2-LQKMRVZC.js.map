{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/variables/mode.js"], "sourcesContent": ["import CodeMirror from 'codemirror';\nimport { list, t, onlineParser, opt, p, } from 'graphql-language-service';\nimport indent from '../utils/mode-indent';\nCodeMirror.defineMode('graphql-variables', config => {\n    const parser = onlineParser({\n        eatWhitespace: stream => stream.eatSpace(),\n        lexRules: LexRules,\n        parseRules: ParseRules,\n        editorConfig: { tabSize: config.tabSize },\n    });\n    return {\n        config,\n        startState: parser.startState,\n        token: parser.token,\n        indent,\n        electricInput: /^\\s*[}\\]]/,\n        fold: 'brace',\n        closeBrackets: {\n            pairs: '[]{}\"\"',\n            explode: '[]{}',\n        },\n    };\n});\nconst LexRules = {\n    Punctuation: /^\\[|]|\\{|\\}|:|,/,\n    Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n    String: /^\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?/,\n    Keyword: /^true|false|null/,\n};\nconst ParseRules = {\n    Document: [p('{'), list('Variable', opt(p(','))), p('}')],\n    Variable: [namedKey('variable'), p(':'), 'Value'],\n    Value(token) {\n        switch (token.kind) {\n            case 'Number':\n                return 'NumberValue';\n            case 'String':\n                return 'StringValue';\n            case 'Punctuation':\n                switch (token.value) {\n                    case '[':\n                        return 'ListValue';\n                    case '{':\n                        return 'ObjectValue';\n                }\n                return null;\n            case 'Keyword':\n                switch (token.value) {\n                    case 'true':\n                    case 'false':\n                        return 'BooleanValue';\n                    case 'null':\n                        return 'NullValue';\n                }\n                return null;\n        }\n    },\n    NumberValue: [t('Number', 'number')],\n    StringValue: [t('String', 'string')],\n    BooleanValue: [t('Keyword', 'builtin')],\n    NullValue: [t('Keyword', 'keyword')],\n    ListValue: [p('['), list('Value', opt(p(','))), p(']')],\n    ObjectValue: [p('{'), list('ObjectField', opt(p(','))), p('}')],\n    ObjectField: [namedKey('attribute'), p(':'), 'Value'],\n};\nfunction namedKey(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'String',\n        update(state, token) {\n            state.name = token.value.slice(1, -1);\n        },\n    };\n}\n//# sourceMappingURL=mode.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,WAAW,WAAW,qBAAqB,CAAA,WAAU;AACjD,QAAM,SAAS,aAAa;IACxB,eAAe,CAAA,WAAU,OAAO,SAAU;IAC1C,UAAU;IACV,YAAY;IACZ,cAAc,EAAE,SAAS,OAAO,QAAS;EACjD,CAAK;AACD,SAAO;IACH;IACA,YAAY,OAAO;IACnB,OAAO,OAAO;IACd;IACA,eAAe;IACf,MAAM;IACN,eAAe;MACX,OAAO;MACP,SAAS;IACZ;EACT;AACA,CAAC;AACD,IAAM,WAAW;EACb,aAAa;EACb,QAAQ;EACR,QAAQ;EACR,SAAS;AACb;AACA,IAAM,aAAa;EACf,UAAU,CAAC,EAAE,GAAG,GAAG,KAAK,YAAY,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EACxD,UAAU,CAAC,SAAS,UAAU,GAAG,EAAE,GAAG,GAAG,OAAO;EAChD,MAAM,OAAO;AACT,YAAQ,MAAM,MAAI;MACd,KAAK;AACD,eAAO;MACX,KAAK;AACD,eAAO;MACX,KAAK;AACD,gBAAQ,MAAM,OAAK;UACf,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;MACX,KAAK;AACD,gBAAQ,MAAM,OAAK;UACf,KAAK;UACL,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO;QACd;AACD,eAAO;IACd;EACJ;EACD,aAAa,CAAC,EAAE,UAAU,QAAQ,CAAC;EACnC,aAAa,CAAC,EAAE,UAAU,QAAQ,CAAC;EACnC,cAAc,CAAC,EAAE,WAAW,SAAS,CAAC;EACtC,WAAW,CAAC,EAAE,WAAW,SAAS,CAAC;EACnC,WAAW,CAAC,EAAE,GAAG,GAAG,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EACtD,aAAa,CAAC,EAAE,GAAG,GAAG,KAAK,eAAe,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EAC9D,aAAa,CAAC,SAAS,WAAW,GAAG,EAAE,GAAG,GAAG,OAAO;AACxD;AACA,SAAS,SAAS,OAAO;AACrB,SAAO;IACH;IACA,OAAO,CAAC,UAAU,MAAM,SAAS;IACjC,OAAO,OAAO,OAAO;AACjB,YAAM,OAAO,MAAM,MAAM,MAAM,GAAG,EAAE;IACvC;EACT;AACA;", "names": []}
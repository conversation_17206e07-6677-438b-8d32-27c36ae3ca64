import {
  __commonJS
} from "./chunk-XPZLJQLW.js";

// node_modules/json-2-csv/lib/constants.js
var require_constants = __commonJS({
  "node_modules/json-2-csv/lib/constants.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.excelBOM = exports.defaultCsv2JsonOptions = exports.defaultJson2CsvOptions = exports.errors = void 0;
    exports.errors = {
      optionsRequired: "Options were not passed and are required.",
      json2csv: {
        cannotCallOn: "Cannot call json2csv on",
        dataCheckFailure: "Data provided was not an array of documents.",
        notSameSchema: "Not all documents have the same schema."
      },
      csv2json: {
        cannotCallOn: "Cannot call csv2json on",
        dataCheckFailure: "CSV is not a string."
      }
    };
    exports.defaultJson2CsvOptions = {
      arrayIndexesAsKeys: false,
      checkSchemaDifferences: false,
      delimiter: {
        field: ",",
        wrap: '"',
        eol: "\n"
      },
      emptyFieldValue: void 0,
      escapeHeaderNestedDots: true,
      excelBOM: false,
      excludeKeys: [],
      expandNestedObjects: true,
      expandArrayObjects: false,
      prependHeader: true,
      preventCsvInjection: false,
      sortHeader: false,
      trimFieldValues: false,
      trimHeaderFields: false,
      unwindArrays: false,
      useDateIso8601Format: false,
      useLocaleFormat: false,
      wrapBooleans: false
    };
    exports.defaultCsv2JsonOptions = {
      delimiter: {
        field: ",",
        wrap: '"',
        eol: "\n"
      },
      excelBOM: false,
      preventCsvInjection: false,
      trimFieldValues: false,
      trimHeaderFields: false
    };
    exports.excelBOM = "\uFEFF";
  }
});

// node_modules/doc-path/lib/path.js
var require_path = __commonJS({
  "node_modules/doc-path/lib/path.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.setPath = exports.evaluatePath = void 0;
    function evaluatePath(obj, kp) {
      if (!obj) {
        return null;
      }
      const { dotIndex, key, remaining } = state(kp);
      const kpVal = typeof obj === "object" && kp in obj ? obj[kp] : void 0;
      const keyVal = typeof obj === "object" && key in obj ? obj[key] : void 0;
      if (dotIndex >= 0 && typeof obj === "object" && !(kp in obj)) {
        const { key: nextKey } = state(remaining);
        const nextKeyAsInt = parseInt(nextKey);
        if (Array.isArray(keyVal) && isNaN(nextKeyAsInt)) {
          return keyVal.map((doc) => evaluatePath(doc, remaining));
        }
        return evaluatePath(keyVal, remaining);
      } else if (Array.isArray(obj)) {
        const keyAsInt = parseInt(key);
        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {
          return keyVal;
        }
        return obj.map((doc) => evaluatePath(doc, kp));
      } else if (dotIndex >= 0 && kp !== key && typeof obj === "object" && key in obj) {
        return evaluatePath(keyVal, remaining);
      } else if (dotIndex === -1 && typeof obj === "object" && key in obj && !(kp in obj)) {
        return keyVal;
      }
      return kpVal;
    }
    exports.evaluatePath = evaluatePath;
    function setPath(obj, kp, v) {
      if (!obj) {
        throw new Error("No object was provided.");
      } else if (!kp) {
        throw new Error("No keyPath was provided.");
      }
      return _sp(obj, kp, v);
    }
    exports.setPath = setPath;
    function _sp(obj, kp, v) {
      const { dotIndex, key, remaining } = state(kp);
      if (kp.startsWith("__proto__") || kp.startsWith("constructor") || kp.startsWith("prototype")) {
        return obj;
      }
      if (dotIndex >= 0) {
        const keyAsInt = parseInt(key);
        if (typeof obj === "object" && obj !== null && !(key in obj) && Array.isArray(obj) && !isNaN(keyAsInt)) {
          obj[key] = obj[key] ?? {};
          _sp(obj[key], remaining, v);
          return obj;
        } else if (typeof obj === "object" && obj !== null && !(key in obj) && Array.isArray(obj)) {
          obj.forEach((doc) => _sp(doc, kp, v));
          return obj;
        } else if (typeof obj === "object" && obj !== null && !(key in obj) && !Array.isArray(obj)) {
          const { key: nextKey } = state(remaining);
          const nextKeyAsInt = parseInt(nextKey);
          if (!isNaN(nextKeyAsInt)) {
            obj[key] = [];
          } else if (remaining === "") {
            obj[kp] = v;
            return obj;
          } else {
            obj[key] = {};
          }
        }
        _sp(obj[key], remaining, v);
      } else if (Array.isArray(obj)) {
        const keyAsInt = parseInt(key);
        if (kp === key && dotIndex === -1 && !isNaN(keyAsInt)) {
          obj[key] = v;
          return obj;
        }
        obj.forEach((doc) => _sp(doc, remaining, v));
        return obj;
      } else {
        obj[key] = v;
      }
      return obj;
    }
    function state(kp) {
      const dotIndex = findFirstNonEscapedDotIndex(kp);
      return {
        dotIndex,
        key: kp.slice(0, dotIndex >= 0 ? dotIndex : void 0).replace(/\\./g, "."),
        remaining: kp.slice(dotIndex + 1)
      };
    }
    function findFirstNonEscapedDotIndex(kp) {
      for (let i = 0; i < kp.length; i++) {
        const previousChar = i > 0 ? kp[i - 1] : "", currentChar = kp[i];
        if (currentChar === "." && previousChar !== "\\")
          return i;
      }
      return -1;
    }
  }
});

// node_modules/deeks/lib/utils.js
var require_utils = __commonJS({
  "node_modules/deeks/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isDocumentToRecurOn = exports.flatten = exports.unique = void 0;
    function unique(array) {
      return [...new Set(array)];
    }
    exports.unique = unique;
    function flatten(array) {
      return [].concat(...array);
    }
    exports.flatten = flatten;
    function isDocumentToRecurOn(val) {
      return typeof val === "object" && val !== null && !Array.isArray(val) && Object.keys(val).length;
    }
    exports.isDocumentToRecurOn = isDocumentToRecurOn;
  }
});

// node_modules/deeks/lib/types.js
var require_types = __commonJS({
  "node_modules/deeks/lib/types.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/deeks/lib/deeks.js
var require_deeks = __commonJS({
  "node_modules/deeks/lib/deeks.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.deepKeysFromList = exports.deepKeys = void 0;
    var utils = __importStar(require_utils());
    __exportStar(require_types(), exports);
    function deepKeys(object, options) {
      const parsedOptions = mergeOptions(options);
      if (typeof object === "object" && object !== null) {
        return generateDeepKeysList("", object, parsedOptions);
      }
      return [];
    }
    exports.deepKeys = deepKeys;
    function deepKeysFromList(list, options) {
      const parsedOptions = mergeOptions(options);
      return list.map((document) => {
        if (typeof document === "object" && document !== null) {
          return deepKeys(document, parsedOptions);
        }
        return [];
      });
    }
    exports.deepKeysFromList = deepKeysFromList;
    function generateDeepKeysList(heading, data, options) {
      const keys = Object.keys(data).map((currentKey) => {
        const keyName = buildKeyName(heading, escapeNestedDotsIfSpecified(currentKey, options));
        if (options.expandNestedObjects && utils.isDocumentToRecurOn(data[currentKey]) || options.arrayIndexesAsKeys && Array.isArray(data[currentKey]) && data[currentKey].length) {
          return generateDeepKeysList(keyName, data[currentKey], options);
        } else if (options.expandArrayObjects && Array.isArray(data[currentKey])) {
          return processArrayKeys(data[currentKey], keyName, options);
        } else if (options.ignoreEmptyArrays && Array.isArray(data[currentKey]) && !data[currentKey].length) {
          return [];
        }
        return keyName;
      });
      return utils.flatten(keys);
    }
    function processArrayKeys(subArray, currentKeyPath, options) {
      let subArrayKeys = deepKeysFromList(subArray, options);
      if (!subArray.length) {
        return options.ignoreEmptyArraysWhenExpanding ? [] : [currentKeyPath];
      } else if (subArray.length && utils.flatten(subArrayKeys).length === 0) {
        return [currentKeyPath];
      } else {
        subArrayKeys = subArrayKeys.map((schemaKeys) => {
          if (Array.isArray(schemaKeys) && schemaKeys.length === 0) {
            return [currentKeyPath];
          }
          return schemaKeys.map((subKey) => buildKeyName(currentKeyPath, escapeNestedDotsIfSpecified(subKey, options)));
        });
        return utils.unique(utils.flatten(subArrayKeys));
      }
    }
    function escapeNestedDotsIfSpecified(key, options) {
      if (options.escapeNestedDots) {
        return key.replace(/\./g, "\\.");
      }
      return key;
    }
    function buildKeyName(upperKeyName, currentKeyName) {
      if (upperKeyName) {
        return upperKeyName + "." + currentKeyName;
      }
      return currentKeyName;
    }
    function mergeOptions(options) {
      return {
        arrayIndexesAsKeys: false,
        expandNestedObjects: true,
        expandArrayObjects: false,
        ignoreEmptyArraysWhenExpanding: false,
        escapeNestedDots: false,
        ignoreEmptyArrays: false,
        ...options ?? {}
      };
    }
  }
});

// node_modules/json-2-csv/lib/utils.js
var require_utils2 = __commonJS({
  "node_modules/json-2-csv/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isInvalid = exports.flatten = exports.unique = exports.arrayDifference = exports.isError = exports.isUndefined = exports.isNull = exports.isObject = exports.isString = exports.isNumber = exports.unwind = exports.getNCharacters = exports.removeEmptyFields = exports.isEmptyField = exports.computeSchemaDifferences = exports.isDateRepresentation = exports.isStringRepresentation = exports.deepCopy = exports.validate = exports.buildC2JOptions = exports.buildJ2COptions = void 0;
    var doc_path_1 = require_path();
    var constants_1 = require_constants();
    var dateStringRegex = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z/;
    var MAX_ARRAY_LENGTH = 1e5;
    function buildJ2COptions(opts) {
      var _a, _b, _c;
      return {
        ...constants_1.defaultJson2CsvOptions,
        ...opts,
        delimiter: {
          field: ((_a = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _a.field) ?? constants_1.defaultJson2CsvOptions.delimiter.field,
          wrap: ((_b = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _b.wrap) || constants_1.defaultJson2CsvOptions.delimiter.wrap,
          eol: ((_c = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _c.eol) || constants_1.defaultJson2CsvOptions.delimiter.eol
        },
        fieldTitleMap: /* @__PURE__ */ Object.create({})
      };
    }
    exports.buildJ2COptions = buildJ2COptions;
    function buildC2JOptions(opts) {
      var _a, _b, _c;
      return {
        ...constants_1.defaultCsv2JsonOptions,
        ...opts,
        delimiter: {
          field: ((_a = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _a.field) ?? constants_1.defaultCsv2JsonOptions.delimiter.field,
          wrap: ((_b = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _b.wrap) || constants_1.defaultCsv2JsonOptions.delimiter.wrap,
          eol: ((_c = opts == null ? void 0 : opts.delimiter) == null ? void 0 : _c.eol) || constants_1.defaultCsv2JsonOptions.delimiter.eol
        }
      };
    }
    exports.buildC2JOptions = buildC2JOptions;
    function validate(data, validationFn, errorMessages) {
      if (!data)
        throw new Error(`${errorMessages.cannotCallOn} ${data}.`);
      if (!validationFn(data))
        throw new Error(errorMessages.dataCheckFailure);
      return true;
    }
    exports.validate = validate;
    function deepCopy(obj) {
      return JSON.parse(JSON.stringify(obj));
    }
    exports.deepCopy = deepCopy;
    function isStringRepresentation(fieldValue, options) {
      const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];
      return firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap;
    }
    exports.isStringRepresentation = isStringRepresentation;
    function isDateRepresentation(fieldValue) {
      return dateStringRegex.test(fieldValue);
    }
    exports.isDateRepresentation = isDateRepresentation;
    function computeSchemaDifferences(schemaA, schemaB) {
      return arrayDifference(schemaA, schemaB).concat(arrayDifference(schemaB, schemaA));
    }
    exports.computeSchemaDifferences = computeSchemaDifferences;
    function isEmptyField(fieldValue) {
      return isUndefined(fieldValue) || isNull(fieldValue) || fieldValue === "";
    }
    exports.isEmptyField = isEmptyField;
    function removeEmptyFields(fields) {
      return fields.filter((field) => !isEmptyField(field));
    }
    exports.removeEmptyFields = removeEmptyFields;
    function getNCharacters(str, start, n) {
      return str.substring(start, start + n);
    }
    exports.getNCharacters = getNCharacters;
    function unwindItem(accumulator, item, fieldPath) {
      const valueToUnwind = (0, doc_path_1.evaluatePath)(item, fieldPath);
      let cloned = deepCopy(item);
      if (Array.isArray(valueToUnwind) && valueToUnwind.length) {
        valueToUnwind.forEach((val) => {
          cloned = deepCopy(item);
          accumulator.push((0, doc_path_1.setPath)(cloned, fieldPath, val));
        });
      } else if (Array.isArray(valueToUnwind) && valueToUnwind.length === 0) {
        (0, doc_path_1.setPath)(cloned, fieldPath, "");
        accumulator.push(cloned);
      } else {
        accumulator.push(cloned);
      }
    }
    function unwind(array, field) {
      const result = [];
      array.forEach((item) => {
        unwindItem(result, item, field);
      });
      return result;
    }
    exports.unwind = unwind;
    function isNumber(value) {
      return !isNaN(Number(value));
    }
    exports.isNumber = isNumber;
    function isString(value) {
      return typeof value === "string";
    }
    exports.isString = isString;
    function isObject(value) {
      return typeof value === "object";
    }
    exports.isObject = isObject;
    function isNull(value) {
      return value === null;
    }
    exports.isNull = isNull;
    function isUndefined(value) {
      return typeof value === "undefined";
    }
    exports.isUndefined = isUndefined;
    function isError(value) {
      return Object.prototype.toString.call(value) === "[object Error]";
    }
    exports.isError = isError;
    function arrayDifference(a, b) {
      return a.filter((x) => !b.includes(x));
    }
    exports.arrayDifference = arrayDifference;
    function unique(array) {
      return [...new Set(array)];
    }
    exports.unique = unique;
    function flatten(array) {
      if (array.flat) {
        return array.flat();
      }
      if (array.length > MAX_ARRAY_LENGTH) {
        let safeArray = [];
        for (let a = 0; a < array.length; a += MAX_ARRAY_LENGTH) {
          safeArray = safeArray.concat(...array.slice(a, a + MAX_ARRAY_LENGTH));
        }
        return safeArray;
      }
      return array.reduce((accumulator, value) => accumulator.concat(value), []);
    }
    exports.flatten = flatten;
    function isInvalid(parsedJson) {
      return parsedJson === Infinity || parsedJson === -Infinity;
    }
    exports.isInvalid = isInvalid;
  }
});

// node_modules/json-2-csv/lib/json2csv.js
var require_json2csv = __commonJS({
  "node_modules/json-2-csv/lib/json2csv.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Json2Csv = void 0;
    var doc_path_1 = require_path();
    var deeks_1 = require_deeks();
    var constants_1 = require_constants();
    var utils = __importStar(require_utils2());
    var Json2Csv = function(options) {
      const wrapDelimiterCheckRegex = new RegExp(options.delimiter.wrap, "g"), crlfSearchRegex = /\r?\n|\r/, customValueParser = options.parseValue && typeof options.parseValue === "function" ? options.parseValue : null, expandingWithoutUnwinding = options.expandArrayObjects && !options.unwindArrays, deeksOptions = {
        arrayIndexesAsKeys: options.arrayIndexesAsKeys,
        expandNestedObjects: options.expandNestedObjects,
        expandArrayObjects: expandingWithoutUnwinding,
        ignoreEmptyArraysWhenExpanding: expandingWithoutUnwinding,
        escapeNestedDots: true
      };
      function getFieldNameList(data) {
        return (0, deeks_1.deepKeysFromList)(data, deeksOptions);
      }
      function processSchemas(documentSchemas) {
        if (options.checkSchemaDifferences) {
          return checkSchemaDifferences(documentSchemas);
        } else {
          const uniqueFieldNames = utils.unique(utils.flatten(documentSchemas));
          return uniqueFieldNames;
        }
      }
      function checkSchemaDifferences(documentSchemas) {
        const firstDocSchema = documentSchemas[0], restOfDocumentSchemas = documentSchemas.slice(1), schemaDifferences = computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas);
        if (schemaDifferences) {
          throw new Error(constants_1.errors.json2csv.notSameSchema);
        }
        return firstDocSchema;
      }
      function computeNumberOfSchemaDifferences(firstDocSchema, restOfDocumentSchemas) {
        return restOfDocumentSchemas.reduce((schemaDifferences, documentSchema) => {
          const numberOfDifferences = utils.computeSchemaDifferences(firstDocSchema, documentSchema).length;
          return numberOfDifferences > 0 ? schemaDifferences + 1 : schemaDifferences;
        }, 0);
      }
      function filterExcludedKeys(keyPaths) {
        if (options.excludeKeys) {
          return keyPaths.filter((keyPath) => {
            for (const excludedKey of options.excludeKeys) {
              const regex = excludedKey instanceof RegExp ? excludedKey : new RegExp(`^${excludedKey}`);
              if (excludedKey === keyPath || keyPath.match(regex)) {
                return false;
              }
            }
            return true;
          });
        }
        return keyPaths;
      }
      function sortHeaderFields(fieldNames) {
        if (options.sortHeader && typeof options.sortHeader === "function") {
          return fieldNames.sort(options.sortHeader);
        } else if (options.sortHeader) {
          return fieldNames.sort();
        }
        return fieldNames;
      }
      function trimHeaderFields(params) {
        if (options.trimHeaderFields) {
          params.headerFields = params.headerFields.map((field) => field.split(".").map((component) => component.trim()).join("."));
        }
        return params;
      }
      function wrapHeaderFields(params) {
        if (options.prependHeader) {
          params.headerFields = params.headerFields.map(function(headingKey) {
            return wrapFieldValueIfNecessary(headingKey);
          });
        }
        return params;
      }
      function generateCsvHeader(params) {
        const fieldTitleMapKeys = Object.keys(options.fieldTitleMap);
        params.header = params.headerFields.map(function(field) {
          let headerKey = field;
          if (fieldTitleMapKeys.includes(field)) {
            headerKey = options.fieldTitleMap[field];
          } else if (!options.escapeHeaderNestedDots) {
            headerKey = headerKey.replace(/\\\./g, ".");
          }
          return headerKey;
        }).join(options.delimiter.field);
        return params;
      }
      function convertKeysToHeaderFields() {
        if (!options.keys)
          return [];
        return options.keys.map((key) => {
          if (typeof key === "object" && "field" in key) {
            options.fieldTitleMap[key.field] = key.title ?? key.field;
            return key.field;
          }
          return key;
        });
      }
      function extractWildcardMatchKeys() {
        if (!options.keys)
          return [];
        return options.keys.flatMap((item) => {
          if (typeof item === "string") {
            return [];
          } else if (item == null ? void 0 : item.wildcardMatch) {
            return item.field;
          }
          return [];
        });
      }
      function retrieveHeaderFields(data) {
        const wildcardMatchKeys = extractWildcardMatchKeys();
        const keyStrings = convertKeysToHeaderFields();
        const fieldNames = getFieldNameList(data);
        const processed = processSchemas(fieldNames);
        if (options.keys) {
          options.keys = keyStrings;
          const matchedKeys = keyStrings.flatMap((userProvidedKey) => {
            if (!wildcardMatchKeys.includes(userProvidedKey)) {
              return userProvidedKey;
            }
            const matches = [];
            const regex = new RegExp(`^${userProvidedKey}`);
            for (const detectedKey of processed) {
              if (userProvidedKey === detectedKey || detectedKey.match(regex)) {
                matches.push(detectedKey);
              }
            }
            return matches;
          });
          if (!options.unwindArrays) {
            const filtered2 = filterExcludedKeys(matchedKeys);
            return sortHeaderFields(filtered2);
          }
        }
        const filtered = filterExcludedKeys(processed);
        return sortHeaderFields(filtered);
      }
      function unwindRecordsIfNecessary(params, finalPass = false) {
        if (options.unwindArrays) {
          const originalRecordsLength = params.records.length;
          params.headerFields.forEach((headerField) => {
            params.records = utils.unwind(params.records, headerField);
          });
          const headerFields = retrieveHeaderFields(params.records);
          params.headerFields = headerFields;
          if (originalRecordsLength !== params.records.length) {
            return unwindRecordsIfNecessary(params);
          }
          if (!finalPass) {
            return unwindRecordsIfNecessary(params, true);
          }
          if (options.keys) {
            const userSelectedFields = convertKeysToHeaderFields();
            params.headerFields = filterExcludedKeys(userSelectedFields);
          }
          return params;
        }
        return params;
      }
      function processRecords(params) {
        params.recordString = params.records.map((record) => {
          const recordFieldData = retrieveRecordFieldData(record, params.headerFields), processedRecordData = recordFieldData.map((fieldValue) => {
            fieldValue = trimRecordFieldValue(fieldValue);
            fieldValue = preventCsvInjection(fieldValue);
            let stringified = customValueParser ? customValueParser(fieldValue, recordFieldValueToString) : recordFieldValueToString(fieldValue);
            stringified = wrapFieldValueIfNecessary(stringified);
            return stringified;
          });
          return generateCsvRowFromRecord(processedRecordData);
        }).join(options.delimiter.eol);
        return params;
      }
      function processRecordFieldDataForExpandedArrayObject(recordFieldValue) {
        const filteredRecordFieldValue = utils.removeEmptyFields(recordFieldValue);
        if (!recordFieldValue.length || !filteredRecordFieldValue.length) {
          return options.emptyFieldValue || "";
        } else if (filteredRecordFieldValue.length === 1) {
          return filteredRecordFieldValue[0];
        }
        return recordFieldValue;
      }
      function retrieveRecordFieldData(record, fields) {
        const recordValues = [];
        fields.forEach((field) => {
          let recordFieldValue = (0, doc_path_1.evaluatePath)(record, field);
          if (!utils.isUndefined(options.emptyFieldValue) && utils.isEmptyField(recordFieldValue)) {
            recordFieldValue = options.emptyFieldValue;
          } else if (options.expandArrayObjects && Array.isArray(recordFieldValue)) {
            recordFieldValue = processRecordFieldDataForExpandedArrayObject(recordFieldValue);
          }
          recordValues.push(recordFieldValue);
        });
        return recordValues;
      }
      function recordFieldValueToString(fieldValue) {
        const isDate = fieldValue instanceof Date;
        if (fieldValue === null || Array.isArray(fieldValue) || typeof fieldValue === "object" && !isDate) {
          return JSON.stringify(fieldValue);
        } else if (typeof fieldValue === "undefined") {
          return "undefined";
        } else if (isDate && options.useDateIso8601Format) {
          return fieldValue.toISOString();
        } else {
          return !options.useLocaleFormat ? fieldValue.toString() : fieldValue.toLocaleString();
        }
      }
      function trimRecordFieldValue(fieldValue) {
        if (options.trimFieldValues) {
          if (Array.isArray(fieldValue)) {
            return fieldValue.map(trimRecordFieldValue);
          } else if (typeof fieldValue === "string") {
            return fieldValue.trim();
          }
          return fieldValue;
        }
        return fieldValue;
      }
      function preventCsvInjection(fieldValue) {
        if (options.preventCsvInjection) {
          if (Array.isArray(fieldValue)) {
            return fieldValue.map(preventCsvInjection);
          } else if (typeof fieldValue === "string" && !utils.isNumber(fieldValue)) {
            return fieldValue.replace(/^[=+\-@\t\r]+/g, "");
          }
          return fieldValue;
        }
        return fieldValue;
      }
      function wrapFieldValueIfNecessary(fieldValue) {
        const wrapDelimiter = options.delimiter.wrap;
        if (fieldValue.includes(options.delimiter.wrap)) {
          fieldValue = fieldValue.replace(wrapDelimiterCheckRegex, wrapDelimiter + wrapDelimiter);
        }
        if (fieldValue.includes(options.delimiter.field) || fieldValue.includes(options.delimiter.wrap) || fieldValue.match(crlfSearchRegex) || options.wrapBooleans && (fieldValue === "true" || fieldValue === "false")) {
          fieldValue = wrapDelimiter + fieldValue + wrapDelimiter;
        }
        return fieldValue;
      }
      function generateCsvRowFromRecord(recordFieldValues) {
        return recordFieldValues.join(options.delimiter.field);
      }
      function generateCsvFromComponents(params) {
        const header = params.header, records = params.recordString, csv = (options.excelBOM ? constants_1.excelBOM : "") + (options.prependHeader ? header + options.delimiter.eol : "") + records;
        return csv;
      }
      function convert(data) {
        if (utils.isObject(data) && !data.length) {
          data = [data];
        }
        const headerFields = {
          headerFields: retrieveHeaderFields(data),
          records: data,
          header: "",
          recordString: ""
        };
        const unwinded = unwindRecordsIfNecessary(headerFields);
        const processed = processRecords(unwinded);
        const wrapped = wrapHeaderFields(processed);
        const trimmed = trimHeaderFields(wrapped);
        const generated = generateCsvHeader(trimmed);
        return generateCsvFromComponents(generated);
      }
      return {
        convert
      };
    };
    exports.Json2Csv = Json2Csv;
  }
});

// node_modules/json-2-csv/lib/csv2json.js
var require_csv2json = __commonJS({
  "node_modules/json-2-csv/lib/csv2json.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Csv2Json = void 0;
    var doc_path_1 = require_path();
    var constants_1 = require_constants();
    var utils = __importStar(require_utils2());
    var Csv2Json = function(options) {
      const escapedWrapDelimiterRegex = new RegExp(options.delimiter.wrap + options.delimiter.wrap, "g"), excelBOMRegex = new RegExp("^" + constants_1.excelBOM), valueParserFn = options.parseValue && typeof options.parseValue === "function" ? options.parseValue : JSON.parse;
      function processHeaderKey(headerKey) {
        headerKey = removeWrapDelimitersFromValue(headerKey);
        if (options.trimHeaderFields) {
          return headerKey.split(".").map((component) => component.trim()).join(".");
        }
        return headerKey;
      }
      function retrieveHeading(lines) {
        let headerFields = [];
        if (options.headerFields) {
          headerFields = options.headerFields.map((headerField, index) => ({
            value: processHeaderKey(headerField),
            index
          }));
        } else {
          const headerRow = lines[0];
          headerFields = headerRow.map((headerKey, index) => ({
            value: processHeaderKey(headerKey),
            index
          }));
          if (options.keys) {
            const keys = options.keys;
            headerFields = headerFields.filter((headerKey) => keys.includes(headerKey.value));
          }
        }
        return {
          lines,
          headerFields,
          recordLines: []
        };
      }
      function stripExcelBOM(csv) {
        if (options.excelBOM) {
          return csv.replace(excelBOMRegex, "");
        }
        return csv;
      }
      function splitLines(csv) {
        const lines = [], lastCharacterIndex = csv.length - 1, eolDelimiterLength = options.delimiter.eol.length, stateVariables = {
          insideWrapDelimiter: false,
          parsingValue: true,
          justParsedDoubleQuote: false,
          startIndex: 0
        };
        let splitLine = [], character, charBefore, charAfter, nextNChar, index = 0;
        while (index < csv.length) {
          character = csv[index];
          charBefore = index ? csv[index - 1] : "";
          charAfter = index < lastCharacterIndex ? csv[index + 1] : "";
          nextNChar = utils.getNCharacters(csv, index, eolDelimiterLength);
          if ((nextNChar === options.delimiter.eol && !stateVariables.insideWrapDelimiter || index === lastCharacterIndex) && charBefore === options.delimiter.field) {
            if (nextNChar === options.delimiter.eol && stateVariables.startIndex === index) {
              splitLine.push("");
            } else if (character === options.delimiter.field) {
              splitLine.push("");
            } else {
              splitLine.push(csv.substring(stateVariables.startIndex));
            }
            splitLine.push("");
            lines.push(splitLine);
            splitLine = [];
            stateVariables.startIndex = index + eolDelimiterLength;
            stateVariables.parsingValue = true;
            stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;
          } else if (index === lastCharacterIndex && character === options.delimiter.field) {
            const parsedValue = csv.substring(stateVariables.startIndex, index);
            splitLine.push(parsedValue);
            splitLine.push("");
            lines.push(splitLine);
          } else if (index === lastCharacterIndex || nextNChar === options.delimiter.eol && // if we aren't inside wrap delimiters or if we are but the character before was a wrap delimiter and we didn't just see two
          (!stateVariables.insideWrapDelimiter || stateVariables.insideWrapDelimiter && charBefore === options.delimiter.wrap && !stateVariables.justParsedDoubleQuote)) {
            const toIndex = index !== lastCharacterIndex || charBefore === options.delimiter.wrap ? index : void 0;
            splitLine.push(csv.substring(stateVariables.startIndex, toIndex));
            lines.push(splitLine);
            splitLine = [];
            stateVariables.startIndex = index + eolDelimiterLength;
            stateVariables.parsingValue = true;
            stateVariables.insideWrapDelimiter = charAfter === options.delimiter.wrap;
          } else if (character === options.delimiter.wrap && charBefore === options.delimiter.field && !stateVariables.insideWrapDelimiter && !stateVariables.parsingValue) {
            stateVariables.startIndex = index;
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            if (utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {
              index += options.delimiter.eol.length + 1;
            }
          } else if ((charBefore !== options.delimiter.wrap || stateVariables.justParsedDoubleQuote && charBefore === options.delimiter.wrap) && character === options.delimiter.wrap && utils.getNCharacters(csv, index + 1, eolDelimiterLength) === options.delimiter.eol) {
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = false;
          } else if (character === options.delimiter.wrap && (index === 0 || utils.getNCharacters(csv, index - eolDelimiterLength, eolDelimiterLength) === options.delimiter.eol && !stateVariables.insideWrapDelimiter)) {
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index;
          } else if (character === options.delimiter.wrap && charAfter === options.delimiter.field) {
            splitLine.push(csv.substring(stateVariables.startIndex, index + 1));
            stateVariables.startIndex = index + 2;
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = false;
          } else if (character === options.delimiter.wrap && charBefore === options.delimiter.field && !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {
            splitLine.push(csv.substring(stateVariables.startIndex, index - 1));
            stateVariables.insideWrapDelimiter = true;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index;
          } else if (character === options.delimiter.wrap && charAfter === options.delimiter.wrap && index !== stateVariables.startIndex) {
            index += 2;
            stateVariables.justParsedDoubleQuote = true;
            continue;
          } else if (character === options.delimiter.field && charBefore !== options.delimiter.wrap && charAfter !== options.delimiter.wrap && !stateVariables.insideWrapDelimiter && stateVariables.parsingValue) {
            splitLine.push(csv.substring(stateVariables.startIndex, index));
            stateVariables.startIndex = index + 1;
          } else if (character === options.delimiter.field && charBefore === options.delimiter.wrap && charAfter !== options.delimiter.wrap && !stateVariables.parsingValue) {
            stateVariables.insideWrapDelimiter = false;
            stateVariables.parsingValue = true;
            stateVariables.startIndex = index + 1;
          }
          index++;
          stateVariables.justParsedDoubleQuote = false;
        }
        return lines;
      }
      function retrieveRecordLines(params) {
        if (options.headerFields) {
          params.recordLines = params.lines;
        } else {
          params.recordLines = params.lines.splice(1);
        }
        return params;
      }
      function retrieveRecordValueFromLine(headerField, line) {
        const value = line[headerField.index];
        return processRecordValue(value);
      }
      function processRecordValue(fieldValue) {
        const parsedJson = parseValue(fieldValue);
        if (!utils.isError(parsedJson) && !utils.isInvalid(parsedJson)) {
          return parsedJson;
        } else if (fieldValue === "undefined") {
          return void 0;
        }
        return fieldValue;
      }
      function trimRecordValue(fieldValue) {
        if (options.trimFieldValues && fieldValue !== null) {
          return fieldValue.trim();
        }
        return fieldValue;
      }
      function createDocument(headerFields, line) {
        return headerFields.reduce((document, headerField) => {
          const value = retrieveRecordValueFromLine(headerField, line);
          try {
            return (0, doc_path_1.setPath)(document, headerField.value, value);
          } catch (error) {
            return document;
          }
        }, {});
      }
      function removeWrapDelimitersFromValue(fieldValue) {
        const firstChar = fieldValue[0], lastIndex = fieldValue.length - 1, lastChar = fieldValue[lastIndex];
        if (firstChar === options.delimiter.wrap && lastChar === options.delimiter.wrap) {
          return fieldValue.length <= 2 ? "" : fieldValue.substring(1, lastIndex);
        }
        return fieldValue;
      }
      function unescapeWrapDelimiterInField(fieldValue) {
        return fieldValue.replace(escapedWrapDelimiterRegex, options.delimiter.wrap);
      }
      function transformRecordLines(params) {
        return params.recordLines.reduce((generatedJsonObjects, line) => {
          line = line.map((fieldValue) => {
            fieldValue = removeWrapDelimitersFromValue(fieldValue);
            fieldValue = unescapeWrapDelimiterInField(fieldValue);
            fieldValue = trimRecordValue(fieldValue);
            return fieldValue;
          });
          const generatedDocument = createDocument(params.headerFields, line);
          return generatedJsonObjects.concat(generatedDocument);
        }, []);
      }
      function parseValue(value) {
        try {
          if (utils.isStringRepresentation(value, options) && !utils.isDateRepresentation(value)) {
            return value;
          }
          const parsedJson = valueParserFn(value);
          if (Array.isArray(parsedJson)) {
            return parsedJson.map(trimRecordValue);
          }
          return parsedJson;
        } catch (err) {
          return err;
        }
      }
      function convert(data) {
        const stripped = stripExcelBOM(data);
        const split = splitLines(stripped);
        const heading = retrieveHeading(split);
        const lines = retrieveRecordLines(heading);
        return transformRecordLines(lines);
      }
      return {
        convert
      };
    };
    exports.Csv2Json = Csv2Json;
  }
});

// node_modules/json-2-csv/lib/converter.js
var require_converter = __commonJS({
  "node_modules/json-2-csv/lib/converter.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.csv2json = exports.json2csv = void 0;
    var constants_1 = require_constants();
    var json2csv_1 = require_json2csv();
    var csv2json_1 = require_csv2json();
    var utils_1 = require_utils2();
    function json2csv(data, options) {
      const builtOptions = (0, utils_1.buildJ2COptions)(options ?? {});
      (0, utils_1.validate)(data, utils_1.isObject, constants_1.errors.json2csv);
      return (0, json2csv_1.Json2Csv)(builtOptions).convert(data);
    }
    exports.json2csv = json2csv;
    function csv2json(data, options) {
      const builtOptions = (0, utils_1.buildC2JOptions)(options ?? {});
      (0, utils_1.validate)(data, utils_1.isString, constants_1.errors.csv2json);
      return (0, csv2json_1.Csv2Json)(builtOptions).convert(data);
    }
    exports.csv2json = csv2json;
  }
});
export default require_converter();
/*! Bundled license information:

doc-path/lib/path.js:
  (**
   * @license MIT
   * doc-path <https://github.com/mrodrig/doc-path>
   * Copyright (c) 2015-present, Michael Rodrigues.
   *)
*/
//# sourceMappingURL=json-2-csv.js.map

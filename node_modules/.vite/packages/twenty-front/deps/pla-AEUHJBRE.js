import {
  __esm
} from "./chunk-XPZLJQLW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js
var conf, language;
var init_pla = __esm({
  "node_modules/monaco-editor/esm/vs/basic-languages/pla/pla.js"() {
    conf = {
      comments: {
        lineComment: "#"
      },
      brackets: [
        ["[", "]"],
        ["<", ">"],
        ["(", ")"]
      ],
      autoClosingPairs: [
        { open: "[", close: "]" },
        { open: "<", close: ">" },
        { open: "(", close: ")" }
      ],
      surroundingPairs: [
        { open: "[", close: "]" },
        { open: "<", close: ">" },
        { open: "(", close: ")" }
      ]
    };
    language = {
      defaultToken: "",
      tokenPostfix: ".pla",
      brackets: [
        { open: "[", close: "]", token: "delimiter.square" },
        { open: "<", close: ">", token: "delimiter.angle" },
        { open: "(", close: ")", token: "delimiter.parenthesis" }
      ],
      keywords: [
        ".i",
        ".o",
        ".mv",
        ".ilb",
        ".ob",
        ".label",
        ".type",
        ".phase",
        ".pair",
        ".symbolic",
        ".symbolic-output",
        ".kiss",
        ".p",
        ".e",
        ".end"
      ],
      // regular expressions
      comment: /#.*$/,
      identifier: /[a-zA-Z]+[a-zA-Z0-9_\-]*/,
      plaContent: /[01\-~\|]+/,
      // The main tokenizer for our languages
      tokenizer: {
        root: [
          // comments and whitespace
          { include: "@whitespace" },
          [/@comment/, "comment"],
          // keyword
          [
            /\.([a-zA-Z_\-]+)/,
            {
              cases: {
                "@eos": { token: "keyword.$1" },
                "@keywords": {
                  cases: {
                    ".type": { token: "keyword.$1", next: "@type" },
                    "@default": { token: "keyword.$1", next: "@keywordArg" }
                  }
                },
                "@default": { token: "keyword.$1" }
              }
            }
          ],
          // identifiers
          [/@identifier/, "identifier"],
          // PLA row
          [/@plaContent/, "string"]
        ],
        whitespace: [[/[ \t\r\n]+/, ""]],
        type: [{ include: "@whitespace" }, [/\w+/, { token: "type", next: "@pop" }]],
        keywordArg: [
          // whitespace
          [
            /[ \t\r\n]+/,
            {
              cases: {
                "@eos": { token: "", next: "@pop" },
                "@default": ""
              }
            }
          ],
          // comments
          [/@comment/, "comment", "@pop"],
          // brackets
          [
            /[<>()\[\]]/,
            {
              cases: {
                "@eos": { token: "@brackets", next: "@pop" },
                "@default": "@brackets"
              }
            }
          ],
          // numbers
          [
            /\-?\d+/,
            {
              cases: {
                "@eos": { token: "number", next: "@pop" },
                "@default": "number"
              }
            }
          ],
          // identifiers
          [
            /@identifier/,
            {
              cases: {
                "@eos": { token: "identifier", next: "@pop" },
                "@default": "identifier"
              }
            }
          ],
          // delimiter
          [
            /[;=]/,
            {
              cases: {
                "@eos": { token: "delimiter", next: "@pop" },
                "@default": "delimiter"
              }
            }
          ]
        ]
      }
    };
  }
});
init_pla();
export {
  conf,
  language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/pla/pla.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=pla-AEUHJBRE.js.map

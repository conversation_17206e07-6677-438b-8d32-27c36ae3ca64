import {
  m
} from "./chunk-SGBOHCGH.js";
import {
  s
} from "./chunk-FXOF7VZK.js";
import {
  je,
  s as s2
} from "./chunk-K3FRCNXE.js";
import {
  Fragment,
  computed,
  createBaseVNode,
  createCommentVNode,
  createElementBlock,
  defineComponent,
  normalizeClass,
  normalizeStyle,
  openBlock,
  ref,
  renderSlot,
  toDisplayString,
  unref,
  useMediaQuery,
  vShow,
  withDirectives
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/themes/dist/pixelPreset.js
var e = {
  "0rem": "0px",
  "0.0625rem": "1px",
  "0.125rem": "2px",
  "0.1875rem": "3px",
  "0.25rem": "4px",
  "0.3125rem": "5px",
  "0.375rem": "6px",
  "0.4375rem": "7px",
  "0.5rem": "8px",
  "0.5625rem": "9px",
  "0.625rem": "10px",
  "0.6875rem": "11px",
  ".75rem": "12px",
  "0.75rem": "12px",
  "0.8125rem": "13px",
  "0.875rem": "14px",
  "0.9375rem": "15px",
  "1rem": "16px",
  "1.0625rem": "17px",
  "1.125rem": "18px",
  "1.1875rem": "19px",
  "1.25rem": "20px",
  "1.3125rem": "21px",
  "1.375rem": "22px",
  "1.4375rem": "23px",
  "1.5rem": "24px",
  "1.625rem": "26px",
  "1.75rem": "28px",
  "1.875rem": "30px",
  "2rem": "32px",
  "2.25rem": "36px",
  "2.5rem": "40px",
  "2.75rem": "44px",
  "3rem": "48px",
  "3.5rem": "56px",
  "3.75rem": "60px",
  "4rem": "64px",
  "4.5rem": "72px",
  "5rem": "80px",
  "5.125rem": "82px",
  "5.25rem": "84px",
  "5.375rem": "86px",
  "5.5rem": "88px",
  "5.625rem": "90px",
  "5.75rem": "92px",
  "5.875rem": "94px",
  "6rem": "96px",
  "7rem": "112px",
  "7.25rem": "116px",
  "7.5rem": "120px",
  "7.75rem": "124px",
  "8rem": "128px",
  "9rem": "144px",
  "10rem": "160px",
  "11rem": "176px",
  "11.25rem": "180px",
  "11.5rem": "184px",
  "11.75rem": "188px",
  "12rem": "192px",
  "13rem": "208px",
  "14rem": "224px",
  "15rem": "240px",
  "16rem": "256px",
  "18rem": "288px",
  "20rem": "320px",
  "24rem": "384px",
  "28rem": "448px",
  "32rem": "512px",
  "36rem": "576px",
  "42rem": "672px",
  "48rem": "768px",
  "56rem": "896px",
  "64rem": "1024px",
  "72rem": "1152px",
  "80rem": "1280px"
};
var m2 = {
  theme: {
    columns: {
      "3xs": e["16rem"],
      "2xs": e["18rem"],
      xs: e["20rem"],
      sm: e["24rem"],
      md: e["28rem"],
      lg: e["32rem"],
      xl: e["36rem"],
      "2xl": e["42rem"],
      "3xl": e["48rem"],
      "4xl": e["56rem"],
      "5xl": e["64rem"],
      "6xl": e["72rem"],
      "7xl": e["80rem"]
    },
    spacing: {
      0: e["0rem"],
      0.25: e["0.0625rem"],
      0.5: e["0.125rem"],
      0.75: e["0.1875rem"],
      1: e["0.25rem"],
      1.25: e["0.3125rem"],
      1.5: e["0.375rem"],
      1.75: e["0.4375rem"],
      2: e["0.5rem"],
      2.25: e["0.5625rem"],
      2.5: e["0.625rem"],
      2.75: e["0.6875rem"],
      3: e["0.75rem"],
      3.25: e["0.8125rem"],
      3.5: e["0.875rem"],
      3.75: e["0.9375rem"],
      4: e["1rem"],
      5: e["1.25rem"],
      6: e["1.5rem"],
      7: e["1.75rem"],
      8: e["2rem"],
      9: e["2.25rem"],
      10: e["2.5rem"],
      11: e["2.75rem"],
      12: e["3rem"],
      14: e["3.5rem"],
      16: e["4rem"],
      20: e["5rem"],
      20.5: e["5.125rem"],
      21: e["5.25rem"],
      21.5: e["5.375rem"],
      22: e["5.5rem"],
      22.5: e["5.625rem"],
      23: e["5.75rem"],
      23.5: e["5.875rem"],
      24: e["6rem"],
      28: e["7rem"],
      29: e["7.25rem"],
      30: e["7.5rem"],
      31: e["7.75rem"],
      32: e["8rem"],
      36: e["9rem"],
      40: e["10rem"],
      44: e["11rem"],
      45: e["11.25rem"],
      46: e["11.5rem"],
      47: e["11.75rem"],
      48: e["12rem"],
      52: e["13rem"],
      56: e["14rem"],
      60: e["15rem"],
      64: e["16rem"],
      72: e["18rem"],
      80: e["20rem"],
      96: e["24rem"]
    },
    lineHeight: {
      3: e[".75rem"],
      3.25: e["0.8125rem"],
      3.5: e["0.875rem"],
      3.75: e["0.9375rem"],
      4: e["1rem"],
      4.25: e["1.0625rem"],
      4.5: e["1.125rem"],
      4.75: e["1.1875rem"],
      5: e["1.25rem"],
      5.25: e["1.3125rem"],
      5.5: e["1.375rem"],
      5.75: e["1.4375rem"],
      6: e["1.5rem"],
      7: e["1.75rem"],
      8: e["2rem"],
      9: e["2.25rem"],
      10: e["2.5rem"]
    },
    maxWidth: () => ({
      0: e["0rem"],
      xs: e["20rem"],
      sm: e["24rem"],
      md: e["28rem"],
      lg: e["32rem"],
      xl: e["36rem"],
      "2xl": e["42rem"],
      "3xl": e["48rem"],
      "4xl": e["56rem"],
      "5xl": e["64rem"],
      "6xl": e["72rem"],
      "7xl": e["80rem"],
      full: "100%",
      "min  ": "min-content",
      max: "max-content",
      fit: "fit-content"
    })
  }
};

// node_modules/@scalar/themes/dist/tailwind.js
var l = {
  darkMode: ["selector", ".dark-mode"],
  presets: [m2],
  theme: {
    borderRadius: {
      DEFAULT: "var(--scalar-radius)",
      md: "var(--scalar-radius)",
      lg: "var(--scalar-radius-lg)",
      xl: "var(--scalar-radius-xl)",
      px: "1px",
      full: "9999px",
      none: "0px"
    },
    borderWidth: {
      DEFAULT: "var(--scalar-border-width)",
      0: "0",
      "1/2": "calc(var(--scalar-border-width) / 2)",
      1: "var(--scalar-border-width)",
      2: "calc(var(--scalar-border-width) * 2)",
      4: "calc(var(--scalar-border-width) * 4)"
    },
    boxShadow: {
      DEFAULT: "var(--scalar-shadow-1)",
      lg: "var(--scalar-shadow-2)",
      md: "var(--scalar-shadow-1)",
      sm: "rgba(0, 0, 0, 0.09) 0px 1px 4px",
      none: "0 0 #0000",
      border: "inset 0 0 0 1px var(--scalar-border-color)",
      "border-1/2": "inset 0 0 0 .5px var(--scalar-border-color)"
    },
    fontFamily: {
      DEFAULT: "var(--scalar-font)",
      sans: "var(--scalar-font)",
      code: "var(--scalar-font-code)"
    },
    fontSize: {
      "3xs": "var(--scalar-font-size-7)",
      xxs: "var(--scalar-font-size-6)",
      xs: "var(--scalar-font-size-5)",
      sm: "var(--scalar-font-size-4)",
      base: "var(--scalar-font-size-3)",
      lg: "var(--scalar-font-size-2)",
      xl: "var(--scalar-font-size-1)"
    },
    fontWeight: {
      DEFAULT: "var(--scalar-regular)",
      normal: "var(--scalar-regular)",
      medium: "var(--scalar-semibold)",
      bold: "var(--scalar-bold)"
    },
    colors: {
      current: "currentColor",
      inherit: "inherit",
      // Backdrop Colors
      b: {
        1: "var(--scalar-background-1)",
        2: "var(--scalar-background-2)",
        3: "var(--scalar-background-3)",
        4: "var(--scalar-background-3)",
        accent: "var(--scalar-background-accent)",
        btn: "var(--scalar-button-1)",
        danger: "var(--scalar-background-danger)",
        alert: "var(--scalar-background-alert)"
      },
      // Foreground / Text Colors
      c: {
        1: "var(--scalar-color-1)",
        2: "var(--scalar-color-2)",
        3: "var(--scalar-color-3)",
        accent: "var(--scalar-color-accent)",
        ghost: "var(--scalar-color-ghost)",
        disabled: "var(--scalar-color-disabled)",
        btn: "var(--scalar-button-1-color)",
        danger: "var(--scalar-color-danger)",
        alert: "var(--scalar-color-alert)"
      },
      // Hover Colors
      h: {
        btn: "var(--scalar-button-1-hover)"
      },
      // Sidebar Colors
      sidebar: {
        b: {
          1: "var(--scalar-sidebar-background-1, var(--scalar-background-1))"
        },
        c: {
          1: "var(--scalar-sidebar-color-1, var(--scalar-color-1))",
          2: "var(--scalar-sidebar-color-2, var(--scalar-color-2))"
        },
        border: "var(--scalar-sidebar-border-color, var(--scalar-border-color))"
      },
      // Utility Colors
      backdrop: "rgba(0, 0, 0, 0.22)",
      // Overlay Backdrops
      backdropdark: "rgba(0, 0, 0, 0.45)",
      // Overlay Backdrops
      border: "var(--scalar-border-color)",
      brand: "var(--scalar-brand)",
      // Themed Primary Colors
      green: "var(--scalar-color-green)",
      red: "var(--scalar-color-red)",
      yellow: "var(--scalar-color-yellow)",
      blue: "var(--scalar-color-blue)",
      orange: "var(--scalar-color-orange)",
      purple: "var(--scalar-color-purple)",
      grey: "var(--scalar-color-3)",
      indigo: "var(--scalar-color-blue)",
      pink: "var(--scalar-color-pink)",
      // Hard-coded Colors
      white: "#FFF",
      transparent: "transparent"
    },
    lineHeight: {
      none: "1",
      tight: "1.25",
      snug: "1.375",
      normal: "1.5",
      relaxed: "1.625",
      loose: "2",
      DEFAULT: "1.5",
      1: "var(--scalar-line-height-1)",
      2: "var(--scalar-line-height-2)",
      3: "var(--scalar-line-height-3)",
      4: "var(--scalar-line-height-4)",
      5: "var(--scalar-line-height-5)"
    },
    screens: {
      /** Mobile */
      xs: "400px",
      /** Large Mobile */
      sm: "600px",
      /** Tablet */
      md: "800px",
      /** Desktop */
      lg: "1000px",
      /** Ultrawide and larger */
      xl: "1200px",
      /** Custom breakpoint for zoomed in screens (should trigger at about 200% zoom) */
      zoomed: { raw: "(max-width: 720px) and (max-height: 480px)" }
    },
    zIndex: {
      "-1": "-1",
      0: "0",
      1: "1"
    },
    extend: {
      borderColor: { DEFAULT: "var(--scalar-border-color)" },
      brightness: {
        lifted: "var(--scalar-lifted-brightness)",
        backdrop: "var(--scalar-backdrop-brightness)"
      },
      spacing: {
        px: "1px",
        header: "48px",
        border: "var(--scalar-border-width)"
      }
    }
  }
};

// node_modules/@scalar/use-hooks/dist/useBreakpoints/useBreakpoints.js
function a() {
  const t = l.theme.screens, s3 = Object.fromEntries(
    Object.entries(t).map(([r, e2]) => [
      r,
      useMediaQuery(typeof e2 == "string" ? `(min-width: ${e2})` : e2.raw)
    ])
  ), n = computed(
    () => Object.fromEntries(
      Object.entries(s3).map(([r, e2]) => [r, unref(e2)])
    )
  );
  return {
    /** The screen sizes defined in the preset */
    screens: t,
    /** Reactive media queries for each of the screen sizes */
    mediaQueries: s3,
    /** The breakpoints as reactive media queries */
    breakpoints: n
  };
}

// node_modules/@scalar/api-client/dist/components/Sidebar/Sidebar.vue2.js
var B = {
  key: 0,
  class: "xl:min-h-client-header flex min-h-12 items-center justify-between px-3 py-1.5 text-sm md:px-[18px] md:py-2.5"
};
var I = { class: "m-0 whitespace-nowrap text-sm font-medium" };
var X = { class: "bg-b-1 has-[.empty-sidebar-item]:border-t-1/2 relative sticky bottom-0 z-10 w-[inherit] pt-0 md:px-2.5 md:pb-2.5" };
var q = defineComponent({
  __name: "Sidebar",
  props: {
    title: {}
  },
  setup(R) {
    const { isSidebarOpen: y } = m(), { sidebarWidth: o, setSidebarWidth: i } = je(), { layout: c } = s(), a2 = ref(false), p = ref(null), { breakpoints: d } = a(), w = (e2) => {
      e2.preventDefault();
      const f = e2.clientX, x = Number.parseInt(
        getComputedStyle(p.value).width || o.value,
        10
      ), b = (k) => {
        a2.value = true, document.body.classList.add("dragging");
        let s3 = x + k.clientX - f;
        s3 > 420 && (s3 = 420 + (s3 - 420) * 0.2), s3 < 240 && (s3 = 240), i(`${s3}px`);
      }, v = () => {
        a2.value = false, document.body.classList.remove("dragging"), document.documentElement.removeEventListener("mousemove", b, false), document.documentElement.removeEventListener("mouseup", v, false), Number.parseInt(o.value, 10) > 420 ? i("360px") : Number.parseInt(o.value, 10) < 240 && i("240px");
      };
      document.documentElement.addEventListener("mousemove", b, false), document.documentElement.addEventListener("mouseup", v, false);
    };
    return (e2, f) => withDirectives((openBlock(), createElementBlock("aside", {
      ref_key: "sidebarRef",
      ref: p,
      class: normalizeClass(["sidebar bg-b-1 md:border-r-1/2 relative flex min-w-full flex-1 flex-col overflow-hidden leading-3 md:min-w-fit md:flex-none md:border-b-0", { dragging: a2.value }]),
      style: normalizeStyle({ width: unref(d).lg ? unref(o) : "100%" })
    }, [
      renderSlot(e2.$slots, "header", {}, void 0, true),
      unref(c) !== "modal" && e2.title ? (openBlock(), createElementBlock("div", B, [
        createBaseVNode("h2", I, toDisplayString(e2.title), 1),
        unref(d).lg ? createCommentVNode("", true) : renderSlot(e2.$slots, "button", { key: 0 }, void 0, true)
      ])) : createCommentVNode("", true),
      createBaseVNode("div", {
        class: normalizeClass(["custom-scroll sidebar-height w-[inherit] pb-0 md:pb-[37px]", {
          "sidebar-mask": unref(c) !== "modal"
        }])
      }, [
        renderSlot(e2.$slots, "content", {}, void 0, true)
      ], 2),
      unref(d).lg ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        createBaseVNode("div", X, [
          renderSlot(e2.$slots, "button", {}, void 0, true)
        ]),
        createBaseVNode("div", {
          class: "resizer",
          onMousedown: w
        }, null, 32)
      ], 64)) : createCommentVNode("", true)
    ], 6)), [
      [vShow, unref(y)]
    ]);
  }
});

// node_modules/@scalar/api-client/dist/components/Sidebar/Sidebar.vue.js
var m3 = s2(q, [["__scopeId", "data-v-d9639e58"]]);

export {
  a,
  m3 as m
};
//# sourceMappingURL=chunk-EKB3HY5M.js.map

import {
  q
} from "./chunk-4LSRZQCZ.js";
import {
  w as w2
} from "./chunk-PEQF4OOI.js";
import {
  N
} from "./chunk-VJPZRQQL.js";
import "./chunk-RBNUC7A3.js";
import "./chunk-GXEHGJ3I.js";
import "./chunk-FXOF7VZK.js";
import "./chunk-EFYDGGJO.js";
import {
  F,
  REGEX,
  je
} from "./chunk-K3FRCNXE.js";
import "./chunk-LNIR43HZ.js";
import {
  $,
  E2 as E,
  Fragment,
  c,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createStaticVNode,
  createVNode,
  defineComponent,
  f,
  m3 as m,
  openBlock,
  ref,
  renderList,
  toDisplayString,
  unref,
  w3 as w,
  watch,
  withCtx,
  x
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-O3EO7ESF.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-2S2EUIOI.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/CollectionServerForm.vue.js
var C = { class: "bg-b-1 divide-0.5 flex w-full flex-col divide-y text-sm" };
var W = defineComponent({
  __name: "CollectionServerForm",
  props: {
    collectionId: { default: "" },
    serverUid: { default: "" }
  },
  setup(v) {
    const l = v, { activeWorkspaceCollections: s } = F(), { servers: d, serverMutators: i } = je(), p = [
      {
        label: "URL",
        key: "url",
        placeholder: "https://void.scalar.com",
        type: "text"
      },
      {
        label: "Description",
        key: "description",
        placeholder: "Production",
        type: "text"
      }
    ], t = computed(() => {
      const e = s.value.find(
        (r) => r.uid === l.collectionId
      );
      return d[e && typeof l.serverUid == "string" && l.serverUid === "default" ? e.servers[0] ?? "" : (e == null ? void 0 : e.servers.find((r) => r === l.serverUid)) ?? ""];
    }), f2 = computed(() => {
      var e, r;
      return (e = t.value) != null && e.url ? ((r = t.value.url.match(REGEX.PATH)) == null ? void 0 : r.map((a) => a.slice(1, -1))) ?? [] : [];
    });
    watch(
      f2,
      (e) => {
        if (!t.value) return;
        const r = t.value.variables ? { ...t.value.variables } : {};
        Object.keys(r).forEach((a) => {
          e.includes(a) || delete r[a];
        }), e.forEach((a) => {
          r[a] || (r[a] = { default: "" });
        }), i.edit(t.value.uid, "variables", r);
      },
      { immediate: true }
    );
    const m2 = (e, r) => {
      !s.value || !t.value || i.edit(t.value.uid, e, r);
    }, b = (e, r) => {
      if (!t.value) return;
      const a = t.value.variables || {};
      a[e] = { ...a[e], default: r }, i.edit(t.value.uid, "variables", a);
    };
    return (e, r) => (openBlock(), createElementBlock("div", C, [
      t.value ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        createVNode(q, {
          data: t.value,
          onUpdate: m2,
          options: p
        }, null, 8, ["data"]),
        t.value.variables ? (openBlock(), createBlock(N, {
          key: 0,
          variables: t.value.variables,
          "onUpdate:variable": b
        }, null, 8, ["variables"])) : createCommentVNode("", true)
      ], 64)) : createCommentVNode("", true)
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Collection/CollectionServers.vue2.js
var O = { class: "flex h-full w-full flex-col gap-12 px-1.5 pt-8" };
var R = { class: "flex flex-col gap-4" };
var T = { class: "bg-b-2 overflow-hidden rounded-lg border" };
var W2 = { class: "flex items-center justify-between py-1 pl-3 pr-1 text-sm" };
var Y = { key: 1 };
var q2 = { class: "text-c-3 flex h-full items-center justify-center rounded-lg border p-4" };
var te = defineComponent({
  __name: "CollectionServers",
  setup(G) {
    const { activeCollection: o } = F(), { servers: f2, events: h, serverMutators: _ } = je(), u = E(), d = ref(null), S = computed(() => {
      var s;
      return !f2 || !((s = o.value) != null && s.servers) ? [] : Object.values(f2).filter(
        (t) => {
          var c2;
          return (c2 = o.value) == null ? void 0 : c2.servers.includes(t.uid);
        }
      );
    }), b = () => h.commandPalette.emit({
      commandName: "Add Server"
    }), g = () => {
      var s;
      !((s = o.value) != null && s.uid) || !d.value || (_.delete(d.value, o.value.uid), u.hide());
    }, k = (s) => {
      d.value = s, u.show();
    };
    return (s, t) => {
      var c2;
      return openBlock(), createElementBlock("div", O, [
        createBaseVNode("div", R, [
          t[3] || (t[3] = createStaticVNode('<div class="flex items-start justify-between gap-2"><div class="flex flex-col"><div class="flex h-8 items-center"><h3 class="font-bold">Servers</h3></div><p class="text-sm"> Add different base URLs for your API. You can use <code class="font-code text-c-2">{variables}</code> for dynamic parts. </p></div></div>', 1)),
          (openBlock(true), createElementBlock(Fragment, null, renderList(S.value, (r, y) => (openBlock(), createElementBlock("div", {
            key: r.uid
          }, [
            createBaseVNode("div", T, [
              createBaseVNode("div", W2, [
                r.description ? (openBlock(), createBlock(unref(w), {
                  key: 0,
                  value: r.description
                }, null, 8, ["value"])) : (openBlock(), createElementBlock("span", Y, "Server " + toDisplayString(y + 1), 1)),
                createVNode(unref(x), { placement: "bottom-end" }, {
                  items: withCtx(() => [
                    createVNode(unref(m), {
                      class: "flex gap-2 font-normal",
                      onClick: (H) => k(r.uid)
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(c), {
                          class: "inline-flex",
                          icon: "Delete",
                          size: "sm",
                          thickness: "1.5"
                        }),
                        t[1] || (t[1] = createBaseVNode("span", null, "Delete", -1))
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ]),
                  default: withCtx(() => [
                    createVNode(unref($), {
                      class: "hover:bg-b-3 h-full max-h-8 gap-1 p-1 text-xs",
                      variant: "ghost"
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(c), {
                          class: "text-c-3",
                          icon: "Ellipses",
                          size: "md"
                        })
                      ]),
                      _: 1
                    })
                  ]),
                  _: 2
                }, 1024)
              ]),
              unref(o) ? (openBlock(), createBlock(W, {
                key: 0,
                collectionId: unref(o).uid,
                serverUid: r.uid
              }, null, 8, ["collectionId", "serverUid"])) : createCommentVNode("", true)
            ])
          ]))), 128)),
          createBaseVNode("div", q2, [
            createVNode(unref($), {
              class: "hover:bg-b-2 hover:text-c-1 flex items-center gap-2",
              size: "sm",
              variant: "ghost",
              onClick: b
            }, {
              default: withCtx(() => [
                createVNode(unref(c), {
                  class: "inline-flex",
                  icon: "Add",
                  size: "sm",
                  thickness: "1.5"
                }),
                t[2] || (t[2] = createBaseVNode("span", null, "Add Server", -1))
              ]),
              _: 1
            })
          ])
        ]),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(u),
          title: `Delete ${d.value ? (c2 = unref(f2)[d.value]) == null ? void 0 : c2.url : "Server"}`
        }, {
          default: withCtx(() => [
            createVNode(w2, {
              variableName: "Server",
              warningMessage: "Are you sure you want to delete this server? This action cannot be undone.",
              onClose: t[0] || (t[0] = (r) => unref(u).hide()),
              onDelete: g
            })
          ]),
          _: 1
        }, 8, ["state", "title"])
      ]);
    };
  }
});
export {
  te as default
};
//# sourceMappingURL=CollectionServers.vue-IPDT2ZPA.js.map

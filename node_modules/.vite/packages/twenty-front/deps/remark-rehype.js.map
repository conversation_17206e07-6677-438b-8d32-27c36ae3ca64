{"version": 3, "sources": ["../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/blockquote.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/break.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/code.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/delete.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/emphasis.js", "../../../../micromark-util-symbol/codes.js", "../../../../micromark-util-character/dev/lib/unicode-punctuation-regex.js", "../../../../micromark-util-character/dev/index.js", "../../../../micromark-util-symbol/values.js", "../../../../micromark-util-sanitize-uri/dev/index.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/footnote.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/heading.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/html.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/revert.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/image-reference.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/image.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/inline-code.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/link-reference.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/link.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/list-item.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/list.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/paragraph.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/root.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/strong.js", "../../../../unist-util-position/lib/index.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/table.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/table-row.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/table-cell.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/text.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/handlers/index.js", "../../../../unist-util-is/lib/index.js", "../../../../remark-rehype/node_modules/unist-util-visit-parents/lib/color.browser.js", "../../../../remark-rehype/node_modules/unist-util-visit-parents/lib/index.js", "../../../../remark-rehype/node_modules/unist-util-visit/lib/index.js", "../../../../unist-util-generated/lib/index.js", "../../../../mdast-util-definitions/node_modules/unist-util-visit-parents/lib/color.browser.js", "../../../../mdast-util-definitions/node_modules/unist-util-visit-parents/lib/index.js", "../../../../mdast-util-definitions/node_modules/unist-util-visit/lib/index.js", "../../../../mdast-util-definitions/lib/index.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/state.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/footer.js", "../../../../remark-rehype/node_modules/mdast-util-to-hast/lib/index.js", "../../../../remark-rehype/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  // To do: next major, use `node.lang` w/o regex, the splitting’s been going\n  // on for years in remark now.\n  const lang = node.lang ? node.lang.match(/^[^ \\t]+(?=[ \\t]|$)/) : null\n  /** @type {Properties} */\n  const properties = {}\n\n  if (lang) {\n    properties.className = ['language-' + lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n\n */\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65279,\n  // Unicode Specials block.\n  replacementCharacter: 65533 // `�`\n})\n", "// This module is generated by `script/`.\n//\n// CommonMark handles attention (emphasis, strong) markers based on what comes\n// before or after them.\n// One such difference is if those characters are Unicode punctuation.\n// This script is generated from the Unicode data.\n\n/**\n * Regular expression that matches a unicode punctuation character.\n */\nexport const unicodePunctuationRegex =\n  /[!-/:-@[-`{-~\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061D-\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1B7D\\u1B7E\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52-\\u2E5D\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\n", "/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {unicodePunctuationRegex} from './lib/unicode-punctuation-regex.js'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(unicodePunctuationRegex)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n * @returns {(code: Code) => boolean}\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && regex.test(String.fromCharCode(code))\n  }\n}\n", "/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n", "import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55295 && code < 57344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56320 && next > 56319 && next < 57344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n", "/**\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('hast').Element} Element\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  if (index === -1) {\n    state.footnoteOrder.push(id)\n    state.footnoteCounts[id] = 1\n    counter = state.footnoteOrder.length\n  } else {\n    state.footnoteCounts[id]++\n    counter = index + 1\n  }\n\n  const reuseCounter = state.footnoteCounts[id]\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + state.clobberPrefix + 'fn-' + safeId,\n      id:\n        state.clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Footnote} Footnote\n * @typedef {import('../state.js').State} State\n */\n\nimport {footnoteReference} from './footnote-reference.js'\n\n// To do: when both:\n// * <https://github.com/micromark/micromark-extension-footnote>\n// * <https://github.com/syntax-tree/mdast-util-footnote>\n// …are archived, remove this (also from mdast).\n// These inline notes are not used in GFM.\n\n/**\n * Turn an mdast `footnote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Footnote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnote(state, node) {\n  const footnoteById = state.footnoteById\n  let no = 1\n\n  while (no in footnoteById) no++\n\n  const identifier = String(no)\n\n  footnoteById[identifier] = {\n    type: 'footnoteDefinition',\n    identifier,\n    children: [{type: 'paragraph', children: node.children}],\n    position: node.position\n  }\n\n  return footnoteReference(state, {\n    type: 'footnoteReference',\n    identifier,\n    position: node.position\n  })\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').HTML} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Raw | Element | null}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.dangerous) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  // To do: next major: return `undefined`.\n  return null\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Reference} Reference\n * @typedef {import('mdast').Root} Root\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Reference>} References\n */\n\n// To do: next major: always return array.\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {References} node\n *   Reference node (image, link).\n * @returns {ElementContent | Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return {type: 'text', value: '![' + node.alt + suffix}\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(def.url || ''), alt: node.alt}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(def.url || '')}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItem<PERSON>oose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === undefined || spread === null\n    ? node.children.length > 1\n    : spread\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastRoot | HastElement}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {NodeLike | Node | null | undefined} [node]\n *   Node.\n * @returns {Position}\n *   Position.\n */\nexport function position(node) {\n  return {start: pointStart(node), end: pointEnd(node)}\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'start' | 'end'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {NodeLike | Node | null | undefined} [node]\n   * @returns {Point}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    // To do: next major: don’t return points when invalid.\n    return {\n      // @ts-expect-error: in practice, null is allowed.\n      line: point.line || null,\n      // @ts-expect-error: in practice, null is allowed.\n      column: point.column || null,\n      // @ts-expect-error: in practice, null is allowed.\n      offset: point.offset > -1 ? point.offset : null\n    }\n  }\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointStart, pointEnd} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start.line && end.line) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Parent>} Parents\n */\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | null | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(node, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastText | HastElement}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {footnote} from './footnote.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  footnote,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  // To do: next major: return `undefined`.\n  return null\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nexport const convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n", "/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from './color.js'\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = convert(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `<PERSON><PERSON><PERSON>`.\n *\n * Returns the ancestor when `Child` can be a child of `<PERSON><PERSON><PERSON>`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ances<PERSON> extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      visitParents(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n", "/**\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef NodeLike\n * @property {PositionLike | null | undefined} [position]\n */\n\n/**\n * Check if `node` is generated.\n *\n * @param {NodeLike | null | undefined} [node]\n *   Node to check.\n * @returns {boolean}\n *   Whether `node` is generated (does not have positional info).\n */\nexport function generated(node) {\n  return (\n    !node ||\n    !node.position ||\n    !node.position.start ||\n    !node.position.start.line ||\n    !node.position.start.column ||\n    !node.position.end ||\n    !node.position.end.line ||\n    !node.position.end.column\n  )\n}\n", "/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from './color.js'\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = convert(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * Check if `Child` can be a child of `<PERSON><PERSON><PERSON>`.\n *\n * Returns the ancestor when `Child` can be a child of `<PERSON><PERSON><PERSON>`, or returns\n * `never`.\n *\n * @template {Node} Ancestor\n *   Node type.\n * @template {Node} Child\n *   Node type.\n * @typedef {(\n *   Ances<PERSON> extends Parent\n *     ? Child extends Ancestor['children'][number]\n *       ? Ancestor\n *       : never\n *     : never\n * )} ParentsOf\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends Node ? number | null : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends Node ? Ancestor | null : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * Build a typed `Visitor` function from a node and all possible parents.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Visited\n *   Node type.\n * @template {Parent} Ancestor\n *   Parent type.\n * @typedef {Visitor<Visited, ParentsOf<Ancestor, Visited>>} BuildVisitorFromMatch\n */\n\n/**\n * Build a typed `Visitor` function from a list of descendants and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     import('unist-util-visit-parents/complex-types.js').Matches<Descendant, Check>,\n *     Extract<Descendant, Parent>\n *   >\n * )} BuildVisitorFromDescendants\n */\n\n/**\n * Build a typed `Visitor` function from a tree and a test.\n *\n * It will infer which values are passed as `node` and which as `parent`.\n *\n * @template {Node} [Tree=Node]\n *   Node type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     import('unist-util-visit-parents/complex-types.js').InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visit =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        visitor = test\n        test = null\n      }\n\n      visitParents(tree, test, overload, reverse)\n\n      /**\n       * @param {Node} node\n       * @param {Array<Parent>} parents\n       */\n      function overload(node, parents) {\n        const parent = parents[parents.length - 1]\n        return visitor(\n          node,\n          parent ? parent.children.indexOf(node) : null,\n          parent\n        )\n      }\n    }\n  )\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n", "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Definition} Definition\n */\n\n/**\n * @typedef {Root | Content} Node\n *\n * @callback GetDefinition\n *   Get a definition by identifier.\n * @param {string | null | undefined} [identifier]\n *   Identifier of definition.\n * @returns {Definition | null}\n *   Definition corresponding to `identifier` or `null`.\n */\n\nimport {visit} from 'unist-util-visit'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Find definitions in `tree`.\n *\n * Uses CommonMark precedence, which means that earlier definitions are\n * preferred over duplicate later definitions.\n *\n * @param {Node} tree\n *   Tree to check.\n * @returns {GetDefinition}\n *   Getter.\n */\nexport function definitions(tree) {\n  /** @type {Record<string, Definition>} */\n  const cache = Object.create(null)\n\n  if (!tree || !tree.type) {\n    throw new Error('mdast-util-definitions expected node')\n  }\n\n  visit(tree, 'definition', (definition) => {\n    const id = clean(definition.identifier)\n    if (id && !own.call(cache, id)) {\n      cache[id] = definition\n    }\n  })\n\n  return definition\n\n  /** @type {GetDefinition} */\n  function definition(identifier) {\n    const id = clean(identifier)\n    // To do: next major: return `undefined` when not found.\n    return id && own.call(cache, id) ? cache[id] : null\n  }\n}\n\n/**\n * @param {string | null | undefined} [value]\n * @returns {string}\n */\nfunction clean(value) {\n  return String(value || '').toUpperCase()\n}\n", "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Parent} MdastParent\n * @typedef {import('mdast').Root} MdastRoot\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n * @typedef {Extract<MdastNodes, MdastParent>} MdastParents\n *\n * @typedef EmbeddedHastFields\n *   hast fields.\n * @property {string | null | undefined} [hName]\n *   Generate a specific element with this tag name instead.\n * @property {HastProperties | null | undefined} [hProperties]\n *   Generate an element with these properties instead.\n * @property {Array<HastElementContent> | null | undefined} [hChildren]\n *   Generate an element with this content instead.\n *\n * @typedef {Record<string, unknown> & EmbeddedHastFields} MdastData\n *   mdast data with embedded hast fields.\n *\n * @typedef {MdastNodes & {data?: MdastData | null | undefined}} MdastNodeWithData\n *   mdast node with embedded hast data.\n *\n * @typedef PointLike\n *   Point-like value.\n * @property {number | null | undefined} [line]\n *   Line.\n * @property {number | null | undefined} [column]\n *   Column.\n * @property {number | null | undefined} [offset]\n *   Offset.\n *\n * @typedef PositionLike\n *   Position-like value.\n * @property {PointLike | null | undefined} [start]\n *   Point-like value.\n * @property {PointLike | null | undefined} [end]\n *   Point-like value.\n *\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | null | undefined} parent\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   hast node.\n *\n * @callback HFunctionProps\n *   Signature of `state` for when props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {HastProperties} props\n *   Properties.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @callback HFunctionNoProps\n *   Signature of `state` for when no props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @typedef HFields\n *   Info on `state`.\n * @property {boolean} dangerous\n *   Whether HTML is allowed.\n * @property {string} clobberPrefix\n *   Prefix to use to prevent DOM clobbering.\n * @property {string} footnoteLabel\n *   Label to use to introduce the footnote section.\n * @property {string} footnoteLabelTagName\n *   HTML used for the footnote label.\n * @property {HastProperties} footnoteLabelProperties\n *   Properties on the HTML tag used for the footnote label.\n * @property {string} footnoteBackLabel\n *   Label to use from backreferences back to their footnote call.\n * @property {(identifier: string) => MdastDefinition | null} definition\n *   Definition cache.\n * @property {Record<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Record<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {Handler} unknownHandler\n *   Handler for any none not in `passThrough` or otherwise handled.\n * @property {(from: MdastNodes, node: HastNodes) => void} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => Type | HastElement} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {(node: MdastNodes, parent: MdastParents | null | undefined) => HastElementContent | Array<HastElementContent> | null | undefined} one\n *   Transform an mdast node to hast.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastContent>(nodes: Array<Type>, loose?: boolean | null | undefined) => Array<Type | HastText>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n * @property {(left: MdastNodeWithData | PositionLike | null | undefined, right: HastElementContent) => HastElementContent} augment\n *   Like `state` but lower-level and usable on non-elements.\n *   Deprecated: use `patch` and `applyData`.\n * @property {Array<string>} passThrough\n *   List of node types to pass through untouched (except for their children).\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree.\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` attribute on footnotes to prevent it from\n *   *clobbering*.\n * @property {string | null | undefined} [footnoteBackLabel='Back to content']\n *   Label to use from backreferences back to their footnote call (affects\n *   screen readers).\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Label to use for the footnotes section (affects screen readers).\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (note that `id: 'footnote-label'`\n *   is always added as footnote calls use it with `aria-describedby` to\n *   provide an accessible label).\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   Tag name to use for the footnote label.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes.\n * @property {Array<string> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes.\n *\n * @typedef {Record<string, Handler>} Handlers\n *   Handle nodes.\n *\n * @typedef {HFunctionProps & HFunctionNoProps & HFields} State\n *   Info passed around.\n */\n\nimport {visit} from 'unist-util-visit'\nimport {position, pointStart, pointEnd} from 'unist-util-position'\nimport {generated} from 'unist-util-generated'\nimport {definitions} from 'mdast-util-definitions'\nimport {handlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || {}\n  const dangerous = settings.allowDangerousHtml || false\n  /** @type {Record<string, MdastFootnoteDefinition>} */\n  const footnoteById = {}\n\n  // To do: next major: add `options` to state, remove:\n  // `dangerous`, `clobberPrefix`, `footnoteLabel`, `footnoteLabelTagName`,\n  // `footnoteLabelProperties`, `footnoteBackLabel`, `passThrough`,\n  // `unknownHandler`.\n\n  // To do: next major: move to `state.options.allowDangerousHtml`.\n  state.dangerous = dangerous\n  // To do: next major: move to `state.options`.\n  state.clobberPrefix =\n    settings.clobberPrefix === undefined || settings.clobberPrefix === null\n      ? 'user-content-'\n      : settings.clobberPrefix\n  // To do: next major: move to `state.options`.\n  state.footnoteLabel = settings.footnoteLabel || 'Footnotes'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelTagName = settings.footnoteLabelTagName || 'h2'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelProperties = settings.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  // To do: next major: move to `state.options`.\n  state.footnoteBackLabel = settings.footnoteBackLabel || 'Back to content'\n  // To do: next major: move to `state.options`.\n  state.unknownHandler = settings.unknownHandler\n  // To do: next major: move to `state.options`.\n  state.passThrough = settings.passThrough\n\n  state.handlers = {...handlers, ...settings.handlers}\n\n  // To do: next major: replace utility with `definitionById` object, so we\n  // only walk once (as we need footnotes too).\n  state.definition = definitions(tree)\n  state.footnoteById = footnoteById\n  /** @type {Array<string>} */\n  state.footnoteOrder = []\n  /** @type {Record<string, number>} */\n  state.footnoteCounts = {}\n\n  state.patch = patch\n  state.applyData = applyData\n  state.one = oneBound\n  state.all = allBound\n  state.wrap = wrap\n  // To do: next major: remove `augment`.\n  state.augment = augment\n\n  visit(tree, 'footnoteDefinition', (definition) => {\n    const id = String(definition.identifier).toUpperCase()\n\n    // Mimick CM behavior of link definitions.\n    // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/8290999/index.js#L26>.\n    if (!own.call(footnoteById, id)) {\n      footnoteById[id] = definition\n    }\n  })\n\n  // @ts-expect-error Hush, it’s fine!\n  return state\n\n  /**\n   * Finalise the created `right`, a hast node, from `left`, an mdast node.\n   *\n   * @param {MdastNodeWithData | PositionLike | null | undefined} left\n   * @param {HastElementContent} right\n   * @returns {HastElementContent}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function augment(left, right) {\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (left && 'data' in left && left.data) {\n      /** @type {MdastData} */\n      const data = left.data\n\n      if (data.hName) {\n        if (right.type !== 'element') {\n          right = {\n            type: 'element',\n            tagName: '',\n            properties: {},\n            children: []\n          }\n        }\n\n        right.tagName = data.hName\n      }\n\n      if (right.type === 'element' && data.hProperties) {\n        right.properties = {...right.properties, ...data.hProperties}\n      }\n\n      if ('children' in right && right.children && data.hChildren) {\n        right.children = data.hChildren\n      }\n    }\n\n    if (left) {\n      const ctx = 'type' in left ? left : {position: left}\n\n      if (!generated(ctx)) {\n        // @ts-expect-error: fine.\n        right.position = {start: pointStart(ctx), end: pointEnd(ctx)}\n      }\n    }\n\n    return right\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Create an element for `node`.\n   *\n   * @type {HFunctionProps}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function state(node, tagName, props, children) {\n    if (Array.isArray(props)) {\n      children = props\n      props = {}\n    }\n\n    // @ts-expect-error augmenting an element yields an element.\n    return augment(node, {\n      type: 'element',\n      tagName,\n      properties: props || {},\n      children: children || []\n    })\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | null | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n   *   Resulting hast node.\n   */\n  function oneBound(node, parent) {\n    // @ts-expect-error: that’s a state :)\n    return one(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function allBound(parent) {\n    // @ts-expect-error: that’s a state :)\n    return all(state, parent)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {void}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {Type | HastElement}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {Type | HastElement} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent is likely to keep the content around (otherwise: pass\n      // `hChildren`).\n      else {\n        result = {\n          type: 'element',\n          tagName: hName,\n          properties: {},\n          children: []\n        }\n\n        // To do: next major: take the children from the `root`, or inject the\n        // raw/text/comment or so into the element?\n        // if ('children' in node) {\n        //   // @ts-expect-error: assume `children` are allowed in elements.\n        //   result.children = node.children\n        // } else {\n        //   // @ts-expect-error: assume `node` is allowed in elements.\n        //   result.children.push(node)\n        // }\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      result.properties = {...result.properties, ...hProperties}\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      // @ts-expect-error: assume valid children are defined.\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an mdast node into a hast node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   mdast node.\n * @param {MdastParents | null | undefined} [parent]\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   Resulting hast node.\n */\n// To do: next major: do not expose, keep bound.\nexport function one(state, node, parent) {\n  const type = node && node.type\n\n  // Fail on non-nodes.\n  if (!type) {\n    throw new Error('Expected node, got `' + node + '`')\n  }\n\n  if (own.call(state.handlers, type)) {\n    return state.handlers[type](state, node, parent)\n  }\n\n  if (state.passThrough && state.passThrough.includes(type)) {\n    // To do: next major: deep clone.\n    // @ts-expect-error: types of passed through nodes are expected to be added manually.\n    return 'children' in node ? {...node, children: all(state, node)} : node\n  }\n\n  if (state.unknownHandler) {\n    return state.unknownHandler(state, node, parent)\n  }\n\n  return defaultUnknownHandler(state, node)\n}\n\n/**\n * Transform the children of an mdast node into hast nodes.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} parent\n *   mdast node to compile\n * @returns {Array<HastElementContent>}\n *   Resulting hast nodes.\n */\n// To do: next major: do not expose, keep bound.\nexport function all(state, parent) {\n  /** @type {Array<HastElementContent>} */\n  const values = []\n\n  if ('children' in parent) {\n    const nodes = parent.children\n    let index = -1\n    while (++index < nodes.length) {\n      const result = one(state, nodes[index], parent)\n\n      // To do: see if we van clean this? Can we merge texts?\n      if (result) {\n        if (index && nodes[index - 1].type === 'break') {\n          if (!Array.isArray(result) && result.type === 'text') {\n            result.value = result.value.replace(/^\\s+/, '')\n          }\n\n          if (!Array.isArray(result) && result.type === 'element') {\n            const head = result.children[0]\n\n            if (head && head.type === 'text') {\n              head.value = head.value.replace(/^\\s+/, '')\n            }\n          }\n        }\n\n        if (Array.isArray(result)) {\n          values.push(...result)\n        } else {\n          values.push(result)\n        }\n      }\n    }\n  }\n\n  return values\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastText | HastElement}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastText | HastElement} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: all(state, node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | null | undefined} [loose=false]\n *   Whether to add line endings at start and end.\n * @returns {Array<Type | HastText>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<Type | HastText>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\nexport function footer(state) {\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let index = -1\n\n  while (++index < state.footnoteOrder.length) {\n    const def = state.footnoteById[state.footnoteOrder[index]]\n\n    if (!def) {\n      continue\n    }\n\n    const content = state.all(def)\n    const id = String(def.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let referenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n\n    while (++referenceIndex <= state.footnoteCounts[id]) {\n      /** @type {Element} */\n      const backReference = {\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            state.clobberPrefix +\n            'fnref-' +\n            safeId +\n            (referenceIndex > 1 ? '-' + referenceIndex : ''),\n          dataFootnoteBackref: true,\n          className: ['data-footnote-backref'],\n          ariaLabel: state.footnoteBackLabel\n        },\n        children: [{type: 'text', value: '↩'}]\n      }\n\n      if (referenceIndex > 1) {\n        backReference.children.push({\n          type: 'element',\n          tagName: 'sup',\n          children: [{type: 'text', value: String(referenceIndex)}]\n        })\n      }\n\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      backReferences.push(backReference)\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: state.clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(def, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: state.footnoteLabelTagName,\n        properties: {\n          // To do: use structured clone.\n          ...JSON.parse(JSON.stringify(state.footnoteLabelProperties)),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: state.footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n", "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Root} HastRoot\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Root} MdastRoot\n *\n * @typedef {import('./state.js').Options} Options\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n */\n\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {HastNodes | null | undefined}\n *   hast tree.\n */\n// To do: next major: always return a single `root`.\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, null)\n  const foot = footer(state)\n\n  if (foot) {\n    // @ts-expect-error If there’s a footer, there were definitions, meaning block\n    // content.\n    // So assume `node` is a parent node.\n    node.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  // To do: next major: always return root?\n  return Array.isArray(node) ? {type: 'root', children: node} : node\n}\n", "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('mdast-util-to-hast').Options} Options\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n *\n * @typedef {import('mdast-util-to-hast')} DoNotTouchAsThisImportIncludesRawInTree\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n// Note: the `<MdastRoot, HastRoot>` overload doesn’t seem to work :'(\n\n/**\n * Plugin that turns markdown into HTML to support rehype.\n *\n * *   If a destination processor is given, that processor runs with a new HTML\n *     (hast) tree (bridge-mode).\n *     As the given processor runs with a hast tree, and rehype plugins support\n *     hast, that means rehype plugins can be used with the given processor.\n *     The hast tree is discarded in the end.\n *     It’s highly unlikely that you want to do this.\n * *   The common case is to not pass a destination processor, in which case the\n *     current processor continues running with a new HTML (hast) tree\n *     (mutate-mode).\n *     As the current processor continues with a hast tree, and rehype plugins\n *     support hast, that means rehype plugins can be used after\n *     `remark-rehype`.\n *     It’s likely that this is what you want to do.\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `mdast-util-to-hast`.\n */\nconst remarkRehype =\n  /** @type {(import('unified').Plugin<[Processor, Options?]|[null|undefined, Options?]|[Options]|[], MdastRoot>)} */\n  (\n    function (destination, options) {\n      return destination && 'run' in destination\n        ? bridge(destination, options)\n        : mutate(destination || options)\n    }\n  )\n\nexport default remarkRehype\n\n/**\n * Bridge-mode.\n * Runs the destination with the new hast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], MdastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run(toHast(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further plugins run on the hast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], MdastRoot, HastRoot>}\n */\nfunction mutate(options) {\n  // @ts-expect-error: assume a corresponding node is returned by `toHast`.\n  return (node) => toHast(node, options)\n}\n"], "mappings": ";;;;;;AAgBO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAC5C;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AAC5E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,CAAC,MAAM,UAAU,MAAM,MAAM,GAAG,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AACpE;;;ACJO,SAAS,KAAK,OAAO,MAAM;AAChC,QAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAG/C,QAAM,OAAO,KAAK,OAAO,KAAK,KAAK,MAAM,qBAAqB,IAAI;AAElE,QAAM,aAAa,CAAC;AAEpB,MAAI,MAAM;AACR,eAAW,YAAY,CAAC,cAAc,IAAI;AAAA,EAC5C;AAIA,MAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,MAAK,CAAC;AAAA,EAClC;AAEA,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,EAAC,MAAM,KAAK,KAAI;AAAA,EAChC;AAEA,QAAM,MAAM,MAAM,MAAM;AACxB,WAAS,MAAM,UAAU,MAAM,MAAM;AAGrC,WAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,CAAC,GAAG,UAAU,CAAC,MAAM,EAAC;AAC7E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO;AACT;;;ACjCO,SAAS,cAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACXO,SAAS,SAAS,OAAO,MAAM;AAEpC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,IAAM;AAAA;AAAA,EAA8B;AAAA,IACzC,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA;AAAA,IACjB,eAAe;AAAA;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,iBAAiB;AAAA;AAAA,IACjB,kBAAkB;AAAA;AAAA,IAClB,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,KAAK;AAAA;AAAA,IACL,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,mBAAmB;AAAA;AAAA,IACnB,WAAW;AAAA;AAAA,IACX,oBAAoB;AAAA;AAAA,IACpB,OAAO;AAAA;AAAA,IACP,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,OAAO;AAAA;AAAA,IACP,KAAK;AAAA;AAAA,IAEL,iBAAiB;AAAA;AAAA,IAEjB,sBAAsB;AAAA;AAAA,EACxB;AAAA;;;ACnJO,IAAM,0BACX;;;ACaK,IAAM,aAAa,WAAW,UAAU;AAcxC,IAAM,oBAAoB,WAAW,YAAY;AAuBjD,IAAM,aAAa,WAAW,qBAAqB;AAgCnD,IAAM,aAAa,WAAW,IAAI;AAoBlC,IAAM,gBAAgB,WAAW,YAAY;AAe7C,IAAM,mBAAmB,WAAW,gBAAgB;AA8EpD,IAAM,qBAAqB,WAAW,uBAAuB;AAsB7D,IAAM,oBAAoB,WAAW,IAAI;AAQhD,SAAS,WAAW,OAAO;AACzB,SAAO;AAUP,WAAS,MAAMA,OAAM;AACnB,WAAOA,UAAS,QAAQ,MAAM,KAAK,OAAO,aAAaA,KAAI,CAAC;AAAA,EAC9D;AACF;;;AClPO,IAAM;AAAA;AAAA,EAA+B;AAAA,IAC1C,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA;;;AC1CO,SAAS,aAAa,OAAO;AAElC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAMC,QAAO,MAAM,WAAW,KAAK;AAEnC,QAAI,UAAU;AAGd,QACEA,UAAS,MAAM,eACf,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,KAC7C,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,GAC7C;AACA,aAAO;AAAA,IACT,WAESA,QAAO,KAAK;AACnB,UAAI,CAAC,oBAAoB,KAAK,OAAO,aAAaA,KAAI,CAAC,GAAG;AACxD,kBAAU,OAAO,aAAaA,KAAI;AAAA,MACpC;AAAA,IACF,WAESA,QAAO,SAASA,QAAO,OAAO;AACrC,YAAM,OAAO,MAAM,WAAW,QAAQ,CAAC;AAGvC,UAAIA,QAAO,SAAS,OAAO,SAAS,OAAO,OAAO;AAChD,kBAAU,OAAO,aAAaA,OAAM,IAAI;AACxC,eAAO;AAAA,MACT,OAEK;AACH,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF,OAEK;AACH,gBAAU,OAAO,aAAaA,KAAI;AAAA,IACpC;AAEA,QAAI,SAAS;AACX,aAAO,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,mBAAmB,OAAO,CAAC;AAClE,cAAQ,QAAQ,OAAO;AACvB,gBAAU;AAAA,IACZ;AAEA,QAAI,MAAM;AACR,eAAS;AACT,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,OAAO,KAAK,EAAE,IAAI,MAAM,MAAM,KAAK;AAC5C;;;AC1GO,SAAS,kBAAkB,OAAO,MAAM;AAC7C,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAM,QAAQ,MAAM,cAAc,QAAQ,EAAE;AAE5C,MAAI;AAEJ,MAAI,UAAU,IAAI;AAChB,UAAM,cAAc,KAAK,EAAE;AAC3B,UAAM,eAAe,EAAE,IAAI;AAC3B,cAAU,MAAM,cAAc;AAAA,EAChC,OAAO;AACL,UAAM,eAAe,EAAE;AACvB,cAAU,QAAQ;AAAA,EACpB;AAEA,QAAM,eAAe,MAAM,eAAe,EAAE;AAG5C,QAAMC,QAAO;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,MACV,MAAM,MAAM,MAAM,gBAAgB,QAAQ;AAAA,MAC1C,IACE,MAAM,gBACN,WACA,UACC,eAAe,IAAI,MAAM,eAAe;AAAA,MAC3C,iBAAiB;AAAA,MACjB,iBAAiB,CAAC,gBAAgB;AAAA,IACpC;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,OAAO,EAAC,CAAC;AAAA,EACnD;AACA,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,MAAM;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,GAAG;AACrB,SAAO,MAAM,UAAU,MAAM,GAAG;AAClC;;;ACvCO,SAAS,SAAS,OAAO,MAAM;AACpC,QAAM,eAAe,MAAM;AAC3B,MAAI,KAAK;AAET,SAAO,MAAM,aAAc;AAE3B,QAAM,aAAa,OAAO,EAAE;AAE5B,eAAa,UAAU,IAAI;AAAA,IACzB,MAAM;AAAA,IACN;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,aAAa,UAAU,KAAK,SAAQ,CAAC;AAAA,IACvD,UAAU,KAAK;AAAA,EACjB;AAEA,SAAO,kBAAkB,OAAO;AAAA,IAC9B,MAAM;AAAA,IACN;AAAA,IACA,UAAU,KAAK;AAAA,EACjB,CAAC;AACH;;;AC5BO,SAAS,QAAQ,OAAO,MAAM;AAEnC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,MAAM,KAAK;AAAA,IACpB,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,SAAS,KAAK,OAAO,MAAM;AAChC,MAAI,MAAM,WAAW;AAEnB,UAAM,SAAS,EAAC,MAAM,OAAO,OAAO,KAAK,MAAK;AAC9C,UAAM,MAAM,MAAM,MAAM;AACxB,WAAO,MAAM,UAAU,MAAM,MAAM;AAAA,EACrC;AAGA,SAAO;AACT;;;ACDO,SAAS,OAAO,OAAO,MAAM;AAClC,QAAM,UAAU,KAAK;AACrB,MAAI,SAAS;AAEb,MAAI,YAAY,aAAa;AAC3B,cAAU;AAAA,EACZ,WAAW,YAAY,QAAQ;AAC7B,cAAU,OAAO,KAAK,SAAS,KAAK,cAAc;AAAA,EACpD;AAEA,MAAI,KAAK,SAAS,kBAAkB;AAClC,WAAO,EAAC,MAAM,QAAQ,OAAO,OAAO,KAAK,MAAM,OAAM;AAAA,EACvD;AAEA,QAAM,WAAW,MAAM,IAAI,IAAI;AAC/B,QAAM,OAAO,SAAS,CAAC;AAEvB,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,QAAQ,MAAM,KAAK;AAAA,EAC1B,OAAO;AACL,aAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,EAC7C;AAEA,QAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AAEzC,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,SAAS;AAAA,EAChB,OAAO;AACL,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,OAAM,CAAC;AAAA,EAC7C;AAEA,SAAO;AACT;;;ACtCO,SAAS,eAAe,OAAO,MAAM;AAC1C,QAAM,MAAM,MAAM,WAAW,KAAK,UAAU;AAE5C,MAAI,CAAC,KAAK;AACR,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,KAAK,aAAa,IAAI,OAAO,EAAE,GAAG,KAAK,KAAK,IAAG;AAEnE,MAAI,IAAI,UAAU,QAAQ,IAAI,UAAU,QAAW;AACjD,eAAW,QAAQ,IAAI;AAAA,EACzB;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpBO,SAAS,MAAM,OAAO,MAAM;AAEjC,QAAM,aAAa,EAAC,KAAK,aAAa,KAAK,GAAG,EAAC;AAE/C,MAAI,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAW;AAC/C,eAAW,MAAM,KAAK;AAAA,EACxB;AAEA,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AClBO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAMC,QAAO,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,aAAa,GAAG,EAAC;AACvE,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACVO,SAAS,cAAc,OAAO,MAAM;AACzC,QAAM,MAAM,MAAM,WAAW,KAAK,UAAU;AAE5C,MAAI,CAAC,KAAK;AACR,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,MAAM,aAAa,IAAI,OAAO,EAAE,EAAC;AAErD,MAAI,IAAI,UAAU,QAAQ,IAAI,UAAU,QAAW;AACjD,eAAW,QAAQ,IAAI;AAAA,EACzB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACzBO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,EAAC,MAAM,aAAa,KAAK,GAAG,EAAC;AAEhD,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,QAAM,QAAQ,SAAS,UAAU,MAAM,IAAI,cAAc,IAAI;AAE7D,QAAM,aAAa,CAAC;AAEpB,QAAM,WAAW,CAAC;AAElB,MAAI,OAAO,KAAK,YAAY,WAAW;AACrC,UAAM,OAAO,QAAQ,CAAC;AAEtB,QAAIC;AAEJ,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,MAAAA,aAAY;AAAA,IACd,OAAO;AACL,MAAAA,aAAY,EAAC,MAAM,WAAW,SAAS,KAAK,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AACxE,cAAQ,QAAQA,UAAS;AAAA,IAC3B;AAEA,QAAIA,WAAU,SAAS,SAAS,GAAG;AACjC,MAAAA,WAAU,SAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,IACvD;AAEA,IAAAA,WAAU,SAAS,QAAQ;AAAA,MACzB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,MAAM,YAAY,SAAS,KAAK,SAAS,UAAU,KAAI;AAAA,MACpE,UAAU,CAAC;AAAA,IACb,CAAC;AAID,eAAW,YAAY,CAAC,gBAAgB;AAAA,EAC1C;AAEA,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQ,KAAK;AAG3B,QACE,SACA,UAAU,KACV,MAAM,SAAS,aACf,MAAM,YAAY,KAClB;AACA,eAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,IAC3C;AAEA,QAAI,MAAM,SAAS,aAAa,MAAM,YAAY,OAAO,CAAC,OAAO;AAC/D,eAAS,KAAK,GAAG,MAAM,QAAQ;AAAA,IACjC,OAAO;AACL,eAAS,KAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAGvC,MAAI,SAAS,SAAS,KAAK,SAAS,aAAa,KAAK,YAAY,MAAM;AACtE,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EAC3C;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,SAAQ;AACpE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAMA,SAAS,UAAU,MAAM;AACvB,MAAI,QAAQ;AACZ,MAAI,KAAK,SAAS,QAAQ;AACxB,YAAQ,KAAK,UAAU;AACvB,UAAM,WAAW,KAAK;AACtB,QAAI,QAAQ;AAEZ,WAAO,CAAC,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC1C,cAAQ,cAAc,SAAS,KAAK,CAAC;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,KAAK;AAEpB,SAAO,WAAW,UAAa,WAAW,OACtC,KAAK,SAAS,SAAS,IACvB;AACN;;;AC/GO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,MAAI,QAAQ;AAEZ,MAAI,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,GAAG;AACtD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQ,KAAK;AAE3B,QACE,MAAM,SAAS,aACf,MAAM,YAAY,QAClB,MAAM,cACN,MAAM,QAAQ,MAAM,WAAW,SAAS,KACxC,MAAM,WAAW,UAAU,SAAS,gBAAgB,GACpD;AACA,iBAAW,YAAY,CAAC,oBAAoB;AAC5C;AAAA,IACF;AAAA,EACF;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,KAAK,UAAU,OAAO;AAAA,IAC/B;AAAA,IACA,UAAU,MAAM,KAAK,SAAS,IAAI;AAAA,EACpC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpCO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,EAAC;AACnE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACNO,SAAS,OAAO,OAAO,MAAM;AAElC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACGO,IAAM,aAAa,MAAM,OAAO;AAUhC,IAAM,WAAW,MAAM,KAAK;AAU5B,SAAS,SAAS,MAAM;AAC7B,SAAO,EAAC,OAAO,WAAW,IAAI,GAAG,KAAK,SAAS,IAAI,EAAC;AACtD;AAUA,SAAS,MAAM,MAAM;AACnB,SAAOC;AAQP,WAASA,OAAM,MAAM;AACnB,UAAMA,SAAS,QAAQ,KAAK,YAAY,KAAK,SAAS,IAAI,KAAM,CAAC;AAGjE,WAAO;AAAA;AAAA,MAEL,MAAMA,OAAM,QAAQ;AAAA;AAAA,MAEpB,QAAQA,OAAM,UAAU;AAAA;AAAA,MAExB,QAAQA,OAAM,SAAS,KAAKA,OAAM,SAAS;AAAA,IAC7C;AAAA,EACF;AACF;;;ACjEO,SAAS,MAAM,OAAO,MAAM;AACjC,QAAM,OAAO,MAAM,IAAI,IAAI;AAC3B,QAAM,WAAW,KAAK,MAAM;AAE5B,QAAM,eAAe,CAAC;AAEtB,MAAI,UAAU;AAEZ,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI;AAAA,IACvC;AACA,UAAM,MAAM,KAAK,SAAS,CAAC,GAAG,IAAI;AAClC,iBAAa,KAAK,IAAI;AAAA,EACxB;AAEA,MAAI,KAAK,SAAS,GAAG;AAEnB,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,MAAM,IAAI;AAAA,IACjC;AAEA,UAAM,QAAQ,WAAW,KAAK,SAAS,CAAC,CAAC;AACzC,UAAM,MAAM,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,QAAI,MAAM,QAAQ,IAAI,KAAM,MAAK,WAAW,EAAC,OAAO,IAAG;AACvD,iBAAa,KAAK,IAAI;AAAA,EACxB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,cAAc,IAAI;AAAA,EACzC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AChCO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,WAAW,SAAS,OAAO,WAAW;AAE5C,QAAM,WAAW,WAAW,SAAS,QAAQ,IAAI,IAAI;AACrD,QAAM,UAAU,aAAa,IAAI,OAAO;AACxC,QAAM,QAAQ,UAAU,OAAO,SAAS,UAAU,OAAO,QAAQ;AACjE,QAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,SAAS;AACpD,MAAI,YAAY;AAEhB,QAAM,QAAQ,CAAC;AAEf,SAAO,EAAE,YAAY,QAAQ;AAE3B,UAAM,OAAO,KAAK,SAAS,SAAS;AAEpC,UAAM,aAAa,CAAC;AACpB,UAAM,aAAa,QAAQ,MAAM,SAAS,IAAI;AAE9C,QAAI,YAAY;AACd,iBAAW,QAAQ;AAAA,IACrB;AAGA,QAAIC,UAAS,EAAC,MAAM,WAAW,SAAS,YAAY,UAAU,CAAC,EAAC;AAEhE,QAAI,MAAM;AACR,MAAAA,QAAO,WAAW,MAAM,IAAI,IAAI;AAChC,YAAM,MAAM,MAAMA,OAAM;AACxB,MAAAA,UAAS,MAAM,UAAU,MAAMA,OAAM;AAAA,IACvC;AAEA,UAAM,KAAKA,OAAM;AAAA,EACnB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,OAAO,IAAI;AAAA,EAClC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACvDO,SAAS,UAAU,OAAO,MAAM;AAIrC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,UAAU,OAAO,KAAK,KAAK,CAAC,EAAC;AAClE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,SAAS,cAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAAC;AAAA,EACb;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACEO,IAAM,WAAW;AAAA,EACtB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,oBAAoB;AACtB;AAGA,SAAS,SAAS;AAEhB,SAAO;AACT;;;ACsGO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYT,SAAU,MAAM;AACd,QAAI,SAAS,UAAa,SAAS,MAAM;AACvC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,IAAI,IAAI,aAAa,IAAI;AAAA,IACnE;AAEA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AAAA;AAOJ,SAAS,WAAW,OAAO;AAEzB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,WAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO,YAAY,GAAG;AAOtB,WAAS,OAAO,YAAY;AAC1B,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAOA,MAAK,EAAE,KAAK,MAAM,GAAG,UAAU,EAAG,QAAO;AAAA,IACtD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,aAAa,OAAO;AAC3B,SAAO,YAAYC,IAAG;AAMtB,WAASA,KAAI,MAAM;AAEjB,QAAI;AAEJ,SAAK,OAAO,OAAO;AAEjB,UAAI,KAAK,GAAG,MAAM,MAAM,GAAG,EAAG,QAAO;AAAA,IACvC;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,YAAY,OAAO;AAC1B,SAAO,YAAY,IAAI;AAKvB,WAAS,KAAK,MAAM;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B;AACF;AAQA,SAAS,YAAY,OAAO;AAC1B,SAAO;AAQP,WAAS,UAAU,SAAS,YAAY;AACtC,WAAO;AAAA,MACL,QACE,OAAO,SAAS,YAChB,UAAU;AAAA,MAEV,QAAQ,MAAM,KAAK,MAAM,MAAM,GAAG,UAAU,CAAC;AAAA,IACjD;AAAA,EACF;AACF;AAEA,SAAS,KAAK;AACZ,SAAO;AACT;;;ACxSO,SAAS,MAAM,GAAG;AACvB,SAAO;AACT;;;AC0EO,IAAM,WAAW;AAKjB,IAAM,OAAO;AAKb,IAAM,OAAO;AA+Bb,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,MAAM,MAAM,SAAS,SAAS;AACtC,QAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;AAC/D,gBAAU;AAEV,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,UAAMC,MAAK,QAAQ,IAAI;AACvB,UAAM,OAAO,UAAU,KAAK;AAE5B,YAAQ,MAAM,QAAW,CAAC,CAAC,EAAE;AAO7B,aAAS,QAAQ,MAAM,OAAO,SAAS;AAGrC,YAAM,QAAQ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;AAEzD,UAAI,OAAO,MAAM,SAAS,UAAU;AAClC,cAAM;AAAA;AAAA,UAEJ,OAAO,MAAM,YAAY,WACrB,MAAM;AAAA;AAAA,YAER,OAAO,MAAM,SAAS,WACpB,MAAM,OACN;AAAA;AAAA;AAEN,eAAO,eAAeC,QAAO,QAAQ;AAAA,UACnC,OACE,WAAW,MAAM,KAAK,QAAQ,OAAO,MAAM,OAAO,MAAM,GAAG,IAAI;AAAA,QACnE,CAAC;AAAA,MACH;AAEA,aAAOA;AAEP,eAASA,SAAQ;AAEf,YAAI,SAAS,CAAC;AAEd,YAAI;AAEJ,YAAI;AAEJ,YAAI;AAEJ,YAAI,CAAC,QAAQD,IAAG,MAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG;AACjE,mBAAS,SAAS,QAAQ,MAAM,OAAO,CAAC;AAExC,cAAI,OAAO,CAAC,MAAM,MAAM;AACtB,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,YAAI,KAAK,YAAY,OAAO,CAAC,MAAM,MAAM;AAEvC,oBAAU,UAAU,KAAK,SAAS,SAAS,MAAM;AAEjD,yBAAe,QAAQ,OAAO,IAAI;AAGlC,iBAAO,SAAS,MAAM,SAAS,KAAK,SAAS,QAAQ;AAEnD,wBAAY,QAAQ,KAAK,SAAS,MAAM,GAAG,QAAQ,YAAY,EAAE;AAEjE,gBAAI,UAAU,CAAC,MAAM,MAAM;AACzB,qBAAO;AAAA,YACT;AAEA,qBACE,OAAO,UAAU,CAAC,MAAM,WAAW,UAAU,CAAC,IAAI,SAAS;AAAA,UAC/D;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAWJ,SAAS,SAAS,OAAO;AACvB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,CAAC,UAAU,KAAK;AAAA,EACzB;AAEA,SAAO,CAAC,KAAK;AACf;;;AClGO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,MAAM,MAAM,SAAS,SAAS;AACtC,QAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;AAC/D,gBAAU;AACV,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,iBAAa,MAAM,MAAM,UAAU,OAAO;AAM1C,aAAS,SAAS,MAAM,SAAS;AAC/B,YAAM,SAAS,QAAQ,QAAQ,SAAS,CAAC;AACzC,aAAO;AAAA,QACL;AAAA,QACA,SAAS,OAAO,SAAS,QAAQ,IAAI,IAAI;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;;;AC5JG,SAAS,UAAU,MAAM;AAC9B,SACE,CAAC,QACD,CAAC,KAAK,YACN,CAAC,KAAK,SAAS,SACf,CAAC,KAAK,SAAS,MAAM,QACrB,CAAC,KAAK,SAAS,MAAM,UACrB,CAAC,KAAK,SAAS,OACf,CAAC,KAAK,SAAS,IAAI,QACnB,CAAC,KAAK,SAAS,IAAI;AAEvB;;;AC7BO,SAASE,OAAM,GAAG;AACvB,SAAO;AACT;;;AC0EO,IAAMC,YAAW;AAKjB,IAAMC,QAAO;AAKb,IAAMC,QAAO;AA+Bb,IAAMC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,MAAM,MAAM,SAAS,SAAS;AACtC,QAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;AAC/D,gBAAU;AAEV,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,UAAMC,MAAK,QAAQ,IAAI;AACvB,UAAM,OAAO,UAAU,KAAK;AAE5B,YAAQ,MAAM,QAAW,CAAC,CAAC,EAAE;AAO7B,aAAS,QAAQ,MAAM,OAAO,SAAS;AAGrC,YAAM,QAAQ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;AAEzD,UAAI,OAAO,MAAM,SAAS,UAAU;AAClC,cAAM;AAAA;AAAA,UAEJ,OAAO,MAAM,YAAY,WACrB,MAAM;AAAA;AAAA,YAER,OAAO,MAAM,SAAS,WACpB,MAAM,OACN;AAAA;AAAA;AAEN,eAAO,eAAeC,QAAO,QAAQ;AAAA,UACnC,OACE,WAAWC,OAAM,KAAK,QAAQ,OAAO,MAAM,OAAO,MAAM,GAAG,IAAI;AAAA,QACnE,CAAC;AAAA,MACH;AAEA,aAAOD;AAEP,eAASA,SAAQ;AAEf,YAAI,SAAS,CAAC;AAEd,YAAI;AAEJ,YAAI;AAEJ,YAAI;AAEJ,YAAI,CAAC,QAAQD,IAAG,MAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG;AACjE,mBAASG,UAAS,QAAQ,MAAM,OAAO,CAAC;AAExC,cAAI,OAAO,CAAC,MAAMN,OAAM;AACtB,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,YAAI,KAAK,YAAY,OAAO,CAAC,MAAMC,OAAM;AAEvC,oBAAU,UAAU,KAAK,SAAS,SAAS,MAAM;AAEjD,yBAAe,QAAQ,OAAO,IAAI;AAGlC,iBAAO,SAAS,MAAM,SAAS,KAAK,SAAS,QAAQ;AAEnD,wBAAY,QAAQ,KAAK,SAAS,MAAM,GAAG,QAAQ,YAAY,EAAE;AAEjE,gBAAI,UAAU,CAAC,MAAMD,OAAM;AACzB,qBAAO;AAAA,YACT;AAEA,qBACE,OAAO,UAAU,CAAC,MAAM,WAAW,UAAU,CAAC,IAAI,SAAS;AAAA,UAC/D;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAWJ,SAASM,UAAS,OAAO;AACvB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,CAACP,WAAU,KAAK;AAAA,EACzB;AAEA,SAAO,CAAC,KAAK;AACf;;;AClGO,IAAMQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeT,SAAU,MAAM,MAAM,SAAS,SAAS;AACtC,QAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;AAC/D,gBAAU;AACV,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,IAAAC,cAAa,MAAM,MAAM,UAAU,OAAO;AAM1C,aAAS,SAAS,MAAM,SAAS;AAC/B,YAAM,SAAS,QAAQ,QAAQ,SAAS,CAAC;AACzC,aAAO;AAAA,QACL;AAAA,QACA,SAAS,OAAO,SAAS,QAAQ,IAAI,IAAI;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;;;AC/JJ,IAAM,MAAM,CAAC,EAAE;AAaR,SAAS,YAAY,MAAM;AAEhC,QAAM,QAAQ,uBAAO,OAAO,IAAI;AAEhC,MAAI,CAAC,QAAQ,CAAC,KAAK,MAAM;AACvB,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AAEA,EAAAC,OAAM,MAAM,cAAc,CAACC,gBAAe;AACxC,UAAM,KAAK,MAAMA,YAAW,UAAU;AACtC,QAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,GAAG;AAC9B,YAAM,EAAE,IAAIA;AAAA,IACd;AAAA,EACF,CAAC;AAED,SAAO;AAGP,WAAS,WAAW,YAAY;AAC9B,UAAM,KAAK,MAAM,UAAU;AAE3B,WAAO,MAAM,IAAI,KAAK,OAAO,EAAE,IAAI,MAAM,EAAE,IAAI;AAAA,EACjD;AACF;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,OAAO,SAAS,EAAE,EAAE,YAAY;AACzC;;;ACwGA,IAAMC,OAAM,CAAC,EAAE;AAYR,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,YAAY,SAAS,sBAAsB;AAEjD,QAAM,eAAe,CAAC;AAQtB,QAAM,YAAY;AAElB,QAAM,gBACJ,SAAS,kBAAkB,UAAa,SAAS,kBAAkB,OAC/D,kBACA,SAAS;AAEf,QAAM,gBAAgB,SAAS,iBAAiB;AAEhD,QAAM,uBAAuB,SAAS,wBAAwB;AAE9D,QAAM,0BAA0B,SAAS,2BAA2B;AAAA,IAClE,WAAW,CAAC,SAAS;AAAA,EACvB;AAEA,QAAM,oBAAoB,SAAS,qBAAqB;AAExD,QAAM,iBAAiB,SAAS;AAEhC,QAAM,cAAc,SAAS;AAE7B,QAAM,WAAW,EAAC,GAAG,UAAU,GAAG,SAAS,SAAQ;AAInD,QAAM,aAAa,YAAY,IAAI;AACnC,QAAM,eAAe;AAErB,QAAM,gBAAgB,CAAC;AAEvB,QAAM,iBAAiB,CAAC;AAExB,QAAM,QAAQ;AACd,QAAM,YAAY;AAClB,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,OAAO;AAEb,QAAM,UAAU;AAEhB,QAAM,MAAM,sBAAsB,CAAC,eAAe;AAChD,UAAM,KAAK,OAAO,WAAW,UAAU,EAAE,YAAY;AAIrD,QAAI,CAACA,KAAI,KAAK,cAAc,EAAE,GAAG;AAC/B,mBAAa,EAAE,IAAI;AAAA,IACrB;AAAA,EACF,CAAC;AAGD,SAAO;AAWP,WAAS,QAAQ,MAAM,OAAO;AAE5B,QAAI,QAAQ,UAAU,QAAQ,KAAK,MAAM;AAEvC,YAAM,OAAO,KAAK;AAElB,UAAI,KAAK,OAAO;AACd,YAAI,MAAM,SAAS,WAAW;AAC5B,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,YAAY,CAAC;AAAA,YACb,UAAU,CAAC;AAAA,UACb;AAAA,QACF;AAEA,cAAM,UAAU,KAAK;AAAA,MACvB;AAEA,UAAI,MAAM,SAAS,aAAa,KAAK,aAAa;AAChD,cAAM,aAAa,EAAC,GAAG,MAAM,YAAY,GAAG,KAAK,YAAW;AAAA,MAC9D;AAEA,UAAI,cAAc,SAAS,MAAM,YAAY,KAAK,WAAW;AAC3D,cAAM,WAAW,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,MAAM;AACR,YAAM,MAAM,UAAU,OAAO,OAAO,EAAC,UAAU,KAAI;AAEnD,UAAI,CAAC,UAAU,GAAG,GAAG;AAEnB,cAAM,WAAW,EAAC,OAAO,WAAW,GAAG,GAAG,KAAK,SAAS,GAAG,EAAC;AAAA,MAC9D;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAUA,WAAS,MAAM,MAAM,SAAS,OAAO,UAAU;AAC7C,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAW;AACX,cAAQ,CAAC;AAAA,IACX;AAGA,WAAO,QAAQ,MAAM;AAAA,MACnB,MAAM;AAAA,MACN;AAAA,MACA,YAAY,SAAS,CAAC;AAAA,MACtB,UAAU,YAAY,CAAC;AAAA,IACzB,CAAC;AAAA,EACH;AAaA,WAAS,SAAS,MAAM,QAAQ;AAE9B,WAAO,IAAI,OAAO,MAAM,MAAM;AAAA,EAChC;AAUA,WAAS,SAAS,QAAQ;AAExB,WAAO,IAAI,OAAO,MAAM;AAAA,EAC1B;AACF;AAYA,SAAS,MAAM,MAAM,IAAI;AACvB,MAAI,KAAK,SAAU,IAAG,WAAW,SAAS,IAAI;AAChD;AAcA,SAAS,UAAU,MAAM,IAAI;AAE3B,MAAI,SAAS;AAGb,MAAI,QAAQ,KAAK,MAAM;AACrB,UAAM,QAAQ,KAAK,KAAK;AACxB,UAAM,YAAY,KAAK,KAAK;AAC5B,UAAM,cAAc,KAAK,KAAK;AAE9B,QAAI,OAAO,UAAU,UAAU;AAG7B,UAAI,OAAO,SAAS,WAAW;AAC7B,eAAO,UAAU;AAAA,MACnB,OAKK;AACH,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY,CAAC;AAAA,UACb,UAAU,CAAC;AAAA,QACb;AAAA,MAWF;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,aAAa,aAAa;AAC5C,aAAO,aAAa,EAAC,GAAG,OAAO,YAAY,GAAG,YAAW;AAAA,IAC3D;AAEA,QACE,cAAc,UACd,OAAO,YACP,cAAc,QACd,cAAc,QACd;AAEA,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAeO,SAAS,IAAI,OAAO,MAAM,QAAQ;AACvC,QAAM,OAAO,QAAQ,KAAK;AAG1B,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,yBAAyB,OAAO,GAAG;AAAA,EACrD;AAEA,MAAIA,KAAI,KAAK,MAAM,UAAU,IAAI,GAAG;AAClC,WAAO,MAAM,SAAS,IAAI,EAAE,OAAO,MAAM,MAAM;AAAA,EACjD;AAEA,MAAI,MAAM,eAAe,MAAM,YAAY,SAAS,IAAI,GAAG;AAGzD,WAAO,cAAc,OAAO,EAAC,GAAG,MAAM,UAAU,IAAI,OAAO,IAAI,EAAC,IAAI;AAAA,EACtE;AAEA,MAAI,MAAM,gBAAgB;AACxB,WAAO,MAAM,eAAe,OAAO,MAAM,MAAM;AAAA,EACjD;AAEA,SAAO,sBAAsB,OAAO,IAAI;AAC1C;AAaO,SAAS,IAAI,OAAO,QAAQ;AAEjC,QAAMC,UAAS,CAAC;AAEhB,MAAI,cAAc,QAAQ;AACxB,UAAM,QAAQ,OAAO;AACrB,QAAI,QAAQ;AACZ,WAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,YAAM,SAAS,IAAI,OAAO,MAAM,KAAK,GAAG,MAAM;AAG9C,UAAI,QAAQ;AACV,YAAI,SAAS,MAAM,QAAQ,CAAC,EAAE,SAAS,SAAS;AAC9C,cAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,QAAQ;AACpD,mBAAO,QAAQ,OAAO,MAAM,QAAQ,QAAQ,EAAE;AAAA,UAChD;AAEA,cAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,WAAW;AACvD,kBAAM,OAAO,OAAO,SAAS,CAAC;AAE9B,gBAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,mBAAK,QAAQ,KAAK,MAAM,QAAQ,QAAQ,EAAE;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAEA,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,UAAAA,QAAO,KAAK,GAAG,MAAM;AAAA,QACvB,OAAO;AACL,UAAAA,QAAO,KAAK,MAAM;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAOA;AACT;AAYA,SAAS,sBAAsB,OAAO,MAAM;AAC1C,QAAM,OAAO,KAAK,QAAQ,CAAC;AAE3B,QAAM,SACJ,WAAW,QACX,EAAED,KAAI,KAAK,MAAM,aAAa,KAAKA,KAAI,KAAK,MAAM,WAAW,KACzD,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAK,IAChC;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,IAAI,OAAO,IAAI;AAAA,EAC3B;AAEN,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAcO,SAAS,KAAK,OAAO,OAAO;AAEjC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,MAAI,OAAO;AACT,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,QAAI,MAAO,QAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAClD,WAAO,KAAK,MAAM,KAAK,CAAC;AAAA,EAC1B;AAEA,MAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO;AACT;;;AC/iBO,SAAS,OAAO,OAAO;AAE5B,QAAM,YAAY,CAAC;AACnB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,cAAc,QAAQ;AAC3C,UAAM,MAAM,MAAM,aAAa,MAAM,cAAc,KAAK,CAAC;AAEzD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AAEA,UAAM,UAAU,MAAM,IAAI,GAAG;AAC7B,UAAM,KAAK,OAAO,IAAI,UAAU,EAAE,YAAY;AAC9C,UAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAI,iBAAiB;AAErB,UAAM,iBAAiB,CAAC;AAExB,WAAO,EAAE,kBAAkB,MAAM,eAAe,EAAE,GAAG;AAEnD,YAAM,gBAAgB;AAAA,QACpB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,UACV,MACE,MACA,MAAM,gBACN,WACA,UACC,iBAAiB,IAAI,MAAM,iBAAiB;AAAA,UAC/C,qBAAqB;AAAA,UACrB,WAAW,CAAC,uBAAuB;AAAA,UACnC,WAAW,MAAM;AAAA,QACnB;AAAA,QACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MACvC;AAEA,UAAI,iBAAiB,GAAG;AACtB,sBAAc,SAAS,KAAK;AAAA,UAC1B,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,cAAc,EAAC,CAAC;AAAA,QAC1D,CAAC;AAAA,MACH;AAEA,UAAI,eAAe,SAAS,GAAG;AAC7B,uBAAe,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAChD;AAEA,qBAAe,KAAK,aAAa;AAAA,IACnC;AAEA,UAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAEvC,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,YAAM,WAAW,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACvD,UAAI,YAAY,SAAS,SAAS,QAAQ;AACxC,iBAAS,SAAS;AAAA,MACpB,OAAO;AACL,aAAK,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAC/C;AAEA,WAAK,SAAS,KAAK,GAAG,cAAc;AAAA,IACtC,OAAO;AACL,cAAQ,KAAK,GAAG,cAAc;AAAA,IAChC;AAGA,UAAME,YAAW;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,IAAI,MAAM,gBAAgB,QAAQ,OAAM;AAAA,MACrD,UAAU,MAAM,KAAK,SAAS,IAAI;AAAA,IACpC;AAEA,UAAM,MAAM,KAAKA,SAAQ;AAEzB,cAAU,KAAKA,SAAQ;AAAA,EACzB;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,EAAC,eAAe,MAAM,WAAW,CAAC,WAAW,EAAC;AAAA,IAC1D,UAAU;AAAA,MACR;AAAA,QACE,MAAM;AAAA,QACN,SAAS,MAAM;AAAA,QACf,YAAY;AAAA;AAAA,UAEV,GAAG,KAAK,MAAM,KAAK,UAAU,MAAM,uBAAuB,CAAC;AAAA,UAC3D,IAAI;AAAA,QACN;AAAA,QACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,MAAM,cAAa,CAAC;AAAA,MACvD;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,CAAC;AAAA,QACb,UAAU,MAAM,KAAK,WAAW,IAAI;AAAA,MACtC;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,IAC5B;AAAA,EACF;AACF;;;AC/BO,SAAS,OAAO,MAAM,SAAS;AACpC,QAAM,QAAQ,YAAY,MAAM,OAAO;AACvC,QAAM,OAAO,MAAM,IAAI,MAAM,IAAI;AACjC,QAAM,OAAO,OAAO,KAAK;AAEzB,MAAI,MAAM;AAIR,SAAK,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,GAAG,IAAI;AAAA,EACtD;AAGA,SAAO,MAAM,QAAQ,IAAI,IAAI,EAAC,MAAM,QAAQ,UAAU,KAAI,IAAI;AAChE;;;AC3EA,IAAM;AAAA;AAAA,EAGF,SAAU,aAAa,SAAS;AAC9B,WAAO,eAAe,SAAS,cAC3B,OAAO,aAAa,OAAO,IAC3B,OAAO,eAAe,OAAO;AAAA,EACnC;AAAA;AAGJ,IAAO,cAAQ;AAQf,SAAS,OAAO,aAAa,SAAS;AACpC,SAAO,CAAC,MAAM,MAAM,SAAS;AAC3B,gBAAY,IAAI,OAAO,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU;AACtD,WAAK,KAAK;AAAA,IACZ,CAAC;AAAA,EACH;AACF;AAQA,SAAS,OAAO,SAAS;AAEvB,SAAO,CAAC,SAAS,OAAO,MAAM,OAAO;AACvC;", "names": ["code", "code", "link", "text", "paragraph", "point", "result", "index", "all", "is", "visit", "color", "CONTINUE", "EXIT", "SKIP", "visitParents", "is", "visit", "color", "toResult", "visit", "visitParents", "visit", "definition", "own", "values", "listItem"]}
import {
  indent
} from "./chunk-ASSDCHCP.js";
import {
  CodeMirror
} from "./chunk-VKDUPG67.js";
import "./chunk-HV37R6KS.js";
import {
  LexRules,
  ParseRules,
  isIgnored,
  onlineParser
} from "./chunk-FBZEYJ3E.js";
import "./chunk-I7RZVVY3.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@graphiql/react/dist/mode.es.js
var graphqlModeFactory = (config) => {
  const parser = onlineParser({
    eatWhitespace: (stream) => stream.eatWhile(isIgnored),
    lexRules: LexRules,
    parseRules: ParseRules,
    editorConfig: { tabSize: config.tabSize }
  });
  return {
    config,
    startState: parser.startState,
    token: parser.token,
    indent,
    electricInput: /^\s*[})\]]/,
    fold: "brace",
    lineComment: "#",
    closeBrackets: {
      pairs: '()[]{}""',
      explode: "()[]{}"
    }
  };
};
CodeMirror.defineMode("graphql", graphqlModeFactory);
//# sourceMappingURL=mode.es-PZYQ6NOC.js.map

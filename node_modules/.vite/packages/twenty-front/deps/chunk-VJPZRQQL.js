import {
  $,
  Fragment,
  P,
  c,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  mergeProps,
  normalizeClass,
  openBlock,
  renderList,
  toDisplayString,
  unref,
  vModelText,
  withCtx,
  withDirectives
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/api-client/dist/components/Server/ServerVariablesSelect.vue.js
var _ = {
  key: 0,
  class: "sr-only"
};
var x = defineComponent({
  __name: "ServerVariablesSelect",
  props: {
    enum: {},
    value: {},
    controls: {}
  },
  emits: ["change"],
  setup(u, { emit: m }) {
    const o = u, d = m, t = computed(
      () => o.enum.map((e) => ({ id: e, label: e }))
    ), l = computed({
      get: () => t.value.find((e) => e.id === o.value),
      set: (e) => d("change", (e == null ? void 0 : e.id) ?? "")
    });
    return (e, n) => (openBlock(), createBlock(unref(P), {
      modelValue: l.value,
      "onUpdate:modelValue": n[0] || (n[0] = (p) => l.value = p),
      options: t.value
    }, {
      default: withCtx(() => [
        createVNode(unref($), {
          "aria-controls": e.controls,
          class: "h-8 w-full p-0 py-1.5 font-normal",
          variant: "ghost"
        }, {
          default: withCtx(() => [
            createBaseVNode("span", {
              class: normalizeClass({ "text-c-1": e.value })
            }, [
              e.value ? (openBlock(), createElementBlock("span", _, " Selected: ")) : createCommentVNode("", true),
              createTextVNode(" " + toDisplayString(e.value || "Select value"), 1)
            ], 2),
            createVNode(unref(c), {
              class: "ml-1",
              icon: "ChevronDown",
              size: "sm"
            })
          ]),
          _: 1
        }, 8, ["aria-controls"])
      ]),
      _: 1
    }, 8, ["modelValue", "options"]));
  }
});

// node_modules/@scalar/api-client/dist/components/Server/ServerVariablesTextbox.vue.js
var g = defineComponent({
  __name: "ServerVariablesTextbox",
  props: {
    value: {},
    controls: {}
  },
  emits: ["change"],
  setup(l, { emit: r }) {
    const a = l, n = r, t = computed({
      get: () => a.value,
      set: (e) => n("change", e)
    });
    return (e, o) => withDirectives((openBlock(), createElementBlock("input", mergeProps({
      "onUpdate:modelValue": o[0] || (o[0] = (s) => t.value = s)
    }, e.controls ? { ...e.$attrs, "aria-controls": e.controls } : {}, {
      autocomplete: "off",
      class: "text-c-1 w-full border-transparent px-1.5 -outline-offset-1 group-last/label:rounded-br-lg",
      placeholder: "value",
      spellcheck: "false",
      type: "text"
    }), null, 16)), [
      [vModelText, t.value]
    ]);
  }
});

// node_modules/@scalar/api-client/dist/components/Server/ServerVariablesForm.vue.js
var $2 = { class: "mr-1.5 flex items-center py-1.5 pl-3 after:content-[':'] group-has-[input]/label:mr-0" };
var N = defineComponent({
  __name: "ServerVariablesForm",
  props: {
    variables: {},
    values: {},
    controls: {}
  },
  emits: ["update:variable"],
  setup(f, { emit: g2 }) {
    const n = f, d = g2;
    function s(e, r) {
      d("update:variable", e, r);
    }
    const i = (e) => {
      var r, l, a;
      return (((r = n.values) == null ? void 0 : r[e]) ?? ((a = (l = n.variables) == null ? void 0 : l[e]) == null ? void 0 : a.default) ?? "").toString();
    };
    return (e, r) => e.variables && Object.keys(e.variables ?? {}).length ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(Object.keys(e.variables), (l) => {
      var a, u, c2, p, b;
      return openBlock(), createElementBlock("label", {
        key: l,
        class: "group/label flex w-full"
      }, [
        createBaseVNode("span", $2, toDisplayString(l), 1),
        (c2 = (u = (a = e.variables) == null ? void 0 : a[l]) == null ? void 0 : u.enum) != null && c2.length ? (openBlock(), createBlock(x, {
          key: 0,
          controls: e.controls,
          enum: ((b = (p = e.variables[l]) == null ? void 0 : p.enum) == null ? void 0 : b.map((t) => `${t}`)) ?? [],
          label: l,
          value: i(l),
          onChange: (t) => s(l, t)
        }, null, 8, ["controls", "enum", "label", "value", "onChange"])) : (openBlock(), createBlock(g, {
          key: 1,
          controls: e.controls,
          value: i(l),
          onChange: (t) => s(l, t)
        }, null, 8, ["controls", "value", "onChange"]))
      ]);
    }), 128)) : createCommentVNode("", true);
  }
});

export {
  N
};
//# sourceMappingURL=chunk-VJPZRQQL.js.map

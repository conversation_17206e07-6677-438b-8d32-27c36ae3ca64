import {
  C as C2
} from "./chunk-3N7B2TRM.js";
import {
  g
} from "./chunk-Q7FPBQNE.js";
import {
  i as i3
} from "./chunk-HFNY7CXG.js";
import {
  i as i2
} from "./chunk-HUQ3BRN5.js";
import "./chunk-WPCBYP5E.js";
import {
  $ as $2
} from "./chunk-2F5VYXMA.js";
import {
  d
} from "./chunk-L7BRX5IX.js";
import {
  a,
  w as w2
} from "./chunk-PEQF4OOI.js";
import {
  u as u3
} from "./chunk-HHBXKWIH.js";
import {
  m as m4
} from "./chunk-EKB3HY5M.js";
import {
  $ as $3,
  C as C3,
  D,
  e,
  require_mime_type
} from "./chunk-D7JDSFLT.js";
import "./chunk-ERAQ5RRJ.js";
import {
  m as m3
} from "./chunk-SGBOHCGH.js";
import {
  s
} from "./chunk-FXOF7VZK.js";
import {
  RouterLink,
  RouterView,
  useRouter
} from "./chunk-EFYDGGJO.js";
import {
  F,
  Q,
  a as a2,
  canMethodHaveBody,
  collectionSchema,
  createExampleFromRequest,
  createHash,
  f as f2,
  fetchSpecFromUrl,
  getNestedValue,
  isDefined,
  isHttpMethod,
  je,
  mergeUrls,
  parseSchema,
  r,
  requestSchema,
  s as s2,
  schemaModel,
  securitySchemeSchema,
  serverSchema,
  shouldIgnoreEntity,
  shouldUseProxy,
  t,
  tagSchema,
  w
} from "./chunk-K3FRCNXE.js";
import {
  $,
  B2 as B,
  C,
  E2 as E,
  Fragment,
  I,
  T2 as T,
  c,
  c3 as c2,
  c4 as c3,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  i3 as i,
  isRef,
  k,
  m3 as m,
  m4 as m2,
  nextTick,
  normalizeClass,
  onBeforeUnmount,
  onMounted,
  openBlock,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  toDisplayString,
  u,
  u2,
  unref,
  useCssVars,
  useId,
  useTimeoutPoll,
  vShow,
  watch,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers,
  x,
  x2
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import {
  Fuse
} from "./chunk-O3EO7ESF.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-WESWXL2S.js";
import "./chunk-YAGSMJYR.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/@scalar/object-utils/dist/parse/json.js
var safeJSON = {
  parse(v3) {
    try {
      return {
        error: false,
        data: JSON.parse(v3)
      };
    } catch (e2) {
      return {
        error: true,
        message: e2.message ? String(e2.message) : "Unknown Error"
      };
    }
  }
};

// node_modules/@scalar/api-client/dist/components/Sidebar/SidebarToggle.vue.js
var u4 = ["aria-pressed"];
var m5 = { class: "sr-only" };
var g2 = {
  class: "size-4",
  fill: "none",
  viewBox: "0 0 24 24",
  xmlns: "http://www.w3.org/2000/svg"
};
var h = { "clip-path": "url(#mask)" };
var k2 = defineComponent({
  __name: "SidebarToggle",
  setup(b) {
    const { isSidebarOpen: s4, toggleSidebarOpen: r3 } = m3();
    return (f4, t3) => (openBlock(), createElementBlock("button", {
      "aria-pressed": unref(s4),
      class: "scalar-sidebar-toggle text-c-3 hover:bg-b-2 active:text-c-1 rounded-lg p-2",
      type: "button",
      onClick: t3[0] || (t3[0] = //@ts-ignore
      (...i5) => unref(r3) && unref(r3)(...i5))
    }, [
      createBaseVNode("span", m5, toDisplayString(unref(s4) ? "Hide" : "Show") + " sidebar", 1),
      (openBlock(), createElementBlock("svg", g2, [
        t3[1] || (t3[1] = createBaseVNode("defs", null, [
          createBaseVNode("clipPath", { id: "mask" }, [
            createBaseVNode("path", {
              "clip-rule": "evenodd",
              d: "M9 3.2H4c-1.7 0-3 1.3-3 3v11.5c0 1.7 1.3 3 3 3h5V3.2z"
            })
          ])
        ], -1)),
        createBaseVNode("g", h, [
          createBaseVNode("path", {
            class: normalizeClass(["transition-transform duration-300", unref(s4) ? "translate-x-0" : "-translate-x-1/2"]),
            d: "M1 3.2h8v17.5H1z",
            fill: "currentColor"
          }, null, 2)
        ]),
        t3[2] || (t3[2] = createBaseVNode("path", {
          d: "M20 20.8H4c-1.7 0-3-1.3-3-3V6.2c0-1.7 1.3-3 3-3h16c1.7 0 3 1.3 3 3v11.5c0 1.7-1.3 3-3 3zM9 3.2v17.5",
          stroke: "currentColor",
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-width": "2"
        }, null, -1))
      ]))
    ], 8, u4));
  }
});

// node_modules/@scalar/api-client/dist/libs/validate-parameters.js
var i4 = (a4) => {
  const e2 = /* @__PURE__ */ new Set();
  return a4 && ["path", "query", "headers", "cookies"].some((n2) => {
    var s4, t3;
    return (t3 = (s4 = a4.parameters) == null ? void 0 : s4[n2]) == null ? void 0 : t3.some((r3) => {
      r3.required && r3.value === "" && e2.add(r3.key);
    });
  }), e2;
};

// node_modules/@scalar/api-client/node_modules/microdiff/dist/index.js
var richTypes = { Date: true, RegExp: true, String: true, Number: true };
function diff(obj, newObj, options = { cyclesFix: true }, _stack = []) {
  var _a, _b;
  let diffs = [];
  const isObjArray = Array.isArray(obj);
  for (const key in obj) {
    const objKey = obj[key];
    const path = isObjArray ? +key : key;
    if (!(key in newObj)) {
      diffs.push({
        type: "REMOVE",
        path: [path],
        oldValue: obj[key]
      });
      continue;
    }
    const newObjKey = newObj[key];
    const areCompatibleObjects = typeof objKey === "object" && typeof newObjKey === "object" && Array.isArray(objKey) === Array.isArray(newObjKey);
    if (objKey && newObjKey && areCompatibleObjects && !richTypes[(_b = (_a = Object.getPrototypeOf(objKey)) == null ? void 0 : _a.constructor) == null ? void 0 : _b.name] && (!options.cyclesFix || !_stack.includes(objKey))) {
      diffs.push.apply(diffs, diff(objKey, newObjKey, options, options.cyclesFix ? _stack.concat([objKey]) : []).map((difference) => {
        difference.path.unshift(path);
        return difference;
      }));
    } else if (objKey !== newObjKey && // treat NaN values as equivalent
    !(Number.isNaN(objKey) && Number.isNaN(newObjKey)) && !(areCompatibleObjects && (isNaN(objKey) ? objKey + "" === newObjKey + "" : +objKey === +newObjKey))) {
      diffs.push({
        path: [path],
        type: "CHANGE",
        value: newObjKey,
        oldValue: objKey
      });
    }
  }
  const isNewObjArray = Array.isArray(newObj);
  for (const key in newObj) {
    if (!(key in obj)) {
      diffs.push({
        type: "CREATE",
        path: [isNewObjArray ? +key : key],
        value: newObj[key]
      });
    }
  }
  return diffs;
}

// node_modules/@scalar/api-client/node_modules/zod/lib/index.mjs
var util;
(function(util2) {
  util2.assertEqual = (val) => val;
  function assertIs(_arg) {
  }
  util2.assertIs = assertIs;
  function assertNever(_x) {
    throw new Error();
  }
  util2.assertNever = assertNever;
  util2.arrayToEnum = (items) => {
    const obj = {};
    for (const item of items) {
      obj[item] = item;
    }
    return obj;
  };
  util2.getValidEnumValues = (obj) => {
    const validKeys = util2.objectKeys(obj).filter((k6) => typeof obj[obj[k6]] !== "number");
    const filtered = {};
    for (const k6 of validKeys) {
      filtered[k6] = obj[k6];
    }
    return util2.objectValues(filtered);
  };
  util2.objectValues = (obj) => {
    return util2.objectKeys(obj).map(function(e2) {
      return obj[e2];
    });
  };
  util2.objectKeys = typeof Object.keys === "function" ? (obj) => Object.keys(obj) : (object) => {
    const keys = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        keys.push(key);
      }
    }
    return keys;
  };
  util2.find = (arr, checker) => {
    for (const item of arr) {
      if (checker(item))
        return item;
    }
    return void 0;
  };
  util2.isInteger = typeof Number.isInteger === "function" ? (val) => Number.isInteger(val) : (val) => typeof val === "number" && isFinite(val) && Math.floor(val) === val;
  function joinValues(array, separator = " | ") {
    return array.map((val) => typeof val === "string" ? `'${val}'` : val).join(separator);
  }
  util2.joinValues = joinValues;
  util2.jsonStringifyReplacer = (_4, value) => {
    if (typeof value === "bigint") {
      return value.toString();
    }
    return value;
  };
})(util || (util = {}));
var objectUtil;
(function(objectUtil2) {
  objectUtil2.mergeShapes = (first, second) => {
    return {
      ...first,
      ...second
      // second overwrites first
    };
  };
})(objectUtil || (objectUtil = {}));
var ZodParsedType = util.arrayToEnum([
  "string",
  "nan",
  "number",
  "integer",
  "float",
  "boolean",
  "date",
  "bigint",
  "symbol",
  "function",
  "undefined",
  "null",
  "array",
  "object",
  "unknown",
  "promise",
  "void",
  "never",
  "map",
  "set"
]);
var getParsedType = (data) => {
  const t3 = typeof data;
  switch (t3) {
    case "undefined":
      return ZodParsedType.undefined;
    case "string":
      return ZodParsedType.string;
    case "number":
      return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;
    case "boolean":
      return ZodParsedType.boolean;
    case "function":
      return ZodParsedType.function;
    case "bigint":
      return ZodParsedType.bigint;
    case "symbol":
      return ZodParsedType.symbol;
    case "object":
      if (Array.isArray(data)) {
        return ZodParsedType.array;
      }
      if (data === null) {
        return ZodParsedType.null;
      }
      if (data.then && typeof data.then === "function" && data.catch && typeof data.catch === "function") {
        return ZodParsedType.promise;
      }
      if (typeof Map !== "undefined" && data instanceof Map) {
        return ZodParsedType.map;
      }
      if (typeof Set !== "undefined" && data instanceof Set) {
        return ZodParsedType.set;
      }
      if (typeof Date !== "undefined" && data instanceof Date) {
        return ZodParsedType.date;
      }
      return ZodParsedType.object;
    default:
      return ZodParsedType.unknown;
  }
};
var ZodIssueCode = util.arrayToEnum([
  "invalid_type",
  "invalid_literal",
  "custom",
  "invalid_union",
  "invalid_union_discriminator",
  "invalid_enum_value",
  "unrecognized_keys",
  "invalid_arguments",
  "invalid_return_type",
  "invalid_date",
  "invalid_string",
  "too_small",
  "too_big",
  "invalid_intersection_types",
  "not_multiple_of",
  "not_finite"
]);
var quotelessJson = (obj) => {
  const json = JSON.stringify(obj, null, 2);
  return json.replace(/"([^"]+)":/g, "$1:");
};
var ZodError = class _ZodError extends Error {
  get errors() {
    return this.issues;
  }
  constructor(issues) {
    super();
    this.issues = [];
    this.addIssue = (sub) => {
      this.issues = [...this.issues, sub];
    };
    this.addIssues = (subs = []) => {
      this.issues = [...this.issues, ...subs];
    };
    const actualProto = new.target.prototype;
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(this, actualProto);
    } else {
      this.__proto__ = actualProto;
    }
    this.name = "ZodError";
    this.issues = issues;
  }
  format(_mapper) {
    const mapper = _mapper || function(issue) {
      return issue.message;
    };
    const fieldErrors = { _errors: [] };
    const processError = (error) => {
      for (const issue of error.issues) {
        if (issue.code === "invalid_union") {
          issue.unionErrors.map(processError);
        } else if (issue.code === "invalid_return_type") {
          processError(issue.returnTypeError);
        } else if (issue.code === "invalid_arguments") {
          processError(issue.argumentsError);
        } else if (issue.path.length === 0) {
          fieldErrors._errors.push(mapper(issue));
        } else {
          let curr = fieldErrors;
          let i5 = 0;
          while (i5 < issue.path.length) {
            const el = issue.path[i5];
            const terminal = i5 === issue.path.length - 1;
            if (!terminal) {
              curr[el] = curr[el] || { _errors: [] };
            } else {
              curr[el] = curr[el] || { _errors: [] };
              curr[el]._errors.push(mapper(issue));
            }
            curr = curr[el];
            i5++;
          }
        }
      }
    };
    processError(this);
    return fieldErrors;
  }
  static assert(value) {
    if (!(value instanceof _ZodError)) {
      throw new Error(`Not a ZodError: ${value}`);
    }
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return this.issues.length === 0;
  }
  flatten(mapper = (issue) => issue.message) {
    const fieldErrors = {};
    const formErrors = [];
    for (const sub of this.issues) {
      if (sub.path.length > 0) {
        fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];
        fieldErrors[sub.path[0]].push(mapper(sub));
      } else {
        formErrors.push(mapper(sub));
      }
    }
    return { formErrors, fieldErrors };
  }
  get formErrors() {
    return this.flatten();
  }
};
ZodError.create = (issues) => {
  const error = new ZodError(issues);
  return error;
};
var errorMap = (issue, _ctx) => {
  let message;
  switch (issue.code) {
    case ZodIssueCode.invalid_type:
      if (issue.received === ZodParsedType.undefined) {
        message = "Required";
      } else {
        message = `Expected ${issue.expected}, received ${issue.received}`;
      }
      break;
    case ZodIssueCode.invalid_literal:
      message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;
      break;
    case ZodIssueCode.unrecognized_keys:
      message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, ", ")}`;
      break;
    case ZodIssueCode.invalid_union:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_union_discriminator:
      message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;
      break;
    case ZodIssueCode.invalid_enum_value:
      message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;
      break;
    case ZodIssueCode.invalid_arguments:
      message = `Invalid function arguments`;
      break;
    case ZodIssueCode.invalid_return_type:
      message = `Invalid function return type`;
      break;
    case ZodIssueCode.invalid_date:
      message = `Invalid date`;
      break;
    case ZodIssueCode.invalid_string:
      if (typeof issue.validation === "object") {
        if ("includes" in issue.validation) {
          message = `Invalid input: must include "${issue.validation.includes}"`;
          if (typeof issue.validation.position === "number") {
            message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;
          }
        } else if ("startsWith" in issue.validation) {
          message = `Invalid input: must start with "${issue.validation.startsWith}"`;
        } else if ("endsWith" in issue.validation) {
          message = `Invalid input: must end with "${issue.validation.endsWith}"`;
        } else {
          util.assertNever(issue.validation);
        }
      } else if (issue.validation !== "regex") {
        message = `Invalid ${issue.validation}`;
      } else {
        message = "Invalid";
      }
      break;
    case ZodIssueCode.too_small:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? "exactly" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.too_big:
      if (issue.type === "array")
        message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;
      else if (issue.type === "string")
        message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;
      else if (issue.type === "number")
        message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "bigint")
        message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;
      else if (issue.type === "date")
        message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;
      else
        message = "Invalid input";
      break;
    case ZodIssueCode.custom:
      message = `Invalid input`;
      break;
    case ZodIssueCode.invalid_intersection_types:
      message = `Intersection results could not be merged`;
      break;
    case ZodIssueCode.not_multiple_of:
      message = `Number must be a multiple of ${issue.multipleOf}`;
      break;
    case ZodIssueCode.not_finite:
      message = "Number must be finite";
      break;
    default:
      message = _ctx.defaultError;
      util.assertNever(issue);
  }
  return { message };
};
var overrideErrorMap = errorMap;
function setErrorMap(map) {
  overrideErrorMap = map;
}
function getErrorMap() {
  return overrideErrorMap;
}
var makeIssue = (params) => {
  const { data, path, errorMaps, issueData } = params;
  const fullPath = [...path, ...issueData.path || []];
  const fullIssue = {
    ...issueData,
    path: fullPath
  };
  if (issueData.message !== void 0) {
    return {
      ...issueData,
      path: fullPath,
      message: issueData.message
    };
  }
  let errorMessage = "";
  const maps = errorMaps.filter((m9) => !!m9).slice().reverse();
  for (const map of maps) {
    errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;
  }
  return {
    ...issueData,
    path: fullPath,
    message: errorMessage
  };
};
var EMPTY_PATH = [];
function addIssueToContext(ctx, issueData) {
  const overrideMap = getErrorMap();
  const issue = makeIssue({
    issueData,
    data: ctx.data,
    path: ctx.path,
    errorMaps: [
      ctx.common.contextualErrorMap,
      // contextual error map is first priority
      ctx.schemaErrorMap,
      // then schema-bound map if available
      overrideMap,
      // then global override map
      overrideMap === errorMap ? void 0 : errorMap
      // then global default map
    ].filter((x3) => !!x3)
  });
  ctx.common.issues.push(issue);
}
var ParseStatus = class _ParseStatus {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    if (this.value === "valid")
      this.value = "dirty";
  }
  abort() {
    if (this.value !== "aborted")
      this.value = "aborted";
  }
  static mergeArray(status, results) {
    const arrayValue = [];
    for (const s4 of results) {
      if (s4.status === "aborted")
        return INVALID;
      if (s4.status === "dirty")
        status.dirty();
      arrayValue.push(s4.value);
    }
    return { status: status.value, value: arrayValue };
  }
  static async mergeObjectAsync(status, pairs) {
    const syncPairs = [];
    for (const pair of pairs) {
      const key = await pair.key;
      const value = await pair.value;
      syncPairs.push({
        key,
        value
      });
    }
    return _ParseStatus.mergeObjectSync(status, syncPairs);
  }
  static mergeObjectSync(status, pairs) {
    const finalObject = {};
    for (const pair of pairs) {
      const { key, value } = pair;
      if (key.status === "aborted")
        return INVALID;
      if (value.status === "aborted")
        return INVALID;
      if (key.status === "dirty")
        status.dirty();
      if (value.status === "dirty")
        status.dirty();
      if (key.value !== "__proto__" && (typeof value.value !== "undefined" || pair.alwaysSet)) {
        finalObject[key.value] = value.value;
      }
    }
    return { status: status.value, value: finalObject };
  }
};
var INVALID = Object.freeze({
  status: "aborted"
});
var DIRTY = (value) => ({ status: "dirty", value });
var OK = (value) => ({ status: "valid", value });
var isAborted = (x3) => x3.status === "aborted";
var isDirty = (x3) => x3.status === "dirty";
var isValid = (x3) => x3.status === "valid";
var isAsync = (x3) => typeof Promise !== "undefined" && x3 instanceof Promise;
function __classPrivateFieldGet(receiver, state, kind, f4) {
  if (kind === "a" && !f4) throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f4 : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f4 : kind === "a" ? f4.call(receiver) : f4 ? f4.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f4) {
  if (kind === "m") throw new TypeError("Private method is not writable");
  if (kind === "a" && !f4) throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f4 : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f4.call(receiver, value) : f4 ? f4.value = value : state.set(receiver, value), value;
}
var errorUtil;
(function(errorUtil2) {
  errorUtil2.errToObj = (message) => typeof message === "string" ? { message } : message || {};
  errorUtil2.toString = (message) => typeof message === "string" ? message : message === null || message === void 0 ? void 0 : message.message;
})(errorUtil || (errorUtil = {}));
var _ZodEnum_cache;
var _ZodNativeEnum_cache;
var ParseInputLazyPath = class {
  constructor(parent, value, path, key) {
    this._cachedPath = [];
    this.parent = parent;
    this.data = value;
    this._path = path;
    this._key = key;
  }
  get path() {
    if (!this._cachedPath.length) {
      if (this._key instanceof Array) {
        this._cachedPath.push(...this._path, ...this._key);
      } else {
        this._cachedPath.push(...this._path, this._key);
      }
    }
    return this._cachedPath;
  }
};
var handleResult = (ctx, result) => {
  if (isValid(result)) {
    return { success: true, data: result.value };
  } else {
    if (!ctx.common.issues.length) {
      throw new Error("Validation failed but no issues detected.");
    }
    return {
      success: false,
      get error() {
        if (this._error)
          return this._error;
        const error = new ZodError(ctx.common.issues);
        this._error = error;
        return this._error;
      }
    };
  }
};
function processCreateParams(params) {
  if (!params)
    return {};
  const { errorMap: errorMap2, invalid_type_error, required_error, description } = params;
  if (errorMap2 && (invalid_type_error || required_error)) {
    throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  }
  if (errorMap2)
    return { errorMap: errorMap2, description };
  const customMap = (iss, ctx) => {
    var _a, _b;
    const { message } = params;
    if (iss.code === "invalid_enum_value") {
      return { message: message !== null && message !== void 0 ? message : ctx.defaultError };
    }
    if (typeof ctx.data === "undefined") {
      return { message: (_a = message !== null && message !== void 0 ? message : required_error) !== null && _a !== void 0 ? _a : ctx.defaultError };
    }
    if (iss.code !== "invalid_type")
      return { message: ctx.defaultError };
    return { message: (_b = message !== null && message !== void 0 ? message : invalid_type_error) !== null && _b !== void 0 ? _b : ctx.defaultError };
  };
  return { errorMap: customMap, description };
}
var ZodType = class {
  get description() {
    return this._def.description;
  }
  _getType(input) {
    return getParsedType(input.data);
  }
  _getOrReturnCtx(input, ctx) {
    return ctx || {
      common: input.parent.common,
      data: input.data,
      parsedType: getParsedType(input.data),
      schemaErrorMap: this._def.errorMap,
      path: input.path,
      parent: input.parent
    };
  }
  _processInputParams(input) {
    return {
      status: new ParseStatus(),
      ctx: {
        common: input.parent.common,
        data: input.data,
        parsedType: getParsedType(input.data),
        schemaErrorMap: this._def.errorMap,
        path: input.path,
        parent: input.parent
      }
    };
  }
  _parseSync(input) {
    const result = this._parse(input);
    if (isAsync(result)) {
      throw new Error("Synchronous parse encountered promise.");
    }
    return result;
  }
  _parseAsync(input) {
    const result = this._parse(input);
    return Promise.resolve(result);
  }
  parse(data, params) {
    const result = this.safeParse(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  safeParse(data, params) {
    var _a;
    const ctx = {
      common: {
        issues: [],
        async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const result = this._parseSync({ data, path: ctx.path, parent: ctx });
    return handleResult(ctx, result);
  }
  "~validate"(data) {
    var _a, _b;
    const ctx = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    if (!this["~standard"].async) {
      try {
        const result = this._parseSync({ data, path: [], parent: ctx });
        return isValid(result) ? {
          value: result.value
        } : {
          issues: ctx.common.issues
        };
      } catch (err) {
        if ((_b = (_a = err === null || err === void 0 ? void 0 : err.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.includes("encountered")) {
          this["~standard"].async = true;
        }
        ctx.common = {
          issues: [],
          async: true
        };
      }
    }
    return this._parseAsync({ data, path: [], parent: ctx }).then((result) => isValid(result) ? {
      value: result.value
    } : {
      issues: ctx.common.issues
    });
  }
  async parseAsync(data, params) {
    const result = await this.safeParseAsync(data, params);
    if (result.success)
      return result.data;
    throw result.error;
  }
  async safeParseAsync(data, params) {
    const ctx = {
      common: {
        issues: [],
        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,
        async: true
      },
      path: (params === null || params === void 0 ? void 0 : params.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data,
      parsedType: getParsedType(data)
    };
    const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });
    const result = await (isAsync(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));
    return handleResult(ctx, result);
  }
  refine(check, message) {
    const getIssueProperties = (val) => {
      if (typeof message === "string" || typeof message === "undefined") {
        return { message };
      } else if (typeof message === "function") {
        return message(val);
      } else {
        return message;
      }
    };
    return this._refinement((val, ctx) => {
      const result = check(val);
      const setError = () => ctx.addIssue({
        code: ZodIssueCode.custom,
        ...getIssueProperties(val)
      });
      if (typeof Promise !== "undefined" && result instanceof Promise) {
        return result.then((data) => {
          if (!data) {
            setError();
            return false;
          } else {
            return true;
          }
        });
      }
      if (!result) {
        setError();
        return false;
      } else {
        return true;
      }
    });
  }
  refinement(check, refinementData) {
    return this._refinement((val, ctx) => {
      if (!check(val)) {
        ctx.addIssue(typeof refinementData === "function" ? refinementData(val, ctx) : refinementData);
        return false;
      } else {
        return true;
      }
    });
  }
  _refinement(refinement) {
    return new ZodEffects({
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "refinement", refinement }
    });
  }
  superRefine(refinement) {
    return this._refinement(refinement);
  }
  constructor(def) {
    this.spa = this.safeParseAsync;
    this._def = def;
    this.parse = this.parse.bind(this);
    this.safeParse = this.safeParse.bind(this);
    this.parseAsync = this.parseAsync.bind(this);
    this.safeParseAsync = this.safeParseAsync.bind(this);
    this.spa = this.spa.bind(this);
    this.refine = this.refine.bind(this);
    this.refinement = this.refinement.bind(this);
    this.superRefine = this.superRefine.bind(this);
    this.optional = this.optional.bind(this);
    this.nullable = this.nullable.bind(this);
    this.nullish = this.nullish.bind(this);
    this.array = this.array.bind(this);
    this.promise = this.promise.bind(this);
    this.or = this.or.bind(this);
    this.and = this.and.bind(this);
    this.transform = this.transform.bind(this);
    this.brand = this.brand.bind(this);
    this.default = this.default.bind(this);
    this.catch = this.catch.bind(this);
    this.describe = this.describe.bind(this);
    this.pipe = this.pipe.bind(this);
    this.readonly = this.readonly.bind(this);
    this.isNullable = this.isNullable.bind(this);
    this.isOptional = this.isOptional.bind(this);
    this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: (data) => this["~validate"](data)
    };
  }
  optional() {
    return ZodOptional.create(this, this._def);
  }
  nullable() {
    return ZodNullable.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return ZodArray.create(this);
  }
  promise() {
    return ZodPromise.create(this, this._def);
  }
  or(option) {
    return ZodUnion.create([this, option], this._def);
  }
  and(incoming) {
    return ZodIntersection.create(this, incoming, this._def);
  }
  transform(transform) {
    return new ZodEffects({
      ...processCreateParams(this._def),
      schema: this,
      typeName: ZodFirstPartyTypeKind.ZodEffects,
      effect: { type: "transform", transform }
    });
  }
  default(def) {
    const defaultValueFunc = typeof def === "function" ? def : () => def;
    return new ZodDefault({
      ...processCreateParams(this._def),
      innerType: this,
      defaultValue: defaultValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodDefault
    });
  }
  brand() {
    return new ZodBranded({
      typeName: ZodFirstPartyTypeKind.ZodBranded,
      type: this,
      ...processCreateParams(this._def)
    });
  }
  catch(def) {
    const catchValueFunc = typeof def === "function" ? def : () => def;
    return new ZodCatch({
      ...processCreateParams(this._def),
      innerType: this,
      catchValue: catchValueFunc,
      typeName: ZodFirstPartyTypeKind.ZodCatch
    });
  }
  describe(description) {
    const This = this.constructor;
    return new This({
      ...this._def,
      description
    });
  }
  pipe(target) {
    return ZodPipeline.create(this, target);
  }
  readonly() {
    return ZodReadonly.create(this);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
};
var cuidRegex = /^c[^\s-]{8,}$/i;
var cuid2Regex = /^[0-9a-z]+$/;
var ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;
var uuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;
var nanoidRegex = /^[a-z0-9_-]{21}$/i;
var jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
var durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/;
var emailRegex = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;
var _emojiRegex = `^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`;
var emojiRegex;
var ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;
var ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/;
var ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
var ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;
var base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
var base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;
var dateRegexSource = `((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))`;
var dateRegex = new RegExp(`^${dateRegexSource}$`);
function timeRegexSource(args) {
  let regex = `([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d`;
  if (args.precision) {
    regex = `${regex}\\.\\d{${args.precision}}`;
  } else if (args.precision == null) {
    regex = `${regex}(\\.\\d+)?`;
  }
  return regex;
}
function timeRegex(args) {
  return new RegExp(`^${timeRegexSource(args)}$`);
}
function datetimeRegex(args) {
  let regex = `${dateRegexSource}T${timeRegexSource(args)}`;
  const opts = [];
  opts.push(args.local ? `Z?` : `Z`);
  if (args.offset)
    opts.push(`([+-]\\d{2}:?\\d{2})`);
  regex = `${regex}(${opts.join("|")})`;
  return new RegExp(`^${regex}$`);
}
function isValidIP(ip, version) {
  if ((version === "v4" || !version) && ipv4Regex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6Regex.test(ip)) {
    return true;
  }
  return false;
}
function isValidJWT(jwt, alg) {
  if (!jwtRegex.test(jwt))
    return false;
  try {
    const [header] = jwt.split(".");
    const base64 = header.replace(/-/g, "+").replace(/_/g, "/").padEnd(header.length + (4 - header.length % 4) % 4, "=");
    const decoded = JSON.parse(atob(base64));
    if (typeof decoded !== "object" || decoded === null)
      return false;
    if (!decoded.typ || !decoded.alg)
      return false;
    if (alg && decoded.alg !== alg)
      return false;
    return true;
  } catch (_a) {
    return false;
  }
}
function isValidCidr(ip, version) {
  if ((version === "v4" || !version) && ipv4CidrRegex.test(ip)) {
    return true;
  }
  if ((version === "v6" || !version) && ipv6CidrRegex.test(ip)) {
    return true;
  }
  return false;
}
var ZodString = class _ZodString extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = String(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.string) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.string,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.length < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.length > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "string",
            inclusive: true,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "length") {
        const tooBig = input.data.length > check.value;
        const tooSmall = input.data.length < check.value;
        if (tooBig || tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          if (tooBig) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_big,
              maximum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          } else if (tooSmall) {
            addIssueToContext(ctx, {
              code: ZodIssueCode.too_small,
              minimum: check.value,
              type: "string",
              inclusive: true,
              exact: true,
              message: check.message
            });
          }
          status.dirty();
        }
      } else if (check.kind === "email") {
        if (!emailRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "email",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "emoji") {
        if (!emojiRegex) {
          emojiRegex = new RegExp(_emojiRegex, "u");
        }
        if (!emojiRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "emoji",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "uuid") {
        if (!uuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "uuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "nanoid") {
        if (!nanoidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "nanoid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid") {
        if (!cuidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cuid2") {
        if (!cuid2Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cuid2",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ulid") {
        if (!ulidRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ulid",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "url") {
        try {
          new URL(input.data);
        } catch (_a) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "regex") {
        check.regex.lastIndex = 0;
        const testResult = check.regex.test(input.data);
        if (!testResult) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "regex",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "trim") {
        input.data = input.data.trim();
      } else if (check.kind === "includes") {
        if (!input.data.includes(check.value, check.position)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { includes: check.value, position: check.position },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "toLowerCase") {
        input.data = input.data.toLowerCase();
      } else if (check.kind === "toUpperCase") {
        input.data = input.data.toUpperCase();
      } else if (check.kind === "startsWith") {
        if (!input.data.startsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { startsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "endsWith") {
        if (!input.data.endsWith(check.value)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: { endsWith: check.value },
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "datetime") {
        const regex = datetimeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "datetime",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "date") {
        const regex = dateRegex;
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "date",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "time") {
        const regex = timeRegex(check);
        if (!regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_string,
            validation: "time",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "duration") {
        if (!durationRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "duration",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "ip") {
        if (!isValidIP(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "ip",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "jwt") {
        if (!isValidJWT(input.data, check.alg)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "jwt",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "cidr") {
        if (!isValidCidr(input.data, check.version)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "cidr",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64") {
        if (!base64Regex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "base64url") {
        if (!base64urlRegex.test(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            validation: "base64url",
            code: ZodIssueCode.invalid_string,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _regex(regex, validation, message) {
    return this.refinement((data) => regex.test(data), {
      validation,
      code: ZodIssueCode.invalid_string,
      ...errorUtil.errToObj(message)
    });
  }
  _addCheck(check) {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  email(message) {
    return this._addCheck({ kind: "email", ...errorUtil.errToObj(message) });
  }
  url(message) {
    return this._addCheck({ kind: "url", ...errorUtil.errToObj(message) });
  }
  emoji(message) {
    return this._addCheck({ kind: "emoji", ...errorUtil.errToObj(message) });
  }
  uuid(message) {
    return this._addCheck({ kind: "uuid", ...errorUtil.errToObj(message) });
  }
  nanoid(message) {
    return this._addCheck({ kind: "nanoid", ...errorUtil.errToObj(message) });
  }
  cuid(message) {
    return this._addCheck({ kind: "cuid", ...errorUtil.errToObj(message) });
  }
  cuid2(message) {
    return this._addCheck({ kind: "cuid2", ...errorUtil.errToObj(message) });
  }
  ulid(message) {
    return this._addCheck({ kind: "ulid", ...errorUtil.errToObj(message) });
  }
  base64(message) {
    return this._addCheck({ kind: "base64", ...errorUtil.errToObj(message) });
  }
  base64url(message) {
    return this._addCheck({
      kind: "base64url",
      ...errorUtil.errToObj(message)
    });
  }
  jwt(options) {
    return this._addCheck({ kind: "jwt", ...errorUtil.errToObj(options) });
  }
  ip(options) {
    return this._addCheck({ kind: "ip", ...errorUtil.errToObj(options) });
  }
  cidr(options) {
    return this._addCheck({ kind: "cidr", ...errorUtil.errToObj(options) });
  }
  datetime(options) {
    var _a, _b;
    if (typeof options === "string") {
      return this._addCheck({
        kind: "datetime",
        precision: null,
        offset: false,
        local: false,
        message: options
      });
    }
    return this._addCheck({
      kind: "datetime",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,
      local: (_b = options === null || options === void 0 ? void 0 : options.local) !== null && _b !== void 0 ? _b : false,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  date(message) {
    return this._addCheck({ kind: "date", message });
  }
  time(options) {
    if (typeof options === "string") {
      return this._addCheck({
        kind: "time",
        precision: null,
        message: options
      });
    }
    return this._addCheck({
      kind: "time",
      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === "undefined" ? null : options === null || options === void 0 ? void 0 : options.precision,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  duration(message) {
    return this._addCheck({ kind: "duration", ...errorUtil.errToObj(message) });
  }
  regex(regex, message) {
    return this._addCheck({
      kind: "regex",
      regex,
      ...errorUtil.errToObj(message)
    });
  }
  includes(value, options) {
    return this._addCheck({
      kind: "includes",
      value,
      position: options === null || options === void 0 ? void 0 : options.position,
      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)
    });
  }
  startsWith(value, message) {
    return this._addCheck({
      kind: "startsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  endsWith(value, message) {
    return this._addCheck({
      kind: "endsWith",
      value,
      ...errorUtil.errToObj(message)
    });
  }
  min(minLength, message) {
    return this._addCheck({
      kind: "min",
      value: minLength,
      ...errorUtil.errToObj(message)
    });
  }
  max(maxLength, message) {
    return this._addCheck({
      kind: "max",
      value: maxLength,
      ...errorUtil.errToObj(message)
    });
  }
  length(len, message) {
    return this._addCheck({
      kind: "length",
      value: len,
      ...errorUtil.errToObj(message)
    });
  }
  /**
   * Equivalent to `.min(1)`
   */
  nonempty(message) {
    return this.min(1, errorUtil.errToObj(message));
  }
  trim() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "trim" }]
    });
  }
  toLowerCase() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toLowerCase" }]
    });
  }
  toUpperCase() {
    return new _ZodString({
      ...this._def,
      checks: [...this._def.checks, { kind: "toUpperCase" }]
    });
  }
  get isDatetime() {
    return !!this._def.checks.find((ch) => ch.kind === "datetime");
  }
  get isDate() {
    return !!this._def.checks.find((ch) => ch.kind === "date");
  }
  get isTime() {
    return !!this._def.checks.find((ch) => ch.kind === "time");
  }
  get isDuration() {
    return !!this._def.checks.find((ch) => ch.kind === "duration");
  }
  get isEmail() {
    return !!this._def.checks.find((ch) => ch.kind === "email");
  }
  get isURL() {
    return !!this._def.checks.find((ch) => ch.kind === "url");
  }
  get isEmoji() {
    return !!this._def.checks.find((ch) => ch.kind === "emoji");
  }
  get isUUID() {
    return !!this._def.checks.find((ch) => ch.kind === "uuid");
  }
  get isNANOID() {
    return !!this._def.checks.find((ch) => ch.kind === "nanoid");
  }
  get isCUID() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid");
  }
  get isCUID2() {
    return !!this._def.checks.find((ch) => ch.kind === "cuid2");
  }
  get isULID() {
    return !!this._def.checks.find((ch) => ch.kind === "ulid");
  }
  get isIP() {
    return !!this._def.checks.find((ch) => ch.kind === "ip");
  }
  get isCIDR() {
    return !!this._def.checks.find((ch) => ch.kind === "cidr");
  }
  get isBase64() {
    return !!this._def.checks.find((ch) => ch.kind === "base64");
  }
  get isBase64url() {
    return !!this._def.checks.find((ch) => ch.kind === "base64url");
  }
  get minLength() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxLength() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
};
ZodString.create = (params) => {
  var _a;
  return new ZodString({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodString,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
function floatSafeRemainder(val, step) {
  const valDecCount = (val.toString().split(".")[1] || "").length;
  const stepDecCount = (step.toString().split(".")[1] || "").length;
  const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;
  const valInt = parseInt(val.toFixed(decCount).replace(".", ""));
  const stepInt = parseInt(step.toFixed(decCount).replace(".", ""));
  return valInt % stepInt / Math.pow(10, decCount);
}
var ZodNumber = class _ZodNumber extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
    this.step = this.multipleOf;
  }
  _parse(input) {
    if (this._def.coerce) {
      input.data = Number(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.number) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.number,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "int") {
        if (!util.isInteger(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.invalid_type,
            expected: "integer",
            received: "float",
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            minimum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            maximum: check.value,
            type: "number",
            inclusive: check.inclusive,
            exact: false,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (floatSafeRemainder(input.data, check.value) !== 0) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "finite") {
        if (!Number.isFinite(input.data)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_finite,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new _ZodNumber({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new _ZodNumber({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  int(message) {
    return this._addCheck({
      kind: "int",
      message: errorUtil.toString(message)
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  finite(message) {
    return this._addCheck({
      kind: "finite",
      message: errorUtil.toString(message)
    });
  }
  safe(message) {
    return this._addCheck({
      kind: "min",
      inclusive: true,
      value: Number.MIN_SAFE_INTEGER,
      message: errorUtil.toString(message)
    })._addCheck({
      kind: "max",
      inclusive: true,
      value: Number.MAX_SAFE_INTEGER,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
  get isInt() {
    return !!this._def.checks.find((ch) => ch.kind === "int" || ch.kind === "multipleOf" && util.isInteger(ch.value));
  }
  get isFinite() {
    let max = null, min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "finite" || ch.kind === "int" || ch.kind === "multipleOf") {
        return true;
      } else if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      } else if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return Number.isFinite(min) && Number.isFinite(max);
  }
};
ZodNumber.create = (params) => {
  return new ZodNumber({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodNumber,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
var ZodBigInt = class _ZodBigInt extends ZodType {
  constructor() {
    super(...arguments);
    this.min = this.gte;
    this.max = this.lte;
  }
  _parse(input) {
    if (this._def.coerce) {
      try {
        input.data = BigInt(input.data);
      } catch (_a) {
        return this._getInvalidInput(input);
      }
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.bigint) {
      return this._getInvalidInput(input);
    }
    let ctx = void 0;
    const status = new ParseStatus();
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;
        if (tooSmall) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            type: "bigint",
            minimum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;
        if (tooBig) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            type: "bigint",
            maximum: check.value,
            inclusive: check.inclusive,
            message: check.message
          });
          status.dirty();
        }
      } else if (check.kind === "multipleOf") {
        if (input.data % check.value !== BigInt(0)) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.not_multiple_of,
            multipleOf: check.value,
            message: check.message
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return { status: status.value, value: input.data };
  }
  _getInvalidInput(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.bigint,
      received: ctx.parsedType
    });
    return INVALID;
  }
  gte(value, message) {
    return this.setLimit("min", value, true, errorUtil.toString(message));
  }
  gt(value, message) {
    return this.setLimit("min", value, false, errorUtil.toString(message));
  }
  lte(value, message) {
    return this.setLimit("max", value, true, errorUtil.toString(message));
  }
  lt(value, message) {
    return this.setLimit("max", value, false, errorUtil.toString(message));
  }
  setLimit(kind, value, inclusive, message) {
    return new _ZodBigInt({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind,
          value,
          inclusive,
          message: errorUtil.toString(message)
        }
      ]
    });
  }
  _addCheck(check) {
    return new _ZodBigInt({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  positive(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  negative(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: false,
      message: errorUtil.toString(message)
    });
  }
  nonpositive(message) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  nonnegative(message) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: true,
      message: errorUtil.toString(message)
    });
  }
  multipleOf(value, message) {
    return this._addCheck({
      kind: "multipleOf",
      value,
      message: errorUtil.toString(message)
    });
  }
  get minValue() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min;
  }
  get maxValue() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max;
  }
};
ZodBigInt.create = (params) => {
  var _a;
  return new ZodBigInt({
    checks: [],
    typeName: ZodFirstPartyTypeKind.ZodBigInt,
    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,
    ...processCreateParams(params)
  });
};
var ZodBoolean = class extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = Boolean(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.boolean) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.boolean,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodBoolean.create = (params) => {
  return new ZodBoolean({
    typeName: ZodFirstPartyTypeKind.ZodBoolean,
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    ...processCreateParams(params)
  });
};
var ZodDate = class _ZodDate extends ZodType {
  _parse(input) {
    if (this._def.coerce) {
      input.data = new Date(input.data);
    }
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.date) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.date,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    if (isNaN(input.data.getTime())) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_date
      });
      return INVALID;
    }
    const status = new ParseStatus();
    let ctx = void 0;
    for (const check of this._def.checks) {
      if (check.kind === "min") {
        if (input.data.getTime() < check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_small,
            message: check.message,
            inclusive: true,
            exact: false,
            minimum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else if (check.kind === "max") {
        if (input.data.getTime() > check.value) {
          ctx = this._getOrReturnCtx(input, ctx);
          addIssueToContext(ctx, {
            code: ZodIssueCode.too_big,
            message: check.message,
            inclusive: true,
            exact: false,
            maximum: check.value,
            type: "date"
          });
          status.dirty();
        }
      } else {
        util.assertNever(check);
      }
    }
    return {
      status: status.value,
      value: new Date(input.data.getTime())
    };
  }
  _addCheck(check) {
    return new _ZodDate({
      ...this._def,
      checks: [...this._def.checks, check]
    });
  }
  min(minDate, message) {
    return this._addCheck({
      kind: "min",
      value: minDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  max(maxDate, message) {
    return this._addCheck({
      kind: "max",
      value: maxDate.getTime(),
      message: errorUtil.toString(message)
    });
  }
  get minDate() {
    let min = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "min") {
        if (min === null || ch.value > min)
          min = ch.value;
      }
    }
    return min != null ? new Date(min) : null;
  }
  get maxDate() {
    let max = null;
    for (const ch of this._def.checks) {
      if (ch.kind === "max") {
        if (max === null || ch.value < max)
          max = ch.value;
      }
    }
    return max != null ? new Date(max) : null;
  }
};
ZodDate.create = (params) => {
  return new ZodDate({
    checks: [],
    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,
    typeName: ZodFirstPartyTypeKind.ZodDate,
    ...processCreateParams(params)
  });
};
var ZodSymbol = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.symbol) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.symbol,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodSymbol.create = (params) => {
  return new ZodSymbol({
    typeName: ZodFirstPartyTypeKind.ZodSymbol,
    ...processCreateParams(params)
  });
};
var ZodUndefined = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.undefined,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodUndefined.create = (params) => {
  return new ZodUndefined({
    typeName: ZodFirstPartyTypeKind.ZodUndefined,
    ...processCreateParams(params)
  });
};
var ZodNull = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.null) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.null,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodNull.create = (params) => {
  return new ZodNull({
    typeName: ZodFirstPartyTypeKind.ZodNull,
    ...processCreateParams(params)
  });
};
var ZodAny = class extends ZodType {
  constructor() {
    super(...arguments);
    this._any = true;
  }
  _parse(input) {
    return OK(input.data);
  }
};
ZodAny.create = (params) => {
  return new ZodAny({
    typeName: ZodFirstPartyTypeKind.ZodAny,
    ...processCreateParams(params)
  });
};
var ZodUnknown = class extends ZodType {
  constructor() {
    super(...arguments);
    this._unknown = true;
  }
  _parse(input) {
    return OK(input.data);
  }
};
ZodUnknown.create = (params) => {
  return new ZodUnknown({
    typeName: ZodFirstPartyTypeKind.ZodUnknown,
    ...processCreateParams(params)
  });
};
var ZodNever = class extends ZodType {
  _parse(input) {
    const ctx = this._getOrReturnCtx(input);
    addIssueToContext(ctx, {
      code: ZodIssueCode.invalid_type,
      expected: ZodParsedType.never,
      received: ctx.parsedType
    });
    return INVALID;
  }
};
ZodNever.create = (params) => {
  return new ZodNever({
    typeName: ZodFirstPartyTypeKind.ZodNever,
    ...processCreateParams(params)
  });
};
var ZodVoid = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.undefined) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.void,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return OK(input.data);
  }
};
ZodVoid.create = (params) => {
  return new ZodVoid({
    typeName: ZodFirstPartyTypeKind.ZodVoid,
    ...processCreateParams(params)
  });
};
var ZodArray = class _ZodArray extends ZodType {
  _parse(input) {
    const { ctx, status } = this._processInputParams(input);
    const def = this._def;
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (def.exactLength !== null) {
      const tooBig = ctx.data.length > def.exactLength.value;
      const tooSmall = ctx.data.length < def.exactLength.value;
      if (tooBig || tooSmall) {
        addIssueToContext(ctx, {
          code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,
          minimum: tooSmall ? def.exactLength.value : void 0,
          maximum: tooBig ? def.exactLength.value : void 0,
          type: "array",
          inclusive: true,
          exact: true,
          message: def.exactLength.message
        });
        status.dirty();
      }
    }
    if (def.minLength !== null) {
      if (ctx.data.length < def.minLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.minLength.message
        });
        status.dirty();
      }
    }
    if (def.maxLength !== null) {
      if (ctx.data.length > def.maxLength.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxLength.value,
          type: "array",
          inclusive: true,
          exact: false,
          message: def.maxLength.message
        });
        status.dirty();
      }
    }
    if (ctx.common.async) {
      return Promise.all([...ctx.data].map((item, i5) => {
        return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i5));
      })).then((result2) => {
        return ParseStatus.mergeArray(status, result2);
      });
    }
    const result = [...ctx.data].map((item, i5) => {
      return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i5));
    });
    return ParseStatus.mergeArray(status, result);
  }
  get element() {
    return this._def.type;
  }
  min(minLength, message) {
    return new _ZodArray({
      ...this._def,
      minLength: { value: minLength, message: errorUtil.toString(message) }
    });
  }
  max(maxLength, message) {
    return new _ZodArray({
      ...this._def,
      maxLength: { value: maxLength, message: errorUtil.toString(message) }
    });
  }
  length(len, message) {
    return new _ZodArray({
      ...this._def,
      exactLength: { value: len, message: errorUtil.toString(message) }
    });
  }
  nonempty(message) {
    return this.min(1, message);
  }
};
ZodArray.create = (schema, params) => {
  return new ZodArray({
    type: schema,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: ZodFirstPartyTypeKind.ZodArray,
    ...processCreateParams(params)
  });
};
function deepPartialify(schema) {
  if (schema instanceof ZodObject) {
    const newShape = {};
    for (const key in schema.shape) {
      const fieldSchema = schema.shape[key];
      newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));
    }
    return new ZodObject({
      ...schema._def,
      shape: () => newShape
    });
  } else if (schema instanceof ZodArray) {
    return new ZodArray({
      ...schema._def,
      type: deepPartialify(schema.element)
    });
  } else if (schema instanceof ZodOptional) {
    return ZodOptional.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodNullable) {
    return ZodNullable.create(deepPartialify(schema.unwrap()));
  } else if (schema instanceof ZodTuple) {
    return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));
  } else {
    return schema;
  }
}
var ZodObject = class _ZodObject extends ZodType {
  constructor() {
    super(...arguments);
    this._cached = null;
    this.nonstrict = this.passthrough;
    this.augment = this.extend;
  }
  _getCached() {
    if (this._cached !== null)
      return this._cached;
    const shape = this._def.shape();
    const keys = util.objectKeys(shape);
    return this._cached = { shape, keys };
  }
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.object) {
      const ctx2 = this._getOrReturnCtx(input);
      addIssueToContext(ctx2, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx2.parsedType
      });
      return INVALID;
    }
    const { status, ctx } = this._processInputParams(input);
    const { shape, keys: shapeKeys } = this._getCached();
    const extraKeys = [];
    if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === "strip")) {
      for (const key in ctx.data) {
        if (!shapeKeys.includes(key)) {
          extraKeys.push(key);
        }
      }
    }
    const pairs = [];
    for (const key of shapeKeys) {
      const keyValidator = shape[key];
      const value = ctx.data[key];
      pairs.push({
        key: { status: "valid", value: key },
        value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (this._def.catchall instanceof ZodNever) {
      const unknownKeys = this._def.unknownKeys;
      if (unknownKeys === "passthrough") {
        for (const key of extraKeys) {
          pairs.push({
            key: { status: "valid", value: key },
            value: { status: "valid", value: ctx.data[key] }
          });
        }
      } else if (unknownKeys === "strict") {
        if (extraKeys.length > 0) {
          addIssueToContext(ctx, {
            code: ZodIssueCode.unrecognized_keys,
            keys: extraKeys
          });
          status.dirty();
        }
      } else if (unknownKeys === "strip") ;
      else {
        throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);
      }
    } else {
      const catchall = this._def.catchall;
      for (const key of extraKeys) {
        const value = ctx.data[key];
        pairs.push({
          key: { status: "valid", value: key },
          value: catchall._parse(
            new ParseInputLazyPath(ctx, value, ctx.path, key)
            //, ctx.child(key), value, getParsedType(value)
          ),
          alwaysSet: key in ctx.data
        });
      }
    }
    if (ctx.common.async) {
      return Promise.resolve().then(async () => {
        const syncPairs = [];
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          syncPairs.push({
            key,
            value,
            alwaysSet: pair.alwaysSet
          });
        }
        return syncPairs;
      }).then((syncPairs) => {
        return ParseStatus.mergeObjectSync(status, syncPairs);
      });
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get shape() {
    return this._def.shape();
  }
  strict(message) {
    errorUtil.errToObj;
    return new _ZodObject({
      ...this._def,
      unknownKeys: "strict",
      ...message !== void 0 ? {
        errorMap: (issue, ctx) => {
          var _a, _b, _c, _d;
          const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;
          if (issue.code === "unrecognized_keys")
            return {
              message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError
            };
          return {
            message: defaultError
          };
        }
      } : {}
    });
  }
  strip() {
    return new _ZodObject({
      ...this._def,
      unknownKeys: "strip"
    });
  }
  passthrough() {
    return new _ZodObject({
      ...this._def,
      unknownKeys: "passthrough"
    });
  }
  // const AugmentFactory =
  //   <Def extends ZodObjectDef>(def: Def) =>
  //   <Augmentation extends ZodRawShape>(
  //     augmentation: Augmentation
  //   ): ZodObject<
  //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
  //     Def["unknownKeys"],
  //     Def["catchall"]
  //   > => {
  //     return new ZodObject({
  //       ...def,
  //       shape: () => ({
  //         ...def.shape(),
  //         ...augmentation,
  //       }),
  //     }) as any;
  //   };
  extend(augmentation) {
    return new _ZodObject({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...augmentation
      })
    });
  }
  /**
   * Prior to zod@1.0.12 there was a bug in the
   * inferred type of merged objects. Please
   * upgrade if you are experiencing issues.
   */
  merge(merging) {
    const merged = new _ZodObject({
      unknownKeys: merging._def.unknownKeys,
      catchall: merging._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...merging._def.shape()
      }),
      typeName: ZodFirstPartyTypeKind.ZodObject
    });
    return merged;
  }
  // merge<
  //   Incoming extends AnyZodObject,
  //   Augmentation extends Incoming["shape"],
  //   NewOutput extends {
  //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
  //       ? Augmentation[k]["_output"]
  //       : k extends keyof Output
  //       ? Output[k]
  //       : never;
  //   },
  //   NewInput extends {
  //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
  //       ? Augmentation[k]["_input"]
  //       : k extends keyof Input
  //       ? Input[k]
  //       : never;
  //   }
  // >(
  //   merging: Incoming
  // ): ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"],
  //   NewOutput,
  //   NewInput
  // > {
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  setKey(key, schema) {
    return this.augment({ [key]: schema });
  }
  // merge<Incoming extends AnyZodObject>(
  //   merging: Incoming
  // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
  // ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"]
  // > {
  //   // const mergedShape = objectUtil.mergeShapes(
  //   //   this._def.shape(),
  //   //   merging._def.shape()
  //   // );
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  catchall(index) {
    return new _ZodObject({
      ...this._def,
      catchall: index
    });
  }
  pick(mask) {
    const shape = {};
    util.objectKeys(mask).forEach((key) => {
      if (mask[key] && this.shape[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  omit(mask) {
    const shape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (!mask[key]) {
        shape[key] = this.shape[key];
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => shape
    });
  }
  /**
   * @deprecated
   */
  deepPartial() {
    return deepPartialify(this);
  }
  partial(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      const fieldSchema = this.shape[key];
      if (mask && !mask[key]) {
        newShape[key] = fieldSchema;
      } else {
        newShape[key] = fieldSchema.optional();
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  required(mask) {
    const newShape = {};
    util.objectKeys(this.shape).forEach((key) => {
      if (mask && !mask[key]) {
        newShape[key] = this.shape[key];
      } else {
        const fieldSchema = this.shape[key];
        let newField = fieldSchema;
        while (newField instanceof ZodOptional) {
          newField = newField._def.innerType;
        }
        newShape[key] = newField;
      }
    });
    return new _ZodObject({
      ...this._def,
      shape: () => newShape
    });
  }
  keyof() {
    return createZodEnum(util.objectKeys(this.shape));
  }
};
ZodObject.create = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.strictCreate = (shape, params) => {
  return new ZodObject({
    shape: () => shape,
    unknownKeys: "strict",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
ZodObject.lazycreate = (shape, params) => {
  return new ZodObject({
    shape,
    unknownKeys: "strip",
    catchall: ZodNever.create(),
    typeName: ZodFirstPartyTypeKind.ZodObject,
    ...processCreateParams(params)
  });
};
var ZodUnion = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const options = this._def.options;
    function handleResults(results) {
      for (const result of results) {
        if (result.result.status === "valid") {
          return result.result;
        }
      }
      for (const result of results) {
        if (result.result.status === "dirty") {
          ctx.common.issues.push(...result.ctx.common.issues);
          return result.result;
        }
      }
      const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return Promise.all(options.map(async (option) => {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        return {
          result: await option._parseAsync({
            data: ctx.data,
            path: ctx.path,
            parent: childCtx
          }),
          ctx: childCtx
        };
      })).then(handleResults);
    } else {
      let dirty = void 0;
      const issues = [];
      for (const option of options) {
        const childCtx = {
          ...ctx,
          common: {
            ...ctx.common,
            issues: []
          },
          parent: null
        };
        const result = option._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: childCtx
        });
        if (result.status === "valid") {
          return result;
        } else if (result.status === "dirty" && !dirty) {
          dirty = { result, ctx: childCtx };
        }
        if (childCtx.common.issues.length) {
          issues.push(childCtx.common.issues);
        }
      }
      if (dirty) {
        ctx.common.issues.push(...dirty.ctx.common.issues);
        return dirty.result;
      }
      const unionErrors = issues.map((issues2) => new ZodError(issues2));
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union,
        unionErrors
      });
      return INVALID;
    }
  }
  get options() {
    return this._def.options;
  }
};
ZodUnion.create = (types, params) => {
  return new ZodUnion({
    options: types,
    typeName: ZodFirstPartyTypeKind.ZodUnion,
    ...processCreateParams(params)
  });
};
var getDiscriminator = (type) => {
  if (type instanceof ZodLazy) {
    return getDiscriminator(type.schema);
  } else if (type instanceof ZodEffects) {
    return getDiscriminator(type.innerType());
  } else if (type instanceof ZodLiteral) {
    return [type.value];
  } else if (type instanceof ZodEnum) {
    return type.options;
  } else if (type instanceof ZodNativeEnum) {
    return util.objectValues(type.enum);
  } else if (type instanceof ZodDefault) {
    return getDiscriminator(type._def.innerType);
  } else if (type instanceof ZodUndefined) {
    return [void 0];
  } else if (type instanceof ZodNull) {
    return [null];
  } else if (type instanceof ZodOptional) {
    return [void 0, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodNullable) {
    return [null, ...getDiscriminator(type.unwrap())];
  } else if (type instanceof ZodBranded) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodReadonly) {
    return getDiscriminator(type.unwrap());
  } else if (type instanceof ZodCatch) {
    return getDiscriminator(type._def.innerType);
  } else {
    return [];
  }
};
var ZodDiscriminatedUnion = class _ZodDiscriminatedUnion extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const discriminator = this.discriminator;
    const discriminatorValue = ctx.data[discriminator];
    const option = this.optionsMap.get(discriminatorValue);
    if (!option) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [discriminator]
      });
      return INVALID;
    }
    if (ctx.common.async) {
      return option._parseAsync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    } else {
      return option._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
    }
  }
  get discriminator() {
    return this._def.discriminator;
  }
  get options() {
    return this._def.options;
  }
  get optionsMap() {
    return this._def.optionsMap;
  }
  /**
   * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
   * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
   * have a different value for each object in the union.
   * @param discriminator the name of the discriminator property
   * @param types an array of object schemas
   * @param params
   */
  static create(discriminator, options, params) {
    const optionsMap = /* @__PURE__ */ new Map();
    for (const type of options) {
      const discriminatorValues = getDiscriminator(type.shape[discriminator]);
      if (!discriminatorValues.length) {
        throw new Error(`A discriminator value for key \`${discriminator}\` could not be extracted from all schema options`);
      }
      for (const value of discriminatorValues) {
        if (optionsMap.has(value)) {
          throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);
        }
        optionsMap.set(value, type);
      }
    }
    return new _ZodDiscriminatedUnion({
      typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,
      discriminator,
      options,
      optionsMap,
      ...processCreateParams(params)
    });
  }
};
function mergeValues(a4, b) {
  const aType = getParsedType(a4);
  const bType = getParsedType(b);
  if (a4 === b) {
    return { valid: true, data: a4 };
  } else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {
    const bKeys = util.objectKeys(b);
    const sharedKeys = util.objectKeys(a4).filter((key) => bKeys.indexOf(key) !== -1);
    const newObj = { ...a4, ...b };
    for (const key of sharedKeys) {
      const sharedValue = mergeValues(a4[key], b[key]);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newObj[key] = sharedValue.data;
    }
    return { valid: true, data: newObj };
  } else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {
    if (a4.length !== b.length) {
      return { valid: false };
    }
    const newArray = [];
    for (let index = 0; index < a4.length; index++) {
      const itemA = a4[index];
      const itemB = b[index];
      const sharedValue = mergeValues(itemA, itemB);
      if (!sharedValue.valid) {
        return { valid: false };
      }
      newArray.push(sharedValue.data);
    }
    return { valid: true, data: newArray };
  } else if (aType === ZodParsedType.date && bType === ZodParsedType.date && +a4 === +b) {
    return { valid: true, data: a4 };
  } else {
    return { valid: false };
  }
}
var ZodIntersection = class extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const handleParsed = (parsedLeft, parsedRight) => {
      if (isAborted(parsedLeft) || isAborted(parsedRight)) {
        return INVALID;
      }
      const merged = mergeValues(parsedLeft.value, parsedRight.value);
      if (!merged.valid) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.invalid_intersection_types
        });
        return INVALID;
      }
      if (isDirty(parsedLeft) || isDirty(parsedRight)) {
        status.dirty();
      }
      return { status: status.value, value: merged.data };
    };
    if (ctx.common.async) {
      return Promise.all([
        this._def.left._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        }),
        this._def.right._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        })
      ]).then(([left, right]) => handleParsed(left, right));
    } else {
      return handleParsed(this._def.left._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }), this._def.right._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      }));
    }
  }
};
ZodIntersection.create = (left, right, params) => {
  return new ZodIntersection({
    left,
    right,
    typeName: ZodFirstPartyTypeKind.ZodIntersection,
    ...processCreateParams(params)
  });
};
var ZodTuple = class _ZodTuple extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.array) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.array,
        received: ctx.parsedType
      });
      return INVALID;
    }
    if (ctx.data.length < this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_small,
        minimum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      return INVALID;
    }
    const rest = this._def.rest;
    if (!rest && ctx.data.length > this._def.items.length) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.too_big,
        maximum: this._def.items.length,
        inclusive: true,
        exact: false,
        type: "array"
      });
      status.dirty();
    }
    const items = [...ctx.data].map((item, itemIndex) => {
      const schema = this._def.items[itemIndex] || this._def.rest;
      if (!schema)
        return null;
      return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));
    }).filter((x3) => !!x3);
    if (ctx.common.async) {
      return Promise.all(items).then((results) => {
        return ParseStatus.mergeArray(status, results);
      });
    } else {
      return ParseStatus.mergeArray(status, items);
    }
  }
  get items() {
    return this._def.items;
  }
  rest(rest) {
    return new _ZodTuple({
      ...this._def,
      rest
    });
  }
};
ZodTuple.create = (schemas, params) => {
  if (!Array.isArray(schemas)) {
    throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  }
  return new ZodTuple({
    items: schemas,
    typeName: ZodFirstPartyTypeKind.ZodTuple,
    rest: null,
    ...processCreateParams(params)
  });
};
var ZodRecord = class _ZodRecord extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.object) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.object,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const pairs = [];
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    for (const key in ctx.data) {
      pairs.push({
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),
        value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),
        alwaysSet: key in ctx.data
      });
    }
    if (ctx.common.async) {
      return ParseStatus.mergeObjectAsync(status, pairs);
    } else {
      return ParseStatus.mergeObjectSync(status, pairs);
    }
  }
  get element() {
    return this._def.valueType;
  }
  static create(first, second, third) {
    if (second instanceof ZodType) {
      return new _ZodRecord({
        keyType: first,
        valueType: second,
        typeName: ZodFirstPartyTypeKind.ZodRecord,
        ...processCreateParams(third)
      });
    }
    return new _ZodRecord({
      keyType: ZodString.create(),
      valueType: first,
      typeName: ZodFirstPartyTypeKind.ZodRecord,
      ...processCreateParams(second)
    });
  }
};
var ZodMap = class extends ZodType {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.map) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.map,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const keyType = this._def.keyType;
    const valueType = this._def.valueType;
    const pairs = [...ctx.data.entries()].map(([key, value], index) => {
      return {
        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, "key"])),
        value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, "value"]))
      };
    });
    if (ctx.common.async) {
      const finalMap = /* @__PURE__ */ new Map();
      return Promise.resolve().then(async () => {
        for (const pair of pairs) {
          const key = await pair.key;
          const value = await pair.value;
          if (key.status === "aborted" || value.status === "aborted") {
            return INVALID;
          }
          if (key.status === "dirty" || value.status === "dirty") {
            status.dirty();
          }
          finalMap.set(key.value, value.value);
        }
        return { status: status.value, value: finalMap };
      });
    } else {
      const finalMap = /* @__PURE__ */ new Map();
      for (const pair of pairs) {
        const key = pair.key;
        const value = pair.value;
        if (key.status === "aborted" || value.status === "aborted") {
          return INVALID;
        }
        if (key.status === "dirty" || value.status === "dirty") {
          status.dirty();
        }
        finalMap.set(key.value, value.value);
      }
      return { status: status.value, value: finalMap };
    }
  }
};
ZodMap.create = (keyType, valueType, params) => {
  return new ZodMap({
    valueType,
    keyType,
    typeName: ZodFirstPartyTypeKind.ZodMap,
    ...processCreateParams(params)
  });
};
var ZodSet = class _ZodSet extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.set) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.set,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const def = this._def;
    if (def.minSize !== null) {
      if (ctx.data.size < def.minSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_small,
          minimum: def.minSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.minSize.message
        });
        status.dirty();
      }
    }
    if (def.maxSize !== null) {
      if (ctx.data.size > def.maxSize.value) {
        addIssueToContext(ctx, {
          code: ZodIssueCode.too_big,
          maximum: def.maxSize.value,
          type: "set",
          inclusive: true,
          exact: false,
          message: def.maxSize.message
        });
        status.dirty();
      }
    }
    const valueType = this._def.valueType;
    function finalizeSet(elements2) {
      const parsedSet = /* @__PURE__ */ new Set();
      for (const element of elements2) {
        if (element.status === "aborted")
          return INVALID;
        if (element.status === "dirty")
          status.dirty();
        parsedSet.add(element.value);
      }
      return { status: status.value, value: parsedSet };
    }
    const elements = [...ctx.data.values()].map((item, i5) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i5)));
    if (ctx.common.async) {
      return Promise.all(elements).then((elements2) => finalizeSet(elements2));
    } else {
      return finalizeSet(elements);
    }
  }
  min(minSize, message) {
    return new _ZodSet({
      ...this._def,
      minSize: { value: minSize, message: errorUtil.toString(message) }
    });
  }
  max(maxSize, message) {
    return new _ZodSet({
      ...this._def,
      maxSize: { value: maxSize, message: errorUtil.toString(message) }
    });
  }
  size(size, message) {
    return this.min(size, message).max(size, message);
  }
  nonempty(message) {
    return this.min(1, message);
  }
};
ZodSet.create = (valueType, params) => {
  return new ZodSet({
    valueType,
    minSize: null,
    maxSize: null,
    typeName: ZodFirstPartyTypeKind.ZodSet,
    ...processCreateParams(params)
  });
};
var ZodFunction = class _ZodFunction extends ZodType {
  constructor() {
    super(...arguments);
    this.validate = this.implement;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.function) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.function,
        received: ctx.parsedType
      });
      return INVALID;
    }
    function makeArgsIssue(args, error) {
      return makeIssue({
        data: args,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x3) => !!x3),
        issueData: {
          code: ZodIssueCode.invalid_arguments,
          argumentsError: error
        }
      });
    }
    function makeReturnsIssue(returns, error) {
      return makeIssue({
        data: returns,
        path: ctx.path,
        errorMaps: [
          ctx.common.contextualErrorMap,
          ctx.schemaErrorMap,
          getErrorMap(),
          errorMap
        ].filter((x3) => !!x3),
        issueData: {
          code: ZodIssueCode.invalid_return_type,
          returnTypeError: error
        }
      });
    }
    const params = { errorMap: ctx.common.contextualErrorMap };
    const fn = ctx.data;
    if (this._def.returns instanceof ZodPromise) {
      const me = this;
      return OK(async function(...args) {
        const error = new ZodError([]);
        const parsedArgs = await me._def.args.parseAsync(args, params).catch((e2) => {
          error.addIssue(makeArgsIssue(args, e2));
          throw error;
        });
        const result = await Reflect.apply(fn, this, parsedArgs);
        const parsedReturns = await me._def.returns._def.type.parseAsync(result, params).catch((e2) => {
          error.addIssue(makeReturnsIssue(result, e2));
          throw error;
        });
        return parsedReturns;
      });
    } else {
      const me = this;
      return OK(function(...args) {
        const parsedArgs = me._def.args.safeParse(args, params);
        if (!parsedArgs.success) {
          throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);
        }
        const result = Reflect.apply(fn, this, parsedArgs.data);
        const parsedReturns = me._def.returns.safeParse(result, params);
        if (!parsedReturns.success) {
          throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);
        }
        return parsedReturns.data;
      });
    }
  }
  parameters() {
    return this._def.args;
  }
  returnType() {
    return this._def.returns;
  }
  args(...items) {
    return new _ZodFunction({
      ...this._def,
      args: ZodTuple.create(items).rest(ZodUnknown.create())
    });
  }
  returns(returnType) {
    return new _ZodFunction({
      ...this._def,
      returns: returnType
    });
  }
  implement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  strictImplement(func) {
    const validatedFunc = this.parse(func);
    return validatedFunc;
  }
  static create(args, returns, params) {
    return new _ZodFunction({
      args: args ? args : ZodTuple.create([]).rest(ZodUnknown.create()),
      returns: returns || ZodUnknown.create(),
      typeName: ZodFirstPartyTypeKind.ZodFunction,
      ...processCreateParams(params)
    });
  }
};
var ZodLazy = class extends ZodType {
  get schema() {
    return this._def.getter();
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const lazySchema = this._def.getter();
    return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });
  }
};
ZodLazy.create = (getter, params) => {
  return new ZodLazy({
    getter,
    typeName: ZodFirstPartyTypeKind.ZodLazy,
    ...processCreateParams(params)
  });
};
var ZodLiteral = class extends ZodType {
  _parse(input) {
    if (input.data !== this._def.value) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_literal,
        expected: this._def.value
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
  get value() {
    return this._def.value;
  }
};
ZodLiteral.create = (value, params) => {
  return new ZodLiteral({
    value,
    typeName: ZodFirstPartyTypeKind.ZodLiteral,
    ...processCreateParams(params)
  });
};
function createZodEnum(values, params) {
  return new ZodEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodEnum,
    ...processCreateParams(params)
  });
}
var ZodEnum = class _ZodEnum extends ZodType {
  constructor() {
    super(...arguments);
    _ZodEnum_cache.set(this, void 0);
  }
  _parse(input) {
    if (typeof input.data !== "string") {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f")) {
      __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values), "f");
    }
    if (!__classPrivateFieldGet(this, _ZodEnum_cache, "f").has(input.data)) {
      const ctx = this._getOrReturnCtx(input);
      const expectedValues = this._def.values;
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Values() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  get Enum() {
    const enumValues = {};
    for (const val of this._def.values) {
      enumValues[val] = val;
    }
    return enumValues;
  }
  extract(values, newDef = this._def) {
    return _ZodEnum.create(values, {
      ...this._def,
      ...newDef
    });
  }
  exclude(values, newDef = this._def) {
    return _ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {
      ...this._def,
      ...newDef
    });
  }
};
_ZodEnum_cache = /* @__PURE__ */ new WeakMap();
ZodEnum.create = createZodEnum;
var ZodNativeEnum = class extends ZodType {
  constructor() {
    super(...arguments);
    _ZodNativeEnum_cache.set(this, void 0);
  }
  _parse(input) {
    const nativeEnumValues = util.getValidEnumValues(this._def.values);
    const ctx = this._getOrReturnCtx(input);
    if (ctx.parsedType !== ZodParsedType.string && ctx.parsedType !== ZodParsedType.number) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        expected: util.joinValues(expectedValues),
        received: ctx.parsedType,
        code: ZodIssueCode.invalid_type
      });
      return INVALID;
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f")) {
      __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(util.getValidEnumValues(this._def.values)), "f");
    }
    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, "f").has(input.data)) {
      const expectedValues = util.objectValues(nativeEnumValues);
      addIssueToContext(ctx, {
        received: ctx.data,
        code: ZodIssueCode.invalid_enum_value,
        options: expectedValues
      });
      return INVALID;
    }
    return OK(input.data);
  }
  get enum() {
    return this._def.values;
  }
};
_ZodNativeEnum_cache = /* @__PURE__ */ new WeakMap();
ZodNativeEnum.create = (values, params) => {
  return new ZodNativeEnum({
    values,
    typeName: ZodFirstPartyTypeKind.ZodNativeEnum,
    ...processCreateParams(params)
  });
};
var ZodPromise = class extends ZodType {
  unwrap() {
    return this._def.type;
  }
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    if (ctx.parsedType !== ZodParsedType.promise && ctx.common.async === false) {
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.promise,
        received: ctx.parsedType
      });
      return INVALID;
    }
    const promisified = ctx.parsedType === ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);
    return OK(promisified.then((data) => {
      return this._def.type.parseAsync(data, {
        path: ctx.path,
        errorMap: ctx.common.contextualErrorMap
      });
    }));
  }
};
ZodPromise.create = (schema, params) => {
  return new ZodPromise({
    type: schema,
    typeName: ZodFirstPartyTypeKind.ZodPromise,
    ...processCreateParams(params)
  });
};
var ZodEffects = class extends ZodType {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    const effect = this._def.effect || null;
    const checkCtx = {
      addIssue: (arg) => {
        addIssueToContext(ctx, arg);
        if (arg.fatal) {
          status.abort();
        } else {
          status.dirty();
        }
      },
      get path() {
        return ctx.path;
      }
    };
    checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);
    if (effect.type === "preprocess") {
      const processed = effect.transform(ctx.data, checkCtx);
      if (ctx.common.async) {
        return Promise.resolve(processed).then(async (processed2) => {
          if (status.value === "aborted")
            return INVALID;
          const result = await this._def.schema._parseAsync({
            data: processed2,
            path: ctx.path,
            parent: ctx
          });
          if (result.status === "aborted")
            return INVALID;
          if (result.status === "dirty")
            return DIRTY(result.value);
          if (status.value === "dirty")
            return DIRTY(result.value);
          return result;
        });
      } else {
        if (status.value === "aborted")
          return INVALID;
        const result = this._def.schema._parseSync({
          data: processed,
          path: ctx.path,
          parent: ctx
        });
        if (result.status === "aborted")
          return INVALID;
        if (result.status === "dirty")
          return DIRTY(result.value);
        if (status.value === "dirty")
          return DIRTY(result.value);
        return result;
      }
    }
    if (effect.type === "refinement") {
      const executeRefinement = (acc) => {
        const result = effect.refinement(acc, checkCtx);
        if (ctx.common.async) {
          return Promise.resolve(result);
        }
        if (result instanceof Promise) {
          throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        }
        return acc;
      };
      if (ctx.common.async === false) {
        const inner = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inner.status === "aborted")
          return INVALID;
        if (inner.status === "dirty")
          status.dirty();
        executeRefinement(inner.value);
        return { status: status.value, value: inner.value };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((inner) => {
          if (inner.status === "aborted")
            return INVALID;
          if (inner.status === "dirty")
            status.dirty();
          return executeRefinement(inner.value).then(() => {
            return { status: status.value, value: inner.value };
          });
        });
      }
    }
    if (effect.type === "transform") {
      if (ctx.common.async === false) {
        const base = this._def.schema._parseSync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (!isValid(base))
          return base;
        const result = effect.transform(base.value, checkCtx);
        if (result instanceof Promise) {
          throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);
        }
        return { status: status.value, value: result };
      } else {
        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((base) => {
          if (!isValid(base))
            return base;
          return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));
        });
      }
    }
    util.assertNever(effect);
  }
};
ZodEffects.create = (schema, effect, params) => {
  return new ZodEffects({
    schema,
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    effect,
    ...processCreateParams(params)
  });
};
ZodEffects.createWithPreprocess = (preprocess, schema, params) => {
  return new ZodEffects({
    schema,
    effect: { type: "preprocess", transform: preprocess },
    typeName: ZodFirstPartyTypeKind.ZodEffects,
    ...processCreateParams(params)
  });
};
var ZodOptional = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.undefined) {
      return OK(void 0);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodOptional.create = (type, params) => {
  return new ZodOptional({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodOptional,
    ...processCreateParams(params)
  });
};
var ZodNullable = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType === ZodParsedType.null) {
      return OK(null);
    }
    return this._def.innerType._parse(input);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodNullable.create = (type, params) => {
  return new ZodNullable({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodNullable,
    ...processCreateParams(params)
  });
};
var ZodDefault = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    let data = ctx.data;
    if (ctx.parsedType === ZodParsedType.undefined) {
      data = this._def.defaultValue();
    }
    return this._def.innerType._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  removeDefault() {
    return this._def.innerType;
  }
};
ZodDefault.create = (type, params) => {
  return new ZodDefault({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodDefault,
    defaultValue: typeof params.default === "function" ? params.default : () => params.default,
    ...processCreateParams(params)
  });
};
var ZodCatch = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const newCtx = {
      ...ctx,
      common: {
        ...ctx.common,
        issues: []
      }
    };
    const result = this._def.innerType._parse({
      data: newCtx.data,
      path: newCtx.path,
      parent: {
        ...newCtx
      }
    });
    if (isAsync(result)) {
      return result.then((result2) => {
        return {
          status: "valid",
          value: result2.status === "valid" ? result2.value : this._def.catchValue({
            get error() {
              return new ZodError(newCtx.common.issues);
            },
            input: newCtx.data
          })
        };
      });
    } else {
      return {
        status: "valid",
        value: result.status === "valid" ? result.value : this._def.catchValue({
          get error() {
            return new ZodError(newCtx.common.issues);
          },
          input: newCtx.data
        })
      };
    }
  }
  removeCatch() {
    return this._def.innerType;
  }
};
ZodCatch.create = (type, params) => {
  return new ZodCatch({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodCatch,
    catchValue: typeof params.catch === "function" ? params.catch : () => params.catch,
    ...processCreateParams(params)
  });
};
var ZodNaN = class extends ZodType {
  _parse(input) {
    const parsedType = this._getType(input);
    if (parsedType !== ZodParsedType.nan) {
      const ctx = this._getOrReturnCtx(input);
      addIssueToContext(ctx, {
        code: ZodIssueCode.invalid_type,
        expected: ZodParsedType.nan,
        received: ctx.parsedType
      });
      return INVALID;
    }
    return { status: "valid", value: input.data };
  }
};
ZodNaN.create = (params) => {
  return new ZodNaN({
    typeName: ZodFirstPartyTypeKind.ZodNaN,
    ...processCreateParams(params)
  });
};
var BRAND = Symbol("zod_brand");
var ZodBranded = class extends ZodType {
  _parse(input) {
    const { ctx } = this._processInputParams(input);
    const data = ctx.data;
    return this._def.type._parse({
      data,
      path: ctx.path,
      parent: ctx
    });
  }
  unwrap() {
    return this._def.type;
  }
};
var ZodPipeline = class _ZodPipeline extends ZodType {
  _parse(input) {
    const { status, ctx } = this._processInputParams(input);
    if (ctx.common.async) {
      const handleAsync = async () => {
        const inResult = await this._def.in._parseAsync({
          data: ctx.data,
          path: ctx.path,
          parent: ctx
        });
        if (inResult.status === "aborted")
          return INVALID;
        if (inResult.status === "dirty") {
          status.dirty();
          return DIRTY(inResult.value);
        } else {
          return this._def.out._parseAsync({
            data: inResult.value,
            path: ctx.path,
            parent: ctx
          });
        }
      };
      return handleAsync();
    } else {
      const inResult = this._def.in._parseSync({
        data: ctx.data,
        path: ctx.path,
        parent: ctx
      });
      if (inResult.status === "aborted")
        return INVALID;
      if (inResult.status === "dirty") {
        status.dirty();
        return {
          status: "dirty",
          value: inResult.value
        };
      } else {
        return this._def.out._parseSync({
          data: inResult.value,
          path: ctx.path,
          parent: ctx
        });
      }
    }
  }
  static create(a4, b) {
    return new _ZodPipeline({
      in: a4,
      out: b,
      typeName: ZodFirstPartyTypeKind.ZodPipeline
    });
  }
};
var ZodReadonly = class extends ZodType {
  _parse(input) {
    const result = this._def.innerType._parse(input);
    const freeze = (data) => {
      if (isValid(data)) {
        data.value = Object.freeze(data.value);
      }
      return data;
    };
    return isAsync(result) ? result.then((data) => freeze(data)) : freeze(result);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ZodReadonly.create = (type, params) => {
  return new ZodReadonly({
    innerType: type,
    typeName: ZodFirstPartyTypeKind.ZodReadonly,
    ...processCreateParams(params)
  });
};
function cleanParams(params, data) {
  const p2 = typeof params === "function" ? params(data) : typeof params === "string" ? { message: params } : params;
  const p22 = typeof p2 === "string" ? { message: p2 } : p2;
  return p22;
}
function custom(check, _params = {}, fatal) {
  if (check)
    return ZodAny.create().superRefine((data, ctx) => {
      var _a, _b;
      const r3 = check(data);
      if (r3 instanceof Promise) {
        return r3.then((r4) => {
          var _a2, _b2;
          if (!r4) {
            const params = cleanParams(_params, data);
            const _fatal = (_b2 = (_a2 = params.fatal) !== null && _a2 !== void 0 ? _a2 : fatal) !== null && _b2 !== void 0 ? _b2 : true;
            ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
          }
        });
      }
      if (!r3) {
        const params = cleanParams(_params, data);
        const _fatal = (_b = (_a = params.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;
        ctx.addIssue({ code: "custom", ...params, fatal: _fatal });
      }
      return;
    });
  return ZodAny.create();
}
var late = {
  object: ZodObject.lazycreate
};
var ZodFirstPartyTypeKind;
(function(ZodFirstPartyTypeKind2) {
  ZodFirstPartyTypeKind2["ZodString"] = "ZodString";
  ZodFirstPartyTypeKind2["ZodNumber"] = "ZodNumber";
  ZodFirstPartyTypeKind2["ZodNaN"] = "ZodNaN";
  ZodFirstPartyTypeKind2["ZodBigInt"] = "ZodBigInt";
  ZodFirstPartyTypeKind2["ZodBoolean"] = "ZodBoolean";
  ZodFirstPartyTypeKind2["ZodDate"] = "ZodDate";
  ZodFirstPartyTypeKind2["ZodSymbol"] = "ZodSymbol";
  ZodFirstPartyTypeKind2["ZodUndefined"] = "ZodUndefined";
  ZodFirstPartyTypeKind2["ZodNull"] = "ZodNull";
  ZodFirstPartyTypeKind2["ZodAny"] = "ZodAny";
  ZodFirstPartyTypeKind2["ZodUnknown"] = "ZodUnknown";
  ZodFirstPartyTypeKind2["ZodNever"] = "ZodNever";
  ZodFirstPartyTypeKind2["ZodVoid"] = "ZodVoid";
  ZodFirstPartyTypeKind2["ZodArray"] = "ZodArray";
  ZodFirstPartyTypeKind2["ZodObject"] = "ZodObject";
  ZodFirstPartyTypeKind2["ZodUnion"] = "ZodUnion";
  ZodFirstPartyTypeKind2["ZodDiscriminatedUnion"] = "ZodDiscriminatedUnion";
  ZodFirstPartyTypeKind2["ZodIntersection"] = "ZodIntersection";
  ZodFirstPartyTypeKind2["ZodTuple"] = "ZodTuple";
  ZodFirstPartyTypeKind2["ZodRecord"] = "ZodRecord";
  ZodFirstPartyTypeKind2["ZodMap"] = "ZodMap";
  ZodFirstPartyTypeKind2["ZodSet"] = "ZodSet";
  ZodFirstPartyTypeKind2["ZodFunction"] = "ZodFunction";
  ZodFirstPartyTypeKind2["ZodLazy"] = "ZodLazy";
  ZodFirstPartyTypeKind2["ZodLiteral"] = "ZodLiteral";
  ZodFirstPartyTypeKind2["ZodEnum"] = "ZodEnum";
  ZodFirstPartyTypeKind2["ZodEffects"] = "ZodEffects";
  ZodFirstPartyTypeKind2["ZodNativeEnum"] = "ZodNativeEnum";
  ZodFirstPartyTypeKind2["ZodOptional"] = "ZodOptional";
  ZodFirstPartyTypeKind2["ZodNullable"] = "ZodNullable";
  ZodFirstPartyTypeKind2["ZodDefault"] = "ZodDefault";
  ZodFirstPartyTypeKind2["ZodCatch"] = "ZodCatch";
  ZodFirstPartyTypeKind2["ZodPromise"] = "ZodPromise";
  ZodFirstPartyTypeKind2["ZodBranded"] = "ZodBranded";
  ZodFirstPartyTypeKind2["ZodPipeline"] = "ZodPipeline";
  ZodFirstPartyTypeKind2["ZodReadonly"] = "ZodReadonly";
})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));
var instanceOfType = (cls, params = {
  message: `Input not instance of ${cls.name}`
}) => custom((data) => data instanceof cls, params);
var stringType = ZodString.create;
var numberType = ZodNumber.create;
var nanType = ZodNaN.create;
var bigIntType = ZodBigInt.create;
var booleanType = ZodBoolean.create;
var dateType = ZodDate.create;
var symbolType = ZodSymbol.create;
var undefinedType = ZodUndefined.create;
var nullType = ZodNull.create;
var anyType = ZodAny.create;
var unknownType = ZodUnknown.create;
var neverType = ZodNever.create;
var voidType = ZodVoid.create;
var arrayType = ZodArray.create;
var objectType = ZodObject.create;
var strictObjectType = ZodObject.strictCreate;
var unionType = ZodUnion.create;
var discriminatedUnionType = ZodDiscriminatedUnion.create;
var intersectionType = ZodIntersection.create;
var tupleType = ZodTuple.create;
var recordType = ZodRecord.create;
var mapType = ZodMap.create;
var setType = ZodSet.create;
var functionType = ZodFunction.create;
var lazyType = ZodLazy.create;
var literalType = ZodLiteral.create;
var enumType = ZodEnum.create;
var nativeEnumType = ZodNativeEnum.create;
var promiseType = ZodPromise.create;
var effectsType = ZodEffects.create;
var optionalType = ZodOptional.create;
var nullableType = ZodNullable.create;
var preprocessType = ZodEffects.createWithPreprocess;
var pipelineType = ZodPipeline.create;
var ostring = () => stringType().optional();
var onumber = () => numberType().optional();
var oboolean = () => booleanType().optional();
var coerce = {
  string: (arg) => ZodString.create({ ...arg, coerce: true }),
  number: (arg) => ZodNumber.create({ ...arg, coerce: true }),
  boolean: (arg) => ZodBoolean.create({
    ...arg,
    coerce: true
  }),
  bigint: (arg) => ZodBigInt.create({ ...arg, coerce: true }),
  date: (arg) => ZodDate.create({ ...arg, coerce: true })
};
var NEVER = INVALID;
var z = Object.freeze({
  __proto__: null,
  defaultErrorMap: errorMap,
  setErrorMap,
  getErrorMap,
  makeIssue,
  EMPTY_PATH,
  addIssueToContext,
  ParseStatus,
  INVALID,
  DIRTY,
  OK,
  isAborted,
  isDirty,
  isValid,
  isAsync,
  get util() {
    return util;
  },
  get objectUtil() {
    return objectUtil;
  },
  ZodParsedType,
  getParsedType,
  ZodType,
  datetimeRegex,
  ZodString,
  ZodNumber,
  ZodBigInt,
  ZodBoolean,
  ZodDate,
  ZodSymbol,
  ZodUndefined,
  ZodNull,
  ZodAny,
  ZodUnknown,
  ZodNever,
  ZodVoid,
  ZodArray,
  ZodObject,
  ZodUnion,
  ZodDiscriminatedUnion,
  ZodIntersection,
  ZodTuple,
  ZodRecord,
  ZodMap,
  ZodSet,
  ZodFunction,
  ZodLazy,
  ZodLiteral,
  ZodEnum,
  ZodNativeEnum,
  ZodPromise,
  ZodEffects,
  ZodTransformer: ZodEffects,
  ZodOptional,
  ZodNullable,
  ZodDefault,
  ZodCatch,
  ZodNaN,
  BRAND,
  ZodBranded,
  ZodPipeline,
  ZodReadonly,
  custom,
  Schema: ZodType,
  ZodSchema: ZodType,
  late,
  get ZodFirstPartyTypeKind() {
    return ZodFirstPartyTypeKind;
  },
  coerce,
  any: anyType,
  array: arrayType,
  bigint: bigIntType,
  boolean: booleanType,
  date: dateType,
  discriminatedUnion: discriminatedUnionType,
  effect: effectsType,
  "enum": enumType,
  "function": functionType,
  "instanceof": instanceOfType,
  intersection: intersectionType,
  lazy: lazyType,
  literal: literalType,
  map: mapType,
  nan: nanType,
  nativeEnum: nativeEnumType,
  never: neverType,
  "null": nullType,
  nullable: nullableType,
  number: numberType,
  object: objectType,
  oboolean,
  onumber,
  optional: optionalType,
  ostring,
  pipeline: pipelineType,
  preprocess: preprocessType,
  promise: promiseType,
  record: recordType,
  set: setType,
  strictObject: strictObjectType,
  string: stringType,
  symbol: symbolType,
  transformer: effectsType,
  tuple: tupleType,
  "undefined": undefinedType,
  union: unionType,
  unknown: unknownType,
  "void": voidType,
  NEVER,
  ZodIssueCode,
  quotelessJson,
  ZodError
});

// node_modules/@scalar/api-client/dist/views/Request/libs/watch-mode.js
var G = (e2, s4 = []) => {
  const t3 = [];
  let a4 = false;
  for (let u5 = 0; u5 < e2.length; u5++) {
    if (a4) {
      a4 = false;
      continue;
    }
    const n2 = e2[u5], r3 = e2[u5 + 1];
    if (n2) {
      if (s4.length)
        n2.path = [...s4, ...n2.path], r3 && (r3.path = [...s4, ...r3.path]);
      else if (n2.path[0] !== "paths") {
        t3.push(n2);
        continue;
      }
      if (n2.type === "REMOVE" && (r3 == null ? void 0 : r3.type) === "CREATE") {
        const [, l2, p2] = n2.path, [, o3, h2] = r3.path, c4 = ["paths", o3].filter((f4) => typeof f4 == "string");
        if (l2 !== o3 && t3.push({
          type: "CHANGE",
          path: ["paths", "path"],
          oldValue: l2,
          value: o3
        }), p2 && typeof h2 == "string" && p2 !== h2 && o3 && (t3.push({
          type: "CHANGE",
          path: ["paths", o3, "method"],
          oldValue: p2,
          value: h2
        }), c4.push(h2)), s4.length === 0) {
          const f4 = diff(n2.oldValue, r3.value);
          if (f4.length) {
            const g4 = G(f4, c4);
            t3.push(...g4);
          }
        }
        a4 = true;
      } else n2.type === "CREATE" && n2.path.length > 3 && typeof n2.path.at(-1) != "number" ? t3.push({ ...n2, type: "CHANGE", oldValue: void 0 }) : n2.type === "REMOVE" && n2.path.length > 3 && typeof n2.path.at(-1) != "number" ? t3.push({ ...n2, type: "CHANGE", value: void 0 }) : t3.push(n2);
    }
  }
  return t3;
};
var v = (e2, s4, t3) => {
  for (const a4 of e2) {
    const u5 = s4[a4];
    if (u5 && t3(u5)) return u5;
  }
  return null;
};
var d2 = (e2) => e2 instanceof z.ZodOptional ? d2(e2.unwrap()) : e2 instanceof z.ZodDefault ? d2(e2._def.innerType) : e2 instanceof z.ZodEffects ? d2(e2._def.schema) : e2 instanceof z.ZodCatch ? d2(e2._def.innerType) : e2;
var _ = (e2, s4) => {
  let t3 = e2;
  for (const a4 of s4) {
    if (t3 = d2(t3), t3 instanceof z.ZodAny)
      return t3;
    if (t3 instanceof z.ZodObject && typeof a4 == "string" && a4 in t3.shape)
      t3 = t3.shape[a4];
    else if (t3 instanceof z.ZodArray)
      if (typeof a4 == "number")
        t3 = t3.element;
      else if (typeof a4 == "string")
        if (t3 = t3.element, t3 instanceof z.ZodObject && a4 in t3.shape)
          t3 = t3.shape[a4];
        else
          return null;
      else
        return null;
    else if (t3 instanceof z.ZodRecord)
      t3 = t3.valueSchema;
    else
      return null;
    t3 = d2(t3);
  }
  return t3;
};
var E2 = (e2, s4) => {
  const t3 = _(e2, s4.path);
  if (!t3) return null;
  const a4 = s4.path.join("."), u5 = s4.path.slice(0, -1).join(".");
  if (s4.type === "REMOVE")
    return {
      path: a4,
      pathMinusOne: u5,
      value: void 0
    };
  const n2 = schemaModel(s4.value, t3, false);
  return n2 ? {
    path: a4,
    pathMinusOne: u5,
    value: n2
  } : null;
};
var L = (e2, { activeCollection: s4 }, { collectionMutators: t3 }) => {
  if (!s4.value) return false;
  if (typeof e2.path[e2.path.length - 1] == "number" && (e2.type === "CREATE" || e2.type === "REMOVE")) {
    const a4 = E2(collectionSchema, {
      ...e2,
      path: e2.path
    });
    if (!a4) return false;
    const u5 = [...getNestedValue(s4.value, a4.pathMinusOne)];
    e2.type === "CREATE" ? u5.push(a4.value) : e2.type === "REMOVE" && u5.pop(), t3.edit(s4.value.uid, a4.pathMinusOne, u5);
  } else {
    const a4 = E2(collectionSchema, e2);
    if (!a4) return false;
    t3.edit(s4.value.uid, a4.path, a4.value);
  }
  return true;
};
var T2 = (e2, s4) => {
  const { requests: t3, requestExamples: a4, requestExampleMutators: u5 } = s4, n2 = t3[e2];
  n2 == null || n2.examples.forEach((r3) => {
    var p2;
    const l2 = createExampleFromRequest(n2, ((p2 = a4[r3]) == null ? void 0 : p2.name) ?? "Default");
    l2 && u5.set({
      ...l2,
      uid: r3
    });
  });
};
var P = (e2, { activeCollection: s4 }, t3) => {
  if (!s4.value) return false;
  const { requests: a4, requestMutators: u5 } = t3, [, n2, r3, ...l2] = e2.path;
  if (n2 === "path" && e2.type === "CHANGE")
    s4.value.requests.forEach((p2) => {
      var o3;
      ((o3 = a4[p2]) == null ? void 0 : o3.path) === e2.oldValue && u5.edit(p2, "path", e2.value);
    });
  else if (r3 === "method" && e2.type === "CHANGE")
    s4.value.requests.forEach((p2) => {
      var o3, h2;
      ((o3 = a4[p2]) == null ? void 0 : o3.method) === e2.oldValue && ((h2 = a4[p2]) == null ? void 0 : h2.path) === n2 && u5.edit(p2, "method", e2.value);
    });
  else if (e2.type !== "CHANGE" && typeof l2.at(-1) == "number") {
    const p2 = v(
      s4.value.requests,
      a4,
      (c4) => c4.path === n2 && c4.method === r3
    ), o3 = E2(requestSchema, {
      ...e2,
      path: e2.path.slice(3)
    });
    if (!p2 || !o3) return false;
    const h2 = [...getNestedValue(p2, o3.pathMinusOne)];
    e2.type === "CREATE" ? h2.push(o3.value) : e2.type === "REMOVE" && h2.pop(), u5.edit(p2.uid, o3.pathMinusOne, h2), (e2.path[3] === "parameters" || e2.path[3] === "requestBody") && T2(p2.uid, t3);
  } else if (e2.type === "CREATE") {
    const [p2] = Object.entries(e2.value ?? {}), [o3, h2] = p2 ?? [], c4 = r3 ? e2.value : h2, f4 = r3 || o3, g4 = serverSchema.array().parse(c4.servers ?? []), { security: y, ...N } = c4, A = {
      ...N,
      method: isHttpMethod(f4) ? f4 : "get",
      path: n2,
      parameters: c4.parameters ?? [],
      servers: g4.map((m9) => m9.uid)
    };
    y != null && y.length && (A.security = y.map((m9) => {
      if (Object.keys(m9).length) {
        const [O2] = Object.keys(m9);
        return O2 ? {
          [O2]: m9[O2]
        } : m9;
      }
      return m9;
    }));
    const b = schemaModel(A, requestSchema, false);
    if (!b) return false;
    u5.add(b, s4.value.uid);
  } else if (e2.type === "REMOVE") {
    const p2 = v(
      s4.value.requests,
      a4,
      (o3) => o3.path === n2 && o3.method === r3
    );
    if (!p2) return false;
    u5.delete(p2, s4.value.uid);
  } else if (e2.type === "CHANGE") {
    const p2 = v(
      s4.value.requests,
      a4,
      (h2) => h2.path === n2 && h2.method === r3
    ), o3 = E2(requestSchema, { ...e2, path: l2 });
    if (!p2 || !o3) return false;
    u5.edit(p2.uid, o3.path, o3.value), (e2.path[3] === "parameters" || e2.path[3] === "requestBody") && T2(p2.uid, t3);
  }
  return true;
};
var W = (e2, { activeCollection: s4 }, { servers: t3, serverMutators: a4 }) => {
  if (!s4.value) return false;
  const [, u5, ...n2] = e2.path;
  if (n2 != null && n2.length) {
    const r3 = s4.value.servers[u5];
    if (!r3) return false;
    const l2 = t3[r3], p2 = E2(serverSchema, { ...e2, path: n2 });
    if (!l2 || !p2) return false;
    const h2 = e2.type === "REMOVE" && n2[n2.length - 1] === "variables" ? {} : p2.value;
    a4.edit(r3, p2.path, h2);
  } else if (e2.type === "REMOVE") {
    if (!s4.value.servers[u5]) return false;
    a4.delete(s4.value.servers[u5], s4.value.uid);
  } else if (e2.type === "CREATE") {
    const r3 = schemaModel(e2.value, serverSchema, false);
    if (!r3) return false;
    a4.add(r3, s4.value.uid);
  }
  return true;
};
var I2 = (e2, { activeCollection: s4 }, { tags: t3, tagMutators: a4 }) => {
  if (!s4.value) return false;
  const [, u5, ...n2] = e2.path;
  if (n2 != null && n2.length) {
    const r3 = s4.value.tags[u5];
    if (!r3) return false;
    const l2 = t3[r3], p2 = E2(tagSchema, { ...e2, path: n2 });
    if (!l2 || !p2) return false;
    a4.edit(r3, p2.path, p2.value);
  } else if (e2.type === "REMOVE") {
    const r3 = s4.value.tags[u5];
    if (!r3) return false;
    const l2 = t3[r3];
    if (!l2) return false;
    a4.delete(l2, s4.value.uid);
  } else if (e2.type === "CREATE") {
    const r3 = schemaModel(e2.value, tagSchema, false);
    if (!r3) return false;
    a4.add(r3, s4.value.uid);
  }
  return true;
};
var w3 = (e2, s4, t3) => {
  const a4 = d2(e2);
  if (a4 instanceof z.ZodUnion || a4 instanceof z.ZodDiscriminatedUnion) {
    for (const u5 of a4.options)
      if (u5 instanceof z.ZodObject && s4 in u5.shape && u5.shape[s4] instanceof z.ZodLiteral && u5.shape[s4].value === t3)
        return u5;
  }
  return null;
};
var J = (e2, { activeCollection: s4 }, { securitySchemes: t3, securitySchemeMutators: a4 }) => {
  if (!s4.value) return false;
  const [, , u5, ...n2] = e2.path, r3 = t3[u5] ?? v(
    s4.value.securitySchemes,
    t3,
    (l2) => l2.nameKey === u5
  );
  if (n2 != null && n2.length) {
    const l2 = w3(securitySchemeSchema, "type", (r3 == null ? void 0 : r3.type) ?? "");
    if (!l2 || !r3) return false;
    const p2 = E2(l2, { ...e2, path: n2 });
    if (!p2) return false;
    const o3 = p2.path;
    a4.edit(r3.uid, o3, p2.value);
  } else if (e2.type === "REMOVE") {
    if (!r3) return false;
    a4.delete(r3.uid);
  } else e2.type === "CREATE" && a4.add(securitySchemeSchema.parse(e2.value), s4.value.uid);
  return true;
};

// node_modules/@scalar/api-client/dist/views/Request/hooks/useOpenApiWatcher.js
var k3 = 5 * 1e3;
var q = 60 * 1e3;
var J2 = () => {
  const { toast: h2 } = i(), c4 = F(), o3 = je(), { activeCollection: t3, activeWorkspace: W2 } = c4, { collectionMutators: n2 } = o3, r3 = (e2) => h2(`[useOpenApiWatcher] Changes to the ${e2} were not applied`, "error"), w4 = (e2) => {
    e2.path[0] === "info" || e2.path[0] === "security" ? L(e2, c4, o3) || r3("collection") : e2.path[0] === "components" && e2.path[1] === "securitySchemes" ? J(e2, c4, o3) || r3("securitySchemes") : e2.path[0] === "servers" ? W(e2, c4, o3) || r3("servers") : e2.path[0] === "tags" ? I2(e2, c4, o3) || r3("tags") : e2.path[0] === "paths" && (P(e2, c4, o3) || r3("requests"));
  }, { pause: p2, resume: m9 } = useTimeoutPoll(async () => {
    var l2, v3;
    const e2 = (l2 = t3.value) == null ? void 0 : l2.documentUrl;
    if (!e2) return;
    const s4 = w[e2];
    try {
      const a4 = await fetchSpecFromUrl(e2, (v3 = W2.value) == null ? void 0 : v3.proxyUrl, false), u5 = createHash(a4);
      if (n2.edit(t3.value.uid, "watchModeStatus", "WATCHING"), s4 != null && s4.hash)
        if (s4.hash && s4.hash !== u5) {
          const { schema: i5 } = await parseSchema(a4), A = diff(s4.schema, i5), y = G(A);
          try {
            y.forEach(w4), w[e2] = {
              hash: u5,
              schema: i5
            };
          } catch (E3) {
            console.error("[useOpenApiWatcher] Error:", E3);
          }
        } else console.log("[useOpenApiWatcher] No changes detected yet…");
      else {
        const { schema: i5 } = await parseSchema(a4);
        i5 && (w[e2] = {
          hash: u5,
          schema: i5
        });
      }
    } catch (a4) {
      console.error("[useOpenApiWatcher] Error:", a4), console.info("[useOpenApiWatcher] Pausing watcher for 60 seconds"), p2(), n2.edit(t3.value.uid, "watchModeStatus", "ERROR"), h2("[useOpenApiWatcher] Unable to fetch the spec file, paused the watcher for 60 seconds", "error"), setTimeout(() => {
        console.info("[useOpenApiWatcher] Resuming watcher"), m9();
      }, q);
    }
  }, k3);
  watch(
    [() => {
      var e2;
      return (e2 = t3.value) == null ? void 0 : e2.documentUrl;
    }, () => {
      var e2;
      return (e2 = t3.value) == null ? void 0 : e2.watchMode;
    }],
    ([e2, s4]) => {
      e2 && s4 ? (console.info(`[useOpenApiWatcher] Watching ${e2} …`), m9()) : t3.value && (p2(), n2.edit(t3.value.uid, "watchModeStatus", "IDLE"));
    },
    { immediate: true }
  );
};

// node_modules/@scalar/api-client/dist/assets/rabbit.ascii.js
var _2 = `         ,\\
         \\\\\\,_
          \\\` ,\\
     __,.-" =__)
   ."        )
,_/   ,    \\/\\_
\\_| )_-\\ \\_-\`
   \`-----\` \`--\``;

// node_modules/@scalar/api-client/dist/assets/rabbitjump.ascii.js
var _3 = `         __
        // \\,_
          \\\` ,\\
     __,.-" =__)
   ."        )
,_/   ,    \\/\\ 
\\_| // /  /  /
   /  /    `;

// node_modules/@scalar/api-client/dist/components/EnvironmentSelector/EnvironmentSelector.vue.js
var q2 = { class: "m-0 flex items-center gap-1.5 whitespace-nowrap font-medium" };
var G2 = { class: "flex h-4 w-4 items-center justify-center" };
var Y = defineComponent({
  __name: "EnvironmentSelector",
  setup(H) {
    const { activeCollection: a4, activeWorkspace: i5, activeEnvironment: v3 } = F(), { collectionMutators: _4 } = je(), { layout: b } = s(), C5 = useRouter(), d5 = (e2) => {
      a4.value && i5.value && (_4.edit(
        a4.value.uid,
        "x-scalar-active-environment",
        e2
      ), i5.value.activeEnvironmentId = e2);
    }, S = () => {
      var e2;
      return C5.push({
        name: "environment",
        params: {
          environment: (e2 = i5.value) == null ? void 0 : e2.uid
        }
      });
    }, y = computed(() => {
      const { value: e2 } = v3, { value: t3 } = a4;
      return (e2 == null ? void 0 : e2.uid) || (t3 == null ? void 0 : t3["x-scalar-active-environment"]) || "No Environment";
    }), N = computed(() => {
      const { value: e2 } = a4, t3 = e2 == null ? void 0 : e2["x-scalar-environments"];
      return t3 ? Object.entries(t3).map(([o3, r3]) => ({
        ...r3,
        uid: o3,
        name: o3
      })) : [];
    }), p2 = (e2) => {
      const t3 = e2["x-scalar-active-environment"];
      t3 && a4.value && i5.value ? (a4.value["x-scalar-active-environment"] = t3, i5.value.activeEnvironmentId = t3) : i5.value && (i5.value.activeEnvironmentId = "");
    };
    return watch(
      a4,
      (e2) => e2 && p2(e2)
    ), onMounted(() => {
      a4.value && p2(a4.value);
    }), (e2, t3) => (openBlock(), createElementBlock("div", null, [
      createVNode(unref(x), { placement: "bottom-end" }, {
        items: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(N.value, (o3) => (openBlock(), createBlock(unref(m), {
            key: o3.uid,
            class: "group/item flex items-center gap-1.5 overflow-hidden text-ellipsis whitespace-nowrap",
            onClick: withModifiers((r3) => d5(o3.uid), ["stop"])
          }, {
            default: withCtx(() => {
              var r3;
              return [
                createVNode(unref(u), {
                  selected: ((r3 = unref(a4)) == null ? void 0 : r3["x-scalar-active-environment"]) === o3.uid
                }, null, 8, ["selected"]),
                createTextVNode(" " + toDisplayString(o3.name), 1)
              ];
            }),
            _: 2
          }, 1032, ["onClick"]))), 128)),
          createVNode(unref(m), {
            class: "group/item flex items-center gap-1.5 overflow-hidden text-ellipsis whitespace-nowrap",
            onClick: t3[0] || (t3[0] = withModifiers((o3) => d5(""), ["stop"]))
          }, {
            default: withCtx(() => {
              var o3, r3;
              return [
                createBaseVNode("div", {
                  class: normalizeClass([
                    "flex h-4 w-4 items-center justify-center rounded-full p-[3px]",
                    ((o3 = unref(v3)) == null ? void 0 : o3.uid) === "" && ((r3 = unref(a4)) == null ? void 0 : r3["x-scalar-active-environment"]) === "" ? "bg-c-accent text-b-1" : "shadow-border text-transparent"
                  ])
                }, [
                  createVNode(unref(c), {
                    class: "size-2.5",
                    icon: "Checkmark",
                    thickness: "3"
                  })
                ], 2),
                t3[1] || (t3[1] = createTextVNode(" No Environment "))
              ];
            }),
            _: 1
          }),
          createVNode(unref(u2)),
          unref(b) !== "modal" ? (openBlock(), createBlock(unref(m), {
            key: 0,
            class: "flex items-center gap-1.5",
            onClick: S
          }, {
            default: withCtx(() => [
              createBaseVNode("div", G2, [
                createVNode(unref(c), {
                  icon: "Brackets",
                  size: "sm"
                })
              ]),
              t3[2] || (t3[2] = createBaseVNode("span", { class: "leading-none" }, "Manage Environments", -1))
            ]),
            _: 1
          })) : createCommentVNode("", true)
        ]),
        default: withCtx(() => [
          createVNode(unref($), {
            class: "text-c-1 hover:bg-b-2 h-auto w-fit justify-start px-1.5 py-1.5 pl-2 font-normal",
            fullWidth: "",
            variant: "ghost"
          }, {
            default: withCtx(() => [
              createBaseVNode("h2", q2, toDisplayString(y.value), 1)
            ]),
            _: 1
          })
        ]),
        _: 1
      })
    ]));
  }
});

// node_modules/@scalar/api-client/dist/components/Search/useSearch.js
function Q2() {
  const u5 = useRouter(), { activeWorkspace: E3, activeWorkspaceRequests: f4, activeWorkspaceCollections: w4 } = F(), { requests: x3, tags: A } = je(), c4 = ref([]), i5 = ref([]), a4 = ref(0), r3 = ref(""), v3 = ref(null), m9 = ref([]), p2 = new Fuse(c4.value, {
    keys: ["title", "description", "body"]
  }), C5 = () => {
    r3.value = "", a4.value = 0, i5.value = [], v3.value instanceof HTMLInputElement && v3.value.blur();
  }, d5 = (e2) => {
    c4.value = e2.filter((t3) => !shouldIgnoreEntity(t3)).filter((t3) => {
      var k6;
      const s4 = (k6 = w4.value) == null ? void 0 : k6.find(
        (l2) => l2.requests.includes(t3.uid)
      );
      return !!!(s4 != null && s4.tags.map((l2) => A[l2]).filter(isDefined).filter((l2) => {
        var I3;
        return (I3 = t3.tags) == null ? void 0 : I3.includes(l2.name);
      }).filter((l2) => shouldIgnoreEntity(l2)).length);
    }).map((t3) => {
      var s4, o3;
      return {
        id: t3.uid,
        title: t3.summary ?? t3.method,
        description: t3.description ?? "",
        httpVerb: t3.method,
        path: t3.path,
        link: (o3 = u5 == null ? void 0 : u5.resolve({
          name: "request",
          params: {
            [a2.Request]: t3.uid,
            [a2.Workspace]: (s4 = E3.value) == null ? void 0 : s4.uid
          }
        })) == null ? void 0 : o3.href
      };
    }), p2.setCollection(c4.value);
  }, R = () => {
    a4.value = 0, i5.value = p2.search(r3.value);
  };
  watch(r3, (e2) => {
    e2.length ? R() : i5.value = [];
  });
  const D2 = (e2) => {
    const t3 = e2 === "up" ? -1 : 1, s4 = h2.value.length;
    a4.value = (a4.value + t3 + s4) % s4, nextTick(() => {
      const o3 = m9.value[a4.value];
      o3 instanceof HTMLElement && o3.scrollIntoView({
        behavior: "smooth",
        block: "center"
      });
    });
  }, F3 = () => {
    if (a4.value >= 0) {
      const e2 = h2.value[a4.value];
      e2 && g4(e2);
    }
  }, H = computed(() => f4.value.map((e2) => x3[e2]).filter(isDefined));
  watch(
    f4,
    () => {
      d5(H.value);
    },
    { immediate: true }
  );
  const g4 = (e2) => {
    u5.push(e2.item.id), C5();
  }, h2 = computed(() => r3.value.length === 0 ? c4.value.map((e2) => ({
    item: e2
  })) : i5.value);
  return {
    searchText: r3,
    searchResultsWithPlaceholderResults: h2,
    selectedSearchResult: a4,
    onSearchResultClick: g4,
    fuseSearch: R,
    searchInputRef: v3,
    searchResultRefs: m9,
    navigateSearchResults: D2,
    selectSearchResult: F3,
    populateFuseDataArray: d5
  };
}

// node_modules/@scalar/api-client/dist/views/Request/handle-drag.js
function F2(f4, { collections: l2, collectionMutators: a4, tags: o3, tagMutators: p2, workspaceMutators: x3 }) {
  const { layout: b } = s(), y = (i5, r3) => {
    i5.type === "collection" ? a4.edit(i5.uid, "children", r3) : i5.type === "tag" && p2.edit(i5.uid, "children", r3);
  };
  return {
    handleDragEnd: (i5, r3) => {
      var D2, U, v3, C5;
      if (!i5 || !r3) return;
      const { id: n2, parentId: d5 } = i5, { id: u5, parentId: s4, offset: c4 } = r3;
      if (d5 ? l2[d5] ? a4.edit(
        d5,
        "children",
        l2[d5].children.filter((e2) => e2 !== n2)
      ) : o3[d5] && p2.edit(
        d5,
        "children",
        o3[d5].children.filter((e2) => e2 !== n2)
      ) : x3.edit(
        (D2 = f4.value) == null ? void 0 : D2.uid,
        "collections",
        ((U = f4.value) == null ? void 0 : U.collections.filter((e2) => e2 !== n2)) ?? []
      ), c4 === 2) {
        const e2 = l2[u5] || o3[u5];
        e2 && y(e2, [...e2.children ?? [], n2]);
      } else if (s4) {
        const e2 = l2[s4] || o3[s4];
        if (!e2) return;
        const t3 = [...e2.children ?? []], h2 = t3.findIndex((E3) => u5 === E3) ?? 0;
        t3.splice(h2 + c4, 0, n2), y(e2, t3);
      } else {
        const e2 = [...((v3 = f4.value) == null ? void 0 : v3.collections) ?? []], t3 = e2.findIndex((h2) => u5 === h2) ?? 0;
        e2.splice(t3 + c4, 0, n2), x3.edit((C5 = f4.value) == null ? void 0 : C5.uid, "collections", e2);
      }
    },
    isDroppable: (i5, r3) => {
      var n2, d5;
      return !(b === "modal" || !l2[i5.id] && r3.offset !== 2 || l2[i5.id] && ((d5 = (n2 = l2[r3.id]) == null ? void 0 : n2.info) == null ? void 0 : d5.title) === "Drafts");
    }
  };
}

// node_modules/@scalar/api-client/dist/components/Sidebar/Actions/EditSidebarListCollection.vue.js
var C4 = { class: "grid grid-cols-[auto,1fr] gap-2" };
var g3 = { class: "flex aspect-square" };
var k4 = defineComponent({
  __name: "EditSidebarListCollection",
  props: {
    name: {},
    icon: {}
  },
  emits: ["close", "edit"],
  setup(d5, { emit: c4 }) {
    const r3 = d5, i5 = c4, a4 = ref(r3.name), o3 = ref(r3.icon);
    return (w4, e2) => (openBlock(), createBlock(a, {
      onCancel: e2[2] || (e2[2] = (t3) => i5("close")),
      onSubmit: e2[3] || (e2[3] = (t3) => i5("edit", a4.value, o3.value))
    }, {
      default: withCtx(() => [
        createBaseVNode("div", C4, [
          createBaseVNode("div", g3, [
            createVNode($2, {
              modelValue: o3.value,
              "onUpdate:modelValue": e2[0] || (e2[0] = (t3) => o3.value = t3),
              placement: "bottom-start"
            }, {
              default: withCtx(() => [
                createVNode(unref($), {
                  class: "aspect-square h-auto px-0",
                  variant: "outlined"
                }, {
                  default: withCtx(() => [
                    createVNode(unref(d), {
                      class: "text-c-2 size-4",
                      src: o3.value
                    }, null, 8, ["src"])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["modelValue"])
          ]),
          createVNode(unref(m2), {
            modelValue: a4.value,
            "onUpdate:modelValue": e2[1] || (e2[1] = (t3) => a4.value = t3),
            autofocus: "",
            class: "flex-1"
          }, null, 8, ["modelValue"])
        ])
      ]),
      _: 1
    }));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebarItemMenu.vue2.js
var ge = defineComponent({
  __name: "RequestSidebarItemMenu",
  props: {
    menuItem: {}
  },
  emits: ["closeMenu", "toggleWatchMode", "clearDrafts"],
  setup(A, { emit: B2 }) {
    const u5 = A, $4 = B2, { replace: D2 } = useRouter(), {
      activeRouterParams: x3,
      activeWorkspaceCollections: q4,
      activeWorkspaceRequests: U
    } = F(), { events: V, requestMutators: L2 } = je(), I3 = E(), w4 = E(), C5 = E(), P3 = () => {
      var e2;
      return V.commandPalette.emit({
        commandName: "Add Example",
        metaData: {
          itemUid: (e2 = u5.menuItem.item) == null ? void 0 : e2.entity.uid
        }
      });
    }, z2 = (e2, t3) => {
      var i5;
      (i5 = u5.menuItem.item) == null || i5.edit(e2, t3), I3.hide();
    }, F3 = () => {
      var e2, t3, i5;
      if ((e2 = u5.menuItem.item) == null || e2.delete(), !U.value.length) {
        const { request: f4 } = Q(), a4 = q4.value.find(
          (o3) => {
            var l2;
            return ((l2 = o3.info) == null ? void 0 : l2.title) === "Drafts";
          }
        );
        a4 && (L2.add(f4, a4.uid), D2({
          name: "request",
          params: {
            [a2.Request]: f4.uid
          }
        }));
      }
      if (x3.value[a2.Request] === ((t3 = u5.menuItem.item) == null ? void 0 : t3.entity.uid) && D2({
        name: "request",
        params: {
          [a2.Request]: "default"
        }
      }), x3.value[a2.Examples] === ((i5 = u5.menuItem.item) == null ? void 0 : i5.entity.uid) && D2({
        name: "request",
        params: {
          [a2.Request]: "default"
        }
      }), q4.value[0]) {
        const f4 = q4.value[0].requests[0];
        D2({
          name: "request",
          params: {
            [a2.Request]: f4
          }
        });
      }
      w4.hide();
    }, k6 = ref(null);
    watch([() => u5.menuItem.open, k6], async ([e2]) => {
      var t3, i5;
      e2 && ((i5 = (t3 = k6.value) == null ? void 0 : t3.$parent) != null && i5.$el) && k6.value.$parent.$el.focus();
    });
    const N = () => u5.menuItem.open && $4("closeMenu");
    onMounted(() => window.addEventListener("click", N)), onBeforeUnmount(() => window.removeEventListener("click", N));
    const K2 = () => {
      $4("toggleWatchMode", u5.menuItem.item);
    }, j = () => {
      $4("clearDrafts"), C5.hide();
    }, E3 = computed(() => {
      var e2;
      return ((e2 = u5.menuItem.item) == null ? void 0 : e2.title) === "Drafts";
    });
    return (e2, t3) => {
      var i5, f4;
      return openBlock(), createElementBlock(Fragment, null, [
        e2.menuItem.targetRef && e2.menuItem.open ? (openBlock(), createBlock(unref(I), {
          key: 0,
          placement: "right-start",
          target: e2.menuItem.targetRef,
          teleport: ""
        }, {
          floating: withCtx(() => [
            createVNode(unref(B), {
              onKeydown: t3[3] || (t3[3] = withKeys((a4) => e2.$emit("closeMenu"), ["escape"]))
            }, {
              default: withCtx(() => {
                var a4, o3;
                return [
                  ((a4 = e2.menuItem.item) == null ? void 0 : a4.entity.type) === "request" ? (openBlock(), createBlock(unref(c2), {
                    key: 0,
                    class: "flex gap-2",
                    onClick: P3
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(c), {
                        class: "inline-flex",
                        icon: "Example",
                        size: "md",
                        thickness: "1.5"
                      }),
                      t3[8] || (t3[8] = createBaseVNode("span", null, "Add Example", -1))
                    ]),
                    _: 1
                  })) : createCommentVNode("", true),
                  E3.value ? createCommentVNode("", true) : (openBlock(), createBlock(unref(c2), {
                    key: 1,
                    ref_key: "menuRef",
                    ref: k6,
                    class: "flex gap-2",
                    onClick: t3[0] || (t3[0] = (l2) => unref(I3).show())
                  }, {
                    default: withCtx(() => {
                      var l2;
                      return [
                        createVNode(unref(c), {
                          class: "inline-flex",
                          icon: "Edit",
                          size: "md",
                          thickness: "1.5"
                        }),
                        createBaseVNode("span", null, [
                          ((l2 = e2.menuItem.item) == null ? void 0 : l2.entity.type) === "collection" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                            createTextVNode(" Edit ")
                          ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                            createTextVNode(" Rename ")
                          ], 64))
                        ])
                      ];
                    }),
                    _: 1
                  }, 512)),
                  (o3 = e2.menuItem.item) != null && o3.documentUrl ? (openBlock(), createBlock(unref(c2), {
                    key: 2,
                    ref_key: "menuRef",
                    ref: k6,
                    class: "flex gap-2",
                    onClick: K2
                  }, {
                    default: withCtx(() => {
                      var l2, v3;
                      return [
                        createVNode(unref(c), {
                          class: "inline-flex",
                          icon: (l2 = e2.menuItem.item) != null && l2.watchMode ? "Unwatch" : "Watch",
                          size: "md",
                          thickness: "1.5"
                        }, null, 8, ["icon"]),
                        createBaseVNode("span", null, toDisplayString((v3 = e2.menuItem.item) != null && v3.watchMode ? "Disable Watch Mode" : "Enable Watch Mode"), 1)
                      ];
                    }),
                    _: 1
                  }, 512)) : createCommentVNode("", true),
                  E3.value ? createCommentVNode("", true) : (openBlock(), createBlock(unref(c2), {
                    key: 3,
                    class: "flex gap-2",
                    onClick: t3[1] || (t3[1] = (l2) => unref(w4).show())
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(c), {
                        class: "inline-flex",
                        icon: "Delete",
                        size: "md",
                        thickness: "1.5"
                      }),
                      t3[9] || (t3[9] = createBaseVNode("span", null, "Delete", -1))
                    ]),
                    _: 1
                  })),
                  E3.value ? (openBlock(), createBlock(unref(c2), {
                    key: 4,
                    class: "flex gap-2",
                    onClick: t3[2] || (t3[2] = (l2) => unref(C5).show())
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(c), {
                        class: "inline-flex",
                        icon: "Delete",
                        size: "md",
                        thickness: "1.5"
                      }),
                      t3[10] || (t3[10] = createBaseVNode("span", null, "Clear Drafts", -1))
                    ]),
                    _: 1
                  })) : createCommentVNode("", true)
                ];
              }),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["target"])) : createCommentVNode("", true),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(w4),
          title: `Delete ${(i5 = e2.menuItem.item) == null ? void 0 : i5.resourceTitle}`
        }, {
          default: withCtx(() => {
            var a4, o3;
            return [
              createVNode(w2, {
                variableName: ((a4 = e2.menuItem.item) == null ? void 0 : a4.title) ?? "",
                warningMessage: (o3 = e2.menuItem.item) == null ? void 0 : o3.warning,
                onClose: t3[4] || (t3[4] = (l2) => unref(w4).hide()),
                onDelete: F3
              }, null, 8, ["variableName", "warningMessage"])
            ];
          }),
          _: 1
        }, 8, ["state", "title"]),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(I3),
          title: `Edit ${(f4 = e2.menuItem.item) == null ? void 0 : f4.resourceTitle}`
        }, {
          default: withCtx(() => {
            var a4, o3, l2, v3;
            return [
              ((a4 = e2.menuItem.item) == null ? void 0 : a4.resourceTitle) === "Collection" ? (openBlock(), createBlock(k4, {
                key: 0,
                icon: ((o3 = e2.menuItem.item) == null ? void 0 : o3.icon) || "interface-content-folder",
                name: (l2 = e2.menuItem.item) == null ? void 0 : l2.title,
                onClose: t3[5] || (t3[5] = (G3) => unref(I3).hide()),
                onEdit: z2
              }, null, 8, ["icon", "name"])) : (openBlock(), createBlock(C2, {
                key: 1,
                name: ((v3 = e2.menuItem.item) == null ? void 0 : v3.title) ?? "",
                onClose: t3[6] || (t3[6] = (G3) => unref(I3).hide()),
                onEdit: z2
              }, null, 8, ["name"]))
            ];
          }),
          _: 1
        }, 8, ["state", "title"]),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(C5),
          title: "Clear Drafts"
        }, {
          default: withCtx(() => [
            createVNode(w2, {
              variableName: "All Drafts",
              warningMessage: "This action will clear all drafts. This cannot be undone.",
              onClose: t3[7] || (t3[7] = (a4) => unref(C5).hide()),
              onDelete: j
            })
          ]),
          _: 1
        }, 8, ["state"])
      ], 64);
    };
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebarItemMenu.vue.js
var a3 = s2(ge, [["__scopeId", "data-v-7d3c4813"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSection/helpers/getting-started.js
var q3 = (u5, t3, i5) => {
  var n2;
  const s4 = u5.find((m9) => {
    var r3;
    return ((r3 = m9.info) == null ? void 0 : r3.title) === "Drafts";
  }), f4 = t3.length === 1;
  if (!t3[0]) return false;
  const e2 = s4 == null ? void 0 : s4.requests.includes(t3[0]);
  if (!e2) return false;
  const g4 = ((n2 = i5[(s4 == null ? void 0 : s4.requests[0]) ?? ""]) == null ? void 0 : n2.summary) !== "My First Request";
  return f4 && e2 && !g4;
};

// node_modules/@scalar/draggable/dist/store.js
var r2 = ref(null);
var t2 = ref(null);

// node_modules/@scalar/draggable/dist/throttle.js
var o = (e2, r3) => {
  let t3 = false;
  return (...l2) => {
    t3 || (e2(...l2), t3 = true, setTimeout(() => t3 = false, r3));
  };
};

// node_modules/@scalar/draggable/dist/Draggable.vue2.js
var k5 = ["draggable"];
var O = defineComponent({
  __name: "Draggable",
  props: {
    ceiling: { default: 0.8 },
    floor: { default: 0.2 },
    isDraggable: { type: Boolean, default: true },
    isDroppable: { type: [Boolean, Function], default: true },
    parentIds: {},
    id: {}
  },
  emits: ["onDragEnd", "onDragStart"],
  setup(t3, { expose: b, emit: p2 }) {
    const d5 = p2, l2 = computed(() => t3.parentIds.at(-1) ?? null), I3 = (e2) => {
      !e2.dataTransfer || !(e2.target instanceof HTMLElement) || !t3.isDraggable || (e2.target.classList.add("dragging"), e2.dataTransfer.dropEffect = "move", e2.dataTransfer.effectAllowed = "move", r2.value = { id: t3.id, parentId: l2.value }, d5("onDragStart", { id: t3.id, parentId: l2.value }));
    }, h2 = (e2) => typeof t3.isDroppable == "function" ? t3.isDroppable(r2.value, {
      id: t3.id,
      parentId: l2.value,
      offset: e2
    }) : t3.isDroppable, s4 = o((e2) => {
      var g4, c4;
      if (!r2.value || r2.value.id === t3.id || t3.parentIds.includes(((g4 = r2.value) == null ? void 0 : g4.id) ?? ""))
        return;
      const a4 = (c4 = t2.value) == null ? void 0 : c4.offset, o3 = e2.target.offsetHeight, f4 = t3.floor * o3, u5 = t3.ceiling * o3;
      let i5 = 3;
      e2.offsetY <= 0 && a4 && a4 !== 3 ? i5 = a4 : e2.offsetY <= f4 ? i5 = 0 : e2.offsetY >= u5 ? i5 = 1 : e2.offsetY > f4 && e2.offsetY < u5 && (i5 = 2), h2(i5) && (t2.value = { id: t3.id, parentId: l2.value, offset: i5 });
    }, 25), E3 = ["above", "below", "asChild"], S = computed(() => {
      var a4;
      let e2 = "sidebar-indent-nested";
      return t3.id === ((a4 = t2.value) == null ? void 0 : a4.id) && (e2 += ` dragover-${E3[t2.value.offset]}`), e2;
    }), Y2 = () => {
      if (!t2.value || !r2.value) return;
      const e2 = { ...r2.value }, a4 = { ...t2.value };
      r2.value = null, t2.value = null, document.querySelectorAll("div.dragging").forEach((o3) => o3.classList.remove("dragging")), e2.id !== a4.id && d5("onDragEnd", e2, a4);
    };
    return b({
      draggingItem: r2,
      hoveredItem: t2
    }), (e2, a4) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(S.value),
      draggable: e2.isDraggable,
      onDragend: Y2,
      onDragover: a4[0] || (a4[0] = withModifiers(
        //@ts-ignore
        (...o3) => unref(s4) && unref(s4)(...o3),
        ["prevent", "stop"]
      )),
      onDragstart: withModifiers(I3, ["stop"])
    }, [
      renderSlot(e2.$slots, "default", {}, void 0, true)
    ], 42, k5));
  }
});

// node_modules/@scalar/draggable/dist/_virtual/_plugin-vue_export-helper.js
var s3 = (t3, e2) => {
  const o3 = t3.__vccOpts || t3;
  for (const [r3, c4] of e2)
    o3[r3] = c4;
  return o3;
};

// node_modules/@scalar/draggable/dist/Draggable.vue.js
var m6 = s3(O, [["__scopeId", "data-v-3449bf19"]]);

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebarItem.vue2.js
var Te = { class: "line-clamp-1 w-full break-all pl-2 font-medium" };
var ze = { class: "flex flex-row items-center gap-1" };
var Me = { class: "relative" };
var $e = { class: "flex items-start" };
var Ee = ["aria-expanded"];
var Se = { class: "flex flex-1 flex-row justify-between font-medium" };
var Be = { class: "line-clamp-1 w-full break-all text-left" };
var je2 = { class: "relative flex h-fit justify-end" };
var We = { class: "w-content bg-b-1 z-100 text-xxs text-c-1 pointer-events-none z-10 grid max-w-10 gap-1.5 rounded p-2 leading-5 shadow-lg" };
var Ae = { class: "text-c-2 flex items-center" };
var Ne = { class: "text-pretty break-all" };
var Oe = ["aria-expanded"];
var Pe = { class: "flex h-5 max-w-[14px] items-center justify-center" };
var Ve = { class: "flex flex-1 flex-row justify-between" };
var Fe = { class: "line-clamp-1 w-full break-all text-left font-medium" };
var Ye = { class: "relative flex h-fit justify-end" };
var He = { class: "w-content bg-b-1 z-100 text-xxs text-c-1 pointer-events-none z-10 grid max-w-10 gap-1.5 rounded p-2 leading-5 shadow-lg" };
var Le = { class: "text-c-2 flex items-center" };
var Ge = { class: "text-pretty break-all" };
var Ke = { key: 3 };
var P2 = "hover:bg-sidebar-active-b indent-padding-left";
var at = defineComponent({
  __name: "RequestSidebarItem",
  props: {
    isDraggable: { type: Boolean, default: false },
    isDroppable: { type: [Boolean, Function], default: false },
    parentUids: {},
    uid: {},
    menuItem: {}
  },
  emits: ["onDragEnd", "newTab", "openMenu"],
  setup(l2, { emit: se }) {
    useCssVars((e2) => ({
      f1b25d28: re.value,
      "30af9cd2": ue2.value
    }));
    const le = se, { activeCollection: oe, activeRequest: V, activeRouterParams: ae, activeWorkspace: q4 } = F(), {
      collections: T3,
      tags: z2,
      requests: B2,
      requestExamples: j,
      collectionMutators: W2,
      tagMutators: F3,
      requestMutators: A,
      requestExampleMutators: Y2,
      events: H
    } = je(), x3 = useRouter(), { collapsedSidebarFolders: I3, toggleSidebarFolder: L2 } = m3(), { layout: p2 } = s(), i5 = computed(() => {
      var g4, o3, d5, k6, $4, Q4;
      const e2 = T3[l2.uid], t3 = z2[l2.uid], r3 = B2[l2.uid], a4 = j[l2.uid];
      return e2 ? {
        title: ((g4 = e2.info) == null ? void 0 : g4.title) || "Untitled Collection",
        entity: e2,
        resourceTitle: "Collection",
        children: e2.children,
        icon: e2["x-scalar-icon"],
        documentUrl: e2.documentUrl,
        watchMode: e2.watchMode,
        to: e2.uid && ((o3 = e2 == null ? void 0 : e2.info) == null ? void 0 : o3.title) !== "Drafts" ? {
          name: "collection",
          params: {
            [a2.Workspace]: (d5 = q4.value) == null ? void 0 : d5.uid,
            [a2.Collection]: e2.uid
          }
        } : void 0,
        warning: "This cannot be undone. You’re about to delete the collection and all folders and requests inside it.",
        edit: (w4, X2) => {
          W2.edit(e2.uid, "info.title", w4), X2 && W2.edit(e2.uid, "x-scalar-icon", X2);
        },
        delete: () => {
          q4.value && W2.delete(e2, q4.value);
        }
      } : t3 ? {
        title: t3.name,
        entity: t3,
        resourceTitle: "Tag",
        children: t3.children,
        warning: "This cannot be undone. You’re about to delete the tag and all requests inside it",
        edit: (w4) => F3.edit(t3.uid, "name", w4),
        delete: () => l2.parentUids[0] && F3.delete(t3, l2.parentUids[0])
      } : r3 ? {
        title: r3.summary ?? r3.path,
        to: {
          name: "request",
          params: {
            workspace: (k6 = q4.value) == null ? void 0 : k6.uid,
            request: r3.uid
          }
        },
        method: r3.method,
        entity: r3,
        resourceTitle: "Request",
        warning: "This cannot be undone. You’re about to delete the request.",
        children: r3.examples.slice(1),
        edit: (w4) => A.edit(r3.uid, "summary", w4),
        delete: () => l2.parentUids[0] && A.delete(r3, l2.parentUids[0])
      } : a4 != null && a4.requestUid ? {
        title: a4.name,
        to: {
          name: "request.examples",
          params: {
            workspace: ($4 = q4.value) == null ? void 0 : $4.uid,
            request: a4.requestUid,
            examples: a4.uid
          }
        },
        method: (Q4 = B2[a4.requestUid]) == null ? void 0 : Q4.method,
        entity: a4,
        resourceTitle: "Example",
        warning: "This cannot be undone. You’re about to delete the example from the request.",
        children: [],
        edit: (w4) => Y2.edit(a4.uid, "name", w4),
        delete: () => Y2.delete(a4)
      } : {
        title: "Unknown",
        entity: {
          uid: "",
          type: "unknown"
        },
        resourceTitle: "Unknown",
        children: [],
        edit: () => null,
        delete: () => null
      };
    }), M = computed(
      () => i5.value.entity.type === "collection" && i5.value.title === "Drafts"
    ), re = computed(() => l2.parentUids.length ? p2 === "modal" ? `${(l2.parentUids.length - 1) * 12}px` : `${l2.parentUids.length * 12}px` : "12px"), ue2 = computed(() => l2.parentUids.length ? p2 === "modal" ? `${(l2.parentUids.length - 1) * 12}px` : `${l2.parentUids.length * 12}px` : "0px"), de2 = computed(
      () => {
        var e2;
        return I3[l2.uid] || ((e2 = V.value) == null ? void 0 : e2.uid) === l2.uid && i5.value.entity.examples.length > 1;
      }
    ), pe = computed(
      () => {
        var e2;
        return typeof x3.currentRoute.value.name == "string" && x3.currentRoute.value.name.startsWith("request") && ae.value[a2.Request] === "default" && ((e2 = V.value) == null ? void 0 : e2.uid) === l2.uid;
      }
    ), N = ref(null), G3 = computed(() => {
      let e2 = 0.5, t3 = 0.5;
      if (!N.value) return { ceiling: e2, floor: t3 };
      const { draggingItem: r3 } = N.value;
      return !T3[r3 == null ? void 0 : r3.id] && i5.value.entity.type === "collection" ? (e2 = 1, t3 = 0) : i5.value.entity.type === "tag" && (e2 = 0.8, t3 = 0.2), { ceiling: e2, floor: t3 };
    }), ce3 = (e2, t3) => !(p2 === "modal" || j[t3.id] || T3[e2.id]), fe = (e2, t3) => {
      e2 && (u3(["default"]).some((g4) => e2[g4]) ? le("newTab", t3.title || "", t3.entity.uid) : t3.to && x3.push(t3.to), nextTick(() => H.focusAddressBar.emit()));
    };
    function O2(e2) {
      var g4, o3, d5;
      const t3 = l2.parentUids[0] ? ((g4 = T3[l2.parentUids[0]]) == null ? void 0 : g4.uid) || "" : e2, r3 = l2.parentUids[0] && ((o3 = z2[e2]) != null && o3.name) ? { tags: [z2[e2].name] } : {}, a4 = A.add(
        r3,
        t3
      );
      a4 && (x3.push({
        name: "request",
        params: {
          workspace: (d5 = q4.value) == null ? void 0 : d5.uid,
          request: a4.uid
        }
      }), H.hotKeys.emit({
        focusAddressBar: new KeyboardEvent("keydown", { key: "l" })
      }));
    }
    const K2 = computed(() => {
      const { uid: e2, watchModeStatus: t3 } = oe.value || {};
      return e2 !== i5.value.entity.uid ? "text-c-3" : t3 === "WATCHING" ? "text-c-1" : t3 === "ERROR" ? "text-red" : "text-c-3";
    }), J3 = computed(() => i5.value.title === "Drafts" && p2 !== "modal" && i5.value.children.length > 0), me = computed(() => {
      const e2 = B2[l2.uid];
      if (e2) return !shouldIgnoreEntity(e2);
      const t3 = z2[l2.uid];
      return t3 ? !shouldIgnoreEntity(t3) : true;
    });
    return (e2, t3) => {
      const r3 = resolveComponent("RequestSidebarItem", true);
      return me.value ? (openBlock(), createElementBlock("li", {
        key: 0,
        class: normalizeClass(["relative flex flex-row", [
          unref(p2) === "modal" && e2.parentUids.length > 1 || unref(p2) !== "modal" && e2.parentUids.length ? "before:bg-border indent-border-line-offset before:z-1 mb-[.5px] before:pointer-events-none before:absolute before:left-[calc(.75rem_+_.5px)] before:top-0 before:h-[calc(100%_+_.5px)] before:w-[.5px] last:mb-0 last:before:h-full" : ""
        ]])
      }, [
        createVNode(unref(m6), {
          id: i5.value.entity.uid,
          ref_key: "draggableRef",
          ref: N,
          ceiling: G3.value.ceiling,
          class: "gap-1/2 flex flex-1 flex-col text-sm",
          floor: G3.value.floor,
          isDraggable: e2.isDraggable,
          isDroppable: e2.isDroppable,
          parentIds: e2.parentUids,
          onOnDragEnd: t3[13] || (t3[13] = (...a4) => e2.$emit("onDragEnd", ...a4))
        }, {
          default: withCtx(() => {
            var a4, g4;
            return [
              (i5.value.entity.type === "request" || i5.value.entity.type === "requestExample") && i5.value.to ? (openBlock(), createBlock(unref(RouterLink), {
                key: 0,
                class: "group no-underline",
                to: i5.value.to,
                onClick: t3[1] || (t3[1] = withModifiers(
                  (o3) => fe(o3, i5.value),
                  ["prevent"]
                ))
              }, {
                default: withCtx(({ isExactActive: o3 }) => {
                  var d5, k6;
                  return [
                    createBaseVNode("div", {
                      class: normalizeClass(["relative flex min-h-8 w-full cursor-pointer flex-row items-start justify-between gap-0.5 rounded py-1.5 pr-2", [
                        P2,
                        o3 || pe.value ? "bg-sidebar-active-b text-sidebar-active-c transition-none" : "text-sidebar-c-2"
                      ]])
                    }, [
                      createBaseVNode("span", Te, toDisplayString(i5.value.title || "Untitled"), 1),
                      createBaseVNode("div", ze, [
                        createBaseVNode("div", Me, [
                          unref(p2) !== "modal" ? (openBlock(), createBlock(unref($), {
                            key: 0,
                            class: normalizeClass(["hover:bg-b-3 hidden aspect-square h-fit px-0.5 py-0 opacity-0 group-hover:flex group-hover:opacity-100 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100", {
                              flex: ((k6 = (d5 = e2.menuItem) == null ? void 0 : d5.item) == null ? void 0 : k6.entity.uid) === i5.value.entity.uid && e2.menuItem.open
                            }]),
                            size: "sm",
                            type: "button",
                            variant: "ghost",
                            onClick: t3[0] || (t3[0] = withModifiers(
                              ($4) => e2.$emit("openMenu", {
                                item: i5.value,
                                parentUids: e2.parentUids,
                                targetRef: $4.currentTarget,
                                open: !e2.menuItem.open
                              }),
                              ["stop", "prevent"]
                            ))
                          }, {
                            default: withCtx(() => [
                              createVNode(unref(c), {
                                icon: "Ellipses",
                                size: "md"
                              })
                            ]),
                            _: 1
                          }, 8, ["class"])) : createCommentVNode("", true)
                        ]),
                        createBaseVNode("span", $e, [
                          t3[14] || (t3[14] = createTextVNode("   ")),
                          t3[15] || (t3[15] = createBaseVNode("span", { class: "sr-only" }, "HTTP Method:", -1)),
                          i5.value.method ? (openBlock(), createBlock(unref(e), {
                            key: 0,
                            class: "font-bold",
                            method: i5.value.method
                          }, null, 8, ["method"])) : createCommentVNode("", true)
                        ])
                      ])
                    ], 2)
                  ];
                }),
                _: 1
              }, 8, ["to"])) : (unref(p2) !== "modal" || e2.parentUids.length) && i5.value.entity.type === "collection" ? (openBlock(), createElementBlock("div", {
                key: 1,
                "aria-expanded": !!unref(I3)[i5.value.entity.uid],
                class: normalizeClass(["hover:bg-b-2 group relative flex w-full flex-row justify-start gap-1.5 rounded p-1.5 focus-visible:z-10", [
                  P2,
                  {
                    "bg-sidebar-active-b text-sidebar-active-c transition-none": typeof unref(x3).currentRoute.value.name == "string" && unref(x3).currentRoute.value.name.startsWith("collection") && unref(x3).currentRoute.value.params[unref(a2).Collection] === i5.value.entity.uid,
                    "text-c-2": i5.value.title === "Untitled Collection"
                  }
                ]])
              }, [
                createBaseVNode("span", {
                  class: "flex h-5 max-w-[14px] cursor-pointer items-center justify-center",
                  onClick: t3[2] || (t3[2] = (o3) => unref(L2)(i5.value.entity.uid))
                }, [
                  renderSlot(e2.$slots, "leftIcon", {}, () => [
                    createVNode(unref(k), {
                      class: "text-c-3 shrink-0",
                      open: !!unref(I3)[i5.value.entity.uid]
                    }, null, 8, ["open"])
                  ], true),
                  t3[16] || (t3[16] = createTextVNode("   "))
                ]),
                createBaseVNode("div", Se, [
                  i5.value.to ? (openBlock(), createBlock(unref(RouterLink), {
                    key: 0,
                    class: "w-full no-underline",
                    to: i5.value.to,
                    onClick: t3[3] || (t3[3] = withModifiers(() => {
                    }, ["stop", "prevent"]))
                  }, {
                    default: withCtx(() => [
                      createBaseVNode("span", Be, toDisplayString(i5.value.title), 1)
                    ]),
                    _: 1
                  }, 8, ["to"])) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                    createTextVNode(toDisplayString(i5.value.title), 1)
                  ], 64)),
                  createBaseVNode("div", je2, [
                    createBaseVNode("div", {
                      class: normalizeClass(["items-center gap-px opacity-0 group-hover:flex group-hover:opacity-100 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100", {
                        flex: e2.menuItem.open,
                        hidden: !e2.menuItem.open || ((a4 = e2.menuItem.item) == null ? void 0 : a4.entity.uid) !== i5.value.entity.uid
                      }])
                    }, [
                      unref(p2) !== "modal" && !M.value || M.value && J3.value ? (openBlock(), createBlock(unref($), {
                        key: 0,
                        class: "hover:bg-b-3 hover:text-c-1 aspect-square h-fit px-0.5 py-0 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100",
                        size: "sm",
                        variant: "ghost",
                        onClick: t3[4] || (t3[4] = withModifiers(
                          (o3) => e2.$emit("openMenu", {
                            item: i5.value,
                            parentUids: e2.parentUids,
                            targetRef: o3.currentTarget.parentNode,
                            open: true
                          }),
                          ["stop", "prevent"]
                        ))
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            icon: "Ellipses",
                            size: "md"
                          })
                        ]),
                        _: 1
                      })) : createCommentVNode("", true),
                      unref(p2) !== "modal" ? (openBlock(), createBlock(unref($), {
                        key: 1,
                        class: "hover:bg-b-3 hover:text-c-1 aspect-square h-fit px-0.5 py-0 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100",
                        size: "sm",
                        variant: "ghost",
                        onClick: t3[5] || (t3[5] = withModifiers((o3) => O2(i5.value.entity.uid), ["stop", "prevent"]))
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            icon: "Add",
                            size: "md",
                            thickness: "2"
                          })
                        ]),
                        _: 1
                      })) : createCommentVNode("", true)
                    ], 2),
                    i5.value.watchMode ? (openBlock(), createBlock(unref(T), {
                      key: 0,
                      side: "right",
                      sideOffset: 12
                    }, {
                      trigger: withCtx(() => [
                        createVNode(unref(c), {
                          class: normalizeClass(["ml-0.5 text-sm", K2.value]),
                          icon: "Watch",
                          size: "md",
                          thickness: "2"
                        }, null, 8, ["class"])
                      ]),
                      content: withCtx(() => [
                        createBaseVNode("div", We, [
                          createBaseVNode("div", Ae, [
                            createBaseVNode("p", Ne, " Watching: " + toDisplayString(i5.value.documentUrl), 1)
                          ])
                        ])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    t3[17] || (t3[17] = createBaseVNode("span", null, " ", -1))
                  ])
                ])
              ], 10, Ee)) : unref(p2) !== "modal" || e2.parentUids.length ? (openBlock(), createElementBlock("button", {
                key: 2,
                "aria-expanded": !!unref(I3)[i5.value.entity.uid],
                class: normalizeClass(["hover:bg-b-2 group relative flex w-full flex-row justify-start gap-1.5 rounded p-1.5 focus-visible:z-10", [P2]]),
                type: "button",
                onClick: t3[8] || (t3[8] = (o3) => unref(L2)(i5.value.entity.uid))
              }, [
                createBaseVNode("span", Pe, [
                  renderSlot(e2.$slots, "leftIcon", {}, () => [
                    createVNode(unref(k), {
                      class: "text-c-3 hover:text-c-1 shrink-0",
                      open: !!unref(I3)[i5.value.entity.uid]
                    }, null, 8, ["open"])
                  ], true),
                  t3[18] || (t3[18] = createTextVNode("   "))
                ]),
                createBaseVNode("div", Ve, [
                  createBaseVNode("span", Fe, toDisplayString(i5.value.title), 1),
                  createBaseVNode("div", Ye, [
                    createBaseVNode("div", {
                      class: normalizeClass(["items-center gap-px opacity-0 group-hover:flex group-hover:opacity-100 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100", {
                        flex: e2.menuItem.open,
                        hidden: !e2.menuItem.open || ((g4 = e2.menuItem.item) == null ? void 0 : g4.entity.uid) !== i5.value.entity.uid
                      }])
                    }, [
                      unref(p2) !== "modal" && !M.value || M.value && J3.value ? (openBlock(), createBlock(unref($), {
                        key: 0,
                        class: "hover:bg-b-3 hover:text-c-1 aspect-square h-fit px-0.5 py-0 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100",
                        size: "sm",
                        variant: "ghost",
                        onClick: t3[6] || (t3[6] = withModifiers(
                          (o3) => e2.$emit("openMenu", {
                            item: i5.value,
                            parentUids: e2.parentUids,
                            targetRef: o3.currentTarget.parentNode,
                            open: true
                          }),
                          ["stop", "prevent"]
                        ))
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            icon: "Ellipses",
                            size: "md"
                          })
                        ]),
                        _: 1
                      })) : createCommentVNode("", true),
                      unref(p2) !== "modal" ? (openBlock(), createBlock(unref($), {
                        key: 1,
                        class: "hover:bg-b-3 hover:text-c-1 aspect-square h-fit px-0.5 py-0 group-focus-visible:opacity-100 group-has-[:focus-visible]:opacity-100",
                        size: "sm",
                        variant: "ghost",
                        onClick: t3[7] || (t3[7] = withModifiers((o3) => O2(i5.value.entity.uid), ["stop", "prevent"]))
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            icon: "Add",
                            size: "md",
                            thickness: "2"
                          })
                        ]),
                        _: 1
                      })) : createCommentVNode("", true)
                    ], 2),
                    i5.value.watchMode ? (openBlock(), createBlock(unref(T), {
                      key: 0,
                      side: "right",
                      sideOffset: 12
                    }, {
                      trigger: withCtx(() => [
                        createVNode(unref(c), {
                          class: normalizeClass(["ml-0.5 text-sm", K2.value]),
                          icon: "Watch",
                          size: "md",
                          thickness: "2"
                        }, null, 8, ["class"])
                      ]),
                      content: withCtx(() => [
                        createBaseVNode("div", He, [
                          createBaseVNode("div", Le, [
                            createBaseVNode("p", Ge, " Watching: " + toDisplayString(i5.value.documentUrl), 1)
                          ])
                        ])
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    t3[19] || (t3[19] = createBaseVNode("span", null, " ", -1))
                  ])
                ])
              ], 10, Oe)) : createCommentVNode("", true),
              de2.value ? (openBlock(), createElementBlock("ul", Ke, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(i5.value.children, (o3) => (openBlock(), createBlock(r3, {
                  key: o3,
                  isDraggable: !unref(j)[o3],
                  isDroppable: ce3,
                  menuItem: e2.menuItem,
                  parentUids: [...e2.parentUids, e2.uid],
                  uid: o3,
                  onNewTab: t3[9] || (t3[9] = (d5, k6) => e2.$emit("newTab", d5, k6)),
                  onOnDragEnd: t3[10] || (t3[10] = (...d5) => e2.$emit("onDragEnd", ...d5)),
                  onOpenMenu: t3[11] || (t3[11] = (d5) => e2.$emit("openMenu", d5))
                }, null, 8, ["isDraggable", "menuItem", "parentUids", "uid"]))), 128)),
                i5.value.children.length === 0 ? (openBlock(), createBlock(unref($), {
                  key: 0,
                  class: normalizeClass(["text-c-1 hover:bg-b-2 flex h-8 w-full justify-start gap-1.5 py-0 text-xs", e2.parentUids.length ? "pl-9" : ""]),
                  variant: "ghost",
                  onClick: t3[12] || (t3[12] = (o3) => O2(i5.value.entity.uid))
                }, {
                  default: withCtx(() => [
                    createVNode(unref(c), {
                      icon: "Add",
                      size: "sm"
                    }),
                    t3[20] || (t3[20] = createBaseVNode("span", null, "Add Request", -1))
                  ]),
                  _: 1
                }, 8, ["class"])) : createCommentVNode("", true)
              ])) : createCommentVNode("", true)
            ];
          }),
          _: 3
        }, 8, ["id", "ceiling", "floor", "isDraggable", "isDroppable", "parentIds"])
      ], 2)) : createCommentVNode("", true);
    };
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebarItem.vue.js
var p = s2(at, [["__scopeId", "data-v-716d294a"]]);

// node_modules/@scalar/api-client/dist/views/Request/components/WorkspaceDropdown.vue.js
var K = { class: "flex w-[inherit] items-center text-sm" };
var Q3 = { class: "m-0 flex items-center gap-1.5 font-bold" };
var X = { class: "line-clamp-1 text-left" };
var Z = { class: "overflow-hidden text-ellipsis" };
var ee = { class: "flex h-4 w-4 items-center justify-center" };
var de = defineComponent({
  __name: "WorkspaceDropdown",
  setup(te) {
    const { activeWorkspace: m9 } = F(), { workspaces: d5, workspaceMutators: b, events: T3 } = je(), { push: _4 } = useRouter(), j = (s4) => {
      var e2;
      s4 !== ((e2 = m9.value) == null ? void 0 : e2.uid) && _4({
        name: "workspace",
        params: {
          workspace: s4
        }
      });
    }, y = computed(() => Object.keys(d5).length === 1), B2 = () => T3.commandPalette.emit({ commandName: "Create Workspace" }), f4 = ref(""), i5 = ref(""), v3 = E(), w4 = E(), M = (s4) => {
      const e2 = d5[s4];
      e2 && (f4.value = e2.name, i5.value = s4, v3.show());
    }, R = (s4) => {
      s4.trim() && (b.edit(i5.value, "name", s4.trim()), v3.hide());
    }, W2 = (s4) => {
      const e2 = d5[s4];
      e2 && (f4.value = e2.name, i5.value = s4, w4.show());
    }, A = async () => {
      var s4;
      if (!y.value) {
        const e2 = ((s4 = m9.value) == null ? void 0 : s4.uid) === i5.value, l2 = { ...d5 };
        if (delete l2[i5.value], b.delete(i5.value), e2) {
          const k6 = Object.keys(l2)[0];
          await _4({
            name: "workspace",
            params: {
              workspace: k6
            }
          });
        }
      }
      w4.hide();
    };
    return (s4, e2) => (openBlock(), createElementBlock("div", null, [
      createBaseVNode("div", K, [
        createVNode(unref(x), null, {
          items: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(d5), (l2, k6) => (openBlock(), createBlock(unref(m), {
              key: k6,
              class: "group/item flex w-full items-center gap-1.5 overflow-hidden text-ellipsis whitespace-nowrap",
              onClick: withModifiers((x3) => j(l2.uid), ["stop"])
            }, {
              default: withCtx(() => {
                var x3;
                return [
                  createVNode(unref(u), {
                    selected: ((x3 = unref(m9)) == null ? void 0 : x3.uid) === k6
                  }, null, 8, ["selected"]),
                  createBaseVNode("span", Z, toDisplayString(l2.name), 1),
                  createVNode(unref(x), {
                    placement: "right-start",
                    teleport: ""
                  }, {
                    items: withCtx(() => [
                      createVNode(unref(m), {
                        class: "flex gap-2",
                        onMousedown: (h2) => M(l2.uid),
                        onTouchend: withModifiers((h2) => M(l2.uid), ["prevent"])
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            class: "inline-flex",
                            icon: "Edit",
                            size: "md",
                            thickness: "1.5"
                          }),
                          e2[4] || (e2[4] = createBaseVNode("span", null, "Rename", -1))
                        ]),
                        _: 2
                      }, 1032, ["onMousedown", "onTouchend"]),
                      y.value ? (openBlock(), createBlock(unref(T), {
                        key: 0,
                        class: "z-overlay",
                        side: "bottom"
                      }, {
                        trigger: withCtx(() => [
                          createVNode(unref(m), {
                            class: "flex w-full gap-2",
                            disabled: "",
                            onMousedown: e2[0] || (e2[0] = withModifiers(() => {
                            }, ["prevent"])),
                            onTouchend: e2[1] || (e2[1] = withModifiers(() => {
                            }, ["prevent"]))
                          }, {
                            default: withCtx(() => [
                              createVNode(unref(c), {
                                class: "inline-flex",
                                icon: "Delete",
                                size: "md",
                                thickness: "1.5"
                              }),
                              e2[5] || (e2[5] = createBaseVNode("span", null, "Delete", -1))
                            ]),
                            _: 1
                          })
                        ]),
                        content: withCtx(() => e2[6] || (e2[6] = [
                          createBaseVNode("div", { class: "w-content bg-b-1 text-xxs text-c-1 pointer-events-none z-10 grid min-w-48 gap-1.5 rounded p-2 leading-5 shadow-lg" }, [
                            createBaseVNode("div", { class: "text-c-2 flex items-center" }, [
                              createBaseVNode("span", null, "Only workspace cannot be deleted.")
                            ])
                          ], -1)
                        ])),
                        _: 1
                      })) : (openBlock(), createBlock(unref(m), {
                        key: 1,
                        class: "flex !gap-2",
                        onMousedown: withModifiers((h2) => W2(l2.uid), ["prevent"]),
                        onTouchend: withModifiers((h2) => W2(l2.uid), ["prevent"])
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            class: "inline-flex",
                            icon: "Delete",
                            size: "sm",
                            thickness: "1.5"
                          }),
                          e2[7] || (e2[7] = createBaseVNode("span", null, "Delete", -1))
                        ]),
                        _: 2
                      }, 1032, ["onMousedown", "onTouchend"]))
                    ]),
                    default: withCtx(() => [
                      createVNode(unref($), {
                        class: "hover:bg-b-3 -mr-1 ml-auto aspect-square h-fit px-0.5 py-0 group-hover/item:flex",
                        size: "sm",
                        type: "button",
                        variant: "ghost"
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(c), {
                            icon: "Ellipses",
                            size: "sm"
                          })
                        ]),
                        _: 1
                      })
                    ]),
                    _: 2
                  }, 1024)
                ];
              }),
              _: 2
            }, 1032, ["onClick"]))), 128)),
            createVNode(unref(u2)),
            createVNode(unref(m), {
              class: "flex items-center gap-1.5",
              onClick: B2
            }, {
              default: withCtx(() => [
                createBaseVNode("div", ee, [
                  createVNode(unref(c), {
                    icon: "Add",
                    size: "sm"
                  })
                ]),
                e2[8] || (e2[8] = createBaseVNode("span", null, "Create Workspace", -1))
              ]),
              _: 1
            })
          ]),
          default: withCtx(() => [
            createVNode(unref($), {
              class: "text-c-1 hover:bg-b-2 line-clamp-1 h-full w-fit justify-start px-1.5 py-1.5 font-normal",
              fullWidth: "",
              variant: "ghost"
            }, {
              default: withCtx(() => {
                var l2;
                return [
                  createBaseVNode("div", Q3, [
                    createBaseVNode("h2", X, toDisplayString((l2 = unref(m9)) == null ? void 0 : l2.name), 1)
                  ])
                ];
              }),
              _: 1
            })
          ]),
          _: 1
        })
      ]),
      createVNode(unref(f), {
        size: "xxs",
        state: unref(w4),
        title: "Delete workspace"
      }, {
        default: withCtx(() => [
          createVNode(w2, {
            variableName: f4.value,
            warningMessage: "This cannot be undone. You’re about to delete the workspace and everything inside it.",
            onClose: e2[2] || (e2[2] = (l2) => unref(w4).hide()),
            onDelete: A
          }, null, 8, ["variableName"])
        ]),
        _: 1
      }, 8, ["state"]),
      createVNode(unref(f), {
        size: "xxs",
        state: unref(v3),
        title: "Rename Workspace"
      }, {
        default: withCtx(() => [
          createVNode(C2, {
            name: f4.value,
            onClose: e2[3] || (e2[3] = (l2) => unref(v3).hide()),
            onEdit: R
          }, null, 8, ["name"])
        ]),
        _: 1
      }, 8, ["state"])
    ]));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebar.vue2.js
var Je = { class: "bg-b-1 sticky top-0 z-20 flex h-12 items-center px-3" };
var Qe = {
  key: 1,
  class: "text-c-3"
};
var Xe = ["aria-pressed"];
var Ye2 = { class: "sr-only" };
var Ze = {
  class: "search-button-fade sticky top-12 z-10 px-3 py-2.5 pt-0 focus-within:z-20",
  role: "search"
};
var et = {
  key: 1,
  class: "contents"
};
var tt = { class: "empty-sidebar-item-content px-2.5 py-2.5" };
var ot = { class: "rabbit-ascii relative m-auto mt-2 h-[68px] w-[60px]" };
var $t = defineComponent({
  __name: "RequestSidebar",
  emits: ["newTab", "clearDrafts"],
  setup(st, { emit: J3 }) {
    const Q4 = J3, {
      collapsedSidebarFolders: X2,
      isSidebarOpen: P3,
      setCollapsedSidebarFolder: Y2,
      toggleSidebarOpen: Z2
    } = m3(), { layout: i5 } = s(), T3 = je(), {
      activeWorkspaceCollections: c4,
      activeRequest: ee2,
      activeWorkspaceRequests: _4,
      activeWorkspace: te
    } = F(), { findRequestParents: oe, events: R, requestMutators: V, requests: y } = T3, { handleDragEnd: se, isDroppable: ae } = F2(
      te,
      T3
    ), { replace: E3 } = useRouter(), re = () => {
      R.commandPalette.emit({
        commandName: "Import from OpenAPI/Swagger/Postman/cURL"
      });
    }, L2 = useId(), { toast: le } = i(), S = reactive({ open: false }), m9 = ref(false);
    watch(
      ee2,
      (s4) => {
        s4 && oe(s4).forEach(
          (e2) => Y2(e2, true)
        );
      },
      { immediate: true }
    );
    const {
      searchText: h2,
      searchResultsWithPlaceholderResults: x3,
      selectedSearchResult: w4,
      onSearchResultClick: ne,
      fuseSearch: ie,
      searchInputRef: I3,
      searchResultRefs: ue2,
      navigateSearchResults: N,
      selectSearchResult: ce3
    } = Q2(), O2 = (s4) => {
      var e2;
      s4 && (s4.toggleSidebar && Z2(), s4.focusRequestSearch && ((e2 = I3.value) == null || e2.focus()));
    };
    onMounted(() => R.hotKeys.on(O2)), onBeforeUnmount(() => {
      R.hotKeys.off(O2);
    });
    const de2 = (s4) => {
      if (s4 != null && s4.documentUrl) {
        s4.watchMode = !s4.watchMode;
        const e2 = c4.value.find(
          (o3) => o3.uid === s4.entity.uid
        );
        e2 && (e2.watchMode = s4.watchMode);
      }
    };
    watch(
      () => c4.value.map(
        (s4) => s4.watchMode
      ),
      (s4, e2) => {
        s4.forEach((o3, a4) => {
          var r3, f4, z2;
          if (i5 !== "modal" && o3 !== e2[a4] && ((f4 = (r3 = c4.value[a4]) == null ? void 0 : r3.info) == null ? void 0 : f4.title) !== "Drafts" && c4.value[a4]) {
            const F3 = c4.value[a4];
            if (!F3) return;
            const he = `${(z2 = F3.info) == null ? void 0 : z2.title}: Watch Mode ${o3 ? "enabled" : "disabled"}`;
            le(he, "info");
          }
        });
      }
    );
    const me = computed(() => {
      var f4;
      const s4 = x3.value;
      if (!s4.length) return "No results found";
      const e2 = (f4 = s4[w4.value]) == null ? void 0 : f4.item;
      if (!e2) return "No result selected";
      const o3 = h2.value.length ? `${s4.length} result${s4.length === 1 ? "" : "s"} found, ` : "", a4 = `, HTTP Method ${e2.httpVerb}, Path ${e2.path}`, r3 = `${e2.title} ${a4}`;
      return `${o3}Selected: ${r3}`;
    }), fe = () => {
      const s4 = c4.value.find(
        (o3) => {
          var a4;
          return ((a4 = o3.info) == null ? void 0 : a4.title) === "Drafts";
        }
      );
      if (s4 && s4.requests.forEach((o3) => {
        y[o3] && V.delete(y[o3], s4.uid);
      }), _4.value.length) {
        const o3 = c4.value[0], a4 = o3 == null ? void 0 : o3.requests[0];
        a4 && E3({
          name: "request",
          params: {
            [a2.Request]: a4
          }
        });
      } else {
        const { request: o3 } = Q();
        s4 && (V.add(o3, s4.uid), E3({
          name: "request",
          params: {
            [a2.Request]: o3.uid
          }
        }));
      }
    }, pe = () => {
      m9.value = !m9.value, m9.value || (h2.value = ""), m9.value && nextTick(() => {
        var s4;
        return (s4 = I3.value) == null ? void 0 : s4.focus();
      });
    }, C5 = computed(
      () => q3(
        c4.value,
        _4.value,
        y
      )
    );
    return (s4, e2) => (openBlock(), createElementBlock(Fragment, null, [
      withDirectives(createVNode(unref(m4), {
        class: normalizeClass([unref(P3) ? "sidebar-active-width" : ""])
      }, createSlots({
        content: withCtx(() => [
          createBaseVNode("div", Je, [
            createBaseVNode("div", {
              class: normalizeClass(["size-8", { "xl:hidden": unref(i5) !== "modal" }])
            }, null, 2),
            unref(i5) !== "modal" ? (openBlock(), createBlock(unref(de), { key: 0 })) : createCommentVNode("", true),
            unref(i5) !== "modal" ? (openBlock(), createElementBlock("span", Qe, " / ")) : createCommentVNode("", true),
            unref(i5) !== "modal" ? (openBlock(), createBlock(Y, { key: 2 })) : createCommentVNode("", true),
            createBaseVNode("button", {
              "aria-pressed": m9.value,
              class: "ml-auto",
              type: "button",
              onClick: pe
            }, [
              createBaseVNode("span", Ye2, toDisplayString(m9.value ? "Hide" : "Show") + " search ", 1),
              createVNode(unref(c), {
                class: "text-c-3 hover:bg-b-2 p-1.75 max-h-8 max-w-8 rounded-lg text-sm",
                icon: "Search"
              })
            ], 8, Xe)
          ]),
          withDirectives(createBaseVNode("div", Ze, [
            createVNode(unref(c3), {
              ref_key: "searchInputRef",
              ref: I3,
              modelValue: unref(h2),
              "onUpdate:modelValue": e2[0] || (e2[0] = (o3) => isRef(h2) ? h2.value = o3 : null),
              "aria-controls": unref(L2),
              label: me.value,
              sidebar: "",
              onInput: unref(ie),
              onKeydown: [
                e2[1] || (e2[1] = withKeys(withModifiers((o3) => unref(N)("down"), ["stop"]), ["down"])),
                e2[2] || (e2[2] = withKeys(withModifiers((o3) => unref(ce3)(), ["stop"]), ["enter"])),
                e2[3] || (e2[3] = withKeys(withModifiers((o3) => unref(N)("up"), ["stop"]), ["up"]))
              ]
            }, null, 8, ["modelValue", "aria-controls", "label", "onInput"])
          ], 512), [
            [vShow, m9.value]
          ]),
          createBaseVNode("div", {
            class: normalizeClass(["gap-1/2 flex flex-1 flex-col overflow-visible overflow-y-auto px-3 pb-3 pt-0", [
              {
                "pb-14": unref(i5) !== "modal"
              },
              {
                "h-[calc(100%-273.5px)]": C5.value
              }
            ]]),
            onDragenter: e2[6] || (e2[6] = withModifiers(() => {
            }, ["prevent"])),
            onDragover: e2[7] || (e2[7] = withModifiers(() => {
            }, ["prevent"]))
          }, [
            unref(h2) ? (openBlock(), createBlock(unref(x2), {
              key: 0,
              id: unref(L2),
              "aria-label": "Search Results",
              class: "gap-px",
              noResults: !unref(x3).length
            }, {
              default: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(x3), (o3, a4) => (openBlock(), createBlock(unref(C), {
                  id: `#search-input-${o3.item.id}`,
                  key: o3.refIndex,
                  ref_for: true,
                  ref: (r3) => unref(ue2)[a4] = r3,
                  active: unref(w4) === a4,
                  class: "px-2",
                  href: o3.item.link,
                  onClick: withModifiers((r3) => unref(ne)(o3), ["prevent"]),
                  onFocus: (r3) => w4.value = a4
                }, {
                  addon: withCtx(() => [
                    e2[9] || (e2[9] = createBaseVNode("span", { class: "sr-only" }, "HTTP Method:", -1)),
                    createVNode(e, {
                      class: "font-bold",
                      method: o3.item.httpVerb ?? "get"
                    }, null, 8, ["method"])
                  ]),
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(o3.item.title) + " ", 1)
                  ]),
                  _: 2
                }, 1032, ["id", "active", "href", "onClick", "onFocus"]))), 128))
              ]),
              _: 1
            }, 8, ["id", "noResults"])) : (openBlock(), createElementBlock("nav", et, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(c4), (o3) => {
                var a4;
                return openBlock(), createBlock(p, {
                  key: o3.uid,
                  isDraggable: unref(i5) !== "modal" && ((a4 = o3.info) == null ? void 0 : a4.title) !== "Drafts",
                  isDroppable: unref(ae),
                  menuItem: S,
                  parentUids: [],
                  uid: o3.uid,
                  onNewTab: e2[4] || (e2[4] = (r3, f4) => Q4("newTab", { name: r3, uid: f4 })),
                  onOnDragEnd: unref(se),
                  onOpenMenu: e2[5] || (e2[5] = (r3) => Object.assign(S, r3))
                }, {
                  leftIcon: withCtx(() => {
                    var r3;
                    return [
                      ((r3 = o3.info) == null ? void 0 : r3.title) === "Drafts" ? (openBlock(), createBlock(unref(c), {
                        key: 0,
                        class: "text-sidebar-c-2 group-hover:hidden",
                        icon: "Scribble",
                        thickness: "2.25"
                      })) : (openBlock(), createBlock(unref(d), {
                        key: 1,
                        class: "text-sidebar-c-2 size-3.5 min-w-3.5 stroke-2 group-hover:hidden",
                        src: o3["x-scalar-icon"] || "interface-content-folder"
                      }, null, 8, ["src"])),
                      createBaseVNode("div", {
                        class: normalizeClass({
                          "rotate-90": unref(X2)[o3.uid]
                        })
                      }, [
                        createVNode(unref(c), {
                          class: "text-c-3 hover:text-c-1 hidden text-sm group-hover:block",
                          icon: "ChevronRight",
                          size: "md"
                        })
                      ], 2)
                    ];
                  }),
                  _: 2
                }, 1032, ["isDraggable", "isDroppable", "menuItem", "uid", "onOnDragEnd"]);
              }), 128))
            ]))
          ], 34)
        ]),
        button: withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass({
              "empty-sidebar-item": C5.value
            })
          }, [
            createBaseVNode("div", tt, [
              createBaseVNode("div", ot, [
                createVNode(i2, {
                  art: unref(_2),
                  class: "rabbitsit font-bold"
                }, null, 8, ["art"]),
                createVNode(i2, {
                  art: unref(_3),
                  class: "rabbitjump absolute left-0 top-0 font-bold"
                }, null, 8, ["art"])
              ]),
              e2[10] || (e2[10] = createBaseVNode("div", { class: "mb-2 mt-2 text-balance text-center text-sm" }, [
                createBaseVNode("b", { class: "font-medium" }, "Let's Get Started"),
                createBaseVNode("p", { class: "mt-2" }, " Create request, folder, collection or import from OpenAPI/Postman ")
              ], -1))
            ]),
            unref(i5) !== "modal" ? (openBlock(), createBlock(unref($), {
              key: 0,
              class: normalizeClass(["mb-1.5 hidden h-fit w-full p-1.5 opacity-0", {
                "flex opacity-100": C5.value
              }]),
              onClick: re
            }, {
              default: withCtx(() => e2[11] || (e2[11] = [
                createTextVNode(" Import Collection ")
              ])),
              _: 1
            }, 8, ["class"])) : createCommentVNode("", true),
            unref(i5) !== "modal" ? (openBlock(), createBlock(g, {
              key: 1,
              click: unref(R).commandPalette.emit,
              hotkey: "K"
            }, {
              title: withCtx(() => e2[12] || (e2[12] = [
                createTextVNode(" Add Item ")
              ])),
              _: 1
            }, 8, ["click"])) : createCommentVNode("", true)
          ], 2)
        ]),
        _: 2
      }, [
        unref(i5) !== "modal" ? {
          name: "header",
          fn: withCtx(() => []),
          key: "0"
        } : void 0
      ]), 1032, ["class"]), [
        [vShow, unref(P3)]
      ]),
      unref(i5) !== "modal" && S ? (openBlock(), createBlock(a3, {
        key: 0,
        menuItem: S,
        onClearDrafts: fe,
        onCloseMenu: e2[8] || (e2[8] = (o3) => S.open = false),
        onToggleWatchMode: de2
      }, null, 8, ["menuItem"])) : createCommentVNode("", true)
    ], 64));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestSidebar.vue.js
var d3 = s2($t, [["__scopeId", "data-v-8def2cd7"]]);

// node_modules/@scalar/api-client/dist/libs/normalize-headers.js
var f3 = (a4, t3 = false) => {
  var c4, l2;
  const o3 = Object.fromEntries(a4);
  t3 && [
    "Access-Control-Allow-Credentials",
    "Access-Control-Allow-Headers",
    "Access-Control-Allow-Methods",
    "Access-Control-Allow-Origin",
    "Access-Control-Expose-Headers"
  ].map((s4) => s4.toLowerCase()).forEach((s4) => delete o3[s4]);
  const r3 = Object.keys(o3).find((e2) => e2.toLowerCase() === "x-scalar-modified-headers"), i5 = r3 ? ((l2 = (c4 = o3[r3]) == null ? void 0 : c4.toString().split(", ")) == null ? void 0 : l2.map((e2) => e2.toLowerCase())) ?? [] : [];
  return Object.keys(o3).forEach((e2) => {
    i5.includes(e2.toLowerCase()) && delete o3[e2];
  }), r3 && delete o3[r3], Object.keys(o3).forEach((e2) => {
    const s4 = /^x-scalar-original-/i;
    if (s4.test(e2)) {
      const n2 = e2.replace(s4, "");
      o3[e2] && (o3[n2] = o3[e2], delete o3[e2]);
    }
  }), Object.keys(o3).forEach((e2) => {
    const s4 = d4(e2);
    e2 !== s4 && o3[e2] && (o3[s4] = o3[e2], delete o3[e2]);
  }), Object.fromEntries(Object.entries(o3).sort(([e2], [s4]) => e2.localeCompare(s4)));
};
var d4 = (a4) => a4.split("-").map((t3) => t3.charAt(0).toUpperCase() + t3.toLowerCase().slice(1)).join("-");

// node_modules/@scalar/api-client/dist/libs/send-request/create-fetch-body.js
function v2(f4, o3, t3) {
  var r3, i5, n2;
  if (!canMethodHaveBody(f4)) return { body: void 0, contentType: void 0 };
  if (o3.body.activeBody === "formData" && o3.body.formData) {
    const y = o3.body.formData.encoding === "form-data" ? "multipart/form-data" : "application/x-www-form-urlencoded", d5 = o3.body.formData.encoding === "form-data" ? new FormData() : new URLSearchParams();
    return o3.body.formData.value.forEach((a4) => {
      !a4.enabled || !a4.key || (a4.file && d5 instanceof FormData ? d5.append(a4.key, a4.file, a4.file.name) : a4.value !== void 0 && d5.append(a4.key, f2(a4.value, t3)));
    }), { body: d5, contentType: y };
  }
  return o3.body.activeBody === "raw" ? {
    body: f2(((r3 = o3.body.raw) == null ? void 0 : r3.value) ?? "", t3),
    contentType: (i5 = o3.body.raw) == null ? void 0 : i5.encoding
  } : o3.body.activeBody === "binary" ? {
    body: o3.body.binary,
    contentType: (n2 = o3.body.binary) == null ? void 0 : n2.type
  } : {
    body: void 0,
    contentType: void 0
  };
}

// node_modules/@scalar/api-client/dist/libs/send-request/create-fetch-headers.js
function l(t3, o3) {
  const a4 = {};
  return t3.parameters.headers.forEach((e2) => {
    const r3 = e2.key.trim().toLowerCase();
    e2.enabled && (r3 !== "content-type" || e2.value !== "multipart/form-data") && (a4[r3] = f2(e2.value, o3));
  }), a4;
}

// node_modules/@scalar/api-client/dist/libs/send-request/create-fetch-query-params.js
function o2(s4, e2) {
  const r3 = new URLSearchParams();
  return s4.parameters.query.forEach((a4) => {
    a4.enabled && (a4.type === "array" ? f2(a4.value ?? "", e2).split(",") : [f2(a4.value ?? "", e2)]).forEach((c4) => r3.append(a4.key, c4.trim()));
  }), r3;
}

// node_modules/@scalar/api-client/dist/libs/send-request/decode-buffer.js
var import_whatwg_mimetype = __toESM(require_mime_type(), 1);
function m7(r3, t3) {
  const e2 = new import_whatwg_mimetype.default(t3);
  return i3.includes(e2.essence) ? new TextDecoder(e2.parameters.get("charset")).decode(r3) : new Blob([r3], { type: e2.essence });
}

// node_modules/@scalar/api-client/dist/libs/send-request/create-request-operation.js
var ce = ({
  environment: R,
  example: a4,
  globalCookies: C5,
  proxyUrl: s4,
  request: d5,
  securitySchemes: P3,
  selectedSecuritySchemeUids: T3 = [],
  server: i5,
  status: o3
}) => {
  try {
    const t3 = R ?? {}, y = new AbortController(), S = a4.parameters.path.reduce((r3, e2) => (e2.enabled && (r3[e2.key] = f2(e2.value, t3)), r3), {}), q4 = f2((i5 == null ? void 0 : i5.url) ?? "", t3), h2 = f2(d5.path, S);
    let n2 = q4 || h2;
    if (!n2) throw r.URL_EMPTY;
    Object.entries((i5 == null ? void 0 : i5.variables) ?? {}).forEach(([r3, e2]) => {
      n2 = f2(n2, {
        [r3]: S[r3] || e2.default
      });
    });
    const H = o2(a4, t3), U = l(a4, t3), { body: L2 } = v2(d5.method, a4, t3), { cookieParams: E3 } = C3({
      example: a4,
      env: t3,
      globalCookies: C5,
      serverUrl: n2,
      proxyUrl: s4
    }), D2 = T3.flat().map((r3) => P3[r3]).filter(isDefined), l2 = $3(D2, t3), m9 = { ...Object.entries(l2.headers).reduce(
      (r3, [e2, u5]) => (r3[e2.toLowerCase()] = u5, r3),
      {}
    ), ...U }, _4 = [...E3, ...l2.cookies], z2 = new URLSearchParams([...H, ...l2.urlParams]);
    n2 = mergeUrls(n2, h2, z2);
    const f4 = f2(D(_4, m9.Cookie), t3);
    f4 && (shouldUseProxy(s4, n2) ? (console.warn(
      "We’re using a `X-Scalar-Cookie` custom header to the request. The proxy will forward this as a `Cookie` header. We do this to avoid the browser omitting the `Cookie` header for cross-origin requests for security reasons."
    ), m9["X-Scalar-Cookie"] = f4) : (console.warn(
      `We’re trying to add a Cookie header, but browsers often omit them for cross-origin requests for various security reasons. If it’s not working, that’s probably why. Here are the requirements for it to work:

          - The browser URL must be on the same domain as the server URL.
          - The connection must be made over HTTPS.
          `
    ), m9.Cookie = f4));
    const F3 = new URLSearchParams([["scalar_url", n2.toString()]]), B2 = shouldUseProxy(s4, n2) ? `${s4}?${F3.toString()}` : n2, b = new Request(B2, {
      method: d5.method.toUpperCase(),
      body: L2 ?? null,
      headers: m9
    });
    return [
      null,
      {
        request: b,
        sendRequest: async () => {
          o3 == null || o3.emit("start");
          const r3 = Date.now();
          try {
            const e2 = await fetch(b, {
              signal: y.signal
            });
            o3 == null || o3.emit("stop");
            const u5 = f3(e2.headers, shouldUseProxy(s4, n2)), O2 = e2.headers.get("content-type") ?? "text/plain;charset=UTF-8", w4 = await e2.arrayBuffer(), W2 = m7(w4, O2), j = "getSetCookie" in e2.headers && typeof e2.headers.getSetCookie == "function" ? e2.headers.getSetCookie() : [];
            return [
              null,
              {
                timestamp: Date.now(),
                request: a4,
                response: {
                  ...e2,
                  headers: u5,
                  cookieHeaderKeys: j,
                  data: W2,
                  size: w4.byteLength,
                  duration: Date.now() - r3,
                  method: d5.method,
                  status: e2.status,
                  path: h2
                }
              }
            ];
          } catch (e2) {
            return o3 == null || o3.emit("abort"), [t(e2, r.REQUEST_FAILED), null];
          }
        },
        controller: y
      }
    ];
  } catch (t3) {
    return console.error(t3), o3 == null || o3.emit("abort"), [t(t3), null];
  }
};

// node_modules/@scalar/api-client/dist/views/Request/RequestRoot.vue2.js
var ue = { class: "flex h-full" };
var ce2 = { class: "flex h-full flex-1 flex-col" };
var Oe2 = defineComponent({
  __name: "RequestRoot",
  emits: ["newTab"],
  setup(me) {
    const U = je(), { toast: f4 } = i(), { layout: n2 } = s(), {
      activeCollection: i5,
      activeExample: a4,
      activeEnvironment: d5,
      activeRequest: u5,
      activeWorkspace: v3,
      activeServer: C5
    } = F(), { cookies: T3, requestHistory: g4, showSidebar: p2, securitySchemes: A, events: l2 } = U, { isSidebarOpen: r3 } = m3(), S = ref(), c4 = ref(/* @__PURE__ */ new Set()), B2 = computed(
      () => {
        var e2, t3;
        return (n2 === "modal" ? (e2 = i5.value) == null ? void 0 : e2.selectedSecuritySchemeUids : (t3 = u5.value) == null ? void 0 : t3.selectedSecuritySchemeUids) ?? [];
      }
    ), R = async () => {
      var q4, h2, E3, k6;
      if (!u5.value || !a4.value || !i5.value)
        return;
      c4.value = i4(a4.value);
      const e2 = typeof d5.value == "object" ? d5.value.value : "{}", t3 = safeJSON.parse(e2);
      t3.error && console.error("INVALID ENVIRONMENT!");
      const s4 = t3.error || typeof t3.data != "object" ? {} : t3.data ?? {}, P3 = ((q4 = v3.value) == null ? void 0 : q4.cookies.map((J3) => T3[J3]).filter(isDefined)) ?? [], z2 = ((E3 = (h2 = i5.value) == null ? void 0 : h2.info) == null ? void 0 : E3.title) === "Drafts" ? void 0 : C5.value, [x3, b] = ce({
        request: u5.value,
        example: a4.value,
        selectedSecuritySchemeUids: B2.value,
        proxyUrl: ((k6 = v3.value) == null ? void 0 : k6.proxyUrl) ?? "",
        environment: s4,
        globalCookies: P3,
        status: l2.requestStatus,
        securitySchemes: A,
        server: z2
      });
      if (x3) {
        f4(x3.message, "error");
        return;
      }
      S.value = b.controller;
      const [y, I3] = await b.sendRequest();
      y ? f4(y.message, "error") : g4.push(JSON.parse(JSON.stringify(I3)));
    }, D2 = async () => {
      var e2;
      return (e2 = S.value) == null ? void 0 : e2.abort(r.REQUEST_ABORTED);
    };
    return onMounted(() => {
      l2.executeRequest.on(R), l2.cancelRequest.on(D2);
    }), J2(), onBeforeUnmount(() => l2.executeRequest.off(R)), watch(
      () => {
        var e2;
        return (e2 = a4.value) == null ? void 0 : e2.parameters;
      },
      () => {
        c4.value.clear();
      },
      { deep: true }
    ), (e2, t3) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["bg-b-1 relative z-0 flex h-full flex-1 flex-col overflow-hidden pt-0", {
        "!mb-0 !mr-0 !border-0": unref(n2) === "modal"
      }])
    }, [
      unref(p2) ? (openBlock(), createBlock(k2, {
        key: 0,
        modelValue: unref(r3),
        "onUpdate:modelValue": t3[0] || (t3[0] = (s4) => isRef(r3) ? r3.value = s4 : null),
        class: normalizeClass(["absolute left-3 top-2 z-50", [
          { hidden: unref(r3) },
          { "xl:!flex": !unref(r3) },
          { "!flex": unref(n2) === "modal" }
        ]])
      }, null, 8, ["modelValue", "class"])) : createCommentVNode("", true),
      createBaseVNode("div", ue, [
        unref(p2) ? (openBlock(), createBlock(d3, {
          key: 0,
          onNewTab: t3[1] || (t3[1] = (s4) => e2.$emit("newTab", s4))
        })) : createCommentVNode("", true),
        createBaseVNode("div", ce2, [
          createVNode(unref(RouterView), { invalidParams: c4.value }, null, 8, ["invalidParams"])
        ])
      ])
    ], 2));
  }
});

// node_modules/@scalar/api-client/dist/views/Request/RequestRoot.vue.js
var m8 = s2(Oe2, [["__scopeId", "data-v-1dfc8727"]]);
export {
  m8 as default
};
//# sourceMappingURL=RequestRoot.vue-UPKE62KU.js.map

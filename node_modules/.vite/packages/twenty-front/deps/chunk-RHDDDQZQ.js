import {
  requiredArgs,
  startOfUTCWeek
} from "./chunk-ENOIHPZK.js";

// node_modules/date-fns/esm/_lib/isSameUTCWeek/index.js
function isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {
  requiredArgs(2, arguments);
  var dateLeftStartOfWeek = startOfUTCWeek(dirtyDateLeft, options);
  var dateRightStartOfWeek = startOfUTCWeek(dirtyDateRight, options);
  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();
}

export {
  isSameUTCWeek
};
//# sourceMappingURL=chunk-RHDDDQZQ.js.map

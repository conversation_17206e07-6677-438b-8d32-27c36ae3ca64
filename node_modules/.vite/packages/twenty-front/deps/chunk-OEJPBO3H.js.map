{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/forEachState.js"], "sourcesContent": ["export default function forEachState(stack, fn) {\n    const reverseStateStack = [];\n    let state = stack;\n    while (state === null || state === void 0 ? void 0 : state.kind) {\n        reverseStateStack.push(state);\n        state = state.prevState;\n    }\n    for (let i = reverseStateStack.length - 1; i >= 0; i--) {\n        fn(reverseStateStack[i]);\n    }\n}\n//# sourceMappingURL=forEachState.js.map"], "mappings": ";AAAe,SAAS,aAAa,OAAO,IAAI;AAC5C,QAAM,oBAAoB,CAAA;AAC1B,MAAI,QAAQ;AACZ,SAAO,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,MAAM;AAC7D,sBAAkB,KAAK,KAAK;AAC5B,YAAQ,MAAM;EACjB;AACD,WAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,OAAG,kBAAkB,CAAC,CAAC;EAC1B;AACL;", "names": []}
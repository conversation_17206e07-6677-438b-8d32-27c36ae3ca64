{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/typespec/typespec.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/typespec/typespec.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar notBefore = (regex) => `(?!${regex})`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar directive = bounded(`[_a-zA-Z-0-9]+`);\nvar keywords = [\n  \"import\",\n  \"model\",\n  \"scalar\",\n  \"namespace\",\n  \"op\",\n  \"interface\",\n  \"union\",\n  \"using\",\n  \"is\",\n  \"extends\",\n  \"enum\",\n  \"alias\",\n  \"return\",\n  \"void\",\n  \"if\",\n  \"else\",\n  \"projection\",\n  \"dec\",\n  \"extern\",\n  \"fn\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\", \"unknown\", \"never\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  indentationRules: {\n    decreaseIndentPattern: new RegExp(\"^((?!.*?/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\"),\n    increaseIndentPattern: new RegExp(\n      \"^((?!//).)*(\\\\{([^}\\\"'`/]*|(\\\\t|[ ])*//.*)|\\\\([^)\\\"'`/]*|\\\\[[^\\\\]\\\"'`/]*)$\"\n    ),\n    // e.g.  * ...| or */| or *-----*/|\n    unIndentedLinePattern: new RegExp(\n      \"^(\\\\t|[ ])*[ ]\\\\*[^/]*\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*([ ]([^\\\\*]|\\\\*(?!/))*)?$\"\n    )\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".tsp\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=:;<>]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|\"|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|\"|\"\")[^\"]`, action: { token: \"string\" } },\n      { regex: `\"\"\"${notBefore(`\"`)}`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\\"$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `\"`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"@expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `\"\"\"`, action: { token: \"string\", next: \"@stringVerbatim\" } },\n      { regex: `\"${notBefore(`\"\"`)}`, action: { token: \"string\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: `@${identifier}`, action: { token: \"tag\" } },\n      { regex: `#${directive}`, action: { token: \"directive\" } }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,SACA,WACA,iBACA,oBACA,YACA,WACA,UAsBA,eACA,cACA,gBACA,MAkCA;AA1EJ;AAAA;AASA,IAAI,UAAU,CAAC,SAAS,MAAM,IAAI;AAClC,IAAI,YAAY,CAAC,UAAU,MAAM,KAAK;AACtC,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,aAAa,QAAQ,GAAG,eAAe,GAAG,kBAAkB,GAAG;AACnE,IAAI,YAAY,QAAQ,gBAAgB;AACxC,IAAI,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAI,gBAAgB,CAAC,QAAQ,SAAS,QAAQ,WAAW,OAAO;AAChE,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAE;AAAA,MACjD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,uBAAuB,IAAI,OAAO,sCAAsC;AAAA,QACxE,uBAAuB,IAAI;AAAA,UACzB;AAAA,QACF;AAAA;AAAA,QAEA,uBAAuB,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,MAC1D;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,WAAW;AAAA,QACT,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,EAAE,SAAS,cAAc,CAAC;AAAA,QAC7D,gBAAgB;AAAA,UACd,EAAE,OAAO,eAAe,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,UACpD,EAAE,OAAO,MAAM,UAAU,GAAG,CAAC,IAAI,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,EAAE;AAAA,QAC7E;AAAA,QACA,eAAe;AAAA,UACb,EAAE,OAAO,SAAS,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,UACnF,EAAE,OAAO,cAAc,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,UACnD,EAAE,OAAO,YAAY,QAAQ,EAAE,OAAO,gBAAgB,EAAE;AAAA,UACxD,EAAE,OAAO,SAAS,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,UAC7D,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,EAAE;AAAA,QAC1D;AAAA,QACA,iBAAiB;AAAA,UACf,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,UAC/E,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,OAAO,EAAE;AAAA,UACnE,EAAE,SAAS,cAAc;AAAA,QAC3B;AAAA,QACA,SAAS;AAAA,UACP,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,UACjD,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,OAAO,EAAE;AAAA,UAC9D,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,QAClD;AAAA,QACA,YAAY;AAAA,UACV,EAAE,OAAO,aAAa;AAAA,UACtB,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,EAAE;AAAA,UAClE,EAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,QACrD;AAAA,QACA,YAAY;AAAA,UACV,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,UAAU,MAAM,kBAAkB,EAAE;AAAA,UACrE,EAAE,OAAO,IAAI,UAAU,IAAI,CAAC,IAAI,QAAQ,EAAE,OAAO,UAAU,MAAM,iBAAiB,EAAE;AAAA,UACpF,EAAE,OAAO,gBAAgB,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,UACrD;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,aAAa,EAAE,OAAO,UAAU;AAAA,gBAChC,kBAAkB,EAAE,OAAO,UAAU;AAAA,gBACrC,YAAY,EAAE,OAAO,aAAa;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,OAAO,IAAI,UAAU,IAAI,QAAQ,EAAE,OAAO,MAAM,EAAE;AAAA,UACpD,EAAE,OAAO,IAAI,SAAS,IAAI,QAAQ,EAAE,OAAO,YAAY,EAAE;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
{"version": 3, "sources": ["../../../../@scalar/api-client/dist/components/Sidebar/Actions/SidebarListElementForm.vue2.js", "../../../../@scalar/api-client/dist/components/Sidebar/Actions/SidebarListElementForm.vue.js", "../../../../@scalar/api-client/dist/components/Sidebar/Actions/DeleteSidebarListElement.vue.js"], "sourcesContent": ["import { defineComponent as u, openBlock as m, createElement<PERSON>lock as d, withModifiers as f, renderSlot as c, createElementVNode as b, createVNode as n, unref as a, withCtx as r, createTextVNode as s, toDisplayString as v } from \"vue\";\nimport { ScalarButton as l } from \"@scalar/components\";\nconst g = { class: \"flex justify-between gap-10\" }, y = /* @__PURE__ */ u({\n  __name: \"SidebarListElementForm\",\n  props: {\n    danger: { type: Boolean },\n    label: {}\n  },\n  emits: [\"cancel\", \"submit\"],\n  setup(x, { emit: i }) {\n    const o = i;\n    return (t, e) => (m(), d(\"form\", {\n      class: \"flex flex-col gap-4 text-base\",\n      onSubmit: e[1] || (e[1] = f((p) => o(\"submit\"), [\"prevent\"]))\n    }, [\n      c(t.$slots, \"default\", {}, void 0, !0),\n      b(\"div\", g, [\n        n(a(l), {\n          class: \"flex h-8 cursor-pointer items-center gap-1.5 px-2.5 shadow-none focus:outline-none\",\n          type: \"button\",\n          variant: \"outlined\",\n          onClick: e[0] || (e[0] = (p) => o(\"cancel\"))\n        }, {\n          default: r(() => e[2] || (e[2] = [\n            s(\" Cancel \")\n          ])),\n          _: 1\n        }),\n        n(a(l), {\n          class: \"custom-scroll h-8 gap-1.5 whitespace-nowrap px-2.5 font-medium shadow-none focus:outline-none\",\n          type: \"submit\",\n          variant: t.danger ? \"danger\" : \"solid\"\n        }, {\n          default: r(() => [\n            s(v(t.label ?? \"Save\"), 1)\n          ]),\n          _: 1\n        }, 8, [\"variant\"])\n      ])\n    ], 32));\n  }\n});\nexport {\n  y as default\n};\n", "import o from \"./SidebarListElementForm.vue2.js\";\n/* empty css                            */\nimport t from \"../../../_virtual/_plugin-vue_export-helper.js\";\nconst a = /* @__PURE__ */ t(o, [[\"__scopeId\", \"data-v-c6b17e96\"]]);\nexport {\n  a as default\n};\n", "import { defineComponent as m, computed as p, openBlock as r, createBlock as c, withCtx as d, createElementBlock as u, toDisplayString as g, createCommentVNode as b } from \"vue\";\nimport f from \"./SidebarListElementForm.vue.js\";\nconst v = {\n  key: 0,\n  class: \"text-c-2 text-pretty text-sm leading-normal\"\n}, w = /* @__PURE__ */ m({\n  __name: \"DeleteSidebarListElement\",\n  props: {\n    variableName: {},\n    warningMessage: {}\n  },\n  emits: [\"close\", \"delete\"],\n  setup(l, { emit: o }) {\n    const t = l, a = o, s = p(() => t.variableName.length > 18 ? t.variableName.slice(0, 18) + \"…\" : t.variableName);\n    return (n, e) => (r(), c(f, {\n      danger: \"\",\n      label: `Delete ${s.value}`,\n      onCancel: e[0] || (e[0] = (i) => a(\"close\")),\n      onSubmit: e[1] || (e[1] = (i) => a(\"delete\"))\n    }, {\n      default: d(() => [\n        n.warningMessage ? (r(), u(\"p\", v, g(n.warningMessage), 1)) : b(\"\", !0)\n      ]),\n      _: 1\n    }, 8, [\"label\"]));\n  }\n});\nexport {\n  w as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAI,EAAE,OAAO,8BAA8B;AAAjD,IAAoD,IAAoB,gBAAE;AAAA,EACxE,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,QAAQ,EAAE,MAAM,QAAQ;AAAA,IACxB,OAAO,CAAC;AAAA,EACV;AAAA,EACA,OAAO,CAAC,UAAU,QAAQ;AAAA,EAC1B,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI;AACV,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,MAC/B,OAAO;AAAA,MACP,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,cAAE,CAAC,MAAM,EAAE,QAAQ,GAAG,CAAC,SAAS,CAAC;AAAA,IAC7D,GAAG;AAAA,MACD,WAAE,EAAE,QAAQ,WAAW,CAAC,GAAG,QAAQ,IAAE;AAAA,MACrC,gBAAE,OAAO,GAAG;AAAA,QACV,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,QAC5C,GAAG;AAAA,UACD,SAAS,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,YAC/B,gBAAE,UAAU;AAAA,UACd,EAAE;AAAA,UACF,GAAG;AAAA,QACL,CAAC;AAAA,QACD,YAAE,MAAE,CAAC,GAAG;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS,EAAE,SAAS,WAAW;AAAA,QACjC,GAAG;AAAA,UACD,SAAS,QAAE,MAAM;AAAA,YACf,gBAAE,gBAAE,EAAE,SAAS,MAAM,GAAG,CAAC;AAAA,UAC3B,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,GAAG,CAAC,SAAS,CAAC;AAAA,MACnB,CAAC;AAAA,IACH,GAAG,EAAE;AAAA,EACP;AACF,CAAC;;;ACtCD,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;;;ACDjE,IAAM,IAAI;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,cAAc,CAAC;AAAA,IACf,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAGA,KAAI,GAAGC,KAAI,SAAE,MAAM,EAAE,aAAa,SAAS,KAAK,EAAE,aAAa,MAAM,GAAG,EAAE,IAAI,MAAM,EAAE,YAAY;AAC/G,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG;AAAA,MAC1B,QAAQ;AAAA,MACR,OAAO,UAAUA,GAAE,KAAK;AAAA,MACxB,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAMD,GAAE,OAAO;AAAA,MAC1C,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAMA,GAAE,QAAQ;AAAA,IAC7C,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACf,EAAE,kBAAkB,UAAE,GAAG,mBAAE,KAAK,GAAG,gBAAE,EAAE,cAAc,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACxE,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,OAAO,CAAC;AAAA,EACjB;AACF,CAAC;", "names": ["a", "s"]}
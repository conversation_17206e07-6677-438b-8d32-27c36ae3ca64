{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dockerfile/dockerfile.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".dockerfile\",\n  variable: /\\${?[\\w]+}?/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      [/(ONBUILD)(\\s+)/, [\"keyword\", \"\"]],\n      [/(ENV)(\\s+)([\\w]+)/, [\"keyword\", \"\", { token: \"variable\", next: \"@arguments\" }]],\n      [\n        /(FROM|MAINTAINER|RUN|EXPOSE|ENV|ADD|ARG|VOLUME|LABEL|USER|WORKDIR|COPY|CMD|STOPSIGNAL|SHELL|HEALTHCHECK|ENTRYPOINT)/,\n        { token: \"keyword\", next: \"@arguments\" }\n      ]\n    ],\n    arguments: [\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      [\n        /(@variable)/,\n        {\n          cases: {\n            \"@eos\": { token: \"variable\", next: \"@popall\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      [\n        /\\\\/,\n        {\n          cases: {\n            \"@eos\": \"\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [\n        /./,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including comments\n    whitespace: [\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"\", next: \"@popall\" },\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    comment: [[/(^#.*$)/, \"comment\", \"@popall\"]],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/\\\\'$/, \"\", \"@popall\"],\n      // \\' leaves @arguments at eol\n      [/\\\\'/, \"\"],\n      // \\' is not a string\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@stringBody\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /[^\\\\\\$']/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/'$/, \"string\", \"@popall\"],\n      [/'/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ],\n    dblStringBody: [\n      [\n        /[^\\\\\\$\"]/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/\\\\./, \"string.escape\"],\n      [/\"$/, \"string\", \"@popall\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/(@variable)/, \"variable\"],\n      [/\\\\$/, \"string\"],\n      [/$/, \"string\", \"@popall\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAqBA;AA9BJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;AAAA,UAClC,CAAC,qBAAqB,CAAC,WAAW,IAAI,EAAE,OAAO,YAAY,MAAM,aAAa,CAAC,CAAC;AAAA,UAChF;AAAA,YACE;AAAA,YACA,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,UACzC;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA,UACtB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,gBAC7C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ;AAAA,gBACR,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,gBACrC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,YAAY;AAAA,UACV;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,gBACrC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS,CAAC,CAAC,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,QAE3C,SAAS;AAAA,UACP,CAAC,QAAQ,IAAI,SAAS;AAAA;AAAA,UAEtB,CAAC,OAAO,EAAE;AAAA;AAAA,UAEV,CAAC,MAAM,UAAU,SAAS;AAAA,UAC1B,CAAC,KAAK,UAAU,aAAa;AAAA,UAC7B,CAAC,MAAM,UAAU,SAAS;AAAA,UAC1B,CAAC,KAAK,UAAU,gBAAgB;AAAA,QAClC;AAAA,QACA,YAAY;AAAA,UACV;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,OAAO,eAAe;AAAA,UACvB,CAAC,MAAM,UAAU,SAAS;AAAA,UAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,UACtB,CAAC,eAAe,UAAU;AAAA,UAC1B,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,UAAU,SAAS;AAAA,QAC3B;AAAA,QACA,eAAe;AAAA,UACb;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,OAAO,eAAe;AAAA,UACvB,CAAC,MAAM,UAAU,SAAS;AAAA,UAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,UACtB,CAAC,eAAe,UAAU;AAAA,UAC1B,CAAC,OAAO,QAAQ;AAAA,UAChB,CAAC,KAAK,UAAU,SAAS;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
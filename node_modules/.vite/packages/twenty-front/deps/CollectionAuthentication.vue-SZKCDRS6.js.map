{"version": 3, "sources": ["../../../../@scalar/api-client/dist/views/Collection/CollectionAuthentication.vue2.js"], "sourcesContent": ["import { defineComponent as e, openBlock as o, createBlock as n, withCtx as i, createTextVNode as r } from \"vue\";\nimport c from \"../../components/ViewLayout/ViewLayoutSection.vue.js\";\nconst p = /* @__PURE__ */ e({\n  __name: \"CollectionAuthentication\",\n  setup(_) {\n    return (a, t) => (o(), n(c, null, {\n      title: i(() => t[0] || (t[0] = [\n        r(\"Authentication\")\n      ])),\n      _: 1\n    }));\n  }\n});\nexport {\n  p as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAMA,IAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,GAAG,MAAM;AAAA,MAChC,OAAO,QAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA,QAC7B,gBAAE,gBAAgB;AAAA,MACpB,EAAE;AAAA,MACF,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;", "names": ["_"]}
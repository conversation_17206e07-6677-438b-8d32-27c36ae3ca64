import {
  g,
  i,
  m
} from "./chunk-RBNUC7A3.js";
import {
  F
} from "./chunk-K3FRCNXE.js";
import {
  _
} from "./chunk-LNIR43HZ.js";
import {
  Fragment,
  c,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createVNode,
  defineComponent,
  normalizeClass,
  openBlock,
  renderList,
  renderSlot,
  toDisplayString,
  unref,
  useId,
  withCtx
} from "./chunk-D5ZO4EYM.js";

// node_modules/@scalar/api-client/dist/components/Form/Form.vue.js
var D = { key: 0 };
var F2 = { class: "custom-scroll flex flex-1 flex-col gap-1.5" };
var I = ["for"];
var N = { class: "bg-b-2 flex-center border-l px-2" };
var q = defineComponent({
  __name: "Form",
  props: {
    title: {},
    options: {},
    data: {},
    onUpdate: { type: Function }
  },
  setup(z) {
    const { activeEnvVariables: u, activeEnvironment: k, activeWorkspace: s } = F(), i2 = useId();
    return (e, T) => (openBlock(), createBlock(_, null, createSlots({
      default: withCtx(() => [
        createBaseVNode("div", F2, [
          Object.keys(e.data).length > 0 && unref(s) ? (openBlock(), createBlock(g, {
            key: 0,
            columns: [""]
          }, {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(e.options, (a, d) => (openBlock(), createBlock(i, {
                key: d,
                class: normalizeClass({ "border-t": d === 0 })
              }, {
                default: withCtx(() => [
                  createVNode(m, {
                    id: unref(i2),
                    envVariables: unref(u),
                    environment: unref(k),
                    modelValue: e.data[a.key] ?? "",
                    placeholder: a.placeholder,
                    workspace: unref(s),
                    "onUpdate:modelValue": (_2) => e.onUpdate(a.key, _2)
                  }, createSlots({
                    default: withCtx(() => [
                      createBaseVNode("label", { for: unref(i2) }, toDisplayString(a.label), 9, I)
                    ]),
                    _: 2
                  }, [
                    a.key === "description" ? {
                      name: "icon",
                      fn: withCtx(() => [
                        createBaseVNode("div", N, [
                          createVNode(unref(c), {
                            icon: "Markdown",
                            size: "lg"
                          })
                        ])
                      ]),
                      key: "0"
                    } : void 0
                  ]), 1032, ["id", "envVariables", "environment", "modelValue", "placeholder", "workspace", "onUpdate:modelValue"])
                ]),
                _: 2
              }, 1032, ["class"]))), 128))
            ]),
            _: 1
          })) : createCommentVNode("", true)
        ])
      ]),
      _: 2
    }, [
      e.title || e.$slots.title ? {
        name: "title",
        fn: withCtx(() => [
          e.title ? (openBlock(), createElementBlock("span", D, toDisplayString(e.title), 1)) : renderSlot(e.$slots, "title", { key: 1 })
        ]),
        key: "0"
      } : void 0
    ]), 1024));
  }
});

export {
  q
};
//# sourceMappingURL=chunk-4LSRZQCZ.js.map

import {
  __commonJS
} from "./chunk-XPZLJQLW.js";

// node_modules/ts-key-enum/dist/js/Key.enum.js
var require_Key_enum = __commonJS({
  "node_modules/ts-key-enum/dist/js/Key.enum.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    var Key;
    (function(Key2) {
      Key2["Unidentified"] = "Unidentified";
      Key2["Alt"] = "Alt";
      Key2["AltGraph"] = "AltGraph";
      Key2["CapsLock"] = "CapsLock";
      Key2["Control"] = "Control";
      Key2["Fn"] = "Fn";
      Key2["FnLock"] = "FnLock";
      Key2["Hyper"] = "Hyper";
      Key2["Meta"] = "Meta";
      Key2["NumLock"] = "NumLock";
      Key2["ScrollLock"] = "ScrollLock";
      Key2["Shift"] = "Shift";
      Key2["Super"] = "Super";
      Key2["Symbol"] = "Symbol";
      Key2["SymbolLock"] = "SymbolLock";
      Key2["Enter"] = "Enter";
      Key2["Tab"] = "Tab";
      Key2["ArrowDown"] = "ArrowDown";
      Key2["ArrowLeft"] = "ArrowLeft";
      Key2["ArrowRight"] = "ArrowRight";
      Key2["ArrowUp"] = "ArrowUp";
      Key2["End"] = "End";
      Key2["Home"] = "Home";
      Key2["PageDown"] = "PageDown";
      Key2["PageUp"] = "PageUp";
      Key2["Backspace"] = "Backspace";
      Key2["Clear"] = "Clear";
      Key2["Copy"] = "Copy";
      Key2["CrSel"] = "CrSel";
      Key2["Cut"] = "Cut";
      Key2["Delete"] = "Delete";
      Key2["EraseEof"] = "EraseEof";
      Key2["ExSel"] = "ExSel";
      Key2["Insert"] = "Insert";
      Key2["Paste"] = "Paste";
      Key2["Redo"] = "Redo";
      Key2["Undo"] = "Undo";
      Key2["Accept"] = "Accept";
      Key2["Again"] = "Again";
      Key2["Attn"] = "Attn";
      Key2["Cancel"] = "Cancel";
      Key2["ContextMenu"] = "ContextMenu";
      Key2["Escape"] = "Escape";
      Key2["Execute"] = "Execute";
      Key2["Find"] = "Find";
      Key2["Finish"] = "Finish";
      Key2["Help"] = "Help";
      Key2["Pause"] = "Pause";
      Key2["Play"] = "Play";
      Key2["Props"] = "Props";
      Key2["Select"] = "Select";
      Key2["ZoomIn"] = "ZoomIn";
      Key2["ZoomOut"] = "ZoomOut";
      Key2["BrightnessDown"] = "BrightnessDown";
      Key2["BrightnessUp"] = "BrightnessUp";
      Key2["Eject"] = "Eject";
      Key2["LogOff"] = "LogOff";
      Key2["Power"] = "Power";
      Key2["PowerOff"] = "PowerOff";
      Key2["PrintScreen"] = "PrintScreen";
      Key2["Hibernate"] = "Hibernate";
      Key2["Standby"] = "Standby";
      Key2["WakeUp"] = "WakeUp";
      Key2["AllCandidates"] = "AllCandidates";
      Key2["Alphanumeric"] = "Alphanumeric";
      Key2["CodeInput"] = "CodeInput";
      Key2["Compose"] = "Compose";
      Key2["Convert"] = "Convert";
      Key2["Dead"] = "Dead";
      Key2["FinalMode"] = "FinalMode";
      Key2["GroupFirst"] = "GroupFirst";
      Key2["GroupLast"] = "GroupLast";
      Key2["GroupNext"] = "GroupNext";
      Key2["GroupPrevious"] = "GroupPrevious";
      Key2["ModeChange"] = "ModeChange";
      Key2["NextCandidate"] = "NextCandidate";
      Key2["NonConvert"] = "NonConvert";
      Key2["PreviousCandidate"] = "PreviousCandidate";
      Key2["Process"] = "Process";
      Key2["SingleCandidate"] = "SingleCandidate";
      Key2["HangulMode"] = "HangulMode";
      Key2["HanjaMode"] = "HanjaMode";
      Key2["JunjaMode"] = "JunjaMode";
      Key2["Eisu"] = "Eisu";
      Key2["Hankaku"] = "Hankaku";
      Key2["Hiragana"] = "Hiragana";
      Key2["HiraganaKatakana"] = "HiraganaKatakana";
      Key2["KanaMode"] = "KanaMode";
      Key2["KanjiMode"] = "KanjiMode";
      Key2["Katakana"] = "Katakana";
      Key2["Romaji"] = "Romaji";
      Key2["Zenkaku"] = "Zenkaku";
      Key2["ZenkakuHanaku"] = "ZenkakuHanaku";
      Key2["F1"] = "F1";
      Key2["F2"] = "F2";
      Key2["F3"] = "F3";
      Key2["F4"] = "F4";
      Key2["F5"] = "F5";
      Key2["F6"] = "F6";
      Key2["F7"] = "F7";
      Key2["F8"] = "F8";
      Key2["F9"] = "F9";
      Key2["F10"] = "F10";
      Key2["F11"] = "F11";
      Key2["F12"] = "F12";
      Key2["F13"] = "F13";
      Key2["F14"] = "F14";
      Key2["F15"] = "F15";
      Key2["F16"] = "F16";
      Key2["F17"] = "F17";
      Key2["F18"] = "F18";
      Key2["F19"] = "F19";
      Key2["F20"] = "F20";
      Key2["Soft1"] = "Soft1";
      Key2["Soft2"] = "Soft2";
      Key2["Soft3"] = "Soft3";
      Key2["Soft4"] = "Soft4";
      Key2["AppSwitch"] = "AppSwitch";
      Key2["Call"] = "Call";
      Key2["Camera"] = "Camera";
      Key2["CameraFocus"] = "CameraFocus";
      Key2["EndCall"] = "EndCall";
      Key2["GoBack"] = "GoBack";
      Key2["GoHome"] = "GoHome";
      Key2["HeadsetHook"] = "HeadsetHook";
      Key2["LastNumberRedial"] = "LastNumberRedial";
      Key2["Notification"] = "Notification";
      Key2["MannerMode"] = "MannerMode";
      Key2["VoiceDial"] = "VoiceDial";
      Key2["ChannelDown"] = "ChannelDown";
      Key2["ChannelUp"] = "ChannelUp";
      Key2["MediaFastForward"] = "MediaFastForward";
      Key2["MediaPause"] = "MediaPause";
      Key2["MediaPlay"] = "MediaPlay";
      Key2["MediaPlayPause"] = "MediaPlayPause";
      Key2["MediaRecord"] = "MediaRecord";
      Key2["MediaRewind"] = "MediaRewind";
      Key2["MediaStop"] = "MediaStop";
      Key2["MediaTrackNext"] = "MediaTrackNext";
      Key2["MediaTrackPrevious"] = "MediaTrackPrevious";
      Key2["AudioBalanceLeft"] = "AudioBalanceLeft";
      Key2["AudioBalanceRight"] = "AudioBalanceRight";
      Key2["AudioBassDown"] = "AudioBassDown";
      Key2["AudioBassBoostDown"] = "AudioBassBoostDown";
      Key2["AudioBassBoostToggle"] = "AudioBassBoostToggle";
      Key2["AudioBassBoostUp"] = "AudioBassBoostUp";
      Key2["AudioBassUp"] = "AudioBassUp";
      Key2["AudioFaderFront"] = "AudioFaderFront";
      Key2["AudioFaderRear"] = "AudioFaderRear";
      Key2["AudioSurroundModeNext"] = "AudioSurroundModeNext";
      Key2["AudioTrebleDown"] = "AudioTrebleDown";
      Key2["AudioTrebleUp"] = "AudioTrebleUp";
      Key2["AudioVolumeDown"] = "AudioVolumeDown";
      Key2["AudioVolumeMute"] = "AudioVolumeMute";
      Key2["AudioVolumeUp"] = "AudioVolumeUp";
      Key2["MicrophoneToggle"] = "MicrophoneToggle";
      Key2["MicrophoneVolumeDown"] = "MicrophoneVolumeDown";
      Key2["MicrophoneVolumeMute"] = "MicrophoneVolumeMute";
      Key2["MicrophoneVolumeUp"] = "MicrophoneVolumeUp";
      Key2["TV"] = "TV";
      Key2["TV3DMode"] = "TV3DMode";
      Key2["TVAntennaCable"] = "TVAntennaCable";
      Key2["TVAudioDescription"] = "TVAudioDescription";
      Key2["TVAudioDescriptionMixDown"] = "TVAudioDescriptionMixDown";
      Key2["TVAudioDescriptionMixUp"] = "TVAudioDescriptionMixUp";
      Key2["TVContentsMenu"] = "TVContentsMenu";
      Key2["TVDataService"] = "TVDataService";
      Key2["TVInput"] = "TVInput";
      Key2["TVInputComponent1"] = "TVInputComponent1";
      Key2["TVInputComponent2"] = "TVInputComponent2";
      Key2["TVInputComposite1"] = "TVInputComposite1";
      Key2["TVInputComposite2"] = "TVInputComposite2";
      Key2["TVInputHDMI1"] = "TVInputHDMI1";
      Key2["TVInputHDMI2"] = "TVInputHDMI2";
      Key2["TVInputHDMI3"] = "TVInputHDMI3";
      Key2["TVInputHDMI4"] = "TVInputHDMI4";
      Key2["TVInputVGA1"] = "TVInputVGA1";
      Key2["TVMediaContext"] = "TVMediaContext";
      Key2["TVNetwork"] = "TVNetwork";
      Key2["TVNumberEntry"] = "TVNumberEntry";
      Key2["TVPower"] = "TVPower";
      Key2["TVRadioService"] = "TVRadioService";
      Key2["TVSatellite"] = "TVSatellite";
      Key2["TVSatelliteBS"] = "TVSatelliteBS";
      Key2["TVSatelliteCS"] = "TVSatelliteCS";
      Key2["TVSatelliteToggle"] = "TVSatelliteToggle";
      Key2["TVTerrestrialAnalog"] = "TVTerrestrialAnalog";
      Key2["TVTerrestrialDigital"] = "TVTerrestrialDigital";
      Key2["TVTimer"] = "TVTimer";
      Key2["AVRInput"] = "AVRInput";
      Key2["AVRPower"] = "AVRPower";
      Key2["ColorF0Red"] = "ColorF0Red";
      Key2["ColorF1Green"] = "ColorF1Green";
      Key2["ColorF2Yellow"] = "ColorF2Yellow";
      Key2["ColorF3Blue"] = "ColorF3Blue";
      Key2["ColorF4Grey"] = "ColorF4Grey";
      Key2["ColorF5Brown"] = "ColorF5Brown";
      Key2["ClosedCaptionToggle"] = "ClosedCaptionToggle";
      Key2["Dimmer"] = "Dimmer";
      Key2["DisplaySwap"] = "DisplaySwap";
      Key2["DVR"] = "DVR";
      Key2["Exit"] = "Exit";
      Key2["FavoriteClear0"] = "FavoriteClear0";
      Key2["FavoriteClear1"] = "FavoriteClear1";
      Key2["FavoriteClear2"] = "FavoriteClear2";
      Key2["FavoriteClear3"] = "FavoriteClear3";
      Key2["FavoriteRecall0"] = "FavoriteRecall0";
      Key2["FavoriteRecall1"] = "FavoriteRecall1";
      Key2["FavoriteRecall2"] = "FavoriteRecall2";
      Key2["FavoriteRecall3"] = "FavoriteRecall3";
      Key2["FavoriteStore0"] = "FavoriteStore0";
      Key2["FavoriteStore1"] = "FavoriteStore1";
      Key2["FavoriteStore2"] = "FavoriteStore2";
      Key2["FavoriteStore3"] = "FavoriteStore3";
      Key2["Guide"] = "Guide";
      Key2["GuideNextDay"] = "GuideNextDay";
      Key2["GuidePreviousDay"] = "GuidePreviousDay";
      Key2["Info"] = "Info";
      Key2["InstantReplay"] = "InstantReplay";
      Key2["Link"] = "Link";
      Key2["ListProgram"] = "ListProgram";
      Key2["LiveContent"] = "LiveContent";
      Key2["Lock"] = "Lock";
      Key2["MediaApps"] = "MediaApps";
      Key2["MediaAudioTrack"] = "MediaAudioTrack";
      Key2["MediaLast"] = "MediaLast";
      Key2["MediaSkipBackward"] = "MediaSkipBackward";
      Key2["MediaSkipForward"] = "MediaSkipForward";
      Key2["MediaStepBackward"] = "MediaStepBackward";
      Key2["MediaStepForward"] = "MediaStepForward";
      Key2["MediaTopMenu"] = "MediaTopMenu";
      Key2["NavigateIn"] = "NavigateIn";
      Key2["NavigateNext"] = "NavigateNext";
      Key2["NavigateOut"] = "NavigateOut";
      Key2["NavigatePrevious"] = "NavigatePrevious";
      Key2["NextFavoriteChannel"] = "NextFavoriteChannel";
      Key2["NextUserProfile"] = "NextUserProfile";
      Key2["OnDemand"] = "OnDemand";
      Key2["Pairing"] = "Pairing";
      Key2["PinPDown"] = "PinPDown";
      Key2["PinPMove"] = "PinPMove";
      Key2["PinPToggle"] = "PinPToggle";
      Key2["PinPUp"] = "PinPUp";
      Key2["PlaySpeedDown"] = "PlaySpeedDown";
      Key2["PlaySpeedReset"] = "PlaySpeedReset";
      Key2["PlaySpeedUp"] = "PlaySpeedUp";
      Key2["RandomToggle"] = "RandomToggle";
      Key2["RcLowBattery"] = "RcLowBattery";
      Key2["RecordSpeedNext"] = "RecordSpeedNext";
      Key2["RfBypass"] = "RfBypass";
      Key2["ScanChannelsToggle"] = "ScanChannelsToggle";
      Key2["ScreenModeNext"] = "ScreenModeNext";
      Key2["Settings"] = "Settings";
      Key2["SplitScreenToggle"] = "SplitScreenToggle";
      Key2["STBInput"] = "STBInput";
      Key2["STBPower"] = "STBPower";
      Key2["Subtitle"] = "Subtitle";
      Key2["Teletext"] = "Teletext";
      Key2["VideoModeNext"] = "VideoModeNext";
      Key2["Wink"] = "Wink";
      Key2["ZoomToggle"] = "ZoomToggle";
      Key2["SpeechCorrectionList"] = "SpeechCorrectionList";
      Key2["SpeechInputToggle"] = "SpeechInputToggle";
      Key2["Close"] = "Close";
      Key2["New"] = "New";
      Key2["Open"] = "Open";
      Key2["Print"] = "Print";
      Key2["Save"] = "Save";
      Key2["SpellCheck"] = "SpellCheck";
      Key2["MailForward"] = "MailForward";
      Key2["MailReply"] = "MailReply";
      Key2["MailSend"] = "MailSend";
      Key2["LaunchCalculator"] = "LaunchCalculator";
      Key2["LaunchCalendar"] = "LaunchCalendar";
      Key2["LaunchContacts"] = "LaunchContacts";
      Key2["LaunchMail"] = "LaunchMail";
      Key2["LaunchMediaPlayer"] = "LaunchMediaPlayer";
      Key2["LaunchMusicPlayer"] = "LaunchMusicPlayer";
      Key2["LaunchMyComputer"] = "LaunchMyComputer";
      Key2["LaunchPhone"] = "LaunchPhone";
      Key2["LaunchScreenSaver"] = "LaunchScreenSaver";
      Key2["LaunchSpreadsheet"] = "LaunchSpreadsheet";
      Key2["LaunchWebBrowser"] = "LaunchWebBrowser";
      Key2["LaunchWebCam"] = "LaunchWebCam";
      Key2["LaunchWordProcessor"] = "LaunchWordProcessor";
      Key2["LaunchApplication1"] = "LaunchApplication1";
      Key2["LaunchApplication2"] = "LaunchApplication2";
      Key2["LaunchApplication3"] = "LaunchApplication3";
      Key2["LaunchApplication4"] = "LaunchApplication4";
      Key2["LaunchApplication5"] = "LaunchApplication5";
      Key2["LaunchApplication6"] = "LaunchApplication6";
      Key2["LaunchApplication7"] = "LaunchApplication7";
      Key2["LaunchApplication8"] = "LaunchApplication8";
      Key2["LaunchApplication9"] = "LaunchApplication9";
      Key2["LaunchApplication10"] = "LaunchApplication10";
      Key2["LaunchApplication11"] = "LaunchApplication11";
      Key2["LaunchApplication12"] = "LaunchApplication12";
      Key2["LaunchApplication13"] = "LaunchApplication13";
      Key2["LaunchApplication14"] = "LaunchApplication14";
      Key2["LaunchApplication15"] = "LaunchApplication15";
      Key2["LaunchApplication16"] = "LaunchApplication16";
      Key2["BrowserBack"] = "BrowserBack";
      Key2["BrowserFavorites"] = "BrowserFavorites";
      Key2["BrowserForward"] = "BrowserForward";
      Key2["BrowserHome"] = "BrowserHome";
      Key2["BrowserRefresh"] = "BrowserRefresh";
      Key2["BrowserSearch"] = "BrowserSearch";
      Key2["BrowserStop"] = "BrowserStop";
      Key2["Decimal"] = "Decimal";
      Key2["Key11"] = "Key11";
      Key2["Key12"] = "Key12";
      Key2["Multiply"] = "Multiply";
      Key2["Add"] = "Add";
      Key2["Divide"] = "Divide";
      Key2["Subtract"] = "Subtract";
      Key2["Separator"] = "Separator";
    })(Key = exports.Key || (exports.Key = {}));
  }
});
export default require_Key_enum();
//# sourceMappingURL=ts-key-enum.js.map

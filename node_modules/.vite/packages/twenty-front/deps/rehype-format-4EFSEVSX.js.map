{"version": 3, "sources": ["../../../../hast-util-minify-whitespace/node_modules/unist-util-is/lib/index.js", "../../../../hast-util-minify-whitespace/lib/block.js", "../../../../hast-util-minify-whitespace/lib/content.js", "../../../../hast-util-minify-whitespace/lib/skippable.js", "../../../../hast-util-minify-whitespace/lib/index.js", "../../../../hast-util-format/lib/index.js", "../../../../rehype-format/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n", "// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nexport const blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n", "export const content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n", "export const skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n", "/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {isElement} from 'hast-util-is-element'\nimport {whitespace} from 'hast-util-whitespace'\nimport {convert} from 'unist-util-is'\nimport {blocks} from './block.js'\nimport {content as contents} from './content.js'\nimport {skippable as skippables} from './skippable.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = convert(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport function minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!whitespace(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return embedded(node) || isElement(node, contents)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return isElement(node, blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    isElement(node, skippables)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n", "/**\n * @import {Nodes, RootContent, Root} from 'hast'\n * @import {BuildVisitor} from 'unist-util-visit-parents'\n * @import {Options, State} from './types.js'\n */\n\nimport {embedded} from 'hast-util-embedded'\nimport {minifyWhitespace} from 'hast-util-minify-whitespace'\nimport {phrasing} from 'hast-util-phrasing'\nimport {whitespace} from 'hast-util-whitespace'\nimport {whitespaceSensitiveTagNames} from 'html-whitespace-sensitive-tag-names'\nimport {SKIP, visitParents} from 'unist-util-visit-parents'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Root} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport function format(tree, options) {\n  const settings = options || emptyOptions\n\n  /** @type {State} */\n  const state = {\n    blanks: settings.blanks || [],\n    head: false,\n    indentInitial: settings.indentInitial !== false,\n    indent:\n      typeof settings.indent === 'number'\n        ? ' '.repeat(settings.indent)\n        : typeof settings.indent === 'string'\n          ? settings.indent\n          : '  '\n  }\n\n  minifyWhitespace(tree, {newlines: true})\n\n  visitParents(tree, visitor)\n\n  /**\n   * @type {BuildVisitor<Root>}\n   */\n  function visitor(node, parents) {\n    if (!('children' in node)) {\n      return\n    }\n\n    if (node.type === 'element' && node.tagName === 'head') {\n      state.head = true\n    }\n\n    if (state.head && node.type === 'element' && node.tagName === 'body') {\n      state.head = false\n    }\n\n    if (\n      node.type === 'element' &&\n      whitespaceSensitiveTagNames.includes(node.tagName)\n    ) {\n      return SKIP\n    }\n\n    // Don’t indent content of whitespace-sensitive nodes / inlines.\n    if (node.children.length === 0 || !padding(state, node)) {\n      return\n    }\n\n    let level = parents.length\n\n    if (!state.indentInitial) {\n      level--\n    }\n\n    let eol = false\n\n    // Indent newlines in `text`.\n    for (const child of node.children) {\n      if (child.type === 'comment' || child.type === 'text') {\n        if (child.value.includes('\\n')) {\n          eol = true\n        }\n\n        child.value = child.value.replace(\n          / *\\n/g,\n          '$&' + state.indent.repeat(level)\n        )\n      }\n    }\n\n    /** @type {Array<RootContent>} */\n    const result = []\n    /** @type {RootContent | undefined} */\n    let previous\n\n    for (const child of node.children) {\n      if (padding(state, child) || (eol && !previous)) {\n        addBreak(result, level, child)\n        eol = true\n      }\n\n      previous = child\n      result.push(child)\n    }\n\n    if (previous && (eol || padding(state, previous))) {\n      // Ignore trailing whitespace (if that already existed), as we’ll add\n      // properly indented whitespace.\n      if (whitespace(previous)) {\n        result.pop()\n        previous = result[result.length - 1]\n      }\n\n      addBreak(result, level - 1)\n    }\n\n    node.children = result\n  }\n\n  /**\n   * @param {Array<RootContent>} list\n   *   Nodes.\n   * @param {number} level\n   *   Indentation level.\n   * @param {RootContent | undefined} [next]\n   *   Next node.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addBreak(list, level, next) {\n    const tail = list[list.length - 1]\n    const previous = tail && whitespace(tail) ? list[list.length - 2] : tail\n    const replace =\n      (blank(state, previous) && blank(state, next) ? '\\n\\n' : '\\n') +\n      state.indent.repeat(Math.max(level, 0))\n\n    if (tail && tail.type === 'text') {\n      tail.value = whitespace(tail) ? replace : tail.value + replace\n    } else {\n      list.push({type: 'text', value: replace})\n    }\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes | undefined} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is a blank.\n */\nfunction blank(state, node) {\n  return Boolean(\n    node &&\n      node.type === 'element' &&\n      state.blanks.length > 0 &&\n      state.blanks.includes(node.tagName)\n  )\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` should be padded.\n */\nfunction padding(state, node) {\n  return (\n    node.type === 'root' ||\n    (node.type === 'element'\n      ? state.head ||\n        node.tagName === 'script' ||\n        embedded(node) ||\n        !phrasing(node)\n      : false)\n  )\n}\n", "/**\n * @import {Options} from 'hast-util-format'\n * @import {Root} from 'hast'\n */\n\nimport {format} from 'hast-util-format'\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeFormat(options) {\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    format(tree, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA2IO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBT,SAAU,MAAM;AACd,QAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,IAAI,IAAI,aAAa,IAAI;AAAA,IACnE;AAEA,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AAAA;AAOJ,SAAS,WAAW,OAAO;AAEzB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,WAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,CAAC;AAAA,EACtC;AAEA,SAAO,YAAY,GAAG;AAMtB,WAAS,OAAO,YAAY;AAC1B,QAAIA,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAOA,MAAK,EAAE,MAAM,MAAM,UAAU,EAAG,QAAO;AAAA,IACpD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,aAAa,OAAO;AAC3B,QAAM;AAAA;AAAA,IAAwD;AAAA;AAE9D,SAAO,YAAYC,IAAG;AAMtB,WAASA,KAAI,MAAM;AACjB,UAAM;AAAA;AAAA;AAAA,MACoB;AAAA;AAI1B,QAAI;AAEJ,SAAK,OAAO,OAAO;AACjB,UAAI,aAAa,GAAG,MAAM,cAAc,GAAG,EAAG,QAAO;AAAA,IACvD;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,YAAY,OAAO;AAC1B,SAAO,YAAY,IAAI;AAKvB,WAAS,KAAK,MAAM;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B;AACF;AAQA,SAAS,YAAY,cAAc;AACjC,SAAO;AAMP,WAAS,MAAM,OAAO,OAAO,QAAQ;AACnC,WAAO;AAAA,MACL,eAAe,KAAK,KAClB,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA,OAAO,UAAU,WAAW,QAAQ;AAAA,QACpC,UAAU;AAAA,MACZ;AAAA,IACJ;AAAA,EACF;AACF;AAEA,SAAS,KAAK;AACZ,SAAO;AACT;AAMA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE;;;ACjSO,IAAM,SAAS;AAAA,EACpB;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AACF;;;AC5DO,IAAM,UAAU;AAAA;AAAA,EAErB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACNO,IAAM,YAAY;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACgCA,IAAM,eAAe,CAAC;AACtB,IAAM,gBAAgB,QAAQ,CAAC,WAAW,SAAS,CAAC;AAY7C,SAAS,iBAAiB,MAAM,SAAS;AAC9C,QAAM,WAAW,WAAW;AAE5B,SAAO,MAAM;AAAA,IACX,UAAU;AAAA,MACR,SAAS,WAAW,kBAAkB;AAAA,IACxC;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAUA,SAAS,OAAO,MAAM,OAAO;AAC3B,MAAI,cAAc,MAAM;AACtB,UAAM,WAAW,EAAC,GAAG,MAAK;AAE1B,QAAI,KAAK,SAAS,UAAU,UAAU,IAAI,GAAG;AAC3C,eAAS,SAAS;AAClB,eAAS,QAAQ;AAAA,IACnB;AAEA,aAAS,aAAa,gBAAgB,MAAM,KAAK;AAEjD,WAAO,IAAI,MAAM,QAAQ;AAAA,EAC3B;AAEA,MAAI,KAAK,SAAS,QAAQ;AACxB,QAAI,MAAM,eAAe,UAAU;AACjC,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B;AAGA,QAAI,MAAM,eAAe,UAAU;AACjC,WAAK,QAAQ,MAAM,SAAS,KAAK,KAAK;AAAA,IACxC;AAAA,EAIF;AAEA,SAAO,EAAC,QAAQ,cAAc,IAAI,GAAG,cAAc,OAAO,QAAQ,MAAK;AACzE;AAUA,SAAS,WAAW,MAAM,OAAO;AAC/B,QAAM,QAAQ,MAAM,SAAS,KAAK,KAAK;AACvC,QAAM,SAAS,EAAC,QAAQ,OAAO,cAAc,OAAO,QAAQ,MAAK;AACjE,MAAI,QAAQ;AACZ,MAAI,MAAM,MAAM;AAEhB,MAAI,MAAM,UAAU,UAAU,MAAM,OAAO,CAAC,CAAC,GAAG;AAC9C;AAAA,EACF;AAEA,MAAI,UAAU,OAAO,UAAU,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG;AACrD,QAAI,MAAM,OAAO;AACf;AAAA,IACF,OAAO;AACL,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,UAAU,KAAK;AACjB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,SAAK,QAAQ,MAAM,MAAM,OAAO,GAAG;AAAA,EACrC;AAEA,SAAO;AACT;AAUA,SAAS,IAAI,QAAQ,OAAO;AAC1B,MAAI,SAAS,MAAM;AACnB,QAAM,QAAQ,MAAM;AACpB,QAAM,WAAW,OAAO;AACxB,MAAI,SAAS,SAAS;AACtB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ;AACvB,UAAM,SAAS,OAAO,SAAS,KAAK,GAAG;AAAA,MACrC,GAAG;AAAA,MACH,OAAO,iBAAiB,UAAU,OAAO,KAAK;AAAA,MAC9C;AAAA,IACF,CAAC;AAED,QAAI,OAAO,QAAQ;AACjB,eAAS,OAAO,OAAO,CAAC;AACxB;AACA;AAAA,IACF,WAAW,CAAC,OAAO,QAAQ;AACzB,eAAS,OAAO;AAAA,IAClB;AAIA,QAAIC,SAAQ,SAAS,KAAK,CAAC,GAAG;AAC5B,eAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO,EAAC,QAAQ,OAAO,cAAc,QAAQ,UAAU,KAAK,GAAG,QAAQ,MAAK;AAC9E;AAYA,SAAS,iBAAiB,OAAO,OAAO,OAAO;AAC7C,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAM,OAAO,MAAM,KAAK;AACxB,QAAI,SAAS,cAAc,IAAI;AAE/B,QAAI,WAAW,UAAa,cAAc,QAAQ,CAACC,WAAU,IAAI,GAAG;AAClE,eAAS,iBAAiB,KAAK,UAAU,EAAE;AAAA,IAC7C;AAEA,QAAI,OAAO,WAAW,WAAW;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAgBA,SAAS,cAAc,MAAM;AAC3B,MAAI,KAAK,SAAS,WAAW;AAC3B,QAAID,SAAQ,IAAI,GAAG;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO;AAAA,IACT;AAAA,EAIF,WAAW,KAAK,SAAS,QAAQ;AAC/B,QAAI,CAAC,WAAW,IAAI,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF,WAAW,CAAC,cAAc,IAAI,GAAG;AAC/B,WAAO;AAAA,EACT;AACF;AAUA,SAASA,SAAQ,MAAM;AACrB,SAAO,SAAS,IAAI,KAAK,UAAU,MAAM,OAAQ;AACnD;AAUA,SAAS,UAAU,MAAM;AACvB,SAAO,UAAU,MAAM,MAAM;AAC/B;AAQA,SAASC,WAAU,MAAM;AACvB,SACE,QAAQ,KAAK,SAAS,aAAa,KAAK,WAAW,MAAM,KACzD,cAAc,IAAI,KAClB,UAAU,MAAM,SAAU;AAE9B;AAQA,SAAS,UAAU,WAAW;AAC5B,SAAO,cAAc,OAAO,cAAc;AAC5C;AAKA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,QAAQ,WAAW,KAAK,KAAK;AACnC,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC5B;AAKA,SAAS,oBAAoB;AAC3B,SAAO;AACT;AAOA,SAAS,gBAAgB,SAAS;AAChC,SAAO;AAKP,WAAS,SAAS,OAAO;AACvB,WAAO,OAAO,KAAK,EAAE,QAAQ,mBAAmB,OAAO;AAAA,EACzD;AACF;AAaA,SAAS,gBAAgB,MAAM,OAAO;AACpC,MAAI,aAAa,QAAQ,KAAK,YAAY;AACxC,YAAQ,KAAK,SAAS;AAAA;AAAA;AAAA,MAGpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,OAAO;AACV,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,OAAO;AACV,eAAO,KAAK,WAAW,OAAO,aAAa;AAAA,MAC7C;AAAA,MAEA,KAAK;AAAA,MACL,KAAK,MAAM;AACT,eAAO,KAAK,WAAW,SAAS,WAAW,MAAM;AAAA,MACnD;AAAA,MAEA,KAAK,YAAY;AACf,eAAO;AAAA,MACT;AAAA,MAEA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,MAAM;AACf;;;AC1WA,IAAMC,gBAAe,CAAC;AAYf,SAAS,OAAO,MAAM,SAAS;AACpC,QAAM,WAAW,WAAWA;AAG5B,QAAM,QAAQ;AAAA,IACZ,QAAQ,SAAS,UAAU,CAAC;AAAA,IAC5B,MAAM;AAAA,IACN,eAAe,SAAS,kBAAkB;AAAA,IAC1C,QACE,OAAO,SAAS,WAAW,WACvB,IAAI,OAAO,SAAS,MAAM,IAC1B,OAAO,SAAS,WAAW,WACzB,SAAS,SACT;AAAA,EACV;AAEA,mBAAiB,MAAM,EAAC,UAAU,KAAI,CAAC;AAEvC,eAAa,MAAM,OAAO;AAK1B,WAAS,QAAQ,MAAM,SAAS;AAC9B,QAAI,EAAE,cAAc,OAAO;AACzB;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,aAAa,KAAK,YAAY,QAAQ;AACtD,YAAM,OAAO;AAAA,IACf;AAEA,QAAI,MAAM,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,QAAQ;AACpE,YAAM,OAAO;AAAA,IACf;AAEA,QACE,KAAK,SAAS,aACd,4BAA4B,SAAS,KAAK,OAAO,GACjD;AACA,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,SAAS,WAAW,KAAK,CAAC,QAAQ,OAAO,IAAI,GAAG;AACvD;AAAA,IACF;AAEA,QAAI,QAAQ,QAAQ;AAEpB,QAAI,CAAC,MAAM,eAAe;AACxB;AAAA,IACF;AAEA,QAAI,MAAM;AAGV,eAAW,SAAS,KAAK,UAAU;AACjC,UAAI,MAAM,SAAS,aAAa,MAAM,SAAS,QAAQ;AACrD,YAAI,MAAM,MAAM,SAAS,IAAI,GAAG;AAC9B,gBAAM;AAAA,QACR;AAEA,cAAM,QAAQ,MAAM,MAAM;AAAA,UACxB;AAAA,UACA,OAAO,MAAM,OAAO,OAAO,KAAK;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAGA,UAAM,SAAS,CAAC;AAEhB,QAAI;AAEJ,eAAW,SAAS,KAAK,UAAU;AACjC,UAAI,QAAQ,OAAO,KAAK,KAAM,OAAO,CAAC,UAAW;AAC/C,iBAAS,QAAQ,OAAO,KAAK;AAC7B,cAAM;AAAA,MACR;AAEA,iBAAW;AACX,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,QAAI,aAAa,OAAO,QAAQ,OAAO,QAAQ,IAAI;AAGjD,UAAI,WAAW,QAAQ,GAAG;AACxB,eAAO,IAAI;AACX,mBAAW,OAAO,OAAO,SAAS,CAAC;AAAA,MACrC;AAEA,eAAS,QAAQ,QAAQ,CAAC;AAAA,IAC5B;AAEA,SAAK,WAAW;AAAA,EAClB;AAYA,WAAS,SAAS,MAAM,OAAO,MAAM;AACnC,UAAM,OAAO,KAAK,KAAK,SAAS,CAAC;AACjC,UAAM,WAAW,QAAQ,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,IAAI;AACpE,UAAM,WACH,MAAM,OAAO,QAAQ,KAAK,MAAM,OAAO,IAAI,IAAI,SAAS,QACzD,MAAM,OAAO,OAAO,KAAK,IAAI,OAAO,CAAC,CAAC;AAExC,QAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,WAAK,QAAQ,WAAW,IAAI,IAAI,UAAU,KAAK,QAAQ;AAAA,IACzD,OAAO;AACL,WAAK,KAAK,EAAC,MAAM,QAAQ,OAAO,QAAO,CAAC;AAAA,IAC1C;AAAA,EACF;AACF;AAUA,SAAS,MAAM,OAAO,MAAM;AAC1B,SAAO;AAAA,IACL,QACE,KAAK,SAAS,aACd,MAAM,OAAO,SAAS,KACtB,MAAM,OAAO,SAAS,KAAK,OAAO;AAAA,EACtC;AACF;AAUA,SAAS,QAAQ,OAAO,MAAM;AAC5B,SACE,KAAK,SAAS,WACb,KAAK,SAAS,YACX,MAAM,QACN,KAAK,YAAY,YACjB,SAAS,IAAI,KACb,CAAC,SAAS,IAAI,IACd;AAER;;;AC1Ke,SAAR,aAA8B,SAAS;AAS5C,SAAO,SAAU,MAAM;AACrB,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;", "names": ["index", "all", "content", "skippable", "emptyOptions"]}
{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/elixir/elixir.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  // Below are lists/regexps to which we reference later.\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  // Matches any of the operator names:\n  // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  // The keys below correspond to tokenizer states.\n  // We start from the root state and match against its rules\n  // until we explicitly transition into another state.\n  // The `include` simply brings in all operations from the given state\n  // and is useful for improving readability.\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      // Keywords start as either an identifier or a string,\n      // but end with a : so it's important to match this first.\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    // Whitespace\n    whitespace: [[/\\s+/, \"white\"]],\n    // Comments\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    // Keyword list shorthand\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      // Use positive look-ahead to ensure the string is followed by :\n      // and should be considered a keyword.\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Numbers\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    // Identifiers\n    identifiers: [\n      // Tokenize identifier name in function-like definitions.\n      // Note: given `def a + b, do: nil`, `a` is not a function name,\n      // so we use negative look-ahead to ensure there's no operator.\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      // Tokenize function calls\n      [\n        // In-scope call - an identifier followed by ( or .(\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        // Referencing function in a module\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Referencing function in an Erlang module\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Piping into a function (tokenized separately as it may not have parentheses)\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        // Function reference passed to another function\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      // Language keywords, builtins, constants and variables\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Module names\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    // Strings\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    // Atoms\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Sigils\n    // See https://elixir-lang.org/getting-started/sigils.html\n    // Sigils allow for typing values using their textual representation.\n    // All sigils start with ~ followed by a letter or\n    // multi-letter uppercase starting at Elixir v1.15.0, indicating sigil type\n    // and then a delimiter pair enclosing the textual representation.\n    // Optional modifiers are allowed after the closing delimiter.\n    // For instance a regular expressions can be written as:\n    // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n    //\n    // In general lowercase sigils allow for interpolation\n    // and escaped characters, whereas uppercase sigils don't\n    //\n    // During tokenization we want to distinguish some\n    // specific sigil types, namely string and regexp,\n    // so that they cen be themed separately.\n    //\n    // To reasonably handle all those combinations we leverage\n    // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n    // then \"sigilStart.interpol.s\" state will match and also all\n    // the individual dot-separated parameters can be accessed.\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    // The definitions below expect states to be of the form:\n    //\n    // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    //\n    // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n    // and immediately switches to the sigilContinue sate, which handles the actual content\n    // and waits for the corresponding end delimiter.\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    // Fallback to the generic sigil by default\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    // Attributes\n    attributes: [\n      // Module @doc* attributes - tokenized as comments\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      // Module attributes\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    // Operators, punctuation, brackets\n    symbols: [\n      // Code point operator (either with regular character ?a or an escaped one ?\\n)\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      // Anonymous function arguments\n      [/&\\d+/, \"operator\"],\n      // Bitshift operators (must go before delimiters, so that << >> don't match first)\n      [/<<<|>>>/, \"operator\"],\n      // Delimiter pairs\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n      [/\\.\\.\\./, \"identifier\"],\n      // Punctuation => (must go before operators, so it's not tokenized as = then >)\n      [/=>/, \"punctuation\"],\n      // Operators\n      [/@operator/, \"operator\"],\n      // Punctuation\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    // Generic helpers\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      // # may be a regular regexp char, so we use a heuristic\n      // assuming a # surrounded by whitespace is actually a comment.\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      // Interpolation brackets may contain arbitrary code,\n      // so we simply match against all the root rules,\n      // until we reach interpolation end (the above matches).\n      { include: \"@root\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA+BA;AAxCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,SAAS,EAAE;AAAA,QAC5C,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,QAC5B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,MAC5B;AAAA,MACA,kBAAkB;AAAA,QAChB,uBAAuB;AAAA,QACvB,uBAAuB;AAAA,MACzB;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,QACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,QAClD,EAAE,MAAM,MAAM,OAAO,MAAM,OAAO,0BAA0B;AAAA,MAC9D;AAAA;AAAA,MAEA,qBAAqB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB,CAAC,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,MACnD,mBAAmB,CAAC,SAAS,UAAU,WAAW,KAAK;AAAA,MACvD,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,SAAS,KAAK;AAAA,MAClC,aAAa,CAAC,cAAc,WAAW,WAAW,cAAc,gBAAgB;AAAA;AAAA;AAAA,MAGhF,UAAU;AAAA;AAAA,MAEV,cAAc;AAAA;AAAA,MAEd,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAEZ,yBAAyB;AAAA,MACzB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA;AAAA,MAER,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMR,WAAW;AAAA,QACT,MAAM;AAAA,UACJ,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,YAAY;AAAA;AAAA;AAAA,UAGvB,EAAE,SAAS,qBAAqB;AAAA,UAChC,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,eAAe;AAAA,UAC1B,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,SAAS;AAAA,UACpB,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,WAAW;AAAA,QACxB;AAAA;AAAA,QAEA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA;AAAA,QAE7B,UAAU,CAAC,CAAC,WAAW,CAAC,uBAAuB,SAAS,CAAC,CAAC;AAAA;AAAA,QAE1D,mBAAmB;AAAA,UACjB,CAAC,uBAAuB,CAAC,YAAY,wBAAwB,OAAO,CAAC;AAAA;AAAA;AAAA,UAGrE;AAAA,YACE;AAAA,YACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,UACpE;AAAA,UACA;AAAA,YACE;AAAA,YACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,UACpE;AAAA,QACF;AAAA,QACA,2BAA2B;AAAA,UACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,UACpD,EAAE,SAAS,iCAAiC;AAAA,QAC9C;AAAA,QACA,2BAA2B;AAAA,UACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,UACpD,EAAE,SAAS,iCAAiC;AAAA,QAC9C;AAAA;AAAA,QAEA,SAAS;AAAA,UACP,CAAC,aAAa,eAAe;AAAA,UAC7B,CAAC,YAAY,cAAc;AAAA,UAC3B,CAAC,UAAU,YAAY;AAAA,UACvB,CAAC,uCAAuC,cAAc;AAAA,UACtD,CAAC,YAAY,QAAQ;AAAA,QACvB;AAAA;AAAA,QAEA,aAAa;AAAA;AAAA;AAAA;AAAA,UAIX;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA;AAAA,YAEE;AAAA,YACA;AAAA,cACE,OAAO;AAAA;AAAA,gBAEL,wBAAwB;AAAA,gBACxB,sBAAsB;AAAA,gBACtB,kBAAkB;AAAA,gBAClB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE;AAAA,YACA,CAAC,mBAAmB,SAAS,YAAY,SAAS,eAAe;AAAA,UACnE;AAAA,UACA;AAAA;AAAA,YAEE;AAAA,YACA,CAAC,wBAAwB,YAAY,SAAS,YAAY,SAAS,eAAe;AAAA,UACpF;AAAA,UACA;AAAA;AAAA,YAEE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,kBACL,kBAAkB;AAAA,kBAClB,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE;AAAA,YACA,CAAC,YAAY,SAAS,eAAe;AAAA,UACvC;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,wBAAwB;AAAA,gBACxB,qBAAqB;AAAA,gBACrB,sBAAsB;AAAA,gBACtB,kBAAkB;AAAA,gBAClB,cAAc;AAAA,gBACd,gBAAgB;AAAA,gBAChB,OAAO;AAAA,gBACP,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,eAAe,iBAAiB;AAAA,QACnC;AAAA;AAAA,QAEA,SAAS;AAAA,UACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,UACnE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,UACnE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,UAChE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,QAClE;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACnD,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACnD,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACjD,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,UACjD,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA;AAAA,QAEA,OAAO;AAAA,UACL,CAAC,kBAAkB,CAAC,wBAAwB,UAAU,CAAC;AAAA,UACvD,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,UACvE,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,QACzE;AAAA,QACA,wBAAwB;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,UACnD,EAAE,SAAS,iCAAiC;AAAA,QAC9C;AAAA,QACA,wBAAwB;AAAA,UACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,UACnD,EAAE,SAAS,iCAAiC;AAAA,QAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBA,QAAQ;AAAA,UACN,CAAC,8BAA8B,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,UAC7E,CAAC,iCAAiC,EAAE,OAAO,YAAY,MAAM,oBAAoB,CAAC;AAAA,QACpF;AAAA,QACA,OAAO;AAAA,UACL,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,UAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,UAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,UAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,UAC/E;AAAA,YACE;AAAA,YACA,EAAE,OAAO,YAAY,UAAU,2BAA2B;AAAA,UAC5D;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,yBAAyB;AAAA,UACvB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,4BAA4B;AAAA,UAC1B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,2BAA2B;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,8BAA8B;AAAA;AAAA,UAE5B,CAAC,iCAAiC,QAAQ;AAAA,UAC1C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,yBAAyB;AAAA,UACvB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,4BAA4B;AAAA,UAC1B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,2BAA2B;AAAA,UACzB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,8BAA8B;AAAA;AAAA,UAE5B,CAAC,iCAAiC,QAAQ;AAAA,UAC1C;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,gBACrD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,0BAA0B;AAAA,UACxB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,gBACpD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,wBAAwB;AAAA,QACrC;AAAA,QACA,yBAAyB;AAAA,UACvB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,4BAA4B;AAAA;AAAA,UAE1B,CAAC,iCAAiC,OAAO;AAAA,UACzC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,gBACpD,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA,EAAE,SAAS,gBAAgB;AAAA,QAC7B;AAAA;AAAA,QAEA,YAAY;AAAA;AAAA,UAEV;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,CAAC,6BAA6B,6BAA6B;AAAA;AAAA,UAE3D,CAAC,qBAAqB,UAAU;AAAA,QAClC;AAAA,QACA,8BAA8B;AAAA,UAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,UAC9D,EAAE,SAAS,oBAAoB;AAAA,QACjC;AAAA,QACA,8BAA8B;AAAA,UAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,UAC9D,EAAE,SAAS,oBAAoB;AAAA,QACjC;AAAA,QACA,6BAA6B;AAAA,UAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,UAC5D,EAAE,SAAS,oBAAoB;AAAA,QACjC;AAAA,QACA,6BAA6B;AAAA,UAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,UAC5D,EAAE,SAAS,oBAAoB;AAAA,QACjC;AAAA;AAAA,QAEA,SAAS;AAAA;AAAA,UAEP,CAAC,mBAAmB,iBAAiB;AAAA;AAAA,UAErC,CAAC,QAAQ,UAAU;AAAA;AAAA,UAEnB,CAAC,WAAW,UAAU;AAAA;AAAA,UAEtB,CAAC,sBAAsB,WAAW;AAAA;AAAA,UAElC,CAAC,UAAU,YAAY;AAAA;AAAA,UAEvB,CAAC,MAAM,aAAa;AAAA;AAAA,UAEpB,CAAC,aAAa,UAAU;AAAA;AAAA,UAExB,CAAC,WAAW,aAAa;AAAA,QAC3B;AAAA;AAAA,QAEA,uBAAuB;AAAA,UACrB,EAAE,SAAS,iBAAiB;AAAA,UAC5B,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,eAAe,CAAC,CAAC,KAAK,QAAQ,CAAC;AAAA,QAC/B,+BAA+B;AAAA,UAC7B,EAAE,SAAS,iBAAiB;AAAA,UAC5B,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,yBAAyB;AAAA,QACtC;AAAA,QACA,uBAAuB,CAAC,CAAC,KAAK,UAAU,CAAC;AAAA,QACzC,uBAAuB;AAAA,UACrB,EAAE,SAAS,iBAAiB;AAAA,UAC5B,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,eAAe;AAAA;AAAA;AAAA,UAGb,CAAC,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,CAAC;AAAA,UAC9D,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,sBAAsB;AAAA,UACpB,EAAE,SAAS,iBAAiB;AAAA,UAC5B,EAAE,SAAS,cAAc;AAAA,UACzB,EAAE,SAAS,gBAAgB;AAAA,QAC7B;AAAA,QACA,cAAc,CAAC,CAAC,KAAK,OAAO,CAAC;AAAA,QAC7B,kBAAkB,CAAC,CAAC,KAAK,6BAA6B,CAAC;AAAA,QACvD,YAAY,CAAC,CAAC,WAAW,2BAA2B,CAAC;AAAA,QACrD,eAAe,CAAC,CAAC,MAAM,EAAE,OAAO,2BAA2B,MAAM,yBAAyB,CAAC,CAAC;AAAA,QAC5F,uBAAuB;AAAA,UACrB,CAAC,KAAK,EAAE,OAAO,2BAA2B,MAAM,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,UAIxD,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
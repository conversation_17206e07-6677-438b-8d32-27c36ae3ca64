{"version": 3, "sources": ["../../../../micro-memoize/src/utils.ts", "../../../../micro-memoize/src/Cache.ts", "../../../../micro-memoize/src/index.ts", "../../../../fast-equals/src/utils.ts", "../../../../fast-equals/src/comparator.ts", "../../../../fast-equals/src/index.ts", "../../../../moize/src/constants.ts", "../../../../moize/src/utils.ts", "../../../../moize/src/maxAge.ts", "../../../../moize/src/stats.ts", "../../../../moize/src/instance.ts", "../../../../moize/src/component.ts", "../../../../moize/src/maxArgs.ts", "../../../../moize/src/serialize.ts", "../../../../moize/src/options.ts", "../../../../moize/src/updateCacheForKey.ts", "../../../../moize/src/index.ts"], "sourcesContent": [null, null, null, "import type { EqualityComparator, InternalEqualityComparator } from './types';\n\ninterface Cache {\n  delete: (key: object) => void;\n  get: (key: object) => object | undefined;\n  set: (key: object, value: object) => void;\n}\n\nconst HAS_WEAK_MAP_SUPPORT = typeof WeakMap === 'function';\n\nconst { keys } = Object;\n\n/**\n * are the values passed strictly equal or both NaN\n *\n * @param a the value to compare against\n * @param b the value to test\n * @returns are the values equal by the SameValueZero principle\n */\nexport function sameValueZeroEqual(a: any, b: any) {\n  return a === b || (a !== a && b !== b);\n}\n\n/**\n * is the value a plain object\n *\n * @param value the value to test\n * @returns is the value a plain object\n */\nexport function isPlainObject(value: any) {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * is the value promise-like (meaning it is thenable)\n *\n * @param value the value to test\n * @returns is the value promise-like\n */\nexport function isPromiseLike(value: any) {\n  return !!value && typeof value.then === 'function';\n}\n\n/**\n * is the value passed a react element\n *\n * @param value the value to test\n * @returns is the value a react element\n */\nexport function isReactElement(value: any) {\n  return !!(value && value.$$typeof);\n}\n\n/**\n * in cases where WeakMap is not supported, creates a new custom\n * object that mimics the necessary API aspects for cache purposes\n *\n * @returns the new cache object\n */\nexport function getNewCacheFallback(): Cache {\n  const entries: [object, object][] = [];\n\n  return {\n    delete(key: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          entries.splice(index, 1);\n          return;\n        }\n      }\n    },\n\n    get(key: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          return entries[index][1];\n        }\n      }\n    },\n\n    set(key: object, value: object) {\n      for (let index = 0; index < entries.length; ++index) {\n        if (entries[index][0] === key) {\n          entries[index][1] = value;\n          return;\n        }\n      }\n\n      entries.push([key, value]);\n    }\n  };\n}\n\n/**\n * get a new cache object to prevent circular references\n *\n * @returns the new cache object\n */\nexport const getNewCache = ((canUseWeakMap: boolean) => {\n  if (canUseWeakMap) {\n    return function _getNewCache(): Cache {\n      return new WeakMap();\n    };\n  }\n\n  return getNewCacheFallback;\n})(HAS_WEAK_MAP_SUPPORT);\n\n/**\n * create a custom isEqual handler specific to circular objects\n *\n * @param [isEqual] the isEqual comparator to use instead of isDeepEqual\n * @returns the method to create the `isEqual` function\n */\nexport function createCircularEqualCreator(isEqual?: EqualityComparator) {\n  return function createCircularEqual(\n    comparator: EqualityComparator,\n  ): InternalEqualityComparator {\n    const _comparator = isEqual || comparator;\n\n    return function circularEqual(\n      a,\n      b,\n      indexOrKeyA,\n      indexOrKeyB,\n      parentA,\n      parentB,\n      cache: Cache = getNewCache(),\n    ) {\n      const isCacheableA = !!a && typeof a === 'object';\n      const isCacheableB = !!b && typeof b === 'object';\n\n      if (isCacheableA !== isCacheableB) {\n        return false;\n      }\n\n      if (!isCacheableA && !isCacheableB) {\n        return _comparator(a, b, cache);\n      }\n\n      const cachedA = cache.get(a);\n      \n      if(cachedA && cache.get(b)) {\n        return cachedA === b;\n      }\n\n      cache.set(a, b);\n      cache.set(b, a);\n\n      const result = _comparator(a, b, cache);\n\n      cache.delete(a);\n      cache.delete(b);\n\n      return result;\n    };\n  };\n}\n\n/**\n * are the arrays equal in value\n *\n * @param a the array to test\n * @param b the array to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the arrays equal\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], index, index, a, b, meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the maps equal in value\n *\n * @param a the map to test\n * @param b the map to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta map to pass through\n * @returns are the maps equal\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n    let indexA = 0;\n\n    a.forEach((aValue, aKey) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndexB = 0;\n\n        b.forEach((bValue, bKey) => {\n          if (!hasMatch && !matchedIndices[matchIndexB]) {\n            hasMatch =\n              isEqual(aKey, bKey, indexA, matchIndexB, a, b, meta) &&\n              isEqual(aValue, bValue, aKey, bKey, a, b, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndexB] = true;\n            }\n          }\n\n          matchIndexB++;\n        });\n\n        indexA++;\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n\ntype Dictionary<Type> = {\n  [key: string]: Type;\n  [index: number]: Type;\n};\n\nconst OWNER = '_owner';\n\nconst hasOwnProperty = Function.prototype.bind.call(\n  Function.prototype.call,\n  Object.prototype.hasOwnProperty,\n);\n\n/**\n * are the objects equal in value\n *\n * @param a the object to test\n * @param b the object to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta object to pass through\n * @returns are the objects equal\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  const keysA = keys(a);\n\n  let index = keysA.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  if (index) {\n    let key: string;\n\n    while (index-- > 0) {\n      key = keysA[index];\n\n      if (key === OWNER) {\n        const reactElementA = isReactElement(a);\n        const reactElementB = isReactElement(b);\n\n        if (\n          (reactElementA || reactElementB) &&\n          reactElementA !== reactElementB\n        ) {\n          return false;\n        }\n      }\n\n      if (\n        !hasOwnProperty(b, key) ||\n        !isEqual(a[key], b[key], key, key, a, b, meta)\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n\n/**\n * are the regExps equal in value\n *\n * @param a the regExp to test\n * @param b the regExp to test agains\n * @returns are the regExps equal\n */\nexport const areRegExpsEqual = (() => {\n  if (/foo/g.flags === 'g') {\n    return function areRegExpsEqual(a: RegExp, b: RegExp) {\n      return a.source === b.source && a.flags === b.flags;\n    };\n  }\n\n  return function areRegExpsEqualFallback(a: RegExp, b: RegExp) {\n    return (\n      a.source === b.source &&\n      a.global === b.global &&\n      a.ignoreCase === b.ignoreCase &&\n      a.multiline === b.multiline &&\n      a.unicode === b.unicode &&\n      a.sticky === b.sticky &&\n      a.lastIndex === b.lastIndex\n    );\n  };\n})();\n\n/**\n * are the sets equal in value\n *\n * @param a the set to test\n * @param b the set to test against\n * @param isEqual the comparator to determine equality\n * @param meta the meta set to pass through\n * @returns are the sets equal\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: InternalEqualityComparator,\n  meta: any,\n) {\n  let isValueEqual = a.size === b.size;\n\n  if (isValueEqual && a.size) {\n    const matchedIndices: Record<number, true> = {};\n\n    a.forEach((aValue, aKey) => {\n      if (isValueEqual) {\n        let hasMatch = false;\n        let matchIndex = 0;\n\n        b.forEach((bValue, bKey) => {\n          if (!hasMatch && !matchedIndices[matchIndex]) {\n            hasMatch = isEqual(aValue, bValue, aKey, bKey, a, b, meta);\n\n            if (hasMatch) {\n              matchedIndices[matchIndex] = true;\n            }\n          }\n\n          matchIndex++;\n        });\n\n        isValueEqual = hasMatch;\n      }\n    });\n  }\n\n  return isValueEqual;\n}\n", "import {\n  areArraysEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  isPlainObject,\n  isPromiseLike,\n  sameValueZeroEqual,\n} from './utils';\n\nimport type { EqualityComparator, InternalEqualityComparator } from './types';\n\nconst HAS_MAP_SUPPORT = typeof Map === 'function';\nconst HAS_SET_SUPPORT = typeof Set === 'function';\n\nconst { valueOf } = Object.prototype;\n\nexport type EqualityComparatorCreator = (\n  fn: EqualityComparator,\n) => InternalEqualityComparator;\n\nexport function createComparator(\n  createIsEqual?: EqualityComparatorCreator,\n): EqualityComparator {\n  const isEqual: InternalEqualityComparator =\n    /* eslint-disable no-use-before-define */\n    typeof createIsEqual === 'function'\n      ? createIsEqual(comparator)\n      : (\n          a: any,\n          b: any,\n          indexOrKeyA: any,\n          indexOrKeyB: any,\n          parentA: any,\n          parentB: any,\n          meta: any,\n        ) => comparator(a, b, meta);\n  /* eslint-enable */\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   *\n   * @param a the value to test against\n   * @param b the value to test\n   * @param [meta] an optional meta object that is passed through to all equality test calls\n   * @returns are a and b equivalent in value\n   */\n  function comparator(a: any, b: any, meta?: any) {\n    if (a === b) {\n      return true;\n    }\n\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n      if (isPlainObject(a) && isPlainObject(b)) {\n        return areObjectsEqual(a, b, isEqual, meta);\n      }\n\n      let aShape = Array.isArray(a);\n      let bShape = Array.isArray(b);\n\n      if (aShape || bShape) {\n        return aShape === bShape && areArraysEqual(a, b, isEqual, meta);\n      }\n\n      aShape = a instanceof Date;\n      bShape = b instanceof Date;\n\n      if (aShape || bShape) {\n        return (\n          aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime())\n        );\n      }\n\n      aShape = a instanceof RegExp;\n      bShape = b instanceof RegExp;\n\n      if (aShape || bShape) {\n        return aShape === bShape && areRegExpsEqual(a, b);\n      }\n\n      if (isPromiseLike(a) || isPromiseLike(b)) {\n        return a === b;\n      }\n\n      if (HAS_MAP_SUPPORT) {\n        aShape = a instanceof Map;\n        bShape = b instanceof Map;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areMapsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      if (HAS_SET_SUPPORT) {\n        aShape = a instanceof Set;\n        bShape = b instanceof Set;\n\n        if (aShape || bShape) {\n          return aShape === bShape && areSetsEqual(a, b, isEqual, meta);\n        }\n      }\n\n      if (a.valueOf !== valueOf || b.valueOf !== valueOf) {\n        return sameValueZeroEqual(a.valueOf(), b.valueOf());\n      }\n\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    return a !== a && b !== b;\n  }\n\n  return comparator;\n}\n", "import { createComparator } from './comparator';\nimport { createCircularEqualCreator, sameValueZeroEqual } from './utils';\n\nexport { createComparator as createCustomEqual, sameValueZeroEqual };\n\nexport const deepEqual = createComparator();\nexport const shallowEqual = createComparator(() => sameValueZeroEqual);\n\nexport const circularDeepEqual = createComparator(createCircularEqualCreator());\nexport const circularShallowEqual = createComparator(\n  createCircularEqualCreator(sameValueZeroEqual),\n);\n", "import type { AnyFn, Options } from '../index.d';\n\n/**\n * @private\n *\n * @constant DEFAULT_OPTIONS\n */\nexport const DEFAULT_OPTIONS: Options<AnyFn> = {\n    isDeepEqual: false,\n    isPromise: false,\n    isReact: false,\n    isSerialized: false,\n    isShallowEqual: false,\n    matchesArg: undefined,\n    matchesKey: undefined,\n    maxAge: undefined,\n    maxArgs: undefined,\n    maxSize: 1,\n    onExpire: undefined,\n    profileName: undefined,\n    serializer: undefined,\n    updateCacheForKey: undefined,\n    transformArgs: undefined,\n    updateExpire: false,\n};\n", "import { DEFAULT_OPTIONS } from './constants';\n\nimport type {\n    AnyFn,\n    Expiration,\n    IsEqual,\n    IsMatching<PERSON>ey,\n    Key,\n    Moizeable,\n    Moized,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * method to combine functions and return a single function that fires them all\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function combine<Args extends any[], Result>(\n    ...functions: Array<(...args: Args) => any>\n): ((...args: Args) => Result) | undefined {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      f.apply(this, arguments);\n                      g.apply(this, arguments);\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * method to compose functions and return a single function\n *\n * @param functions the functions to compose\n * @returns the composed function\n */\nexport function compose<Method>(...functions: Method[]): Method {\n    return functions.reduce(function (f: any, g: any) {\n        if (typeof f === 'function') {\n            return typeof g === 'function'\n                ? function (this: any) {\n                      return f(g.apply(this, arguments));\n                  }\n                : f;\n        }\n\n        if (typeof g === 'function') {\n            return g;\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * find the index of the expiration based on the key\n *\n * @param expirations the list of expirations\n * @param key the key to match\n * @returns the index of the expiration\n */\nexport function findExpirationIndex(expirations: Expiration[], key: Key) {\n    for (let index = 0; index < expirations.length; index++) {\n        if (expirations[index].key === key) {\n            return index;\n        }\n    }\n\n    return -1;\n}\n\n/**\n * @private\n *\n * @description\n * create function that finds the index of the key in the list of cache keys\n *\n * @param isEqual the function to test individual argument equality\n * @param isMatchingKey the function to test full key equality\n * @returns the function that finds the index of the key\n */\nexport function createFindKeyIndex(\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey | undefined\n) {\n    const areKeysEqual: IsMatchingKey =\n        typeof isMatchingKey === 'function'\n            ? isMatchingKey\n            : function (cacheKey: Key, key: Key) {\n                  for (let index = 0; index < key.length; index++) {\n                      if (!isEqual(cacheKey[index], key[index])) {\n                          return false;\n                      }\n                  }\n\n                  return true;\n              };\n\n    return function (keys: Key[], key: Key) {\n        for (let keysIndex = 0; keysIndex < keys.length; keysIndex++) {\n            if (\n                keys[keysIndex].length === key.length &&\n                areKeysEqual(keys[keysIndex], key)\n            ) {\n                return keysIndex;\n            }\n        }\n\n        return -1;\n    };\n}\n\ntype MergedOptions<\n    OriginalOptions extends Options<Moizeable>,\n    NewOptions extends Options<Moizeable>\n> = Omit<OriginalOptions, keyof NewOptions> & NewOptions;\n\n/**\n * @private\n *\n * @description\n * merge two options objects, combining or composing functions as necessary\n *\n * @param originalOptions the options that already exist on the method\n * @param newOptions the new options to merge\n * @returns the merged options\n */\nexport function mergeOptions<\n    OriginalOptions extends Options<Moizeable>,\n    NewOptions extends Options<Moizeable>\n>(\n    originalOptions: OriginalOptions,\n    newOptions: NewOptions | undefined\n): MergedOptions<OriginalOptions, NewOptions> {\n    if (!newOptions || newOptions === DEFAULT_OPTIONS) {\n        return originalOptions as unknown as MergedOptions<\n            OriginalOptions,\n            NewOptions\n        >;\n    }\n\n    return {\n        ...originalOptions,\n        ...newOptions,\n        onCacheAdd: combine(originalOptions.onCacheAdd, newOptions.onCacheAdd),\n        onCacheChange: combine(\n            originalOptions.onCacheChange,\n            newOptions.onCacheChange\n        ),\n        onCacheHit: combine(originalOptions.onCacheHit, newOptions.onCacheHit),\n        transformArgs: compose(\n            originalOptions.transformArgs,\n            newOptions.transformArgs\n        ),\n    };\n}\n\nexport function isMoized(\n    fn: Moizeable | Moized | Options<AnyFn>\n): fn is Moized {\n    return typeof fn === 'function' && (fn as Moizeable).isMoized;\n}\n\nexport function setName(\n    fn: Moized,\n    originalFunctionName: string,\n    profileName: string\n) {\n    try {\n        const name = profileName || originalFunctionName || 'anonymous';\n\n        Object.defineProperty(fn, 'name', {\n            configurable: true,\n            enumerable: false,\n            value: `moized(${name})`,\n            writable: true,\n        });\n    } catch {\n        // For engines where `function.name` is not configurable, do nothing.\n    }\n}\n", "import { createFindKeyIndex, findExpirationIndex } from './utils';\n\nimport type {\n    AnyFn,\n    Cache,\n    Expiration,\n    IsEqual,\n    IsMatching<PERSON>ey,\n    Key,\n    OnCacheOperation,\n    Options,\n} from '../index.d';\n\n/**\n * @private\n *\n * @description\n * clear an active expiration and remove it from the list if applicable\n *\n * @param expirations the list of expirations\n * @param key the key to clear\n * @param shouldRemove should the expiration be removed from the list\n */\nexport function clearExpiration(\n    expirations: Expiration[],\n    key: Key,\n    shouldRemove?: boolean\n) {\n    const expirationIndex = findExpirationIndex(expirations, key);\n\n    if (expirationIndex !== -1) {\n        clearTimeout(expirations[expirationIndex].timeoutId);\n\n        if (shouldRemove) {\n            expirations.splice(expirationIndex, 1);\n        }\n    }\n}\n\n/**\n * @private\n *\n * @description\n * Create the timeout for the given expiration method. If the ability to `unref`\n * exists, then apply it to avoid process locks in NodeJS.\n *\n * @param expirationMethod the method to fire upon expiration\n * @param maxAge the time to expire after\n * @returns the timeout ID\n */\nexport function createTimeout(expirationMethod: () => void, maxAge: number) {\n    const timeoutId = setTimeout(expirationMethod, maxAge);\n\n    if (typeof timeoutId.unref === 'function') {\n        timeoutId.unref();\n    }\n\n    return timeoutId;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that, when an item is added to the cache, adds an expiration for it\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @param isEqual the function to check argument equality\n * @param isMatchingKey the function to check complete key equality\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheAddSetExpiration<MoizeableFn extends AnyFn>(\n    expirations: Expiration[],\n    options: Options<MoizeableFn>,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): OnCacheOperation<MoizeableFn> {\n    const { maxAge } = options;\n\n    return function onCacheAdd(\n        cache: Cache<MoizeableFn>,\n        moizedOptions: Options<MoizeableFn>,\n        moized: MoizeableFn\n    ) {\n        const key: any = cache.keys[0];\n\n        if (findExpirationIndex(expirations, key) === -1) {\n            const expirationMethod = function () {\n                const findKeyIndex = createFindKeyIndex(isEqual, isMatchingKey);\n\n                const keyIndex: number = findKeyIndex(cache.keys, key);\n                const value: any = cache.values[keyIndex];\n\n                if (~keyIndex) {\n                    cache.keys.splice(keyIndex, 1);\n                    cache.values.splice(keyIndex, 1);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n\n                clearExpiration(expirations, key, true);\n\n                if (\n                    typeof options.onExpire === 'function' &&\n                    options.onExpire(key) === false\n                ) {\n                    cache.keys.unshift(key);\n                    cache.values.unshift(value);\n\n                    onCacheAdd(cache, moizedOptions, moized);\n\n                    if (typeof options.onCacheChange === 'function') {\n                        options.onCacheChange(cache, moizedOptions, moized);\n                    }\n                }\n            };\n\n            expirations.push({\n                expirationMethod,\n                key,\n                timeoutId: createTimeout(expirationMethod, maxAge),\n            });\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * creates a function that, when a cache item is hit, reset the expiration\n *\n * @param expirations the mutable expirations array\n * @param options the options passed on initialization\n * @returns the onCacheAdd function to handle expirations\n */\nexport function createOnCacheHitResetExpiration<MoizeableFn extends AnyFn>(\n    expirations: Expiration[],\n    options: Options<MoizeableFn>\n): OnCacheOperation<MoizeableFn> {\n    return function onCacheHit(cache: Cache<MoizeableFn>) {\n        const key = cache.keys[0];\n        const expirationIndex = findExpirationIndex(expirations, key);\n\n        if (~expirationIndex) {\n            clearExpiration(expirations, key, false);\n\n            expirations[expirationIndex].timeoutId = createTimeout(\n                expirations[expirationIndex].expirationMethod,\n                options.maxAge\n            );\n        }\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the micro-memoize options specific to the maxAge option\n *\n * @param expirations the expirations for the memoized function\n * @param options the options passed to the moizer\n * @param isEqual the function to test equality of the key on a per-argument basis\n * @param isMatchingKey the function to test equality of the whole key\n * @returns the object of options based on the entries passed\n */\nexport function getMaxAgeOptions<MoizeableFn extends AnyFn>(\n    expirations: Expiration[],\n    options: Options<MoizeableFn>,\n    isEqual: IsEqual,\n    isMatchingKey: IsMatchingKey\n): {\n    onCacheAdd: OnCacheOperation<MoizeableFn> | undefined;\n    onCacheHit: OnCacheOperation<MoizeableFn> | undefined;\n} {\n    const onCacheAdd =\n        typeof options.maxAge === 'number' && isFinite(options.maxAge)\n            ? createOnCacheAddSetExpiration(\n                  expirations,\n                  options,\n                  isEqual,\n                  isMatchingKey\n              )\n            : undefined;\n\n    return {\n        onCacheAdd,\n        onCacheHit:\n            onCacheAdd && options.updateExpire\n                ? createOnCacheHitResetExpiration(expirations, options)\n                : undefined,\n    };\n}\n", "import type {\n    GlobalStatsObject,\n    Moizeable,\n    OnCacheOperation,\n    Options,\n    StatsCache,\n    StatsProfile,\n} from '../index.d';\n\nexport const statsCache: StatsCache = {\n    anonymousProfileNameCounter: 1,\n    isCollectingStats: false,\n    profiles: {},\n};\n\nlet hasWarningDisplayed = false;\n\nexport function clearStats(profileName?: string) {\n    if (profileName) {\n        delete statsCache.profiles[profileName];\n    } else {\n        statsCache.profiles = {};\n    }\n}\n\n/**\n * @private\n *\n * @description\n * activate stats collection\n *\n * @param isCollectingStats should stats be collected\n */\nexport function collectStats(isCollectingStats = true) {\n    statsCache.isCollectingStats = isCollectingStats;\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls for the specific profile\n */\nexport function createOnCacheAddIncrementCalls<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n) {\n    const { profileName } = options;\n\n    return function () {\n        if (profileName && !statsCache.profiles[profileName]) {\n            statsCache.profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        statsCache.profiles[profileName].calls++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * create a function that increments the number of calls and cache hits for the specific profile\n */\nexport function createOnCacheHitIncrementCallsAndHits<\n    MoizeableFn extends Moizeable\n>(options: Options<MoizeableFn>) {\n    return function () {\n        const { profiles } = statsCache;\n        const { profileName } = options;\n\n        if (!profiles[profileName]) {\n            profiles[profileName] = {\n                calls: 0,\n                hits: 0,\n            };\n        }\n\n        profiles[profileName].calls++;\n        profiles[profileName].hits++;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the profileName for the function when one is not provided\n *\n * @param fn the function to be memoized\n * @returns the derived profileName for the function\n */\nexport function getDefaultProfileName<MoizeableFn extends Moizeable>(\n    fn: MoizeableFn\n) {\n    return (\n        fn.displayName ||\n        fn.name ||\n        `Anonymous ${statsCache.anonymousProfileNameCounter++}`\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the usage percentage based on the number of hits and total calls\n *\n * @param calls the number of calls made\n * @param hits the number of cache hits when called\n * @returns the usage as a percentage string\n */\nexport function getUsagePercentage(calls: number, hits: number) {\n    return calls ? `${((hits / calls) * 100).toFixed(4)}%` : '0.0000%';\n}\n\n/**\n * @private\n *\n * @description\n * get the statistics for a given method or all methods\n *\n * @param [profileName] the profileName to get the statistics for (get all when not provided)\n * @returns the object with stats information\n */\nexport function getStats(profileName?: string): GlobalStatsObject {\n    if (!statsCache.isCollectingStats && !hasWarningDisplayed) {\n        console.warn(\n            'Stats are not currently being collected, please run \"collectStats\" to enable them.'\n        ); // eslint-disable-line no-console\n\n        hasWarningDisplayed = true;\n    }\n\n    const { profiles } = statsCache;\n\n    if (profileName) {\n        if (!profiles[profileName]) {\n            return {\n                calls: 0,\n                hits: 0,\n                usage: '0.0000%',\n            };\n        }\n\n        const { [profileName]: profile } = profiles;\n\n        return {\n            ...profile,\n            usage: getUsagePercentage(profile.calls, profile.hits),\n        };\n    }\n\n    const completeStats: StatsProfile = Object.keys(statsCache.profiles).reduce(\n        (completeProfiles, profileName) => {\n            completeProfiles.calls += profiles[profileName].calls;\n            completeProfiles.hits += profiles[profileName].hits;\n\n            return completeProfiles;\n        },\n        {\n            calls: 0,\n            hits: 0,\n        }\n    );\n\n    return {\n        ...completeStats,\n        profiles: Object.keys(profiles).reduce(\n            (computedProfiles, profileName) => {\n                computedProfiles[profileName] = getStats(profileName);\n\n                return computedProfiles;\n            },\n            {} as Record<string, StatsProfile>\n        ),\n        usage: getUsagePercentage(completeStats.calls, completeStats.hits),\n    };\n}\n\n/**\n * @private\n *\n * @function getStatsOptions\n *\n * @description\n * get the options specific to storing statistics\n *\n * @param {Options} options the options passed to the moizer\n * @returns {Object} the options specific to keeping stats\n */\nexport function getStatsOptions<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n): {\n    onCacheAdd?: OnCacheOperation<MoizeableFn>;\n    onCacheHit?: OnCacheOperation<MoizeableFn>;\n} {\n    return statsCache.isCollectingStats\n        ? {\n              onCacheAdd: createOnCacheAddIncrementCalls(options),\n              onCacheHit: createOnCacheHitIncrementCallsAndHits(options),\n          }\n        : {};\n}\n", "import { clearExpiration } from './maxAge';\nimport { clearStats, getStats } from './stats';\nimport { createFindKeyIndex } from './utils';\n\nimport type {\n    Key,\n    Memoized,\n    Moizeable,\n    MoizeConfiguration,\n    Moized,\n    Options,\n    StatsProfile,\n} from '../index.d';\n\nconst ALWAYS_SKIPPED_PROPERTIES: Record<string, boolean> = {\n    arguments: true,\n    callee: true,\n    caller: true,\n    constructor: true,\n    length: true,\n    name: true,\n    prototype: true,\n};\n\n/**\n * @private\n *\n * @description\n * copy the static properties from the original function to the moized\n * function\n *\n * @param originalFn the function copying from\n * @param newFn the function copying to\n * @param skippedProperties the list of skipped properties, if any\n */\nexport function copyStaticProperties<\n    OriginalMoizeableFn extends Moizeable,\n    NewMoizeableFn extends Moizeable\n>(\n    originalFn: OriginalMoizeableFn,\n    newFn: NewMoizeableFn,\n    skippedProperties: string[] = []\n) {\n    Object.getOwnPropertyNames(originalFn).forEach((property) => {\n        if (\n            !ALWAYS_SKIPPED_PROPERTIES[property] &&\n            skippedProperties.indexOf(property) === -1\n        ) {\n            const descriptor = Object.getOwnPropertyDescriptor(\n                originalFn,\n                property\n            );\n\n            if (descriptor.get || descriptor.set) {\n                Object.defineProperty(newFn, property, descriptor);\n            } else {\n                // @ts-expect-error - properites may not align\n                newFn[property] = originalFn[property];\n            }\n        }\n    });\n}\n\n/**\n * @private\n *\n * @description\n * add methods to the moized fuction object that allow extra features\n *\n * @param memoized the memoized function from micro-memoize\n */\nexport function addInstanceMethods<MoizeableFn extends Moizeable>(\n    memoized: Moizeable,\n    { expirations }: MoizeConfiguration<MoizeableFn>\n) {\n    const { options } = memoized;\n\n    const findKeyIndex = createFindKeyIndex(\n        options.isEqual,\n        options.isMatchingKey\n    );\n\n    const moized = memoized as unknown as Moized<\n        MoizeableFn,\n        Options<MoizeableFn>\n    >;\n\n    moized.clear = function () {\n        const {\n            _microMemoizeOptions: { onCacheChange },\n            cache,\n        } = moized;\n\n        cache.keys.length = 0;\n        cache.values.length = 0;\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        return true;\n    };\n\n    moized.clearStats = function () {\n        clearStats(moized.options.profileName);\n    };\n\n    moized.get = function (key: Key) {\n        const {\n            _microMemoizeOptions: { transformKey },\n            cache,\n        } = moized;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        return keyIndex !== -1 ? moized.apply(this, key) : undefined;\n    };\n\n    moized.getStats = function (): StatsProfile {\n        return getStats(moized.options.profileName);\n    };\n\n    moized.has = function (key: Key) {\n        const { transformKey } = moized._microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n\n        return findKeyIndex(moized.cache.keys, cacheKey) !== -1;\n    };\n\n    moized.keys = function () {\n        return moized.cacheSnapshot.keys;\n    };\n\n    moized.remove = function (key: Key) {\n        const {\n            _microMemoizeOptions: { onCacheChange, transformKey },\n            cache,\n        } = moized;\n\n        const keyIndex = findKeyIndex(\n            cache.keys,\n            transformKey ? transformKey(key) : key\n        );\n\n        if (keyIndex === -1) {\n            return false;\n        }\n\n        const existingKey = cache.keys[keyIndex];\n\n        cache.keys.splice(keyIndex, 1);\n        cache.values.splice(keyIndex, 1);\n\n        if (onCacheChange) {\n            onCacheChange(cache, moized.options, moized);\n        }\n\n        clearExpiration(expirations, existingKey, true);\n\n        return true;\n    };\n\n    moized.set = function (key: Key, value: any) {\n        const { _microMemoizeOptions, cache, options } = moized;\n        const { onCacheAdd, onCacheChange, transformKey } =\n            _microMemoizeOptions;\n\n        const cacheKey = transformKey ? transformKey(key) : key;\n        const keyIndex = findKeyIndex(cache.keys, cacheKey);\n\n        if (keyIndex === -1) {\n            const cutoff = options.maxSize - 1;\n\n            if (cache.size > cutoff) {\n                cache.keys.length = cutoff;\n                cache.values.length = cutoff;\n            }\n\n            cache.keys.unshift(cacheKey);\n            cache.values.unshift(value);\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (onCacheAdd) {\n                onCacheAdd(cache, options, moized);\n            }\n\n            if (onCacheChange) {\n                onCacheChange(cache, options, moized);\n            }\n        } else {\n            const existingKey = cache.keys[keyIndex];\n\n            cache.values[keyIndex] = value;\n\n            if (keyIndex > 0) {\n                cache.orderByLru(existingKey, value, keyIndex);\n            }\n\n            if (options.isPromise) {\n                cache.updateAsyncCache(moized);\n            }\n\n            if (typeof onCacheChange === 'function') {\n                onCacheChange(cache, options, moized);\n            }\n        }\n    };\n\n    moized.values = function () {\n        return moized.cacheSnapshot.values;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * add propeties to the moized fuction object that surfaces extra information\n *\n * @param memoized the memoized function\n * @param expirations the list of expirations for cache items\n * @param options the options passed to the moizer\n * @param originalFunction the function that is being memoized\n */\nexport function addInstanceProperties<MoizeableFn extends Moizeable>(\n    memoized: Memoized<MoizeableFn>,\n    {\n        expirations,\n        options: moizeOptions,\n        originalFunction,\n    }: MoizeConfiguration<MoizeableFn>\n) {\n    const { options: microMemoizeOptions } = memoized;\n\n    Object.defineProperties(memoized, {\n        _microMemoizeOptions: {\n            configurable: true,\n            get() {\n                return microMemoizeOptions;\n            },\n        },\n\n        cacheSnapshot: {\n            configurable: true,\n            get() {\n                const { cache: currentCache } = memoized;\n\n                return {\n                    keys: currentCache.keys.slice(0),\n                    size: currentCache.size,\n                    values: currentCache.values.slice(0),\n                };\n            },\n        },\n\n        expirations: {\n            configurable: true,\n            get() {\n                return expirations;\n            },\n        },\n\n        expirationsSnapshot: {\n            configurable: true,\n            get() {\n                return expirations.slice(0);\n            },\n        },\n\n        isMoized: {\n            configurable: true,\n            get() {\n                return true;\n            },\n        },\n\n        options: {\n            configurable: true,\n            get() {\n                return moizeOptions;\n            },\n        },\n\n        originalFunction: {\n            configurable: true,\n            get() {\n                return originalFunction;\n            },\n        },\n    });\n\n    const moized = memoized as unknown as Moized<\n        MoizeableFn,\n        Options<MoizeableFn>\n    >;\n\n    copyStaticProperties(originalFunction, moized);\n}\n\n/**\n * @private\n *\n * @description\n * add methods and properties to the memoized function for more features\n *\n * @param memoized the memoized function\n * @param configuration the configuration object for the instance\n * @returns the memoized function passed\n */\nexport function createMoizeInstance<\n    MoizeableFn extends Moizeable,\n    CombinedOptions extends Options<MoizeableFn>\n>(\n    memoized: Memoized<MoizeableFn>,\n    configuration: MoizeConfiguration<MoizeableFn>\n) {\n    addInstanceMethods<MoizeableFn>(memoized, configuration);\n    addInstanceProperties<MoizeableFn>(memoized, configuration);\n\n    return memoized as Moized<MoizeableFn, CombinedOptions>;\n}\n", "import { copyStaticProperties } from './instance';\nimport { setName } from './utils';\n\nimport type {\n    Moize,\n    Moized as MoizedFunction,\n    Moizeable,\n    Options,\n} from '../index.d';\n\n// This was stolen from React internals, which allows us to create React elements without needing\n// a dependency on the React library itself.\nconst REACT_ELEMENT_TYPE =\n    typeof Symbol === 'function' && Symbol.for\n        ? Symbol.for('react.element')\n        : 0xeac7;\n\n/**\n * @private\n *\n * @description\n * Create a component that memoizes based on `props` and legacy `context`\n * on a per-instance basis. This requires creating a component class to\n * store the memoized function. The cost is quite low, and avoids the\n * need to have access to the React dependency by basically re-creating\n * the basic essentials for a component class and the results of the\n * `createElement` function.\n *\n * @param moizer the top-level moize method\n * @param fn the component to memoize\n * @param options the memoization options\n * @returns the memoized component\n */\nexport function createMoizedComponent<MoizeableFn extends Moizeable>(\n    moizer: Moize,\n    fn: MoizeableFn,\n    options: Options<MoizeableFn>\n) {\n    /**\n     * This is a hack override setting the necessary options\n     * for a React component to be memoized. In the main `moize`\n     * method, if the `isReact` option is set it is short-circuited\n     * to call this function, and these overrides allow the\n     * necessary transformKey method to be derived.\n     *\n     * The order is based on:\n     * 1) Set the necessary aspects of transformKey for React components.\n     * 2) Allow setting of other options and overrides of those aspects\n     *    if desired (for example, `isDeepEqual` will use deep equality).\n     * 3) Always set `isReact` to false to prevent infinite loop.\n     */\n    const reactMoizer = moizer({\n        maxArgs: 2,\n        isShallowEqual: true,\n        ...options,\n        isReact: false,\n    });\n\n    if (!fn.displayName) {\n        // @ts-ignore - allow setting of displayName\n        fn.displayName = fn.name || 'Component';\n    }\n\n    function Moized<Props extends Record<string, unknown>, Context, Updater>(\n        this: any,\n        props: Props,\n        context: Context,\n        updater: Updater\n    ) {\n        this.props = props;\n        this.context = context;\n        this.updater = updater;\n\n        this.MoizedComponent = reactMoizer(fn);\n    }\n\n    Moized.prototype.isReactComponent = {};\n\n    Moized.prototype.render = function (): ReturnType<MoizeableFn> {\n        return {\n            $$typeof: REACT_ELEMENT_TYPE,\n            type: this.MoizedComponent,\n            props: this.props,\n            ref: null,\n            key: null,\n            _owner: null,\n        } as ReturnType<MoizeableFn>;\n    };\n\n    copyStaticProperties(fn, Moized, ['contextType', 'contextTypes']);\n\n    Moized.displayName = `Moized(${fn.displayName || fn.name || 'Component'})`;\n\n    setName(Moized as MoizedFunction, fn.name, options.profileName);\n\n    return Moized;\n}\n", "import type { Key } from '../index.d';\n\nexport function createGetInitialArgs(size: number) {\n    /**\n     * @private\n     *\n     * @description\n     * take the first N number of items from the array (faster than slice)\n     *\n     * @param args the args to take from\n     * @returns the shortened list of args as an array\n     */\n    return function (args: Key): Key {\n        if (size >= args.length) {\n            return args;\n        }\n\n        if (size === 0) {\n            return [];\n        }\n\n        if (size === 1) {\n            return [args[0]];\n        }\n\n        if (size === 2) {\n            return [args[0], args[1]];\n        }\n\n        if (size === 3) {\n            return [args[0], args[1], args[2]];\n        }\n\n        const clone = [];\n\n        for (let index = 0; index < size; index++) {\n            clone[index] = args[index];\n        }\n\n        return clone;\n    };\n}\n", "import type { Key, Moizeable, Options } from '../index.d';\n\n/**\n * @function getCutoff\n *\n * @description\n * faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n    const { length } = array;\n\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n\n    return 0;\n}\n\n/**\n * @private\n *\n * @description\n * custom replacer for the stringify function\n *\n * @returns if function then toString of it, else the value itself\n */\nexport function createDefaultReplacer() {\n    const cache: any[] = [];\n    const keys: string[] = [];\n\n    return function defaultReplacer(key: string, value: any) {\n        const type = typeof value;\n\n        if (type === 'function' || type === 'symbol') {\n            return value.toString();\n        }\n\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                } else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n\n                keys[keys.length] = key;\n\n                const valueCutoff = getCutoff(cache, value);\n\n                if (valueCutoff !== 0) {\n                    return `[ref=${\n                        keys.slice(0, valueCutoff).join('.') || '.'\n                    }]`;\n                }\n            } else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n\n            return value;\n        }\n\n        return '' + value;\n    };\n}\n\n/**\n * @private\n *\n * @description\n * get the stringified version of the argument passed\n *\n * @param arg argument to stringify\n * @returns the stringified argument\n */\nexport function getStringifiedArgument<Type>(arg: Type) {\n    const typeOfArg = typeof arg;\n\n    return arg && (typeOfArg === 'object' || typeOfArg === 'function')\n        ? JSON.stringify(arg, createDefaultReplacer())\n        : arg;\n}\n\n/**\n * @private\n *\n * @description\n * serialize the arguments passed\n *\n * @param options the options passed to the moizer\n * @param options.maxArgs the cap on the number of arguments used in serialization\n * @returns argument serialization method\n */\nexport function defaultArgumentSerializer(args: Key) {\n    let key = '|';\n\n    for (let index = 0; index < args.length; index++) {\n        key += getStringifiedArgument(args[index]) + '|';\n    }\n\n    return [key];\n}\n\n/**\n * @private\n *\n * @description\n * based on the options passed, either use the serializer passed or generate the internal one\n *\n * @param options the options passed to the moized function\n * @returns the function to use in serializing the arguments\n */\nexport function getSerializerFunction<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n) {\n    return typeof options.serializer === 'function'\n        ? options.serializer\n        : defaultArgumentSerializer;\n}\n\n/**\n * @private\n *\n * @description\n * are the serialized keys equal to one another\n *\n * @param cacheKey the cache key to compare\n * @param key the key to test\n * @returns are the keys equal\n */\nexport function getIsSerializedKeyEqual(cacheKey: Key, key: Key) {\n    return cacheKey[0] === key[0];\n}\n", "import { deepEqual, sameValueZeroEqual, shallowEqual } from 'fast-equals';\nimport { createGetInitialArgs } from './maxArgs';\nimport { getIsSerializedKeyEqual, getSerializerFunction } from './serialize';\nimport { compose } from './utils';\n\nimport type {\n    Cache,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moizeable,\n    Moized,\n    OnCacheOperation,\n    Options,\n    TransformKey,\n} from '../index.d';\n\nexport function createOnCacheOperation<MoizeableFn extends Moizeable>(\n    fn?: OnCacheOperation<MoizeableFn>\n): OnCacheOperation<MoizeableFn> {\n    if (typeof fn === 'function') {\n        return (\n            _cacheIgnored: Cache<MoizeableFn>,\n            _microMemoizeOptionsIgnored: MicroMemoizeOptions<MoizeableFn>,\n            memoized: Moized\n        ): void => fn(memoized.cache, memoized.options, memoized);\n    }\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsEqual<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n): IsEqual {\n    return (\n        options.matchesArg ||\n        (options.isDeepEqual && deepEqual) ||\n        (options.isShallowEqual && shallowEqual) ||\n        sameValueZeroEqual\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the isEqual method passed to micro-memoize\n *\n * @param options the options passed to the moizer\n * @returns the isEqual method to apply\n */\nexport function getIsMatchingKey<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n): IsMatchingKey | undefined {\n    return (\n        options.matchesKey ||\n        (options.isSerialized && getIsSerializedKeyEqual) ||\n        undefined\n    );\n}\n\n/**\n * @private\n *\n * @description\n * get the function that will transform the key based on the arguments passed\n *\n * @param options the options passed to the moizer\n * @returns the function to transform the key with\n */\nexport function getTransformKey<MoizeableFn extends Moizeable>(\n    options: Options<MoizeableFn>\n): TransformKey | undefined {\n    return compose(\n        options.isSerialized && getSerializerFunction(options),\n        typeof options.transformArgs === 'function' && options.transformArgs,\n        typeof options.maxArgs === 'number' &&\n            createGetInitialArgs(options.maxArgs)\n    ) as TransformKey;\n}\n", "import { copyStaticProperties } from './instance';\n\nimport type { Moized } from '../index.d';\n\nexport function createRefreshableMoized<MoizedFn extends Moized>(\n    moized: MoizedFn\n) {\n    const {\n        options: { updateCacheForKey },\n    } = moized;\n\n    /**\n     * @private\n     *\n     * @description\n     * Wrapper around already-`moize`d function which will intercept the memoization\n     * and call the underlying function directly with the purpose of updating the cache\n     * for the given key.\n     *\n     * Promise values use a tweak of the logic that exists at cache.updateAsyncCache, which\n     * reverts to the original value if the promise is rejected and there was already a cached\n     * value.\n     */\n    const refreshableMoized = function refreshableMoized(\n        this: any,\n        ...args: Parameters<typeof moized.fn>\n    ) {\n        if (!updateCacheForKey(args)) {\n            return moized.apply(this, args);\n        }\n\n        const result = moized.fn.apply(this, args);\n\n        moized.set(args, result);\n\n        return result;\n    } as typeof moized;\n\n    copyStaticProperties(moized, refreshableMoized);\n\n    return refreshableMoized;\n}\n", "import memoize from 'micro-memoize';\nimport { createMoizedComponent } from './component';\nimport { DEFAULT_OPTIONS } from './constants';\nimport { createMoizeInstance } from './instance';\nimport { getMaxAgeOptions } from './maxAge';\nimport {\n    createOnCacheOperation,\n    getIsEqual,\n    getIsMatchingKey,\n    getTransformKey,\n} from './options';\nimport {\n    clearStats,\n    collectStats,\n    getDefaultProfileName,\n    getStats,\n    getStatsOptions,\n    statsCache,\n} from './stats';\nimport { createRefreshableMoized } from './updateCacheForKey';\nimport { combine, compose, isMoized, mergeOptions, setName } from './utils';\n\nimport type {\n    Expiration,\n    IsEqual,\n    IsMatchingKey,\n    MicroMemoizeOptions,\n    Moize,\n    Moizeable,\n    Moized,\n    OnExpire,\n    Options,\n    Serialize,\n    TransformKey,\n    UpdateCacheForKey,\n} from '../index.d';\n\n/**\n * @module moize\n */\n\n/**\n * @description\n * memoize a function based its arguments passed, potentially improving runtime performance\n *\n * @example\n * import moize from 'moize';\n *\n * // standard implementation\n * const fn = (foo, bar) => `${foo} ${bar}`;\n * const memoizedFn = moize(fn);\n *\n * // implementation with options\n * const fn = async (id) => get(`http://foo.com/${id}`);\n * const memoizedFn = moize(fn, {isPromise: true, maxSize: 5});\n *\n * // implementation with convenience methods\n * const Foo = ({foo}) => <div>{foo}</div>;\n * const MemoizedFoo = moize.react(Foo);\n *\n * @param fn the function to memoized, or a list of options when currying\n * @param [options=DEFAULT_OPTIONS] the options to apply\n * @returns the memoized function\n */\nconst moize: Moize = function <\n    MoizeableFn extends Moizeable,\n    PassedOptions extends Options<MoizeableFn>\n>(fn: MoizeableFn | PassedOptions, passedOptions?: PassedOptions) {\n    type CombinedOptions = Omit<Options<MoizeableFn>, keyof PassedOptions> &\n        PassedOptions;\n\n    const options: Options<MoizeableFn> = passedOptions || DEFAULT_OPTIONS;\n\n    if (isMoized(fn)) {\n        const moizeable = fn.originalFunction as MoizeableFn;\n        const mergedOptions = mergeOptions(\n            fn.options,\n            options\n        ) as CombinedOptions;\n\n        return moize<MoizeableFn, CombinedOptions>(moizeable, mergedOptions);\n    }\n\n    if (typeof fn === 'object') {\n        return function <\n            CurriedFn extends Moizeable,\n            CurriedOptions extends Options<CurriedFn>\n        >(\n            curriedFn: CurriedFn | CurriedOptions,\n            curriedOptions: CurriedOptions\n        ) {\n            type CombinedCurriedOptions = Omit<\n                CombinedOptions,\n                keyof CurriedOptions\n            > &\n                CurriedOptions;\n\n            if (typeof curriedFn === 'function') {\n                const mergedOptions = mergeOptions(\n                    fn as CombinedOptions,\n                    curriedOptions\n                ) as CombinedCurriedOptions;\n\n                return moize(curriedFn, mergedOptions);\n            }\n\n            const mergedOptions = mergeOptions(\n                fn as CombinedOptions,\n                curriedFn as CurriedOptions\n            );\n\n            return moize(mergedOptions);\n        };\n    }\n\n    if (options.isReact) {\n        return createMoizedComponent(moize, fn, options);\n    }\n\n    const coalescedOptions: Options<MoizeableFn> = {\n        ...DEFAULT_OPTIONS,\n        ...options,\n        maxAge:\n            typeof options.maxAge === 'number' && options.maxAge >= 0\n                ? options.maxAge\n                : DEFAULT_OPTIONS.maxAge,\n        maxArgs:\n            typeof options.maxArgs === 'number' && options.maxArgs >= 0\n                ? options.maxArgs\n                : DEFAULT_OPTIONS.maxArgs,\n        maxSize:\n            typeof options.maxSize === 'number' && options.maxSize >= 0\n                ? options.maxSize\n                : DEFAULT_OPTIONS.maxSize,\n        profileName: options.profileName || getDefaultProfileName(fn),\n    };\n    const expirations: Array<Expiration> = [];\n\n    const {\n        matchesArg: equalsIgnored,\n        isDeepEqual: isDeepEqualIgnored,\n        isPromise,\n        isReact: isReactIgnored,\n        isSerialized: isSerialzedIgnored,\n        isShallowEqual: isShallowEqualIgnored,\n        matchesKey: matchesKeyIgnored,\n        maxAge: maxAgeIgnored,\n        maxArgs: maxArgsIgnored,\n        maxSize,\n        onCacheAdd,\n        onCacheChange,\n        onCacheHit,\n        onExpire: onExpireIgnored,\n        profileName: profileNameIgnored,\n        serializer: serializerIgnored,\n        updateCacheForKey,\n        transformArgs: transformArgsIgnored,\n        updateExpire: updateExpireIgnored,\n        ...customOptions\n    } = coalescedOptions;\n\n    const isEqual = getIsEqual(coalescedOptions);\n    const isMatchingKey = getIsMatchingKey(coalescedOptions);\n\n    const maxAgeOptions = getMaxAgeOptions(\n        expirations,\n        coalescedOptions,\n        isEqual,\n        isMatchingKey\n    );\n    const statsOptions = getStatsOptions(coalescedOptions);\n\n    const transformKey = getTransformKey(coalescedOptions);\n\n    const microMemoizeOptions: MicroMemoizeOptions<MoizeableFn> = {\n        ...customOptions,\n        isEqual,\n        isMatchingKey,\n        isPromise,\n        maxSize,\n        onCacheAdd: createOnCacheOperation(\n            combine(\n                onCacheAdd,\n                maxAgeOptions.onCacheAdd,\n                statsOptions.onCacheAdd\n            )\n        ),\n        onCacheChange: createOnCacheOperation(onCacheChange),\n        onCacheHit: createOnCacheOperation(\n            combine(\n                onCacheHit,\n                maxAgeOptions.onCacheHit,\n                statsOptions.onCacheHit\n            )\n        ),\n        transformKey,\n    };\n\n    const memoized = memoize(fn, microMemoizeOptions);\n\n    let moized = createMoizeInstance<MoizeableFn, CombinedOptions>(memoized, {\n        expirations,\n        options: coalescedOptions,\n        originalFunction: fn,\n    });\n\n    if (updateCacheForKey) {\n        moized = createRefreshableMoized<typeof moized>(moized);\n    }\n\n    setName(moized, (fn as Moizeable).name, options.profileName);\n\n    return moized;\n};\n\n/**\n * @function\n * @name clearStats\n * @memberof module:moize\n * @alias moize.clearStats\n *\n * @description\n * clear all existing stats stored\n */\nmoize.clearStats = clearStats;\n\n/**\n * @function\n * @name collectStats\n * @memberof module:moize\n * @alias moize.collectStats\n *\n * @description\n * start collecting statistics\n */\nmoize.collectStats = collectStats;\n\n/**\n * @function\n * @name compose\n * @memberof module:moize\n * @alias moize.compose\n *\n * @description\n * method to compose moized methods and return a single moized function\n *\n * @param moized the functions to compose\n * @returns the composed function\n */\nmoize.compose = function (...moized: Moize[]) {\n    return compose<Moize>(...moized) || moize;\n};\n\n/**\n * @function\n * @name deep\n * @memberof module:moize\n * @alias moize.deep\n *\n * @description\n * should deep equality check be used\n *\n * @returns the moizer function\n */\nmoize.deep = moize({ isDeepEqual: true });\n\n/**\n * @function\n * @name getStats\n * @memberof module:moize\n * @alias moize.getStats\n *\n * @description\n * get the statistics of a given profile, or overall usage\n *\n * @returns statistics for a given profile or overall usage\n */\nmoize.getStats = getStats;\n\n/**\n * @function\n * @name infinite\n * @memberof module:moize\n * @alias moize.infinite\n *\n * @description\n * a moized method that will remove all limits from the cache size\n *\n * @returns the moizer function\n */\nmoize.infinite = moize({ maxSize: Infinity });\n\n/**\n * @function\n * @name isCollectingStats\n * @memberof module:moize\n * @alias moize.isCollectingStats\n *\n * @description\n * are stats being collected\n *\n * @returns are stats being collected\n */\nmoize.isCollectingStats = function isCollectingStats(): boolean {\n    return statsCache.isCollectingStats;\n};\n\n/**\n * @function\n * @name isMoized\n * @memberof module:moize\n * @alias moize.isMoized\n *\n * @description\n * is the fn passed a moized function\n *\n * @param fn the object to test\n * @returns is fn a moized function\n */\nmoize.isMoized = function isMoized(fn: any): fn is Moized {\n    return typeof fn === 'function' && !!fn.isMoized;\n};\n\n/**\n * @function\n * @name matchesArg\n * @memberof module:moize\n * @alias moize.matchesArg\n *\n * @description\n * a moized method where the arg matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesArg = function (argMatcher: IsEqual) {\n    return moize({ matchesArg: argMatcher });\n};\n\n/**\n * @function\n * @name matchesKey\n * @memberof module:moize\n * @alias moize.matchesKey\n *\n * @description\n * a moized method where the key matching method is the custom one passed\n *\n * @param keyMatcher the method to compare against those in cache\n * @returns the moizer function\n */\nmoize.matchesKey = function (keyMatcher: IsMatchingKey) {\n    return moize({ matchesKey: keyMatcher });\n};\n\nfunction maxAge<MaxAge extends number>(\n    maxAge: MaxAge\n): Moize<{ maxAge: MaxAge }>;\nfunction maxAge<MaxAge extends number, UpdateExpire extends boolean>(\n    maxAge: MaxAge,\n    expireOptions: UpdateExpire\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<MaxAge extends number, ExpireHandler extends OnExpire>(\n    maxAge: MaxAge,\n    expireOptions: ExpireHandler\n): Moize<{ maxAge: MaxAge; onExpire: ExpireHandler }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; onExpire: ExpireOptions['onExpire'] }>;\nfunction maxAge<\n    MaxAge extends number,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{ maxAge: MaxAge; updateExpire: UpdateExpire }>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire: ExpireHandler;\n        updateExpire: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions: ExpireOptions\n): Moize<{\n    maxAge: MaxAge;\n    onExpire: ExpireHandler;\n    updateExpire: UpdateExpire;\n}>;\nfunction maxAge<\n    MaxAge extends number,\n    ExpireHandler extends OnExpire,\n    UpdateExpire extends boolean,\n    ExpireOptions extends {\n        onExpire?: ExpireHandler;\n        updateExpire?: UpdateExpire;\n    }\n>(\n    maxAge: MaxAge,\n    expireOptions?: ExpireHandler | UpdateExpire | ExpireOptions\n) {\n    if (expireOptions === true) {\n        return moize({\n            maxAge,\n            updateExpire: expireOptions,\n        });\n    }\n\n    if (typeof expireOptions === 'object') {\n        const { onExpire, updateExpire } = expireOptions;\n\n        return moize({\n            maxAge,\n            onExpire,\n            updateExpire,\n        });\n    }\n\n    if (typeof expireOptions === 'function') {\n        return moize({\n            maxAge,\n            onExpire: expireOptions,\n            updateExpire: true,\n        });\n    }\n\n    return moize({ maxAge });\n}\n\n/**\n * @function\n * @name maxAge\n * @memberof module:moize\n * @alias moize.maxAge\n *\n * @description\n * a moized method where the age of the cache is limited to the number of milliseconds passed\n *\n * @param maxAge the TTL of the value in cache\n * @returns the moizer function\n */\nmoize.maxAge = maxAge;\n\n/**\n * @function\n * @name maxArgs\n * @memberof module:moize\n * @alias moize.maxArgs\n *\n * @description\n * a moized method where the number of arguments used for determining cache is limited to the value passed\n *\n * @param maxArgs the number of args to base the key on\n * @returns the moizer function\n */\nmoize.maxArgs = function maxArgs(maxArgs: number) {\n    return moize({ maxArgs });\n};\n\n/**\n * @function\n * @name maxSize\n * @memberof module:moize\n * @alias moize.maxSize\n *\n * @description\n * a moized method where the total size of the cache is limited to the value passed\n *\n * @param maxSize the maximum size of the cache\n * @returns the moizer function\n */\nmoize.maxSize = function maxSize(maxSize: number) {\n    return moize({ maxSize });\n};\n\n/**\n * @function\n * @name profile\n * @memberof module:moize\n * @alias moize.profile\n *\n * @description\n * a moized method with a profile name\n *\n * @returns the moizer function\n */\nmoize.profile = function (profileName: string) {\n    return moize({ profileName });\n};\n\n/**\n * @function\n * @name promise\n * @memberof module:moize\n * @alias moize.promise\n *\n * @description\n * a moized method specific to caching resolved promise / async values\n *\n * @returns the moizer function\n */\nmoize.promise = moize({\n    isPromise: true,\n    updateExpire: true,\n});\n\n/**\n * @function\n * @name react\n * @memberof module:moize\n * @alias moize.react\n *\n * @description\n * a moized method specific to caching React element values\n *\n * @returns the moizer function\n */\nmoize.react = moize({ isReact: true });\n\n/**\n * @function\n * @name serialize\n * @memberof module:moize\n * @alias moize.serialize\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n *\n * @returns the moizer function\n */\nmoize.serialize = moize({ isSerialized: true });\n\n/**\n * @function\n * @name serializeWith\n * @memberof module:moize\n * @alias moize.serializeWith\n *\n * @description\n * a moized method that will serialize the arguments passed to use as the cache key\n * based on the serializer passed\n *\n * @returns the moizer function\n */\nmoize.serializeWith = function (serializer: Serialize) {\n    return moize({ isSerialized: true, serializer });\n};\n\n/**\n * @function\n * @name shallow\n * @memberof module:moize\n * @alias moize.shallow\n *\n * @description\n * should shallow equality check be used\n *\n * @returns the moizer function\n */\nmoize.shallow = moize({ isShallowEqual: true });\n\n/**\n * @function\n * @name transformArgs\n * @memberof module:moize\n * @alias moize.transformArgs\n *\n * @description\n * transform the args to allow for specific cache key comparison\n *\n * @param transformArgs the args transformer\n * @returns the moizer function\n */\nmoize.transformArgs = <Transformer extends TransformKey>(\n    transformArgs: Transformer\n) => moize({ transformArgs });\n\n/**\n * @function\n * @name updateCacheForKey\n * @memberof module:moize\n * @alias moize.updateCacheForKey\n *\n * @description\n * update the cache for a given key when the method passed returns truthy\n *\n * @param updateCacheForKey the method to determine when to update cache\n * @returns the moizer function\n */\nmoize.updateCacheForKey = <UpdateWhen extends UpdateCacheForKey>(\n    updateCacheForKey: UpdateWhen\n) => moize({ updateCacheForKey });\n\n// Add self-referring `default` property for edge-case cross-compatibility of mixed ESM/CommonJS usage.\n// This property is frozen and non-enumerable to avoid visibility on iteration or accidental overrides.\nObject.defineProperty(moize, 'default', {\n    configurable: false,\n    enumerable: false,\n    value: moize,\n    writable: false,\n});\n\nexport default moize;\n"], "mappings": ";;;;;;;;;;;AAWA,UAAM,uBAAyC;QAC7C,SAAS;QACT,eAAe;QACf,WAAW;QACX,SAAS;QACT,YAAY;QACZ,eAAe;QACf,YAAY;QACZ,cAAc;;AASD,UAAA,QAAU,MAAM,UAAS;AAWlC,eAAU,WAAW,WAA6B;AAC9C,YAAA,SAAW,UAAS;AAE5B,YAAI,CAAC,QAAQ;AACX,iBAAO,CAAA;QACR;AAED,YAAI,WAAW,GAAG;AAChB,iBAAO,CAAC,UAAU,CAAC,CAAC;QACrB;AAED,YAAI,WAAW,GAAG;AAChB,iBAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;QACnC;AAED,YAAI,WAAW,GAAG;AAChB,iBAAO,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;QACjD;AAED,eAAO,MAAM,KAAK,WAAW,CAAC;MAChC;AAWM,eAAU,iBAAmC,SAAoB;AACrE,YAAM,gBAA6B,CAAA;AAInC,iBAAW,OAAO,SAAS;AACzB,cAAI,CAAC,qBAAqB,GAAG,GAAG;AAC9B,0BAAc,GAAG,IAAI,QAAQ,GAAG;UACjC;QACF;AAID,eAAO;MACT;AAWM,eAAU,WAAW,IAAO;AAChC,eAAO,OAAO,OAAO,cAAe,GAAuB;MAC7D;AAYgB,eAAA,gBAAgB,SAAc,SAAY;AAExD,eAAO,YAAY,WAAY,YAAY,WAAW,YAAY;MACpE;AAYgB,eAAA,aACd,iBACA,YAAuB;AAEvB,YAAM,SAAS,CAAA;AAIf,iBAAW,OAAO,iBAAiB;AACjC,iBAAO,GAAG,IAAI,gBAAgB,GAAG;QAClC;AAED,iBAAW,OAAO,YAAY;AAC5B,iBAAO,GAAG,IAAI,WAAW,GAAG;QAC7B;AAID,eAAO;MACT;AC/HA,UAAA;;QAAA,WAAA;AAkBE,mBAAAA,OAAY,SAA8B;AACxC,iBAAK,OAAO,CAAA;AACZ,iBAAK,SAAS,CAAA;AACd,iBAAK,UAAU;AAEf,gBAAM,wBAAwB,OAAO,QAAQ,kBAAkB;AAE/D,gBAAI,uBAAuB;AACzB,mBAAK,cAAc,KAAK;YACzB,WAAU,QAAQ,UAAU,GAAG;AAC9B,mBAAK,cAAc,KAAK;YACzB,OAAM;AACL,mBAAK,cAAc,KAAK;YACzB;AAED,iBAAK,kBAAkB,OAAO,QAAQ,iBAAiB;AACvD,iBAAK,uBAAuB,KAAK,mBAAmB;AAEpD,iBAAK,oBAAoB,OAAO,QAAQ,eAAe;AACvD,iBAAK,uBAAuB,OAAO,QAAQ,kBAAkB;AAC7D,iBAAK,oBAAoB,OAAO,QAAQ,eAAe;;AAMzD,iBAAA,eAAIA,OAAI,WAAA,QAAA;;;;YAAR,KAAA,WAAA;AACE,qBAAO,KAAK,KAAK;;;;UAClB,CAAA;AAOD,iBAAA,eAAIA,OAAQ,WAAA,YAAA;;;;;;YAAZ,KAAA,WAAA;AACE,qBAAO;gBACL,MAAM,WAAW,KAAK,IAAI;gBAC1B,MAAM,KAAK;gBACX,QAAQ,WAAW,KAAK,MAAM;;;;;UAEjC,CAAA;AAKD,UAAAA,OAA2B,UAAA,8BAA3B,SAA4B,YAAkB;AACtC,gBAAA,KAA6B,KAAK,SAAhC,gBAAa,GAAA,eAAE,UAAO,GAAA;AAKtB,gBAAA,OAAS,KAAI;AACrB,gBAAM,aAAa,KAAK;AAExB,gBAAI,CAAC,YAAY;AACf,qBAAO;YACR;AAED,gBAAI,cAAc,KAAK,CAAC,GAAI,UAAU,GAAG;AACvC,qBAAO;YACR;AAED,gBAAI,UAAU,GAAG;AACf,uBAAS,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC/C,oBAAI,cAAc,KAAK,KAAK,GAAI,UAAU,GAAG;AAC3C,yBAAO;gBACR;cACF;YACF;AAED,mBAAO;;AAMT,UAAAA,OAAmB,UAAA,sBAAnB,SAAoB,YAAkB;AAC5B,gBAAA,UAAY,KAAK,QAAO;AAExB,gBAAA,OAAS,KAAI;AACrB,gBAAM,aAAa,KAAK;AAExB,gBAAI,CAAC,YAAY;AACf,qBAAO;YACR;AAED,gBAAI,eAAe,GAAG;AACpB,qBAAO,KAAK,sBAAsB,UAAU;YAC7C;AAED,gBAAM,YAAY,WAAW;AAE7B,gBAAI;AACJ,gBAAI;AAEJ,gBAAI,YAAY,GAAG;AACjB,uBAAS,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC/C,8BAAc,KAAK,KAAK;AAExB,oBAAI,YAAY,WAAW,WAAW;AACpC,6BAAW;AAEX,yBAAO,WAAW,WAAW,YAAY;AACvC,wBAAI,CAAC,QAAQ,YAAY,QAAQ,GAAG,WAAW,QAAQ,CAAC,GAAG;AACzD;oBACD;kBACF;AAED,sBAAI,aAAa,WAAW;AAC1B,2BAAO;kBACR;gBACF;cACF;YACF,OAAM;AACL,uBAAS,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC/C,8BAAc,KAAK,KAAK;AAExB,oBACE,YAAY,WAAW,aACvB,QAAQ,YAAY,CAAC,GAAG,WAAW,CAAC,CAAC,GACrC;AACA,yBAAO;gBACR;cACF;YACF;AAED,mBAAO;;AAMT,UAAAA,OAAqB,UAAA,wBAArB,SAAsB,YAAkB;AAC9B,gBAAA,OAAS,KAAI;AAErB,gBAAI,CAAC,KAAK,QAAQ;AAChB,qBAAO;YACR;AAED,gBAAM,cAAc,KAAK,CAAC;AAClB,gBAAA,SAAW,YAAW;AAE9B,gBAAI,WAAW,WAAW,QAAQ;AAChC,qBAAO;YACR;AAEO,gBAAA,UAAY,KAAK,QAAO;AAEhC,gBAAI,SAAS,GAAG;AACd,uBAAS,QAAQ,GAAG,QAAQ,QAAQ,SAAS;AAC3C,oBAAI,CAAC,QAAQ,YAAY,KAAK,GAAG,WAAW,KAAK,CAAC,GAAG;AACnD,yBAAO;gBACR;cACF;AAED,qBAAO;YACR;AAED,mBAAO,QAAQ,YAAY,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,IAAI;;AAMtD,UAAAA,OAAA,UAAA,aAAA,SAAW,KAAU,OAAc,eAAqB;AAC9C,gBAAA,OAAS,KAAI;AACb,gBAAA,SAAW,KAAI;AAEvB,gBAAM,gBAAgB,KAAK;AAE3B,gBAAI,QAAQ;AAEZ,mBAAO,SAAS;AACd,mBAAK,QAAQ,CAAC,IAAI,KAAK,KAAK;AAC5B,qBAAO,QAAQ,CAAC,IAAI,OAAO,KAAK;YACjC;AAED,iBAAK,CAAC,IAAI;AACV,mBAAO,CAAC,IAAI;AAEJ,gBAAA,UAAY,KAAK,QAAO;AAEhC,gBAAI,kBAAkB,WAAW,kBAAkB,eAAe;AAChE,mBAAK,IAAG;AACR,qBAAO,IAAG;YACX,WAAU,iBAAiB,SAAS;AAEnC,mBAAK,SAAS,OAAO,SAAS;YAC/B;;AAOH,UAAAA,OAAgB,UAAA,mBAAhB,SAAiB,UAAsB;AAAvC,gBAgCC,QAAA;AA/BO,gBAAA,KAAgC,KAAK,SAAnC,gBAAa,GAAA,eAAE,aAAU,GAAA;AAK1B,gBAAA,WAAY,KAAK,KAAI,CAAA;AACrB,gBAAA,aAAc,KAAK,OAAM,CAAA;AAEhC,iBAAK,OAAO,CAAC,IAAI,WAAW,KAC1B,SAAC,OAAU;AACT,kBAAI,MAAK,mBAAmB;AAC1B,2BAAW,OAAM,MAAK,SAAS,QAAQ;cACxC;AAED,kBAAI,MAAK,sBAAsB;AAC7B,8BAAc,OAAM,MAAK,SAAS,QAAQ;cAC3C;AAED,qBAAO;eAET,SAAC,OAAY;AACX,kBAAM,WAAW,MAAK,YAAY,QAAS;AAE3C,kBAAI,aAAa,IAAI;AACnB,sBAAK,KAAK,OAAO,UAAU,CAAC;AAC5B,sBAAK,OAAO,OAAO,UAAU,CAAC;cAC/B;AAED,oBAAM;YACR,CAAC;;AAGP,iBAACA;QAAD,EAAC;;ACtPD,eAAS,uBACP,IACA,SAAyB;AAAzB,YAAA,YAAA,QAAA;AAAA,oBAAyB,CAAA;QAAA;AAEzB,YAAI,WAAW,EAAE,GAAG;AAClB,iBAAO,uBACL,GAAG,IACH,aAAa,GAAG,SAAS,OAAO,CAAC;QAEpC;AAED,YAAI,OAAO,OAAO,YAAY;AAC5B,gBAAM,IAAI,UAAU,wCAAwC;QAC7D;AAGC,YAAA,KAQE,QARuB,SAAzB,UAAU,OAAA,SAAA,kBAAe,IACzB,gBAOE,QAPW,eACb,KAME,QANe,WAAjB,YAAY,OAAA,SAAA,QAAK,IACjB,KAKE,QAAO,SALT,UAAU,OAAA,SAAA,IAAC,IACX,aAIE,QAJQ,YACV,gBAGE,QAHW,eACb,aAEE,QAAO,YADT,eACE,QAAO;AAEX,YAAM,oBAAoB,aACxB;UACE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACD,GACD,iBAAiB,OAAO,CAAC;AAG3B,YAAM,QAAQ,IAAI,MAAM,iBAAiB;AAGvC,YAAA,OAOE,MAAK,MANP,SAME,MAAK,QALP,kBAKE,MAAK,iBAJP,uBAIE,MAAK,sBAHP,oBAGE,MAAK,mBAFP,uBAEE,MAAK,sBADP,oBACE,MAAK;AAET,YAAM,WAAW,WAAA;AACf,cAAI,MAAM,uBACN,WAAW,SAAS,IACnB;AAEL,cAAI,iBAAiB;AACnB,kBAAO,aAAgC,GAAG;UAC3C;AAED,cAAM,WAAW,KAAK,SAAS,MAAM,YAAY,GAAG,IAAI;AAExD,cAAI,aAAa,IAAI;AACnB,gBAAI,mBAAmB;AACpB,yBACC,OACA,mBACA,QAAQ;YAEX;AAED,gBAAI,UAAU;AACZ,oBAAM,WAAW,KAAK,QAAQ,GAAI,OAAO,QAAQ,GAAG,QAAQ;AAE5D,kBAAI,sBAAsB;AACvB,8BACC,OACA,mBACA,QAAQ;cAEX;YACF;UACF,OAAM;AACL,gBAAM,WAAW,GAAG,MAAM,MAAM,SAA6B;AAC7D,gBAAM,SAAS,uBACV,MACD,WAAW,SAAS;AAExB,kBAAM,WAAW,QAAQ,UAAU,KAAK,MAAM;AAE9C,gBAAI,WAAW;AACb,oBAAM,iBAAiB,QAAQ;YAChC;AAED,gBAAI,mBAAmB;AACpB,yBACC,OACA,mBACA,QAAQ;YAEX;AAED,gBAAI,sBAAsB;AACvB,4BACC,OACA,mBACA,QAAQ;YAEX;UACF;AAED,iBAAO,OAAO,CAAC;QACjB;AAEA,iBAAS,QAAQ;AACjB,iBAAS,KAAK;AACd,iBAAS,aAAa;AACtB,iBAAS,UAAU;AAEnB,eAAO;MACT;;;;;;;;;;;;;AClIA,UAAM,uBAAuB,OAAO,YAAY;AAExC,UAAA,OAAS,OAAM;AASP,eAAA,mBAAmB,GAAQ,GAAM;AAC/C,eAAO,MAAM,KAAM,MAAM,KAAK,MAAM;MACtC;AAQM,eAAU,cAAc,OAAU;AACtC,eAAO,MAAM,gBAAgB,UAAU,MAAM,eAAe;MAC9D;AAQM,eAAU,cAAc,OAAU;AACtC,eAAO,CAAC,CAAC,SAAS,OAAO,MAAM,SAAS;MAC1C;AAQM,eAAU,eAAe,OAAU;AACvC,eAAO,CAAC,EAAE,SAAS,MAAM;MAC3B;eAQgB,sBAAmB;AACjC,YAAM,UAA8B,CAAA;AAEpC,eAAO;UACL,QAAA,SAAO,KAAW;AAChB,qBAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,EAAE,OAAO;AACnD,kBAAI,QAAQ,KAAK,EAAE,CAAC,MAAM,KAAK;AAC7B,wBAAQ,OAAO,OAAO,CAAC;AACvB;cACD;YACF;;UAGH,KAAA,SAAI,KAAW;AACb,qBAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,EAAE,OAAO;AACnD,kBAAI,QAAQ,KAAK,EAAE,CAAC,MAAM,KAAK;AAC7B,uBAAO,QAAQ,KAAK,EAAE,CAAC;cACxB;YACF;;UAGH,KAAA,SAAI,KAAa,OAAa;AAC5B,qBAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,EAAE,OAAO;AACnD,kBAAI,QAAQ,KAAK,EAAE,CAAC,MAAM,KAAK;AAC7B,wBAAQ,KAAK,EAAE,CAAC,IAAI;AACpB;cACD;YACF;AAED,oBAAQ,KAAK,CAAC,KAAK,KAAK,CAAC;;;MAG/B;AAOO,UAAM,cAAe,SAAC,eAAsB;AACjD,YAAI,eAAe;AACjB,iBAAO,SAAS,eAAY;AAC1B,mBAAO,oBAAI,QAAO;UACpB;QACD;AAED,eAAO;MACT,EAAG,oBAAoB;AAQjB,eAAU,2BAA2B,SAA4B;AACrE,eAAO,SAAS,oBACd,YAA8B;AAE9B,cAAM,cAAc,WAAW;AAE/B,iBAAO,SAAS,cACd,GACA,GACA,aACA,aACA,SACA,SACA,OAA4B;AAA5B,gBAAA,UAAA,QAAA;AAAA,sBAAe,YAAW;YAAE;AAE5B,gBAAM,eAAe,CAAC,CAAC,KAAK,OAAO,MAAM;AACzC,gBAAM,eAAe,CAAC,CAAC,KAAK,OAAO,MAAM;AAEzC,gBAAI,iBAAiB,cAAc;AACjC,qBAAO;YACR;AAED,gBAAI,CAAC,gBAAgB,CAAC,cAAc;AAClC,qBAAO,YAAY,GAAG,GAAG,KAAK;YAC/B;AAED,gBAAM,UAAU,MAAM,IAAI,CAAC;AAE3B,gBAAG,WAAW,MAAM,IAAI,CAAC,GAAG;AAC1B,qBAAO,YAAY;YACpB;AAED,kBAAM,IAAI,GAAG,CAAC;AACd,kBAAM,IAAI,GAAG,CAAC;AAEd,gBAAM,SAAS,YAAY,GAAG,GAAG,KAAK;AAEtC,kBAAM,OAAO,CAAC;AACd,kBAAM,OAAO,CAAC;AAEd,mBAAO;UACT;QACF;MACF;AAWM,eAAU,eACd,GACA,GACA,SACA,MAAS;AAET,YAAI,QAAQ,EAAE;AAEd,YAAI,EAAE,WAAW,OAAO;AACtB,iBAAO;QACR;AAED,eAAO,UAAU,GAAG;AAClB,cAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,OAAO,OAAO,GAAG,GAAG,IAAI,GAAG;AAC1D,mBAAO;UACR;QACF;AAED,eAAO;MACT;AAWM,eAAU,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,gBAAgB,EAAE,MAAM;AAC1B,cAAM,mBAAuC,CAAA;AAC7C,cAAI,WAAS;AAEb,YAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,gBAAI,cAAc;AAChB,kBAAI,aAAW;AACf,kBAAI,gBAAc;AAElB,gBAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,oBAAI,CAAC,cAAY,CAAC,iBAAe,aAAW,GAAG;AAC7C,+BACE,QAAQ,MAAM,MAAM,UAAQ,eAAa,GAAG,GAAG,IAAI,KACnD,QAAQ,QAAQ,QAAQ,MAAM,MAAM,GAAG,GAAG,IAAI;AAEhD,sBAAI,YAAU;AACZ,qCAAe,aAAW,IAAI;kBAC/B;gBACF;AAED;cACF,CAAC;AAED;AACA,6BAAe;YAChB;UACH,CAAC;QACF;AAED,eAAO;MACT;AAOA,UAAM,QAAQ;AAEd,UAAM,iBAAiB,SAAS,UAAU,KAAK,KAC7C,SAAS,UAAU,MACnB,OAAO,UAAU,cAAc;AAY3B,eAAU,gBACd,GACA,GACA,SACA,MAAS;AAET,YAAM,QAAQ,KAAK,CAAC;AAEpB,YAAI,QAAQ,MAAM;AAElB,YAAI,KAAK,CAAC,EAAE,WAAW,OAAO;AAC5B,iBAAO;QACR;AAED,YAAI,OAAO;AACT,cAAI,MAAG;AAEP,iBAAO,UAAU,GAAG;AAClB,kBAAM,MAAM,KAAK;AAEjB,gBAAI,QAAQ,OAAO;AACjB,kBAAM,gBAAgB,eAAe,CAAC;AACtC,kBAAM,gBAAgB,eAAe,CAAC;AAEtC,mBACG,iBAAiB,kBAClB,kBAAkB,eAClB;AACA,uBAAO;cACR;YACF;AAED,gBACE,CAAC,eAAe,GAAG,GAAG,KACtB,CAAC,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,IAAI,GAC7C;AACA,qBAAO;YACR;UACF;QACF;AAED,eAAO;MACT;AASO,UAAM,kBAAmB,WAAA;AAC9B,YAAI,OAAO,UAAU,KAAK;AACxB,iBAAO,SAASC,iBAAgB,GAAW,GAAS;AAClD,mBAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;UAChD;QACD;AAED,eAAO,SAAS,wBAAwB,GAAW,GAAS;AAC1D,iBACE,EAAE,WAAW,EAAE,UACf,EAAE,WAAW,EAAE,UACf,EAAE,eAAe,EAAE,cACnB,EAAE,cAAc,EAAE,aAClB,EAAE,YAAY,EAAE,WAChB,EAAE,WAAW,EAAE,UACf,EAAE,cAAc,EAAE;QAEtB;MACF,EAAC;AAWK,eAAU,aACd,GACA,GACA,SACA,MAAS;AAET,YAAI,eAAe,EAAE,SAAS,EAAE;AAEhC,YAAI,gBAAgB,EAAE,MAAM;AAC1B,cAAM,mBAAuC,CAAA;AAE7C,YAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,gBAAI,cAAc;AAChB,kBAAI,aAAW;AACf,kBAAI,eAAa;AAEjB,gBAAE,QAAQ,SAAC,QAAQ,MAAI;AACrB,oBAAI,CAAC,cAAY,CAAC,iBAAe,YAAU,GAAG;AAC5C,+BAAW,QAAQ,QAAQ,QAAQ,MAAM,MAAM,GAAG,GAAG,IAAI;AAEzD,sBAAI,YAAU;AACZ,qCAAe,YAAU,IAAI;kBAC9B;gBACF;AAED;cACF,CAAC;AAED,6BAAe;YAChB;UACH,CAAC;QACF;AAED,eAAO;MACT;ACxWA,UAAM,kBAAkB,OAAO,QAAQ;AACvC,UAAM,kBAAkB,OAAO,QAAQ;AAE/B,UAAA,UAAY,OAAO,UAAS;AAM9B,eAAU,iBACd,eAAyC;AAEzC,YAAM;;UAEJ,OAAO,kBAAkB,aACrB,cAAc,UAAU,IACxB,SACE,GACA,GACA,aACA,aACA,SACA,SACA,MACG;AAAA,mBAAA,WAAW,GAAG,GAAG,IAAI;UAArB;;AAWX,iBAAS,WAAW,GAAQ,GAAQ,MAAU;AAC5C,cAAI,MAAM,GAAG;AACX,mBAAO;UACR;AAED,cAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,gBAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACxC,qBAAO,gBAAgB,GAAG,GAAG,SAAS,IAAI;YAC3C;AAED,gBAAI,SAAS,MAAM,QAAQ,CAAC;AAC5B,gBAAI,SAAS,MAAM,QAAQ,CAAC;AAE5B,gBAAI,UAAU,QAAQ;AACpB,qBAAO,WAAW,UAAU,eAAe,GAAG,GAAG,SAAS,IAAI;YAC/D;AAED,qBAAS,aAAa;AACtB,qBAAS,aAAa;AAEtB,gBAAI,UAAU,QAAQ;AACpB,qBACE,WAAW,UAAU,mBAAmB,EAAE,QAAO,GAAI,EAAE,QAAO,CAAE;YAEnE;AAED,qBAAS,aAAa;AACtB,qBAAS,aAAa;AAEtB,gBAAI,UAAU,QAAQ;AACpB,qBAAO,WAAW,UAAU,gBAAgB,GAAG,CAAC;YACjD;AAED,gBAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACxC,qBAAO,MAAM;YACd;AAED,gBAAI,iBAAiB;AACnB,uBAAS,aAAa;AACtB,uBAAS,aAAa;AAEtB,kBAAI,UAAU,QAAQ;AACpB,uBAAO,WAAW,UAAU,aAAa,GAAG,GAAG,SAAS,IAAI;cAC7D;YACF;AAED,gBAAI,iBAAiB;AACnB,uBAAS,aAAa;AACtB,uBAAS,aAAa;AAEtB,kBAAI,UAAU,QAAQ;AACpB,uBAAO,WAAW,UAAU,aAAa,GAAG,GAAG,SAAS,IAAI;cAC7D;YACF;AAED,gBAAI,EAAE,YAAY,WAAW,EAAE,YAAY,SAAS;AAClD,qBAAO,mBAAmB,EAAE,QAAO,GAAI,EAAE,QAAO,CAAE;YACnD;AAED,mBAAO,gBAAgB,GAAG,GAAG,SAAS,IAAI;UAC3C;AAED,iBAAO,MAAM,KAAK,MAAM;;AAG1B,eAAO;MACT;AC7Ga,UAAA,YAAY,iBAAgB;AAClC,UAAM,eAAe,iBAAiB,WAAM;AAAA,eAAA;MAAkB,CAAA;UAExD,oBAAoB,iBAAiB,2BAA0B,CAAE;AACjE,UAAA,uBAAuB,iBAClC,2BAA2B,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHzC,UAAMC,kBAAkC;QAC3CC,aAAa;QACbC,WAAW;QACXC,SAAS;QACTC,cAAc;QACdC,gBAAgB;QAChBC,YAAYC;QACZC,YAAYD;QACZE,QAAQF;QACRG,SAASH;QACTI,SAAS;QACTC,UAAUL;QACVM,aAAaN;QACbO,YAAYP;QACZQ,mBAAmBR;QACnBS,eAAeT;QACfU,cAAc;MAClB;ACFO,eAASC,UAE2B;AAAA,iBAAAC,OAAAC,UAAAC,QADpCC,YAASC,IAAAA,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAATF,oBAASE,IAAA,IAAAJ,UAAAI,IAAA;QAAA;AAEZ,eAAOF,UAAUG,OAAO,SAAUC,GAAQC,GAAQ;AAC9C,cAAI,OAAOD,MAAM,YAAY;AACzB,mBAAO,OAAOC,MAAM,aACd,WAAqB;AACjBD,gBAAEE,MAAM,MAAMR,SAAS;AACvBO,gBAAEC,MAAM,MAAMR,SAAS;YAC3B,IACAM;UACV;AAEA,cAAI,OAAOC,MAAM,YAAY;AACzB,mBAAOA;UACX;QACJ,CAAC;MACL;AAWO,eAASE,UAAgD;AAAA,iBAAAC,QAAAV,UAAAC,QAA7BC,YAASC,IAAAA,MAAAO,KAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAATT,oBAASS,KAAA,IAAAX,UAAAW,KAAA;QAAA;AACxC,eAAOT,UAAUG,OAAO,SAAUC,GAAQC,GAAQ;AAC9C,cAAI,OAAOD,MAAM,YAAY;AACzB,mBAAO,OAAOC,MAAM,aACd,WAAqB;AACjB,qBAAOD,EAAEC,EAAEC,MAAM,MAAMR,SAAS,CAAC;YACrC,IACAM;UACV;AAEA,cAAI,OAAOC,MAAM,YAAY;AACzB,mBAAOA;UACX;QACJ,CAAC;MACL;AAYO,eAASK,oBAAoBC,aAA2BC,KAAU;AACrE,iBAASC,QAAQ,GAAGA,QAAQF,YAAYZ,QAAQc,SAAS;AACrD,cAAIF,YAAYE,KAAK,EAAED,QAAQA,KAAK;AAChC,mBAAOC;UACX;QACJ;AAEA,eAAO;MACX;AAYO,eAASC,mBACZC,SACAC,eACF;AACE,YAAMC,eACF,OAAOD,kBAAkB,aACnBA,gBACA,SAAUE,UAAeN,KAAU;AAC/B,mBAASC,QAAQ,GAAGA,QAAQD,IAAIb,QAAQc,SAAS;AAC7C,gBAAI,CAACE,QAAQG,SAASL,KAAK,GAAGD,IAAIC,KAAK,CAAC,GAAG;AACvC,qBAAO;YACX;UACJ;AAEA,iBAAO;;AAGrB,eAAO,SAAUM,MAAaP,KAAU;AACpC,mBAASQ,YAAY,GAAGA,YAAYD,KAAKpB,QAAQqB,aAAa;AAC1D,gBACID,KAAKC,SAAS,EAAErB,WAAWa,IAAIb,UAC/BkB,aAAaE,KAAKC,SAAS,GAAGR,GAAG,GACnC;AACE,qBAAOQ;YACX;UACJ;AAEA,iBAAO;;MAEf;AAiBO,eAASC,aAIZC,iBACAC,YAC0C;AAC1C,YAAI,CAACA,cAAcA,eAAe7C,iBAAiB;AAC/C,iBAAO4C;QAIX;AAEA,eAAAE,SAAA,CAAA,GACOF,iBACAC,YAAU;UACbE,YAAY7B,QAAQ0B,gBAAgBG,YAAYF,WAAWE,UAAU;UACrEC,eAAe9B,QACX0B,gBAAgBI,eAChBH,WAAWG,aACf;UACAC,YAAY/B,QAAQ0B,gBAAgBK,YAAYJ,WAAWI,UAAU;UACrEjC,eAAea,QACXe,gBAAgB5B,eAChB6B,WAAW7B,aACf;QAAC,CAAA;MAET;AAEO,eAASkC,SACZC,IACY;AACZ,eAAO,OAAOA,OAAO,cAAeA,GAAiBD;MACzD;AAEO,eAASE,QACZD,IACAE,sBACAxC,aACF;AACE,YAAI;AACA,cAAMyC,OAAOzC,eAAewC,wBAAwB;AAEpDE,iBAAOC,eAAeL,IAAI,QAAQ;YAC9BM,cAAc;YACdC,YAAY;YACZC,OAAK,YAAYL,OAAO;YACxBM,UAAU;UACd,CAAC;iBACHC,SAAM;QACJ;MAER;AC5KO,eAASC,gBACZ7B,aACAC,KACA6B,cACF;AACE,YAAMC,kBAAkBhC,oBAAoBC,aAAaC,GAAG;AAE5D,YAAI8B,oBAAoB,IAAI;AACxBC,uBAAahC,YAAY+B,eAAe,EAAEE,SAAS;AAEnD,cAAIH,cAAc;AACd9B,wBAAYkC,OAAOH,iBAAiB,CAAC;UACzC;QACJ;MACJ;AAaO,eAASI,cAAcC,kBAA8B5D,SAAgB;AACxE,YAAMyD,YAAYI,WAAWD,kBAAkB5D,OAAM;AAErD,YAAI,OAAOyD,UAAUK,UAAU,YAAY;AACvCL,oBAAUK,MAAK;QACnB;AAEA,eAAOL;MACX;AAcO,eAASM,8BACZvC,aACAwC,SACApC,SACAC,eAC6B;AAC7B,YAAQ7B,UAAWgE,QAAXhE;AAER,eAAO,SAASsC,WACZ2B,OACAC,eACAC,QACF;AACE,cAAM1C,MAAWwC,MAAMjC,KAAK,CAAC;AAE7B,cAAIT,oBAAoBC,aAAaC,GAAG,MAAM,IAAI;AAC9C,gBAAMmC,mBAAmB,SAAnBA,oBAA+B;AACjC,kBAAMQ,eAAezC,mBAAmBC,SAASC,aAAa;AAE9D,kBAAMwC,WAAmBD,aAAaH,MAAMjC,MAAMP,GAAG;AACrD,kBAAMyB,QAAae,MAAMK,OAAOD,QAAQ;AAExC,kBAAI,CAACA,UAAU;AACXJ,sBAAMjC,KAAK0B,OAAOW,UAAU,CAAC;AAC7BJ,sBAAMK,OAAOZ,OAAOW,UAAU,CAAC;AAE/B,oBAAI,OAAOL,QAAQzB,kBAAkB,YAAY;AAC7CyB,0BAAQzB,cAAc0B,OAAOC,eAAeC,MAAM;gBACtD;cACJ;AAEAd,8BAAgB7B,aAAaC,KAAK,IAAI;AAEtC,kBACI,OAAOuC,QAAQ7D,aAAa,cAC5B6D,QAAQ7D,SAASsB,GAAG,MAAM,OAC5B;AACEwC,sBAAMjC,KAAKuC,QAAQ9C,GAAG;AACtBwC,sBAAMK,OAAOC,QAAQrB,KAAK;AAE1BZ,2BAAW2B,OAAOC,eAAeC,MAAM;AAEvC,oBAAI,OAAOH,QAAQzB,kBAAkB,YAAY;AAC7CyB,0BAAQzB,cAAc0B,OAAOC,eAAeC,MAAM;gBACtD;cACJ;;AAGJ3C,wBAAYgD,KAAK;cACbZ;cACAnC;cACAgC,WAAWE,cAAcC,kBAAkB5D,OAAM;YACrD,CAAC;UACL;;MAER;AAYO,eAASyE,gCACZjD,aACAwC,SAC6B;AAC7B,eAAO,SAASxB,WAAWyB,OAA2B;AAClD,cAAMxC,MAAMwC,MAAMjC,KAAK,CAAC;AACxB,cAAMuB,kBAAkBhC,oBAAoBC,aAAaC,GAAG;AAE5D,cAAI,CAAC8B,iBAAiB;AAClBF,4BAAgB7B,aAAaC,KAAK,KAAK;AAEvCD,wBAAY+B,eAAe,EAAEE,YAAYE,cACrCnC,YAAY+B,eAAe,EAAEK,kBAC7BI,QAAQhE,MACZ;UACJ;;MAER;AAcO,eAAS0E,iBACZlD,aACAwC,SACApC,SACAC,eAIF;AACE,YAAMS,aACF,OAAO0B,QAAQhE,WAAW,YAAY2E,SAASX,QAAQhE,MAAM,IACvD+D,8BACIvC,aACAwC,SACApC,SACAC,aACJ,IACA/B;AAEV,eAAO;UACHwC;UACAE,YACIF,cAAc0B,QAAQxD,eAChBiE,gCAAgCjD,aAAawC,OAAO,IACpDlE;;MAElB;AC3LO,UAAM8E,aAAyB;QAClCC,6BAA6B;QAC7BC,mBAAmB;QACnBC,UAAU,CAAA;MACd;AAEA,UAAIC,sBAAsB;AAEnB,eAASC,WAAW7E,aAAsB;AAC7C,YAAIA,aAAa;AACb,iBAAOwE,WAAWG,SAAS3E,WAAW;QAC1C,OAAO;AACHwE,qBAAWG,WAAW,CAAA;QAC1B;MACJ;AAUO,eAASG,aAAaJ,mBAA0B;AAAA,YAA1BA,sBAAiB,QAAA;AAAjBA,8BAAoB;QAAI;AACjDF,mBAAWE,oBAAoBA;MACnC;AAQO,eAASK,+BACZnB,SACF;AACE,YAAQ5D,cAAgB4D,QAAhB5D;AAER,eAAO,WAAY;AACf,cAAIA,eAAe,CAACwE,WAAWG,SAAS3E,WAAW,GAAG;AAClDwE,uBAAWG,SAAS3E,WAAW,IAAI;cAC/BgF,OAAO;cACPC,MAAM;;UAEd;AAEAT,qBAAWG,SAAS3E,WAAW,EAAEgF;;MAEzC;AAQO,eAASE,sCAEdtB,SAA+B;AAC7B,eAAO,WAAY;AACf,cAAQe,WAAaH,WAAbG;AACR,cAAQ3E,cAAgB4D,QAAhB5D;AAER,cAAI,CAAC2E,SAAS3E,WAAW,GAAG;AACxB2E,qBAAS3E,WAAW,IAAI;cACpBgF,OAAO;cACPC,MAAM;;UAEd;AAEAN,mBAAS3E,WAAW,EAAEgF;AACtBL,mBAAS3E,WAAW,EAAEiF;;MAE9B;AAWO,eAASE,sBACZ7C,IACF;AACE,eACIA,GAAG8C,eACH9C,GAAGG,QACU+B,eAAAA,WAAWC;MAEhC;AAYO,eAASY,mBAAmBL,OAAeC,MAAc;AAC5D,eAAOD,SAAaC,OAAOD,QAAS,KAAKM,QAAQ,CAAC,IAAC,MAAM;MAC7D;AAWO,eAASC,SAASvF,aAAyC;AAC9D,YAAI,CAACwE,WAAWE,qBAAqB,CAACE,qBAAqB;AACvDY,kBAAQC,KACJ,oFACJ;AAEAb,gCAAsB;QAC1B;AAEA,YAAQD,WAAaH,WAAbG;AAER,YAAI3E,aAAa;AACb,cAAI,CAAC2E,SAAS3E,WAAW,GAAG;AACxB,mBAAO;cACHgF,OAAO;cACPC,MAAM;cACNS,OAAO;;UAEf;AAEA,cAAuBC,UAAYhB,SAA1B3E,WAAW;AAEpB,iBAAAiC,SAAA,CAAA,GACO0D,SAAO;YACVD,OAAOL,mBAAmBM,QAAQX,OAAOW,QAAQV,IAAI;UAAC,CAAA;QAE9D;AAEA,YAAMW,gBAA8BlD,OAAOd,KAAK4C,WAAWG,QAAQ,EAAE/D,OACjE,SAACiF,kBAAkB7F,cAAgB;AAC/B6F,2BAAiBb,SAASL,SAAS3E,YAAW,EAAEgF;AAChDa,2BAAiBZ,QAAQN,SAAS3E,YAAW,EAAEiF;AAE/C,iBAAOY;QACX,GACA;UACIb,OAAO;UACPC,MAAM;QACV,CACJ;AAEA,eAAAhD,SAAA,CAAA,GACO2D,eAAa;UAChBjB,UAAUjC,OAAOd,KAAK+C,QAAQ,EAAE/D,OAC5B,SAACkF,kBAAkB9F,cAAgB;AAC/B8F,6BAAiB9F,YAAW,IAAIuF,SAASvF,YAAW;AAEpD,mBAAO8F;aAEX,CAAA,CACJ;UACAJ,OAAOL,mBAAmBO,cAAcZ,OAAOY,cAAcX,IAAI;QAAC,CAAA;MAE1E;AAaO,eAASc,gBACZnC,SAIF;AACE,eAAOY,WAAWE,oBACZ;UACIxC,YAAY6C,+BAA+BnB,OAAO;UAClDxB,YAAY8C,sCAAsCtB,OAAO;YAE7D,CAAA;MACV;AC/LA,UAAMoC,4BAAqD;QACvDzF,WAAW;QACX0F,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACb3F,QAAQ;QACRiC,MAAM;QACN2D,WAAW;MACf;AAaO,eAASC,qBAIZC,YACAC,OACAC,mBACF;AAAA,YADEA,sBAA2B,QAAA;AAA3BA,8BAA8B,CAAA;QAAE;AAEhC9D,eAAO+D,oBAAoBH,UAAU,EAAEI,QAAQ,SAACC,UAAa;AACzD,cACI,CAACX,0BAA0BW,QAAQ,KACnCH,kBAAkBI,QAAQD,QAAQ,MAAM,IAC1C;AACE,gBAAME,aAAanE,OAAOoE,yBACtBR,YACAK,QACJ;AAEA,gBAAIE,WAAWE,OAAOF,WAAWG,KAAK;AAClCtE,qBAAOC,eAAe4D,OAAOI,UAAUE,UAAU;YACrD,OAAO;AAEHN,oBAAMI,QAAQ,IAAIL,WAAWK,QAAQ;YACzC;UACJ;QACJ,CAAC;MACL;AAUO,eAASM,mBACZC,UAAmBC,MAErB;AAAA,YADI/F,cAAW+F,KAAX/F;AAEF,YAAQwC,UAAYsD,SAAZtD;AAER,YAAMI,eAAezC,mBACjBqC,QAAQpC,SACRoC,QAAQnC,aACZ;AAEA,YAAMsC,SAASmD;AAKfnD,eAAOqD,QAAQ,WAAY;AACvB,cAC4BjF,gBAExB4B,OAFAsD,qBAAwBlF,eACxB0B,QACAE,OADAF;AAGJA,gBAAMjC,KAAKpB,SAAS;AACpBqD,gBAAMK,OAAO1D,SAAS;AAEtB,cAAI2B,eAAe;AACfA,0BAAc0B,OAAOE,OAAOH,SAASG,MAAM;UAC/C;AAEA,iBAAO;;AAGXA,eAAOc,aAAa,WAAY;AAC5BA,qBAAWd,OAAOH,QAAQ5D,WAAW;;AAGzC+D,eAAOgD,MAAM,SAAU1F,KAAU;AAC7B,cAC4BiG,eAExBvD,OAFAsD,qBAAwBC,cACxBzD,QACAE,OADAF;AAGJ,cAAMlC,WAAW2F,eAAeA,aAAajG,GAAG,IAAIA;AACpD,cAAM4C,WAAWD,aAAaH,MAAMjC,MAAMD,QAAQ;AAElD,iBAAOsC,aAAa,KAAKF,OAAOhD,MAAM,MAAMM,GAAG,IAAI3B;;AAGvDqE,eAAOwB,WAAW,WAA0B;AACxC,iBAAOA,SAASxB,OAAOH,QAAQ5D,WAAW;;AAG9C+D,eAAOwD,MAAM,SAAUlG,KAAU;AAC7B,cAAQiG,eAAiBvD,OAAOsD,qBAAxBC;AAER,cAAM3F,WAAW2F,eAAeA,aAAajG,GAAG,IAAIA;AAEpD,iBAAO2C,aAAaD,OAAOF,MAAMjC,MAAMD,QAAQ,MAAM;;AAGzDoC,eAAOnC,OAAO,WAAY;AACtB,iBAAOmC,OAAOyD,cAAc5F;;AAGhCmC,eAAO0D,SAAS,SAAUpG,KAAU;AAChC,cAAAqG,wBAGI3D,OAFAsD,sBAAwBlF,gBAAauF,sBAAbvF,eAAemF,eAAYI,sBAAZJ,cACvCzD,QACAE,OADAF;AAGJ,cAAMI,WAAWD,aACbH,MAAMjC,MACN0F,eAAeA,aAAajG,GAAG,IAAIA,GACvC;AAEA,cAAI4C,aAAa,IAAI;AACjB,mBAAO;UACX;AAEA,cAAM0D,cAAc9D,MAAMjC,KAAKqC,QAAQ;AAEvCJ,gBAAMjC,KAAK0B,OAAOW,UAAU,CAAC;AAC7BJ,gBAAMK,OAAOZ,OAAOW,UAAU,CAAC;AAE/B,cAAI9B,eAAe;AACfA,0BAAc0B,OAAOE,OAAOH,SAASG,MAAM;UAC/C;AAEAd,0BAAgB7B,aAAauG,aAAa,IAAI;AAE9C,iBAAO;;AAGX5D,eAAOiD,MAAM,SAAU3F,KAAUyB,OAAY;AACzC,cAAQuE,uBAAyCtD,OAAzCsD,sBAAsBxD,QAAmBE,OAAnBF,OAAOD,WAAYG,OAAZH;AACrC,cAAQ1B,aACJmF,qBADInF,YAAYC,gBAChBkF,qBADgBlF,eAAemF,eAC/BD,qBAD+BC;AAGnC,cAAM3F,WAAW2F,eAAeA,aAAajG,GAAG,IAAIA;AACpD,cAAM4C,WAAWD,aAAaH,MAAMjC,MAAMD,QAAQ;AAElD,cAAIsC,aAAa,IAAI;AACjB,gBAAM2D,SAAShE,SAAQ9D,UAAU;AAEjC,gBAAI+D,MAAMgE,OAAOD,QAAQ;AACrB/D,oBAAMjC,KAAKpB,SAASoH;AACpB/D,oBAAMK,OAAO1D,SAASoH;YAC1B;AAEA/D,kBAAMjC,KAAKuC,QAAQxC,QAAQ;AAC3BkC,kBAAMK,OAAOC,QAAQrB,KAAK;AAE1B,gBAAIc,SAAQvE,WAAW;AACnBwE,oBAAMiE,iBAAiB/D,MAAM;YACjC;AAEA,gBAAI7B,YAAY;AACZA,yBAAW2B,OAAOD,UAASG,MAAM;YACrC;AAEA,gBAAI5B,eAAe;AACfA,4BAAc0B,OAAOD,UAASG,MAAM;YACxC;UACJ,OAAO;AACH,gBAAM4D,cAAc9D,MAAMjC,KAAKqC,QAAQ;AAEvCJ,kBAAMK,OAAOD,QAAQ,IAAInB;AAEzB,gBAAImB,WAAW,GAAG;AACdJ,oBAAMkE,WAAWJ,aAAa7E,OAAOmB,QAAQ;YACjD;AAEA,gBAAIL,SAAQvE,WAAW;AACnBwE,oBAAMiE,iBAAiB/D,MAAM;YACjC;AAEA,gBAAI,OAAO5B,kBAAkB,YAAY;AACrCA,4BAAc0B,OAAOD,UAASG,MAAM;YACxC;UACJ;;AAGJA,eAAOG,SAAS,WAAY;AACxB,iBAAOH,OAAOyD,cAActD;;MAEpC;AAaO,eAAS8D,sBACZd,UAA+Be,OAMjC;AAAA,YAJM7G,cAAW6G,MAAX7G,aACS8G,eAAYD,MAArBrE,SACAuE,mBAAgBF,MAAhBE;AAGJ,YAAiBC,sBAAwBlB,SAAjCtD;AAERlB,eAAO2F,iBAAiBnB,UAAU;UAC9BG,sBAAsB;YAClBzE,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAOqB;YACX;;UAGJZ,eAAe;YACX5E,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,kBAAeuB,eAAiBpB,SAAxBrD;AAER,qBAAO;gBACHjC,MAAM0G,aAAa1G,KAAK2G,MAAM,CAAC;gBAC/BV,MAAMS,aAAaT;gBACnB3D,QAAQoE,aAAapE,OAAOqE,MAAM,CAAC;;YAE3C;;UAGJnH,aAAa;YACTwB,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAO3F;YACX;;UAGJoH,qBAAqB;YACjB5F,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAO3F,YAAYmH,MAAM,CAAC;YAC9B;;UAGJlG,UAAU;YACNO,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAO;YACX;;UAGJnD,SAAS;YACLhB,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAOmB;YACX;;UAGJC,kBAAkB;YACdvF,cAAc;YACdmE,KAAG,SAAAA,MAAG;AACF,qBAAOoB;YACX;UACJ;QACJ,CAAC;AAED,YAAMpE,SAASmD;AAKfb,6BAAqB8B,kBAAkBpE,MAAM;MACjD;AAYO,eAAS0E,oBAIZvB,UACAwB,eACF;AACEzB,2BAAgCC,UAAUwB,aAAa;AACvDV,8BAAmCd,UAAUwB,aAAa;AAE1D,eAAOxB;MACX;ACzTA,UAAMyB,qBACF,OAAOC,WAAW,cAAcA,OAAOC,MACjCD,OAAOC,IAAI,eAAe,IAC1B;AAkBH,eAASC,sBACZC,QACAzG,IACAsB,SACF;AAcE,YAAMoF,cAAcD,OAAM9G,SAAA;UACtBpC,SAAS;UACTL,gBAAgB;QAAI,GACjBoE,SAAO;UACVtE,SAAS;QAAK,CAAA,CACjB;AAED,YAAI,CAACgD,GAAG8C,aAAa;AAEjB9C,aAAG8C,cAAc9C,GAAGG,QAAQ;QAChC;AAEA,iBAASwG,OAELC,OACAC,SACAC,SACF;AACE,eAAKF,QAAQA;AACb,eAAKC,UAAUA;AACf,eAAKC,UAAUA;AAEf,eAAKC,kBAAkBL,YAAY1G,EAAE;QACzC;AAEA2G,eAAO7C,UAAUkD,mBAAmB,CAAA;AAEpCL,eAAO7C,UAAUmD,SAAS,WAAqC;AAC3D,iBAAO;YACHC,UAAUb;YACVc,MAAM,KAAKJ;YACXH,OAAO,KAAKA;YACZQ,KAAK;YACLrI,KAAK;YACLsI,QAAQ;;;AAIhBtD,6BAAqB/D,IAAI2G,QAAQ,CAAC,eAAe,cAAc,CAAC;AAEhEA,eAAO7D,cAAwB9C,aAAAA,GAAG8C,eAAe9C,GAAGG,QAAQ,eAAc;AAE1EF,gBAAQ0G,QAA0B3G,GAAGG,MAAMmB,QAAQ5D,WAAW;AAE9D,eAAOiJ;MACX;AC9FO,eAASW,qBAAqB/B,MAAc;AAU/C,eAAO,SAAUgC,MAAgB;AAC7B,cAAIhC,QAAQgC,KAAKrJ,QAAQ;AACrB,mBAAOqJ;UACX;AAEA,cAAIhC,SAAS,GAAG;AACZ,mBAAO,CAAA;UACX;AAEA,cAAIA,SAAS,GAAG;AACZ,mBAAO,CAACgC,KAAK,CAAC,CAAC;UACnB;AAEA,cAAIhC,SAAS,GAAG;AACZ,mBAAO,CAACgC,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;UAC5B;AAEA,cAAIhC,SAAS,GAAG;AACZ,mBAAO,CAACgC,KAAK,CAAC,GAAGA,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;UACrC;AAEA,cAAMC,QAAQ,CAAA;AAEd,mBAASxI,QAAQ,GAAGA,QAAQuG,MAAMvG,SAAS;AACvCwI,kBAAMxI,KAAK,IAAIuI,KAAKvI,KAAK;UAC7B;AAEA,iBAAOwI;;MAEf;AC7BA,eAASC,UAAUC,OAAclH,OAAY;AACzC,YAAQtC,SAAWwJ,MAAXxJ;AAER,iBAASc,QAAQ,GAAGA,QAAQd,QAAQ,EAAEc,OAAO;AACzC,cAAI0I,MAAM1I,KAAK,MAAMwB,OAAO;AACxB,mBAAOxB,QAAQ;UACnB;QACJ;AAEA,eAAO;MACX;AAUO,eAAS2I,wBAAwB;AACpC,YAAMpG,QAAe,CAAA;AACrB,YAAMjC,OAAiB,CAAA;AAEvB,eAAO,SAASsI,gBAAgB7I,KAAayB,OAAY;AACrD,cAAM2G,OAAO,OAAO3G;AAEpB,cAAI2G,SAAS,cAAcA,SAAS,UAAU;AAC1C,mBAAO3G,MAAMqH,SAAQ;UACzB;AAEA,cAAI,OAAOrH,UAAU,UAAU;AAC3B,gBAAIe,MAAMrD,QAAQ;AACd,kBAAM4J,aAAaL,UAAUlG,OAAO,IAAI;AAExC,kBAAIuG,eAAe,GAAG;AAClBvG,sBAAMA,MAAMrD,MAAM,IAAI;cAC1B,OAAO;AACHqD,sBAAMP,OAAO8G,UAAU;AACvBxI,qBAAK0B,OAAO8G,UAAU;cAC1B;AAEAxI,mBAAKA,KAAKpB,MAAM,IAAIa;AAEpB,kBAAMgJ,cAAcN,UAAUlG,OAAOf,KAAK;AAE1C,kBAAIuH,gBAAgB,GAAG;AACnB,uBAAA,WACIzI,KAAK2G,MAAM,GAAG8B,WAAW,EAAEC,KAAK,GAAG,KAAK,OAAG;cAEnD;YACJ,OAAO;AACHzG,oBAAM,CAAC,IAAIf;AACXlB,mBAAK,CAAC,IAAIP;YACd;AAEA,mBAAOyB;UACX;AAEA,iBAAO,KAAKA;;MAEpB;AAWO,eAASyH,uBAA6BC,KAAW;AACpD,YAAMC,YAAY,OAAOD;AAEzB,eAAOA,QAAQC,cAAc,YAAYA,cAAc,cACjDC,KAAKC,UAAUH,KAAKP,sBAAqB,CAAE,IAC3CO;MACV;AAYO,eAASI,0BAA0Bf,MAAW;AACjD,YAAIxI,MAAM;AAEV,iBAASC,QAAQ,GAAGA,QAAQuI,KAAKrJ,QAAQc,SAAS;AAC9CD,iBAAOkJ,uBAAuBV,KAAKvI,KAAK,CAAC,IAAI;QACjD;AAEA,eAAO,CAACD,GAAG;MACf;AAWO,eAASwJ,sBACZjH,SACF;AACE,eAAO,OAAOA,QAAQ3D,eAAe,aAC/B2D,QAAQ3D,aACR2K;MACV;AAYO,eAASE,wBAAwBnJ,UAAeN,KAAU;AAC7D,eAAOM,SAAS,CAAC,MAAMN,IAAI,CAAC;MAChC;AC5HO,eAAS0J,uBACZzI,IAC6B;AAC7B,YAAI,OAAOA,OAAO,YAAY;AAC1B,iBAAO,SACH0I,eACAC,6BACA/D,UAAgB;AAAA,mBACT5E,GAAG4E,SAASrD,OAAOqD,SAAStD,SAASsD,QAAQ;UAAC;QAC7D;MACJ;AAWO,eAASgE,WACZtH,SACO;AACP,eACIA,QAAQnE,cACPmE,QAAQxE,eAAe+L,WAAAA,aACvBvH,QAAQpE,kBAAkB4L,WAAAA,gBAC3BC,WAAAA;MAER;AAWO,eAASC,iBACZ1H,SACyB;AACzB,eACIA,QAAQjE,cACPiE,QAAQrE,gBAAgBuL,2BACzBpL;MAER;AAWO,eAAS6L,gBACZ3H,SACwB;AACxB,eAAO5C,QACH4C,QAAQrE,gBAAgBsL,sBAAsBjH,OAAO,GACrD,OAAOA,QAAQzD,kBAAkB,cAAcyD,QAAQzD,eACvD,OAAOyD,QAAQ/D,YAAY,YACvB+J,qBAAqBhG,QAAQ/D,OAAO,CAC5C;MACJ;AClFO,eAAS2L,wBACZzH,QACF;AACE,YACe7D,oBACX6D,OADAH,QAAW1D;AAef,YAAMuL,oBAAoB,SAASA,qBAGjC;AAAA,mBAAAnL,OAAAC,UAAAC,QADKqJ,OAAInJ,IAAAA,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAJkJ,iBAAIlJ,IAAA,IAAAJ,UAAAI,IAAA;UAAA;AAEP,cAAI,CAACT,kBAAkB2J,IAAI,GAAG;AAC1B,mBAAO9F,OAAOhD,MAAM,MAAM8I,IAAI;UAClC;AAEA,cAAM6B,SAAS3H,OAAOzB,GAAGvB,MAAM,MAAM8I,IAAI;AAEzC9F,iBAAOiD,IAAI6C,MAAM6B,MAAM;AAEvB,iBAAOA;;AAGXrF,6BAAqBtC,QAAQ0H,iBAAiB;AAE9C,eAAOA;MACX;;ACuBME,UAAAA,QAAe,SAAfA,OAGJrJ,IAAiCsJ,eAA+B;AAI9D,YAAMhI,UAAgCgI,iBAAiBzM;AAEvD,YAAIkD,SAASC,EAAE,GAAG;AACd,cAAMuJ,YAAYvJ,GAAG6F;AACrB,cAAM2D,gBAAgBhK,aAClBQ,GAAGsB,SACHA,OACJ;AAEA,iBAAO+H,OAAoCE,WAAWC,aAAa;QACvE;AAEA,YAAI,OAAOxJ,OAAO,UAAU;AACxB,iBAAO,SAIHyJ,WACAC,gBACF;AAOE,gBAAI,OAAOD,cAAc,YAAY;AACjC,kBAAMD,iBAAgBhK,aAClBQ,IACA0J,cACJ;AAEA,qBAAOL,OAAMI,WAAWD,cAAa;YACzC;AAEA,gBAAMA,iBAAgBhK,aAClBQ,IACAyJ,SACJ;AAEA,mBAAOJ,OAAMG,cAAa;;QAElC;AAEA,YAAIlI,QAAQtE,SAAS;AACjB,iBAAOwJ,sBAAsB6C,QAAOrJ,IAAIsB,OAAO;QACnD;AAEA,YAAMqI,mBAAsChK,SACrC9C,CAAAA,GAAAA,iBACAyE,SAAO;UACVhE,QACI,OAAOgE,QAAQhE,WAAW,YAAYgE,QAAQhE,UAAU,IAClDgE,QAAQhE,SACRT,gBAAgBS;UAC1BC,SACI,OAAO+D,QAAQ/D,YAAY,YAAY+D,QAAQ/D,WAAW,IACpD+D,QAAQ/D,UACRV,gBAAgBU;UAC1BC,SACI,OAAO8D,QAAQ9D,YAAY,YAAY8D,QAAQ9D,WAAW,IACpD8D,QAAQ9D,UACRX,gBAAgBW;UAC1BE,aAAa4D,QAAQ5D,eAAemF,sBAAsB7C,EAAE;SAC/D;AACD,YAAMlB,cAAiC,CAAA;AAuBnC6K,yBApBAxM;AAoBAwM,yBAnBA7M;YACAC,YAkBA4M,iBAlBA5M;AAkBA4M,yBAjBA3M;AAiBA2M,yBAhBA1M;AAgBA0M,yBAfAzM;AAeAyM,yBAdAtM;AAcAsM,yBAbArM;AAaAqM,yBAZApM;YACAC,UAWAmM,iBAXAnM,SACAoC,aAUA+J,iBAVA/J,YACAC,gBASA8J,iBATA9J,eACAC,aAQA6J,iBARA7J;AAQA6J,yBAPAlM;AAOAkM,yBANAjM;AAMAiM,yBALAhM;YACAC,oBAIA+L,iBAJA/L;AAIA+L,yBAHA9L;AAGA8L,yBAFA7L;AACG8L,YAAAA,gBAAaC,8BAChBF,kBAAgBG,SAAA;AAEpB,YAAM5K,UAAU0J,WAAWe,gBAAgB;AAC3C,YAAMxK,gBAAgB6J,iBAAiBW,gBAAgB;AAEvD,YAAMI,gBAAgB/H,iBAClBlD,aACA6K,kBACAzK,SACAC,aACJ;AACA,YAAM6K,eAAevG,gBAAgBkG,gBAAgB;AAErD,YAAM3E,eAAeiE,gBAAgBU,gBAAgB;AAErD,YAAM7D,sBAAqDnG,SAAA,CAAA,GACpDiK,eAAa;UAChB1K;UACAC;UACApC;UACAS;UACAoC,YAAY6I,uBACR1K,QACI6B,YACAmK,cAAcnK,YACdoK,aAAapK,UACjB,CACJ;UACAC,eAAe4I,uBAAuB5I,aAAa;UACnDC,YAAY2I,uBACR1K,QACI+B,YACAiK,cAAcjK,YACdkK,aAAalK,UACjB,CACJ;UACAkF;SACH;AAED,YAAMJ,WAAWqF,QAAQjK,IAAI8F,mBAAmB;AAEhD,YAAIrE,SAAS0E,oBAAkDvB,UAAU;UACrE9F;UACAwC,SAASqI;UACT9D,kBAAkB7F;QACtB,CAAC;AAED,YAAIpC,mBAAmB;AACnB6D,mBAASyH,wBAAuCzH,MAAM;QAC1D;AAEAxB,gBAAQwB,QAASzB,GAAiBG,MAAMmB,QAAQ5D,WAAW;AAE3D,eAAO+D;MACX;AAWA4H,YAAM9G,aAAaA;AAWnB8G,YAAM7G,eAAeA;AAcrB6G,YAAM3K,UAAU,WAA8B;AAC1C,eAAOA,QAAOD,MAAA,QAAAR,SAAiB,KAAKoL;MACxC;AAaAA,YAAMa,OAAOb,MAAM;QAAEvM,aAAa;MAAK,CAAC;AAaxCuM,YAAMpG,WAAWA;AAajBoG,YAAMc,WAAWd,MAAM;QAAE7L,SAAS4M;MAAS,CAAC;AAa5Cf,YAAMjH,oBAAoB,SAASA,oBAA6B;AAC5D,eAAOF,WAAWE;MACtB;AAcAiH,YAAMtJ,WAAW,SAASA,UAASC,IAAuB;AACtD,eAAO,OAAOA,OAAO,cAAc,CAAC,CAACA,GAAGD;MAC5C;AAcAsJ,YAAMlM,aAAa,SAAUkN,YAAqB;AAC9C,eAAOhB,MAAM;UAAElM,YAAYkN;QAAW,CAAC;MAC3C;AAcAhB,YAAMhM,aAAa,SAAUiN,YAA2B;AACpD,eAAOjB,MAAM;UAAEhM,YAAYiN;QAAW,CAAC;MAC3C;AAiDA,eAAShN,OASLA,SACAiN,eACF;AACE,YAAIA,kBAAkB,MAAM;AACxB,iBAAOlB,MAAM;YACT/L,QAAAA;YACAQ,cAAcyM;UAClB,CAAC;QACL;AAEA,YAAI,OAAOA,kBAAkB,UAAU;AACnC,cAAQ9M,WAA2B8M,cAA3B9M,UAAUK,eAAiByM,cAAjBzM;AAElB,iBAAOuL,MAAM;YACT/L,QAAAA;YACAG;YACAK;UACJ,CAAC;QACL;AAEA,YAAI,OAAOyM,kBAAkB,YAAY;AACrC,iBAAOlB,MAAM;YACT/L,QAAAA;YACAG,UAAU8M;YACVzM,cAAc;UAClB,CAAC;QACL;AAEA,eAAOuL,MAAM;UAAE/L,QAAAA;QAAO,CAAC;MAC3B;AAcA+L,YAAM/L,SAASA;AAcf+L,YAAM9L,UAAU,SAASA,QAAQA,SAAiB;AAC9C,eAAO8L,MAAM;UAAE9L;QAAQ,CAAC;MAC5B;AAcA8L,YAAM7L,UAAU,SAASA,QAAQA,SAAiB;AAC9C,eAAO6L,MAAM;UAAE7L;QAAQ,CAAC;MAC5B;AAaA6L,YAAMhG,UAAU,SAAU3F,aAAqB;AAC3C,eAAO2L,MAAM;UAAE3L;QAAY,CAAC;MAChC;AAaA2L,YAAMmB,UAAUnB,MAAM;QAClBtM,WAAW;QACXe,cAAc;MAClB,CAAC;AAaDuL,YAAMoB,QAAQpB,MAAM;QAAErM,SAAS;MAAK,CAAC;AAarCqM,YAAMqB,YAAYrB,MAAM;QAAEpM,cAAc;MAAK,CAAC;AAc9CoM,YAAMsB,gBAAgB,SAAUhN,YAAuB;AACnD,eAAO0L,MAAM;UAAEpM,cAAc;UAAMU;QAAW,CAAC;MACnD;AAaA0L,YAAMuB,UAAUvB,MAAM;QAAEnM,gBAAgB;MAAK,CAAC;AAc9CmM,YAAMxL,gBAAgB,SAClBA,eAA0B;AAAA,eACzBwL,MAAM;UAAExL;QAAc,CAAC;MAAC;AAc7BwL,YAAMzL,oBAAoB,SACtBA,mBAA6B;AAAA,eAC5ByL,MAAM;UAAEzL;QAAkB,CAAC;MAAC;AAIjCwC,aAAOC,eAAegJ,OAAO,WAAW;QACpC/I,cAAc;QACdC,YAAY;QACZC,OAAO6I;QACP5I,UAAU;MACd,CAAC;;;;;", "names": ["<PERSON><PERSON>", "areRegExpsEqual", "DEFAULT_OPTIONS", "isDeepEqual", "isPromise", "isReact", "isSerialized", "isShallowEqual", "matchesArg", "undefined", "matchesKey", "maxAge", "maxArgs", "maxSize", "onExpire", "profileName", "serializer", "updateCacheForKey", "transformArgs", "updateExpire", "combine", "_len", "arguments", "length", "functions", "Array", "_key", "reduce", "f", "g", "apply", "compose", "_len2", "_key2", "findExpirationIndex", "expirations", "key", "index", "createFindKeyIndex", "isEqual", "isMatchingKey", "areKeysEqual", "cache<PERSON>ey", "keys", "keysIndex", "mergeOptions", "originalOptions", "newOptions", "_extends", "onCacheAdd", "onCacheChange", "onCacheHit", "isMoized", "fn", "setName", "originalFunctionName", "name", "Object", "defineProperty", "configurable", "enumerable", "value", "writable", "_unused", "clearExpiration", "<PERSON><PERSON><PERSON><PERSON>", "expirationIndex", "clearTimeout", "timeoutId", "splice", "createTimeout", "expirationMethod", "setTimeout", "unref", "createOnCacheAddSetExpiration", "options", "cache", "moizedOptions", "moized", "findKeyIndex", "keyIndex", "values", "unshift", "push", "createOnCacheHitResetExpiration", "getMaxAgeOptions", "isFinite", "statsCache", "anonymousProfileNameCounter", "isCollectingStats", "profiles", "hasWarningDisplayed", "clearStats", "collectStats", "createOnCacheAddIncrementCalls", "calls", "hits", "createOnCacheHitIncrementCallsAndHits", "getDefaultProfileName", "displayName", "getUsagePercentage", "toFixed", "getStats", "console", "warn", "usage", "profile", "completeStats", "completeProfiles", "computedProfiles", "getStatsOptions", "ALWAYS_SKIPPED_PROPERTIES", "callee", "caller", "constructor", "prototype", "copyStaticProperties", "originalFn", "newFn", "skippedProperties", "getOwnPropertyNames", "for<PERSON>ach", "property", "indexOf", "descriptor", "getOwnPropertyDescriptor", "get", "set", "addInstanceMethods", "memoized", "_ref", "clear", "_microMemoizeOptions", "transform<PERSON>ey", "has", "cacheSnapshot", "remove", "_moized$_microMemoize", "existingKey", "cutoff", "size", "updateAsyncCache", "orderByLru", "addInstanceProperties", "_ref2", "moizeOptions", "originalFunction", "microMemoizeOptions", "defineProperties", "currentCache", "slice", "expirationsSnapshot", "createMoizeInstance", "configuration", "REACT_ELEMENT_TYPE", "Symbol", "for", "createMoizedComponent", "moizer", "reactMoizer", "Moized", "props", "context", "updater", "MoizedComponent", "isReactComponent", "render", "$$typeof", "type", "ref", "_owner", "createGetInitialArgs", "args", "clone", "<PERSON><PERSON><PERSON><PERSON>", "array", "createDefaultReplacer", "defaultReplacer", "toString", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "join", "getStringifiedArgument", "arg", "typeOfArg", "JSON", "stringify", "defaultArgumentSerializer", "getSerializerFunction", "getIsSerializedKeyEqual", "createOnCacheOperation", "_cacheIgnored", "_microMemoizeOptionsIgnored", "getIsEqual", "deepEqual", "shallowEqual", "sameValueZeroEqual", "getIsMatchingKey", "getTransformKey", "createRefreshableMoized", "refreshableMoized", "result", "moize", "passedOptions", "moizeable", "mergedOptions", "curriedFn", "curriedOptions", "coalescedOptions", "customOptions", "_objectWithoutPropertiesLoose", "_excluded", "maxAgeOptions", "statsOptions", "memoize", "deep", "infinite", "Infinity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "expireOptions", "promise", "react", "serialize", "serializeWith", "shallow"]}
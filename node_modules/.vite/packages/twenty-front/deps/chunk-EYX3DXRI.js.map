{"version": 3, "sources": ["../../../../date-fns/esm/locale/sv/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/sv/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/sv/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/sv/_lib/localize/index.js", "../../../../date-fns/esm/locale/sv/_lib/match/index.js", "../../../../date-fns/esm/locale/sv/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre än en sekund',\n    other: 'mindre än {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'en sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'en halv minut',\n  lessThanXMinutes: {\n    one: 'mindre än en minut',\n    other: 'mindre än {{count}} minuter'\n  },\n  xMinutes: {\n    one: 'en minut',\n    other: '{{count}} minuter'\n  },\n  aboutXHours: {\n    one: 'ungefär en timme',\n    other: 'ungefär {{count}} timmar'\n  },\n  xHours: {\n    one: 'en timme',\n    other: '{{count}} timmar'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dagar'\n  },\n  aboutXWeeks: {\n    one: 'ungefär en vecka',\n    other: 'ungefär {{count}} vecka'\n  },\n  xWeeks: {\n    one: 'en vecka',\n    other: '{{count}} vecka'\n  },\n  aboutXMonths: {\n    one: 'ungefär en månad',\n    other: 'ungefär {{count}} månader'\n  },\n  xMonths: {\n    one: 'en månad',\n    other: '{{count}} månader'\n  },\n  aboutXYears: {\n    one: 'ungefär ett år',\n    other: 'ungefär {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'över ett år',\n    other: 'över {{count}} år'\n  },\n  almostXYears: {\n    one: 'nästan ett år',\n    other: 'nästan {{count}} år'\n  }\n};\nvar wordMapping = ['noll', 'en', 'två', 'tre', 'fyra', 'fem', 'sex', 'sju', 'åtta', 'nio', 'tio', 'elva', 'tolv'];\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    if (options && options.onlyNumeric) {\n      result = tokenValue.other.replace('{{count}}', String(count));\n    } else {\n      result = tokenValue.other.replace('{{count}}', count < 13 ? wordMapping[count] : String(count));\n    }\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' sedan';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE d MMMM y',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'y-MM-dd'\n};\nvar timeFormats = {\n  full: \"'kl'. HH:mm:ss zzzz\",\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'kl.' {{time}}\",\n  long: \"{{date}} 'kl.' {{time}}\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "var formatRelativeLocale = {\n  lastWeek: \"'i' EEEE's kl.' p\",\n  yesterday: \"'igår kl.' p\",\n  today: \"'idag kl.' p\",\n  tomorrow: \"'imorgon kl.' p\",\n  nextWeek: \"EEEE 'kl.' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['f<PERSON><PERSON>', 'e<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1:a kvartalet', '2:a kvartalet', '3:e kvartalet', '4:e kvartalet']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'maj', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'mars', 'april', 'maj', 'juni', 'juli', 'augusti', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['sö', 'må', 'ti', 'on', 'to', 'fr', 'lö'],\n  abbreviated: ['sön', 'mån', 'tis', 'ons', 'tors', 'fre', 'lör'],\n  wide: ['söndag', 'måndag', 'tisdag', 'onsdag', 'torsdag', 'fredag', 'lördag']\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sv.html#1888\nvar dayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morg.',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  abbreviated: {\n    am: 'f.m.',\n    pm: 'e.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'efterm.',\n    evening: 'kväll',\n    night: 'natt'\n  },\n  wide: {\n    am: 'förmiddag',\n    pm: 'eftermiddag',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'morgon',\n    afternoon: 'eftermiddag',\n    evening: 'kväll',\n    night: 'natt'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  abbreviated: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på efterm.',\n    evening: 'på kvällen',\n    night: 'på natten'\n  },\n  wide: {\n    am: 'fm',\n    pm: 'em',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på eftermiddagen',\n    evening: 'på kvällen',\n    night: 'på natten'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n      case 2:\n        return number + ':a';\n    }\n  }\n  return number + ':e';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(:a|:e)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(f\\.? ?Kr\\.?|f\\.? ?v\\.? ?t\\.?|e\\.? ?Kr\\.?|v\\.? ?t\\.?)/i,\n  abbreviated: /^(f\\.? ?Kr\\.?|f\\.? ?v\\.? ?t\\.?|e\\.? ?Kr\\.?|v\\.? ?t\\.?)/i,\n  wide: /^(före <PERSON>|före vår tid|efter <PERSON>|vår tid)/i\n};\nvar parseEraPatterns = {\n  any: [/^f/i, /^[ev]/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](:a|:e)? kvartalet/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar[s]?|apr|maj|jun[i]?|jul[i]?|aug|sep|okt|nov|dec)\\.?/i,\n  wide: /^(januari|februari|mars|april|maj|juni|juli|augusti|september|oktober|november|december)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^maj/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smtofl]/i,\n  short: /^(sö|må|ti|on|to|fr|lö)/i,\n  abbreviated: /^(sön|mån|tis|ons|tors|fre|lör)/i,\n  wide: /^(söndag|måndag|tisdag|onsdag|torsdag|fredag|lördag)/i\n};\nvar parseDayPatterns = {\n  any: [/^s/i, /^m/i, /^ti/i, /^o/i, /^to/i, /^f/i, /^l/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^([fe]\\.?\\s?m\\.?|midn(att)?|midd(ag)?|(på) (morgonen|eftermiddagen|kvällen|natten))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^f/i,\n    pm: /^e/i,\n    midnight: /^midn/i,\n    noon: /^midd/i,\n    morning: /morgon/i,\n    afternoon: /eftermiddag/i,\n    evening: /kväll/i,\n    night: /natt/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Swedish locale.\n * @language Swedish\n * @iso-639-2 swe\n * <AUTHOR> [@ejulen]{@link https://github.com/ejulen}\n * <AUTHOR> [@alexandernanberg]{@link https://github.com/alexandernanberg}\n * <AUTHOR> [@limelights]{@link https://github.com/limelights}\n */\nvar locale = {\n  code: 'sv',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;AAAA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,cAAc,CAAC,QAAQ,MAAM,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ,MAAM;AAChH,IAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAClE,MAAI;AACJ,MAAI,aAAa,qBAAqB,KAAK;AAC3C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,QAAI,WAAW,QAAQ,aAAa;AAClC,eAAS,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,IAC9D,OAAO;AACL,eAAS,WAAW,MAAM,QAAQ,aAAa,QAAQ,KAAK,YAAY,KAAK,IAAI,OAAO,KAAK,CAAC;AAAA,IAChG;AAAA,EACF;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;ACtFf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;ACjCf,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,OAAO,WAAW,UAAU;AAC9E,SAAO,qBAAqB,KAAK;AACnC;AACA,IAAO,yBAAQ;;;ACVf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,SAAS,OAAO;AAAA,EACzB,aAAa,CAAC,SAAS,OAAO;AAAA,EAC9B,MAAM,CAAC,gBAAgB,eAAe;AACxC;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,iBAAiB,iBAAiB,iBAAiB,eAAe;AAC3E;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC3G,MAAM,CAAC,WAAW,YAAY,QAAQ,SAAS,OAAO,QAAQ,QAAQ,WAAW,aAAa,WAAW,YAAY,UAAU;AACjI;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,KAAK;AAAA,EAC9D,MAAM,CAAC,UAAU,UAAU,UAAU,UAAU,WAAW,UAAU,QAAQ;AAC9E;AAGA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa,UAAU;AAChE,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,MAAM,SAAS,IAAI;AAC9B,YAAQ,SAAS,IAAI;AAAA,MACnB,KAAK;AAAA,MACL,KAAK;AACH,eAAO,SAAS;AAAA,IACpB;AAAA,EACF;AACA,SAAO,SAAS;AAClB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAO,mBAAQ;;;AC9Hf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,QAAQ;AACvB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACrG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK;AACzD;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AACP;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;AChFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["formatDistance", "formatRelative", "ordinalNumber", "valueCallback"]}
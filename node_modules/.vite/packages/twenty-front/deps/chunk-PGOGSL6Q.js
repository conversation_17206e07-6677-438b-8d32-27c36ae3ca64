import {
  Kind,
  TypeInfo,
  getNamedType,
  graphql_exports,
  init_graphql,
  isLeafType,
  parse,
  print,
  visit,
  visitWithTypeInfo
} from "./chunk-I7RZVVY3.js";
import {
  __commonJS,
  __toCommonJS
} from "./chunk-XPZLJQLW.js";

// node_modules/graphql-ws/lib/utils.js
var require_utils = __commonJS({
  "node_modules/graphql-ws/lib/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.limitCloseReason = exports.areGraphQLErrors = exports.isAsyncGenerator = exports.isAsyncIterable = exports.isObject = exports.extendedTypeof = void 0;
    function extendedTypeof(val) {
      if (val === null) {
        return "null";
      }
      if (Array.isArray(val)) {
        return "array";
      }
      return typeof val;
    }
    exports.extendedTypeof = extendedTypeof;
    function isObject(val) {
      return extendedTypeof(val) === "object";
    }
    exports.isObject = isObject;
    function isAsyncIterable3(val) {
      return typeof Object(val)[Symbol.asyncIterator] === "function";
    }
    exports.isAsyncIterable = isAsyncIterable3;
    function isAsyncGenerator(val) {
      return isObject(val) && typeof Object(val)[Symbol.asyncIterator] === "function" && typeof val.return === "function";
    }
    exports.isAsyncGenerator = isAsyncGenerator;
    function areGraphQLErrors(obj) {
      return Array.isArray(obj) && // must be at least one error
      obj.length > 0 && // error has at least a message
      obj.every((ob) => "message" in ob);
    }
    exports.areGraphQLErrors = areGraphQLErrors;
    function limitCloseReason(reason, whenTooLong) {
      return reason.length < 124 ? reason : whenTooLong;
    }
    exports.limitCloseReason = limitCloseReason;
  }
});

// node_modules/graphql-ws/lib/common.js
var require_common = __commonJS({
  "node_modules/graphql-ws/lib/common.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.stringifyMessage = exports.parseMessage = exports.isMessage = exports.validateMessage = exports.MessageType = exports.CloseCode = exports.DEPRECATED_GRAPHQL_WS_PROTOCOL = exports.GRAPHQL_TRANSPORT_WS_PROTOCOL = void 0;
    var utils_1 = require_utils();
    exports.GRAPHQL_TRANSPORT_WS_PROTOCOL = "graphql-transport-ws";
    exports.DEPRECATED_GRAPHQL_WS_PROTOCOL = "graphql-ws";
    var CloseCode;
    (function(CloseCode2) {
      CloseCode2[CloseCode2["InternalServerError"] = 4500] = "InternalServerError";
      CloseCode2[CloseCode2["InternalClientError"] = 4005] = "InternalClientError";
      CloseCode2[CloseCode2["BadRequest"] = 4400] = "BadRequest";
      CloseCode2[CloseCode2["BadResponse"] = 4004] = "BadResponse";
      CloseCode2[CloseCode2["Unauthorized"] = 4401] = "Unauthorized";
      CloseCode2[CloseCode2["Forbidden"] = 4403] = "Forbidden";
      CloseCode2[CloseCode2["SubprotocolNotAcceptable"] = 4406] = "SubprotocolNotAcceptable";
      CloseCode2[CloseCode2["ConnectionInitialisationTimeout"] = 4408] = "ConnectionInitialisationTimeout";
      CloseCode2[CloseCode2["ConnectionAcknowledgementTimeout"] = 4504] = "ConnectionAcknowledgementTimeout";
      CloseCode2[CloseCode2["SubscriberAlreadyExists"] = 4409] = "SubscriberAlreadyExists";
      CloseCode2[CloseCode2["TooManyInitialisationRequests"] = 4429] = "TooManyInitialisationRequests";
    })(CloseCode = exports.CloseCode || (exports.CloseCode = {}));
    var MessageType;
    (function(MessageType2) {
      MessageType2["ConnectionInit"] = "connection_init";
      MessageType2["ConnectionAck"] = "connection_ack";
      MessageType2["Ping"] = "ping";
      MessageType2["Pong"] = "pong";
      MessageType2["Subscribe"] = "subscribe";
      MessageType2["Next"] = "next";
      MessageType2["Error"] = "error";
      MessageType2["Complete"] = "complete";
    })(MessageType = exports.MessageType || (exports.MessageType = {}));
    function validateMessage(val) {
      if (!(0, utils_1.isObject)(val)) {
        throw new Error(`Message is expected to be an object, but got ${(0, utils_1.extendedTypeof)(val)}`);
      }
      if (!val.type) {
        throw new Error(`Message is missing the 'type' property`);
      }
      if (typeof val.type !== "string") {
        throw new Error(`Message is expects the 'type' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.type)}`);
      }
      switch (val.type) {
        case MessageType.ConnectionInit:
        case MessageType.ConnectionAck:
        case MessageType.Ping:
        case MessageType.Pong: {
          if (val.payload != null && !(0, utils_1.isObject)(val.payload)) {
            throw new Error(`"${val.type}" message expects the 'payload' property to be an object or nullish or missing, but got "${val.payload}"`);
          }
          break;
        }
        case MessageType.Subscribe: {
          if (typeof val.id !== "string") {
            throw new Error(`"${val.type}" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);
          }
          if (!val.id) {
            throw new Error(`"${val.type}" message requires a non-empty 'id' property`);
          }
          if (!(0, utils_1.isObject)(val.payload)) {
            throw new Error(`"${val.type}" message expects the 'payload' property to be an object, but got ${(0, utils_1.extendedTypeof)(val.payload)}`);
          }
          if (typeof val.payload.query !== "string") {
            throw new Error(`"${val.type}" message payload expects the 'query' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.payload.query)}`);
          }
          if (val.payload.variables != null && !(0, utils_1.isObject)(val.payload.variables)) {
            throw new Error(`"${val.type}" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.variables)}`);
          }
          if (val.payload.operationName != null && (0, utils_1.extendedTypeof)(val.payload.operationName) !== "string") {
            throw new Error(`"${val.type}" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.operationName)}`);
          }
          if (val.payload.extensions != null && !(0, utils_1.isObject)(val.payload.extensions)) {
            throw new Error(`"${val.type}" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${(0, utils_1.extendedTypeof)(val.payload.extensions)}`);
          }
          break;
        }
        case MessageType.Next: {
          if (typeof val.id !== "string") {
            throw new Error(`"${val.type}" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);
          }
          if (!val.id) {
            throw new Error(`"${val.type}" message requires a non-empty 'id' property`);
          }
          if (!(0, utils_1.isObject)(val.payload)) {
            throw new Error(`"${val.type}" message expects the 'payload' property to be an object, but got ${(0, utils_1.extendedTypeof)(val.payload)}`);
          }
          break;
        }
        case MessageType.Error: {
          if (typeof val.id !== "string") {
            throw new Error(`"${val.type}" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);
          }
          if (!val.id) {
            throw new Error(`"${val.type}" message requires a non-empty 'id' property`);
          }
          if (!(0, utils_1.areGraphQLErrors)(val.payload)) {
            throw new Error(`"${val.type}" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(val.payload)}`);
          }
          break;
        }
        case MessageType.Complete: {
          if (typeof val.id !== "string") {
            throw new Error(`"${val.type}" message expects the 'id' property to be a string, but got ${(0, utils_1.extendedTypeof)(val.id)}`);
          }
          if (!val.id) {
            throw new Error(`"${val.type}" message requires a non-empty 'id' property`);
          }
          break;
        }
        default:
          throw new Error(`Invalid message 'type' property "${val.type}"`);
      }
      return val;
    }
    exports.validateMessage = validateMessage;
    function isMessage(val) {
      try {
        validateMessage(val);
        return true;
      } catch (_a) {
        return false;
      }
    }
    exports.isMessage = isMessage;
    function parseMessage(data, reviver) {
      return validateMessage(typeof data === "string" ? JSON.parse(data, reviver) : data);
    }
    exports.parseMessage = parseMessage;
    function stringifyMessage(msg, replacer) {
      validateMessage(msg);
      return JSON.stringify(msg, replacer);
    }
    exports.stringifyMessage = stringifyMessage;
  }
});

// node_modules/graphql-ws/lib/client.js
var require_client = __commonJS({
  "node_modules/graphql-ws/lib/client.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.createClient = void 0;
    var common_1 = require_common();
    var utils_1 = require_utils();
    __exportStar(require_common(), exports);
    function createClient(options) {
      const {
        url,
        connectionParams,
        lazy = true,
        onNonLazyError = console.error,
        lazyCloseTimeout: lazyCloseTimeoutMs = 0,
        keepAlive = 0,
        disablePong,
        connectionAckWaitTimeout = 0,
        retryAttempts = 5,
        retryWait = async function randomisedExponentialBackoff(retries2) {
          let retryDelay = 1e3;
          for (let i = 0; i < retries2; i++) {
            retryDelay *= 2;
          }
          await new Promise((resolve) => setTimeout(resolve, retryDelay + // add random timeout from 300ms to 3s
          Math.floor(Math.random() * (3e3 - 300) + 300)));
        },
        shouldRetry = isLikeCloseEvent,
        isFatalConnectionProblem,
        on,
        webSocketImpl,
        /**
         * Generates a v4 UUID to be used as the ID using `Math`
         * as the random number generator. Supply your own generator
         * in case you need more uniqueness.
         *
         * Reference: https://gist.github.com/jed/982883
         */
        generateID = function generateUUID() {
          return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0, v = c == "x" ? r : r & 3 | 8;
            return v.toString(16);
          });
        },
        jsonMessageReplacer: replacer,
        jsonMessageReviver: reviver
      } = options;
      let ws;
      if (webSocketImpl) {
        if (!isWebSocket(webSocketImpl)) {
          throw new Error("Invalid WebSocket implementation provided");
        }
        ws = webSocketImpl;
      } else if (typeof WebSocket !== "undefined") {
        ws = WebSocket;
      } else if (typeof global !== "undefined") {
        ws = global.WebSocket || // @ts-expect-error: Support more browsers
        global.MozWebSocket;
      } else if (typeof window !== "undefined") {
        ws = window.WebSocket || // @ts-expect-error: Support more browsers
        window.MozWebSocket;
      }
      if (!ws)
        throw new Error("WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`");
      const WebSocketImpl = ws;
      const emitter = (() => {
        const message = /* @__PURE__ */ (() => {
          const listeners2 = {};
          return {
            on(id, listener) {
              listeners2[id] = listener;
              return () => {
                delete listeners2[id];
              };
            },
            emit(message2) {
              var _a;
              if ("id" in message2)
                (_a = listeners2[message2.id]) === null || _a === void 0 ? void 0 : _a.call(listeners2, message2);
            }
          };
        })();
        const listeners = {
          connecting: (on === null || on === void 0 ? void 0 : on.connecting) ? [on.connecting] : [],
          opened: (on === null || on === void 0 ? void 0 : on.opened) ? [on.opened] : [],
          connected: (on === null || on === void 0 ? void 0 : on.connected) ? [on.connected] : [],
          ping: (on === null || on === void 0 ? void 0 : on.ping) ? [on.ping] : [],
          pong: (on === null || on === void 0 ? void 0 : on.pong) ? [on.pong] : [],
          message: (on === null || on === void 0 ? void 0 : on.message) ? [message.emit, on.message] : [message.emit],
          closed: (on === null || on === void 0 ? void 0 : on.closed) ? [on.closed] : [],
          error: (on === null || on === void 0 ? void 0 : on.error) ? [on.error] : []
        };
        return {
          onMessage: message.on,
          on(event, listener) {
            const l = listeners[event];
            l.push(listener);
            return () => {
              l.splice(l.indexOf(listener), 1);
            };
          },
          emit(event, ...args) {
            for (const listener of [...listeners[event]]) {
              listener(...args);
            }
          }
        };
      })();
      function errorOrClosed(cb) {
        const listening = [
          // errors are fatal and more critical than close events, throw them first
          emitter.on("error", (err) => {
            listening.forEach((unlisten) => unlisten());
            cb(err);
          }),
          // closes can be graceful and not fatal, throw them second (if error didnt throw)
          emitter.on("closed", (event) => {
            listening.forEach((unlisten) => unlisten());
            cb(event);
          })
        ];
      }
      let connecting, locks = 0, lazyCloseTimeout, retrying = false, retries = 0, disposed = false;
      async function connect() {
        clearTimeout(lazyCloseTimeout);
        const [socket, throwOnClose] = await (connecting !== null && connecting !== void 0 ? connecting : connecting = new Promise((connected, denied) => (async () => {
          if (retrying) {
            await retryWait(retries);
            if (!locks) {
              connecting = void 0;
              return denied({ code: 1e3, reason: "All Subscriptions Gone" });
            }
            retries++;
          }
          emitter.emit("connecting");
          const socket2 = new WebSocketImpl(typeof url === "function" ? await url() : url, common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL);
          let connectionAckTimeout, queuedPing;
          function enqueuePing() {
            if (isFinite(keepAlive) && keepAlive > 0) {
              clearTimeout(queuedPing);
              queuedPing = setTimeout(() => {
                if (socket2.readyState === WebSocketImpl.OPEN) {
                  socket2.send((0, common_1.stringifyMessage)({ type: common_1.MessageType.Ping }));
                  emitter.emit("ping", false, void 0);
                }
              }, keepAlive);
            }
          }
          errorOrClosed((errOrEvent) => {
            connecting = void 0;
            clearTimeout(connectionAckTimeout);
            clearTimeout(queuedPing);
            denied(errOrEvent);
            if (isLikeCloseEvent(errOrEvent) && errOrEvent.code === 4499) {
              socket2.close(4499, "Terminated");
              socket2.onerror = null;
              socket2.onclose = null;
            }
          });
          socket2.onerror = (err) => emitter.emit("error", err);
          socket2.onclose = (event) => emitter.emit("closed", event);
          socket2.onopen = async () => {
            try {
              emitter.emit("opened", socket2);
              const payload = typeof connectionParams === "function" ? await connectionParams() : connectionParams;
              if (socket2.readyState !== WebSocketImpl.OPEN)
                return;
              socket2.send((0, common_1.stringifyMessage)(payload ? {
                type: common_1.MessageType.ConnectionInit,
                payload
              } : {
                type: common_1.MessageType.ConnectionInit
                // payload is completely absent if not provided
              }, replacer));
              if (isFinite(connectionAckWaitTimeout) && connectionAckWaitTimeout > 0) {
                connectionAckTimeout = setTimeout(() => {
                  socket2.close(common_1.CloseCode.ConnectionAcknowledgementTimeout, "Connection acknowledgement timeout");
                }, connectionAckWaitTimeout);
              }
              enqueuePing();
            } catch (err) {
              emitter.emit("error", err);
              socket2.close(common_1.CloseCode.InternalClientError, (0, utils_1.limitCloseReason)(err instanceof Error ? err.message : new Error(err).message, "Internal client error"));
            }
          };
          let acknowledged = false;
          socket2.onmessage = ({ data }) => {
            try {
              const message = (0, common_1.parseMessage)(data, reviver);
              emitter.emit("message", message);
              if (message.type === "ping" || message.type === "pong") {
                emitter.emit(message.type, true, message.payload);
                if (message.type === "pong") {
                  enqueuePing();
                } else if (!disablePong) {
                  socket2.send((0, common_1.stringifyMessage)(message.payload ? {
                    type: common_1.MessageType.Pong,
                    payload: message.payload
                  } : {
                    type: common_1.MessageType.Pong
                    // payload is completely absent if not provided
                  }));
                  emitter.emit("pong", false, message.payload);
                }
                return;
              }
              if (acknowledged)
                return;
              if (message.type !== common_1.MessageType.ConnectionAck)
                throw new Error(`First message cannot be of type ${message.type}`);
              clearTimeout(connectionAckTimeout);
              acknowledged = true;
              emitter.emit("connected", socket2, message.payload);
              retrying = false;
              retries = 0;
              connected([
                socket2,
                new Promise((_, reject) => errorOrClosed(reject))
              ]);
            } catch (err) {
              socket2.onmessage = null;
              emitter.emit("error", err);
              socket2.close(common_1.CloseCode.BadResponse, (0, utils_1.limitCloseReason)(err instanceof Error ? err.message : new Error(err).message, "Bad response"));
            }
          };
        })()));
        if (socket.readyState === WebSocketImpl.CLOSING)
          await throwOnClose;
        let release = () => {
        };
        const released = new Promise((resolve) => release = resolve);
        return [
          socket,
          release,
          Promise.race([
            // wait for
            released.then(() => {
              if (!locks) {
                const complete = () => socket.close(1e3, "Normal Closure");
                if (isFinite(lazyCloseTimeoutMs) && lazyCloseTimeoutMs > 0) {
                  lazyCloseTimeout = setTimeout(() => {
                    if (socket.readyState === WebSocketImpl.OPEN)
                      complete();
                  }, lazyCloseTimeoutMs);
                } else {
                  complete();
                }
              }
            }),
            // or
            throwOnClose
          ])
        ];
      }
      function shouldRetryConnectOrThrow(errOrCloseEvent) {
        if (isLikeCloseEvent(errOrCloseEvent) && (isFatalInternalCloseCode(errOrCloseEvent.code) || [
          common_1.CloseCode.InternalServerError,
          common_1.CloseCode.InternalClientError,
          common_1.CloseCode.BadRequest,
          common_1.CloseCode.BadResponse,
          common_1.CloseCode.Unauthorized,
          // CloseCode.Forbidden, might grant access out after retry
          common_1.CloseCode.SubprotocolNotAcceptable,
          // CloseCode.ConnectionInitialisationTimeout, might not time out after retry
          // CloseCode.ConnectionAcknowledgementTimeout, might not time out after retry
          common_1.CloseCode.SubscriberAlreadyExists,
          common_1.CloseCode.TooManyInitialisationRequests
          // 4499, // Terminated, probably because the socket froze, we want to retry
        ].includes(errOrCloseEvent.code)))
          throw errOrCloseEvent;
        if (disposed)
          return false;
        if (isLikeCloseEvent(errOrCloseEvent) && errOrCloseEvent.code === 1e3)
          return locks > 0;
        if (!retryAttempts || retries >= retryAttempts)
          throw errOrCloseEvent;
        if (!shouldRetry(errOrCloseEvent))
          throw errOrCloseEvent;
        if (isFatalConnectionProblem === null || isFatalConnectionProblem === void 0 ? void 0 : isFatalConnectionProblem(errOrCloseEvent))
          throw errOrCloseEvent;
        return retrying = true;
      }
      if (!lazy) {
        (async () => {
          locks++;
          for (; ; ) {
            try {
              const [, , throwOnClose] = await connect();
              await throwOnClose;
            } catch (errOrCloseEvent) {
              try {
                if (!shouldRetryConnectOrThrow(errOrCloseEvent))
                  return;
              } catch (errOrCloseEvent2) {
                return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(errOrCloseEvent2);
              }
            }
          }
        })();
      }
      return {
        on: emitter.on,
        subscribe(payload, sink) {
          const id = generateID(payload);
          let done = false, errored = false, releaser = () => {
            locks--;
            done = true;
          };
          (async () => {
            locks++;
            for (; ; ) {
              try {
                const [socket, release, waitForReleaseOrThrowOnClose] = await connect();
                if (done)
                  return release();
                const unlisten = emitter.onMessage(id, (message) => {
                  switch (message.type) {
                    case common_1.MessageType.Next: {
                      sink.next(message.payload);
                      return;
                    }
                    case common_1.MessageType.Error: {
                      errored = true, done = true;
                      sink.error(message.payload);
                      releaser();
                      return;
                    }
                    case common_1.MessageType.Complete: {
                      done = true;
                      releaser();
                      return;
                    }
                  }
                });
                socket.send((0, common_1.stringifyMessage)({
                  id,
                  type: common_1.MessageType.Subscribe,
                  payload
                }, replacer));
                releaser = () => {
                  if (!done && socket.readyState === WebSocketImpl.OPEN)
                    socket.send((0, common_1.stringifyMessage)({
                      id,
                      type: common_1.MessageType.Complete
                    }, replacer));
                  locks--;
                  done = true;
                  release();
                };
                await waitForReleaseOrThrowOnClose.finally(unlisten);
                return;
              } catch (errOrCloseEvent) {
                if (!shouldRetryConnectOrThrow(errOrCloseEvent))
                  return;
              }
            }
          })().then(() => {
            if (!errored)
              sink.complete();
          }).catch((err) => {
            sink.error(err);
          });
          return () => {
            if (!done)
              releaser();
          };
        },
        async dispose() {
          disposed = true;
          if (connecting) {
            const [socket] = await connecting;
            socket.close(1e3, "Normal Closure");
          }
        },
        terminate() {
          if (connecting) {
            emitter.emit("closed", {
              code: 4499,
              reason: "Terminated",
              wasClean: false
            });
          }
        }
      };
    }
    exports.createClient = createClient;
    function isLikeCloseEvent(val) {
      return (0, utils_1.isObject)(val) && "code" in val && "reason" in val;
    }
    function isFatalInternalCloseCode(code) {
      if ([
        1e3,
        1001,
        1006,
        1005,
        1012,
        1013,
        1013
        // Bad Gateway
      ].includes(code))
        return false;
      return code >= 1e3 && code <= 1999;
    }
    function isWebSocket(val) {
      return typeof val === "function" && "constructor" in val && "CLOSED" in val && "CLOSING" in val && "CONNECTING" in val && "OPEN" in val;
    }
  }
});

// node_modules/graphql-ws/lib/server.js
var require_server = __commonJS({
  "node_modules/graphql-ws/lib/server.js"(exports) {
    "use strict";
    var __asyncValues2 = exports && exports.__asyncValues || function(o) {
      if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
      var m = o[Symbol.asyncIterator], i;
      return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
      }, i);
      function verb(n) {
        i[n] = o[n] && function(v) {
          return new Promise(function(resolve, reject) {
            v = o[n](v), settle(resolve, reject, v.done, v.value);
          });
        };
      }
      function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v2) {
          resolve({ value: v2, done: d });
        }, reject);
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.handleProtocols = exports.makeServer = void 0;
    var graphql_1 = (init_graphql(), __toCommonJS(graphql_exports));
    var common_1 = require_common();
    var utils_1 = require_utils();
    function makeServer(options) {
      const {
        schema,
        context,
        roots,
        validate,
        execute,
        subscribe,
        connectionInitWaitTimeout = 3e3,
        // 3 seconds
        onConnect,
        onDisconnect,
        onClose,
        onSubscribe,
        onOperation,
        onNext,
        onError,
        onComplete,
        jsonMessageReviver: reviver,
        jsonMessageReplacer: replacer
      } = options;
      return {
        opened(socket, extra) {
          const ctx = {
            connectionInitReceived: false,
            acknowledged: false,
            subscriptions: {},
            extra
          };
          if (socket.protocol !== common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL) {
            socket.close(common_1.CloseCode.SubprotocolNotAcceptable, "Subprotocol not acceptable");
            return async (code, reason) => {
              await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));
            };
          }
          const connectionInitWait = connectionInitWaitTimeout > 0 && isFinite(connectionInitWaitTimeout) ? setTimeout(() => {
            if (!ctx.connectionInitReceived)
              socket.close(common_1.CloseCode.ConnectionInitialisationTimeout, "Connection initialisation timeout");
          }, connectionInitWaitTimeout) : null;
          socket.onMessage(async function onMessage(data) {
            var _a, e_1, _b, _c;
            var _d;
            let message;
            try {
              message = (0, common_1.parseMessage)(data, reviver);
            } catch (err) {
              return socket.close(common_1.CloseCode.BadRequest, "Invalid message received");
            }
            switch (message.type) {
              case common_1.MessageType.ConnectionInit: {
                if (ctx.connectionInitReceived)
                  return socket.close(common_1.CloseCode.TooManyInitialisationRequests, "Too many initialisation requests");
                ctx.connectionInitReceived = true;
                if ((0, utils_1.isObject)(message.payload))
                  ctx.connectionParams = message.payload;
                const permittedOrPayload = await (onConnect === null || onConnect === void 0 ? void 0 : onConnect(ctx));
                if (permittedOrPayload === false)
                  return socket.close(common_1.CloseCode.Forbidden, "Forbidden");
                await socket.send((0, common_1.stringifyMessage)((0, utils_1.isObject)(permittedOrPayload) ? {
                  type: common_1.MessageType.ConnectionAck,
                  payload: permittedOrPayload
                } : {
                  type: common_1.MessageType.ConnectionAck
                  // payload is completely absent if not provided
                }, replacer));
                ctx.acknowledged = true;
                return;
              }
              case common_1.MessageType.Ping: {
                if (socket.onPing)
                  return await socket.onPing(message.payload);
                await socket.send((0, common_1.stringifyMessage)(message.payload ? { type: common_1.MessageType.Pong, payload: message.payload } : {
                  type: common_1.MessageType.Pong
                  // payload is completely absent if not provided
                }));
                return;
              }
              case common_1.MessageType.Pong:
                return await ((_d = socket.onPong) === null || _d === void 0 ? void 0 : _d.call(socket, message.payload));
              case common_1.MessageType.Subscribe: {
                if (!ctx.acknowledged)
                  return socket.close(common_1.CloseCode.Unauthorized, "Unauthorized");
                const { id, payload } = message;
                if (id in ctx.subscriptions)
                  return socket.close(common_1.CloseCode.SubscriberAlreadyExists, `Subscriber for ${id} already exists`);
                ctx.subscriptions[id] = null;
                const emit = {
                  next: async (result, args) => {
                    let nextMessage = {
                      id,
                      type: common_1.MessageType.Next,
                      payload: result
                    };
                    const maybeResult = await (onNext === null || onNext === void 0 ? void 0 : onNext(ctx, nextMessage, args, result));
                    if (maybeResult)
                      nextMessage = Object.assign(Object.assign({}, nextMessage), { payload: maybeResult });
                    await socket.send((0, common_1.stringifyMessage)(nextMessage, replacer));
                  },
                  error: async (errors) => {
                    let errorMessage = {
                      id,
                      type: common_1.MessageType.Error,
                      payload: errors
                    };
                    const maybeErrors = await (onError === null || onError === void 0 ? void 0 : onError(ctx, errorMessage, errors));
                    if (maybeErrors)
                      errorMessage = Object.assign(Object.assign({}, errorMessage), { payload: maybeErrors });
                    await socket.send((0, common_1.stringifyMessage)(errorMessage, replacer));
                  },
                  complete: async (notifyClient) => {
                    const completeMessage = {
                      id,
                      type: common_1.MessageType.Complete
                    };
                    await (onComplete === null || onComplete === void 0 ? void 0 : onComplete(ctx, completeMessage));
                    if (notifyClient)
                      await socket.send((0, common_1.stringifyMessage)(completeMessage, replacer));
                  }
                };
                try {
                  let execArgs;
                  const maybeExecArgsOrErrors = await (onSubscribe === null || onSubscribe === void 0 ? void 0 : onSubscribe(ctx, message));
                  if (maybeExecArgsOrErrors) {
                    if ((0, utils_1.areGraphQLErrors)(maybeExecArgsOrErrors))
                      return await emit.error(maybeExecArgsOrErrors);
                    else if (Array.isArray(maybeExecArgsOrErrors))
                      throw new Error("Invalid return value from onSubscribe hook, expected an array of GraphQLError objects");
                    execArgs = maybeExecArgsOrErrors;
                  } else {
                    if (!schema)
                      throw new Error("The GraphQL schema is not provided");
                    const args = {
                      operationName: payload.operationName,
                      document: (0, graphql_1.parse)(payload.query),
                      variableValues: payload.variables
                    };
                    execArgs = Object.assign(Object.assign({}, args), { schema: typeof schema === "function" ? await schema(ctx, message, args) : schema });
                    const validationErrors = (validate !== null && validate !== void 0 ? validate : graphql_1.validate)(execArgs.schema, execArgs.document);
                    if (validationErrors.length > 0)
                      return await emit.error(validationErrors);
                  }
                  const operationAST = (0, graphql_1.getOperationAST)(execArgs.document, execArgs.operationName);
                  if (!operationAST)
                    return await emit.error([
                      new graphql_1.GraphQLError("Unable to identify operation")
                    ]);
                  if (!("rootValue" in execArgs))
                    execArgs.rootValue = roots === null || roots === void 0 ? void 0 : roots[operationAST.operation];
                  if (!("contextValue" in execArgs))
                    execArgs.contextValue = typeof context === "function" ? await context(ctx, message, execArgs) : context;
                  let operationResult;
                  if (operationAST.operation === "subscription")
                    operationResult = await (subscribe !== null && subscribe !== void 0 ? subscribe : graphql_1.subscribe)(execArgs);
                  else
                    operationResult = await (execute !== null && execute !== void 0 ? execute : graphql_1.execute)(execArgs);
                  const maybeResult = await (onOperation === null || onOperation === void 0 ? void 0 : onOperation(ctx, message, execArgs, operationResult));
                  if (maybeResult)
                    operationResult = maybeResult;
                  if ((0, utils_1.isAsyncIterable)(operationResult)) {
                    if (!(id in ctx.subscriptions)) {
                      if ((0, utils_1.isAsyncGenerator)(operationResult))
                        operationResult.return(void 0);
                    } else {
                      ctx.subscriptions[id] = operationResult;
                      try {
                        for (var _e = true, operationResult_1 = __asyncValues2(operationResult), operationResult_1_1; operationResult_1_1 = await operationResult_1.next(), _a = operationResult_1_1.done, !_a; ) {
                          _c = operationResult_1_1.value;
                          _e = false;
                          try {
                            const result = _c;
                            await emit.next(result, execArgs);
                          } finally {
                            _e = true;
                          }
                        }
                      } catch (e_1_1) {
                        e_1 = { error: e_1_1 };
                      } finally {
                        try {
                          if (!_e && !_a && (_b = operationResult_1.return)) await _b.call(operationResult_1);
                        } finally {
                          if (e_1) throw e_1.error;
                        }
                      }
                    }
                  } else {
                    if (id in ctx.subscriptions)
                      await emit.next(operationResult, execArgs);
                  }
                  await emit.complete(id in ctx.subscriptions);
                } finally {
                  delete ctx.subscriptions[id];
                }
                return;
              }
              case common_1.MessageType.Complete: {
                const subscription = ctx.subscriptions[message.id];
                delete ctx.subscriptions[message.id];
                if ((0, utils_1.isAsyncGenerator)(subscription))
                  await subscription.return(void 0);
                return;
              }
              default:
                throw new Error(`Unexpected message of type ${message.type} received`);
            }
          });
          return async (code, reason) => {
            if (connectionInitWait)
              clearTimeout(connectionInitWait);
            for (const sub of Object.values(ctx.subscriptions)) {
              if ((0, utils_1.isAsyncGenerator)(sub))
                await sub.return(void 0);
            }
            if (ctx.acknowledged)
              await (onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect(ctx, code, reason));
            await (onClose === null || onClose === void 0 ? void 0 : onClose(ctx, code, reason));
          };
        }
      };
    }
    exports.makeServer = makeServer;
    function handleProtocols(protocols) {
      switch (true) {
        case (protocols instanceof Set && protocols.has(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL)):
        case (Array.isArray(protocols) && protocols.includes(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL)):
        case (typeof protocols === "string" && protocols.split(",").map((p) => p.trim()).includes(common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL)):
          return common_1.GRAPHQL_TRANSPORT_WS_PROTOCOL;
        default:
          return false;
      }
    }
    exports.handleProtocols = handleProtocols;
  }
});

// node_modules/graphql-ws/lib/index.js
var require_lib = __commonJS({
  "node_modules/graphql-ws/lib/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_client(), exports);
    __exportStar(require_server(), exports);
    __exportStar(require_common(), exports);
  }
});

// node_modules/@graphiql/toolkit/esm/async-helpers/index.js
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
function isPromise(value) {
  return typeof value === "object" && value !== null && typeof value.then === "function";
}
function observableToPromise(observable) {
  return new Promise((resolve, reject) => {
    const subscription = observable.subscribe({
      next(v) {
        resolve(v);
        subscription.unsubscribe();
      },
      error: reject,
      complete() {
        reject(new Error("no value resolved"));
      }
    });
  });
}
function isObservable(value) {
  return typeof value === "object" && value !== null && "subscribe" in value && typeof value.subscribe === "function";
}
function isAsyncIterable(input) {
  return typeof input === "object" && input !== null && (input[Symbol.toStringTag] === "AsyncGenerator" || Symbol.asyncIterator in input);
}
function asyncIterableToPromise(input) {
  var _a;
  return __awaiter(this, void 0, void 0, function* () {
    const iteratorReturn = (_a = ("return" in input ? input : input[Symbol.asyncIterator]()).return) === null || _a === void 0 ? void 0 : _a.bind(input);
    const iteratorNext = ("next" in input ? input : input[Symbol.asyncIterator]()).next.bind(input);
    const result = yield iteratorNext();
    void (iteratorReturn === null || iteratorReturn === void 0 ? void 0 : iteratorReturn());
    return result.value;
  });
}
function fetcherReturnToPromise(fetcherResult) {
  return __awaiter(this, void 0, void 0, function* () {
    const result = yield fetcherResult;
    if (isAsyncIterable(result)) {
      return asyncIterableToPromise(result);
    }
    if (isObservable(result)) {
      return observableToPromise(result);
    }
    return result;
  });
}

// node_modules/@graphiql/toolkit/esm/create-fetcher/lib.js
init_graphql();

// node_modules/meros/browser/index.mjs
var e = new TextDecoder();
async function t(t2, n) {
  if (!t2.ok || !t2.body || t2.bodyUsed) return t2;
  let i = t2.headers.get("content-type");
  if (!i || !~i.indexOf("multipart/")) return t2;
  let l = i.indexOf("boundary="), r = "-";
  if (~l) {
    let e2 = l + 9, t3 = i.indexOf(";", e2);
    r = i.slice(e2, t3 > -1 ? t3 : void 0).trim().replace(/"/g, "");
  }
  return async function* (t3, n2, i2) {
    let l2, r2, d, o = t3.getReader(), a = !i2 || !i2.multiple, f = n2.length, s = "", c = [];
    try {
      let t4;
      e: for (; !(t4 = await o.read()).done; ) {
        let i3 = e.decode(t4.value);
        l2 = s.length, s += i3;
        let o2 = i3.indexOf(n2);
        for (~o2 ? l2 += o2 : l2 = s.indexOf(n2), c = []; ~l2; ) {
          let e2 = s.slice(0, l2), t5 = s.slice(l2 + f);
          if (r2) {
            let n3 = e2.indexOf("\r\n\r\n") + 4, i4 = e2.lastIndexOf("\r\n", n3), l3 = false, r3 = e2.slice(n3, i4 > -1 ? void 0 : i4), o3 = String(e2.slice(0, n3)).trim().split("\r\n"), f2 = {}, s2 = o3.length;
            for (; d = o3[--s2]; d = d.split(": "), f2[d.shift().toLowerCase()] = d.join(": ")) ;
            if (d = f2["content-type"], d && ~d.indexOf("application/json")) try {
              r3 = JSON.parse(r3), l3 = true;
            } catch (e3) {
            }
            if (d = { headers: f2, body: r3, json: l3 }, a ? yield d : c.push(d), "--" === t5.slice(0, 2)) break e;
          } else n2 = "\r\n" + n2, r2 = f += 2;
          s = t5, l2 = s.indexOf(n2);
        }
        c.length && (yield c);
      }
    } finally {
      c.length && (yield c), await o.cancel();
    }
  }(t2.body, `--${r}`, n);
}

// node_modules/@n1ru4l/push-pull-async-iterable-iterator/index.mjs
function withHandlers(source, onReturn, onThrow) {
  const stream = async function* withReturnSource() {
    yield* source;
  }();
  const originalReturn = stream.return.bind(stream);
  if (onReturn) {
    stream.return = (...args) => {
      onReturn();
      return originalReturn(...args);
    };
  }
  if (onThrow) {
    const originalThrow = stream.throw.bind(stream);
    stream.throw = (err) => {
      onThrow(err);
      return originalThrow(err);
    };
  }
  return stream;
}
function createDeferred() {
  const d = {};
  d.promise = new Promise((resolve, reject) => {
    d.resolve = resolve;
    d.reject = reject;
  });
  return d;
}
function makePushPullAsyncIterableIterator() {
  let state = {
    type: "running"
    /* running */
  };
  let next = createDeferred();
  const values = [];
  function pushValue(value) {
    if (state.type !== "running") {
      return;
    }
    values.push(value);
    next.resolve();
    next = createDeferred();
  }
  const source = async function* PushPullAsyncIterableIterator() {
    while (true) {
      if (values.length > 0) {
        yield values.shift();
      } else {
        if (state.type === "error") {
          throw state.error;
        }
        if (state.type === "finished") {
          return;
        }
        await next.promise;
      }
    }
  }();
  const stream = withHandlers(source, () => {
    if (state.type !== "running") {
      return;
    }
    state = {
      type: "finished"
      /* finished */
    };
    next.resolve();
  }, (error) => {
    if (state.type !== "running") {
      return;
    }
    state = {
      type: "error",
      error
    };
    next.resolve();
  });
  return {
    pushValue,
    asyncIterableIterator: stream
  };
}
var makeAsyncIterableIteratorFromSink = (make) => {
  const { pushValue, asyncIterableIterator } = makePushPullAsyncIterableIterator();
  const dispose = make({
    next: (value) => {
      pushValue(value);
    },
    complete: () => {
      asyncIterableIterator.return();
    },
    error: (err) => {
      asyncIterableIterator.throw(err);
    }
  });
  const originalReturn = asyncIterableIterator.return;
  let returnValue = void 0;
  asyncIterableIterator.return = () => {
    if (returnValue === void 0) {
      dispose();
      returnValue = originalReturn();
    }
    return returnValue;
  };
  return asyncIterableIterator;
};
function isAsyncIterable2(input) {
  return typeof input === "object" && input !== null && // The AsyncGenerator check is for Safari on iOS which currently does not have
  // Symbol.asyncIterator implemented
  // That means every custom AsyncIterable must be built using a AsyncGeneratorFunction (async function * () {})
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (input[Symbol.toStringTag] === "AsyncGenerator" || Symbol.asyncIterator && Symbol.asyncIterator in input);
}

// node_modules/@graphiql/toolkit/esm/create-fetcher/lib.js
var __awaiter2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __await = function(v) {
  return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncValues = function(o) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var m = o[Symbol.asyncIterator], i;
  return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
    return this;
  }, i);
  function verb(n) {
    i[n] = o[n] && function(v) {
      return new Promise(function(resolve, reject) {
        v = o[n](v), settle(resolve, reject, v.done, v.value);
      });
    };
  }
  function settle(resolve, reject, d, v) {
    Promise.resolve(v).then(function(v2) {
      resolve({ value: v2, done: d });
    }, reject);
  }
};
var __asyncGenerator = function(thisArg, _arguments, generator) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var g = generator.apply(thisArg, _arguments || []), i, q = [];
  return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
    return this;
  }, i;
  function verb(n) {
    if (g[n]) i[n] = function(v) {
      return new Promise(function(a, b) {
        q.push([n, v, a, b]) > 1 || resume(n, v);
      });
    };
  }
  function resume(n, v) {
    try {
      step(g[n](v));
    } catch (e2) {
      settle(q[0][3], e2);
    }
  }
  function step(r) {
    r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
  }
  function fulfill(value) {
    resume("next", value);
  }
  function reject(value) {
    resume("throw", value);
  }
  function settle(f, v) {
    if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
  }
};
var errorHasCode = (err) => {
  return typeof err === "object" && err !== null && "code" in err;
};
var isSubscriptionWithName = (document, name) => {
  let isSubscription = false;
  visit(document, {
    OperationDefinition(node) {
      var _a;
      if (name === ((_a = node.name) === null || _a === void 0 ? void 0 : _a.value) && node.operation === "subscription") {
        isSubscription = true;
      }
    }
  });
  return isSubscription;
};
var createSimpleFetcher = (options, httpFetch) => (graphQLParams, fetcherOpts) => __awaiter2(void 0, void 0, void 0, function* () {
  const data = yield httpFetch(options.url, {
    method: "POST",
    body: JSON.stringify(graphQLParams),
    headers: Object.assign(Object.assign({ "content-type": "application/json" }, options.headers), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers)
  });
  return data.json();
});
var createWebsocketsFetcherFromUrl = (url, connectionParams) => {
  let wsClient;
  try {
    const { createClient } = require_lib();
    wsClient = createClient({
      url,
      connectionParams
    });
    return createWebsocketsFetcherFromClient(wsClient);
  } catch (err) {
    if (errorHasCode(err) && err.code === "MODULE_NOT_FOUND") {
      throw new Error("You need to install the 'graphql-ws' package to use websockets when passing a 'subscriptionUrl'");
    }
    console.error(`Error creating websocket client for ${url}`, err);
  }
};
var createWebsocketsFetcherFromClient = (wsClient) => (graphQLParams) => makeAsyncIterableIteratorFromSink((sink) => wsClient.subscribe(graphQLParams, Object.assign(Object.assign({}, sink), { error(err) {
  if (err instanceof CloseEvent) {
    sink.error(new Error(`Socket closed with event ${err.code} ${err.reason || ""}`.trim()));
  } else {
    sink.error(err);
  }
} })));
var createLegacyWebsocketsFetcher = (legacyWsClient) => (graphQLParams) => {
  const observable = legacyWsClient.request(graphQLParams);
  return makeAsyncIterableIteratorFromSink((sink) => observable.subscribe(sink).unsubscribe);
};
var createMultipartFetcher = (options, httpFetch) => function(graphQLParams, fetcherOpts) {
  return __asyncGenerator(this, arguments, function* () {
    var e_1, _a;
    const response = yield __await(httpFetch(options.url, {
      method: "POST",
      body: JSON.stringify(graphQLParams),
      headers: Object.assign(Object.assign({ "content-type": "application/json", accept: "application/json, multipart/mixed" }, options.headers), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers)
    }).then((r) => t(r, {
      multiple: true
    })));
    if (!isAsyncIterable2(response)) {
      return yield __await(yield yield __await(response.json()));
    }
    try {
      for (var response_1 = __asyncValues(response), response_1_1; response_1_1 = yield __await(response_1.next()), !response_1_1.done; ) {
        const chunk = response_1_1.value;
        if (chunk.some((part) => !part.json)) {
          const message = chunk.map((part) => `Headers::
${part.headers}

Body::
${part.body}`);
          throw new Error(`Expected multipart chunks to be of json type. got:
${message}`);
        }
        yield yield __await(chunk.map((part) => part.body));
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (response_1_1 && !response_1_1.done && (_a = response_1.return)) yield __await(_a.call(response_1));
      } finally {
        if (e_1) throw e_1.error;
      }
    }
  });
};
var getWsFetcher = (options, fetcherOpts) => {
  if (options.wsClient) {
    return createWebsocketsFetcherFromClient(options.wsClient);
  }
  if (options.subscriptionUrl) {
    return createWebsocketsFetcherFromUrl(options.subscriptionUrl, Object.assign(Object.assign({}, options.wsConnectionParams), fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.headers));
  }
  const legacyWebsocketsClient = options.legacyClient || options.legacyWsClient;
  if (legacyWebsocketsClient) {
    return createLegacyWebsocketsFetcher(legacyWebsocketsClient);
  }
};

// node_modules/@graphiql/toolkit/esm/create-fetcher/createFetcher.js
function createGraphiQLFetcher(options) {
  let httpFetch;
  if (typeof window !== "undefined" && window.fetch) {
    httpFetch = window.fetch;
  }
  if ((options === null || options === void 0 ? void 0 : options.enableIncrementalDelivery) === null || options.enableIncrementalDelivery !== false) {
    options.enableIncrementalDelivery = true;
  }
  if (options.fetch) {
    httpFetch = options.fetch;
  }
  if (!httpFetch) {
    throw new Error("No valid fetcher implementation available");
  }
  const simpleFetcher = createSimpleFetcher(options, httpFetch);
  const httpFetcher = options.enableIncrementalDelivery ? createMultipartFetcher(options, httpFetch) : simpleFetcher;
  return (graphQLParams, fetcherOpts) => {
    if (graphQLParams.operationName === "IntrospectionQuery") {
      return (options.schemaFetcher || simpleFetcher)(graphQLParams, fetcherOpts);
    }
    const isSubscription = (fetcherOpts === null || fetcherOpts === void 0 ? void 0 : fetcherOpts.documentAST) ? isSubscriptionWithName(fetcherOpts.documentAST, graphQLParams.operationName || void 0) : false;
    if (isSubscription) {
      const wsFetcher = getWsFetcher(options, fetcherOpts);
      if (!wsFetcher) {
        throw new Error(`Your GraphiQL createFetcher is not properly configured for websocket subscriptions yet. ${options.subscriptionUrl ? `Provided URL ${options.subscriptionUrl} failed` : "Please provide subscriptionUrl, wsClient or legacyClient option first."}`);
      }
      return wsFetcher(graphQLParams);
    }
    return httpFetcher(graphQLParams, fetcherOpts);
  };
}

// node_modules/@graphiql/toolkit/esm/format/index.js
function stringify(obj) {
  return JSON.stringify(obj, null, 2);
}
function formatSingleError(error) {
  return Object.assign(Object.assign({}, error), { message: error.message, stack: error.stack });
}
function handleSingleError(error) {
  if (error instanceof Error) {
    return formatSingleError(error);
  }
  return error;
}
function formatError(error) {
  if (Array.isArray(error)) {
    return stringify({
      errors: error.map((e2) => handleSingleError(e2))
    });
  }
  return stringify({ errors: [handleSingleError(error)] });
}
function formatResult(result) {
  return stringify(result);
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/auto-complete.js
init_graphql();
function fillLeafs(schema, docString, getDefaultFieldNames) {
  const insertions = [];
  if (!schema || !docString) {
    return { insertions, result: docString };
  }
  let ast;
  try {
    ast = parse(docString);
  } catch (_a) {
    return { insertions, result: docString };
  }
  const fieldNameFn = getDefaultFieldNames || defaultGetDefaultFieldNames;
  const typeInfo = new TypeInfo(schema);
  visit(ast, {
    leave(node) {
      typeInfo.leave(node);
    },
    enter(node) {
      typeInfo.enter(node);
      if (node.kind === "Field" && !node.selectionSet) {
        const fieldType = typeInfo.getType();
        const selectionSet = buildSelectionSet(isFieldType(fieldType), fieldNameFn);
        if (selectionSet && node.loc) {
          const indent = getIndentation(docString, node.loc.start);
          insertions.push({
            index: node.loc.end,
            string: " " + print(selectionSet).replaceAll("\n", "\n" + indent)
          });
        }
      }
    }
  });
  return {
    insertions,
    result: withInsertions(docString, insertions)
  };
}
function defaultGetDefaultFieldNames(type) {
  if (!("getFields" in type)) {
    return [];
  }
  const fields = type.getFields();
  if (fields.id) {
    return ["id"];
  }
  if (fields.edges) {
    return ["edges"];
  }
  if (fields.node) {
    return ["node"];
  }
  const leafFieldNames = [];
  for (const fieldName of Object.keys(fields)) {
    if (isLeafType(fields[fieldName].type)) {
      leafFieldNames.push(fieldName);
    }
  }
  return leafFieldNames;
}
function buildSelectionSet(type, getDefaultFieldNames) {
  const namedType = getNamedType(type);
  if (!type || isLeafType(type)) {
    return;
  }
  const fieldNames = getDefaultFieldNames(namedType);
  if (!Array.isArray(fieldNames) || fieldNames.length === 0 || !("getFields" in namedType)) {
    return;
  }
  return {
    kind: Kind.SELECTION_SET,
    selections: fieldNames.map((fieldName) => {
      const fieldDef = namedType.getFields()[fieldName];
      const fieldType = fieldDef ? fieldDef.type : null;
      return {
        kind: Kind.FIELD,
        name: {
          kind: Kind.NAME,
          value: fieldName
        },
        selectionSet: buildSelectionSet(fieldType, getDefaultFieldNames)
      };
    })
  };
}
function withInsertions(initial, insertions) {
  if (insertions.length === 0) {
    return initial;
  }
  let edited = "";
  let prevIndex = 0;
  for (const { index, string } of insertions) {
    edited += initial.slice(prevIndex, index) + string;
    prevIndex = index;
  }
  edited += initial.slice(prevIndex);
  return edited;
}
function getIndentation(str, index) {
  let indentStart = index;
  let indentEnd = index;
  while (indentStart) {
    const c = str.charCodeAt(indentStart - 1);
    if (c === 10 || c === 13 || c === 8232 || c === 8233) {
      break;
    }
    indentStart--;
    if (c !== 9 && c !== 11 && c !== 12 && c !== 32 && c !== 160) {
      indentEnd = indentStart;
    }
  }
  return str.slice(indentStart, indentEnd);
}
function isFieldType(fieldType) {
  if (fieldType) {
    return fieldType;
  }
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/merge-ast.js
init_graphql();
function uniqueBy(array, iteratee) {
  var _a;
  const FilteredMap = /* @__PURE__ */ new Map();
  const result = [];
  for (const item of array) {
    if (item.kind === "Field") {
      const uniqueValue = iteratee(item);
      const existing = FilteredMap.get(uniqueValue);
      if ((_a = item.directives) === null || _a === void 0 ? void 0 : _a.length) {
        const itemClone = Object.assign({}, item);
        result.push(itemClone);
      } else if ((existing === null || existing === void 0 ? void 0 : existing.selectionSet) && item.selectionSet) {
        existing.selectionSet.selections = [
          ...existing.selectionSet.selections,
          ...item.selectionSet.selections
        ];
      } else if (!existing) {
        const itemClone = Object.assign({}, item);
        FilteredMap.set(uniqueValue, itemClone);
        result.push(itemClone);
      }
    } else {
      result.push(item);
    }
  }
  return result;
}
function inlineRelevantFragmentSpreads(fragmentDefinitions, selections, selectionSetType) {
  var _a;
  const selectionSetTypeName = selectionSetType ? getNamedType(selectionSetType).name : null;
  const outputSelections = [];
  const seenSpreads = [];
  for (let selection of selections) {
    if (selection.kind === "FragmentSpread") {
      const fragmentName = selection.name.value;
      if (!selection.directives || selection.directives.length === 0) {
        if (seenSpreads.includes(fragmentName)) {
          continue;
        } else {
          seenSpreads.push(fragmentName);
        }
      }
      const fragmentDefinition = fragmentDefinitions[selection.name.value];
      if (fragmentDefinition) {
        const { typeCondition, directives, selectionSet } = fragmentDefinition;
        selection = {
          kind: Kind.INLINE_FRAGMENT,
          typeCondition,
          directives,
          selectionSet
        };
      }
    }
    if (selection.kind === Kind.INLINE_FRAGMENT && (!selection.directives || ((_a = selection.directives) === null || _a === void 0 ? void 0 : _a.length) === 0)) {
      const fragmentTypeName = selection.typeCondition ? selection.typeCondition.name.value : null;
      if (!fragmentTypeName || fragmentTypeName === selectionSetTypeName) {
        outputSelections.push(...inlineRelevantFragmentSpreads(fragmentDefinitions, selection.selectionSet.selections, selectionSetType));
        continue;
      }
    }
    outputSelections.push(selection);
  }
  return outputSelections;
}
function mergeAst(documentAST, schema) {
  const typeInfo = schema ? new TypeInfo(schema) : null;
  const fragmentDefinitions = /* @__PURE__ */ Object.create(null);
  for (const definition of documentAST.definitions) {
    if (definition.kind === Kind.FRAGMENT_DEFINITION) {
      fragmentDefinitions[definition.name.value] = definition;
    }
  }
  const flattenVisitors = {
    SelectionSet(node) {
      const selectionSetType = typeInfo ? typeInfo.getParentType() : null;
      let { selections } = node;
      selections = inlineRelevantFragmentSpreads(fragmentDefinitions, selections, selectionSetType);
      return Object.assign(Object.assign({}, node), { selections });
    },
    FragmentDefinition() {
      return null;
    }
  };
  const flattenedAST = visit(documentAST, typeInfo ? visitWithTypeInfo(typeInfo, flattenVisitors) : flattenVisitors);
  const deduplicateVisitors = {
    SelectionSet(node) {
      let { selections } = node;
      selections = uniqueBy(selections, (selection) => selection.alias ? selection.alias.value : selection.name.value);
      return Object.assign(Object.assign({}, node), { selections });
    },
    FragmentDefinition() {
      return null;
    }
  };
  return visit(flattenedAST, deduplicateVisitors);
}

// node_modules/@graphiql/toolkit/esm/graphql-helpers/operation-name.js
function getSelectedOperationName(prevOperations, prevSelectedOperationName, operations) {
  if (!operations || operations.length < 1) {
    return;
  }
  const names = operations.map((op) => {
    var _a;
    return (_a = op.name) === null || _a === void 0 ? void 0 : _a.value;
  });
  if (prevSelectedOperationName && names.includes(prevSelectedOperationName)) {
    return prevSelectedOperationName;
  }
  if (prevSelectedOperationName && prevOperations) {
    const prevNames = prevOperations.map((op) => {
      var _a;
      return (_a = op.name) === null || _a === void 0 ? void 0 : _a.value;
    });
    const prevIndex = prevNames.indexOf(prevSelectedOperationName);
    if (prevIndex !== -1 && prevIndex < names.length) {
      return names[prevIndex];
    }
  }
  return names[0];
}

// node_modules/@graphiql/toolkit/esm/storage/base.js
function isQuotaError(storage, e2) {
  return e2 instanceof DOMException && (e2.code === 22 || e2.code === 1014 || e2.name === "QuotaExceededError" || e2.name === "NS_ERROR_DOM_QUOTA_REACHED") && storage.length !== 0;
}
var StorageAPI = class {
  constructor(storage) {
    if (storage) {
      this.storage = storage;
    } else if (storage === null) {
      this.storage = null;
    } else if (typeof window === "undefined") {
      this.storage = null;
    } else {
      this.storage = {
        getItem: localStorage.getItem.bind(localStorage),
        setItem: localStorage.setItem.bind(localStorage),
        removeItem: localStorage.removeItem.bind(localStorage),
        get length() {
          let keys = 0;
          for (const key in localStorage) {
            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {
              keys += 1;
            }
          }
          return keys;
        },
        clear() {
          for (const key in localStorage) {
            if (key.indexOf(`${STORAGE_NAMESPACE}:`) === 0) {
              localStorage.removeItem(key);
            }
          }
        }
      };
    }
  }
  get(name) {
    if (!this.storage) {
      return null;
    }
    const key = `${STORAGE_NAMESPACE}:${name}`;
    const value = this.storage.getItem(key);
    if (value === "null" || value === "undefined") {
      this.storage.removeItem(key);
      return null;
    }
    return value || null;
  }
  set(name, value) {
    let quotaError = false;
    let error = null;
    if (this.storage) {
      const key = `${STORAGE_NAMESPACE}:${name}`;
      if (value) {
        try {
          this.storage.setItem(key, value);
        } catch (e2) {
          error = e2 instanceof Error ? e2 : new Error(`${e2}`);
          quotaError = isQuotaError(this.storage, e2);
        }
      } else {
        this.storage.removeItem(key);
      }
    }
    return { isQuotaError: quotaError, error };
  }
  clear() {
    if (this.storage) {
      this.storage.clear();
    }
  }
};
var STORAGE_NAMESPACE = "graphiql";

// node_modules/@graphiql/toolkit/esm/storage/history.js
init_graphql();

// node_modules/@graphiql/toolkit/esm/storage/query.js
var QueryStore = class {
  constructor(key, storage, maxSize = null) {
    this.key = key;
    this.storage = storage;
    this.maxSize = maxSize;
    this.items = this.fetchAll();
  }
  get length() {
    return this.items.length;
  }
  contains(item) {
    return this.items.some((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
  }
  edit(item, index) {
    if (typeof index === "number" && this.items[index]) {
      const found = this.items[index];
      if (found.query === item.query && found.variables === item.variables && found.headers === item.headers && found.operationName === item.operationName) {
        this.items.splice(index, 1, item);
        this.save();
        return;
      }
    }
    const itemIndex = this.items.findIndex((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
    if (itemIndex !== -1) {
      this.items.splice(itemIndex, 1, item);
      this.save();
    }
  }
  delete(item) {
    const itemIndex = this.items.findIndex((x) => x.query === item.query && x.variables === item.variables && x.headers === item.headers && x.operationName === item.operationName);
    if (itemIndex !== -1) {
      this.items.splice(itemIndex, 1);
      this.save();
    }
  }
  fetchRecent() {
    return this.items.at(-1);
  }
  fetchAll() {
    const raw = this.storage.get(this.key);
    if (raw) {
      return JSON.parse(raw)[this.key];
    }
    return [];
  }
  push(item) {
    const items = [...this.items, item];
    if (this.maxSize && items.length > this.maxSize) {
      items.shift();
    }
    for (let attempts = 0; attempts < 5; attempts++) {
      const response = this.storage.set(this.key, JSON.stringify({ [this.key]: items }));
      if (!(response === null || response === void 0 ? void 0 : response.error)) {
        this.items = items;
      } else if (response.isQuotaError && this.maxSize) {
        items.shift();
      } else {
        return;
      }
    }
  }
  save() {
    this.storage.set(this.key, JSON.stringify({ [this.key]: this.items }));
  }
};

// node_modules/@graphiql/toolkit/esm/storage/history.js
var MAX_QUERY_SIZE = 1e5;
var HistoryStore = class {
  constructor(storage, maxHistoryLength) {
    this.storage = storage;
    this.maxHistoryLength = maxHistoryLength;
    this.updateHistory = ({ query, variables, headers, operationName }) => {
      if (!this.shouldSaveQuery(query, variables, headers, this.history.fetchRecent())) {
        return;
      }
      this.history.push({
        query,
        variables,
        headers,
        operationName
      });
      const historyQueries = this.history.items;
      const favoriteQueries = this.favorite.items;
      this.queries = historyQueries.concat(favoriteQueries);
    };
    this.deleteHistory = ({ query, variables, headers, operationName, favorite }, clearFavorites = false) => {
      function deleteFromStore(store) {
        const found = store.items.find((x) => x.query === query && x.variables === variables && x.headers === headers && x.operationName === operationName);
        if (found) {
          store.delete(found);
        }
      }
      if (favorite || clearFavorites) {
        deleteFromStore(this.favorite);
      }
      if (!favorite || clearFavorites) {
        deleteFromStore(this.history);
      }
      this.queries = [...this.history.items, ...this.favorite.items];
    };
    this.history = new QueryStore("queries", this.storage, this.maxHistoryLength);
    this.favorite = new QueryStore("favorites", this.storage, null);
    this.queries = [...this.history.fetchAll(), ...this.favorite.fetchAll()];
  }
  shouldSaveQuery(query, variables, headers, lastQuerySaved) {
    if (!query) {
      return false;
    }
    try {
      parse(query);
    } catch (_a) {
      return false;
    }
    if (query.length > MAX_QUERY_SIZE) {
      return false;
    }
    if (!lastQuerySaved) {
      return true;
    }
    if (JSON.stringify(query) === JSON.stringify(lastQuerySaved.query)) {
      if (JSON.stringify(variables) === JSON.stringify(lastQuerySaved.variables)) {
        if (JSON.stringify(headers) === JSON.stringify(lastQuerySaved.headers)) {
          return false;
        }
        if (headers && !lastQuerySaved.headers) {
          return false;
        }
      }
      if (variables && !lastQuerySaved.variables) {
        return false;
      }
    }
    return true;
  }
  toggleFavorite({ query, variables, headers, operationName, label, favorite }) {
    const item = {
      query,
      variables,
      headers,
      operationName,
      label
    };
    if (favorite) {
      item.favorite = false;
      this.favorite.delete(item);
      this.history.push(item);
    } else {
      item.favorite = true;
      this.favorite.push(item);
      this.history.delete(item);
    }
    this.queries = [...this.history.items, ...this.favorite.items];
  }
  editLabel({ query, variables, headers, operationName, label, favorite }, index) {
    const item = {
      query,
      variables,
      headers,
      operationName,
      label
    };
    if (favorite) {
      this.favorite.edit(Object.assign(Object.assign({}, item), { favorite }), index);
    } else {
      this.history.edit(item, index);
    }
    this.queries = [...this.history.items, ...this.favorite.items];
  }
};

// node_modules/@graphiql/toolkit/esm/storage/custom.js
function createLocalStorage({ namespace }) {
  const storageKeyPrefix = `${namespace}:`;
  const getStorageKey = (key) => `${storageKeyPrefix}${key}`;
  const storage = {
    setItem: (key, value) => localStorage.setItem(getStorageKey(key), value),
    getItem: (key) => localStorage.getItem(getStorageKey(key)),
    removeItem: (key) => localStorage.removeItem(getStorageKey(key)),
    get length() {
      let keys = 0;
      for (const key in localStorage) {
        if (key.indexOf(storageKeyPrefix) === 0) {
          keys += 1;
        }
      }
      return keys;
    },
    clear() {
      for (const key in localStorage) {
        if (key.indexOf(storageKeyPrefix) === 0) {
          localStorage.removeItem(key);
        }
      }
    }
  };
  return storage;
}

export {
  isPromise,
  isObservable,
  isAsyncIterable,
  fetcherReturnToPromise,
  createGraphiQLFetcher,
  formatError,
  formatResult,
  fillLeafs,
  mergeAst,
  getSelectedOperationName,
  StorageAPI,
  QueryStore,
  HistoryStore,
  createLocalStorage
};
//# sourceMappingURL=chunk-PGOGSL6Q.js.map

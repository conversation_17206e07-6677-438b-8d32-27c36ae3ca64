{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/css/css.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/css/css.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|((::|[@#.!:])?[\\w-?]+%?)|::|[@#.!:]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".css\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.bracket\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@strings\" },\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@selectorname\" },\n      [\"[\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      { include: \"@comments\" },\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@name\" },\n      { include: \"@strings\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"delimiter\"],\n      [\",\", \"delimiter\"]\n    ],\n    rulevalue: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@term\" },\n      [\"!important\", \"keyword\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [/[^*/]+/, \"comment\"],\n      [/./, \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"-?(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"attribute.value\"],\n      [\"{\", { token: \"delimiter.bracket\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    functioninvocation: [\n      [\"@identifier\\\\(\", { token: \"attribute.value\", next: \"@functionarguments\" }]\n    ],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"attribute.value\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\\"]+/, \"string\"],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\']+/, \"string\"],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA+BA;AAxCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,UAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,IAAI;AAAA;AAAA,MAEJ,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,QACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,QACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,QACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,MACpD;AAAA,MACA,WAAW;AAAA,QACT,MAAM,CAAC,EAAE,SAAS,YAAY,CAAC;AAAA,QAC/B,UAAU;AAAA,UACR,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,UAAU;AAAA,UACrB,EAAE,SAAS,WAAW;AAAA,UACtB;AAAA,YACE;AAAA,YACA,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,UACnD;AAAA,UACA,CAAC,6CAA6C,EAAE,OAAO,UAAU,CAAC;AAAA,UAClE,CAAC,0BAA0B,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,UACzE;AAAA,YACE;AAAA,YACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,UACjF;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,UACjF;AAAA,UACA,EAAE,SAAS,gBAAgB;AAAA,UAC3B,CAAC,SAAS,KAAK;AAAA;AAAA,UAEf,CAAC,WAAW,WAAW;AAAA;AAAA,UAEvB,CAAC,OAAO,EAAE,OAAO,qBAAqB,MAAM,qBAAqB,CAAC;AAAA,UAClE,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,gBAAgB,CAAC;AAAA,QAC7D;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,SAAS,YAAY;AAAA,UACvB,CAAC,iDAAiD,kBAAkB,YAAY;AAAA;AAAA,UAEhF,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC;AAAA,QACpD;AAAA,QACA,cAAc;AAAA,UACZ,CAAC,sCAAsC,KAAK;AAAA;AAAA,QAE9C;AAAA,QACA,mBAAmB,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC,CAAC;AAAA,QAC7F,MAAM;AAAA,UACJ,EAAE,SAAS,YAAY;AAAA,UACvB;AAAA,YACE;AAAA,YACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,UACjF;AAAA,UACA;AAAA,YACE;AAAA,YACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,UACjF;AAAA,UACA,EAAE,SAAS,sBAAsB;AAAA,UACjC,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,QAAQ;AAAA,UACnB,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,iCAAiC,WAAW;AAAA,UAC7C,CAAC,KAAK,WAAW;AAAA,QACnB;AAAA,QACA,WAAW;AAAA,UACT,EAAE,SAAS,YAAY;AAAA,UACvB,EAAE,SAAS,WAAW;AAAA,UACtB,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,cAAc,SAAS;AAAA,UACxB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,QAEvC;AAAA,QACA,WAAW,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,QAC/E,QAAQ,CAAC,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,QACxE,gBAAgB;AAAA,UACd,EAAE,SAAS,WAAW;AAAA,UACtB,CAAC,aAAa,QAAQ;AAAA,UACtB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,QAC1D;AAAA,QACA,iBAAiB;AAAA,UACf,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,QAC1D;AAAA,QACA,iBAAiB;AAAA,UACf,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,QAEvC;AAAA,QACA,UAAU;AAAA,UACR,CAAC,UAAU,WAAW,UAAU;AAAA,UAChC,CAAC,aAAa,SAAS;AAAA,QACzB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,UAAU,WAAW,MAAM;AAAA,UAC5B,CAAC,UAAU,SAAS;AAAA,UACpB,CAAC,KAAK,SAAS;AAAA,QACjB;AAAA,QACA,MAAM,CAAC,CAAC,eAAe,iBAAiB,CAAC;AAAA,QACzC,SAAS;AAAA,UACP,CAAC,sCAAsC,EAAE,OAAO,0BAA0B,MAAM,SAAS,CAAC;AAAA,UAC1F,CAAC,yBAAyB,qBAAqB;AAAA,QACjD;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,UACnB,CAAC,eAAe,iBAAiB;AAAA,UACjC,CAAC,KAAK,EAAE,OAAO,qBAAqB,UAAU,gBAAgB,CAAC;AAAA,QACjE;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,gBAAgB,CAAC;AAAA,UAC3D,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC;AAAA,QACpD;AAAA,QACA,oBAAoB;AAAA,UAClB,CAAC,kBAAkB,EAAE,OAAO,mBAAmB,MAAM,qBAAqB,CAAC;AAAA,QAC7E;AAAA,QACA,mBAAmB;AAAA,UACjB,CAAC,sBAAsB,gBAAgB;AAAA,UACvC,CAAC,OAAO,WAAW;AAAA,UACnB,EAAE,SAAS,QAAQ;AAAA,UACnB,CAAC,OAAO,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,QACpD;AAAA,QACA,SAAS;AAAA,UACP,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,wBAAwB,CAAC;AAAA,UAC1D,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,kBAAkB,CAAC;AAAA,QACtD;AAAA,QACA,sBAAsB;AAAA,UACpB,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UACvC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UACvC,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
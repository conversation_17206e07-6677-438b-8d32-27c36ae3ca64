import {
  require_jsx_runtime
} from "./chunk-VV4ISGQI.js";
import {
  require_react
} from "./chunk-LABDTKBP.js";
import {
  __toESM
} from "./chunk-XPZLJQLW.js";

// node_modules/react-data-grid/lib/bundle.js
var import_react = __toESM(require_react());

// node_modules/react-data-grid/node_modules/clsx/dist/clsx.m.js
function r(e) {
  var t, f, n = "";
  if ("string" == typeof e || "number" == typeof e) n += e;
  else if ("object" == typeof e) if (Array.isArray(e)) for (t = 0; t < e.length; t++) e[t] && (f = r(e[t])) && (n && (n += " "), n += f);
  else for (t in e) e[t] && (n && (n += " "), n += t);
  return n;
}
function clsx() {
  for (var e, t, f = 0, n = ""; f < arguments.length; ) (e = arguments[f++]) && (t = r(e)) && (n && (n += " "), n += t);
  return n;
}
var clsx_m_default = clsx;

// node_modules/react-data-grid/lib/bundle.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
function styleInject(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z$f = ".c1wupbe700-beta13{background-color:inherit;border-block-end:1px solid var(--rdg-border-color);border-inline-end:1px solid var(--rdg-border-color);contain:size style;grid-row-start:var(--rdg-grid-row-start);outline:none;overflow:hidden;overflow:clip;padding-block:0;padding-inline:8px;position:relative;text-overflow:ellipsis;white-space:nowrap}.c1wupbe700-beta13[aria-selected=true]{outline:2px solid var(--rdg-selection-color);outline-offset:-2px}.cd0kgiy700-beta13 .c1wupbe700-beta13{contain:content}.c1730fa4700-beta13{position:sticky;z-index:1}.c9dpaye700-beta13{box-shadow:calc(2px*var(--rdg-sign)) 0 5px -2px hsla(0,0%,53%,.3)}";
styleInject(css_248z$f, { "insertAt": "top" });
var cell = "c1wupbe700-beta13";
var cellClassname = `rdg-cell ${cell}`;
var cellAutoResizeClassname = "cd0kgiy700-beta13";
var cellFrozen = "c1730fa4700-beta13";
var cellFrozenClassname = `rdg-cell-frozen ${cellFrozen}`;
var cellFrozenLast = "c9dpaye700-beta13";
var cellFrozenLastClassname = `rdg-cell-frozen-last ${cellFrozenLast}`;
var css_248z$e = '.r104f42s700-beta13{--rdg-color:#000;--rdg-border-color:#ddd;--rdg-summary-border-color:#aaa;--rdg-background-color:#fff;--rdg-header-background-color:#f9f9f9;--rdg-row-hover-background-color:#f5f5f5;--rdg-row-selected-background-color:#dbecfa;--row-selected-hover-background-color:#c9e3f8;--rdg-checkbox-color:#005194;--rdg-checkbox-focus-color:#61b8ff;--rdg-checkbox-disabled-border-color:#ccc;--rdg-checkbox-disabled-background-color:#ddd;--rdg-selection-color:#66afe9;--rdg-font-size:14px;content-visibility:auto;background-color:var(--rdg-background-color);block-size:350px;border:1px solid var(--rdg-border-color);box-sizing:border-box;color:var(--rdg-color);color-scheme:var(--rdg-color-scheme,light dark);contain:strict;contain:size layout style paint;display:grid;font-size:var(--rdg-font-size);overflow:auto;user-select:none}@supports not (contain:strict){.r104f42s700-beta13{position:relative;z-index:0}}.r104f42s700-beta13 *,.r104f42s700-beta13 :after,.r104f42s700-beta13 :before{box-sizing:inherit}.r104f42s700-beta13:before{content:"";grid-column:1/-1;grid-row:1/-1}.r104f42s700-beta13.rdg-dark{--rdg-color-scheme:dark;--rdg-color:#ddd;--rdg-border-color:#444;--rdg-summary-border-color:#555;--rdg-background-color:#212121;--rdg-header-background-color:#1b1b1b;--rdg-row-hover-background-color:#171717;--rdg-row-selected-background-color:#1a73bc;--row-selected-hover-background-color:#1768ab;--rdg-checkbox-color:#94cfff;--rdg-checkbox-focus-color:#c7e6ff;--rdg-checkbox-disabled-border-color:#000;--rdg-checkbox-disabled-background-color:#333}.r104f42s700-beta13.rdg-light{--rdg-color-scheme:light}@media (prefers-color-scheme:dark){.r104f42s700-beta13:not(.rdg-light){--rdg-color:#ddd;--rdg-border-color:#444;--rdg-summary-border-color:#555;--rdg-background-color:#212121;--rdg-header-background-color:#1b1b1b;--rdg-row-hover-background-color:#171717;--rdg-row-selected-background-color:#1a73bc;--row-selected-hover-background-color:#1768ab;--rdg-checkbox-color:#94cfff;--rdg-checkbox-focus-color:#c7e6ff;--rdg-checkbox-disabled-border-color:#000;--rdg-checkbox-disabled-background-color:#333}}.v7ly7s700-beta13.r1otpg64700-beta13{cursor:move}.fc4f4zb700-beta13{grid-column:1/-1;pointer-events:none;z-index:4}';
styleInject(css_248z$e, { "insertAt": "top" });
var root = "r104f42s700-beta13";
var rootClassname = `rdg ${root}`;
var viewportDragging = "v7ly7s700-beta13";
var viewportDraggingClassname = `rdg-viewport-dragging ${viewportDragging}`;
var focusSinkClassname = "fc4f4zb700-beta13";
var css_248z$d = '.r1otpg64700-beta13{background-color:var(--rdg-background-color);display:contents;line-height:var(--rdg-row-height)}.r1otpg64700-beta13:hover{background-color:var(--rdg-row-hover-background-color)}.r1otpg64700-beta13[aria-selected=true]{background-color:var(--rdg-row-selected-background-color)}.r1otpg64700-beta13[aria-selected=true]:hover{background-color:var(--row-selected-hover-background-color)}.rel5gk2700-beta13{outline:2px solid var(--rdg-selection-color);outline-offset:-2px}.r1qymf1z700-beta13:before{border-inline-start:2px solid var(--rdg-selection-color);content:"";display:inline-block;height:100%;inset-inline-start:0;position:sticky}';
styleInject(css_248z$d, { "insertAt": "top" });
var row = "r1otpg64700-beta13";
var rowClassname = `rdg-row ${row}`;
var rowSelected = "rel5gk2700-beta13";
var rowSelectedClassname = `rdg-row-selected`;
var rowSelectedWithFrozenCell = "r1qymf1z700-beta13";
var css_248z$c = '.cd9l4jz700-beta13{align-items:center;cursor:pointer;display:flex;inset:0;justify-content:center;margin-inline-end:1px;position:absolute}.c1noyk41700-beta13{all:unset}.cdwjxv8700-beta13{background-color:var(--rdg-background-color);block-size:20px;border:2px solid var(--rdg-border-color);content:"";inline-size:20px}.c1noyk41700-beta13:checked+.cdwjxv8700-beta13{background-color:var(--rdg-checkbox-color);outline:4px solid var(--rdg-background-color);outline-offset:-6px}.c1noyk41700-beta13:focus+.cdwjxv8700-beta13{border-color:var(--rdg-checkbox-focus-color)}.cca4mwn700-beta13{cursor:default}.cca4mwn700-beta13 .cdwjxv8700-beta13{background-color:var(--rdg-checkbox-disabled-background-color);border-color:var(--rdg-checkbox-disabled-border-color)}';
styleInject(css_248z$c, { "insertAt": "top" });
var checkboxLabel = "cd9l4jz700-beta13";
var checkboxLabelClassname = `rdg-checkbox-label ${checkboxLabel}`;
var checkboxInput = "c1noyk41700-beta13";
var checkboxInputClassname = `rdg-checkbox-input ${checkboxInput}`;
var checkbox = "cdwjxv8700-beta13";
var checkboxClassname = `rdg-checkbox ${checkbox}`;
var checkboxLabelDisabled = "cca4mwn700-beta13";
var checkboxLabelDisabledClassname = `rdg-checkbox-label-disabled ${checkboxLabelDisabled}`;
var CheckboxFormatter = (0, import_react.forwardRef)(function CheckboxFormatter2({
  onChange,
  ...props
}, ref) {
  function handleChange(e) {
    onChange(e.target.checked, e.nativeEvent.shiftKey);
  }
  return (0, import_jsx_runtime.jsxs)("label", {
    className: clsx_m_default(checkboxLabelClassname, props.disabled && checkboxLabelDisabledClassname),
    children: [(0, import_jsx_runtime.jsx)("input", {
      type: "checkbox",
      ref,
      ...props,
      className: checkboxInputClassname,
      onChange: handleChange
    }), (0, import_jsx_runtime.jsx)("div", {
      className: checkboxClassname
    })]
  });
});
var useLayoutEffect = typeof window === "undefined" ? import_react.useEffect : import_react.useLayoutEffect;
function useFocusRef(isSelected) {
  const ref = (0, import_react.useRef)(null);
  useLayoutEffect(() => {
    var _a;
    if (!isSelected) return;
    (_a = ref.current) == null ? void 0 : _a.focus({
      preventScroll: true
    });
  }, [isSelected]);
  return {
    ref,
    tabIndex: isSelected ? 0 : -1
  };
}
var DataGridDefaultComponentsContext = (0, import_react.createContext)(void 0);
var DataGridDefaultComponentsProvider = DataGridDefaultComponentsContext.Provider;
function useDefaultComponents() {
  return (0, import_react.useContext)(DataGridDefaultComponentsContext);
}
function SelectCellFormatter({
  value,
  isCellSelected,
  disabled,
  onChange,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledBy
}) {
  const {
    ref,
    tabIndex
  } = useFocusRef(isCellSelected);
  const Formatter = useDefaultComponents().checkboxFormatter;
  return (0, import_jsx_runtime.jsx)(Formatter, {
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledBy,
    ref,
    tabIndex,
    disabled,
    checked: value,
    onChange
  });
}
function ValueFormatter(props) {
  try {
    return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, {
      children: props.row[props.column.key]
    });
  } catch {
    return null;
  }
}
var css_248z$b = ".gch972y700-beta13{outline:none}.cz2qf0d700-beta13{stroke:currentColor;stroke-width:1.5px;fill:transparent;margin-inline-start:4px;vertical-align:middle}.cz2qf0d700-beta13>path{transition:d .1s}";
styleInject(css_248z$b, { "insertAt": "top" });
var groupCellContent = "gch972y700-beta13";
var groupCellContentClassname = `rdg-group-cell-content ${groupCellContent}`;
var caret = "cz2qf0d700-beta13";
var caretClassname = `rdg-caret ${caret}`;
function ToggleGroupFormatter({
  groupKey,
  isExpanded,
  isCellSelected,
  toggleGroup
}) {
  const {
    ref,
    tabIndex
  } = useFocusRef(isCellSelected);
  function handleKeyDown({
    key
  }) {
    if (key === "Enter") {
      toggleGroup();
    }
  }
  const d = isExpanded ? "M1 1 L 7 7 L 13 1" : "M1 7 L 7 1 L 13 7";
  return (0, import_jsx_runtime.jsxs)("span", {
    ref,
    className: groupCellContentClassname,
    tabIndex,
    onKeyDown: handleKeyDown,
    children: [groupKey, (0, import_jsx_runtime.jsx)("svg", {
      viewBox: "0 0 14 8",
      width: "14",
      height: "8",
      className: caretClassname,
      "aria-hidden": true,
      children: (0, import_jsx_runtime.jsx)("path", {
        d
      })
    })]
  });
}
var RowSelectionContext = (0, import_react.createContext)(void 0);
var RowSelectionProvider = RowSelectionContext.Provider;
var RowSelectionChangeContext = (0, import_react.createContext)(void 0);
var RowSelectionChangeProvider = RowSelectionChangeContext.Provider;
function useRowSelection() {
  const rowSelectionContext = (0, import_react.useContext)(RowSelectionContext);
  const rowSelectionChangeContext = (0, import_react.useContext)(RowSelectionChangeContext);
  if (rowSelectionContext === void 0 || rowSelectionChangeContext === void 0) {
    throw new Error("useRowSelection must be used within DataGrid cells");
  }
  return [rowSelectionContext, rowSelectionChangeContext];
}
var SELECT_COLUMN_KEY = "select-row";
function SelectFormatter(props) {
  const [isRowSelected, onRowSelectionChange] = useRowSelection();
  return (0, import_jsx_runtime.jsx)(SelectCellFormatter, {
    "aria-label": "Select",
    isCellSelected: props.isCellSelected,
    value: isRowSelected,
    onChange: (checked, isShiftClick) => {
      onRowSelectionChange({
        row: props.row,
        checked,
        isShiftClick
      });
    }
  });
}
function SelectGroupFormatter(props) {
  const [isRowSelected, onRowSelectionChange] = useRowSelection();
  return (0, import_jsx_runtime.jsx)(SelectCellFormatter, {
    "aria-label": "Select Group",
    isCellSelected: props.isCellSelected,
    value: isRowSelected,
    onChange: (checked) => {
      onRowSelectionChange({
        row: props.row,
        checked,
        isShiftClick: false
      });
    }
  });
}
var SelectColumn = {
  key: SELECT_COLUMN_KEY,
  name: "",
  width: 35,
  minWidth: 35,
  maxWidth: 35,
  resizable: false,
  sortable: false,
  frozen: true,
  headerRenderer(props) {
    return (0, import_jsx_runtime.jsx)(SelectCellFormatter, {
      "aria-label": "Select All",
      isCellSelected: props.isCellSelected,
      value: props.allRowsSelected,
      onChange: props.onAllRowsSelectionChange
    });
  },
  formatter: SelectFormatter,
  groupFormatter: SelectGroupFormatter
};
function getColSpan(column, lastFrozenColumnIndex, args) {
  const colSpan = typeof column.colSpan === "function" ? column.colSpan(args) : 1;
  if (Number.isInteger(colSpan) && colSpan > 1 && (!column.frozen || column.idx + colSpan - 1 <= lastFrozenColumnIndex)) {
    return colSpan;
  }
  return void 0;
}
function scrollIntoView(element) {
  element == null ? void 0 : element.scrollIntoView({
    inline: "nearest",
    block: "nearest"
  });
}
var nonInputKeys = /* @__PURE__ */ new Set(["Unidentified", "Alt", "AltGraph", "CapsLock", "Control", "Fn", "FnLock", "Meta", "NumLock", "ScrollLock", "Shift", "Tab", "ArrowDown", "ArrowLeft", "ArrowRight", "ArrowUp", "End", "Home", "PageDown", "PageUp", "Insert", "ContextMenu", "Escape", "Pause", "Play", "PrintScreen", "F1", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"]);
function isCtrlKeyHeldDown(e) {
  return (e.ctrlKey || e.metaKey) && e.key !== "Control";
}
function isDefaultCellInput(event) {
  return !nonInputKeys.has(event.key);
}
function onEditorNavigation({
  key,
  target
}) {
  if (key === "Tab" && (target instanceof HTMLInputElement || target instanceof HTMLTextAreaElement || target instanceof HTMLSelectElement)) {
    return target.matches(".rdg-editor-container > :only-child, .rdg-editor-container > label:only-child > :only-child");
  }
  return false;
}
function isSelectedCellEditable({
  selectedPosition,
  columns,
  rows,
  isGroupRow
}) {
  const column = columns[selectedPosition.idx];
  const row2 = rows[selectedPosition.rowIdx];
  return !isGroupRow(row2) && isCellEditable(column, row2);
}
function isCellEditable(column, row2) {
  return column.editor != null && !column.rowGroup && (typeof column.editable === "function" ? column.editable(row2) : column.editable) !== false;
}
function getSelectedCellColSpan({
  rows,
  summaryRows,
  rowIdx,
  lastFrozenColumnIndex,
  column,
  isGroupRow
}) {
  if (rowIdx === -1) {
    return getColSpan(column, lastFrozenColumnIndex, {
      type: "HEADER"
    });
  }
  if (rowIdx >= 0 && rowIdx < rows.length) {
    const row2 = rows[rowIdx];
    if (!isGroupRow(row2)) {
      return getColSpan(column, lastFrozenColumnIndex, {
        type: "ROW",
        row: row2
      });
    }
    return void 0;
  }
  if (summaryRows) {
    return getColSpan(column, lastFrozenColumnIndex, {
      type: "SUMMARY",
      row: summaryRows[rowIdx - rows.length]
    });
  }
  return void 0;
}
function getNextSelectedCellPosition({
  cellNavigationMode,
  columns,
  colSpanColumns,
  rows,
  summaryRows,
  minRowIdx,
  maxRowIdx,
  currentPosition: {
    idx: currentIdx
  },
  nextPosition,
  lastFrozenColumnIndex,
  isCellWithinBounds,
  isGroupRow
}) {
  let {
    idx: nextIdx,
    rowIdx: nextRowIdx
  } = nextPosition;
  const setColSpan = (moveRight) => {
    if (nextRowIdx >= 0 && nextRowIdx < rows.length) {
      const row2 = rows[nextRowIdx];
      if (isGroupRow(row2)) return;
    }
    for (const column of colSpanColumns) {
      const colIdx = column.idx;
      if (colIdx > nextIdx) break;
      const colSpan = getSelectedCellColSpan({
        rows,
        summaryRows,
        rowIdx: nextRowIdx,
        lastFrozenColumnIndex,
        column,
        isGroupRow
      });
      if (colSpan && nextIdx > colIdx && nextIdx < colSpan + colIdx) {
        nextIdx = colIdx + (moveRight ? colSpan : 0);
        break;
      }
    }
  };
  if (isCellWithinBounds(nextPosition)) {
    setColSpan(nextIdx - currentIdx > 0);
  }
  if (cellNavigationMode !== "NONE") {
    const columnsCount = columns.length;
    const isAfterLastColumn = nextIdx === columnsCount;
    const isBeforeFirstColumn = nextIdx === -1;
    if (isAfterLastColumn) {
      if (cellNavigationMode === "CHANGE_ROW") {
        const isLastRow = nextRowIdx === maxRowIdx;
        if (!isLastRow) {
          nextIdx = 0;
          nextRowIdx += 1;
        }
      } else {
        nextIdx = 0;
      }
    } else if (isBeforeFirstColumn) {
      if (cellNavigationMode === "CHANGE_ROW") {
        const isFirstRow = nextRowIdx === minRowIdx;
        if (!isFirstRow) {
          nextRowIdx -= 1;
          nextIdx = columnsCount - 1;
        }
      } else {
        nextIdx = columnsCount - 1;
      }
      setColSpan(false);
    }
  }
  return {
    idx: nextIdx,
    rowIdx: nextRowIdx
  };
}
function canExitGrid({
  cellNavigationMode,
  maxColIdx,
  minRowIdx,
  maxRowIdx,
  selectedPosition: {
    rowIdx,
    idx
  },
  shiftKey
}) {
  if (cellNavigationMode === "NONE" || cellNavigationMode === "CHANGE_ROW") {
    const atLastCellInRow = idx === maxColIdx;
    const atFirstCellInRow = idx === 0;
    const atLastRow = rowIdx === maxRowIdx;
    const atFirstRow = rowIdx === minRowIdx;
    return shiftKey ? atFirstCellInRow && atFirstRow : atLastCellInRow && atLastRow;
  }
  return false;
}
function getRowStyle(rowIdx, height) {
  if (height !== void 0) {
    return {
      "--rdg-grid-row-start": rowIdx,
      "--rdg-row-height": `${height}px`
    };
  }
  return {
    "--rdg-grid-row-start": rowIdx
  };
}
function getCellStyle(column, colSpan) {
  return {
    gridColumnStart: column.idx + 1,
    gridColumnEnd: colSpan !== void 0 ? `span ${colSpan}` : void 0,
    insetInlineStart: column.frozen ? `var(--rdg-frozen-left-${column.idx})` : void 0
  };
}
function getCellClassname(column, ...extraClasses) {
  return clsx_m_default(cellClassname, ...extraClasses, column.frozen && cellFrozenClassname, column.isLastFrozenColumn && cellFrozenLastClassname);
}
var {
  min,
  max,
  round,
  floor,
  sign,
  abs,
  ceil
} = Math;
function assertIsValidKeyGetter(keyGetter) {
  if (typeof keyGetter !== "function") {
    throw new Error("Please specify the rowKeyGetter prop to use selection");
  }
}
function clampColumnWidth(width, {
  minWidth,
  maxWidth
}) {
  width = max(width, minWidth);
  if (typeof maxWidth === "number" && maxWidth >= minWidth) {
    return min(width, maxWidth);
  }
  return width;
}
function useCalculatedColumns({
  rawColumns,
  columnWidths,
  viewportWidth,
  scrollLeft,
  defaultColumnOptions,
  rawGroupBy,
  enableVirtualization
}) {
  const defaultWidth = defaultColumnOptions == null ? void 0 : defaultColumnOptions.width;
  const defaultMinWidth = (defaultColumnOptions == null ? void 0 : defaultColumnOptions.minWidth) ?? 80;
  const defaultMaxWidth = defaultColumnOptions == null ? void 0 : defaultColumnOptions.maxWidth;
  const defaultFormatter = (defaultColumnOptions == null ? void 0 : defaultColumnOptions.formatter) ?? ValueFormatter;
  const defaultSortable = (defaultColumnOptions == null ? void 0 : defaultColumnOptions.sortable) ?? false;
  const defaultResizable = (defaultColumnOptions == null ? void 0 : defaultColumnOptions.resizable) ?? false;
  const {
    columns,
    colSpanColumns,
    lastFrozenColumnIndex,
    groupBy
  } = (0, import_react.useMemo)(() => {
    const groupBy2 = [];
    let lastFrozenColumnIndex2 = -1;
    const columns2 = rawColumns.map((rawColumn) => {
      const rowGroup = (rawGroupBy == null ? void 0 : rawGroupBy.includes(rawColumn.key)) ?? false;
      const frozen = rowGroup || rawColumn.frozen || false;
      const column = {
        ...rawColumn,
        idx: 0,
        frozen,
        isLastFrozenColumn: false,
        rowGroup,
        width: rawColumn.width ?? defaultWidth,
        minWidth: rawColumn.minWidth ?? defaultMinWidth,
        maxWidth: rawColumn.maxWidth ?? defaultMaxWidth,
        sortable: rawColumn.sortable ?? defaultSortable,
        resizable: rawColumn.resizable ?? defaultResizable,
        formatter: rawColumn.formatter ?? defaultFormatter
      };
      if (rowGroup) {
        column.groupFormatter ?? (column.groupFormatter = ToggleGroupFormatter);
      }
      if (frozen) {
        lastFrozenColumnIndex2++;
      }
      return column;
    });
    columns2.sort(({
      key: aKey,
      frozen: frozenA
    }, {
      key: bKey,
      frozen: frozenB
    }) => {
      if (aKey === SELECT_COLUMN_KEY) return -1;
      if (bKey === SELECT_COLUMN_KEY) return 1;
      if (rawGroupBy == null ? void 0 : rawGroupBy.includes(aKey)) {
        if (rawGroupBy.includes(bKey)) {
          return rawGroupBy.indexOf(aKey) - rawGroupBy.indexOf(bKey);
        }
        return -1;
      }
      if (rawGroupBy == null ? void 0 : rawGroupBy.includes(bKey)) return 1;
      if (frozenA) {
        if (frozenB) return 0;
        return -1;
      }
      if (frozenB) return 1;
      return 0;
    });
    const colSpanColumns2 = [];
    columns2.forEach((column, idx) => {
      column.idx = idx;
      if (column.rowGroup) {
        groupBy2.push(column.key);
      }
      if (column.colSpan != null) {
        colSpanColumns2.push(column);
      }
    });
    if (lastFrozenColumnIndex2 !== -1) {
      columns2[lastFrozenColumnIndex2].isLastFrozenColumn = true;
    }
    return {
      columns: columns2,
      colSpanColumns: colSpanColumns2,
      lastFrozenColumnIndex: lastFrozenColumnIndex2,
      groupBy: groupBy2
    };
  }, [rawColumns, defaultWidth, defaultMinWidth, defaultMaxWidth, defaultFormatter, defaultResizable, defaultSortable, rawGroupBy]);
  const {
    layoutCssVars,
    totalFrozenColumnWidth,
    columnMetrics
  } = (0, import_react.useMemo)(() => {
    const columnMetrics2 = /* @__PURE__ */ new Map();
    let left = 0;
    let totalFrozenColumnWidth2 = 0;
    let templateColumns = "";
    let allocatedWidth = 0;
    let unassignedColumnsCount = 0;
    for (const column of columns) {
      let width = getSpecifiedWidth(column, columnWidths, viewportWidth);
      if (width === void 0) {
        unassignedColumnsCount++;
      } else {
        width = clampColumnWidth(width, column);
        allocatedWidth += width;
        columnMetrics2.set(column, {
          width,
          left: 0
        });
      }
    }
    for (const column of columns) {
      let width;
      if (columnMetrics2.has(column)) {
        const columnMetric = columnMetrics2.get(column);
        columnMetric.left = left;
        ({
          width
        } = columnMetric);
      } else {
        const unallocatedWidth = viewportWidth - allocatedWidth;
        const unallocatedColumnWidth = round(unallocatedWidth / unassignedColumnsCount);
        width = clampColumnWidth(unallocatedColumnWidth, column);
        allocatedWidth += width;
        unassignedColumnsCount--;
        columnMetrics2.set(column, {
          width,
          left
        });
      }
      left += width;
      templateColumns += `${width}px `;
    }
    if (lastFrozenColumnIndex !== -1) {
      const columnMetric = columnMetrics2.get(columns[lastFrozenColumnIndex]);
      totalFrozenColumnWidth2 = columnMetric.left + columnMetric.width;
    }
    const layoutCssVars2 = {
      gridTemplateColumns: templateColumns
    };
    for (let i = 0; i <= lastFrozenColumnIndex; i++) {
      const column = columns[i];
      layoutCssVars2[`--rdg-frozen-left-${column.idx}`] = `${columnMetrics2.get(column).left}px`;
    }
    return {
      layoutCssVars: layoutCssVars2,
      totalFrozenColumnWidth: totalFrozenColumnWidth2,
      columnMetrics: columnMetrics2
    };
  }, [columnWidths, columns, viewportWidth, lastFrozenColumnIndex]);
  const [colOverscanStartIdx, colOverscanEndIdx] = (0, import_react.useMemo)(() => {
    if (!enableVirtualization) {
      return [0, columns.length - 1];
    }
    const viewportLeft = scrollLeft + totalFrozenColumnWidth;
    const viewportRight = scrollLeft + viewportWidth;
    const lastColIdx = columns.length - 1;
    const firstUnfrozenColumnIdx = min(lastFrozenColumnIndex + 1, lastColIdx);
    if (viewportLeft >= viewportRight) {
      return [firstUnfrozenColumnIdx, firstUnfrozenColumnIdx];
    }
    let colVisibleStartIdx = firstUnfrozenColumnIdx;
    while (colVisibleStartIdx < lastColIdx) {
      const {
        left,
        width
      } = columnMetrics.get(columns[colVisibleStartIdx]);
      if (left + width > viewportLeft) {
        break;
      }
      colVisibleStartIdx++;
    }
    let colVisibleEndIdx = colVisibleStartIdx;
    while (colVisibleEndIdx < lastColIdx) {
      const {
        left,
        width
      } = columnMetrics.get(columns[colVisibleEndIdx]);
      if (left + width >= viewportRight) {
        break;
      }
      colVisibleEndIdx++;
    }
    const colOverscanStartIdx2 = max(firstUnfrozenColumnIdx, colVisibleStartIdx - 1);
    const colOverscanEndIdx2 = min(lastColIdx, colVisibleEndIdx + 1);
    return [colOverscanStartIdx2, colOverscanEndIdx2];
  }, [columnMetrics, columns, lastFrozenColumnIndex, scrollLeft, totalFrozenColumnWidth, viewportWidth, enableVirtualization]);
  return {
    columns,
    colSpanColumns,
    colOverscanStartIdx,
    colOverscanEndIdx,
    layoutCssVars,
    columnMetrics,
    lastFrozenColumnIndex,
    totalFrozenColumnWidth,
    groupBy
  };
}
function getSpecifiedWidth({
  key,
  width
}, columnWidths, viewportWidth) {
  if (columnWidths.has(key)) {
    return columnWidths.get(key);
  }
  if (typeof width === "number") {
    return width;
  }
  if (typeof width === "string" && /^\d+%$/.test(width)) {
    return floor(viewportWidth * parseInt(width, 10) / 100);
  }
  return void 0;
}
function useGridDimensions() {
  const gridRef = (0, import_react.useRef)(null);
  const [inlineSize, setInlineSize] = (0, import_react.useState)(1);
  const [blockSize, setBlockSize] = (0, import_react.useState)(1);
  useLayoutEffect(() => {
    const {
      ResizeObserver
    } = window;
    if (ResizeObserver == null) return;
    const {
      clientWidth,
      clientHeight,
      offsetWidth,
      offsetHeight
    } = gridRef.current;
    const {
      width,
      height
    } = gridRef.current.getBoundingClientRect();
    const initialWidth = width - offsetWidth + clientWidth;
    const initialHeight = height - offsetHeight + clientHeight;
    setInlineSize(handleDevicePixelRatio(initialWidth));
    setBlockSize(initialHeight);
    const resizeObserver = new ResizeObserver((entries) => {
      const size = entries[0].contentBoxSize[0];
      setInlineSize(handleDevicePixelRatio(size.inlineSize));
      setBlockSize(size.blockSize);
    });
    resizeObserver.observe(gridRef.current);
    return () => {
      resizeObserver.disconnect();
    };
  }, []);
  return [gridRef, inlineSize, blockSize];
}
function handleDevicePixelRatio(size) {
  return size - (devicePixelRatio === 1 ? 0 : ceil(devicePixelRatio));
}
function useLatestFunc(fn) {
  const ref = (0, import_react.useRef)(fn);
  (0, import_react.useEffect)(() => {
    ref.current = fn;
  });
  return (0, import_react.useCallback)((...args) => {
    ref.current(...args);
  }, []);
}
function useRovingCellRef(isSelected) {
  const [isChildFocused, setIsChildFocused] = (0, import_react.useState)(false);
  if (isChildFocused && !isSelected) {
    setIsChildFocused(false);
  }
  const ref = (0, import_react.useCallback)((cell2) => {
    if (cell2 === null) return;
    scrollIntoView(cell2);
    if (cell2.contains(document.activeElement)) return;
    cell2.focus({
      preventScroll: true
    });
  }, []);
  function onFocus(event) {
    if (event.target !== event.currentTarget) {
      setIsChildFocused(true);
    }
  }
  const isFocused = isSelected && !isChildFocused;
  return {
    ref: isSelected ? ref : void 0,
    tabIndex: isFocused ? 0 : -1,
    onFocus: isSelected ? onFocus : void 0
  };
}
function useViewportColumns({
  columns,
  colSpanColumns,
  rows,
  summaryRows,
  colOverscanStartIdx,
  colOverscanEndIdx,
  lastFrozenColumnIndex,
  rowOverscanStartIdx,
  rowOverscanEndIdx,
  isGroupRow
}) {
  const startIdx = (0, import_react.useMemo)(() => {
    if (colOverscanStartIdx === 0) return 0;
    let startIdx2 = colOverscanStartIdx;
    const updateStartIdx = (colIdx, colSpan) => {
      if (colSpan !== void 0 && colIdx + colSpan > colOverscanStartIdx) {
        startIdx2 = colIdx;
        return true;
      }
      return false;
    };
    for (const column of colSpanColumns) {
      const colIdx = column.idx;
      if (colIdx >= startIdx2) break;
      if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {
        type: "HEADER"
      }))) {
        break;
      }
      for (let rowIdx = rowOverscanStartIdx; rowIdx <= rowOverscanEndIdx; rowIdx++) {
        const row2 = rows[rowIdx];
        if (isGroupRow(row2)) continue;
        if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {
          type: "ROW",
          row: row2
        }))) {
          break;
        }
      }
      if (summaryRows != null) {
        for (const row2 of summaryRows) {
          if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {
            type: "SUMMARY",
            row: row2
          }))) {
            break;
          }
        }
      }
    }
    return startIdx2;
  }, [rowOverscanStartIdx, rowOverscanEndIdx, rows, summaryRows, colOverscanStartIdx, lastFrozenColumnIndex, colSpanColumns, isGroupRow]);
  return (0, import_react.useMemo)(() => {
    const viewportColumns = [];
    for (let colIdx = 0; colIdx <= colOverscanEndIdx; colIdx++) {
      const column = columns[colIdx];
      if (colIdx < startIdx && !column.frozen) continue;
      viewportColumns.push(column);
    }
    return viewportColumns;
  }, [startIdx, colOverscanEndIdx, columns]);
}
function isReadonlyArray(arr) {
  return Array.isArray(arr);
}
function useViewportRows({
  rawRows,
  rowHeight,
  clientHeight,
  scrollTop,
  groupBy,
  rowGrouper,
  expandedGroupIds,
  enableVirtualization
}) {
  const [groupedRows, rowsCount] = (0, import_react.useMemo)(() => {
    if (groupBy.length === 0 || rowGrouper == null) return [void 0, rawRows.length];
    const groupRows = (rows2, [groupByKey, ...remainingGroupByKeys], startRowIndex) => {
      let groupRowsCount = 0;
      const groups = {};
      for (const [key, childRows] of Object.entries(rowGrouper(rows2, groupByKey))) {
        const [childGroups, childRowsCount] = remainingGroupByKeys.length === 0 ? [childRows, childRows.length] : groupRows(childRows, remainingGroupByKeys, startRowIndex + groupRowsCount + 1);
        groups[key] = {
          childRows,
          childGroups,
          startRowIndex: startRowIndex + groupRowsCount
        };
        groupRowsCount += childRowsCount + 1;
      }
      return [groups, groupRowsCount];
    };
    return groupRows(rawRows, groupBy, 0);
  }, [groupBy, rowGrouper, rawRows]);
  const [rows, isGroupRow] = (0, import_react.useMemo)(() => {
    const allGroupRows = /* @__PURE__ */ new Set();
    if (!groupedRows) return [rawRows, isGroupRow2];
    const flattenedRows = [];
    const expandGroup = (rows2, parentId, level) => {
      if (isReadonlyArray(rows2)) {
        flattenedRows.push(...rows2);
        return;
      }
      Object.keys(rows2).forEach((groupKey, posInSet, keys) => {
        const id = parentId !== void 0 ? `${parentId}__${groupKey}` : groupKey;
        const isExpanded = (expandedGroupIds == null ? void 0 : expandedGroupIds.has(id)) ?? false;
        const {
          childRows,
          childGroups,
          startRowIndex
        } = rows2[groupKey];
        const groupRow2 = {
          id,
          parentId,
          groupKey,
          isExpanded,
          childRows,
          level,
          posInSet,
          startRowIndex,
          setSize: keys.length
        };
        flattenedRows.push(groupRow2);
        allGroupRows.add(groupRow2);
        if (isExpanded) {
          expandGroup(childGroups, id, level + 1);
        }
      });
    };
    expandGroup(groupedRows, void 0, 0);
    return [flattenedRows, isGroupRow2];
    function isGroupRow2(row2) {
      return allGroupRows.has(row2);
    }
  }, [expandedGroupIds, groupedRows, rawRows]);
  const {
    totalRowHeight,
    gridTemplateRows,
    getRowTop,
    getRowHeight,
    findRowIdx
  } = (0, import_react.useMemo)(() => {
    if (typeof rowHeight === "number") {
      return {
        totalRowHeight: rowHeight * rows.length,
        gridTemplateRows: ` repeat(${rows.length}, ${rowHeight}px)`,
        getRowTop: (rowIdx) => rowIdx * rowHeight,
        getRowHeight: () => rowHeight,
        findRowIdx: (offset) => floor(offset / rowHeight)
      };
    }
    let totalRowHeight2 = 0;
    let gridTemplateRows2 = " ";
    const rowPositions = rows.map((row2) => {
      const currentRowHeight = isGroupRow(row2) ? rowHeight({
        type: "GROUP",
        row: row2
      }) : rowHeight({
        type: "ROW",
        row: row2
      });
      const position = {
        top: totalRowHeight2,
        height: currentRowHeight
      };
      gridTemplateRows2 += `${currentRowHeight}px `;
      totalRowHeight2 += currentRowHeight;
      return position;
    });
    const validateRowIdx = (rowIdx) => {
      return max(0, min(rows.length - 1, rowIdx));
    };
    return {
      totalRowHeight: totalRowHeight2,
      gridTemplateRows: gridTemplateRows2,
      getRowTop: (rowIdx) => rowPositions[validateRowIdx(rowIdx)].top,
      getRowHeight: (rowIdx) => rowPositions[validateRowIdx(rowIdx)].height,
      findRowIdx(offset) {
        let start = 0;
        let end = rowPositions.length - 1;
        while (start <= end) {
          const middle = start + floor((end - start) / 2);
          const currentOffset = rowPositions[middle].top;
          if (currentOffset === offset) return middle;
          if (currentOffset < offset) {
            start = middle + 1;
          } else if (currentOffset > offset) {
            end = middle - 1;
          }
          if (start > end) return end;
        }
        return 0;
      }
    };
  }, [isGroupRow, rowHeight, rows]);
  let rowOverscanStartIdx = 0;
  let rowOverscanEndIdx = rows.length - 1;
  if (enableVirtualization) {
    const overscanThreshold = 4;
    const rowVisibleStartIdx = findRowIdx(scrollTop);
    const rowVisibleEndIdx = findRowIdx(scrollTop + clientHeight);
    rowOverscanStartIdx = max(0, rowVisibleStartIdx - overscanThreshold);
    rowOverscanEndIdx = min(rows.length - 1, rowVisibleEndIdx + overscanThreshold);
  }
  return {
    rowOverscanStartIdx,
    rowOverscanEndIdx,
    rows,
    rowsCount,
    totalRowHeight,
    gridTemplateRows,
    isGroupRow,
    getRowTop,
    getRowHeight,
    findRowIdx
  };
}
var css_248z$a = ".h1tr5c9i700-beta13{cursor:pointer;display:flex}.h1tr5c9i700-beta13:focus{outline:none}.h19r0msv700-beta13{flex-grow:1;overflow:hidden;overflow:clip;text-overflow:ellipsis}";
styleInject(css_248z$a, { "insertAt": "top" });
var headerSortCell = "h1tr5c9i700-beta13";
var headerSortCellClassname = `rdg-header-sort-cell ${headerSortCell}`;
var headerSortName = "h19r0msv700-beta13";
var headerSortNameClassname = `rdg-header-sort-name ${headerSortName}`;
function HeaderRenderer({
  column,
  sortDirection,
  priority,
  onSort,
  isCellSelected
}) {
  if (!column.sortable) return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, {
    children: column.name
  });
  return (0, import_jsx_runtime.jsx)(SortableHeaderCell, {
    onSort,
    sortDirection,
    priority,
    isCellSelected,
    children: column.name
  });
}
function SortableHeaderCell({
  onSort,
  sortDirection,
  priority,
  children,
  isCellSelected
}) {
  const SortIcon2 = useDefaultComponents().sortIcon;
  const {
    ref,
    tabIndex
  } = useFocusRef(isCellSelected);
  function handleKeyDown(event) {
    if (event.key === " " || event.key === "Enter") {
      event.preventDefault();
      onSort(event.ctrlKey || event.metaKey);
    }
  }
  function handleClick(event) {
    onSort(event.ctrlKey || event.metaKey);
  }
  return (0, import_jsx_runtime.jsxs)("span", {
    ref,
    tabIndex,
    className: headerSortCellClassname,
    onClick: handleClick,
    onKeyDown: handleKeyDown,
    children: [(0, import_jsx_runtime.jsx)("span", {
      className: headerSortNameClassname,
      children
    }), (0, import_jsx_runtime.jsxs)("span", {
      children: [(0, import_jsx_runtime.jsx)(SortIcon2, {
        sortDirection
      }), priority]
    })]
  });
}
var css_248z$9 = '.celq7o9700-beta13{touch-action:none}.celq7o9700-beta13:after{content:"";cursor:col-resize;inline-size:10px;inset-block-end:0;inset-block-start:0;inset-inline-end:0;position:absolute}';
styleInject(css_248z$9, { "insertAt": "top" });
var cellResizable = "celq7o9700-beta13";
var cellResizableClassname = `rdg-cell-resizable ${cellResizable}`;
function HeaderCell({
  column,
  colSpan,
  isCellSelected,
  onColumnResize,
  allRowsSelected,
  onAllRowsSelectionChange,
  sortColumns,
  onSortColumnsChange,
  selectCell,
  shouldFocusGrid,
  direction
}) {
  const isRtl = direction === "rtl";
  const {
    ref,
    tabIndex,
    onFocus
  } = useRovingCellRef(isCellSelected);
  const sortIndex = sortColumns == null ? void 0 : sortColumns.findIndex((sort) => sort.columnKey === column.key);
  const sortColumn = sortIndex !== void 0 && sortIndex > -1 ? sortColumns[sortIndex] : void 0;
  const sortDirection = sortColumn == null ? void 0 : sortColumn.direction;
  const priority = sortColumn !== void 0 && sortColumns.length > 1 ? sortIndex + 1 : void 0;
  const ariaSort = sortDirection && !priority ? sortDirection === "ASC" ? "ascending" : "descending" : void 0;
  const className = getCellClassname(column, column.headerCellClass, column.resizable && cellResizableClassname);
  const HeaderRenderer$1 = column.headerRenderer ?? HeaderRenderer;
  function onPointerDown(event) {
    if (event.pointerType === "mouse" && event.buttons !== 1) {
      return;
    }
    const {
      currentTarget,
      pointerId
    } = event;
    const {
      right,
      left
    } = currentTarget.getBoundingClientRect();
    const offset = isRtl ? event.clientX - left : right - event.clientX;
    if (offset > 11) {
      return;
    }
    function onPointerMove(event2) {
      const {
        right: right2,
        left: left2
      } = currentTarget.getBoundingClientRect();
      const width = isRtl ? right2 + offset - event2.clientX : event2.clientX + offset - left2;
      if (width > 0) {
        onColumnResize(column, clampColumnWidth(width, column));
      }
    }
    function onLostPointerCapture() {
      currentTarget.removeEventListener("pointermove", onPointerMove);
      currentTarget.removeEventListener("lostpointercapture", onLostPointerCapture);
    }
    currentTarget.setPointerCapture(pointerId);
    currentTarget.addEventListener("pointermove", onPointerMove);
    currentTarget.addEventListener("lostpointercapture", onLostPointerCapture);
  }
  function onSort(ctrlClick) {
    if (onSortColumnsChange == null) return;
    const {
      sortDescendingFirst
    } = column;
    if (sortColumn === void 0) {
      const nextSort = {
        columnKey: column.key,
        direction: sortDescendingFirst ? "DESC" : "ASC"
      };
      onSortColumnsChange(sortColumns && ctrlClick ? [...sortColumns, nextSort] : [nextSort]);
    } else {
      let nextSortColumn;
      if (sortDescendingFirst && sortDirection === "DESC" || !sortDescendingFirst && sortDirection === "ASC") {
        nextSortColumn = {
          columnKey: column.key,
          direction: sortDirection === "ASC" ? "DESC" : "ASC"
        };
      }
      if (ctrlClick) {
        const nextSortColumns = [...sortColumns];
        if (nextSortColumn) {
          nextSortColumns[sortIndex] = nextSortColumn;
        } else {
          nextSortColumns.splice(sortIndex, 1);
        }
        onSortColumnsChange(nextSortColumns);
      } else {
        onSortColumnsChange(nextSortColumn ? [nextSortColumn] : []);
      }
    }
  }
  function onClick() {
    selectCell(column.idx);
  }
  function onDoubleClick(event) {
    const {
      right,
      left
    } = event.currentTarget.getBoundingClientRect();
    const offset = isRtl ? event.clientX - left : right - event.clientX;
    if (offset > 11) {
      return;
    }
    onColumnResize(column, "auto");
  }
  function handleFocus(event) {
    onFocus == null ? void 0 : onFocus(event);
    if (shouldFocusGrid) {
      selectCell(0);
    }
  }
  return (0, import_jsx_runtime.jsx)("div", {
    role: "columnheader",
    "aria-colindex": column.idx + 1,
    "aria-selected": isCellSelected,
    "aria-sort": ariaSort,
    "aria-colspan": colSpan,
    ref,
    tabIndex: shouldFocusGrid ? 0 : tabIndex,
    className,
    style: {
      ...getCellStyle(column, colSpan),
      minWidth: column.minWidth,
      maxWidth: column.maxWidth ?? void 0
    },
    onFocus: handleFocus,
    onClick,
    onDoubleClick: column.resizable ? onDoubleClick : void 0,
    onPointerDown: column.resizable ? onPointerDown : void 0,
    children: (0, import_jsx_runtime.jsx)(HeaderRenderer$1, {
      column,
      sortDirection,
      priority,
      onSort,
      allRowsSelected,
      onAllRowsSelectionChange,
      isCellSelected
    })
  });
}
var css_248z$8 = ".h197vzie700-beta13{background-color:var(--rdg-header-background-color);display:contents;font-weight:700;line-height:var(--rdg-header-row-height)}.h197vzie700-beta13>.c1wupbe700-beta13{inset-block-start:0;position:sticky;z-index:2}.h197vzie700-beta13>.c1730fa4700-beta13{z-index:3}";
styleInject(css_248z$8, { "insertAt": "top" });
var headerRow = "h197vzie700-beta13";
var headerRowClassname = `rdg-header-row ${headerRow}`;
function HeaderRow({
  columns,
  allRowsSelected,
  onAllRowsSelectionChange,
  onColumnResize,
  sortColumns,
  onSortColumnsChange,
  lastFrozenColumnIndex,
  selectedCellIdx,
  selectCell,
  shouldFocusGrid,
  direction
}) {
  const cells = [];
  for (let index = 0; index < columns.length; index++) {
    const column = columns[index];
    const colSpan = getColSpan(column, lastFrozenColumnIndex, {
      type: "HEADER"
    });
    if (colSpan !== void 0) {
      index += colSpan - 1;
    }
    cells.push((0, import_jsx_runtime.jsx)(HeaderCell, {
      column,
      colSpan,
      isCellSelected: selectedCellIdx === column.idx,
      onColumnResize,
      allRowsSelected,
      onAllRowsSelectionChange,
      onSortColumnsChange,
      sortColumns,
      selectCell,
      shouldFocusGrid: shouldFocusGrid && index === 0,
      direction
    }, column.key));
  }
  return (0, import_jsx_runtime.jsx)("div", {
    role: "row",
    "aria-rowindex": 1,
    className: clsx_m_default(headerRowClassname, selectedCellIdx === -1 && rowSelectedClassname),
    style: getRowStyle(1),
    children: cells
  });
}
var HeaderRow$1 = (0, import_react.memo)(HeaderRow);
var css_248z$7 = ".c1bmg16t700-beta13,.ccpfvsn700-beta13{background-color:#ccf}.c1bmg16t700-beta13.ccpfvsn700-beta13{background-color:#99f}";
styleInject(css_248z$7, { "insertAt": "top" });
var cellCopied = "ccpfvsn700-beta13";
var cellCopiedClassname = `rdg-cell-copied ${cellCopied}`;
var cellDraggedOver = "c1bmg16t700-beta13";
var cellDraggedOverClassname = `rdg-cell-dragged-over ${cellDraggedOver}`;
function Cell({
  column,
  colSpan,
  isCellSelected,
  isCopied,
  isDraggedOver,
  row: row2,
  dragHandle,
  onRowClick,
  onRowDoubleClick,
  onRowChange,
  selectCell,
  ...props
}) {
  const {
    ref,
    tabIndex,
    onFocus
  } = useRovingCellRef(isCellSelected);
  const {
    cellClass
  } = column;
  const className = getCellClassname(column, typeof cellClass === "function" ? cellClass(row2) : cellClass, isCopied && cellCopiedClassname, isDraggedOver && cellDraggedOverClassname);
  function selectCellWrapper(openEditor) {
    selectCell(row2, column, openEditor);
  }
  function handleClick() {
    var _a;
    selectCellWrapper((_a = column.editorOptions) == null ? void 0 : _a.editOnClick);
    onRowClick == null ? void 0 : onRowClick(row2, column);
  }
  function handleContextMenu() {
    selectCellWrapper();
  }
  function handleDoubleClick() {
    selectCellWrapper(true);
    onRowDoubleClick == null ? void 0 : onRowDoubleClick(row2, column);
  }
  return (0, import_jsx_runtime.jsx)("div", {
    role: "gridcell",
    "aria-colindex": column.idx + 1,
    "aria-selected": isCellSelected,
    "aria-colspan": colSpan,
    "aria-readonly": !isCellEditable(column, row2) || void 0,
    ref,
    tabIndex,
    className,
    style: getCellStyle(column, colSpan),
    onClick: handleClick,
    onDoubleClick: handleDoubleClick,
    onContextMenu: handleContextMenu,
    onFocus,
    ...props,
    children: !column.rowGroup && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
      children: [(0, import_jsx_runtime.jsx)(column.formatter, {
        column,
        row: row2,
        isCellSelected,
        onRowChange
      }), dragHandle]
    })
  });
}
var Cell$1 = (0, import_react.memo)(Cell);
function Row({
  className,
  rowIdx,
  gridRowStart,
  height,
  selectedCellIdx,
  isRowSelected,
  copiedCellIdx,
  draggedOverCellIdx,
  lastFrozenColumnIndex,
  row: row2,
  viewportColumns,
  selectedCellEditor,
  selectedCellDragHandle,
  onRowClick,
  onRowDoubleClick,
  rowClass,
  setDraggedOverRowIdx,
  onMouseEnter,
  onRowChange,
  selectCell,
  ...props
}, ref) {
  const handleRowChange = useLatestFunc((newRow) => {
    onRowChange(rowIdx, newRow);
  });
  function handleDragEnter(event) {
    setDraggedOverRowIdx == null ? void 0 : setDraggedOverRowIdx(rowIdx);
    onMouseEnter == null ? void 0 : onMouseEnter(event);
  }
  className = clsx_m_default(rowClassname, `rdg-row-${rowIdx % 2 === 0 ? "even" : "odd"}`, rowClass == null ? void 0 : rowClass(row2), className, selectedCellIdx === -1 && rowSelectedClassname);
  const cells = [];
  for (let index = 0; index < viewportColumns.length; index++) {
    const column = viewportColumns[index];
    const {
      idx
    } = column;
    const colSpan = getColSpan(column, lastFrozenColumnIndex, {
      type: "ROW",
      row: row2
    });
    if (colSpan !== void 0) {
      index += colSpan - 1;
    }
    const isCellSelected = selectedCellIdx === idx;
    if (isCellSelected && selectedCellEditor) {
      cells.push(selectedCellEditor);
    } else {
      cells.push((0, import_jsx_runtime.jsx)(Cell$1, {
        column,
        colSpan,
        row: row2,
        isCopied: copiedCellIdx === idx,
        isDraggedOver: draggedOverCellIdx === idx,
        isCellSelected,
        dragHandle: isCellSelected ? selectedCellDragHandle : void 0,
        onRowClick,
        onRowDoubleClick,
        onRowChange: handleRowChange,
        selectCell
      }, column.key));
    }
  }
  return (0, import_jsx_runtime.jsx)(RowSelectionProvider, {
    value: isRowSelected,
    children: (0, import_jsx_runtime.jsx)("div", {
      role: "row",
      ref,
      className,
      onMouseEnter: handleDragEnter,
      style: getRowStyle(gridRowStart, height),
      ...props,
      children: cells
    })
  });
}
var Row$1 = (0, import_react.memo)((0, import_react.forwardRef)(Row));
function GroupCell({
  id,
  groupKey,
  childRows,
  isExpanded,
  isCellSelected,
  column,
  row: row2,
  groupColumnIndex,
  toggleGroup: toggleGroupWrapper
}) {
  const {
    ref,
    tabIndex,
    onFocus
  } = useRovingCellRef(isCellSelected);
  function toggleGroup() {
    toggleGroupWrapper(id);
  }
  const isLevelMatching = column.rowGroup && groupColumnIndex === column.idx;
  return (0, import_jsx_runtime.jsx)("div", {
    role: "gridcell",
    "aria-colindex": column.idx + 1,
    "aria-selected": isCellSelected,
    ref,
    tabIndex,
    className: getCellClassname(column),
    style: {
      ...getCellStyle(column),
      cursor: isLevelMatching ? "pointer" : "default"
    },
    onClick: isLevelMatching ? toggleGroup : void 0,
    onFocus,
    children: (!column.rowGroup || groupColumnIndex === column.idx) && column.groupFormatter && (0, import_jsx_runtime.jsx)(column.groupFormatter, {
      groupKey,
      childRows,
      column,
      row: row2,
      isExpanded,
      isCellSelected,
      toggleGroup
    })
  }, column.key);
}
var GroupCell$1 = (0, import_react.memo)(GroupCell);
var css_248z$6 = ".gyxx7e9700-beta13:not([aria-selected=true]){background-color:var(--rdg-header-background-color)}.gyxx7e9700-beta13>.c1wupbe700-beta13:not(:last-child):not(.c9dpaye700-beta13){border-inline-end:none}";
styleInject(css_248z$6, { "insertAt": "top" });
var groupRow = "gyxx7e9700-beta13";
var groupRowClassname = `rdg-group-row ${groupRow}`;
function GroupedRow({
  id,
  groupKey,
  viewportColumns,
  childRows,
  rowIdx,
  row: row2,
  gridRowStart,
  height,
  level,
  isExpanded,
  selectedCellIdx,
  isRowSelected,
  selectGroup,
  toggleGroup,
  ...props
}) {
  const idx = viewportColumns[0].key === SELECT_COLUMN_KEY ? level + 1 : level;
  function handleSelectGroup() {
    selectGroup(rowIdx);
  }
  return (0, import_jsx_runtime.jsx)(RowSelectionProvider, {
    value: isRowSelected,
    children: (0, import_jsx_runtime.jsx)("div", {
      role: "row",
      "aria-level": level,
      "aria-expanded": isExpanded,
      className: clsx_m_default(rowClassname, groupRowClassname, `rdg-row-${rowIdx % 2 === 0 ? "even" : "odd"}`, selectedCellIdx === -1 && rowSelectedClassname),
      onClick: handleSelectGroup,
      style: getRowStyle(gridRowStart, height),
      ...props,
      children: viewportColumns.map((column) => (0, import_jsx_runtime.jsx)(GroupCell$1, {
        id,
        groupKey,
        childRows,
        isExpanded,
        isCellSelected: selectedCellIdx === column.idx,
        column,
        row: row2,
        groupColumnIndex: idx,
        toggleGroup
      }, column.key))
    })
  });
}
var GroupRowRenderer = (0, import_react.memo)(GroupedRow);
var css_248z$5 = ".s1n3hxke700-beta13{inset-block-end:var(--rdg-summary-row-bottom);inset-block-start:var(--rdg-summary-row-top)}";
styleInject(css_248z$5, { "insertAt": "top" });
var summaryCellClassname = "s1n3hxke700-beta13";
function SummaryCell({
  column,
  colSpan,
  row: row2,
  isCellSelected,
  selectCell
}) {
  const {
    ref,
    tabIndex,
    onFocus
  } = useRovingCellRef(isCellSelected);
  const {
    summaryFormatter: SummaryFormatter,
    summaryCellClass
  } = column;
  const className = getCellClassname(column, summaryCellClassname, typeof summaryCellClass === "function" ? summaryCellClass(row2) : summaryCellClass);
  function onClick() {
    selectCell(row2, column);
  }
  return (0, import_jsx_runtime.jsx)("div", {
    role: "gridcell",
    "aria-colindex": column.idx + 1,
    "aria-colspan": colSpan,
    "aria-selected": isCellSelected,
    ref,
    tabIndex,
    className,
    style: getCellStyle(column, colSpan),
    onClick,
    onFocus,
    children: SummaryFormatter && (0, import_jsx_runtime.jsx)(SummaryFormatter, {
      column,
      row: row2,
      isCellSelected
    })
  });
}
var SummaryCell$1 = (0, import_react.memo)(SummaryCell);
var css_248z$4 = ".snfqesz700-beta13.r1otpg64700-beta13{line-height:var(--rdg-summary-row-height)}.snfqesz700-beta13.r1otpg64700-beta13>.c1wupbe700-beta13{position:sticky}.s1jijrjz700-beta13>.c1wupbe700-beta13{border-block-start:2px solid var(--rdg-summary-border-color)}";
styleInject(css_248z$4, { "insertAt": "top" });
var summaryRow = "snfqesz700-beta13";
var summaryRowBorderClassname = "s1jijrjz700-beta13";
var summaryRowClassname = `rdg-summary-row ${summaryRow}`;
function SummaryRow({
  rowIdx,
  gridRowStart,
  row: row2,
  viewportColumns,
  top,
  bottom,
  lastFrozenColumnIndex,
  selectedCellIdx,
  selectCell,
  "aria-rowindex": ariaRowIndex
}) {
  const cells = [];
  for (let index = 0; index < viewportColumns.length; index++) {
    const column = viewportColumns[index];
    const colSpan = getColSpan(column, lastFrozenColumnIndex, {
      type: "SUMMARY",
      row: row2
    });
    if (colSpan !== void 0) {
      index += colSpan - 1;
    }
    const isCellSelected = selectedCellIdx === column.idx;
    cells.push((0, import_jsx_runtime.jsx)(SummaryCell$1, {
      column,
      colSpan,
      row: row2,
      isCellSelected,
      selectCell
    }, column.key));
  }
  return (0, import_jsx_runtime.jsx)("div", {
    role: "row",
    "aria-rowindex": ariaRowIndex,
    className: clsx_m_default(rowClassname, `rdg-row-${rowIdx % 2 === 0 ? "even" : "odd"}`, summaryRowClassname, rowIdx === 0 && summaryRowBorderClassname, selectedCellIdx === -1 && rowSelectedClassname),
    style: {
      ...getRowStyle(gridRowStart),
      "--rdg-summary-row-top": top !== void 0 ? `${top}px` : void 0,
      "--rdg-summary-row-bottom": bottom !== void 0 ? `${bottom}px` : void 0
    },
    children: cells
  });
}
var SummaryRow$1 = (0, import_react.memo)(SummaryRow);
var css_248z$3 = ".c1tngyp1700-beta13.rdg-cell{padding:0}";
styleInject(css_248z$3, { "insertAt": "top" });
var cellEditing = "c1tngyp1700-beta13";
function EditCell({
  column,
  colSpan,
  row: row2,
  onRowChange,
  closeEditor
}) {
  var _a, _b, _c;
  const frameRequestRef = (0, import_react.useRef)();
  const commitOnOutsideClick = ((_a = column.editorOptions) == null ? void 0 : _a.commitOnOutsideClick) !== false;
  const commitOnOutsideMouseDown = useLatestFunc(() => {
    onClose(true);
  });
  (0, import_react.useEffect)(() => {
    if (!commitOnOutsideClick) return;
    function onWindowCaptureMouseDown() {
      frameRequestRef.current = requestAnimationFrame(commitOnOutsideMouseDown);
    }
    addEventListener("mousedown", onWindowCaptureMouseDown, {
      capture: true
    });
    return () => {
      removeEventListener("mousedown", onWindowCaptureMouseDown, {
        capture: true
      });
      cancelFrameRequest();
    };
  }, [commitOnOutsideClick, commitOnOutsideMouseDown]);
  function cancelFrameRequest() {
    cancelAnimationFrame(frameRequestRef.current);
  }
  function onKeyDown(event) {
    var _a2;
    if (event.key === "Escape") {
      event.stopPropagation();
      onClose();
    } else if (event.key === "Enter") {
      event.stopPropagation();
      onClose(true);
    } else {
      const onNavigation = ((_a2 = column.editorOptions) == null ? void 0 : _a2.onNavigation) ?? onEditorNavigation;
      if (!onNavigation(event)) {
        event.stopPropagation();
      }
    }
  }
  function onClose(commitChanges) {
    if (commitChanges) {
      onRowChange(row2, true);
    } else {
      closeEditor();
    }
  }
  const {
    cellClass
  } = column;
  const className = getCellClassname(column, "rdg-editor-container", typeof cellClass === "function" ? cellClass(row2) : cellClass, !((_b = column.editorOptions) == null ? void 0 : _b.renderFormatter) && cellEditing);
  return (0, import_jsx_runtime.jsx)("div", {
    role: "gridcell",
    "aria-colindex": column.idx + 1,
    "aria-colspan": colSpan,
    "aria-selected": true,
    className,
    style: getCellStyle(column, colSpan),
    onKeyDown,
    onMouseDownCapture: commitOnOutsideClick ? cancelFrameRequest : void 0,
    children: column.editor != null && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
      children: [(0, import_jsx_runtime.jsx)(column.editor, {
        column,
        row: row2,
        onRowChange,
        onClose
      }), ((_c = column.editorOptions) == null ? void 0 : _c.renderFormatter) && (0, import_jsx_runtime.jsx)(column.formatter, {
        column,
        row: row2,
        isCellSelected: true,
        onRowChange
      })]
    })
  });
}
var css_248z$2 = ".cadd3bp700-beta13{background-color:var(--rdg-selection-color);block-size:8px;cursor:move;inline-size:8px;inset-block-end:0;inset-inline-end:0;position:absolute}.cadd3bp700-beta13:hover{background-color:var(--rdg-background-color);block-size:16px;border:2px solid var(--rdg-selection-color);inline-size:16px}";
styleInject(css_248z$2, { "insertAt": "top" });
var cellDragHandle = "cadd3bp700-beta13";
var cellDragHandleClassname = `rdg-cell-drag-handle ${cellDragHandle}`;
function DragHandle({
  rows,
  columns,
  selectedPosition,
  latestDraggedOverRowIdx,
  isCellEditable: isCellEditable2,
  onRowsChange,
  onFill,
  setDragging,
  setDraggedOverRowIdx
}) {
  function handleMouseDown(event) {
    if (event.buttons !== 1) return;
    setDragging(true);
    window.addEventListener("mouseover", onMouseOver);
    window.addEventListener("mouseup", onMouseUp);
    function onMouseOver(event2) {
      if (event2.buttons !== 1) onMouseUp();
    }
    function onMouseUp() {
      window.removeEventListener("mouseover", onMouseOver);
      window.removeEventListener("mouseup", onMouseUp);
      setDragging(false);
      handleDragEnd();
    }
  }
  function handleDragEnd() {
    const overRowIdx = latestDraggedOverRowIdx.current;
    if (overRowIdx === void 0) return;
    const {
      rowIdx
    } = selectedPosition;
    const startRowIndex = rowIdx < overRowIdx ? rowIdx + 1 : overRowIdx;
    const endRowIndex = rowIdx < overRowIdx ? overRowIdx + 1 : rowIdx;
    updateRows(startRowIndex, endRowIndex);
    setDraggedOverRowIdx(void 0);
  }
  function handleDoubleClick(event) {
    event.stopPropagation();
    updateRows(selectedPosition.rowIdx + 1, rows.length);
  }
  function updateRows(startRowIdx, endRowIdx) {
    const {
      idx,
      rowIdx
    } = selectedPosition;
    const column = columns[idx];
    const sourceRow = rows[rowIdx];
    const updatedRows = [...rows];
    const indexes = [];
    for (let i = startRowIdx; i < endRowIdx; i++) {
      if (isCellEditable2({
        rowIdx: i,
        idx
      })) {
        const updatedRow = onFill({
          columnKey: column.key,
          sourceRow,
          targetRow: rows[i]
        });
        if (updatedRow !== rows[i]) {
          updatedRows[i] = updatedRow;
          indexes.push(i);
        }
      }
    }
    if (indexes.length > 0) {
      onRowsChange == null ? void 0 : onRowsChange(updatedRows, {
        indexes,
        column
      });
    }
  }
  return (0, import_jsx_runtime.jsx)("div", {
    className: cellDragHandleClassname,
    onMouseDown: handleMouseDown,
    onDoubleClick: handleDoubleClick
  });
}
var css_248z$1 = ".a888944700-beta13{fill:currentColor}.a888944700-beta13>path{transition:d .1s}";
styleInject(css_248z$1, { "insertAt": "top" });
var arrow = "a888944700-beta13";
var arrowClassname = `rdg-sort-arrow ${arrow}`;
function SortIcon({
  sortDirection
}) {
  return sortDirection !== void 0 ? (0, import_jsx_runtime.jsx)("svg", {
    viewBox: "0 0 12 8",
    width: "12",
    height: "8",
    className: arrowClassname,
    "aria-hidden": true,
    children: (0, import_jsx_runtime.jsx)("path", {
      d: sortDirection === "ASC" ? "M0 8 6 0 12 8" : "M0 0 6 8 12 0"
    })
  }) : null;
}
var initialPosition = {
  idx: -1,
  rowIdx: -2,
  mode: "SELECT"
};
function DataGrid({
  columns: rawColumns,
  rows: rawRows,
  summaryRows,
  rowKeyGetter,
  onRowsChange,
  rowHeight,
  headerRowHeight: rawHeaderRowHeight,
  summaryRowHeight: rawSummaryRowHeight,
  selectedRows,
  onSelectedRowsChange,
  sortColumns,
  onSortColumnsChange,
  defaultColumnOptions,
  groupBy: rawGroupBy,
  rowGrouper,
  expandedGroupIds,
  onExpandedGroupIdsChange,
  onRowClick,
  onRowDoubleClick,
  onScroll,
  onColumnResize,
  onFill,
  onCopy,
  onPaste,
  cellNavigationMode: rawCellNavigationMode,
  enableVirtualization,
  components,
  className,
  style,
  rowClass,
  direction,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledBy,
  "aria-describedby": ariaDescribedBy,
  "data-testid": testId
}, ref) {
  const defaultComponents = useDefaultComponents();
  rowHeight ?? (rowHeight = 35);
  const headerRowHeight = rawHeaderRowHeight ?? (typeof rowHeight === "number" ? rowHeight : 35);
  const summaryRowHeight = rawSummaryRowHeight ?? (typeof rowHeight === "number" ? rowHeight : 35);
  const RowRenderer = (components == null ? void 0 : components.rowRenderer) ?? (defaultComponents == null ? void 0 : defaultComponents.rowRenderer) ?? Row$1;
  const sortIcon = (components == null ? void 0 : components.sortIcon) ?? (defaultComponents == null ? void 0 : defaultComponents.sortIcon) ?? SortIcon;
  const checkboxFormatter = (components == null ? void 0 : components.checkboxFormatter) ?? (defaultComponents == null ? void 0 : defaultComponents.checkboxFormatter) ?? CheckboxFormatter;
  const noRowsFallback = (components == null ? void 0 : components.noRowsFallback) ?? (defaultComponents == null ? void 0 : defaultComponents.noRowsFallback);
  const cellNavigationMode = rawCellNavigationMode ?? "NONE";
  enableVirtualization ?? (enableVirtualization = true);
  direction ?? (direction = "ltr");
  const [scrollTop, setScrollTop] = (0, import_react.useState)(0);
  const [scrollLeft, setScrollLeft] = (0, import_react.useState)(0);
  const [columnWidths, setColumnWidths] = (0, import_react.useState)(() => /* @__PURE__ */ new Map());
  const [selectedPosition, setSelectedPosition] = (0, import_react.useState)(initialPosition);
  const [copiedCell, setCopiedCell] = (0, import_react.useState)(null);
  const [isDragging, setDragging] = (0, import_react.useState)(false);
  const [draggedOverRowIdx, setOverRowIdx] = (0, import_react.useState)(void 0);
  const [autoResizeColumn, setAutoResizeColumn] = (0, import_react.useState)(null);
  const prevSelectedPosition = (0, import_react.useRef)(selectedPosition);
  const latestDraggedOverRowIdx = (0, import_react.useRef)(draggedOverRowIdx);
  const lastSelectedRowIdx = (0, import_react.useRef)(-1);
  const rowRef = (0, import_react.useRef)(null);
  const [gridRef, gridWidth, gridHeight] = useGridDimensions();
  const headerRowsCount = 1;
  const summaryRowsCount = (summaryRows == null ? void 0 : summaryRows.length) ?? 0;
  const clientHeight = gridHeight - headerRowHeight - summaryRowsCount * summaryRowHeight;
  const isSelectable = selectedRows != null && onSelectedRowsChange != null;
  const isHeaderRowSelected = selectedPosition.rowIdx === -1;
  const isRtl = direction === "rtl";
  const leftKey = isRtl ? "ArrowRight" : "ArrowLeft";
  const rightKey = isRtl ? "ArrowLeft" : "ArrowRight";
  const defaultGridComponents = (0, import_react.useMemo)(() => ({
    sortIcon,
    checkboxFormatter
  }), [sortIcon, checkboxFormatter]);
  const allRowsSelected = (0, import_react.useMemo)(() => {
    const {
      length
    } = rawRows;
    return length !== 0 && selectedRows != null && rowKeyGetter != null && selectedRows.size >= length && rawRows.every((row2) => selectedRows.has(rowKeyGetter(row2)));
  }, [rawRows, selectedRows, rowKeyGetter]);
  const {
    columns,
    colSpanColumns,
    colOverscanStartIdx,
    colOverscanEndIdx,
    layoutCssVars,
    columnMetrics,
    lastFrozenColumnIndex,
    totalFrozenColumnWidth,
    groupBy
  } = useCalculatedColumns({
    rawColumns,
    columnWidths,
    scrollLeft,
    viewportWidth: gridWidth,
    defaultColumnOptions,
    rawGroupBy: rowGrouper ? rawGroupBy : void 0,
    enableVirtualization
  });
  const {
    rowOverscanStartIdx,
    rowOverscanEndIdx,
    rows,
    rowsCount,
    totalRowHeight,
    gridTemplateRows,
    isGroupRow,
    getRowTop,
    getRowHeight,
    findRowIdx
  } = useViewportRows({
    rawRows,
    groupBy,
    rowGrouper,
    rowHeight,
    clientHeight,
    scrollTop,
    expandedGroupIds,
    enableVirtualization
  });
  const viewportColumns = useViewportColumns({
    columns,
    colSpanColumns,
    colOverscanStartIdx,
    colOverscanEndIdx,
    lastFrozenColumnIndex,
    rowOverscanStartIdx,
    rowOverscanEndIdx,
    rows,
    summaryRows,
    isGroupRow
  });
  const hasGroups = groupBy.length > 0 && typeof rowGrouper === "function";
  const minColIdx = hasGroups ? -1 : 0;
  const maxColIdx = columns.length - 1;
  const minRowIdx = -1;
  const maxRowIdx = headerRowsCount + rows.length + summaryRowsCount - 2;
  const selectedCellIsWithinSelectionBounds = isCellWithinSelectionBounds(selectedPosition);
  const selectedCellIsWithinViewportBounds = isCellWithinViewportBounds(selectedPosition);
  const selectRowLatest = useLatestFunc(selectRow);
  const selectAllRowsLatest = useLatestFunc(selectAllRows);
  const handleFormatterRowChangeLatest = useLatestFunc(updateRow);
  const selectViewportCellLatest = useLatestFunc((row2, column, enableEditor) => {
    const rowIdx = rows.indexOf(row2);
    selectCell({
      rowIdx,
      idx: column.idx
    }, enableEditor);
  });
  const selectGroupLatest = useLatestFunc((rowIdx) => {
    selectCell({
      rowIdx,
      idx: -1
    });
  });
  const selectHeaderCellLatest = useLatestFunc((idx) => {
    selectCell({
      rowIdx: -1,
      idx
    });
  });
  const selectSummaryCellLatest = useLatestFunc((summaryRow2, column) => {
    const rowIdx = summaryRows.indexOf(summaryRow2) + headerRowsCount + rows.length - 1;
    selectCell({
      rowIdx,
      idx: column.idx
    });
  });
  const toggleGroupLatest = useLatestFunc(toggleGroup);
  useLayoutEffect(() => {
    if (!selectedCellIsWithinSelectionBounds || isSamePosition(selectedPosition, prevSelectedPosition.current)) {
      prevSelectedPosition.current = selectedPosition;
      return;
    }
    prevSelectedPosition.current = selectedPosition;
    if (selectedPosition.idx === -1) {
      rowRef.current.focus({
        preventScroll: true
      });
      scrollIntoView(rowRef.current);
    }
  });
  useLayoutEffect(() => {
    if (autoResizeColumn === null) return;
    const columnElement = gridRef.current.querySelector(`[aria-colindex="${autoResizeColumn.idx + 1}"]`);
    const {
      width
    } = columnElement.getBoundingClientRect();
    setColumnWidths((columnWidths2) => {
      const newColumnWidths = new Map(columnWidths2);
      newColumnWidths.set(autoResizeColumn.key, width);
      return newColumnWidths;
    });
    setAutoResizeColumn(null);
    onColumnResize == null ? void 0 : onColumnResize(autoResizeColumn.idx, width);
  }, [autoResizeColumn, gridRef, onColumnResize]);
  (0, import_react.useImperativeHandle)(ref, () => ({
    element: gridRef.current,
    scrollToColumn,
    scrollToRow(rowIdx) {
      const {
        current
      } = gridRef;
      if (!current) return;
      current.scrollTo({
        top: getRowTop(rowIdx),
        behavior: "smooth"
      });
    },
    selectCell
  }));
  const handleColumnResize = (0, import_react.useCallback)((column, width) => {
    if (width === "auto") {
      setAutoResizeColumn(column);
      return;
    }
    setColumnWidths((columnWidths2) => {
      const newColumnWidths = new Map(columnWidths2);
      newColumnWidths.set(column.key, width);
      return newColumnWidths;
    });
    onColumnResize == null ? void 0 : onColumnResize(column.idx, width);
  }, [onColumnResize]);
  const setDraggedOverRowIdx = (0, import_react.useCallback)((rowIdx) => {
    setOverRowIdx(rowIdx);
    latestDraggedOverRowIdx.current = rowIdx;
  }, []);
  function selectRow({
    row: row2,
    checked,
    isShiftClick
  }) {
    if (!onSelectedRowsChange) return;
    assertIsValidKeyGetter(rowKeyGetter);
    const newSelectedRows = new Set(selectedRows);
    if (isGroupRow(row2)) {
      for (const childRow of row2.childRows) {
        const rowKey2 = rowKeyGetter(childRow);
        if (checked) {
          newSelectedRows.add(rowKey2);
        } else {
          newSelectedRows.delete(rowKey2);
        }
      }
      onSelectedRowsChange(newSelectedRows);
      return;
    }
    const rowKey = rowKeyGetter(row2);
    if (checked) {
      newSelectedRows.add(rowKey);
      const previousRowIdx = lastSelectedRowIdx.current;
      const rowIdx = rows.indexOf(row2);
      lastSelectedRowIdx.current = rowIdx;
      if (isShiftClick && previousRowIdx !== -1 && previousRowIdx !== rowIdx) {
        const step = sign(rowIdx - previousRowIdx);
        for (let i = previousRowIdx + step; i !== rowIdx; i += step) {
          const row3 = rows[i];
          if (isGroupRow(row3)) continue;
          newSelectedRows.add(rowKeyGetter(row3));
        }
      }
    } else {
      newSelectedRows.delete(rowKey);
      lastSelectedRowIdx.current = -1;
    }
    onSelectedRowsChange(newSelectedRows);
  }
  function selectAllRows(checked) {
    if (!onSelectedRowsChange) return;
    assertIsValidKeyGetter(rowKeyGetter);
    const newSelectedRows = new Set(selectedRows);
    for (const row2 of rawRows) {
      const rowKey = rowKeyGetter(row2);
      if (checked) {
        newSelectedRows.add(rowKey);
      } else {
        newSelectedRows.delete(rowKey);
      }
    }
    onSelectedRowsChange(newSelectedRows);
  }
  function toggleGroup(expandedGroupId) {
    if (!onExpandedGroupIdsChange) return;
    const newExpandedGroupIds = new Set(expandedGroupIds);
    if (newExpandedGroupIds.has(expandedGroupId)) {
      newExpandedGroupIds.delete(expandedGroupId);
    } else {
      newExpandedGroupIds.add(expandedGroupId);
    }
    onExpandedGroupIdsChange(newExpandedGroupIds);
  }
  function handleKeyDown(event) {
    if (!(event.target instanceof Element)) return;
    const isCellEvent = event.target.closest(".rdg-cell") !== null;
    const isRowEvent = hasGroups && event.target === rowRef.current;
    if (!isCellEvent && !isRowEvent) return;
    const {
      key,
      keyCode
    } = event;
    const {
      rowIdx
    } = selectedPosition;
    if (selectedCellIsWithinViewportBounds && (onPaste != null || onCopy != null) && isCtrlKeyHeldDown(event) && !isGroupRow(rows[rowIdx]) && selectedPosition.mode === "SELECT") {
      const cKey = 67;
      const vKey = 86;
      if (keyCode === cKey) {
        handleCopy();
        return;
      }
      if (keyCode === vKey) {
        handlePaste();
        return;
      }
    }
    if (isRowIdxWithinViewportBounds(rowIdx)) {
      const row2 = rows[rowIdx];
      if (isGroupRow(row2) && selectedPosition.idx === -1 && (key === leftKey && row2.isExpanded || key === rightKey && !row2.isExpanded)) {
        event.preventDefault();
        toggleGroup(row2.id);
        return;
      }
    }
    switch (event.key) {
      case "Escape":
        setCopiedCell(null);
        return;
      case "ArrowUp":
      case "ArrowDown":
      case "ArrowLeft":
      case "ArrowRight":
      case "Tab":
      case "Home":
      case "End":
      case "PageUp":
      case "PageDown":
        navigate(event);
        break;
      default:
        handleCellInput(event);
        break;
    }
  }
  function handleScroll(event) {
    const {
      scrollTop: scrollTop2,
      scrollLeft: scrollLeft2
    } = event.currentTarget;
    setScrollTop(scrollTop2);
    setScrollLeft(abs(scrollLeft2));
    onScroll == null ? void 0 : onScroll(event);
  }
  function getRawRowIdx(rowIdx) {
    return hasGroups ? rawRows.indexOf(rows[rowIdx]) : rowIdx;
  }
  function updateRow(rowIdx, row2) {
    if (typeof onRowsChange !== "function") return;
    const rawRowIdx = getRawRowIdx(rowIdx);
    if (row2 === rawRows[rawRowIdx]) return;
    const updatedRows = [...rawRows];
    updatedRows[rawRowIdx] = row2;
    onRowsChange(updatedRows, {
      indexes: [rawRowIdx],
      column: columns[selectedPosition.idx]
    });
  }
  function commitEditorChanges() {
    if (selectedPosition.mode !== "EDIT") return;
    updateRow(selectedPosition.rowIdx, selectedPosition.row);
  }
  function handleCopy() {
    const {
      idx,
      rowIdx
    } = selectedPosition;
    const sourceRow = rawRows[getRawRowIdx(rowIdx)];
    const sourceColumnKey = columns[idx].key;
    setCopiedCell({
      row: sourceRow,
      columnKey: sourceColumnKey
    });
    onCopy == null ? void 0 : onCopy({
      sourceRow,
      sourceColumnKey
    });
  }
  function handlePaste() {
    if (!onPaste || !onRowsChange || copiedCell === null || !isCellEditable2(selectedPosition)) {
      return;
    }
    const {
      idx,
      rowIdx
    } = selectedPosition;
    const targetRow = rawRows[getRawRowIdx(rowIdx)];
    const updatedTargetRow = onPaste({
      sourceRow: copiedCell.row,
      sourceColumnKey: copiedCell.columnKey,
      targetRow,
      targetColumnKey: columns[idx].key
    });
    updateRow(rowIdx, updatedTargetRow);
  }
  function handleCellInput(event) {
    var _a, _b;
    if (!selectedCellIsWithinViewportBounds) return;
    const row2 = rows[selectedPosition.rowIdx];
    if (isGroupRow(row2)) return;
    const {
      key,
      shiftKey
    } = event;
    if (isSelectable && shiftKey && key === " ") {
      assertIsValidKeyGetter(rowKeyGetter);
      const rowKey = rowKeyGetter(row2);
      selectRow({
        row: row2,
        checked: !selectedRows.has(rowKey),
        isShiftClick: false
      });
      event.preventDefault();
      return;
    }
    const column = columns[selectedPosition.idx];
    (_b = (_a = column.editorOptions) == null ? void 0 : _a.onCellKeyDown) == null ? void 0 : _b.call(_a, event);
    if (event.isDefaultPrevented()) return;
    if (isCellEditable2(selectedPosition) && isDefaultCellInput(event)) {
      setSelectedPosition(({
        idx,
        rowIdx
      }) => ({
        idx,
        rowIdx,
        mode: "EDIT",
        row: row2,
        originalRow: row2
      }));
    }
  }
  function isColIdxWithinSelectionBounds(idx) {
    return idx >= minColIdx && idx <= maxColIdx;
  }
  function isRowIdxWithinViewportBounds(rowIdx) {
    return rowIdx >= 0 && rowIdx < rows.length;
  }
  function isCellWithinSelectionBounds({
    idx,
    rowIdx
  }) {
    return rowIdx >= minRowIdx && rowIdx <= maxRowIdx && isColIdxWithinSelectionBounds(idx);
  }
  function isCellWithinViewportBounds({
    idx,
    rowIdx
  }) {
    return isRowIdxWithinViewportBounds(rowIdx) && isColIdxWithinSelectionBounds(idx);
  }
  function isCellEditable2(position) {
    return isCellWithinViewportBounds(position) && isSelectedCellEditable({
      columns,
      rows,
      selectedPosition: position,
      isGroupRow
    });
  }
  function selectCell(position, enableEditor) {
    var _a;
    if (!isCellWithinSelectionBounds(position)) return;
    commitEditorChanges();
    if (enableEditor && isCellEditable2(position)) {
      const row2 = rows[position.rowIdx];
      setSelectedPosition({
        ...position,
        mode: "EDIT",
        row: row2,
        originalRow: row2
      });
    } else if (isSamePosition(selectedPosition, position)) {
      scrollIntoView((_a = gridRef.current) == null ? void 0 : _a.querySelector('[tabindex="0"]'));
    } else {
      setSelectedPosition({
        ...position,
        mode: "SELECT"
      });
    }
  }
  function scrollToColumn(idx) {
    const {
      current
    } = gridRef;
    if (!current) return;
    if (idx > lastFrozenColumnIndex) {
      const {
        rowIdx
      } = selectedPosition;
      if (!isCellWithinSelectionBounds({
        rowIdx,
        idx
      })) return;
      const {
        clientWidth
      } = current;
      const column = columns[idx];
      const {
        left,
        width
      } = columnMetrics.get(column);
      let right = left + width;
      const colSpan = getSelectedCellColSpan({
        rows,
        summaryRows,
        rowIdx,
        lastFrozenColumnIndex,
        column,
        isGroupRow
      });
      if (colSpan !== void 0) {
        const {
          left: left2,
          width: width2
        } = columnMetrics.get(columns[column.idx + colSpan - 1]);
        right = left2 + width2;
      }
      const isCellAtLeftBoundary = left < scrollLeft + totalFrozenColumnWidth;
      const isCellAtRightBoundary = right > clientWidth + scrollLeft;
      const sign2 = isRtl ? -1 : 1;
      if (isCellAtLeftBoundary) {
        current.scrollLeft = (left - totalFrozenColumnWidth) * sign2;
      } else if (isCellAtRightBoundary) {
        current.scrollLeft = (right - clientWidth) * sign2;
      }
    }
  }
  function getNextPosition(key, ctrlKey, shiftKey) {
    const {
      idx,
      rowIdx
    } = selectedPosition;
    const row2 = rows[rowIdx];
    const isRowSelected = selectedCellIsWithinSelectionBounds && idx === -1;
    if (key === leftKey && isRowSelected && isGroupRow(row2) && !row2.isExpanded && row2.level !== 0) {
      let parentRowIdx = -1;
      for (let i = selectedPosition.rowIdx - 1; i >= 0; i--) {
        const parentRow = rows[i];
        if (isGroupRow(parentRow) && parentRow.id === row2.parentId) {
          parentRowIdx = i;
          break;
        }
      }
      if (parentRowIdx !== -1) {
        return {
          idx,
          rowIdx: parentRowIdx
        };
      }
    }
    switch (key) {
      case "ArrowUp":
        return {
          idx,
          rowIdx: rowIdx - 1
        };
      case "ArrowDown":
        return {
          idx,
          rowIdx: rowIdx + 1
        };
      case leftKey:
        return {
          idx: idx - 1,
          rowIdx
        };
      case rightKey:
        return {
          idx: idx + 1,
          rowIdx
        };
      case "Tab":
        return {
          idx: idx + (shiftKey ? -1 : 1),
          rowIdx
        };
      case "Home":
        if (isRowSelected) return {
          idx,
          rowIdx: 0
        };
        return {
          idx: 0,
          rowIdx: ctrlKey ? minRowIdx : rowIdx
        };
      case "End":
        if (isRowSelected) return {
          idx,
          rowIdx: rows.length - 1
        };
        return {
          idx: maxColIdx,
          rowIdx: ctrlKey ? maxRowIdx : rowIdx
        };
      case "PageUp": {
        if (selectedPosition.rowIdx === minRowIdx) return selectedPosition;
        const nextRowY = getRowTop(rowIdx) + getRowHeight(rowIdx) - clientHeight;
        return {
          idx,
          rowIdx: nextRowY > 0 ? findRowIdx(nextRowY) : 0
        };
      }
      case "PageDown": {
        if (selectedPosition.rowIdx >= rows.length) return selectedPosition;
        const nextRowY = getRowTop(rowIdx) + clientHeight;
        return {
          idx,
          rowIdx: nextRowY < totalRowHeight ? findRowIdx(nextRowY) : rows.length - 1
        };
      }
      default:
        return selectedPosition;
    }
  }
  function navigate(event) {
    const {
      key,
      shiftKey
    } = event;
    let mode = cellNavigationMode;
    if (key === "Tab") {
      if (canExitGrid({
        shiftKey,
        cellNavigationMode,
        maxColIdx,
        minRowIdx,
        maxRowIdx,
        selectedPosition
      })) {
        commitEditorChanges();
        return;
      }
      mode = cellNavigationMode === "NONE" ? "CHANGE_ROW" : cellNavigationMode;
    }
    event.preventDefault();
    const ctrlKey = isCtrlKeyHeldDown(event);
    const nextPosition = getNextPosition(key, ctrlKey, shiftKey);
    if (isSamePosition(selectedPosition, nextPosition)) return;
    const nextSelectedCellPosition = getNextSelectedCellPosition({
      columns,
      colSpanColumns,
      rows,
      summaryRows,
      minRowIdx,
      maxRowIdx,
      lastFrozenColumnIndex,
      cellNavigationMode: mode,
      currentPosition: selectedPosition,
      nextPosition,
      isCellWithinBounds: isCellWithinSelectionBounds,
      isGroupRow
    });
    selectCell(nextSelectedCellPosition);
  }
  function getDraggedOverCellIdx(currentRowIdx) {
    if (draggedOverRowIdx === void 0) return;
    const {
      rowIdx
    } = selectedPosition;
    const isDraggedOver = rowIdx < draggedOverRowIdx ? rowIdx < currentRowIdx && currentRowIdx <= draggedOverRowIdx : rowIdx > currentRowIdx && currentRowIdx >= draggedOverRowIdx;
    return isDraggedOver ? selectedPosition.idx : void 0;
  }
  function getLayoutCssVars() {
    if (autoResizeColumn === null) return layoutCssVars;
    const {
      gridTemplateColumns
    } = layoutCssVars;
    const newSizes = gridTemplateColumns.split(" ");
    newSizes[autoResizeColumn.idx] = "max-content";
    return {
      ...layoutCssVars,
      gridTemplateColumns: newSizes.join(" ")
    };
  }
  function getDragHandle(rowIdx) {
    if (selectedPosition.rowIdx !== rowIdx || selectedPosition.mode === "EDIT" || hasGroups || onFill == null) {
      return;
    }
    return (0, import_jsx_runtime.jsx)(DragHandle, {
      rows: rawRows,
      columns,
      selectedPosition,
      isCellEditable: isCellEditable2,
      latestDraggedOverRowIdx,
      onRowsChange,
      onFill,
      setDragging,
      setDraggedOverRowIdx
    });
  }
  function getCellEditor(rowIdx) {
    if (selectedPosition.rowIdx !== rowIdx || selectedPosition.mode === "SELECT") return;
    const {
      idx,
      row: row2
    } = selectedPosition;
    const column = columns[idx];
    const colSpan = getColSpan(column, lastFrozenColumnIndex, {
      type: "ROW",
      row: row2
    });
    const closeEditor = () => {
      setSelectedPosition(({
        idx: idx2,
        rowIdx: rowIdx2
      }) => ({
        idx: idx2,
        rowIdx: rowIdx2,
        mode: "SELECT"
      }));
    };
    const onRowChange = (row3, commitChanges) => {
      if (commitChanges) {
        updateRow(selectedPosition.rowIdx, row3);
        closeEditor();
      } else {
        setSelectedPosition((position) => ({
          ...position,
          row: row3
        }));
      }
    };
    if (rows[selectedPosition.rowIdx] !== selectedPosition.originalRow) {
      closeEditor();
    }
    return (0, import_jsx_runtime.jsx)(EditCell, {
      column,
      colSpan,
      row: row2,
      onRowChange,
      closeEditor
    }, column.key);
  }
  function getRowViewportColumns(rowIdx) {
    const selectedColumn = columns[selectedPosition.idx];
    if (selectedColumn !== void 0 && selectedPosition.rowIdx === rowIdx && !viewportColumns.includes(selectedColumn)) {
      return selectedPosition.idx > colOverscanEndIdx ? [...viewportColumns, selectedColumn] : [...viewportColumns.slice(0, lastFrozenColumnIndex + 1), selectedColumn, ...viewportColumns.slice(lastFrozenColumnIndex + 1)];
    }
    return viewportColumns;
  }
  function getViewportRows() {
    const rowElements = [];
    let startRowIndex = 0;
    const {
      idx: selectedIdx,
      rowIdx: selectedRowIdx
    } = selectedPosition;
    const startRowIdx = selectedCellIsWithinViewportBounds && selectedRowIdx < rowOverscanStartIdx ? rowOverscanStartIdx - 1 : rowOverscanStartIdx;
    const endRowIdx = selectedCellIsWithinViewportBounds && selectedRowIdx > rowOverscanEndIdx ? rowOverscanEndIdx + 1 : rowOverscanEndIdx;
    for (let viewportRowIdx = startRowIdx; viewportRowIdx <= endRowIdx; viewportRowIdx++) {
      const isRowOutsideViewport = viewportRowIdx === rowOverscanStartIdx - 1 || viewportRowIdx === rowOverscanEndIdx + 1;
      const rowIdx = isRowOutsideViewport ? selectedRowIdx : viewportRowIdx;
      let rowColumns = viewportColumns;
      const selectedColumn = columns[selectedIdx];
      if (selectedColumn !== void 0) {
        if (isRowOutsideViewport) {
          rowColumns = [selectedColumn];
        } else {
          rowColumns = getRowViewportColumns(rowIdx);
        }
      }
      const row2 = rows[rowIdx];
      const gridRowStart = headerRowsCount + rowIdx + 1;
      if (isGroupRow(row2)) {
        ({
          startRowIndex
        } = row2);
        const isGroupRowSelected = isSelectable && row2.childRows.every((cr) => selectedRows.has(rowKeyGetter(cr)));
        rowElements.push((0, import_jsx_runtime.jsx)(GroupRowRenderer, {
          "aria-level": row2.level + 1,
          "aria-setsize": row2.setSize,
          "aria-posinset": row2.posInSet + 1,
          "aria-rowindex": headerRowsCount + startRowIndex + 1,
          "aria-selected": isSelectable ? isGroupRowSelected : void 0,
          id: row2.id,
          groupKey: row2.groupKey,
          viewportColumns: rowColumns,
          childRows: row2.childRows,
          rowIdx,
          row: row2,
          gridRowStart,
          height: getRowHeight(rowIdx),
          level: row2.level,
          isExpanded: row2.isExpanded,
          selectedCellIdx: selectedRowIdx === rowIdx ? selectedIdx : void 0,
          isRowSelected: isGroupRowSelected,
          selectGroup: selectGroupLatest,
          toggleGroup: toggleGroupLatest
        }, row2.id));
        continue;
      }
      startRowIndex++;
      let key;
      let isRowSelected = false;
      if (typeof rowKeyGetter === "function") {
        key = rowKeyGetter(row2);
        isRowSelected = (selectedRows == null ? void 0 : selectedRows.has(key)) ?? false;
      } else {
        key = hasGroups ? startRowIndex : rowIdx;
      }
      rowElements.push((0, import_jsx_runtime.jsx)(RowRenderer, {
        "aria-rowindex": headerRowsCount + (hasGroups ? startRowIndex : rowIdx) + 1,
        "aria-selected": isSelectable ? isRowSelected : void 0,
        rowIdx,
        row: row2,
        viewportColumns: rowColumns,
        isRowSelected,
        onRowClick,
        onRowDoubleClick,
        rowClass,
        gridRowStart,
        height: getRowHeight(rowIdx),
        copiedCellIdx: copiedCell !== null && copiedCell.row === row2 ? columns.findIndex((c) => c.key === copiedCell.columnKey) : void 0,
        selectedCellIdx: selectedRowIdx === rowIdx ? selectedIdx : void 0,
        draggedOverCellIdx: getDraggedOverCellIdx(rowIdx),
        setDraggedOverRowIdx: isDragging ? setDraggedOverRowIdx : void 0,
        lastFrozenColumnIndex,
        onRowChange: handleFormatterRowChangeLatest,
        selectCell: selectViewportCellLatest,
        selectedCellDragHandle: getDragHandle(rowIdx),
        selectedCellEditor: getCellEditor(rowIdx)
      }, key));
    }
    return rowElements;
  }
  if (selectedPosition.idx > maxColIdx || selectedPosition.rowIdx > maxRowIdx) {
    setSelectedPosition(initialPosition);
    setDraggedOverRowIdx(void 0);
  }
  let templateRows = `${headerRowHeight}px`;
  if (rows.length > 0) {
    templateRows += gridTemplateRows;
  }
  if (summaryRowsCount > 0) {
    templateRows += ` repeat(${summaryRowsCount}, ${summaryRowHeight}px)`;
  }
  const isGroupRowFocused = selectedPosition.idx === -1 && selectedPosition.rowIdx !== -2;
  return (0, import_jsx_runtime.jsxs)("div", {
    role: hasGroups ? "treegrid" : "grid",
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledBy,
    "aria-describedby": ariaDescribedBy,
    "aria-multiselectable": isSelectable ? true : void 0,
    "aria-colcount": columns.length,
    "aria-rowcount": headerRowsCount + rowsCount + summaryRowsCount,
    className: clsx_m_default(rootClassname, className, isDragging && viewportDraggingClassname, autoResizeColumn !== null && cellAutoResizeClassname),
    style: {
      ...style,
      scrollPaddingInlineStart: selectedPosition.idx > lastFrozenColumnIndex ? `${totalFrozenColumnWidth}px` : void 0,
      scrollPaddingBlock: selectedPosition.rowIdx >= 0 && selectedPosition.rowIdx < rows.length ? `${headerRowHeight}px ${summaryRowsCount * summaryRowHeight}px` : void 0,
      gridTemplateRows: templateRows,
      "--rdg-header-row-height": `${headerRowHeight}px`,
      "--rdg-summary-row-height": `${summaryRowHeight}px`,
      "--rdg-sign": isRtl ? -1 : 1,
      ...getLayoutCssVars()
    },
    dir: direction,
    ref: gridRef,
    onScroll: handleScroll,
    onKeyDown: handleKeyDown,
    "data-testid": testId,
    children: [hasGroups && (0, import_jsx_runtime.jsx)("div", {
      ref: rowRef,
      tabIndex: isGroupRowFocused ? 0 : -1,
      className: clsx_m_default(focusSinkClassname, isGroupRowFocused && [rowSelected, lastFrozenColumnIndex !== -1 && rowSelectedWithFrozenCell]),
      style: {
        gridRowStart: selectedPosition.rowIdx + 2
      },
      onKeyDown: handleKeyDown
    }), (0, import_jsx_runtime.jsxs)(DataGridDefaultComponentsProvider, {
      value: defaultGridComponents,
      children: [(0, import_jsx_runtime.jsx)(HeaderRow$1, {
        columns: getRowViewportColumns(-1),
        onColumnResize: handleColumnResize,
        allRowsSelected,
        onAllRowsSelectionChange: selectAllRowsLatest,
        sortColumns,
        onSortColumnsChange,
        lastFrozenColumnIndex,
        selectedCellIdx: isHeaderRowSelected ? selectedPosition.idx : void 0,
        selectCell: selectHeaderCellLatest,
        shouldFocusGrid: !selectedCellIsWithinSelectionBounds,
        direction
      }), rows.length === 0 && noRowsFallback ? noRowsFallback : (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
        children: [(0, import_jsx_runtime.jsx)(RowSelectionChangeProvider, {
          value: selectRowLatest,
          children: getViewportRows()
        }), summaryRows == null ? void 0 : summaryRows.map((row2, rowIdx) => {
          const gridRowStart = headerRowsCount + rows.length + rowIdx + 1;
          const summaryRowIdx = headerRowsCount + rows.length + rowIdx - 1;
          const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;
          const top = clientHeight > totalRowHeight ? gridHeight - summaryRowHeight * (summaryRows.length - rowIdx) : void 0;
          const bottom = top === void 0 ? summaryRowHeight * (summaryRows.length - 1 - rowIdx) : void 0;
          return (0, import_jsx_runtime.jsx)(SummaryRow$1, {
            "aria-rowindex": headerRowsCount + rowsCount + rowIdx + 1,
            rowIdx,
            gridRowStart,
            row: row2,
            top,
            bottom,
            viewportColumns: getRowViewportColumns(summaryRowIdx),
            lastFrozenColumnIndex,
            selectedCellIdx: isSummaryRowSelected ? selectedPosition.idx : void 0,
            selectCell: selectSummaryCellLatest
          }, rowIdx);
        })]
      })]
    })]
  });
}
function isSamePosition(p1, p2) {
  return p1.idx === p2.idx && p1.rowIdx === p2.rowIdx;
}
var DataGrid$1 = (0, import_react.forwardRef)(DataGrid);
var css_248z = ".t16y9g8l700-beta13{appearance:none;background-color:var(--rdg-background-color);block-size:100%;border:2px solid #ccc;box-sizing:border-box;color:var(--rdg-color);font-family:inherit;font-size:var(--rdg-font-size);inline-size:100%;padding-block:0;padding-inline:6px;vertical-align:top}.t16y9g8l700-beta13:focus{border-color:var(--rdg-selection-color);outline:none}.t16y9g8l700-beta13::placeholder{color:#999;opacity:1}";
styleInject(css_248z, { "insertAt": "top" });
var textEditor = "t16y9g8l700-beta13";
var textEditorClassname = `rdg-text-editor ${textEditor}`;
function autoFocusAndSelect(input) {
  input == null ? void 0 : input.focus();
  input == null ? void 0 : input.select();
}
function TextEditor({
  row: row2,
  column,
  onRowChange,
  onClose
}) {
  return (0, import_jsx_runtime.jsx)("input", {
    className: textEditorClassname,
    ref: autoFocusAndSelect,
    value: row2[column.key],
    onChange: (event) => onRowChange({
      ...row2,
      [column.key]: event.target.value
    }),
    onBlur: () => onClose(true)
  });
}
export {
  CheckboxFormatter,
  DataGridDefaultComponentsProvider,
  HeaderRenderer,
  Row$1 as Row,
  SELECT_COLUMN_KEY,
  SelectCellFormatter,
  SelectColumn,
  TextEditor,
  ToggleGroupFormatter,
  ValueFormatter,
  DataGrid$1 as default,
  useRowSelection
};
//# sourceMappingURL=react-data-grid.js.map

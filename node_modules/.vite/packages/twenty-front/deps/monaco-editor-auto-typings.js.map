{"version": 3, "sources": ["../../../../monaco-editor-auto-typings/lib/DummySourceCache.js", "../../../../monaco-editor-auto-typings/lib/UnpkgSourceResolver.js", "../../../../monaco-editor-auto-typings/lib/DependencyParser.js", "../../../../monaco-editor-auto-typings/lib/ImportResourcePath.js", "../../../../monaco-editor-auto-typings/lib/invokeUpdate.js", "../../../../monaco-editor-auto-typings/lib/RecursionDepth.js", "../../../../monaco-editor-auto-typings/lib/ImportResolver.js", "../../../../monaco-editor-auto-typings/lib/AutoTypingsCore.js", "../../../../monaco-editor-auto-typings/lib/AutoTypings.js", "../../../../monaco-editor-auto-typings/lib/LocalStorageCache.js", "../../../../monaco-editor-auto-typings/lib/Options.js", "../../../../monaco-editor-auto-typings/lib/ProgressUpdate.js", "../../../../monaco-editor-auto-typings/lib/SourceCache.js", "../../../../monaco-editor-auto-typings/lib/SourceResolver.js", "../../../../monaco-editor-auto-typings/lib/index.js"], "sourcesContent": ["\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.DummySourceCache = void 0;\r\nvar DummySourceCache = /** @class */ (function () {\r\n    function DummySourceCache() {\r\n    }\r\n    DummySourceCache.prototype.getFile = function (uri) {\r\n        return Promise.resolve(undefined);\r\n    };\r\n    DummySourceCache.prototype.isFileAvailable = function (uri) {\r\n        return Promise.resolve(false);\r\n    };\r\n    DummySourceCache.prototype.storeFile = function (uri, content) {\r\n        return Promise.resolve(undefined);\r\n    };\r\n    DummySourceCache.prototype.clear = function () {\r\n        return Promise.resolve(undefined);\r\n    };\r\n    return DummySourceCache;\r\n}());\r\nexports.DummySourceCache = DummySourceCache;\r\n", "\"use strict\";\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.UnpkgSourceResolver = void 0;\r\nvar UnpkgSourceResolver = /** @class */ (function () {\r\n    function UnpkgSourceResolver() {\r\n    }\r\n    UnpkgSourceResolver.prototype.resolvePackageJson = function (packageName, version, subPath) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, this.resolveFile(\"https://unpkg.com/\".concat(packageName).concat(version ? \"@\".concat(version) : '').concat(subPath ? \"/\".concat(subPath) : '', \"/package.json\"))];\r\n                    case 1: return [2 /*return*/, _a.sent()];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    UnpkgSourceResolver.prototype.resolveSourceFile = function (packageName, version, path) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, this.resolveFile(\"https://unpkg.com/\".concat(packageName).concat(version ? \"@\".concat(version) : '', \"/\").concat(path))];\r\n                    case 1: return [2 /*return*/, _a.sent()];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    UnpkgSourceResolver.prototype.resolveFile = function (url) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var res;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, fetch(url, { method: 'GET' })];\r\n                    case 1:\r\n                        res = _a.sent();\r\n                        if (!res.ok) return [3 /*break*/, 3];\r\n                        return [4 /*yield*/, res.text()];\r\n                    case 2: return [2 /*return*/, _a.sent()];\r\n                    case 3:\r\n                        if (res.status === 404) {\r\n                            return [2 /*return*/, ''];\r\n                        }\r\n                        else {\r\n                            throw Error(\"Error other than 404 while fetching from Unpkg at \".concat(url));\r\n                        }\r\n                        _a.label = 4;\r\n                    case 4: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    return UnpkgSourceResolver;\r\n}());\r\nexports.UnpkgSourceResolver = UnpkgSourceResolver;\r\n", "\"use strict\";\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __read = (this && this.__read) || function (o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n};\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.DependencyParser = void 0;\r\nvar path = __importStar(require(\"path\"));\r\nvar DependencyParser = /** @class */ (function () {\r\n    function DependencyParser() {\r\n        this.REGEX_CLEAN = /[\\n|\\r]/g;\r\n        this.REGEX_DETECT_IMPORT = /(?:(?:(?:import)|(?:export))(?:.)*?from\\s+[\"']([^\"']+)[\"'])|(?:((?:require|import))(?:\\s+)?\\([\"']([^\"']+)[\"']\\))|(?:\\/+\\s+<reference\\s+path=[\"']([^\"']+)[\"']\\s+\\/>)/g;\r\n        this.REGEX_NODE_MODULE = /^node:([\\w\\W\\/]+)$/;\r\n    }\r\n    DependencyParser.prototype.parseDependencies = function (source, parent) {\r\n        var _this = this;\r\n        var cleaned = source; // source.replace(this.REGEX_CLEAN, '');\r\n        return __spreadArray([], __read(cleaned.matchAll(this.REGEX_DETECT_IMPORT)), false).map(function (x) { var _a, _b; return (_b = (_a = x[1]) !== null && _a !== void 0 ? _a : x[2]) !== null && _b !== void 0 ? _b : x[3]; })\r\n            .filter(function (x) { return !!x; })\r\n            .map(function (imp) { return _this.resolvePath(imp, parent); });\r\n    };\r\n    DependencyParser.prototype.resolvePath = function (importPath, parent) {\r\n        var nodeImport = importPath.match(this.REGEX_NODE_MODULE);\r\n        if (nodeImport) {\r\n            return {\r\n                kind: 'relative-in-package',\r\n                packageName: '@types/node',\r\n                importPath: \"\".concat(nodeImport[1], \".d.ts\"),\r\n                sourcePath: '',\r\n            };\r\n        }\r\n        if (typeof parent === 'string') {\r\n            if (importPath.startsWith('.')) {\r\n                return {\r\n                    kind: 'relative',\r\n                    importPath: importPath,\r\n                    sourcePath: parent,\r\n                };\r\n            }\r\n            else if (importPath.startsWith('@')) {\r\n                var segments = importPath.split('/');\r\n                return {\r\n                    kind: 'package',\r\n                    packageName: \"\".concat(segments[0], \"/\").concat(segments[1]),\r\n                    importPath: segments.slice(2).join('/'),\r\n                };\r\n            }\r\n            else {\r\n                var segments = importPath.split('/');\r\n                return {\r\n                    kind: 'package',\r\n                    packageName: segments[0],\r\n                    importPath: segments.slice(1).join('/'),\r\n                };\r\n            }\r\n        }\r\n        else {\r\n            switch (parent.kind) {\r\n                case 'package':\r\n                    throw Error('TODO?');\r\n                case 'relative':\r\n                    throw Error('TODO2?');\r\n                case 'relative-in-package':\r\n                    if (importPath.startsWith('.')) {\r\n                        return {\r\n                            kind: 'relative-in-package',\r\n                            packageName: parent.packageName,\r\n                            sourcePath: path.join(parent.sourcePath, parent.importPath),\r\n                            importPath: importPath,\r\n                        };\r\n                    }\r\n                    else if (importPath.startsWith('@')) {\r\n                        var segments = importPath.split('/');\r\n                        return {\r\n                            kind: 'package',\r\n                            packageName: \"\".concat(segments[0], \"/\").concat(segments[1]),\r\n                            importPath: segments.slice(2).join('/'),\r\n                        };\r\n                    }\r\n                    else {\r\n                        var segments = importPath.split('/');\r\n                        return {\r\n                            kind: 'package',\r\n                            packageName: segments[0],\r\n                            importPath: segments.slice(1).join('/'),\r\n                        };\r\n                    }\r\n            }\r\n        }\r\n    };\r\n    return DependencyParser;\r\n}());\r\nexports.DependencyParser = DependencyParser;\r\n", "\"use strict\";\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.importResourcePathToString = void 0;\r\nvar path_1 = __importDefault(require(\"path\"));\r\nvar importResourcePathToString = function (p) {\r\n    var _a;\r\n    switch (p.kind) {\r\n        case 'package':\r\n            return path_1.default.join(p.packageName, (_a = p.importPath) !== null && _a !== void 0 ? _a : '', 'package.json');\r\n        case 'relative':\r\n            return path_1.default.join(p.sourcePath, p.importPath);\r\n        case 'relative-in-package':\r\n            return path_1.default.join(p.packageName, p.sourcePath, p.importPath);\r\n    }\r\n};\r\nexports.importResourcePathToString = importResourcePathToString;\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.invokeUpdate = void 0;\r\nvar invokeUpdate = function (progress, options) {\r\n    var _a;\r\n    var textual = \"\".concat(progress.type, \": \");\r\n    switch (progress.type) {\r\n        case 'CodeChanged':\r\n            textual += \"\";\r\n            break;\r\n        case 'ResolveNewImports':\r\n            textual += \"\";\r\n            break;\r\n        // case 'DetectedImport':\r\n        //   textual += `at \"${progress.source}\" the import \"${progress.importPath}\" was detected`;\r\n        //   break;\r\n        // case 'CompletedImport':\r\n        //   textual += `at \"${progress.source}\" the import \"${progress.importPath}\" was completed`;\r\n        //   break;\r\n        case 'LookedUpTypeFile':\r\n            textual += \"\\\"\".concat(progress.path, \"\\\" was \").concat(progress.success ? 'sucessfully' : 'not sucessfully', \" looked up\");\r\n            break;\r\n        case 'AttemptedLookUpFile':\r\n            textual += \"\\\"\".concat(progress.path, \"\\\" was \").concat(progress.success ? 'sucessfully' : 'not sucessfully', \" attempted to looked up\");\r\n            break;\r\n        case 'LookedUpPackage':\r\n            textual += \"package.json for package \\\"\".concat(progress.package, \"\\\" was \").concat(progress.success ? 'sucessfully' : 'not sucessfully', \" looked up\").concat(progress.definitelyTyped ? ' (found in definitely typed repo)' : '');\r\n            break;\r\n        case 'LoadedFromCache':\r\n            textual += \"\\\"\".concat(progress.importPath, \"\\\" was loaded from cache\");\r\n            break;\r\n        case 'StoredToCache':\r\n            textual += \"\\\"\".concat(progress.importPath, \"\\\" was stored to cache\");\r\n            break;\r\n    }\r\n    if (textual.endsWith(': ')) {\r\n        textual = textual.slice(undefined, -2);\r\n    }\r\n    (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, progress, textual);\r\n};\r\nexports.invokeUpdate = invokeUpdate;\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.RecursionDepth = void 0;\r\nvar RecursionDepth = /** @class */ (function () {\r\n    function RecursionDepth(options, fileRecursionDepth, packageRecursionDepth) {\r\n        if (fileRecursionDepth === void 0) { fileRecursionDepth = 0; }\r\n        if (packageRecursionDepth === void 0) { packageRecursionDepth = 0; }\r\n        this.options = options;\r\n        this.fileRecursionDepth = fileRecursionDepth;\r\n        this.packageRecursionDepth = packageRecursionDepth;\r\n    }\r\n    RecursionDepth.prototype.nextPackage = function () {\r\n        return new RecursionDepth(this.options, this.fileRecursionDepth, this.packageRecursionDepth + 1);\r\n    };\r\n    RecursionDepth.prototype.nextFile = function () {\r\n        return new RecursionDepth(this.options, this.fileRecursionDepth + 1, this.packageRecursionDepth);\r\n    };\r\n    RecursionDepth.prototype.same = function () {\r\n        return new RecursionDepth(this.options, this.fileRecursionDepth, this.packageRecursionDepth);\r\n    };\r\n    RecursionDepth.prototype.shouldStop = function () {\r\n        return ((this.options.fileRecursionDepth > 0 && this.fileRecursionDepth >= this.options.fileRecursionDepth) ||\r\n            (this.options.packageRecursionDepth > 0 && this.packageRecursionDepth >= this.options.packageRecursionDepth));\r\n    };\r\n    return RecursionDepth;\r\n}());\r\nexports.RecursionDepth = RecursionDepth;\r\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __values = (this && this.__values) || function(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n};\r\nvar __read = (this && this.__read) || function (o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.ImportResolver = void 0;\r\nvar DependencyParser_1 = require(\"./DependencyParser\");\r\nvar ImportResourcePath_1 = require(\"./ImportResourcePath\");\r\nvar path = __importStar(require(\"path\"));\r\nvar invokeUpdate_1 = require(\"./invokeUpdate\");\r\nvar RecursionDepth_1 = require(\"./RecursionDepth\");\r\nvar ImportResolver = /** @class */ (function () {\r\n    function ImportResolver(options) {\r\n        var e_1, _a;\r\n        this.options = options;\r\n        this.loadedFiles = [];\r\n        this.dependencyParser = new DependencyParser_1.DependencyParser();\r\n        this.cache = options.sourceCache;\r\n        this.sourceResolver = options.sourceResolver;\r\n        this.newImportsResolved = false;\r\n        this.monaco = options.monaco;\r\n        if (options.preloadPackages && options.versions) {\r\n            this.versions = options.versions;\r\n            try {\r\n                for (var _b = __values(Object.entries(options.versions)), _c = _b.next(); !_c.done; _c = _b.next()) {\r\n                    var _d = __read(_c.value, 2), packageName = _d[0], version = _d[1];\r\n                    this.resolveImport({\r\n                        kind: 'package',\r\n                        packageName: packageName,\r\n                        importPath: '',\r\n                    }, new RecursionDepth_1.RecursionDepth(this.options)).catch(function (e) {\r\n                        console.error(e);\r\n                    });\r\n                }\r\n            }\r\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\r\n            finally {\r\n                try {\r\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\r\n                }\r\n                finally { if (e_1) throw e_1.error; }\r\n            }\r\n        }\r\n    }\r\n    ImportResolver.prototype.wereNewImportsResolved = function () {\r\n        return this.newImportsResolved;\r\n    };\r\n    ImportResolver.prototype.resetNewImportsResolved = function () {\r\n        this.newImportsResolved = false;\r\n    };\r\n    ImportResolver.prototype.resolveImportsInFile = function (source, parent, depth) {\r\n        var _a, _b, _c;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var imports, imports_1, imports_1_1, importCall, e_2, e_3_1;\r\n            var e_3, _d;\r\n            return __generator(this, function (_e) {\r\n                switch (_e.label) {\r\n                    case 0:\r\n                        if (depth.shouldStop()) {\r\n                            return [2 /*return*/];\r\n                        }\r\n                        imports = this.dependencyParser.parseDependencies(source, parent);\r\n                        _e.label = 1;\r\n                    case 1:\r\n                        _e.trys.push([1, 8, 9, 10]);\r\n                        imports_1 = __values(imports), imports_1_1 = imports_1.next();\r\n                        _e.label = 2;\r\n                    case 2:\r\n                        if (!!imports_1_1.done) return [3 /*break*/, 7];\r\n                        importCall = imports_1_1.value;\r\n                        _e.label = 3;\r\n                    case 3:\r\n                        _e.trys.push([3, 5, , 6]);\r\n                        return [4 /*yield*/, this.resolveImport(importCall, depth)];\r\n                    case 4:\r\n                        _e.sent();\r\n                        return [3 /*break*/, 6];\r\n                    case 5:\r\n                        e_2 = _e.sent();\r\n                        if (this.options.onError) {\r\n                            (_b = (_a = this.options).onError) === null || _b === void 0 ? void 0 : _b.call(_a, (_c = e_2.message) !== null && _c !== void 0 ? _c : e_2);\r\n                        }\r\n                        else {\r\n                            console.error(e_2);\r\n                        }\r\n                        return [3 /*break*/, 6];\r\n                    case 6:\r\n                        imports_1_1 = imports_1.next();\r\n                        return [3 /*break*/, 2];\r\n                    case 7: return [3 /*break*/, 10];\r\n                    case 8:\r\n                        e_3_1 = _e.sent();\r\n                        e_3 = { error: e_3_1 };\r\n                        return [3 /*break*/, 10];\r\n                    case 9:\r\n                        try {\r\n                            if (imports_1_1 && !imports_1_1.done && (_d = imports_1.return)) _d.call(imports_1);\r\n                        }\r\n                        finally { if (e_3) throw e_3.error; }\r\n                        return [7 /*endfinally*/];\r\n                    case 10: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.resolveImport = function (importResource, depth) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var hash, _a, packageRelativeImport;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        hash = this.hashImportResourcePath(importResource);\r\n                        if (this.loadedFiles.includes(hash)) {\r\n                            return [2 /*return*/];\r\n                        }\r\n                        this.loadedFiles.push(hash);\r\n                        _a = importResource.kind;\r\n                        switch (_a) {\r\n                            case 'package': return [3 /*break*/, 1];\r\n                            case 'relative': return [3 /*break*/, 5];\r\n                            case 'relative-in-package': return [3 /*break*/, 6];\r\n                        }\r\n                        return [3 /*break*/, 8];\r\n                    case 1: return [4 /*yield*/, this.resolveImportFromPackageRoot(importResource)];\r\n                    case 2:\r\n                        packageRelativeImport = _b.sent();\r\n                        if (!packageRelativeImport) return [3 /*break*/, 4];\r\n                        return [4 /*yield*/, this.resolveImportInPackage(packageRelativeImport, depth.nextPackage().nextFile())];\r\n                    case 3: return [2 /*return*/, _b.sent()];\r\n                    case 4: return [3 /*break*/, 8];\r\n                    case 5: throw Error('Not implemented yet');\r\n                    case 6: return [4 /*yield*/, this.resolveImportInPackage(importResource, depth.nextFile())];\r\n                    case 7: return [2 /*return*/, _b.sent()];\r\n                    case 8: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.resolveImportInPackage = function (importResource, depth) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var contents, source, at;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, this.loadSourceFileContents(importResource)];\r\n                    case 1:\r\n                        contents = _a.sent();\r\n                        if (!contents) return [3 /*break*/, 3];\r\n                        source = contents.source, at = contents.at;\r\n                        this.createModel(source, this.monaco.Uri.parse(this.options.fileRootPath + path.join(\"node_modules/\".concat(importResource.packageName), at)));\r\n                        return [4 /*yield*/, this.resolveImportsInFile(source, {\r\n                                kind: 'relative-in-package',\r\n                                packageName: importResource.packageName,\r\n                                sourcePath: path.dirname(at),\r\n                                importPath: '',\r\n                            }, depth)];\r\n                    case 2:\r\n                        _a.sent();\r\n                        _a.label = 3;\r\n                    case 3: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.resolveImportFromPackageRoot = function (importResource) {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var failedProgressUpdate, doesPkgJsonHasSubpath, pkgJsonSubpath, pkgJson, pkg, typings, typingPackageName, pkgJsonTypings, pkg_1, typings;\r\n            return __generator(this, function (_k) {\r\n                switch (_k.label) {\r\n                    case 0:\r\n                        failedProgressUpdate = {\r\n                            type: 'LookedUpPackage',\r\n                            package: importResource.packageName,\r\n                            definitelyTyped: false,\r\n                            success: false,\r\n                        };\r\n                        if (this.options.onlySpecifiedPackages) {\r\n                            if (!((_a = this.versions) === null || _a === void 0 ? void 0 : _a[importResource.packageName]) && !((_b = this.versions) === null || _b === void 0 ? void 0 : _b['@types/' + importResource.packageName])) {\r\n                                (0, invokeUpdate_1.invokeUpdate)(failedProgressUpdate, this.options);\r\n                                return [2 /*return*/];\r\n                            }\r\n                        }\r\n                        doesPkgJsonHasSubpath = (_d = (_c = importResource.importPath) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0 > 0;\r\n                        pkgJsonSubpath = doesPkgJsonHasSubpath ? \"/\".concat(importResource.importPath) : '';\r\n                        return [4 /*yield*/, this.resolvePackageJson(importResource.packageName, (_e = this.versions) === null || _e === void 0 ? void 0 : _e[importResource.packageName], doesPkgJsonHasSubpath ? importResource.importPath : undefined)];\r\n                    case 1:\r\n                        pkgJson = _k.sent();\r\n                        if (!(!pkgJson && doesPkgJsonHasSubpath)) return [3 /*break*/, 3];\r\n                        return [4 /*yield*/, this.resolvePackageJson(importResource.packageName, (_f = this.versions) === null || _f === void 0 ? void 0 : _f[importResource.packageName])];\r\n                    case 2:\r\n                        pkgJson = _k.sent();\r\n                        pkgJsonSubpath = '';\r\n                        _k.label = 3;\r\n                    case 3:\r\n                        if (!pkgJson) return [3 /*break*/, 7];\r\n                        pkg = JSON.parse(pkgJson);\r\n                        if (!(pkg.typings || pkg.types)) return [3 /*break*/, 4];\r\n                        typings = pkg.typings || pkg.types;\r\n                        this.createModel(pkgJson, this.monaco.Uri.parse(\"\".concat(this.options.fileRootPath, \"node_modules/\").concat(importResource.packageName).concat(pkgJsonSubpath, \"/package.json\")));\r\n                        (0, invokeUpdate_1.invokeUpdate)({\r\n                            type: 'LookedUpPackage',\r\n                            package: importResource.packageName,\r\n                            definitelyTyped: false,\r\n                            success: true,\r\n                        }, this.options);\r\n                        this.setVersion(importResource.packageName, pkg.version);\r\n                        return [2 /*return*/, {\r\n                                kind: 'relative-in-package',\r\n                                packageName: importResource.packageName,\r\n                                sourcePath: '',\r\n                                importPath: path.join((_g = importResource.importPath) !== null && _g !== void 0 ? _g : '', typings.startsWith('./') ? typings.slice(2) : typings),\r\n                            }];\r\n                    case 4:\r\n                        typingPackageName = \"@types/\".concat(importResource.packageName.startsWith('@')\r\n                            ? importResource.packageName.slice(1).replace(/\\//, '__')\r\n                            : importResource.packageName);\r\n                        return [4 /*yield*/, this.resolvePackageJson(typingPackageName, (_h = this.versions) === null || _h === void 0 ? void 0 : _h[typingPackageName])];\r\n                    case 5:\r\n                        pkgJsonTypings = _k.sent();\r\n                        if (pkgJsonTypings) {\r\n                            pkg_1 = JSON.parse(pkgJsonTypings);\r\n                            if (pkg_1.typings || pkg_1.types) {\r\n                                typings = pkg_1.typings || pkg_1.types;\r\n                                this.createModel(pkgJsonTypings, this.monaco.Uri.parse(\"\".concat(this.options.fileRootPath, \"node_modules/\").concat(typingPackageName, \"/package.json\")));\r\n                                (0, invokeUpdate_1.invokeUpdate)({\r\n                                    type: 'LookedUpPackage',\r\n                                    package: typingPackageName,\r\n                                    definitelyTyped: true,\r\n                                    success: true,\r\n                                }, this.options);\r\n                                this.setVersion(typingPackageName, pkg_1.version);\r\n                                return [2 /*return*/, {\r\n                                        kind: 'relative-in-package',\r\n                                        packageName: typingPackageName,\r\n                                        sourcePath: '',\r\n                                        importPath: path.join((_j = importResource.importPath) !== null && _j !== void 0 ? _j : '', typings.startsWith('./') ? typings.slice(2) : typings),\r\n                                    }];\r\n                            }\r\n                            else {\r\n                                (0, invokeUpdate_1.invokeUpdate)(failedProgressUpdate, this.options);\r\n                            }\r\n                        }\r\n                        else {\r\n                            (0, invokeUpdate_1.invokeUpdate)(failedProgressUpdate, this.options);\r\n                        }\r\n                        _k.label = 6;\r\n                    case 6: return [3 /*break*/, 8];\r\n                    case 7:\r\n                        (0, invokeUpdate_1.invokeUpdate)(failedProgressUpdate, this.options);\r\n                        _k.label = 8;\r\n                    case 8: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.loadSourceFileContents = function (importResource) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var progressUpdatePath, failedProgressUpdate, pkgName, version, appends, source, appends_1, appends_1_1, append, resourcePath, fullPath, source, e_4_1, pkgJson, types, fullPath, source;\r\n            var e_4, _a;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        progressUpdatePath = path.join(importResource.packageName, importResource.sourcePath, importResource.importPath);\r\n                        failedProgressUpdate = {\r\n                            type: 'LookedUpTypeFile',\r\n                            path: progressUpdatePath,\r\n                            definitelyTyped: false,\r\n                            success: false,\r\n                        };\r\n                        pkgName = importResource.packageName;\r\n                        version = this.getVersion(importResource.packageName);\r\n                        appends = ['.d.ts', '/index.d.ts', '.ts', '.tsx', '/index.ts', '/index.tsx'];\r\n                        if (!appends.map(function (append) { return importResource.importPath.endsWith(append); }).reduce(function (a, b) { return a || b; }, false)) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.resolveSourceFile(pkgName, version, path.join(importResource.sourcePath, importResource.importPath))];\r\n                    case 1:\r\n                        source = _b.sent();\r\n                        if (source) {\r\n                            return [2 /*return*/, { source: source, at: path.join(importResource.sourcePath, importResource.importPath) }];\r\n                        }\r\n                        return [3 /*break*/, 9];\r\n                    case 2:\r\n                        _b.trys.push([2, 7, 8, 9]);\r\n                        appends_1 = __values(appends), appends_1_1 = appends_1.next();\r\n                        _b.label = 3;\r\n                    case 3:\r\n                        if (!!appends_1_1.done) return [3 /*break*/, 6];\r\n                        append = appends_1_1.value;\r\n                        resourcePath = path.join(importResource.sourcePath, importResource.importPath);\r\n                        fullPath = (append === '.d.ts' && resourcePath.endsWith('.js') ? resourcePath.slice(0, -3) : resourcePath) + append;\r\n                        return [4 /*yield*/, this.resolveSourceFile(pkgName, version, fullPath)];\r\n                    case 4:\r\n                        source = _b.sent();\r\n                        (0, invokeUpdate_1.invokeUpdate)({\r\n                            type: 'AttemptedLookUpFile',\r\n                            path: path.join(pkgName, fullPath),\r\n                            success: !!source,\r\n                        }, this.options);\r\n                        if (source) {\r\n                            (0, invokeUpdate_1.invokeUpdate)({\r\n                                type: 'LookedUpTypeFile',\r\n                                path: path.join(pkgName, fullPath),\r\n                                success: true,\r\n                            }, this.options);\r\n                            return [2 /*return*/, { source: source, at: fullPath }];\r\n                        }\r\n                        _b.label = 5;\r\n                    case 5:\r\n                        appends_1_1 = appends_1.next();\r\n                        return [3 /*break*/, 3];\r\n                    case 6: return [3 /*break*/, 9];\r\n                    case 7:\r\n                        e_4_1 = _b.sent();\r\n                        e_4 = { error: e_4_1 };\r\n                        return [3 /*break*/, 9];\r\n                    case 8:\r\n                        try {\r\n                            if (appends_1_1 && !appends_1_1.done && (_a = appends_1.return)) _a.call(appends_1);\r\n                        }\r\n                        finally { if (e_4) throw e_4.error; }\r\n                        return [7 /*endfinally*/];\r\n                    case 9: return [4 /*yield*/, this.resolvePackageJson(pkgName, version, path.join(importResource.sourcePath, importResource.importPath))];\r\n                    case 10:\r\n                        pkgJson = _b.sent();\r\n                        if (!pkgJson) return [3 /*break*/, 12];\r\n                        types = JSON.parse(pkgJson).types;\r\n                        if (!types) return [3 /*break*/, 12];\r\n                        fullPath = path.join(importResource.sourcePath, importResource.importPath, types);\r\n                        return [4 /*yield*/, this.resolveSourceFile(pkgName, version, fullPath)];\r\n                    case 11:\r\n                        source = _b.sent();\r\n                        if (source) {\r\n                            (0, invokeUpdate_1.invokeUpdate)({\r\n                                type: 'LookedUpTypeFile',\r\n                                path: path.join(pkgName, fullPath),\r\n                                success: true,\r\n                            }, this.options);\r\n                            return [2 /*return*/, { source: source, at: fullPath }];\r\n                        }\r\n                        _b.label = 12;\r\n                    case 12:\r\n                        (0, invokeUpdate_1.invokeUpdate)(failedProgressUpdate, this.options);\r\n                        return [2 /*return*/, null];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.getVersion = function (packageName) {\r\n        var _a;\r\n        return (_a = this.versions) === null || _a === void 0 ? void 0 : _a[packageName];\r\n    };\r\n    ImportResolver.prototype.setVersions = function (versions) {\r\n        var _a, _b;\r\n        this.versions = versions;\r\n        (_b = (_a = this.options).onUpdateVersions) === null || _b === void 0 ? void 0 : _b.call(_a, versions);\r\n        // TODO reload packages whose version has changed\r\n    };\r\n    ImportResolver.prototype.setVersion = function (packageName, version) {\r\n        var _a;\r\n        this.setVersions(__assign(__assign({}, this.versions), (_a = {}, _a[packageName] = version, _a)));\r\n    };\r\n    ImportResolver.prototype.createModel = function (source, uri) {\r\n        uri = uri.with({ path: uri.path.replace('@types/', '') });\r\n        if (!this.monaco.editor.getModel(uri)) {\r\n            this.monaco.editor.createModel(source, 'typescript', uri);\r\n            this.newImportsResolved = true;\r\n        }\r\n    };\r\n    ImportResolver.prototype.hashImportResourcePath = function (p) {\r\n        return (0, ImportResourcePath_1.importResourcePathToString)(p);\r\n    };\r\n    ImportResolver.prototype.resolvePackageJson = function (packageName, version, subPath) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var uri, isAvailable, content, _a;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        uri = path.join(packageName + (version ? \"@\".concat(version) : ''), subPath !== null && subPath !== void 0 ? subPath : '', 'package.json');\r\n                        isAvailable = false;\r\n                        content = undefined;\r\n                        if (!this.cache.isFileAvailable) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.cache.isFileAvailable(uri)];\r\n                    case 1:\r\n                        isAvailable = _b.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 2: return [4 /*yield*/, this.cache.getFile(uri)];\r\n                    case 3:\r\n                        content = _b.sent();\r\n                        isAvailable = content !== undefined;\r\n                        _b.label = 4;\r\n                    case 4:\r\n                        if (!isAvailable) return [3 /*break*/, 8];\r\n                        if (!(content !== null && content !== void 0)) return [3 /*break*/, 5];\r\n                        _a = content;\r\n                        return [3 /*break*/, 7];\r\n                    case 5: return [4 /*yield*/, this.cache.getFile(uri)];\r\n                    case 6:\r\n                        _a = (_b.sent());\r\n                        _b.label = 7;\r\n                    case 7: return [2 /*return*/, _a];\r\n                    case 8: return [4 /*yield*/, this.sourceResolver.resolvePackageJson(packageName, version, subPath)];\r\n                    case 9:\r\n                        content = _b.sent();\r\n                        if (content) {\r\n                            this.cache.storeFile(uri, content);\r\n                        }\r\n                        return [2 /*return*/, content];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ImportResolver.prototype.resolveSourceFile = function (packageName, version, filePath) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var uri, isAvailable, content, _a;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        uri = path.join(packageName + (version ? \"@\".concat(version) : ''), filePath);\r\n                        isAvailable = false;\r\n                        content = undefined;\r\n                        if (!this.cache.isFileAvailable) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.cache.isFileAvailable(uri)];\r\n                    case 1:\r\n                        isAvailable = _b.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 2: return [4 /*yield*/, this.cache.getFile(uri)];\r\n                    case 3:\r\n                        content = _b.sent();\r\n                        isAvailable = content !== undefined;\r\n                        _b.label = 4;\r\n                    case 4:\r\n                        if (!isAvailable) return [3 /*break*/, 8];\r\n                        (0, invokeUpdate_1.invokeUpdate)({\r\n                            type: 'LoadedFromCache',\r\n                            importPath: uri,\r\n                        }, this.options);\r\n                        if (!(content !== null && content !== void 0)) return [3 /*break*/, 5];\r\n                        _a = content;\r\n                        return [3 /*break*/, 7];\r\n                    case 5: return [4 /*yield*/, this.cache.getFile(uri)];\r\n                    case 6:\r\n                        _a = (_b.sent());\r\n                        _b.label = 7;\r\n                    case 7: return [2 /*return*/, _a];\r\n                    case 8: return [4 /*yield*/, this.sourceResolver.resolveSourceFile(packageName, version, filePath)];\r\n                    case 9:\r\n                        content = _b.sent();\r\n                        if (content) {\r\n                            (0, invokeUpdate_1.invokeUpdate)({\r\n                                type: 'StoredToCache',\r\n                                importPath: uri,\r\n                            }, this.options);\r\n                            this.cache.storeFile(uri, content);\r\n                        }\r\n                        return [2 /*return*/, content];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    return ImportResolver;\r\n}());\r\nexports.ImportResolver = ImportResolver;\r\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __values = (this && this.__values) || function(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.AutoTypingsCore = void 0;\r\nvar DummySourceCache_1 = require(\"./DummySourceCache\");\r\nvar UnpkgSourceResolver_1 = require(\"./UnpkgSourceResolver\");\r\nvar ImportResolver_1 = require(\"./ImportResolver\");\r\nvar path = __importStar(require(\"path\"));\r\nvar invokeUpdate_1 = require(\"./invokeUpdate\");\r\nvar RecursionDepth_1 = require(\"./RecursionDepth\");\r\nvar AutoTypingsCore = /** @class */ (function () {\r\n    function AutoTypingsCore(editor, options) {\r\n        var _this = this;\r\n        this.editor = editor;\r\n        this.options = options;\r\n        this.disposables = [];\r\n        this.importResolver = new ImportResolver_1.ImportResolver(options);\r\n        var changeModelDisposable = editor.onDidChangeModelContent(function (e) {\r\n            _this.debouncedResolveContents();\r\n        });\r\n        this.disposables.push(changeModelDisposable);\r\n        this.resolveContents();\r\n        if (!options.dontAdaptEditorOptions) {\r\n            options.monaco.languages.typescript.typescriptDefaults.setCompilerOptions(__assign(__assign({}, options.monaco.languages.typescript.typescriptDefaults.getCompilerOptions()), { moduleResolution: options.monaco.languages.typescript.ModuleResolutionKind.NodeJs, allowSyntheticDefaultImports: true, rootDir: options.fileRootPath }));\r\n        }\r\n    }\r\n    AutoTypingsCore.create = function (editor, options) {\r\n        var _a;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var monacoInstance;\r\n            return __generator(this, function (_b) {\r\n                if ((options === null || options === void 0 ? void 0 : options.shareCache) && options.sourceCache && !AutoTypingsCore.sharedCache) {\r\n                    AutoTypingsCore.sharedCache = options.sourceCache;\r\n                }\r\n                monacoInstance = options === null || options === void 0 ? void 0 : options.monaco;\r\n                if (!monacoInstance) {\r\n                    throw new Error('monacoInstance not found, you can specify the monaco instance via options.monaco');\r\n                }\r\n                return [2 /*return*/, new AutoTypingsCore(editor, __assign(__assign({ fileRootPath: 'inmemory://model/', onlySpecifiedPackages: false, preloadPackages: false, shareCache: false, dontAdaptEditorOptions: false, dontRefreshModelValueAfterResolvement: false, sourceCache: (_a = AutoTypingsCore.sharedCache) !== null && _a !== void 0 ? _a : new DummySourceCache_1.DummySourceCache(), sourceResolver: new UnpkgSourceResolver_1.UnpkgSourceResolver(), debounceDuration: 4000, fileRecursionDepth: 10, packageRecursionDepth: 3 }, options), { monaco: monacoInstance }))];\r\n            });\r\n        });\r\n    };\r\n    AutoTypingsCore.prototype.dispose = function () {\r\n        var e_1, _a;\r\n        try {\r\n            for (var _b = __values(this.disposables), _c = _b.next(); !_c.done; _c = _b.next()) {\r\n                var disposable = _c.value;\r\n                disposable.dispose();\r\n            }\r\n        }\r\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\r\n        finally {\r\n            try {\r\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\r\n            }\r\n            finally { if (e_1) throw e_1.error; }\r\n        }\r\n    };\r\n    AutoTypingsCore.prototype.setVersions = function (versions) {\r\n        this.importResolver.setVersions(versions);\r\n        this.options.versions = versions;\r\n    };\r\n    AutoTypingsCore.prototype.clearCache = function () {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, this.options.sourceCache.clear()];\r\n                    case 1:\r\n                        _a.sent();\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    AutoTypingsCore.prototype.debouncedResolveContents = function () {\r\n        var _this = this;\r\n        if (this.isResolving) {\r\n            return;\r\n        }\r\n        (0, invokeUpdate_1.invokeUpdate)({\r\n            type: 'CodeChanged',\r\n        }, this.options);\r\n        if (this.options.debounceDuration <= 0) {\r\n            this.resolveContents();\r\n        }\r\n        else {\r\n            if (this.debounceTimer !== undefined) {\r\n                clearTimeout(this.debounceTimer);\r\n            }\r\n            this.debounceTimer = setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\r\n                return __generator(this, function (_a) {\r\n                    switch (_a.label) {\r\n                        case 0: return [4 /*yield*/, this.resolveContents()];\r\n                        case 1:\r\n                            _a.sent();\r\n                            this.debounceTimer = undefined;\r\n                            return [2 /*return*/];\r\n                    }\r\n                });\r\n            }); }, this.options.debounceDuration);\r\n        }\r\n    };\r\n    AutoTypingsCore.prototype.resolveContents = function () {\r\n        var _a;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var model, content, e_2, currentPosition;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        this.isResolving = true;\r\n                        (0, invokeUpdate_1.invokeUpdate)({\r\n                            type: 'ResolveNewImports',\r\n                        }, this.options);\r\n                        model = this.editor.getModel();\r\n                        if (!model) {\r\n                            throw Error('No model');\r\n                        }\r\n                        content = model.getLinesContent();\r\n                        _b.label = 1;\r\n                    case 1:\r\n                        _b.trys.push([1, 3, , 4]);\r\n                        return [4 /*yield*/, this.importResolver.resolveImportsInFile(content.join('\\n'), path.dirname(model.uri.toString()), new RecursionDepth_1.RecursionDepth(this.options))];\r\n                    case 2:\r\n                        _b.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3:\r\n                        e_2 = _b.sent();\r\n                        if (this.options.onError) {\r\n                            this.options.onError((_a = e_2.message) !== null && _a !== void 0 ? _a : e_2);\r\n                        }\r\n                        else {\r\n                            throw e_2;\r\n                        }\r\n                        return [3 /*break*/, 4];\r\n                    case 4:\r\n                        if (this.importResolver.wereNewImportsResolved()) {\r\n                            if (!this.options.dontRefreshModelValueAfterResolvement) {\r\n                                currentPosition = this.editor.getPosition();\r\n                                model.setValue(model.getValue());\r\n                                if (currentPosition) {\r\n                                    this.editor.setPosition(currentPosition);\r\n                                }\r\n                            }\r\n                            this.importResolver.resetNewImportsResolved();\r\n                        }\r\n                        this.isResolving = false;\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    return AutoTypingsCore;\r\n}());\r\nexports.AutoTypingsCore = AutoTypingsCore;\r\n", "\"use strict\";\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n});\r\nvar __importStar = (this && this.__importStar) || function (mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n};\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.AutoTypings = void 0;\r\nvar AutoTypingsCore_1 = require(\"./AutoTypingsCore\");\r\nvar AutoTypings = /** @class */ (function (_super) {\r\n    __extends(AutoTypings, _super);\r\n    function AutoTypings(editor, options) {\r\n        return _super.call(this, editor, options) || this;\r\n    }\r\n    AutoTypings.create = function (editor, options) {\r\n        var _a;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var _b, _c, _d, _e, _f;\r\n            var _g;\r\n            return __generator(this, function (_h) {\r\n                switch (_h.label) {\r\n                    case 0:\r\n                        _c = (_b = AutoTypingsCore_1.AutoTypingsCore).create;\r\n                        _d = [editor];\r\n                        _e = [__assign({}, options)];\r\n                        _g = {};\r\n                        if (!((_a = options === null || options === void 0 ? void 0 : options.monaco) !== null && _a !== void 0)) return [3 /*break*/, 1];\r\n                        _f = _a;\r\n                        return [3 /*break*/, 3];\r\n                    case 1: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('monaco-editor')); })];\r\n                    case 2:\r\n                        _f = (_h.sent());\r\n                        _h.label = 3;\r\n                    case 3: return [4 /*yield*/, _c.apply(_b, _d.concat([__assign.apply(void 0, _e.concat([(_g.monaco = _f, _g)]))]))];\r\n                    case 4: return [2 /*return*/, _h.sent()];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    return AutoTypings;\r\n}(AutoTypingsCore_1.AutoTypingsCore));\r\nexports.AutoTypings = AutoTypings;\r\n", "\"use strict\";\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __values = (this && this.__values) || function(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.LocalStorageCache = void 0;\r\nvar LocalStorageCache = /** @class */ (function () {\r\n    function LocalStorageCache() {\r\n        this.localCache = {};\r\n    }\r\n    LocalStorageCache.prototype.getFile = function (uri) {\r\n        var _a;\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_b) {\r\n                try {\r\n                    return [2 /*return*/, (_a = localStorage.getItem(LocalStorageCache.LOCALSTORAGE_PREFIX + uri)) !== null && _a !== void 0 ? _a : undefined];\r\n                }\r\n                catch (e) {\r\n                    return [2 /*return*/, this.localCache[uri]];\r\n                }\r\n                return [2 /*return*/];\r\n            });\r\n        });\r\n    };\r\n    LocalStorageCache.prototype.storeFile = function (uri, content) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                this.localCache[uri] = content;\r\n                try {\r\n                    localStorage.setItem(LocalStorageCache.LOCALSTORAGE_PREFIX + uri, content);\r\n                }\r\n                catch (e) {\r\n                    // Ignore\r\n                }\r\n                return [2 /*return*/];\r\n            });\r\n        });\r\n    };\r\n    LocalStorageCache.prototype.clear = function () {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var foundKeys, i, key, foundKeys_1, foundKeys_1_1, key;\r\n            var e_1, _a;\r\n            return __generator(this, function (_b) {\r\n                this.localCache = {};\r\n                foundKeys = [];\r\n                for (i = 0; i < localStorage.length; i++) {\r\n                    key = localStorage.key(i);\r\n                    if (key === null || key === void 0 ? void 0 : key.startsWith(LocalStorageCache.LOCALSTORAGE_PREFIX)) {\r\n                        foundKeys.push(key);\r\n                    }\r\n                }\r\n                try {\r\n                    for (foundKeys_1 = __values(foundKeys), foundKeys_1_1 = foundKeys_1.next(); !foundKeys_1_1.done; foundKeys_1_1 = foundKeys_1.next()) {\r\n                        key = foundKeys_1_1.value;\r\n                        localStorage.removeItem(key);\r\n                    }\r\n                }\r\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\r\n                finally {\r\n                    try {\r\n                        if (foundKeys_1_1 && !foundKeys_1_1.done && (_a = foundKeys_1.return)) _a.call(foundKeys_1);\r\n                    }\r\n                    finally { if (e_1) throw e_1.error; }\r\n                }\r\n                return [2 /*return*/];\r\n            });\r\n        });\r\n    };\r\n    LocalStorageCache.LOCALSTORAGE_PREFIX = '__autotyper_cache_';\r\n    return LocalStorageCache;\r\n}());\r\nexports.LocalStorageCache = LocalStorageCache;\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n", "\"use strict\";\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n", "\"use strict\";\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n__exportStar(require(\"./AutoTypings\"), exports);\r\n__exportStar(require(\"./ImportResourcePath\"), exports);\r\n__exportStar(require(\"./LocalStorageCache\"), exports);\r\n__exportStar(require(\"./Options\"), exports);\r\n__exportStar(require(\"./ProgressUpdate\"), exports);\r\n__exportStar(require(\"./SourceCache\"), exports);\r\n__exportStar(require(\"./SourceResolver\"), exports);\r\n__exportStar(require(\"./UnpkgSourceResolver\"), exports);\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB;AAC3B,QAAI;AAAA;AAAA,MAAkC,WAAY;AAC9C,iBAASA,oBAAmB;AAAA,QAC5B;AACA,QAAAA,kBAAiB,UAAU,UAAU,SAAU,KAAK;AAChD,iBAAO,QAAQ,QAAQ,MAAS;AAAA,QACpC;AACA,QAAAA,kBAAiB,UAAU,kBAAkB,SAAU,KAAK;AACxD,iBAAO,QAAQ,QAAQ,KAAK;AAAA,QAChC;AACA,QAAAA,kBAAiB,UAAU,YAAY,SAAU,KAAK,SAAS;AAC3D,iBAAO,QAAQ,QAAQ,MAAS;AAAA,QACpC;AACA,QAAAA,kBAAiB,UAAU,QAAQ,WAAY;AAC3C,iBAAO,QAAQ,QAAQ,MAAS;AAAA,QACpC;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,mBAAmB;AAAA;AAAA;;;ACpB3B;AAAA;AAAA;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,sBAAsB;AAC9B,QAAI;AAAA;AAAA,MAAqC,WAAY;AACjD,iBAASC,uBAAsB;AAAA,QAC/B;AACA,QAAAA,qBAAoB,UAAU,qBAAqB,SAAU,aAAa,SAAS,SAAS;AACxF,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,YAAY,qBAAqB,OAAO,WAAW,EAAE,OAAO,UAAU,IAAI,OAAO,OAAO,IAAI,EAAE,EAAE,OAAO,UAAU,IAAI,OAAO,OAAO,IAAI,IAAI,eAAe,CAAC,CAAC;AAAA,gBAC9L,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,cAC3C;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,qBAAoB,UAAU,oBAAoB,SAAU,aAAa,SAAS,MAAM;AACpF,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,YAAY,qBAAqB,OAAO,WAAW,EAAE,OAAO,UAAU,IAAI,OAAO,OAAO,IAAI,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,gBACpJ,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,cAC3C;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,qBAAoB,UAAU,cAAc,SAAU,KAAK;AACvD,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI;AACJ,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AAAG,yBAAO,CAAC,GAAa,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,gBAC1D,KAAK;AACD,wBAAM,GAAG,KAAK;AACd,sBAAI,CAAC,IAAI,GAAI,QAAO,CAAC,GAAa,CAAC;AACnC,yBAAO,CAAC,GAAa,IAAI,KAAK,CAAC;AAAA,gBACnC,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,gBACvC,KAAK;AACD,sBAAI,IAAI,WAAW,KAAK;AACpB,2BAAO,CAAC,GAAc,EAAE;AAAA,kBAC5B,OACK;AACD,0BAAM,MAAM,qDAAqD,OAAO,GAAG,CAAC;AAAA,kBAChF;AACA,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,sBAAsB;AAAA;AAAA;;;ACxF9B;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,SAAU,WAAQ,QAAK,UAAW,SAAU,GAAG,GAAG;AAClD,UAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,UAAI,CAAC,EAAG,QAAO;AACf,UAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,UAAI;AACA,gBAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,MAC7E,SACO,OAAO;AAAE,YAAI,EAAE,MAAa;AAAA,MAAG,UACtC;AACI,YAAI;AACA,cAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,QACnD,UACA;AAAU,cAAI,EAAG,OAAM,EAAE;AAAA,QAAO;AAAA,MACpC;AACA,aAAO;AAAA,IACX;AACA,QAAI,gBAAiB,WAAQ,QAAK,iBAAkB,SAAU,IAAI,MAAM,MAAM;AAC1E,UAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,YAAI,MAAM,EAAE,KAAK,OAAO;AACpB,cAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,aAAG,CAAC,IAAI,KAAK,CAAC;AAAA,QAClB;AAAA,MACJ;AACA,aAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAAA,IAC3D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB;AAC3B,QAAI,OAAO,aAAa,yCAAe;AACvC,QAAI;AAAA;AAAA,MAAkC,WAAY;AAC9C,iBAASC,oBAAmB;AACxB,eAAK,cAAc;AACnB,eAAK,sBAAsB;AAC3B,eAAK,oBAAoB;AAAA,QAC7B;AACA,QAAAA,kBAAiB,UAAU,oBAAoB,SAAU,QAAQ,QAAQ;AACrE,cAAI,QAAQ;AACZ,cAAI,UAAU;AACd,iBAAO,cAAc,CAAC,GAAG,OAAO,QAAQ,SAAS,KAAK,mBAAmB,CAAC,GAAG,KAAK,EAAE,IAAI,SAAU,GAAG;AAAE,gBAAI,IAAI;AAAI,oBAAQ,MAAM,KAAK,EAAE,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,EAAE,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,EAAE,CAAC;AAAA,UAAG,CAAC,EACtN,OAAO,SAAU,GAAG;AAAE,mBAAO,CAAC,CAAC;AAAA,UAAG,CAAC,EACnC,IAAI,SAAU,KAAK;AAAE,mBAAO,MAAM,YAAY,KAAK,MAAM;AAAA,UAAG,CAAC;AAAA,QACtE;AACA,QAAAA,kBAAiB,UAAU,cAAc,SAAU,YAAY,QAAQ;AACnE,cAAI,aAAa,WAAW,MAAM,KAAK,iBAAiB;AACxD,cAAI,YAAY;AACZ,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,aAAa;AAAA,cACb,YAAY,GAAG,OAAO,WAAW,CAAC,GAAG,OAAO;AAAA,cAC5C,YAAY;AAAA,YAChB;AAAA,UACJ;AACA,cAAI,OAAO,WAAW,UAAU;AAC5B,gBAAI,WAAW,WAAW,GAAG,GAAG;AAC5B,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN;AAAA,gBACA,YAAY;AAAA,cAChB;AAAA,YACJ,WACS,WAAW,WAAW,GAAG,GAAG;AACjC,kBAAI,WAAW,WAAW,MAAM,GAAG;AACnC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,aAAa,GAAG,OAAO,SAAS,CAAC,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;AAAA,gBAC3D,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,cAC1C;AAAA,YACJ,OACK;AACD,kBAAI,WAAW,WAAW,MAAM,GAAG;AACnC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,aAAa,SAAS,CAAC;AAAA,gBACvB,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,cAC1C;AAAA,YACJ;AAAA,UACJ,OACK;AACD,oBAAQ,OAAO,MAAM;AAAA,cACjB,KAAK;AACD,sBAAM,MAAM,OAAO;AAAA,cACvB,KAAK;AACD,sBAAM,MAAM,QAAQ;AAAA,cACxB,KAAK;AACD,oBAAI,WAAW,WAAW,GAAG,GAAG;AAC5B,yBAAO;AAAA,oBACH,MAAM;AAAA,oBACN,aAAa,OAAO;AAAA,oBACpB,YAAY,KAAK,KAAK,OAAO,YAAY,OAAO,UAAU;AAAA,oBAC1D;AAAA,kBACJ;AAAA,gBACJ,WACS,WAAW,WAAW,GAAG,GAAG;AACjC,sBAAI,WAAW,WAAW,MAAM,GAAG;AACnC,yBAAO;AAAA,oBACH,MAAM;AAAA,oBACN,aAAa,GAAG,OAAO,SAAS,CAAC,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;AAAA,oBAC3D,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,kBAC1C;AAAA,gBACJ,OACK;AACD,sBAAI,WAAW,WAAW,MAAM,GAAG;AACnC,yBAAO;AAAA,oBACH,MAAM;AAAA,oBACN,aAAa,SAAS,CAAC;AAAA,oBACvB,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,kBAC1C;AAAA,gBACJ;AAAA,YACR;AAAA,UACJ;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,mBAAmB;AAAA;AAAA;;;ACxI3B;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,6BAA6B;AACrC,QAAI,SAAS,gBAAgB,yCAAe;AAC5C,QAAI,6BAA6B,SAAU,GAAG;AAC1C,UAAI;AACJ,cAAQ,EAAE,MAAM;AAAA,QACZ,KAAK;AACD,iBAAO,OAAO,QAAQ,KAAK,EAAE,cAAc,KAAK,EAAE,gBAAgB,QAAQ,OAAO,SAAS,KAAK,IAAI,cAAc;AAAA,QACrH,KAAK;AACD,iBAAO,OAAO,QAAQ,KAAK,EAAE,YAAY,EAAE,UAAU;AAAA,QACzD,KAAK;AACD,iBAAO,OAAO,QAAQ,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU;AAAA,MAC5E;AAAA,IACJ;AACA,YAAQ,6BAA6B;AAAA;AAAA;;;AClBrC;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,eAAe;AACvB,QAAI,eAAe,SAAU,UAAU,SAAS;AAC5C,UAAI;AACJ,UAAI,UAAU,GAAG,OAAO,SAAS,MAAM,IAAI;AAC3C,cAAQ,SAAS,MAAM;AAAA,QACnB,KAAK;AACD,qBAAW;AACX;AAAA,QACJ,KAAK;AACD,qBAAW;AACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOJ,KAAK;AACD,qBAAW,IAAK,OAAO,SAAS,MAAM,QAAS,EAAE,OAAO,SAAS,UAAU,gBAAgB,mBAAmB,YAAY;AAC1H;AAAA,QACJ,KAAK;AACD,qBAAW,IAAK,OAAO,SAAS,MAAM,QAAS,EAAE,OAAO,SAAS,UAAU,gBAAgB,mBAAmB,yBAAyB;AACvI;AAAA,QACJ,KAAK;AACD,qBAAW,6BAA8B,OAAO,SAAS,SAAS,QAAS,EAAE,OAAO,SAAS,UAAU,gBAAgB,mBAAmB,YAAY,EAAE,OAAO,SAAS,kBAAkB,sCAAsC,EAAE;AAClO;AAAA,QACJ,KAAK;AACD,qBAAW,IAAK,OAAO,SAAS,YAAY,yBAA0B;AACtE;AAAA,QACJ,KAAK;AACD,qBAAW,IAAK,OAAO,SAAS,YAAY,uBAAwB;AACpE;AAAA,MACR;AACA,UAAI,QAAQ,SAAS,IAAI,GAAG;AACxB,kBAAU,QAAQ,MAAM,QAAW,EAAE;AAAA,MACzC;AACA,OAAC,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,UAAU,OAAO;AAAA,IACnG;AACA,YAAQ,eAAe;AAAA;AAAA;;;ACxCvB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAI;AAAA;AAAA,MAAgC,WAAY;AAC5C,iBAASC,gBAAe,SAAS,oBAAoB,uBAAuB;AACxE,cAAI,uBAAuB,QAAQ;AAAE,iCAAqB;AAAA,UAAG;AAC7D,cAAI,0BAA0B,QAAQ;AAAE,oCAAwB;AAAA,UAAG;AACnE,eAAK,UAAU;AACf,eAAK,qBAAqB;AAC1B,eAAK,wBAAwB;AAAA,QACjC;AACA,QAAAA,gBAAe,UAAU,cAAc,WAAY;AAC/C,iBAAO,IAAIA,gBAAe,KAAK,SAAS,KAAK,oBAAoB,KAAK,wBAAwB,CAAC;AAAA,QACnG;AACA,QAAAA,gBAAe,UAAU,WAAW,WAAY;AAC5C,iBAAO,IAAIA,gBAAe,KAAK,SAAS,KAAK,qBAAqB,GAAG,KAAK,qBAAqB;AAAA,QACnG;AACA,QAAAA,gBAAe,UAAU,OAAO,WAAY;AACxC,iBAAO,IAAIA,gBAAe,KAAK,SAAS,KAAK,oBAAoB,KAAK,qBAAqB;AAAA,QAC/F;AACA,QAAAA,gBAAe,UAAU,aAAa,WAAY;AAC9C,iBAAS,KAAK,QAAQ,qBAAqB,KAAK,KAAK,sBAAsB,KAAK,QAAQ,sBACnF,KAAK,QAAQ,wBAAwB,KAAK,KAAK,yBAAyB,KAAK,QAAQ;AAAA,QAC9F;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,iBAAiB;AAAA;AAAA;;;AC1BzB;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,SAAS,GAAG;AAClD,UAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,UAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,UAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,QAC1C,MAAM,WAAY;AACd,cAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,iBAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,QAC1C;AAAA,MACJ;AACA,YAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AAAA,IACzF;AACA,QAAI,SAAU,WAAQ,QAAK,UAAW,SAAU,GAAG,GAAG;AAClD,UAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,UAAI,CAAC,EAAG,QAAO;AACf,UAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,UAAI;AACA,gBAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,MAC7E,SACO,OAAO;AAAE,YAAI,EAAE,MAAa;AAAA,MAAG,UACtC;AACI,YAAI;AACA,cAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,QACnD,UACA;AAAU,cAAI,EAAG,OAAM,EAAE;AAAA,QAAO;AAAA,MACpC;AACA,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAI,qBAAqB;AACzB,QAAI,uBAAuB;AAC3B,QAAI,OAAO,aAAa,yCAAe;AACvC,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AACvB,QAAI;AAAA;AAAA,MAAgC,WAAY;AAC5C,iBAASC,gBAAe,SAAS;AAC7B,cAAI,KAAK;AACT,eAAK,UAAU;AACf,eAAK,cAAc,CAAC;AACpB,eAAK,mBAAmB,IAAI,mBAAmB,iBAAiB;AAChE,eAAK,QAAQ,QAAQ;AACrB,eAAK,iBAAiB,QAAQ;AAC9B,eAAK,qBAAqB;AAC1B,eAAK,SAAS,QAAQ;AACtB,cAAI,QAAQ,mBAAmB,QAAQ,UAAU;AAC7C,iBAAK,WAAW,QAAQ;AACxB,gBAAI;AACA,uBAAS,KAAK,SAAS,OAAO,QAAQ,QAAQ,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AAChG,oBAAI,KAAK,OAAO,GAAG,OAAO,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AACjE,qBAAK,cAAc;AAAA,kBACf,MAAM;AAAA,kBACN;AAAA,kBACA,YAAY;AAAA,gBAChB,GAAG,IAAI,iBAAiB,eAAe,KAAK,OAAO,CAAC,EAAE,MAAM,SAAU,GAAG;AACrE,0BAAQ,MAAM,CAAC;AAAA,gBACnB,CAAC;AAAA,cACL;AAAA,YACJ,SACO,OAAO;AAAE,oBAAM,EAAE,OAAO,MAAM;AAAA,YAAG,UACxC;AACI,kBAAI;AACA,oBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,QAAS,IAAG,KAAK,EAAE;AAAA,cACtD,UACA;AAAU,oBAAI,IAAK,OAAM,IAAI;AAAA,cAAO;AAAA,YACxC;AAAA,UACJ;AAAA,QACJ;AACA,QAAAA,gBAAe,UAAU,yBAAyB,WAAY;AAC1D,iBAAO,KAAK;AAAA,QAChB;AACA,QAAAA,gBAAe,UAAU,0BAA0B,WAAY;AAC3D,eAAK,qBAAqB;AAAA,QAC9B;AACA,QAAAA,gBAAe,UAAU,uBAAuB,SAAU,QAAQ,QAAQ,OAAO;AAC7E,cAAI,IAAI,IAAI;AACZ,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,SAAS,WAAW,aAAa,YAAY,KAAK;AACtD,gBAAI,KAAK;AACT,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,sBAAI,MAAM,WAAW,GAAG;AACpB,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,kBACxB;AACA,4BAAU,KAAK,iBAAiB,kBAAkB,QAAQ,MAAM;AAChE,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,qBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;AAC1B,8BAAY,SAAS,OAAO,GAAG,cAAc,UAAU,KAAK;AAC5D,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,sBAAI,CAAC,CAAC,YAAY,KAAM,QAAO,CAAC,GAAa,CAAC;AAC9C,+BAAa,YAAY;AACzB,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,qBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,yBAAO,CAAC,GAAa,KAAK,cAAc,YAAY,KAAK,CAAC;AAAA,gBAC9D,KAAK;AACD,qBAAG,KAAK;AACR,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,wBAAM,GAAG,KAAK;AACd,sBAAI,KAAK,QAAQ,SAAS;AACtB,qBAAC,MAAM,KAAK,KAAK,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,GAAG;AAAA,kBAC/I,OACK;AACD,4BAAQ,MAAM,GAAG;AAAA,kBACrB;AACA,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,gCAAc,UAAU,KAAK;AAC7B,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC/B,KAAK;AACD,0BAAQ,GAAG,KAAK;AAChB,wBAAM,EAAE,OAAO,MAAM;AACrB,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC3B,KAAK;AACD,sBAAI;AACA,wBAAI,eAAe,CAAC,YAAY,SAAS,KAAK,UAAU,QAAS,IAAG,KAAK,SAAS;AAAA,kBACtF,UACA;AAAU,wBAAI,IAAK,OAAM,IAAI;AAAA,kBAAO;AACpC,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAgB;AAAA,gBAC5B,KAAK;AAAI,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cACjC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,gBAAe,UAAU,gBAAgB,SAAU,gBAAgB,OAAO;AACtE,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,MAAM,IAAI;AACd,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,yBAAO,KAAK,uBAAuB,cAAc;AACjD,sBAAI,KAAK,YAAY,SAAS,IAAI,GAAG;AACjC,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,kBACxB;AACA,uBAAK,YAAY,KAAK,IAAI;AAC1B,uBAAK,eAAe;AACpB,0BAAQ,IAAI;AAAA,oBACR,KAAK;AAAW,6BAAO,CAAC,GAAa,CAAC;AAAA,oBACtC,KAAK;AAAY,6BAAO,CAAC,GAAa,CAAC;AAAA,oBACvC,KAAK;AAAuB,6BAAO,CAAC,GAAa,CAAC;AAAA,kBACtD;AACA,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,6BAA6B,cAAc,CAAC;AAAA,gBAC9E,KAAK;AACD,0CAAwB,GAAG,KAAK;AAChC,sBAAI,CAAC,sBAAuB,QAAO,CAAC,GAAa,CAAC;AAClD,yBAAO,CAAC,GAAa,KAAK,uBAAuB,uBAAuB,MAAM,YAAY,EAAE,SAAS,CAAC,CAAC;AAAA,gBAC3G,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,gBACvC,KAAK;AAAG,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC9B,KAAK;AAAG,wBAAM,MAAM,qBAAqB;AAAA,gBACzC,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,uBAAuB,gBAAgB,MAAM,SAAS,CAAC,CAAC;AAAA,gBAC1F,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,gBACvC,KAAK;AAAG,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,gBAAe,UAAU,yBAAyB,SAAU,gBAAgB,OAAO;AAC/E,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,UAAU,QAAQ;AACtB,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,uBAAuB,cAAc,CAAC;AAAA,gBACxE,KAAK;AACD,6BAAW,GAAG,KAAK;AACnB,sBAAI,CAAC,SAAU,QAAO,CAAC,GAAa,CAAC;AACrC,2BAAS,SAAS,QAAQ,KAAK,SAAS;AACxC,uBAAK,YAAY,QAAQ,KAAK,OAAO,IAAI,MAAM,KAAK,QAAQ,eAAe,KAAK,KAAK,gBAAgB,OAAO,eAAe,WAAW,GAAG,EAAE,CAAC,CAAC;AAC7I,yBAAO,CAAC,GAAa,KAAK,qBAAqB,QAAQ;AAAA,oBAC/C,MAAM;AAAA,oBACN,aAAa,eAAe;AAAA,oBAC5B,YAAY,KAAK,QAAQ,EAAE;AAAA,oBAC3B,YAAY;AAAA,kBAChB,GAAG,KAAK,CAAC;AAAA,gBACjB,KAAK;AACD,qBAAG,KAAK;AACR,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,gBAAe,UAAU,+BAA+B,SAAU,gBAAgB;AAC9E,cAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,sBAAsB,uBAAuB,gBAAgB,SAAS,KAAK,SAAS,mBAAmB,gBAAgB,OAAO;AAClI,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,yCAAuB;AAAA,oBACnB,MAAM;AAAA,oBACN,SAAS,eAAe;AAAA,oBACxB,iBAAiB;AAAA,oBACjB,SAAS;AAAA,kBACb;AACA,sBAAI,KAAK,QAAQ,uBAAuB;AACpC,wBAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,WAAW,MAAM,GAAG,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,eAAe,WAAW,IAAI;AACxM,uBAAC,GAAG,eAAe,cAAc,sBAAsB,KAAK,OAAO;AACnE,6BAAO;AAAA,wBAAC;AAAA;AAAA,sBAAY;AAAA,oBACxB;AAAA,kBACJ;AACA,2CAAyB,MAAM,KAAK,eAAe,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK,IAAI;AACpJ,mCAAiB,wBAAwB,IAAI,OAAO,eAAe,UAAU,IAAI;AACjF,yBAAO,CAAC,GAAa,KAAK,mBAAmB,eAAe,cAAc,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,WAAW,GAAG,wBAAwB,eAAe,aAAa,MAAS,CAAC;AAAA,gBACrO,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,sBAAI,EAAE,CAAC,WAAW,uBAAwB,QAAO,CAAC,GAAa,CAAC;AAChE,yBAAO,CAAC,GAAa,KAAK,mBAAmB,eAAe,cAAc,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,WAAW,CAAC,CAAC;AAAA,gBACtK,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,mCAAiB;AACjB,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,sBAAI,CAAC,QAAS,QAAO,CAAC,GAAa,CAAC;AACpC,wBAAM,KAAK,MAAM,OAAO;AACxB,sBAAI,EAAE,IAAI,WAAW,IAAI,OAAQ,QAAO,CAAC,GAAa,CAAC;AACvD,4BAAU,IAAI,WAAW,IAAI;AAC7B,uBAAK,YAAY,SAAS,KAAK,OAAO,IAAI,MAAM,GAAG,OAAO,KAAK,QAAQ,cAAc,eAAe,EAAE,OAAO,eAAe,WAAW,EAAE,OAAO,gBAAgB,eAAe,CAAC,CAAC;AACjL,mBAAC,GAAG,eAAe,cAAc;AAAA,oBAC7B,MAAM;AAAA,oBACN,SAAS,eAAe;AAAA,oBACxB,iBAAiB;AAAA,oBACjB,SAAS;AAAA,kBACb,GAAG,KAAK,OAAO;AACf,uBAAK,WAAW,eAAe,aAAa,IAAI,OAAO;AACvD,yBAAO,CAAC,GAAc;AAAA,oBACd,MAAM;AAAA,oBACN,aAAa,eAAe;AAAA,oBAC5B,YAAY;AAAA,oBACZ,YAAY,KAAK,MAAM,KAAK,eAAe,gBAAgB,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ,WAAW,IAAI,IAAI,QAAQ,MAAM,CAAC,IAAI,OAAO;AAAA,kBACrJ,CAAC;AAAA,gBACT,KAAK;AACD,sCAAoB,UAAU,OAAO,eAAe,YAAY,WAAW,GAAG,IACxE,eAAe,YAAY,MAAM,CAAC,EAAE,QAAQ,MAAM,IAAI,IACtD,eAAe,WAAW;AAChC,yBAAO,CAAC,GAAa,KAAK,mBAAmB,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,CAAC,CAAC;AAAA,gBACpJ,KAAK;AACD,mCAAiB,GAAG,KAAK;AACzB,sBAAI,gBAAgB;AAChB,4BAAQ,KAAK,MAAM,cAAc;AACjC,wBAAI,MAAM,WAAW,MAAM,OAAO;AAC9B,gCAAU,MAAM,WAAW,MAAM;AACjC,2BAAK,YAAY,gBAAgB,KAAK,OAAO,IAAI,MAAM,GAAG,OAAO,KAAK,QAAQ,cAAc,eAAe,EAAE,OAAO,mBAAmB,eAAe,CAAC,CAAC;AACxJ,uBAAC,GAAG,eAAe,cAAc;AAAA,wBAC7B,MAAM;AAAA,wBACN,SAAS;AAAA,wBACT,iBAAiB;AAAA,wBACjB,SAAS;AAAA,sBACb,GAAG,KAAK,OAAO;AACf,2BAAK,WAAW,mBAAmB,MAAM,OAAO;AAChD,6BAAO,CAAC,GAAc;AAAA,wBACd,MAAM;AAAA,wBACN,aAAa;AAAA,wBACb,YAAY;AAAA,wBACZ,YAAY,KAAK,MAAM,KAAK,eAAe,gBAAgB,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ,WAAW,IAAI,IAAI,QAAQ,MAAM,CAAC,IAAI,OAAO;AAAA,sBACrJ,CAAC;AAAA,oBACT,OACK;AACD,uBAAC,GAAG,eAAe,cAAc,sBAAsB,KAAK,OAAO;AAAA,oBACvE;AAAA,kBACJ,OACK;AACD,qBAAC,GAAG,eAAe,cAAc,sBAAsB,KAAK,OAAO;AAAA,kBACvE;AACA,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC9B,KAAK;AACD,mBAAC,GAAG,eAAe,cAAc,sBAAsB,KAAK,OAAO;AACnE,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAChC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,gBAAe,UAAU,yBAAyB,SAAU,gBAAgB;AACxE,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,oBAAoB,sBAAsB,SAAS,SAAS,SAAS,QAAQ,WAAW,aAAa,QAAQ,cAAc,UAAU,QAAQ,OAAO,SAAS,OAAO,UAAU;AAClL,gBAAI,KAAK;AACT,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,uCAAqB,KAAK,KAAK,eAAe,aAAa,eAAe,YAAY,eAAe,UAAU;AAC/G,yCAAuB;AAAA,oBACnB,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,iBAAiB;AAAA,oBACjB,SAAS;AAAA,kBACb;AACA,4BAAU,eAAe;AACzB,4BAAU,KAAK,WAAW,eAAe,WAAW;AACpD,4BAAU,CAAC,SAAS,eAAe,OAAO,QAAQ,aAAa,YAAY;AAC3E,sBAAI,CAAC,QAAQ,IAAI,SAAUC,SAAQ;AAAE,2BAAO,eAAe,WAAW,SAASA,OAAM;AAAA,kBAAG,CAAC,EAAE,OAAO,SAAU,GAAG,GAAG;AAAE,2BAAO,KAAK;AAAA,kBAAG,GAAG,KAAK,EAAG,QAAO,CAAC,GAAa,CAAC;AACpK,yBAAO,CAAC,GAAa,KAAK,kBAAkB,SAAS,SAAS,KAAK,KAAK,eAAe,YAAY,eAAe,UAAU,CAAC,CAAC;AAAA,gBAClI,KAAK;AACD,2BAAS,GAAG,KAAK;AACjB,sBAAI,QAAQ;AACR,2BAAO,CAAC,GAAc,EAAE,QAAgB,IAAI,KAAK,KAAK,eAAe,YAAY,eAAe,UAAU,EAAE,CAAC;AAAA,kBACjH;AACA,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,qBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,8BAAY,SAAS,OAAO,GAAG,cAAc,UAAU,KAAK;AAC5D,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,sBAAI,CAAC,CAAC,YAAY,KAAM,QAAO,CAAC,GAAa,CAAC;AAC9C,2BAAS,YAAY;AACrB,iCAAe,KAAK,KAAK,eAAe,YAAY,eAAe,UAAU;AAC7E,8BAAY,WAAW,WAAW,aAAa,SAAS,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,IAAI,gBAAgB;AAC7G,yBAAO,CAAC,GAAa,KAAK,kBAAkB,SAAS,SAAS,QAAQ,CAAC;AAAA,gBAC3E,KAAK;AACD,2BAAS,GAAG,KAAK;AACjB,mBAAC,GAAG,eAAe,cAAc;AAAA,oBAC7B,MAAM;AAAA,oBACN,MAAM,KAAK,KAAK,SAAS,QAAQ;AAAA,oBACjC,SAAS,CAAC,CAAC;AAAA,kBACf,GAAG,KAAK,OAAO;AACf,sBAAI,QAAQ;AACR,qBAAC,GAAG,eAAe,cAAc;AAAA,sBAC7B,MAAM;AAAA,sBACN,MAAM,KAAK,KAAK,SAAS,QAAQ;AAAA,sBACjC,SAAS;AAAA,oBACb,GAAG,KAAK,OAAO;AACf,2BAAO,CAAC,GAAc,EAAE,QAAgB,IAAI,SAAS,CAAC;AAAA,kBAC1D;AACA,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,gCAAc,UAAU,KAAK;AAC7B,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC9B,KAAK;AACD,0BAAQ,GAAG,KAAK;AAChB,wBAAM,EAAE,OAAO,MAAM;AACrB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,sBAAI;AACA,wBAAI,eAAe,CAAC,YAAY,SAAS,KAAK,UAAU,QAAS,IAAG,KAAK,SAAS;AAAA,kBACtF,UACA;AAAU,wBAAI,IAAK,OAAM,IAAI;AAAA,kBAAO;AACpC,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAgB;AAAA,gBAC5B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,mBAAmB,SAAS,SAAS,KAAK,KAAK,eAAe,YAAY,eAAe,UAAU,CAAC,CAAC;AAAA,gBACvI,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,sBAAI,CAAC,QAAS,QAAO,CAAC,GAAa,EAAE;AACrC,0BAAQ,KAAK,MAAM,OAAO,EAAE;AAC5B,sBAAI,CAAC,MAAO,QAAO,CAAC,GAAa,EAAE;AACnC,6BAAW,KAAK,KAAK,eAAe,YAAY,eAAe,YAAY,KAAK;AAChF,yBAAO,CAAC,GAAa,KAAK,kBAAkB,SAAS,SAAS,QAAQ,CAAC;AAAA,gBAC3E,KAAK;AACD,2BAAS,GAAG,KAAK;AACjB,sBAAI,QAAQ;AACR,qBAAC,GAAG,eAAe,cAAc;AAAA,sBAC7B,MAAM;AAAA,sBACN,MAAM,KAAK,KAAK,SAAS,QAAQ;AAAA,sBACjC,SAAS;AAAA,oBACb,GAAG,KAAK,OAAO;AACf,2BAAO,CAAC,GAAc,EAAE,QAAgB,IAAI,SAAS,CAAC;AAAA,kBAC1D;AACA,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,mBAAC,GAAG,eAAe,cAAc,sBAAsB,KAAK,OAAO;AACnE,yBAAO,CAAC,GAAc,IAAI;AAAA,cAClC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAD,gBAAe,UAAU,aAAa,SAAU,aAAa;AACzD,cAAI;AACJ,kBAAQ,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,QACnF;AACA,QAAAA,gBAAe,UAAU,cAAc,SAAU,UAAU;AACvD,cAAI,IAAI;AACR,eAAK,WAAW;AAChB,WAAC,MAAM,KAAK,KAAK,SAAS,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ;AAAA,QAEzG;AACA,QAAAA,gBAAe,UAAU,aAAa,SAAU,aAAa,SAAS;AAClE,cAAI;AACJ,eAAK,YAAY,SAAS,SAAS,CAAC,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,WAAW,IAAI,SAAS,GAAG,CAAC;AAAA,QACpG;AACA,QAAAA,gBAAe,UAAU,cAAc,SAAU,QAAQ,KAAK;AAC1D,gBAAM,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,QAAQ,WAAW,EAAE,EAAE,CAAC;AACxD,cAAI,CAAC,KAAK,OAAO,OAAO,SAAS,GAAG,GAAG;AACnC,iBAAK,OAAO,OAAO,YAAY,QAAQ,cAAc,GAAG;AACxD,iBAAK,qBAAqB;AAAA,UAC9B;AAAA,QACJ;AACA,QAAAA,gBAAe,UAAU,yBAAyB,SAAU,GAAG;AAC3D,kBAAQ,GAAG,qBAAqB,4BAA4B,CAAC;AAAA,QACjE;AACA,QAAAA,gBAAe,UAAU,qBAAqB,SAAU,aAAa,SAAS,SAAS;AACnF,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,KAAK,aAAa,SAAS;AAC/B,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,wBAAM,KAAK,KAAK,eAAe,UAAU,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY,QAAQ,YAAY,SAAS,UAAU,IAAI,cAAc;AACzI,gCAAc;AACd,4BAAU;AACV,sBAAI,CAAC,KAAK,MAAM,gBAAiB,QAAO,CAAC,GAAa,CAAC;AACvD,yBAAO,CAAC,GAAa,KAAK,MAAM,gBAAgB,GAAG,CAAC;AAAA,gBACxD,KAAK;AACD,gCAAc,GAAG,KAAK;AACtB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACpD,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,gCAAc,YAAY;AAC1B,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,sBAAI,CAAC,YAAa,QAAO,CAAC,GAAa,CAAC;AACxC,sBAAI,EAAE,YAAY,QAAQ,YAAY,QAAS,QAAO,CAAC,GAAa,CAAC;AACrE,uBAAK;AACL,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACpD,KAAK;AACD,uBAAM,GAAG,KAAK;AACd,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO,CAAC,GAAc,EAAE;AAAA,gBAChC,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,eAAe,mBAAmB,aAAa,SAAS,OAAO,CAAC;AAAA,gBAClG,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,sBAAI,SAAS;AACT,yBAAK,MAAM,UAAU,KAAK,OAAO;AAAA,kBACrC;AACA,yBAAO,CAAC,GAAc,OAAO;AAAA,cACrC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,gBAAe,UAAU,oBAAoB,SAAU,aAAa,SAAS,UAAU;AACnF,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,KAAK,aAAa,SAAS;AAC/B,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,wBAAM,KAAK,KAAK,eAAe,UAAU,IAAI,OAAO,OAAO,IAAI,KAAK,QAAQ;AAC5E,gCAAc;AACd,4BAAU;AACV,sBAAI,CAAC,KAAK,MAAM,gBAAiB,QAAO,CAAC,GAAa,CAAC;AACvD,yBAAO,CAAC,GAAa,KAAK,MAAM,gBAAgB,GAAG,CAAC;AAAA,gBACxD,KAAK;AACD,gCAAc,GAAG,KAAK;AACtB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACpD,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,gCAAc,YAAY;AAC1B,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,sBAAI,CAAC,YAAa,QAAO,CAAC,GAAa,CAAC;AACxC,mBAAC,GAAG,eAAe,cAAc;AAAA,oBAC7B,MAAM;AAAA,oBACN,YAAY;AAAA,kBAChB,GAAG,KAAK,OAAO;AACf,sBAAI,EAAE,YAAY,QAAQ,YAAY,QAAS,QAAO,CAAC,GAAa,CAAC;AACrE,uBAAK;AACL,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,MAAM,QAAQ,GAAG,CAAC;AAAA,gBACpD,KAAK;AACD,uBAAM,GAAG,KAAK;AACd,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO,CAAC,GAAc,EAAE;AAAA,gBAChC,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,eAAe,kBAAkB,aAAa,SAAS,QAAQ,CAAC;AAAA,gBAClG,KAAK;AACD,4BAAU,GAAG,KAAK;AAClB,sBAAI,SAAS;AACT,qBAAC,GAAG,eAAe,cAAc;AAAA,sBAC7B,MAAM;AAAA,sBACN,YAAY;AAAA,oBAChB,GAAG,KAAK,OAAO;AACf,yBAAK,MAAM,UAAU,KAAK,OAAO;AAAA,kBACrC;AACA,yBAAO,CAAC,GAAc,OAAO;AAAA,cACrC;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,iBAAiB;AAAA;AAAA;;;AC1iBzB;AAAA;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,SAAS,GAAG;AAClD,UAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,UAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,UAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,QAC1C,MAAM,WAAY;AACd,cAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,iBAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,QAC1C;AAAA,MACJ;AACA,YAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AAAA,IACzF;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB;AAC1B,QAAI,qBAAqB;AACzB,QAAI,wBAAwB;AAC5B,QAAI,mBAAmB;AACvB,QAAI,OAAO,aAAa,yCAAe;AACvC,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AACvB,QAAI;AAAA;AAAA,MAAiC,WAAY;AAC7C,iBAASE,iBAAgB,QAAQ,SAAS;AACtC,cAAI,QAAQ;AACZ,eAAK,SAAS;AACd,eAAK,UAAU;AACf,eAAK,cAAc,CAAC;AACpB,eAAK,iBAAiB,IAAI,iBAAiB,eAAe,OAAO;AACjE,cAAI,wBAAwB,OAAO,wBAAwB,SAAU,GAAG;AACpE,kBAAM,yBAAyB;AAAA,UACnC,CAAC;AACD,eAAK,YAAY,KAAK,qBAAqB;AAC3C,eAAK,gBAAgB;AACrB,cAAI,CAAC,QAAQ,wBAAwB;AACjC,oBAAQ,OAAO,UAAU,WAAW,mBAAmB,mBAAmB,SAAS,SAAS,CAAC,GAAG,QAAQ,OAAO,UAAU,WAAW,mBAAmB,mBAAmB,CAAC,GAAG,EAAE,kBAAkB,QAAQ,OAAO,UAAU,WAAW,qBAAqB,QAAQ,8BAA8B,MAAM,SAAS,QAAQ,aAAa,CAAC,CAAC;AAAA,UAC3U;AAAA,QACJ;AACA,QAAAA,iBAAgB,SAAS,SAAU,QAAQ,SAAS;AAChD,cAAI;AACJ,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI;AACJ,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,mBAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe,QAAQ,eAAe,CAACA,iBAAgB,aAAa;AAC/H,gBAAAA,iBAAgB,cAAc,QAAQ;AAAA,cAC1C;AACA,+BAAiB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC3E,kBAAI,CAAC,gBAAgB;AACjB,sBAAM,IAAI,MAAM,kFAAkF;AAAA,cACtG;AACA,qBAAO,CAAC,GAAc,IAAIA,iBAAgB,QAAQ,SAAS,SAAS,EAAE,cAAc,qBAAqB,uBAAuB,OAAO,iBAAiB,OAAO,YAAY,OAAO,wBAAwB,OAAO,uCAAuC,OAAO,cAAc,KAAKA,iBAAgB,iBAAiB,QAAQ,OAAO,SAAS,KAAK,IAAI,mBAAmB,iBAAiB,GAAG,gBAAgB,IAAI,sBAAsB,oBAAoB,GAAG,kBAAkB,KAAM,oBAAoB,IAAI,uBAAuB,EAAE,GAAG,OAAO,GAAG,EAAE,QAAQ,eAAe,CAAC,CAAC,CAAC;AAAA,YACljB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,cAAI,KAAK;AACT,cAAI;AACA,qBAAS,KAAK,SAAS,KAAK,WAAW,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AAChF,kBAAI,aAAa,GAAG;AACpB,yBAAW,QAAQ;AAAA,YACvB;AAAA,UACJ,SACO,OAAO;AAAE,kBAAM,EAAE,OAAO,MAAM;AAAA,UAAG,UACxC;AACI,gBAAI;AACA,kBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,QAAS,IAAG,KAAK,EAAE;AAAA,YACtD,UACA;AAAU,kBAAI,IAAK,OAAM,IAAI;AAAA,YAAO;AAAA,UACxC;AAAA,QACJ;AACA,QAAAA,iBAAgB,UAAU,cAAc,SAAU,UAAU;AACxD,eAAK,eAAe,YAAY,QAAQ;AACxC,eAAK,QAAQ,WAAW;AAAA,QAC5B;AACA,QAAAA,iBAAgB,UAAU,aAAa,WAAY;AAC/C,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AAAG,yBAAO,CAAC,GAAa,KAAK,QAAQ,YAAY,MAAM,CAAC;AAAA,gBAC7D,KAAK;AACD,qBAAG,KAAK;AACR,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAC5B;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,iBAAgB,UAAU,2BAA2B,WAAY;AAC7D,cAAI,QAAQ;AACZ,cAAI,KAAK,aAAa;AAClB;AAAA,UACJ;AACA,WAAC,GAAG,eAAe,cAAc;AAAA,YAC7B,MAAM;AAAA,UACV,GAAG,KAAK,OAAO;AACf,cAAI,KAAK,QAAQ,oBAAoB,GAAG;AACpC,iBAAK,gBAAgB;AAAA,UACzB,OACK;AACD,gBAAI,KAAK,kBAAkB,QAAW;AAClC,2BAAa,KAAK,aAAa;AAAA,YACnC;AACA,iBAAK,gBAAgB,WAAW,WAAY;AAAE,qBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAC9F,uBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,0BAAQ,GAAG,OAAO;AAAA,oBACd,KAAK;AAAG,6BAAO,CAAC,GAAa,KAAK,gBAAgB,CAAC;AAAA,oBACnD,KAAK;AACD,yBAAG,KAAK;AACR,2BAAK,gBAAgB;AACrB,6BAAO;AAAA,wBAAC;AAAA;AAAA,sBAAY;AAAA,kBAC5B;AAAA,gBACJ,CAAC;AAAA,cACL,CAAC;AAAA,YAAG,GAAG,KAAK,QAAQ,gBAAgB;AAAA,UACxC;AAAA,QACJ;AACA,QAAAA,iBAAgB,UAAU,kBAAkB,WAAY;AACpD,cAAI;AACJ,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,OAAO,SAAS,KAAK;AACzB,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,uBAAK,cAAc;AACnB,mBAAC,GAAG,eAAe,cAAc;AAAA,oBAC7B,MAAM;AAAA,kBACV,GAAG,KAAK,OAAO;AACf,0BAAQ,KAAK,OAAO,SAAS;AAC7B,sBAAI,CAAC,OAAO;AACR,0BAAM,MAAM,UAAU;AAAA,kBAC1B;AACA,4BAAU,MAAM,gBAAgB;AAChC,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,qBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,yBAAO,CAAC,GAAa,KAAK,eAAe,qBAAqB,QAAQ,KAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,IAAI,SAAS,CAAC,GAAG,IAAI,iBAAiB,eAAe,KAAK,OAAO,CAAC,CAAC;AAAA,gBAC5K,KAAK;AACD,qBAAG,KAAK;AACR,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,wBAAM,GAAG,KAAK;AACd,sBAAI,KAAK,QAAQ,SAAS;AACtB,yBAAK,QAAQ,SAAS,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,GAAG;AAAA,kBAChF,OACK;AACD,0BAAM;AAAA,kBACV;AACA,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,sBAAI,KAAK,eAAe,uBAAuB,GAAG;AAC9C,wBAAI,CAAC,KAAK,QAAQ,uCAAuC;AACrD,wCAAkB,KAAK,OAAO,YAAY;AAC1C,4BAAM,SAAS,MAAM,SAAS,CAAC;AAC/B,0BAAI,iBAAiB;AACjB,6BAAK,OAAO,YAAY,eAAe;AAAA,sBAC3C;AAAA,oBACJ;AACA,yBAAK,eAAe,wBAAwB;AAAA,kBAChD;AACA,uBAAK,cAAc;AACnB,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAC5B;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,kBAAkB;AAAA;AAAA;;;ACzO1B;AAAA;AAAA;AACA,QAAI,YAAa,WAAQ,QAAK,aAAe,2BAAY;AACrD,UAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;AAAA,QAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,mBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAAG;AACpG,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,SAAU,GAAG,GAAG;AACnB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;AAAA,QAAG;AACtC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAAA,IACJ,EAAG;AACH,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAI,oBAAoB;AACxB,QAAI;AAAA;AAAA,MAA6B,SAAU,QAAQ;AAC/C,kBAAUC,cAAa,MAAM;AAC7B,iBAASA,aAAY,QAAQ,SAAS;AAClC,iBAAO,OAAO,KAAK,MAAM,QAAQ,OAAO,KAAK;AAAA,QACjD;AACA,QAAAA,aAAY,SAAS,SAAU,QAAQ,SAAS;AAC5C,cAAI;AACJ,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,IAAI,IAAI,IAAI,IAAI;AACpB,gBAAI;AACJ,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,wBAAM,KAAK,kBAAkB,iBAAiB;AAC9C,uBAAK,CAAC,MAAM;AACZ,uBAAK,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAC3B,uBAAK,CAAC;AACN,sBAAI,GAAG,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,QAAS,QAAO,CAAC,GAAa,CAAC;AAChI,uBAAK;AACL,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,QAAQ,QAAQ,EAAE,KAAK,WAAY;AAAE,2BAAO,aAAa,uDAAwB;AAAA,kBAAG,CAAC,CAAC;AAAA,gBACnH,KAAK;AACD,uBAAM,GAAG,KAAK;AACd,qBAAG,QAAQ;AAAA,gBACf,KAAK;AAAG,yBAAO,CAAC,GAAa,GAAG,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,MAAM,QAAQ,GAAG,OAAO,EAAE,GAAG,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,gBACjH,KAAK;AAAG,yBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,cAC3C;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,eAAOA;AAAA,MACX,EAAE,kBAAkB,eAAe;AAAA;AACnC,YAAQ,cAAc;AAAA;AAAA;;;ACzHtB;AAAA;AAAA;AACA,QAAI,YAAa,WAAQ,QAAK,aAAc,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,eAAS,MAAM,OAAO;AAAE,eAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,kBAAQ,KAAK;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3G,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC1F,iBAAS,SAAS,OAAO;AAAE,cAAI;AAAE,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAAG,SAAS,GAAG;AAAE,mBAAO,CAAC;AAAA,UAAG;AAAA,QAAE;AAC7F,iBAAS,KAAK,QAAQ;AAAE,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QAAG;AAC7G,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACA,QAAI,cAAe,WAAQ,QAAK,eAAgB,SAAU,SAAS,MAAM;AACrE,UAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,YAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,eAAO,EAAE,CAAC;AAAA,MAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,aAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,eAAO;AAAA,MAAM,IAAI;AACvJ,eAAS,KAAK,GAAG;AAAE,eAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG;AAAA,MAAG;AACjE,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,cAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,cAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,kBAAQ,GAAG,CAAC,GAAG;AAAA,YACX,KAAK;AAAA,YAAG,KAAK;AAAG,kBAAI;AAAI;AAAA,YACxB,KAAK;AAAG,gBAAE;AAAS,qBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,YACtD,KAAK;AAAG,gBAAE;AAAS,kBAAI,GAAG,CAAC;AAAG,mBAAK,CAAC,CAAC;AAAG;AAAA,YACxC,KAAK;AAAG,mBAAK,EAAE,IAAI,IAAI;AAAG,gBAAE,KAAK,IAAI;AAAG;AAAA,YACxC;AACI,kBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,oBAAI;AAAG;AAAA,cAAU;AAC3G,kBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,kBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,cAAO;AACrF,kBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,oBAAI;AAAI;AAAA,cAAO;AACpE,kBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,kBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,cAAO;AAClE,kBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,gBAAE,KAAK,IAAI;AAAG;AAAA,UACtB;AACA,eAAK,KAAK,KAAK,SAAS,CAAC;AAAA,QAC7B,SAAS,GAAG;AAAE,eAAK,CAAC,GAAG,CAAC;AAAG,cAAI;AAAA,QAAG,UAAE;AAAU,cAAI,IAAI;AAAA,QAAG;AACzD,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACnF;AAAA,IACJ;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,SAAS,GAAG;AAClD,UAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,UAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,UAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,QAC1C,MAAM,WAAY;AACd,cAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,iBAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,QAC1C;AAAA,MACJ;AACA,YAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AAAA,IACzF;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB;AAC5B,QAAI;AAAA;AAAA,MAAmC,WAAY;AAC/C,iBAASC,qBAAoB;AACzB,eAAK,aAAa,CAAC;AAAA,QACvB;AACA,QAAAA,mBAAkB,UAAU,UAAU,SAAU,KAAK;AACjD,cAAI;AACJ,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAI;AACA,uBAAO,CAAC,IAAe,KAAK,aAAa,QAAQA,mBAAkB,sBAAsB,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK,MAAS;AAAA,cAC7I,SACO,GAAG;AACN,uBAAO,CAAC,GAAc,KAAK,WAAW,GAAG,CAAC;AAAA,cAC9C;AACA,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,mBAAkB,UAAU,YAAY,SAAU,KAAK,SAAS;AAC5D,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,mBAAK,WAAW,GAAG,IAAI;AACvB,kBAAI;AACA,6BAAa,QAAQA,mBAAkB,sBAAsB,KAAK,OAAO;AAAA,cAC7E,SACO,GAAG;AAAA,cAEV;AACA,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,mBAAkB,UAAU,QAAQ,WAAY;AAC5C,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,WAAW,GAAG,KAAK,aAAa,eAAe;AACnD,gBAAI,KAAK;AACT,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,mBAAK,aAAa,CAAC;AACnB,0BAAY,CAAC;AACb,mBAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACtC,sBAAM,aAAa,IAAI,CAAC;AACxB,oBAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,WAAWA,mBAAkB,mBAAmB,GAAG;AACjG,4BAAU,KAAK,GAAG;AAAA,gBACtB;AAAA,cACJ;AACA,kBAAI;AACA,qBAAK,cAAc,SAAS,SAAS,GAAG,gBAAgB,YAAY,KAAK,GAAG,CAAC,cAAc,MAAM,gBAAgB,YAAY,KAAK,GAAG;AACjI,wBAAM,cAAc;AACpB,+BAAa,WAAW,GAAG;AAAA,gBAC/B;AAAA,cACJ,SACO,OAAO;AAAE,sBAAM,EAAE,OAAO,MAAM;AAAA,cAAG,UACxC;AACI,oBAAI;AACA,sBAAI,iBAAiB,CAAC,cAAc,SAAS,KAAK,YAAY,QAAS,IAAG,KAAK,WAAW;AAAA,gBAC9F,UACA;AAAU,sBAAI,IAAK,OAAM,IAAI;AAAA,gBAAO;AAAA,cACxC;AACA,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,mBAAkB,sBAAsB;AACxC,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,oBAAoB;AAAA;AAAA;;;ACnH5B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA;AAAA;;;ACD5D;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,iBAAa,uBAA0B,OAAO;AAC9C,iBAAa,8BAAiC,OAAO;AACrD,iBAAa,6BAAgC,OAAO;AACpD,iBAAa,mBAAsB,OAAO;AAC1C,iBAAa,0BAA6B,OAAO;AACjD,iBAAa,uBAA0B,OAAO;AAC9C,iBAAa,0BAA6B,OAAO;AACjD,iBAAa,+BAAkC,OAAO;AAAA;AAAA;", "names": ["Dummy<PERSON>ource<PERSON><PERSON>", "UnpkgSourceResolver", "Dependency<PERSON><PERSON><PERSON>", "RecursionDepth", "ImportResolver", "append", "AutoTypingsCore", "d", "b", "AutoTypings", "LocalStorageCache", "exports"]}
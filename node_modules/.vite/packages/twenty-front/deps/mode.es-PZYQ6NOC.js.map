{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/mode-factory.js", "../../../../@graphiql/codemirror-graphql/esm/mode.js"], "sourcesContent": ["import { LexR<PERSON>, ParseRules, isIgnored, onlineParser, } from 'graphql-language-service';\nimport indent from './mode-indent';\nconst graphqlModeFactory = config => {\n    const parser = onlineParser({\n        eatWhitespace: stream => stream.eatWhile(isIgnored),\n        lexRules: LexRules,\n        parseRules: ParseRules,\n        editorConfig: { tabSize: config.tabSize },\n    });\n    return {\n        config,\n        startState: parser.startState,\n        token: parser.token,\n        indent,\n        electricInput: /^\\s*[})\\]]/,\n        fold: 'brace',\n        lineComment: '#',\n        closeBrackets: {\n            pairs: '()[]{}\"\"',\n            explode: '()[]{}',\n        },\n    };\n};\nexport default graphqlModeFactory;\n//# sourceMappingURL=mode-factory.js.map", "import CodeMirror from 'codemirror';\nimport modeFactory from './utils/mode-factory';\nCodeMirror.defineMode('graphql', modeFactory);\n//# sourceMappingURL=mode.js.map"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,IAAM,qBAAqB,CAAA,WAAU;AACjC,QAAM,SAAS,aAAa;IACxB,eAAe,CAAA,WAAU,OAAO,SAAS,SAAS;IAClD,UAAU;IACV,YAAY;IACZ,cAAc,EAAE,SAAS,OAAO,QAAS;EACjD,CAAK;AACD,SAAO;IACH;IACA,YAAY,OAAO;IACnB,OAAO,OAAO;IACd;IACA,eAAe;IACf,MAAM;IACN,aAAa;IACb,eAAe;MACX,OAAO;MACP,SAAS;IACZ;EACT;AACA;ACpBA,WAAW,WAAW,WAAWA,kBAAW;", "names": ["modeFactory"]}
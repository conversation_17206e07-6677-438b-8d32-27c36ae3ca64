import {
  requireMatchbrackets
} from "./chunk-OTNN3UYA.js";
import {
  getDefaultExportFromCjs
} from "./chunk-HV37R6KS.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@graphiql/react/dist/matchbrackets.es.js
function _mergeNamespaces(n, m) {
  for (var i = 0; i < m.length; i++) {
    const e = m[i];
    if (typeof e !== "string" && !Array.isArray(e)) {
      for (const k in e) {
        if (k !== "default" && !(k in n)) {
          const d = Object.getOwnPropertyDescriptor(e, k);
          if (d) {
            Object.defineProperty(n, k, d.get ? d : {
              enumerable: true,
              get: () => e[k]
            });
          }
        }
      }
    }
  }
  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: "Module" }));
}
var matchbracketsExports = requireMatchbrackets();
var matchbrackets = getDefaultExportFromCjs(matchbracketsExports);
var matchbrackets$1 = _mergeNamespaces({
  __proto__: null,
  default: matchbrackets
}, [matchbracketsExports]);
export {
  matchbrackets$1 as m
};
//# sourceMappingURL=matchbrackets.es-VYBSAIZM.js.map

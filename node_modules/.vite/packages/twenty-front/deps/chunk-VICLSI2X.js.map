{"version": 3, "sources": ["../../../../rollup-plugin-node-polyfills/polyfills/path.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// resolves . and .. elements in a path array with directory names there\n// must be no slashes, empty elements, or device names (c:\\) in the array\n// (so also no leading and trailing slashes - it does not distinguish\n// relative and absolute paths)\nfunction normalizeArray(parts, allowAboveRoot) {\n  // if the path tries to go above the root, `up` ends up > 0\n  var up = 0;\n  for (var i = parts.length - 1; i >= 0; i--) {\n    var last = parts[i];\n    if (last === '.') {\n      parts.splice(i, 1);\n    } else if (last === '..') {\n      parts.splice(i, 1);\n      up++;\n    } else if (up) {\n      parts.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (allowAboveRoot) {\n    for (; up--; up) {\n      parts.unshift('..');\n    }\n  }\n\n  return parts;\n}\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nvar splitPathRe =\n    /^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;\nvar splitPath = function(filename) {\n  return splitPathRe.exec(filename).slice(1);\n};\n\n// path.resolve([from ...], to)\n// posix version\nexport function resolve() {\n  var resolvedPath = '',\n      resolvedAbsolute = false;\n\n  for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    var path = (i >= 0) ? arguments[i] : '/';\n\n    // Skip empty and invalid entries\n    if (typeof path !== 'string') {\n      throw new TypeError('Arguments to path.resolve must be strings');\n    } else if (!path) {\n      continue;\n    }\n\n    resolvedPath = path + '/' + resolvedPath;\n    resolvedAbsolute = path.charAt(0) === '/';\n  }\n\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n\n  // Normalize the path\n  resolvedPath = normalizeArray(filter(resolvedPath.split('/'), function(p) {\n    return !!p;\n  }), !resolvedAbsolute).join('/');\n\n  return ((resolvedAbsolute ? '/' : '') + resolvedPath) || '.';\n};\n\n// path.normalize(path)\n// posix version\nexport function normalize(path) {\n  var isPathAbsolute = isAbsolute(path),\n      trailingSlash = substr(path, -1) === '/';\n\n  // Normalize the path\n  path = normalizeArray(filter(path.split('/'), function(p) {\n    return !!p;\n  }), !isPathAbsolute).join('/');\n\n  if (!path && !isPathAbsolute) {\n    path = '.';\n  }\n  if (path && trailingSlash) {\n    path += '/';\n  }\n\n  return (isPathAbsolute ? '/' : '') + path;\n};\n\n// posix version\nexport function isAbsolute(path) {\n  return path.charAt(0) === '/';\n}\n\n// posix version\nexport function join() {\n  var paths = Array.prototype.slice.call(arguments, 0);\n  return normalize(filter(paths, function(p, index) {\n    if (typeof p !== 'string') {\n      throw new TypeError('Arguments to path.join must be strings');\n    }\n    return p;\n  }).join('/'));\n}\n\n\n// path.relative(from, to)\n// posix version\nexport function relative(from, to) {\n  from = resolve(from).substr(1);\n  to = resolve(to).substr(1);\n\n  function trim(arr) {\n    var start = 0;\n    for (; start < arr.length; start++) {\n      if (arr[start] !== '') break;\n    }\n\n    var end = arr.length - 1;\n    for (; end >= 0; end--) {\n      if (arr[end] !== '') break;\n    }\n\n    if (start > end) return [];\n    return arr.slice(start, end - start + 1);\n  }\n\n  var fromParts = trim(from.split('/'));\n  var toParts = trim(to.split('/'));\n\n  var length = Math.min(fromParts.length, toParts.length);\n  var samePartsLength = length;\n  for (var i = 0; i < length; i++) {\n    if (fromParts[i] !== toParts[i]) {\n      samePartsLength = i;\n      break;\n    }\n  }\n\n  var outputParts = [];\n  for (var i = samePartsLength; i < fromParts.length; i++) {\n    outputParts.push('..');\n  }\n\n  outputParts = outputParts.concat(toParts.slice(samePartsLength));\n\n  return outputParts.join('/');\n}\n\nexport var sep = '/';\nexport var delimiter = ':';\n\nexport function dirname(path) {\n  var result = splitPath(path),\n      root = result[0],\n      dir = result[1];\n\n  if (!root && !dir) {\n    // No dirname whatsoever\n    return '.';\n  }\n\n  if (dir) {\n    // It has a dirname, strip trailing slash\n    dir = dir.substr(0, dir.length - 1);\n  }\n\n  return root + dir;\n}\n\nexport function basename(path, ext) {\n  var f = splitPath(path)[2];\n  // TODO: make this comparison case-insensitive on windows?\n  if (ext && f.substr(-1 * ext.length) === ext) {\n    f = f.substr(0, f.length - ext.length);\n  }\n  return f;\n}\n\n\nexport function extname(path) {\n  return splitPath(path)[3];\n}\nexport default {\n  extname: extname,\n  basename: basename,\n  dirname: dirname,\n  sep: sep,\n  delimiter: delimiter,\n  relative: relative,\n  join: join,\n  isAbsolute: isAbsolute,\n  normalize: normalize,\n  resolve: resolve\n};\nfunction filter (xs, f) {\n    if (xs.filter) return xs.filter(f);\n    var res = [];\n    for (var i = 0; i < xs.length; i++) {\n        if (f(xs[i], i, xs)) res.push(xs[i]);\n    }\n    return res;\n}\n\n// String.prototype.substr - negative index don't work in IE8\nvar substr = 'ab'.substr(-1) === 'b' ?\n    function (str, start, len) { return str.substr(start, len) } :\n    function (str, start, len) {\n        if (start < 0) start = str.length + start;\n        return str.substr(start, len);\n    }\n;\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA,SAAS,eAAe,OAAO,gBAAgB;AAE7C,MAAI,KAAK;AACT,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,SAAS,KAAK;AAChB,YAAM,OAAO,GAAG,CAAC;AAAA,IACnB,WAAW,SAAS,MAAM;AACxB,YAAM,OAAO,GAAG,CAAC;AACjB;AAAA,IACF,WAAW,IAAI;AACb,YAAM,OAAO,GAAG,CAAC;AACjB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,gBAAgB;AAClB,WAAO,MAAM,IAAI;AACf,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAYO,SAAS,UAAU;AACxB,MAAI,eAAe,IACf,mBAAmB;AAEvB,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,MAAM,CAAC,kBAAkB,KAAK;AACpE,QAAI,OAAQ,KAAK,IAAK,UAAU,CAAC,IAAI;AAGrC,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,UAAU,2CAA2C;AAAA,IACjE,WAAW,CAAC,MAAM;AAChB;AAAA,IACF;AAEA,mBAAe,OAAO,MAAM;AAC5B,uBAAmB,KAAK,OAAO,CAAC,MAAM;AAAA,EACxC;AAMA,iBAAe,eAAe,OAAO,aAAa,MAAM,GAAG,GAAG,SAAS,GAAG;AACxE,WAAO,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,GAAG;AAE/B,UAAS,mBAAmB,MAAM,MAAM,gBAAiB;AAC3D;AAIO,SAAS,UAAU,MAAM;AAC9B,MAAI,iBAAiB,WAAW,IAAI,GAChC,gBAAgB,OAAO,MAAM,EAAE,MAAM;AAGzC,SAAO,eAAe,OAAO,KAAK,MAAM,GAAG,GAAG,SAAS,GAAG;AACxD,WAAO,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,GAAG;AAE7B,MAAI,CAAC,QAAQ,CAAC,gBAAgB;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,eAAe;AACzB,YAAQ;AAAA,EACV;AAEA,UAAQ,iBAAiB,MAAM,MAAM;AACvC;AAGO,SAAS,WAAW,MAAM;AAC/B,SAAO,KAAK,OAAO,CAAC,MAAM;AAC5B;AAGO,SAAS,OAAO;AACrB,MAAI,QAAQ,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACnD,SAAO,UAAU,OAAO,OAAO,SAAS,GAAG,OAAO;AAChD,QAAI,OAAO,MAAM,UAAU;AACzB,YAAM,IAAI,UAAU,wCAAwC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,GAAG,CAAC;AACd;AAKO,SAAS,SAAS,MAAM,IAAI;AACjC,SAAO,QAAQ,IAAI,EAAE,OAAO,CAAC;AAC7B,OAAK,QAAQ,EAAE,EAAE,OAAO,CAAC;AAEzB,WAAS,KAAK,KAAK;AACjB,QAAI,QAAQ;AACZ,WAAO,QAAQ,IAAI,QAAQ,SAAS;AAClC,UAAI,IAAI,KAAK,MAAM,GAAI;AAAA,IACzB;AAEA,QAAI,MAAM,IAAI,SAAS;AACvB,WAAO,OAAO,GAAG,OAAO;AACtB,UAAI,IAAI,GAAG,MAAM,GAAI;AAAA,IACvB;AAEA,QAAI,QAAQ,IAAK,QAAO,CAAC;AACzB,WAAO,IAAI,MAAM,OAAO,MAAM,QAAQ,CAAC;AAAA,EACzC;AAEA,MAAI,YAAY,KAAK,KAAK,MAAM,GAAG,CAAC;AACpC,MAAI,UAAU,KAAK,GAAG,MAAM,GAAG,CAAC;AAEhC,MAAI,SAAS,KAAK,IAAI,UAAU,QAAQ,QAAQ,MAAM;AACtD,MAAI,kBAAkB;AACtB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,GAAG;AAC/B,wBAAkB;AAClB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,cAAc,CAAC;AACnB,WAAS,IAAI,iBAAiB,IAAI,UAAU,QAAQ,KAAK;AACvD,gBAAY,KAAK,IAAI;AAAA,EACvB;AAEA,gBAAc,YAAY,OAAO,QAAQ,MAAM,eAAe,CAAC;AAE/D,SAAO,YAAY,KAAK,GAAG;AAC7B;AAKO,SAAS,QAAQ,MAAM;AAC5B,MAAI,SAAS,UAAU,IAAI,GACvB,OAAO,OAAO,CAAC,GACf,MAAM,OAAO,CAAC;AAElB,MAAI,CAAC,QAAQ,CAAC,KAAK;AAEjB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK;AAEP,UAAM,IAAI,OAAO,GAAG,IAAI,SAAS,CAAC;AAAA,EACpC;AAEA,SAAO,OAAO;AAChB;AAEO,SAAS,SAAS,MAAM,KAAK;AAClC,MAAI,IAAI,UAAU,IAAI,EAAE,CAAC;AAEzB,MAAI,OAAO,EAAE,OAAO,KAAK,IAAI,MAAM,MAAM,KAAK;AAC5C,QAAI,EAAE,OAAO,GAAG,EAAE,SAAS,IAAI,MAAM;AAAA,EACvC;AACA,SAAO;AACT;AAGO,SAAS,QAAQ,MAAM;AAC5B,SAAO,UAAU,IAAI,EAAE,CAAC;AAC1B;AAaA,SAAS,OAAQ,IAAI,GAAG;AACpB,MAAI,GAAG,OAAQ,QAAO,GAAG,OAAO,CAAC;AACjC,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,QAAI,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,EAAG,KAAI,KAAK,GAAG,CAAC,CAAC;AAAA,EACvC;AACA,SAAO;AACX;AAhOA,IAqDI,aAEA,WAoHO,KACA,WAiCJ,cAsBH;AAnOJ;AAAA;AAqDA,IAAI,cACA;AACJ,IAAI,YAAY,SAAS,UAAU;AACjC,aAAO,YAAY,KAAK,QAAQ,EAAE,MAAM,CAAC;AAAA,IAC3C;AAkHO,IAAI,MAAM;AACV,IAAI,YAAY;AAiCvB,IAAO,eAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAWA,IAAI,SAAS,KAAK,OAAO,EAAE,MAAM,MAC7B,SAAU,KAAK,OAAO,KAAK;AAAE,aAAO,IAAI,OAAO,OAAO,GAAG;AAAA,IAAE,IAC3D,SAAU,KAAK,OAAO,KAAK;AACvB,UAAI,QAAQ,EAAG,SAAQ,IAAI,SAAS;AACpC,aAAO,IAAI,OAAO,OAAO,GAAG;AAAA,IAChC;AAAA;AAAA;", "names": []}
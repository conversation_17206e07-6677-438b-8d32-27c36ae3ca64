import {
  w as w2
} from "./chunk-PEQF4OOI.js";
import {
  useRouter
} from "./chunk-EFYDGGJO.js";
import {
  F,
  a,
  je,
  s
} from "./chunk-K3FRCNXE.js";
import {
  $,
  E2 as E,
  Fragment,
  c,
  createBaseVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  f,
  openBlock,
  toDisplayString,
  unref,
  w5 as w,
  withCtx
} from "./chunk-D5ZO4EYM.js";
import "./chunk-QKW3RGIP.js";
import "./chunk-ESNIZFAM.js";
import "./chunk-EQ6OFAN5.js";
import "./chunk-KKDV6FLU.js";
import "./chunk-CZU42NES.js";
import "./chunk-WVQBL4AG.js";
import "./chunk-VRPX3MPE.js";
import "./chunk-7LX7AH2P.js";
import "./chunk-FPMN7SAE.js";
import "./chunk-LG6Z3D2E.js";
import "./chunk-M2KGN5WX.js";
import "./chunk-7YXTSSTX.js";
import "./chunk-UCEBVBQV.js";
import "./chunk-TFQJNSQ7.js";
import "./chunk-OBJQZ5YF.js";
import "./chunk-CEFWFDOS.js";
import "./chunk-UBKGHFA7.js";
import "./chunk-KOZRLTEU.js";
import "./chunk-L3M7MDWL.js";
import "./chunk-4IFNTA3D.js";
import "./chunk-OILITMIS.js";
import "./chunk-QTFPMWUM.js";
import "./chunk-7TAJEJOW.js";
import "./chunk-XTJKMDAQ.js";
import "./chunk-C4W2J7YQ.js";
import "./chunk-DGKAHXPJ.js";
import "./chunk-ZBBXX2VR.js";
import "./chunk-F3KEQQNW.js";
import "./chunk-YAGSMJYR.js";
import "./chunk-XPZLJQLW.js";

// node_modules/@scalar/api-client/dist/views/Collection/CollectionSettings.vue2.js
var $2 = { class: "flex h-full w-full flex-col gap-12 px-1.5 pt-8" };
var L = { class: "flex flex-col gap-2" };
var O = { class: "bg-b-2 rounded-lg border text-sm" };
var P = { class: "bg-b-1 -m-1/2 flex items-center justify-between gap-4 rounded-t-lg border p-3" };
var R = { class: "text-c-1 flex items-center overflow-x-auto whitespace-nowrap py-1.5" };
var j = { class: "flex items-center" };
var z = ["href"];
var F2 = { class: "flex flex-col gap-4" };
var Z = { class: "flex items-center justify-between rounded-lg border p-3 text-sm" };
var te = defineComponent({
  __name: "CollectionSettings",
  setup(q) {
    const { activeCollection: o, activeWorkspace: a2, activeWorkspaceCollections: C } = F(), { collectionMutators: m } = je(), { replace: y } = useRouter(), i = E();
    function M() {
      var n, e;
      o.value && (n = o.value) != null && n.documentUrl && m.edit(
        o.value.uid,
        "watchMode",
        !((e = o.value) != null && e.watchMode)
      );
    }
    function U() {
      if (!o.value || !a2.value) return;
      m.delete(o.value, a2.value);
      const n = C.value[0];
      n && y({
        name: "collection",
        params: {
          [a.Workspace]: a2.value.uid,
          [a.Collection]: n.uid
        }
      }), i.hide();
    }
    return (n, e) => {
      var f3, p, v, x, g;
      return openBlock(), createElementBlock(Fragment, null, [
        createBaseVNode("div", $2, [
          createBaseVNode("div", L, [
            e[5] || (e[5] = createBaseVNode("div", { class: "flex h-8 items-center" }, [
              createBaseVNode("h3", { class: "font-bold" }, "Features")
            ], -1)),
            createBaseVNode("div", O, [
              createBaseVNode("div", P, [
                e[2] || (e[2] = createBaseVNode("div", null, [
                  createBaseVNode("h4", null, "Watch Mode"),
                  createBaseVNode("p", { class: "text-c-2 mt-1" }, " When enabled, the OpenAPI document will be polled for changes. The collection will be updated automatically. ")
                ], -1)),
                createVNode(unref(w), {
                  class: "w-4",
                  disabled: !((f3 = unref(o)) != null && f3.documentUrl),
                  modelValue: ((p = unref(o)) == null ? void 0 : p.watchMode) ?? false,
                  "onUpdate:modelValue": M
                }, null, 8, ["disabled", "modelValue"])
              ]),
              createBaseVNode("div", R, [
                createBaseVNode("div", j, [
                  (v = unref(o)) != null && v.documentUrl ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                    e[3] || (e[3] = createBaseVNode("span", { class: "bg-b-2 sticky left-0 pl-3 pr-2" }, "Source", -1)),
                    createBaseVNode("a", {
                      class: "text-c-2 group rounded pr-3 no-underline hover:underline",
                      href: unref(o).documentUrl,
                      target: "_blank"
                    }, [
                      createTextVNode(toDisplayString(unref(o).documentUrl) + " ", 1),
                      createVNode(unref(c), {
                        class: "ml-1 hidden w-2.5 group-hover:inline",
                        icon: "ExternalLink"
                      })
                    ], 8, z)
                  ], 64)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                    createVNode(unref(c), {
                      class: "text-c-2 ml-3 mr-2 w-4",
                      icon: "NotAllowed",
                      size: "sm"
                    }),
                    e[4] || (e[4] = createBaseVNode("span", { class: "text-c-2 pr-3" }, " No URL configured. Try importing an OpenAPI document from an URL. ", -1))
                  ], 64))
                ])
              ])
            ])
          ]),
          createBaseVNode("div", F2, [
            e[8] || (e[8] = createBaseVNode("h3", { class: "font-bold" }, "Danger Zone", -1)),
            createBaseVNode("div", Z, [
              e[7] || (e[7] = createBaseVNode("div", null, [
                createBaseVNode("h4", null, "Delete Collection"),
                createBaseVNode("p", { class: "text-c-2 mt-1" }, " Be careful, my friend. Once deleted, there is no way to recover the collection. ")
              ], -1)),
              createVNode(unref($), {
                class: "custom-scroll h-8 gap-1.5 whitespace-nowrap px-2.5 font-medium shadow-none focus:outline-none",
                variant: "danger",
                onClick: e[0] || (e[0] = (r) => unref(i).show())
              }, {
                default: withCtx(() => e[6] || (e[6] = [
                  createTextVNode(" Delete Collection ")
                ])),
                _: 1
              })
            ])
          ])
        ]),
        createVNode(unref(f), {
          size: "xxs",
          state: unref(i),
          title: `Delete ${(g = (x = unref(o)) == null ? void 0 : x.info) == null ? void 0 : g.title}`
        }, {
          default: withCtx(() => {
            var r, h;
            return [
              createVNode(w2, {
                variableName: ((h = (r = unref(o)) == null ? void 0 : r.info) == null ? void 0 : h.title) ?? "",
                warningMessage: "This action cannot be undone.",
                onClose: e[1] || (e[1] = (G) => unref(i).hide()),
                onDelete: U
              }, null, 8, ["variableName"])
            ];
          }),
          _: 1
        }, 8, ["state", "title"])
      ], 64);
    };
  }
});

// node_modules/@scalar/api-client/dist/views/Collection/CollectionSettings.vue.js
var f2 = s(te, [["__scopeId", "data-v-ea98df67"]]);
export {
  f2 as default
};
//# sourceMappingURL=CollectionSettings.vue-FOIL7AKQ.js.map

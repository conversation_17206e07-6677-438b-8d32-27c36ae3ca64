import {
  getDefaultExportFromCjs,
  requireCodemirror
} from "./chunk-HV37R6KS.js";

// node_modules/@graphiql/react/dist/codemirror.es.js
function _mergeNamespaces(n, m) {
  for (var i = 0; i < m.length; i++) {
    const e = m[i];
    if (typeof e !== "string" && !Array.isArray(e)) {
      for (const k in e) {
        if (k !== "default" && !(k in n)) {
          const d = Object.getOwnPropertyDescriptor(e, k);
          if (d) {
            Object.defineProperty(n, k, d.get ? d : {
              enumerable: true,
              get: () => e[k]
            });
          }
        }
      }
    }
  }
  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: "Module" }));
}
var codemirrorExports = requireCodemirror();
var CodeMirror = getDefaultExportFromCjs(codemirrorExports);
var codemirror = _mergeNamespaces({
  __proto__: null,
  default: CodeMirror
}, [codemirrorExports]);

export {
  CodeMirror,
  codemirror
};
//# sourceMappingURL=chunk-VKDUPG67.js.map

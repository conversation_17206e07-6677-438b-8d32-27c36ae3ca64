{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/cpp/cpp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cpp/cpp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cpp\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"abstract\",\n    \"amp\",\n    \"array\",\n    \"auto\",\n    \"bool\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"class\",\n    \"const\",\n    \"constexpr\",\n    \"const_cast\",\n    \"continue\",\n    \"cpu\",\n    \"decltype\",\n    \"default\",\n    \"delegate\",\n    \"delete\",\n    \"do\",\n    \"double\",\n    \"dynamic_cast\",\n    \"each\",\n    \"else\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"export\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"friend\",\n    \"gcnew\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"initonly\",\n    \"inline\",\n    \"int\",\n    \"interface\",\n    \"interior_ptr\",\n    \"internal\",\n    \"literal\",\n    \"long\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    \"noexcept\",\n    \"nullptr\",\n    \"__nullptr\",\n    \"operator\",\n    \"override\",\n    \"partial\",\n    \"pascal\",\n    \"pin_ptr\",\n    \"private\",\n    \"property\",\n    \"protected\",\n    \"public\",\n    \"ref\",\n    \"register\",\n    \"reinterpret_cast\",\n    \"restrict\",\n    \"return\",\n    \"safe_cast\",\n    \"sealed\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"static_assert\",\n    \"static_cast\",\n    \"struct\",\n    \"switch\",\n    \"template\",\n    \"this\",\n    \"thread_local\",\n    \"throw\",\n    \"tile_static\",\n    \"true\",\n    \"try\",\n    \"typedef\",\n    \"typeid\",\n    \"typename\",\n    \"union\",\n    \"unsigned\",\n    \"using\",\n    \"virtual\",\n    \"void\",\n    \"volatile\",\n    \"wchar_t\",\n    \"where\",\n    \"while\",\n    \"_asm\",\n    // reserved word with one underscores\n    \"_based\",\n    \"_cdecl\",\n    \"_declspec\",\n    \"_fastcall\",\n    \"_if_exists\",\n    \"_if_not_exists\",\n    \"_inline\",\n    \"_multiple_inheritance\",\n    \"_pascal\",\n    \"_single_inheritance\",\n    \"_stdcall\",\n    \"_virtual_inheritance\",\n    \"_w64\",\n    \"__abstract\",\n    // reserved word with two underscores\n    \"__alignof\",\n    \"__asm\",\n    \"__assume\",\n    \"__based\",\n    \"__box\",\n    \"__builtin_alignof\",\n    \"__cdecl\",\n    \"__clrcall\",\n    \"__declspec\",\n    \"__delegate\",\n    \"__event\",\n    \"__except\",\n    \"__fastcall\",\n    \"__finally\",\n    \"__forceinline\",\n    \"__gc\",\n    \"__hook\",\n    \"__identifier\",\n    \"__if_exists\",\n    \"__if_not_exists\",\n    \"__inline\",\n    \"__int128\",\n    \"__int16\",\n    \"__int32\",\n    \"__int64\",\n    \"__int8\",\n    \"__interface\",\n    \"__leave\",\n    \"__m128\",\n    \"__m128d\",\n    \"__m128i\",\n    \"__m256\",\n    \"__m256d\",\n    \"__m256i\",\n    \"__m512\",\n    \"__m512d\",\n    \"__m512i\",\n    \"__m64\",\n    \"__multiple_inheritance\",\n    \"__newslot\",\n    \"__nogc\",\n    \"__noop\",\n    \"__nounwind\",\n    \"__novtordisp\",\n    \"__pascal\",\n    \"__pin\",\n    \"__pragma\",\n    \"__property\",\n    \"__ptr32\",\n    \"__ptr64\",\n    \"__raise\",\n    \"__restrict\",\n    \"__resume\",\n    \"__sealed\",\n    \"__single_inheritance\",\n    \"__stdcall\",\n    \"__super\",\n    \"__thiscall\",\n    \"__try\",\n    \"__try_cast\",\n    \"__typeof\",\n    \"__unaligned\",\n    \"__unhook\",\n    \"__uuidof\",\n    \"__value\",\n    \"__virtual_inheritance\",\n    \"__w64\",\n    \"__wchar_t\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[0abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  integersuffix: /([uU](ll|LL|l|L)|(ll|LL|l|L)?[uU]?)/,\n  floatsuffix: /[fFlL]?/,\n  encoding: /u|u8|U|L/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // C++ 11 Raw String\n      [/@encoding?R\\\"(?:([^ ()\\\\\\t]*))\\(/, { token: \"string.raw.begin\", next: \"@raw.$1\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // The preprocessor checks must be before whitespace as they check /^\\s*#/ which\n      // otherwise fails to match later after other whitespace has been removed.\n      // Inclusion\n      [/^\\s*#\\s*include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directive\n      [/^\\s*#\\s*\\w+/, \"keyword.directive\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // [[ attributes ]].\n      [/\\[\\s*\\[/, { token: \"annotation\", next: \"@annotation\" }],\n      // delimiters and operators\n      [/[{}()<>\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, \"number.hex\"],\n      [/0[0-7']*[0-7](@integersuffix)/, \"number.octal\"],\n      [/0[bB][0-1']*[0-1](@integersuffix)/, \"number.binary\"],\n      [/\\d[\\d']*\\d(@integersuffix)/, \"number\"],\n      [/\\d(@integersuffix)/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@doccomment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*\\\\$/, \"comment\", \"@linecomment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //For use with continuous line comments\n    linecomment: [\n      [/.*[^\\\\]$/, \"comment\", \"@pop\"],\n      [/[^]+/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    doccomment: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    raw: [\n      [/[^)]+/, \"string.raw\"],\n      [/\\)$S2\\\"/, { token: \"string.raw.end\", next: \"@pop\" }],\n      [/\\)/, \"string.raw\"]\n    ],\n    annotation: [\n      { include: \"@whitespace\" },\n      [/using|alignas/, \"keyword\"],\n      [/[a-zA-Z0-9_]+/, \"annotation\"],\n      [/[,:]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [/\\]\\s*\\]/, { token: \"annotation\", next: \"@pop\" }]\n    ],\n    include: [\n      [\n        /(\\s*)(<)([^<>]*)(>)/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(\")([^\"]*)(\")/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA+BA;AAxCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC7C;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,2BAA2B;AAAA,UAC7C,KAAK,IAAI,OAAO,8BAA8B;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,QAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,QACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,QACnD,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,MACpD;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,SAAS;AAAA,MACT,SAAS;AAAA,MACT,eAAe;AAAA,MACf,aAAa;AAAA,MACb,UAAU;AAAA;AAAA,MAEV,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,oCAAoC,EAAE,OAAO,oBAAoB,MAAM,UAAU,CAAC;AAAA;AAAA,UAEnF;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa,EAAE,OAAO,aAAa;AAAA,gBACnC,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,CAAC,mBAAmB,EAAE,OAAO,6BAA6B,MAAM,WAAW,CAAC;AAAA;AAAA,UAE5E,CAAC,eAAe,mBAAmB;AAAA;AAAA,UAEnC,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,WAAW,EAAE,OAAO,cAAc,MAAM,cAAc,CAAC;AAAA;AAAA,UAExD,CAAC,gBAAgB,WAAW;AAAA,UAC5B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,CAAC,wCAAwC,cAAc;AAAA,UACvD,CAAC,0CAA0C,cAAc;AAAA,UACzD,CAAC,iDAAiD,YAAY;AAAA,UAC9D,CAAC,iCAAiC,cAAc;AAAA,UAChD,CAAC,qCAAqC,eAAe;AAAA,UACrD,CAAC,8BAA8B,QAAQ;AAAA,UACvC,CAAC,sBAAsB,QAAQ;AAAA;AAAA,UAE/B,CAAC,SAAS,WAAW;AAAA;AAAA,UAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,UAEzB,CAAC,YAAY,QAAQ;AAAA,UACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,UAC1D,CAAC,KAAK,gBAAgB;AAAA,QACxB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,gBAAgB,eAAe,aAAa;AAAA,UAC7C,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,aAAa,WAAW,cAAc;AAAA,UACvC,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA;AAAA,QAEA,aAAa;AAAA,UACX,CAAC,YAAY,WAAW,MAAM;AAAA,UAC9B,CAAC,QAAQ,SAAS;AAAA,QACpB;AAAA;AAAA,QAEA,YAAY;AAAA,UACV,CAAC,WAAW,aAAa;AAAA,UACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,UAC9B,CAAC,SAAS,aAAa;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,QACxB;AAAA,QACA,KAAK;AAAA,UACH,CAAC,SAAS,YAAY;AAAA,UACtB,CAAC,WAAW,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,UACrD,CAAC,MAAM,YAAY;AAAA,QACrB;AAAA,QACA,YAAY;AAAA,UACV,EAAE,SAAS,cAAc;AAAA,UACzB,CAAC,iBAAiB,SAAS;AAAA,UAC3B,CAAC,iBAAiB,YAAY;AAAA,UAC9B,CAAC,QAAQ,WAAW;AAAA,UACpB,CAAC,QAAQ,WAAW;AAAA,UACpB,CAAC,WAAW,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,QACnD;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,OAAO,iCAAiC,MAAM,OAAO;AAAA,YACzD;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,OAAO,iCAAiC,MAAM,OAAO;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
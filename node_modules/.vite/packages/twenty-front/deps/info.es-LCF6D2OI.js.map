{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/info.js"], "sourcesContent": ["import { GraphQL<PERSON>ist, GraphQLNonNull, } from 'graphql';\nimport CodeMirror from 'codemirror';\nimport getTypeInfo from './utils/getTypeInfo';\nimport { getArgumentReference, getDirectiveReference, getEnumValueReference, getFieldReference, getTypeReference, } from './utils/SchemaReference';\nimport './utils/info-addon';\nCodeMirror.registerHelper('info', 'graphql', (token, options) => {\n    var _a;\n    if (!options.schema || !token.state) {\n        return;\n    }\n    const { kind, step } = token.state;\n    const typeInfo = getTypeInfo(options.schema, token.state);\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef) ||\n        (kind === 'ObjectField' && step === 0 && typeInfo.fieldDef)) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderField(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.fieldDef);\n        return into;\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderDirective(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.directiveDef);\n        return into;\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderArg(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.argDef);\n        return into;\n    }\n    if (kind === 'EnumValue' && ((_a = typeInfo.enumValue) === null || _a === void 0 ? void 0 : _a.description)) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderEnumValue(header, typeInfo, options);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.enumValue);\n        return into;\n    }\n    if (kind === 'NamedType' &&\n        typeInfo.type &&\n        typeInfo.type.description) {\n        const header = document.createElement('div');\n        header.className = 'CodeMirror-info-header';\n        renderType(header, typeInfo, options, typeInfo.type);\n        const into = document.createElement('div');\n        into.append(header);\n        renderDescription(into, options, typeInfo.type);\n        return into;\n    }\n});\nfunction renderField(into, typeInfo, options) {\n    renderQualifiedField(into, typeInfo, options);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.type);\n}\nfunction renderQualifiedField(into, typeInfo, options) {\n    var _a;\n    const fieldName = ((_a = typeInfo.fieldDef) === null || _a === void 0 ? void 0 : _a.name) || '';\n    text(into, fieldName, 'field-name', options, getFieldReference(typeInfo));\n}\nfunction renderDirective(into, typeInfo, options) {\n    var _a;\n    const name = '@' + (((_a = typeInfo.directiveDef) === null || _a === void 0 ? void 0 : _a.name) || '');\n    text(into, name, 'directive-name', options, getDirectiveReference(typeInfo));\n}\nfunction renderArg(into, typeInfo, options) {\n    var _a;\n    const name = ((_a = typeInfo.argDef) === null || _a === void 0 ? void 0 : _a.name) || '';\n    text(into, name, 'arg-name', options, getArgumentReference(typeInfo));\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.inputType);\n}\nfunction renderEnumValue(into, typeInfo, options) {\n    var _a;\n    const name = ((_a = typeInfo.enumValue) === null || _a === void 0 ? void 0 : _a.name) || '';\n    renderType(into, typeInfo, options, typeInfo.inputType);\n    text(into, '.');\n    text(into, name, 'enum-value', options, getEnumValueReference(typeInfo));\n}\nfunction renderTypeAnnotation(into, typeInfo, options, t) {\n    const typeSpan = document.createElement('span');\n    typeSpan.className = 'type-name-pill';\n    if (t instanceof GraphQLNonNull) {\n        renderType(typeSpan, typeInfo, options, t.ofType);\n        text(typeSpan, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(typeSpan, '[');\n        renderType(typeSpan, typeInfo, options, t.ofType);\n        text(typeSpan, ']');\n    }\n    else {\n        text(typeSpan, (t === null || t === void 0 ? void 0 : t.name) || '', 'type-name', options, getTypeReference(typeInfo, t));\n    }\n    into.append(typeSpan);\n}\nfunction renderType(into, typeInfo, options, t) {\n    if (t instanceof GraphQLNonNull) {\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(into, '[');\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, ']');\n    }\n    else {\n        text(into, (t === null || t === void 0 ? void 0 : t.name) || '', 'type-name', options, getTypeReference(typeInfo, t));\n    }\n}\nfunction renderDescription(into, options, def) {\n    const { description } = def;\n    if (description) {\n        const descriptionDiv = document.createElement('div');\n        descriptionDiv.className = 'info-description';\n        if (options.renderDescription) {\n            descriptionDiv.innerHTML = options.renderDescription(description);\n        }\n        else {\n            descriptionDiv.append(document.createTextNode(description));\n        }\n        into.append(descriptionDiv);\n    }\n    renderDeprecation(into, options, def);\n}\nfunction renderDeprecation(into, options, def) {\n    const reason = def.deprecationReason;\n    if (reason) {\n        const deprecationDiv = document.createElement('div');\n        deprecationDiv.className = 'info-deprecation';\n        into.append(deprecationDiv);\n        const label = document.createElement('span');\n        label.className = 'info-deprecation-label';\n        label.append(document.createTextNode('Deprecated'));\n        deprecationDiv.append(label);\n        const reasonDiv = document.createElement('div');\n        reasonDiv.className = 'info-deprecation-reason';\n        if (options.renderDescription) {\n            reasonDiv.innerHTML = options.renderDescription(reason);\n        }\n        else {\n            reasonDiv.append(document.createTextNode(reason));\n        }\n        deprecationDiv.append(reasonDiv);\n    }\n}\nfunction text(into, content, className = '', options = { onClick: null }, ref = null) {\n    if (className) {\n        const { onClick } = options;\n        let node;\n        if (onClick) {\n            node = document.createElement('a');\n            node.href = 'javascript:void 0';\n            node.addEventListener('click', (e) => {\n                e.preventDefault();\n                onClick(ref, e);\n            });\n        }\n        else {\n            node = document.createElement('span');\n        }\n        node.className = className;\n        node.append(document.createTextNode(content));\n        into.append(node);\n    }\n    else {\n        into.append(document.createTextNode(content));\n    }\n}\n//# sourceMappingURL=info.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKA,WAAW,eAAe,QAAQ,WAAW,CAAC,OAAO,YAAY;AAC7D,MAAI;AACJ,MAAI,CAAC,QAAQ,UAAU,CAAC,MAAM,OAAO;AACjC;EACH;AACD,QAAM,EAAE,MAAM,KAAA,IAAS,MAAM;AAC7B,QAAM,WAAW,YAAY,QAAQ,QAAQ,MAAM,KAAK;AACxD,MAAK,SAAS,WAAW,SAAS,KAAK,SAAS,YAC3C,SAAS,kBAAkB,SAAS,KAAK,SAAS,YAClD,SAAS,iBAAiB,SAAS,KAAK,SAAS,UAAW;AAC7D,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,YAAY;AACnB,gBAAY,QAAQ,UAAU,OAAO;AACrC,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,OAAO,MAAM;AAClB,sBAAkB,MAAM,SAAS,SAAS,QAAQ;AAClD,WAAO;EACV;AACD,MAAI,SAAS,eAAe,SAAS,KAAK,SAAS,cAAc;AAC7D,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,YAAY;AACnB,oBAAgB,QAAQ,UAAU,OAAO;AACzC,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,OAAO,MAAM;AAClB,sBAAkB,MAAM,SAAS,SAAS,YAAY;AACtD,WAAO;EACV;AACD,MAAI,SAAS,cAAc,SAAS,KAAK,SAAS,QAAQ;AACtD,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,YAAY;AACnB,cAAU,QAAQ,UAAU,OAAO;AACnC,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,OAAO,MAAM;AAClB,sBAAkB,MAAM,SAAS,SAAS,MAAM;AAChD,WAAO;EACV;AACD,MAAI,SAAS,iBAAiB,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AACzG,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,YAAY;AACnB,oBAAgB,QAAQ,UAAU,OAAO;AACzC,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,OAAO,MAAM;AAClB,sBAAkB,MAAM,SAAS,SAAS,SAAS;AACnD,WAAO;EACV;AACD,MAAI,SAAS,eACT,SAAS,QACT,SAAS,KAAK,aAAa;AAC3B,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,YAAY;AACnB,eAAW,QAAQ,UAAU,SAAS,SAAS,IAAI;AACnD,UAAM,OAAO,SAAS,cAAc,KAAK;AACzC,SAAK,OAAO,MAAM;AAClB,sBAAkB,MAAM,SAAS,SAAS,IAAI;AAC9C,WAAO;EACV;AACL,CAAC;AACD,SAAS,YAAY,MAAM,UAAU,SAAS;AAC1C,uBAAqB,MAAM,UAAU,OAAO;AAC5C,uBAAqB,MAAM,UAAU,SAAS,SAAS,IAAI;AAC/D;AACA,SAAS,qBAAqB,MAAM,UAAU,SAAS;AACnD,MAAI;AACJ,QAAM,cAAc,KAAK,SAAS,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAC7F,OAAK,MAAM,WAAW,cAAc,SAAS,kBAAkB,QAAQ,CAAC;AAC5E;AACA,SAAS,gBAAgB,MAAM,UAAU,SAAS;AAC9C,MAAI;AACJ,QAAM,OAAO,SAAS,KAAK,SAAS,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACnG,OAAK,MAAM,MAAM,kBAAkB,SAAS,sBAAsB,QAAQ,CAAC;AAC/E;AACA,SAAS,UAAU,MAAM,UAAU,SAAS;AACxC,MAAI;AACJ,QAAM,SAAS,KAAK,SAAS,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACtF,OAAK,MAAM,MAAM,YAAY,SAAS,qBAAqB,QAAQ,CAAC;AACpE,uBAAqB,MAAM,UAAU,SAAS,SAAS,SAAS;AACpE;AACA,SAAS,gBAAgB,MAAM,UAAU,SAAS;AAC9C,MAAI;AACJ,QAAM,SAAS,KAAK,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AACzF,aAAW,MAAM,UAAU,SAAS,SAAS,SAAS;AACtD,OAAK,MAAM,GAAG;AACd,OAAK,MAAM,MAAM,cAAc,SAAS,sBAAsB,QAAQ,CAAC;AAC3E;AACA,SAAS,qBAAqB,MAAM,UAAU,SAAS,GAAG;AACtD,QAAM,WAAW,SAAS,cAAc,MAAM;AAC9C,WAAS,YAAY;AACrB,MAAI,aAAa,gBAAgB;AAC7B,eAAW,UAAU,UAAU,SAAS,EAAE,MAAM;AAChD,SAAK,UAAU,GAAG;EACrB,WACQ,aAAa,aAAa;AAC/B,SAAK,UAAU,GAAG;AAClB,eAAW,UAAU,UAAU,SAAS,EAAE,MAAM;AAChD,SAAK,UAAU,GAAG;EACrB,OACI;AACD,SAAK,WAAW,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,SAAS,IAAI,aAAa,SAAS,iBAAiB,UAAU,CAAC,CAAC;EAC3H;AACD,OAAK,OAAO,QAAQ;AACxB;AACA,SAAS,WAAW,MAAM,UAAU,SAAS,GAAG;AAC5C,MAAI,aAAa,gBAAgB;AAC7B,eAAW,MAAM,UAAU,SAAS,EAAE,MAAM;AAC5C,SAAK,MAAM,GAAG;EACjB,WACQ,aAAa,aAAa;AAC/B,SAAK,MAAM,GAAG;AACd,eAAW,MAAM,UAAU,SAAS,EAAE,MAAM;AAC5C,SAAK,MAAM,GAAG;EACjB,OACI;AACD,SAAK,OAAO,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,SAAS,IAAI,aAAa,SAAS,iBAAiB,UAAU,CAAC,CAAC;EACvH;AACL;AACA,SAAS,kBAAkB,MAAM,SAAS,KAAK;AAC3C,QAAM,EAAE,YAAa,IAAG;AACxB,MAAI,aAAa;AACb,UAAM,iBAAiB,SAAS,cAAc,KAAK;AACnD,mBAAe,YAAY;AAC3B,QAAI,QAAQ,mBAAmB;AAC3B,qBAAe,YAAY,QAAQ,kBAAkB,WAAW;IACnE,OACI;AACD,qBAAe,OAAO,SAAS,eAAe,WAAW,CAAC;IAC7D;AACD,SAAK,OAAO,cAAc;EAC7B;AACD,oBAAkB,MAAM,SAAS,GAAG;AACxC;AACA,SAAS,kBAAkB,MAAM,SAAS,KAAK;AAC3C,QAAM,SAAS,IAAI;AACnB,MAAI,QAAQ;AACR,UAAM,iBAAiB,SAAS,cAAc,KAAK;AACnD,mBAAe,YAAY;AAC3B,SAAK,OAAO,cAAc;AAC1B,UAAM,QAAQ,SAAS,cAAc,MAAM;AAC3C,UAAM,YAAY;AAClB,UAAM,OAAO,SAAS,eAAe,YAAY,CAAC;AAClD,mBAAe,OAAO,KAAK;AAC3B,UAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,cAAU,YAAY;AACtB,QAAI,QAAQ,mBAAmB;AAC3B,gBAAU,YAAY,QAAQ,kBAAkB,MAAM;IACzD,OACI;AACD,gBAAU,OAAO,SAAS,eAAe,MAAM,CAAC;IACnD;AACD,mBAAe,OAAO,SAAS;EAClC;AACL;AACA,SAAS,KAAK,MAAM,SAAS,YAAY,IAAI,UAAU,EAAE,SAAS,KAAA,GAAQ,MAAM,MAAM;AAClF,MAAI,WAAW;AACX,UAAM,EAAE,QAAS,IAAG;AACpB,QAAI;AACJ,QAAI,SAAS;AACT,aAAO,SAAS,cAAc,GAAG;AACjC,WAAK,OAAO;AACZ,WAAK,iBAAiB,SAAS,CAAC,MAAM;AAClC,UAAE,eAAc;AAChB,gBAAQ,KAAK,CAAC;MAC9B,CAAa;IACJ,OACI;AACD,aAAO,SAAS,cAAc,MAAM;IACvC;AACD,SAAK,YAAY;AACjB,SAAK,OAAO,SAAS,eAAe,OAAO,CAAC;AAC5C,SAAK,OAAO,IAAI;EACnB,OACI;AACD,SAAK,OAAO,SAAS,eAAe,OAAO,CAAC;EAC/C;AACL;", "names": []}
{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/graphql/graphql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/graphql/graphql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"', notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".gql\",\n  keywords: [\n    \"null\",\n    \"true\",\n    \"false\",\n    \"query\",\n    \"mutation\",\n    \"subscription\",\n    \"extend\",\n    \"schema\",\n    \"directive\",\n    \"scalar\",\n    \"type\",\n    \"interface\",\n    \"union\",\n    \"enum\",\n    \"input\",\n    \"implements\",\n    \"fragment\",\n    \"on\"\n  ],\n  typeKeywords: [\"Int\", \"Float\", \"String\", \"Boolean\", \"ID\"],\n  directiveLocations: [\n    \"SCHEMA\",\n    \"SCALAR\",\n    \"OBJECT\",\n    \"FIELD_DEFINITION\",\n    \"ARGUMENT_DEFINITION\",\n    \"INTERFACE\",\n    \"UNION\",\n    \"ENUM\",\n    \"ENUM_VALUE\",\n    \"INPUT_OBJECT\",\n    \"INPUT_FIELD_DEFINITION\",\n    \"QUERY\",\n    \"MUTATION\",\n    \"SUBSCRIPTION\",\n    \"FIELD\",\n    \"FRAGMENT_DEFINITION\",\n    \"FRAGMENT_SPREAD\",\n    \"INLINE_FRAGMENT\",\n    \"VARIABLE_DEFINITION\"\n  ],\n  operators: [\"=\", \"!\", \"?\", \":\", \"&\", \"|\"],\n  // we include these common regular expressions\n  symbols: /[=!?:&|]+/,\n  // https://facebook.github.io/graphql/draft/#sec-String-Value\n  escapes: /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9A-Fa-f]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // fields and argument names\n      [\n        /[a-z_][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      // identify typed input variables\n      [\n        /[$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"argument.identifier\"\n          }\n        }\n      ],\n      // to show class names nicely\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // @ annotations.\n      // As an example, we emit a debugging log message on these tokens.\n      // Note: message are supressed during the first load -- change some lines to see them.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, { token: \"annotation\", log: \"annotation token: $0\" }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      [/\"\"\"/, { token: \"string\", next: \"@mlstring\", nextEmbedded: \"markdown\" }],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    mlstring: [\n      [/[^\"]+/, \"string\"],\n      ['\"\"\"', { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/#.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA2BA;AApCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,QAC1D,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,MACxD;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,QAC5B,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AACA,IAAI,WAAW;AAAA;AAAA,MAEb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO,SAAS,UAAU,WAAW,IAAI;AAAA,MACxD,oBAAoB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MAExC,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,iBAAiB;AAAA,gBACjB,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,cAAc,WAAW;AAAA,UAC1B,CAAC,YAAY,EAAE,OAAO,EAAE,cAAc,YAAY,YAAY,GAAG,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA,UAIpE,CAAC,0BAA0B,EAAE,OAAO,cAAc,KAAK,uBAAuB,CAAC;AAAA;AAAA,UAE/E,CAAC,4BAA4B,cAAc;AAAA,UAC3C,CAAC,qBAAqB,YAAY;AAAA,UAClC,CAAC,OAAO,QAAQ;AAAA;AAAA,UAEhB,CAAC,SAAS,WAAW;AAAA,UACrB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,aAAa,cAAc,WAAW,CAAC;AAAA;AAAA,UAExE,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,UAEpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,QACpE;AAAA,QACA,UAAU;AAAA,UACR,CAAC,SAAS,QAAQ;AAAA,UAClB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,QACjE;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,WAAW,QAAQ;AAAA,UACpB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,QAClE;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,EAAE;AAAA,UACjB,CAAC,QAAQ,SAAS;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
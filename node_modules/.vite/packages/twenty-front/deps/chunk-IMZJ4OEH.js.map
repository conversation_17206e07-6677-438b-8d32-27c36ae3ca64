{"version": 3, "sources": ["../../../../date-fns/esm/locale/fi/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/fi/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/fi/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/fi/_lib/localize/index.js", "../../../../date-fns/esm/locale/fi/_lib/match/index.js", "../../../../date-fns/esm/locale/fi/index.js"], "sourcesContent": ["function futureSeconds(text) {\n  return text.replace(/sekuntia?/, 'sekunnin');\n}\nfunction futureMinutes(text) {\n  return text.replace(/minuuttia?/, 'minuutin');\n}\nfunction futureHours(text) {\n  return text.replace(/tuntia?/, 'tunnin');\n}\nfunction futureDays(text) {\n  return text.replace(/päivää?/, 'päivän');\n}\nfunction futureWeeks(text) {\n  return text.replace(/(viikko|viikkoa)/, 'viikon');\n}\nfunction futureMonths(text) {\n  return text.replace(/(kuukausi|kuukautta)/, 'kuukauden');\n}\nfunction futureYears(text) {\n  return text.replace(/(vuosi|vuotta)/, 'vuoden');\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'alle sekunti',\n    other: 'alle {{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  xSeconds: {\n    one: 'sekunti',\n    other: '{{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  halfAMinute: {\n    one: 'puoli minuuttia',\n    other: 'puoli minuuttia',\n    futureTense: function futureTense(_text) {\n      return 'puolen minuutin';\n    }\n  },\n  lessThanXMinutes: {\n    one: 'alle minuutti',\n    other: 'alle {{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  xMinutes: {\n    one: 'minuutti',\n    other: '{{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  aboutXHours: {\n    one: 'noin tunti',\n    other: 'noin {{count}} tuntia',\n    futureTense: futureHours\n  },\n  xHours: {\n    one: 'tunti',\n    other: '{{count}} tuntia',\n    futureTense: futureHours\n  },\n  xDays: {\n    one: 'päivä',\n    other: '{{count}} päivää',\n    futureTense: futureDays\n  },\n  aboutXWeeks: {\n    one: 'noin viikko',\n    other: 'noin {{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  xWeeks: {\n    one: 'viikko',\n    other: '{{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  aboutXMonths: {\n    one: 'noin kuukausi',\n    other: 'noin {{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  xMonths: {\n    one: 'kuukausi',\n    other: '{{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  aboutXYears: {\n    one: 'noin vuosi',\n    other: 'noin {{count}} vuotta',\n    futureTense: futureYears\n  },\n  xYears: {\n    one: 'vuosi',\n    other: '{{count}} vuotta',\n    futureTense: futureYears\n  },\n  overXYears: {\n    one: 'yli vuosi',\n    other: 'yli {{count}} vuotta',\n    futureTense: futureYears\n  },\n  almostXYears: {\n    one: 'lähes vuosi',\n    other: 'lähes {{count}} vuotta',\n    futureTense: futureYears\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = count === 1 ? tokenValue.one : tokenValue.other.replace('{{count}}', String(count));\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return tokenValue.futureTense(result) + ' kuluttua';\n    } else {\n      return result + ' sitten';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'eeee d. MMMM y',\n  long: 'd. MMMM y',\n  medium: 'd. MMM y',\n  short: 'd.M.y'\n};\nvar timeFormats = {\n  full: 'HH.mm.ss zzzz',\n  long: 'HH.mm.ss z',\n  medium: 'HH.mm.ss',\n  short: 'HH.mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'klo' {{time}}\",\n  long: \"{{date}} 'klo' {{time}}\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "var formatRelativeLocale = {\n  lastWeek: \"'viime' eeee 'klo' p\",\n  yesterday: \"'eilen klo' p\",\n  today: \"'tänään klo' p\",\n  tomorrow: \"'huomenna klo' p\",\n  nextWeek: \"'ensi' eeee 'klo' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['eaa.', 'jaa.'],\n  abbreviated: ['eaa.', 'jaa.'],\n  wide: ['ennen ajanlaskun alkua', 'jä<PERSON>een ajanlaskun alun']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. kvartaali', '2. kvartaali', '3. kvartaali', '4. kvartaali']\n};\nvar monthValues = {\n  narrow: ['T', 'H', 'M', 'H', 'T', 'K', 'H', 'E', 'S', 'L', 'M', 'J'],\n  abbreviated: ['tammi', 'helmi', 'maalis', 'huhti', 'touko', 'kesä', 'heinä', 'elo', 'syys', 'loka', 'marras', 'joulu'],\n  wide: ['tammikuu', 'helmikuu', 'maaliskuu', 'huhtikuu', 'toukokuu', 'kesäkuu', 'heinäkuu', 'elokuu', 'syyskuu', 'lokakuu', 'marraskuu', 'joulukuu']\n};\nvar formattingMonthValues = {\n  narrow: monthValues.narrow,\n  abbreviated: monthValues.abbreviated,\n  wide: ['tammikuuta', 'helmikuuta', 'maaliskuuta', 'huhtikuuta', 'toukokuuta', 'kesäkuuta', 'heinäkuuta', 'elokuuta', 'syyskuuta', 'lokakuuta', 'marraskuuta', 'joulukuuta']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'K', 'T', 'P', 'L'],\n  short: ['su', 'ma', 'ti', 'ke', 'to', 'pe', 'la'],\n  abbreviated: ['sunn.', 'maan.', 'tiis.', 'kesk.', 'torst.', 'perj.', 'la'],\n  wide: ['sunnuntai', 'maanantai', 'tiistai', 'keskiviikko', 'torstai', 'perjantai', 'lauantai']\n};\nvar formattingDayValues = {\n  narrow: dayValues.narrow,\n  short: dayValues.short,\n  abbreviated: dayValues.abbreviated,\n  wide: ['sunnuntaina', 'maanantaina', 'tiistaina', 'keskiviikkona', 'torstaina', 'perjantaina', 'lauantaina']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ap',\n    pm: 'ip',\n    midnight: 'keskiyö',\n    noon: 'keskipäivä',\n    morning: 'ap',\n    afternoon: 'ip',\n    evening: 'illalla',\n    night: 'yöllä'\n  },\n  abbreviated: {\n    am: 'ap',\n    pm: 'ip',\n    midnight: 'keskiyö',\n    noon: 'keskipäivä',\n    morning: 'ap',\n    afternoon: 'ip',\n    evening: 'illalla',\n    night: 'yöllä'\n  },\n  wide: {\n    am: 'ap',\n    pm: 'ip',\n    midnight: 'keskiyöllä',\n    noon: 'keskipäivällä',\n    morning: 'aamupäivällä',\n    afternoon: 'iltapäivällä',\n    evening: 'illalla',\n    night: 'yöllä'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+)(\\.)/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(e|j)/i,\n  abbreviated: /^(eaa.|jaa.)/i,\n  wide: /^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i\n};\nvar parseEraPatterns = {\n  any: [/^e/i, /^j/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234]\\.? kvartaali/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[thmkeslj]/i,\n  abbreviated: /^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,\n  wide: /^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^t/i, /^h/i, /^m/i, /^h/i, /^t/i, /^k/i, /^h/i, /^e/i, /^s/i, /^l/i, /^m/i, /^j/i],\n  any: [/^ta/i, /^hel/i, /^maa/i, /^hu/i, /^to/i, /^k/i, /^hei/i, /^e/i, /^s/i, /^l/i, /^mar/i, /^j/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smtkpl]/i,\n  short: /^(su|ma|ti|ke|to|pe|la)/i,\n  abbreviated: /^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,\n  wide: /^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i\n};\nvar parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^k/i, /^t/i, /^p/i, /^l/i],\n  any: [/^s/i, /^m/i, /^ti/i, /^k/i, /^to/i, /^p/i, /^l/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,\n  any: /^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ap/i,\n    pm: /^ip/i,\n    midnight: /^keskiyö/i,\n    noon: /^keskipäivä/i,\n    morning: /aamupäivällä/i,\n    afternoon: /iltapäivällä/i,\n    evening: /illalla/i,\n    night: /yöllä/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Finnish locale.\n * @language Finnish\n * @iso-639-2 fin\n * <AUTHOR> [@Pyppe]{@link https://github.com/Pyppe}\n * <AUTHOR> [@mikolajgrzyb]{@link https://github.com/mikolajgrzyb}\n * <AUTHOR> [@sjuvonen]{@link https://github.com/sjuvonen}\n */\nvar locale = {\n  code: 'fi',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;AAAA,SAAS,cAAc,MAAM;AAC3B,SAAO,KAAK,QAAQ,aAAa,UAAU;AAC7C;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,KAAK,QAAQ,cAAc,UAAU;AAC9C;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,QAAQ,WAAW,QAAQ;AACzC;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,QAAQ,WAAW,QAAQ;AACzC;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,QAAQ,oBAAoB,QAAQ;AAClD;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,QAAQ,wBAAwB,WAAW;AACzD;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,QAAQ,kBAAkB,QAAQ;AAChD;AACA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa,SAAS,YAAY,OAAO;AACvC,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AACF;AACA,IAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAClE,MAAI,aAAa,qBAAqB,KAAK;AAC3C,MAAI,SAAS,UAAU,IAAI,WAAW,MAAM,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAC/F,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,WAAW,YAAY,MAAM,IAAI;AAAA,IAC1C,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;ACpHf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;ACjCf,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,OAAO,WAAW,UAAU;AAC9E,SAAO,qBAAqB,KAAK;AACnC;AACA,IAAO,yBAAQ;;;ACVf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,QAAQ,MAAM;AAAA,EACvB,aAAa,CAAC,QAAQ,MAAM;AAAA,EAC5B,MAAM,CAAC,0BAA0B,yBAAyB;AAC5D;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACvE;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,OAAO;AAAA,EACrH,MAAM,CAAC,YAAY,YAAY,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,WAAW,WAAW,aAAa,UAAU;AACpJ;AACA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,YAAY;AAAA,EACpB,aAAa,YAAY;AAAA,EACzB,MAAM,CAAC,cAAc,cAAc,eAAe,cAAc,cAAc,aAAa,cAAc,YAAY,aAAa,aAAa,eAAe,YAAY;AAC5K;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,SAAS,SAAS,SAAS,SAAS,UAAU,SAAS,IAAI;AAAA,EACzE,MAAM,CAAC,aAAa,aAAa,WAAW,eAAe,WAAW,aAAa,UAAU;AAC/F;AACA,IAAI,sBAAsB;AAAA,EACxB,QAAQ,UAAU;AAAA,EAClB,OAAO,UAAU;AAAA,EACjB,aAAa,UAAU;AAAA,EACvB,MAAM,CAAC,eAAe,eAAe,aAAa,iBAAiB,aAAa,eAAe,YAAY;AAC7G;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa,UAAU;AAChE,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,mBAAQ;;;ACjGf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,SAAS,SAAS,QAAQ,QAAQ,OAAO,SAAS,OAAO,OAAO,OAAO,SAAS,KAAK;AACrG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,OAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK;AACzD;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;AClFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["formatDistance", "formatRelative", "ordinalNumber", "valueCallback"]}
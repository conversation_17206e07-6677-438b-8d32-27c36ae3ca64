{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/language/json/jsonMode.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `JSONWorker` instance\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker2) => {\n      return worker2.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia = false) {\n  const len = text.length;\n  let pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    let digits = 0;\n    let value2 = 0;\n    while (digits < count || !exact) {\n      let ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    let start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    let end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    let result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      const ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        const ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            const ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    let code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        const start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          const safeLength = len - 1;\n          let commentClosed = false;\n          while (pos < safeLength) {\n            const ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    let result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: () => pos,\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: () => token,\n    getTokenValue: () => value,\n    getTokenOffset: () => tokenOffset,\n    getTokenLength: () => pos - tokenOffset,\n    getTokenStartLine: () => lineStartOffset,\n    getTokenStartCharacter: () => tokenOffset - prevTokenLineStartOffset,\n    getTokenError: () => scanError\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function(CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\n\n// node_modules/jsonc-parser/lib/esm/impl/string-intern.js\nvar cachedSpaces = new Array(20).fill(0).map((_, index) => {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\nvar ScanError;\n(function(ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function(SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar ParseErrorCode;\n(function(ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class _ParentsStack {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new _ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class _JSONState {\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new _JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof _JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\n        \"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3)\n      );\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(\n      state.getStateData(),\n      scanner.getTokenError(),\n      lastWasColon,\n      parents\n    );\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar worker;\nfunction getWorker() {\n  return new Promise((resolve, reject) => {\n    if (!worker) {\n      return reject(\"JSON not registered!\");\n    }\n    resolve(worker);\n  });\n}\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker2, defaults) {\n    super(languageId, worker2, defaults.onDidChange);\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n        this._resetSchema(model.uri);\n      })\n    );\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        this._resetSchema(event.model.uri);\n      })\n    );\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker2) => {\n      worker2.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\" \", \":\", '\"'])\n        )\n      );\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\nexport {\n  CompletionAdapter,\n  DefinitionAdapter,\n  DiagnosticsAdapter,\n  DocumentColorAdapter,\n  DocumentFormattingEditProvider,\n  DocumentHighlightAdapter,\n  DocumentLinkAdapter,\n  DocumentRangeFormattingEditProvider,\n  DocumentSymbolAdapter,\n  FoldingRangeAdapter,\n  HoverAdapter,\n  ReferenceAdapter,\n  RenameAdapter,\n  SelectionRangeAdapter,\n  WorkerManager,\n  fromPosition,\n  fromRange,\n  getWorker,\n  setupMode,\n  toRange,\n  toTextEdit\n};\n"], "mappings": ";;;;;;;;;AAi0CA,SAAS,WAAW,YAAY;AAC9B,UAAQ,YAAY;AAAA,IAClB,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD,KAAK,mBAAmB;AACtB,aAAO,2BAA2B,eAAe;AAAA,IACnD;AACE,aAAO,2BAA2B,eAAe;AAAA,EACrD;AACF;AACA,SAAS,cAAc,UAAU,MAAM;AACrC,MAAI,OAAO,OAAO,KAAK,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO;AAAA,IACL,UAAU,WAAW,KAAK,QAAQ;AAAA,IAClC,iBAAiB,KAAK,MAAM,MAAM,OAAO;AAAA,IACzC,aAAa,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1C,eAAe,KAAK,MAAM,IAAI,OAAO;AAAA,IACrC,WAAW,KAAK,MAAM,IAAI,YAAY;AAAA,IACtC,SAAS,KAAK;AAAA,IACd;AAAA,IACA,QAAQ,KAAK;AAAA,EACf;AACF;AA8DA,SAAS,aAAa,UAAU;AAC9B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAE;AACzE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM,MAAM,kBAAkB;AAAA,MAC9B,WAAW,MAAM,cAAc;AAAA,IACjC;AAAA,IACA,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAE;AAAA,EACvE;AACF;AACA,SAAS,QAAQ,OAAO;AACtB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,IAAI,2BAA2B;AAAA,IACpC,MAAM,MAAM,OAAO;AAAA,IACnB,MAAM,MAAM,YAAY;AAAA,IACxB,MAAM,IAAI,OAAO;AAAA,IACjB,MAAM,IAAI,YAAY;AAAA,EACxB;AACF;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,OAAO,KAAK,WAAW,eAAe,OAAO,KAAK,YAAY;AACvE;AACA,SAAS,qBAAqB,MAAM;AAClC,QAAM,YAAY,2BAA2B,UAAU;AACvD,UAAQ,MAAM;AAAA,IACZ,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,IACnB,KAAK,mBAAmB;AACtB,aAAO,UAAU;AAAA,EACrB;AACA,SAAO,UAAU;AACnB;AACA,SAAS,WAAW,UAAU;AAC5B,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B,MAAM,SAAS;AAAA,EACjB;AACF;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK,EAAE,YAAY,iCAAiC,EAAE,IAAI,EAAE,SAAS,OAAO,EAAE,OAAO,WAAW,EAAE,UAAU,IAAI;AACzH;AAoBA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AACrE;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,gBAAgB,KAAK,GAAG;AAC1B,QAAI,MAAM,SAAS,aAAa;AAC9B,aAAO;AAAA,QACL,OAAO,MAAM,MAAM,QAAQ,yBAAyB,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO,EAAE,OAAO,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AAC1E;AACA,SAAS,oBAAoB,UAAU;AACrC,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,SAAS,IAAI,gBAAgB;AAAA,EACtC;AACA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACpC;AAoBA,SAAS,wBAAwB,MAAM;AACrC,UAAQ,MAAM;AAAA,IACZ,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,IACpE,KAAK,sBAAsB;AACzB,aAAO,2BAA2B,UAAU,sBAAsB;AAAA,EACtE;AACA,SAAO,2BAA2B,UAAU,sBAAsB;AACpE;AAiBA,SAAS,WAAW,UAAU;AAC5B,SAAO;AAAA,IACL,KAAK,2BAA2B,IAAI,MAAM,SAAS,GAAG;AAAA,IACtD,OAAO,QAAQ,SAAS,KAAK;AAAA,EAC/B;AACF;AA8BA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,CAAC;AACrB,WAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,OAAO,2BAA2B,IAAI,MAAM,GAAG;AACrD,aAAS,KAAK,KAAK,QAAQ,GAAG,GAAG;AAC/B,oBAAc,KAAK;AAAA,QACjB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR,OAAO,QAAQ,EAAE,KAAK;AAAA,UACtB,MAAM,EAAE;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AA4BA,SAAS,iBAAiB,QAAQ;AAChC,SAAO,cAAc;AACvB;AACA,SAAS,iBAAiB,QAAQ;AAChC,SAAO;AAAA,IACL,MAAM,OAAO;AAAA,IACb,QAAQ,OAAO,UAAU;AAAA,IACzB,MAAM,aAAa,OAAO,IAAI;AAAA,IAC9B,OAAO,QAAQ,OAAO,KAAK;AAAA,IAC3B,gBAAgB,QAAQ,OAAO,cAAc;AAAA,IAC7C,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtB,WAAW,OAAO,YAAY,CAAC,GAAG,IAAI,CAAC,SAAS,iBAAiB,IAAI,CAAC;AAAA,EACxE;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,QAAQ,2BAA2B,UAAU;AACjD,UAAQ,MAAM;AAAA,IACZ,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,IACf,KAAK,WAAW;AACd,aAAO,MAAM;AAAA,EACjB;AACA,SAAO,MAAM;AACf;AAqDA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,SAAS,QAAQ;AAAA,IACjB,cAAc,QAAQ;AAAA,EACxB;AACF;AA+DA,SAAS,mBAAmB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACZ,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,IAC/D,KAAK,iBAAiB;AACpB,aAAO,2BAA2B,UAAU,iBAAiB;AAAA,EACjE;AACA,SAAO;AACT;AA6BA,SAAS,cAAc,MAAM,eAAe,OAAO;AACjD,QAAM,MAAM,KAAK;AACjB,MAAI,MAAM,GAAG,QAAQ,IAAI,cAAc,GAAG,QAAQ,IAAI,aAAa,GAAG,kBAAkB,GAAG,uBAAuB,GAAG,2BAA2B,GAAG,YAAY;AAC/J,WAAS,cAAc,OAAO,OAAO;AACnC,QAAI,SAAS;AACb,QAAI,SAAS;AACb,WAAO,SAAS,SAAS,CAAC,OAAO;AAC/B,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM,MAAM,MAAM,IAAI;AACxB,iBAAS,SAAS,KAAK,KAAK;AAAA,MAC9B,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,WAAW,MAAM,MAAM,MAAM,KAAK;AAChC,iBAAS,SAAS,KAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL;AAAA,MACF;AACA;AACA;AAAA,IACF;AACA,QAAI,SAAS,OAAO;AAClB,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAY,aAAa;AAChC,UAAM;AACN,YAAQ;AACR,kBAAc;AACd,YAAQ;AACR,gBAAY;AAAA,EACd;AACA,WAAS,aAAa;AACpB,QAAI,QAAQ;AACZ,QAAI,KAAK,WAAW,GAAG,MAAM,IAAI;AAC/B;AAAA,IACF,OAAO;AACL;AACA,aAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,IAAI;AACpD;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AAAA,MACF,OAAO;AACL,oBAAY;AACZ,eAAO,KAAK,UAAU,OAAO,GAAG;AAAA,MAClC;AAAA,IACF;AACA,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM;AACtF;AACA,UAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AACnF;AAAA,MACF;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACtD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACzD;AAAA,QACF;AACA,cAAM;AAAA,MACR,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,KAAK,UAAU,OAAO,GAAG;AAAA,EAClC;AACA,WAAS,aAAa;AACpB,QAAI,SAAS,IAAI,QAAQ;AACzB,WAAO,MAAM;AACX,UAAI,OAAO,KAAK;AACd,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,oBAAY;AACZ;AAAA,MACF;AACA,YAAM,KAAK,KAAK,WAAW,GAAG;AAC9B,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA;AAAA,MACF;AACA,UAAI,OAAO,IAAI;AACb,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA,YAAI,OAAO,KAAK;AACd,sBAAY;AACZ;AAAA,QACF;AACA,cAAM,MAAM,KAAK,WAAW,KAAK;AACjC,gBAAQ,KAAK;AAAA,UACX,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,kBAAM,MAAM,cAAc,GAAG,IAAI;AACjC,gBAAI,OAAO,GAAG;AACZ,wBAAU,OAAO,aAAa,GAAG;AAAA,YACnC,OAAO;AACL,0BAAY;AAAA,YACd;AACA;AAAA,UACF;AACE,wBAAY;AAAA,QAChB;AACA,gBAAQ;AACR;AAAA,MACF;AACA,UAAI,MAAM,KAAK,MAAM,IAAI;AACvB,YAAI,YAAY,EAAE,GAAG;AACnB,oBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,sBAAY;AACZ;AAAA,QACF,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,WAAW;AAClB,YAAQ;AACR,gBAAY;AACZ,kBAAc;AACd,sBAAkB;AAClB,+BAA2B;AAC3B,QAAI,OAAO,KAAK;AACd,oBAAc;AACd,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,OAAO,KAAK,WAAW,GAAG;AAC9B,QAAI,aAAa,IAAI,GAAG;AACtB,SAAG;AACD;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC5B,SAAS,aAAa,IAAI;AAC1B,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,YAAY,IAAI,GAAG;AACrB;AACA,eAAS,OAAO,aAAa,IAAI;AACjC,UAAI,SAAS,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC9C;AACA,iBAAS;AAAA,MACX;AACA;AACA,6BAAuB;AACvB,aAAO,QAAQ;AAAA,IACjB;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH;AACA,gBAAQ,WAAW;AACnB,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,cAAM,QAAQ,MAAM;AACpB,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,iBAAO,MAAM,KAAK;AAChB,gBAAI,YAAY,KAAK,WAAW,GAAG,CAAC,GAAG;AACrC;AAAA,YACF;AACA;AAAA,UACF;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AACnC,iBAAO;AACP,gBAAM,aAAa,MAAM;AACzB,cAAI,gBAAgB;AACpB,iBAAO,MAAM,YAAY;AACvB,kBAAM,KAAK,KAAK,WAAW,GAAG;AAC9B,gBAAI,OAAO,MAAM,KAAK,WAAW,MAAM,CAAC,MAAM,IAAI;AAChD,qBAAO;AACP,8BAAgB;AAChB;AAAA,YACF;AACA;AACA,gBAAI,YAAY,EAAE,GAAG;AACnB,kBAAI,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI;AAC5C;AAAA,cACF;AACA;AACA,qCAAuB;AAAA,YACzB;AAAA,UACF;AACA,cAAI,CAAC,eAAe;AAClB;AACA,wBAAY;AAAA,UACd;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,YAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACjD,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,WAAW;AACpB,eAAO,QAAQ;AAAA,MACjB;AACE,eAAO,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACnD;AACA,iBAAO,KAAK,WAAW,GAAG;AAAA,QAC5B;AACA,YAAI,gBAAgB,KAAK;AACvB,kBAAQ,KAAK,UAAU,aAAa,GAAG;AACvC,kBAAQ,OAAO;AAAA,YACb,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,YACjB,KAAK;AACH,qBAAO,QAAQ;AAAA,UACnB;AACA,iBAAO,QAAQ;AAAA,QACjB;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,WAAS,0BAA0B,MAAM;AACvC,QAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,WAAS,oBAAoB;AAC3B,QAAI;AACJ,OAAG;AACD,eAAS,SAAS;AAAA,IACpB,SAAS,UAAU,MAAM,UAAU;AACnC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,MAAM;AAAA,IACnB,MAAM,eAAe,oBAAoB;AAAA,IACzC,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,IACrB,gBAAgB,MAAM;AAAA,IACtB,gBAAgB,MAAM,MAAM;AAAA,IAC5B,mBAAmB,MAAM;AAAA,IACzB,wBAAwB,MAAM,cAAc;AAAA,IAC5C,eAAe,MAAM;AAAA,EACvB;AACF;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAAS,YAAY,IAAI;AACvB,SAAO,OAAO,MAAM,OAAO;AAC7B;AACA,SAAS,QAAQ,IAAI;AACnB,SAAO,MAAM,MAAM,MAAM;AAC3B;AAgLA,SAAS,0BAA0B,iBAAiB;AAClD,SAAO;AAAA,IACL,iBAAiB,MAAM,IAAI,UAAU,MAAM,MAAM,OAAO,IAAI;AAAA,IAC5D,UAAU,CAAC,MAAM,UAAU,SAAS,iBAAiB,MAAM,KAAK;AAAA,EAClE;AACF;AAwEA,SAAS,SAAS,UAAU,MAAM,OAAO,cAAc,GAAG;AACxD,MAAI,6BAA6B;AACjC,MAAI,eAAe;AACnB,UAAQ,MAAM,WAAW;AAAA,IACvB,KAAK;AACH,aAAO,MAAM;AACb,mCAA6B;AAC7B;AAAA,IACF,KAAK;AACH,aAAO,OAAO;AACd,mCAA6B;AAC7B;AAAA,EACJ;AACA,QAAM,UAAU,eAAe,IAAI;AACnC,MAAI,eAAe,MAAM;AACzB,MAAI,UAAU,MAAM;AACpB,QAAM,MAAM;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,UAAU,MAAM,MAAM;AAAA,EACxB;AACA,SAAO,MAAM;AACX,QAAI,SAAS,cAAc,QAAQ,YAAY;AAC/C,QAAI,OAAO;AACX,UAAM,OAAO,QAAQ,KAAK;AAC1B,QAAI,SAAS,IAAc;AACzB;AAAA,IACF;AACA,QAAI,WAAW,cAAc,QAAQ,YAAY,GAAG;AAClD,YAAM,IAAI;AAAA,QACR,qDAAqD,KAAK,OAAO,QAAQ,YAAY,GAAG,CAAC;AAAA,MAC3F;AAAA,IACF;AACA,QAAI,cAAc;AAChB,gBAAU;AAAA,IACZ;AACA,mBAAe,6BAA6B;AAC5C,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAc;AACnD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAa;AAClD,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,MACF,KAAK;AACH,cAAM,gBAAgB,UAAU,QAAQ,OAAO;AAC/C,cAAM,UAAU,kBAAkB;AAClC,eAAO,gBAAgB,UAAU,qBAAqB;AACtD,uBAAe;AACf;AAAA,MACF,KAAK;AACH,eAAO;AACP,uBAAe;AACf;AAAA,IACJ;AACA,QAAI,UAAU;AACZ,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AACP;AAAA,QACF,KAAK;AACH,iBAAO;AACP;AAAA,MACJ;AAAA,IACF;AACA,QAAI,WAAW,IAAI;AAAA,MACjB,MAAM,aAAa;AAAA,MACnB,QAAQ,cAAc;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAAA,MACd,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,SAAS,YAAY;AACnB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,QAAQ;AACX,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,YAAQ,MAAM;AAAA,EAChB,CAAC;AACH;AAqBA,SAAS,UAAU,UAAU;AAC3B,QAAM,cAAc,CAAC;AACrB,QAAM,YAAY,CAAC;AACnB,QAAM,SAAS,IAAI,cAAc,QAAQ;AACzC,cAAY,KAAK,MAAM;AACvB,WAAS,IAAI,SAAS;AACpB,WAAO,OAAO,yBAAyB,GAAG,IAAI;AAAA,EAChD;AACA,WAAS,oBAAoB;AAC3B,UAAM,EAAE,YAAY,mBAAmB,mBAAmB,IAAI;AAC9D,eAAW,SAAS;AACpB,QAAI,mBAAmB,yBAAyB;AAC9C,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,+BAA+B,MAAM;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,8BAA8B;AACnD,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,oCAAoC,MAAM;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,kBAAkB,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU;AAAA,QACR,2BAA2B,UAAU,sBAAsB,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,MACjG;AAAA,IACF;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,sBAAsB,MAAM;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU,KAAK,2BAA2B,UAAU,kBAAkB,YAAY,0BAA0B,IAAI,CAAC,CAAC;AAAA,IACpH;AACA,QAAI,mBAAmB,QAAQ;AAC7B,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,qBAAqB,MAAM;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,eAAe;AACpC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,oBAAoB,MAAM;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAmB,aAAa;AAClC,gBAAU,KAAK,IAAI,uBAAuB,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACzE;AACA,QAAI,mBAAmB,iBAAiB;AACtC,gBAAU;AAAA,QACR,2BAA2B,UAAU;AAAA,UACnC;AAAA,UACA,IAAI,sBAAsB,MAAM;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,oBAAkB;AAClB,cAAY,KAAK,2BAA2B,UAAU,yBAAyB,SAAS,YAAY,qBAAqB,CAAC;AAC1H,MAAI,oBAAoB,SAAS;AACjC,WAAS,YAAY,CAAC,gBAAgB;AACpC,QAAI,YAAY,sBAAsB,mBAAmB;AACvD,0BAAoB,YAAY;AAChC,wBAAkB;AAAA,IACpB;AAAA,EACF,CAAC;AACD,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO,aAAa,WAAW;AACjC;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,EAAE,SAAS,MAAM,WAAW,WAAW,EAAE;AAClD;AACA,SAAS,WAAW,aAAa;AAC/B,SAAO,YAAY,QAAQ;AACzB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC5B;AACF;AA1qFA,IAOI,WACA,kBACA,mBACA,cACA,aAQA,YAGA,4BAKA,oBACA,eA6DA,aAOA,KAOA,SASA,UASA,UAkBA,OAkBA,UAYA,cAYA,OAiBA,kBAeA,mBAgBA,kBAMA,cA4BA,8BAeA,oBAOA,eAKA,iBAQA,YA0BA,SAgBA,UAoBA,kBAmBA,4BAQA,mBAoBA,kBAYA,YAsBA,YAuBA,YAsBA,eAcA,wBAYA,iCAYA,yCAYA,kBAYA,YAUA,eAQA,oBA4BA,kBAKA,mBAIA,mBAYA,gBAKA,4BAQA,gBAOA,gBAOA,cAYA,OAQA,sBAOA,sBAgBA,uBAMA,mBAWA,YA6BA,WAIA,mBAeA,iBAOA,gBAsBA,gBAYA,uBAKA,mBAmBA,YAyBA,UAgBA,mBAYA,cAYA,gBAYA,oBA0BA,wBAaA,gBAQA,iBAYA,2BAYA,kCAYA,oBAYA,eASA,oBAYA,WAgBA,aAOA,sBAOA,sBAOA,6BAKA,wBAOA,yBAOA,iBAQA,cAgEA,kBAsFA,IAkDA,oBAoGA,mBAmJA,cAiDA,0BA8BA,mBAsBA,kBAgBA,eAmCA,uBAmFA,qBAmBA,gCAgBA,qCAuBA,sBAuCA,qBAkCA,uBAoWA,gBAqFA,cAGA,iBACA,4BA0BA,cAQA,gBACA,WAUA,YAoBA,gBA2BA,oBACA,mBACA,mBACA,mBACA,qBACA,kBACA,oBACA,oBACA,qBACA,qBACA,oBACA,cAkCA,WAwIA,QASA,wBAuHA;AA3qFJ;AAAA;AAwBA;AAjBA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,eAAW,4BAA4B,kBAAuB;AAI9D,IAAI,qBAAqB,IAAI,KAAK;AAClC,IAAI,gBAAgB,MAAM;AAAA,MACxB,YAAY,UAAU;AACpB,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,qBAAqB,OAAO,YAAY,MAAM,KAAK,aAAa,GAAG,KAAK,GAAG;AAChF,aAAK,gBAAgB;AACrB,aAAK,wBAAwB,KAAK,UAAU,YAAY,MAAM,KAAK,YAAY,CAAC;AAAA,MAClF;AAAA,MACA,cAAc;AACZ,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AACA,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,UAAU;AACR,sBAAc,KAAK,kBAAkB;AACrC,aAAK,sBAAsB,QAAQ;AACnC,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,eAAe;AACb,YAAI,CAAC,KAAK,SAAS;AACjB;AAAA,QACF;AACA,YAAI,0BAA0B,KAAK,IAAI,IAAI,KAAK;AAChD,YAAI,0BAA0B,oBAAoB;AAChD,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA,MACA,aAAa;AACX,aAAK,gBAAgB,KAAK,IAAI;AAC9B,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK,UAAU,2BAA2B,OAAO,gBAAgB;AAAA;AAAA,YAE/D,UAAU;AAAA,YACV,OAAO,KAAK,UAAU;AAAA;AAAA,YAEtB,YAAY;AAAA,cACV,kBAAkB,KAAK,UAAU;AAAA,cACjC,YAAY,KAAK,UAAU;AAAA,cAC3B,qBAAqB,KAAK,UAAU,mBAAmB;AAAA,YACzD;AAAA,UACF,CAAC;AACD,eAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,QACvC;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MACA,4BAA4B,WAAW;AACrC,YAAI;AACJ,eAAO,KAAK,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,oBAAU;AAAA,QACZ,CAAC,EAAE,KAAK,CAAC,MAAM;AACb,cAAI,KAAK,SAAS;AAChB,mBAAO,KAAK,QAAQ,oBAAoB,SAAS;AAAA,UACnD;AAAA,QACF,CAAC,EAAE,KAAK,CAAC,MAAM,OAAO;AAAA,MACxB;AAAA,IACF;AAIA,KAAC,SAAS,cAAc;AACtB,eAAS,GAAG,OAAO;AACjB,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,mBAAa,KAAK;AAAA,IACpB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAEpC,KAAC,SAAS,MAAM;AACd,eAAS,GAAG,OAAO;AACjB,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,WAAK,KAAK;AAAA,IACZ,GAAG,QAAQ,MAAM,CAAC,EAAE;AAEpB,KAAC,SAAS,UAAU;AAClB,eAAS,YAAY;AACrB,eAAS,YAAY;AACrB,eAAS,GAAG,OAAO;AACjB,eAAO,OAAO,UAAU,YAAY,SAAS,aAAa,SAAS,SAAS,SAAS;AAAA,MACvF;AACA,eAAS,KAAK;AAAA,IAChB,GAAG,YAAY,UAAU,CAAC,EAAE;AAE5B,KAAC,SAAS,WAAW;AACnB,gBAAU,YAAY;AACtB,gBAAU,YAAY;AACtB,eAAS,GAAG,OAAO;AACjB,eAAO,OAAO,UAAU,YAAY,UAAU,aAAa,SAAS,SAAS,UAAU;AAAA,MACzF;AACA,gBAAU,KAAK;AAAA,IACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,KAAC,SAAS,WAAW;AACnB,eAAS,OAAO,MAAM,WAAW;AAC/B,YAAI,SAAS,OAAO,WAAW;AAC7B,iBAAO,SAAS;AAAA,QAClB;AACA,YAAI,cAAc,OAAO,WAAW;AAClC,sBAAY,SAAS;AAAA,QACvB;AACA,eAAO,EAAE,MAAM,UAAU;AAAA,MAC3B;AACA,gBAAU,SAAS;AACnB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,MACtG;AACA,gBAAU,KAAK;AAAA,IACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,KAAC,SAAS,QAAQ;AAChB,eAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACrC,YAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACnF,iBAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,QAC/E,WAAW,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC/C,iBAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,QAChC,OAAO;AACL,gBAAM,IAAI,MAAM,8CAA8C,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,IAAI,GAAG;AAAA,QACjG;AAAA,MACF;AACA,aAAO,SAAS;AAChB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,MACjG;AACA,aAAO,KAAK;AAAA,IACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AAExB,KAAC,SAAS,WAAW;AACnB,eAAS,OAAO,KAAK,OAAO;AAC1B,eAAO,EAAE,KAAK,MAAM;AAAA,MACtB;AACA,gBAAU,SAAS;AACnB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,MAC5H;AACA,gBAAU,KAAK;AAAA,IACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,KAAC,SAAS,eAAe;AACvB,eAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAClF,eAAO,EAAE,WAAW,aAAa,sBAAsB,qBAAqB;AAAA,MAC9E;AACA,oBAAc,SAAS;AACvB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,KAAK,MAAM,GAAG,UAAU,oBAAoB,MAAM,MAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,MACjP;AACA,oBAAc,KAAK;AAAA,IACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,KAAC,SAAS,QAAQ;AAChB,eAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACvC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO,SAAS;AAChB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KAAK,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,MACpM;AACA,aAAO,KAAK;AAAA,IACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AAExB,KAAC,SAAS,mBAAmB;AAC3B,eAAS,OAAO,OAAO,OAAO;AAC5B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,wBAAkB,SAAS;AAC3B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,MAC7F;AACA,wBAAkB,KAAK;AAAA,IACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,OAAO,UAAU,qBAAqB;AACpD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,yBAAmB,SAAS;AAC5B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OAAO,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,MAC9O;AACA,yBAAmB,KAAK;AAAA,IAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,mBAAmB;AAC3B,wBAAkB,UAAU;AAC5B,wBAAkB,UAAU;AAC5B,wBAAkB,SAAS;AAAA,IAC7B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,eAAe;AACvB,eAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM,eAAe;AACrF,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA,YAAI,GAAG,QAAQ,cAAc,GAAG;AAC9B,iBAAO,iBAAiB;AAAA,QAC1B;AACA,YAAI,GAAG,QAAQ,YAAY,GAAG;AAC5B,iBAAO,eAAe;AAAA,QACxB;AACA,YAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,iBAAO,OAAO;AAAA,QAChB;AACA,YAAI,GAAG,QAAQ,aAAa,GAAG;AAC7B,iBAAO,gBAAgB;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AACA,oBAAc,SAAS;AACvB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MAAM,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAAO,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAAO,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,MAC/U;AACA,oBAAc,KAAK;AAAA,IACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,KAAC,SAAS,+BAA+B;AACvC,eAAS,OAAO,UAAU,SAAS;AACjC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,oCAA8B,SAAS;AACvC,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,MAChG;AACA,oCAA8B,KAAK;AAAA,IACrC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AAEtE,KAAC,SAAS,qBAAqB;AAC7B,0BAAoB,QAAQ;AAC5B,0BAAoB,UAAU;AAC9B,0BAAoB,cAAc;AAClC,0BAAoB,OAAO;AAAA,IAC7B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAElD,KAAC,SAAS,gBAAgB;AACxB,qBAAe,cAAc;AAC7B,qBAAe,aAAa;AAAA,IAC9B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,KAAC,SAAS,kBAAkB;AAC1B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,MAChE;AACA,uBAAiB,KAAK;AAAA,IACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAE5C,KAAC,SAAS,aAAa;AACrB,eAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AAC1E,YAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,YAAI,GAAG,QAAQ,QAAQ,GAAG;AACxB,iBAAO,WAAW;AAAA,QACpB;AACA,YAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,iBAAO,OAAO;AAAA,QAChB;AACA,YAAI,GAAG,QAAQ,MAAM,GAAG;AACtB,iBAAO,SAAS;AAAA,QAClB;AACA,YAAI,GAAG,QAAQ,kBAAkB,GAAG;AAClC,iBAAO,qBAAqB;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AACA,kBAAY,SAAS;AACrB,eAAS,GAAG,OAAO;AACjB,YAAI;AACJ,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO,MAAM,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAAO,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OAAO,GAAG,UAAU,UAAU,eAAe,KAAK,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAO,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAAO,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,MACzkB;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,UAAU;AAClB,eAAS,OAAO,OAAO,YAAY,MAAM;AACvC,YAAI,SAAS,EAAE,OAAO,QAAQ;AAC9B,YAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACvC,iBAAO,YAAY;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AACA,eAAS,SAAS;AAClB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,MAC3F;AACA,eAAS,KAAK;AAAA,IAChB,GAAG,YAAY,UAAU,CAAC,EAAE;AAE5B,KAAC,SAAS,WAAW;AACnB,eAAS,QAAQ,OAAO,SAAS;AAC/B,eAAO,EAAE,OAAO,QAAQ;AAAA,MAC1B;AACA,gBAAU,UAAU;AACpB,eAAS,OAAO,UAAU,SAAS;AACjC,eAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAQ;AAAA,MAC9D;AACA,gBAAU,SAAS;AACnB,eAAS,IAAI,OAAO;AAClB,eAAO,EAAE,OAAO,SAAS,GAAG;AAAA,MAC9B;AACA,gBAAU,MAAM;AAChB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,MAChG;AACA,gBAAU,KAAK;AAAA,IACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,KAAC,SAAS,mBAAmB;AAC3B,eAAS,OAAO,OAAO,mBAAmB,aAAa;AACrD,cAAM,SAAS,EAAE,MAAM;AACvB,YAAI,sBAAsB,QAAQ;AAChC,iBAAO,oBAAoB;AAAA,QAC7B;AACA,YAAI,gBAAgB,QAAQ;AAC1B,iBAAO,cAAc;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,wBAAkB,SAAS;AAC3B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MAAM,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAAY,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,MAC5N;AACA,wBAAkB,KAAK;AAAA,IACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,6BAA6B;AACrC,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,OAAO,SAAS;AAAA,MAC5B;AACA,kCAA4B,KAAK;AAAA,IACnC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAElE,KAAC,SAAS,oBAAoB;AAC5B,eAAS,QAAQ,OAAO,SAAS,YAAY;AAC3C,eAAO,EAAE,OAAO,SAAS,cAAc,WAAW;AAAA,MACpD;AACA,yBAAmB,UAAU;AAC7B,eAAS,OAAO,UAAU,SAAS,YAAY;AAC7C,eAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAS,cAAc,WAAW;AAAA,MACxF;AACA,yBAAmB,SAAS;AAC5B,eAAS,IAAI,OAAO,YAAY;AAC9B,eAAO,EAAE,OAAO,SAAS,IAAI,cAAc,WAAW;AAAA,MACxD;AACA,yBAAmB,MAAM;AACzB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,MACvI;AACA,yBAAmB,KAAK;AAAA,IAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,mBAAmB;AAC3B,eAAS,OAAO,cAAc,OAAO;AACnC,eAAO,EAAE,cAAc,MAAM;AAAA,MAC/B;AACA,wBAAkB,SAAS;AAC3B,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,wCAAwC,GAAG,UAAU,YAAY,KAAK,MAAM,QAAQ,UAAU,KAAK;AAAA,MACrI;AACA,wBAAkB,KAAK;AAAA,IACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,aAAa;AACrB,eAAS,OAAO,KAAK,SAAS,YAAY;AACxC,YAAI,SAAS;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AACA,YAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,eAAe,QAAQ;AACzB,iBAAO,eAAe;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AACA,kBAAY,SAAS;AACrB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,MACrY;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,aAAa;AACrB,eAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACnD,YAAI,SAAS;AAAA,UACX,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF;AACA,YAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,mBAAmB,SAAS;AAC7F,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,eAAe,QAAQ;AACzB,iBAAO,eAAe;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AACA,kBAAY,SAAS;AACrB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAU,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,MACva;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,aAAa;AACrB,eAAS,OAAO,KAAK,SAAS,YAAY;AACxC,YAAI,SAAS;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AACA,YAAI,YAAY,WAAW,QAAQ,cAAc,UAAU,QAAQ,sBAAsB,SAAS;AAChG,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,eAAe,QAAQ;AACzB,iBAAO,eAAe;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AACA,kBAAY,SAAS;AACrB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAAW,UAAU,QAAQ,cAAc,UAAU,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAU,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAQ,UAAU,iBAAiB,UAAU,2BAA2B,GAAG,UAAU,YAAY;AAAA,MAC3Y;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,gBAAgB;AACxB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,cAAc,UAAU,YAAY,UAAU,UAAU,oBAAoB,YAAY,UAAU,oBAAoB,UAAU,UAAU,gBAAgB,MAAM,CAAC,WAAW;AACjL,cAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AAC1B,mBAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,UAC/E,OAAO;AACL,mBAAO,iBAAiB,GAAG,MAAM;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AACA,qBAAe,KAAK;AAAA,IACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,KAAC,SAAS,yBAAyB;AACjC,eAAS,OAAO,KAAK;AACnB,eAAO,EAAE,IAAI;AAAA,MACf;AACA,8BAAwB,SAAS;AACjC,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,MACzD;AACA,8BAAwB,KAAK;AAAA,IAC/B,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAE1D,KAAC,SAAS,kCAAkC;AAC1C,eAAS,OAAO,KAAK,SAAS;AAC5B,eAAO,EAAE,KAAK,QAAQ;AAAA,MACxB;AACA,uCAAiC,SAAS;AAC1C,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,MAC1F;AACA,uCAAiC,KAAK;AAAA,IACxC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAE5E,KAAC,SAAS,0CAA0C;AAClD,eAAS,OAAO,KAAK,SAAS;AAC5B,eAAO,EAAE,KAAK,QAAQ;AAAA,MACxB;AACA,+CAAyC,SAAS;AAClD,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,MACzH;AACA,+CAAyC,KAAK;AAAA,IAChD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAE5F,KAAC,SAAS,mBAAmB;AAC3B,eAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC9C,eAAO,EAAE,KAAK,YAAY,SAAS,KAAK;AAAA,MAC1C;AACA,wBAAkB,SAAS;AAC3B,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,MAC1J;AACA,wBAAkB,KAAK;AAAA,IACzB,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,aAAa;AACrB,kBAAY,YAAY;AACxB,kBAAY,WAAW;AACvB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,cAAc,YAAY,aAAa,cAAc,YAAY;AAAA,MAC1E;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,gBAAgB;AACxB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,MAC9F;AACA,qBAAe,KAAK;AAAA,IACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,KAAC,SAAS,qBAAqB;AAC7B,0BAAoB,OAAO;AAC3B,0BAAoB,SAAS;AAC7B,0BAAoB,WAAW;AAC/B,0BAAoB,cAAc;AAClC,0BAAoB,QAAQ;AAC5B,0BAAoB,WAAW;AAC/B,0BAAoB,QAAQ;AAC5B,0BAAoB,YAAY;AAChC,0BAAoB,SAAS;AAC7B,0BAAoB,WAAW;AAC/B,0BAAoB,OAAO;AAC3B,0BAAoB,QAAQ;AAC5B,0BAAoB,OAAO;AAC3B,0BAAoB,UAAU;AAC9B,0BAAoB,UAAU;AAC9B,0BAAoB,QAAQ;AAC5B,0BAAoB,OAAO;AAC3B,0BAAoB,YAAY;AAChC,0BAAoB,SAAS;AAC7B,0BAAoB,aAAa;AACjC,0BAAoB,WAAW;AAC/B,0BAAoB,SAAS;AAC7B,0BAAoB,QAAQ;AAC5B,0BAAoB,WAAW;AAC/B,0BAAoB,gBAAgB;AAAA,IACtC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAElD,KAAC,SAAS,mBAAmB;AAC3B,wBAAkB,YAAY;AAC9B,wBAAkB,UAAU;AAAA,IAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,KAAC,SAAS,oBAAoB;AAC5B,yBAAmB,aAAa;AAAA,IAClC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,SAAS,QAAQ,SAAS;AACxC,eAAO,EAAE,SAAS,QAAQ,QAAQ;AAAA,MACpC;AACA,yBAAmB,SAAS;AAC5B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAK,MAAM,GAAG,UAAU,MAAM,KAAK,MAAM,GAAG,UAAU,OAAO;AAAA,MAC9G;AACA,yBAAmB,KAAK;AAAA,IAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,iBAAiB;AACzB,sBAAgB,OAAO;AACvB,sBAAgB,oBAAoB;AAAA,IACtC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,6BAA6B;AACrC,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,cAAc,GAAG,OAAO,UAAU,MAAM,KAAK,UAAU,WAAW,YAAY,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,MACrJ;AACA,kCAA4B,KAAK;AAAA,IACnC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAElE,KAAC,SAAS,iBAAiB;AACzB,eAAS,OAAO,OAAO;AACrB,eAAO,EAAE,MAAM;AAAA,MACjB;AACA,sBAAgB,SAAS;AAAA,IAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,iBAAiB;AACzB,eAAS,OAAO,OAAO,cAAc;AACnC,eAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,MACnE;AACA,sBAAgB,SAAS;AAAA,IAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,eAAe;AACvB,eAAS,cAAc,WAAW;AAChC,eAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,MAC1D;AACA,oBAAc,gBAAgB;AAC9B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,OAAO,SAAS,KAAK,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,MAC1H;AACA,oBAAc,KAAK;AAAA,IACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,KAAC,SAAS,QAAQ;AAChB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KAAK,aAAa,GAAG,UAAU,QAAQ,KAAK,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAU,MAAM,GAAG,MAAM,KAAK;AAAA,MAC7O;AACA,aAAO,KAAK;AAAA,IACd,GAAG,UAAU,QAAQ,CAAC,EAAE;AAExB,KAAC,SAAS,uBAAuB;AAC/B,eAAS,OAAO,OAAO,eAAe;AACpC,eAAO,gBAAgB,EAAE,OAAO,cAAc,IAAI,EAAE,MAAM;AAAA,MAC5D;AACA,4BAAsB,SAAS;AAAA,IACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,KAAC,SAAS,uBAAuB;AAC/B,eAAS,OAAO,OAAO,kBAAkB,YAAY;AACnD,YAAI,SAAS,EAAE,MAAM;AACrB,YAAI,GAAG,QAAQ,aAAa,GAAG;AAC7B,iBAAO,gBAAgB;AAAA,QACzB;AACA,YAAI,GAAG,QAAQ,UAAU,GAAG;AAC1B,iBAAO,aAAa;AAAA,QACtB,OAAO;AACL,iBAAO,aAAa,CAAC;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,4BAAsB,SAAS;AAAA,IACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,KAAC,SAAS,wBAAwB;AAChC,6BAAuB,OAAO;AAC9B,6BAAuB,OAAO;AAC9B,6BAAuB,QAAQ;AAAA,IACjC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAExD,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,OAAO,MAAM;AAC3B,YAAI,SAAS,EAAE,MAAM;AACrB,YAAI,GAAG,OAAO,IAAI,GAAG;AACnB,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,yBAAmB,SAAS;AAAA,IAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,aAAa;AACrB,kBAAY,OAAO;AACnB,kBAAY,SAAS;AACrB,kBAAY,YAAY;AACxB,kBAAY,UAAU;AACtB,kBAAY,QAAQ;AACpB,kBAAY,SAAS;AACrB,kBAAY,WAAW;AACvB,kBAAY,QAAQ;AACpB,kBAAY,cAAc;AAC1B,kBAAY,OAAO;AACnB,kBAAY,YAAY;AACxB,kBAAY,WAAW;AACvB,kBAAY,WAAW;AACvB,kBAAY,WAAW;AACvB,kBAAY,SAAS;AACrB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,QAAQ;AACpB,kBAAY,SAAS;AACrB,kBAAY,MAAM;AAClB,kBAAY,OAAO;AACnB,kBAAY,aAAa;AACzB,kBAAY,SAAS;AACrB,kBAAY,QAAQ;AACpB,kBAAY,WAAW;AACvB,kBAAY,gBAAgB;AAAA,IAC9B,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,YAAY;AACpB,iBAAW,aAAa;AAAA,IAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,MAAM,MAAM,OAAO,KAAK,eAAe;AACrD,YAAI,SAAS;AAAA,UACX;AAAA,UACA;AAAA,UACA,UAAU,EAAE,KAAK,MAAM;AAAA,QACzB;AACA,YAAI,eAAe;AACjB,iBAAO,gBAAgB;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AACA,yBAAmB,SAAS;AAAA,IAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,kBAAkB;AAC1B,eAAS,OAAO,MAAM,MAAM,KAAK,OAAO;AACtC,eAAO,UAAU,SAAS,EAAE,MAAM,MAAM,UAAU,EAAE,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,MAAM,UAAU,EAAE,IAAI,EAAE;AAAA,MACvG;AACA,uBAAiB,SAAS;AAAA,IAC5B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAE5C,KAAC,SAAS,iBAAiB;AACzB,eAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACnE,YAAI,SAAS;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,aAAa,QAAQ;AACvB,iBAAO,WAAW;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AACA,sBAAgB,SAAS;AACzB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,aAAa,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,cAAc,MAAM,UAAU,WAAW,UAAU,GAAG,OAAO,UAAU,MAAM,OAAO,UAAU,eAAe,UAAU,GAAG,QAAQ,UAAU,UAAU,OAAO,UAAU,aAAa,UAAU,MAAM,QAAQ,UAAU,QAAQ,OAAO,UAAU,SAAS,UAAU,MAAM,QAAQ,UAAU,IAAI;AAAA,MAC9Z;AACA,sBAAgB,KAAK;AAAA,IACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,iBAAiB;AACzB,sBAAgB,QAAQ;AACxB,sBAAgB,WAAW;AAC3B,sBAAgB,WAAW;AAC3B,sBAAgB,kBAAkB;AAClC,sBAAgB,iBAAiB;AACjC,sBAAgB,kBAAkB;AAClC,sBAAgB,SAAS;AACzB,sBAAgB,wBAAwB;AACxC,sBAAgB,eAAe;AAAA,IACjC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,wBAAwB;AAChC,6BAAuB,UAAU;AACjC,6BAAuB,YAAY;AAAA,IACrC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAExD,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,aAAa,MAAM,aAAa;AAC9C,YAAI,SAAS,EAAE,YAAY;AAC3B,YAAI,SAAS,UAAU,SAAS,MAAM;AACpC,iBAAO,OAAO;AAAA,QAChB;AACA,YAAI,gBAAgB,UAAU,gBAAgB,MAAM;AAClD,iBAAO,cAAc;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,yBAAmB,SAAS;AAC5B,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAAM,UAAU,SAAS,UAAU,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM,OAAO,UAAU,gBAAgB,UAAU,UAAU,gBAAgB,sBAAsB,WAAW,UAAU,gBAAgB,sBAAsB;AAAA,MACpT;AACA,yBAAmB,KAAK;AAAA,IAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,aAAa;AACrB,eAAS,OAAO,OAAO,qBAAqB,MAAM;AAChD,YAAI,SAAS,EAAE,MAAM;AACrB,YAAI,YAAY;AAChB,YAAI,OAAO,wBAAwB,UAAU;AAC3C,sBAAY;AACZ,iBAAO,OAAO;AAAA,QAChB,WAAW,QAAQ,GAAG,mBAAmB,GAAG;AAC1C,iBAAO,UAAU;AAAA,QACnB,OAAO;AACL,iBAAO,OAAO;AAAA,QAChB;AACA,YAAI,aAAa,SAAS,QAAQ;AAChC,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,kBAAY,SAAS;AACrB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MAAM,UAAU,gBAAgB,UAAU,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OAAO,UAAU,SAAS,UAAU,GAAG,OAAO,UAAU,IAAI,OAAO,UAAU,SAAS,UAAU,UAAU,YAAY,YAAY,UAAU,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,GAAG,QAAQ,UAAU,WAAW,OAAO,UAAU,SAAS,UAAU,cAAc,GAAG,UAAU,IAAI;AAAA,MACvd;AACA,kBAAY,KAAK;AAAA,IACnB,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,WAAW;AACnB,eAAS,OAAO,OAAO,MAAM;AAC3B,YAAI,SAAS,EAAE,MAAM;AACrB,YAAI,GAAG,QAAQ,IAAI,GAAG;AACpB,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,MAC/H;AACA,gBAAU,KAAK;AAAA,IACjB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,KAAC,SAAS,oBAAoB;AAC5B,eAAS,OAAO,SAAS,cAAc;AACrC,eAAO,EAAE,SAAS,aAAa;AAAA,MACjC;AACA,yBAAmB,SAAS;AAC5B,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,MACrG;AACA,yBAAmB,KAAK;AAAA,IAC1B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,KAAC,SAAS,eAAe;AACvB,eAAS,OAAO,OAAO,QAAQ,MAAM;AACnC,eAAO,EAAE,OAAO,QAAQ,KAAK;AAAA,MAC/B;AACA,oBAAc,SAAS;AACvB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,MAC5H;AACA,oBAAc,KAAK;AAAA,IACrB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,KAAC,SAAS,iBAAiB;AACzB,eAAS,OAAO,OAAO,QAAQ;AAC7B,eAAO,EAAE,OAAO,OAAO;AAAA,MACzB;AACA,sBAAgB,SAAS;AACzB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,cAAc,SAAS,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAU,gBAAgB,GAAG,UAAU,MAAM;AAAA,MACxI;AACA,sBAAgB,KAAK;AAAA,IACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,qBAAqB;AAC7B,0BAAoB,WAAW,IAAI;AACnC,0BAAoB,MAAM,IAAI;AAC9B,0BAAoB,OAAO,IAAI;AAC/B,0BAAoB,MAAM,IAAI;AAC9B,0BAAoB,WAAW,IAAI;AACnC,0BAAoB,QAAQ,IAAI;AAChC,0BAAoB,eAAe,IAAI;AACvC,0BAAoB,WAAW,IAAI;AACnC,0BAAoB,UAAU,IAAI;AAClC,0BAAoB,UAAU,IAAI;AAClC,0BAAoB,YAAY,IAAI;AACpC,0BAAoB,OAAO,IAAI;AAC/B,0BAAoB,UAAU,IAAI;AAClC,0BAAoB,QAAQ,IAAI;AAChC,0BAAoB,OAAO,IAAI;AAC/B,0BAAoB,SAAS,IAAI;AACjC,0BAAoB,UAAU,IAAI;AAClC,0BAAoB,SAAS,IAAI;AACjC,0BAAoB,QAAQ,IAAI;AAChC,0BAAoB,QAAQ,IAAI;AAChC,0BAAoB,QAAQ,IAAI;AAChC,0BAAoB,UAAU,IAAI;AAClC,0BAAoB,WAAW,IAAI;AAAA,IACrC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAElD,KAAC,SAAS,yBAAyB;AACjC,8BAAwB,aAAa,IAAI;AACzC,8BAAwB,YAAY,IAAI;AACxC,8BAAwB,UAAU,IAAI;AACtC,8BAAwB,QAAQ,IAAI;AACpC,8BAAwB,YAAY,IAAI;AACxC,8BAAwB,UAAU,IAAI;AACtC,8BAAwB,OAAO,IAAI;AACnC,8BAAwB,cAAc,IAAI;AAC1C,8BAAwB,eAAe,IAAI;AAC3C,8BAAwB,gBAAgB,IAAI;AAAA,IAC9C,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAE1D,KAAC,SAAS,iBAAiB;AACzB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,MAAM,UAAU,aAAa,UAAU,OAAO,UAAU,aAAa,aAAa,MAAM,QAAQ,UAAU,IAAI,MAAM,UAAU,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,CAAC,MAAM;AAAA,MACnN;AACA,sBAAgB,KAAK;AAAA,IACvB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAE1C,KAAC,SAAS,kBAAkB;AAC1B,eAAS,OAAO,OAAO,MAAM;AAC3B,eAAO,EAAE,OAAO,KAAK;AAAA,MACvB;AACA,uBAAiB,SAAS;AAC1B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,MAC5G;AACA,uBAAiB,KAAK;AAAA,IACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAE5C,KAAC,SAAS,4BAA4B;AACpC,eAAS,OAAO,OAAO,cAAc,qBAAqB;AACxD,eAAO,EAAE,OAAO,cAAc,oBAAoB;AAAA,MACpD;AACA,iCAA2B,SAAS;AACpC,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,KAAK,GAAG,QAAQ,UAAU,mBAAmB,MAAM,GAAG,OAAO,UAAU,YAAY,KAAK,UAAU,iBAAiB;AAAA,MAClM;AACA,iCAA2B,KAAK;AAAA,IAClC,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,KAAC,SAAS,mCAAmC;AAC3C,eAAS,OAAO,OAAO,YAAY;AACjC,eAAO,EAAE,OAAO,WAAW;AAAA,MAC7B;AACA,wCAAkC,SAAS;AAC3C,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,cAAc,UAAU,cAAc,QAAQ,MAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,UAAU,KAAK,UAAU,eAAe;AAAA,MACjJ;AACA,wCAAkC,KAAK;AAAA,IACzC,GAAG,qCAAqC,mCAAmC,CAAC,EAAE;AAE9E,KAAC,SAAS,qBAAqB;AAC7B,eAAS,OAAO,SAAS,iBAAiB;AACxC,eAAO,EAAE,SAAS,gBAAgB;AAAA,MACpC;AACA,0BAAoB,SAAS;AAC7B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,QAAQ,SAAS,KAAK,MAAM,GAAG,MAAM,eAAe;AAAA,MAChE;AACA,0BAAoB,KAAK;AAAA,IAC3B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAElD,KAAC,SAAS,gBAAgB;AACxB,qBAAe,OAAO;AACtB,qBAAe,YAAY;AAC3B,eAAS,GAAG,OAAO;AACjB,eAAO,UAAU,KAAK,UAAU;AAAA,MAClC;AACA,qBAAe,KAAK;AAAA,IACtB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,KAAC,SAAS,qBAAqB;AAC7B,eAAS,OAAO,OAAO;AACrB,eAAO,EAAE,MAAM;AAAA,MACjB;AACA,0BAAoB,SAAS;AAC7B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,MAAM,UAAU,YAAY,UAAU,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OAAO,UAAU,aAAa,UAAU,SAAS,GAAG,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO;AAAA,MACpR;AACA,0BAAoB,KAAK;AAAA,IAC3B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAElD,KAAC,SAAS,YAAY;AACpB,eAAS,OAAO,UAAU,OAAO,MAAM;AACrC,cAAM,SAAS,EAAE,UAAU,MAAM;AACjC,YAAI,SAAS,QAAQ;AACnB,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AACA,iBAAW,SAAS;AACpB,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,MAAM,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,WAAW,UAAU,OAAO,mBAAmB,EAAE,OAAO,UAAU,SAAS,UAAU,cAAc,GAAG,UAAU,IAAI,MAAM,UAAU,cAAc,UAAU,GAAG,WAAW,UAAU,WAAW,SAAS,EAAE,MAAM,UAAU,YAAY,UAAU,GAAG,OAAO,UAAU,OAAO,KAAK,cAAc,GAAG,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,GAAG,QAAQ,UAAU,WAAW,OAAO,UAAU,iBAAiB,UAAU,GAAG,QAAQ,UAAU,YAAY;AAAA,MACvjB;AACA,iBAAW,KAAK;AAAA,IAClB,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,KAAC,SAAS,cAAc;AACtB,eAAS,cAAc,OAAO;AAC5B,eAAO,EAAE,MAAM,WAAW,MAAM;AAAA,MAClC;AACA,mBAAa,gBAAgB;AAAA,IAC/B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAEpC,KAAC,SAAS,uBAAuB;AAC/B,eAAS,OAAO,YAAY,YAAY,OAAO,SAAS;AACtD,eAAO,EAAE,YAAY,YAAY,OAAO,QAAQ;AAAA,MAClD;AACA,4BAAsB,SAAS;AAAA,IACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,KAAC,SAAS,uBAAuB;AAC/B,eAAS,OAAO,OAAO;AACrB,eAAO,EAAE,MAAM;AAAA,MACjB;AACA,4BAAsB,SAAS;AAAA,IACjC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,KAAC,SAAS,8BAA8B;AACtC,mCAA6B,UAAU;AACvC,mCAA6B,YAAY;AAAA,IAC3C,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAEpE,KAAC,SAAS,yBAAyB;AACjC,eAAS,OAAO,OAAO,MAAM;AAC3B,eAAO,EAAE,OAAO,KAAK;AAAA,MACvB;AACA,8BAAwB,SAAS;AAAA,IACnC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAE1D,KAAC,SAAS,0BAA0B;AAClC,eAAS,OAAO,aAAa,wBAAwB;AACnD,eAAO,EAAE,aAAa,uBAAuB;AAAA,MAC/C;AACA,+BAAyB,SAAS;AAAA,IACpC,GAAG,4BAA4B,0BAA0B,CAAC,EAAE;AAE5D,KAAC,SAAS,kBAAkB;AAC1B,eAAS,GAAG,OAAO;AACjB,cAAM,YAAY;AAClB,eAAO,GAAG,cAAc,SAAS,KAAK,IAAI,GAAG,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,MACzF;AACA,uBAAiB,KAAK;AAAA,IACxB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAE5C,KAAC,SAAS,eAAe;AACvB,eAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AACjD,eAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,MAC/D;AACA,oBAAc,SAAS;AACvB,eAAS,GAAG,OAAO;AACjB,YAAI,YAAY;AAChB,eAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,MACjR;AACA,oBAAc,KAAK;AACnB,eAAS,WAAW,UAAU,OAAO;AACnC,YAAI,OAAO,SAAS,QAAQ;AAC5B,YAAI,cAAc,UAAU,OAAO,CAAC,GAAG,MAAM;AAC3C,cAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,cAAI,SAAS,GAAG;AACd,mBAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,UACjD;AACA,iBAAO;AAAA,QACT,CAAC;AACD,YAAI,qBAAqB,KAAK;AAC9B,iBAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,cAAI,IAAI,YAAY,CAAC;AACrB,cAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,cAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,cAAI,aAAa,oBAAoB;AACnC,mBAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,UAC3F,OAAO;AACL,kBAAM,IAAI,MAAM,kBAAkB;AAAA,UACpC;AACA,+BAAqB;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,oBAAc,aAAa;AAC3B,eAAS,UAAU,MAAM,SAAS;AAChC,YAAI,KAAK,UAAU,GAAG;AACpB,iBAAO;AAAA,QACT;AACA,cAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,cAAM,OAAO,KAAK,MAAM,GAAG,CAAC;AAC5B,cAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,kBAAU,MAAM,OAAO;AACvB,kBAAU,OAAO,OAAO;AACxB,YAAI,UAAU;AACd,YAAI,WAAW;AACf,YAAI,IAAI;AACR,eAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACvD,cAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,cAAI,OAAO,GAAG;AACZ,iBAAK,GAAG,IAAI,KAAK,SAAS;AAAA,UAC5B,OAAO;AACL,iBAAK,GAAG,IAAI,MAAM,UAAU;AAAA,UAC9B;AAAA,QACF;AACA,eAAO,UAAU,KAAK,QAAQ;AAC5B,eAAK,GAAG,IAAI,KAAK,SAAS;AAAA,QAC5B;AACA,eAAO,WAAW,MAAM,QAAQ;AAC9B,eAAK,GAAG,IAAI,MAAM,UAAU;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAAA,IACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI,mBAAmB,MAAM;AAAA,MAC3B,YAAY,KAAK,YAAY,SAAS,SAAS;AAC7C,aAAK,OAAO;AACZ,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,eAAe;AAAA,MACtB;AAAA,MACA,IAAI,MAAM;AACR,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,aAAa;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,IAAI,UAAU;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,QAAQ,OAAO;AACb,YAAI,OAAO;AACT,cAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,cAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,iBAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,QAC3C;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MACA,OAAO,OAAO,SAAS;AACrB,aAAK,WAAW,MAAM;AACtB,aAAK,WAAW;AAChB,aAAK,eAAe;AAAA,MACtB;AAAA,MACA,iBAAiB;AACf,YAAI,KAAK,iBAAiB,QAAQ;AAChC,cAAI,cAAc,CAAC;AACnB,cAAI,OAAO,KAAK;AAChB,cAAI,cAAc;AAClB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,aAAa;AACf,0BAAY,KAAK,CAAC;AAClB,4BAAc;AAAA,YAChB;AACA,gBAAI,KAAK,KAAK,OAAO,CAAC;AACtB,0BAAc,OAAO,QAAQ,OAAO;AACpC,gBAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACrE;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe,KAAK,SAAS,GAAG;AAClC,wBAAY,KAAK,KAAK,MAAM;AAAA,UAC9B;AACA,eAAK,eAAe;AAAA,QACtB;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MACA,WAAW,QAAQ;AACjB,iBAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,YAAI,cAAc,KAAK,eAAe;AACtC,YAAI,MAAM,GAAG,OAAO,YAAY;AAChC,YAAI,SAAS,GAAG;AACd,iBAAO,SAAS,OAAO,GAAG,MAAM;AAAA,QAClC;AACA,eAAO,MAAM,MAAM;AACjB,cAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,cAAI,YAAY,GAAG,IAAI,QAAQ;AAC7B,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,MAAM;AAAA,UACd;AAAA,QACF;AACA,YAAI,OAAO,MAAM;AACjB,eAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,MACzD;AAAA,MACA,SAAS,UAAU;AACjB,YAAI,cAAc,KAAK,eAAe;AACtC,YAAI,SAAS,QAAQ,YAAY,QAAQ;AACvC,iBAAO,KAAK,SAAS;AAAA,QACvB,WAAW,SAAS,OAAO,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,YAAI,iBAAiB,SAAS,OAAO,IAAI,YAAY,SAAS,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC7G,eAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,MACvF;AAAA,MACA,IAAI,YAAY;AACd,eAAO,KAAK,eAAe,EAAE;AAAA,MAC/B;AAAA,IACF;AAEA,KAAC,SAAS,KAAK;AACb,YAAM,WAAW,OAAO,UAAU;AAClC,eAAS,QAAQ,OAAO;AACtB,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,UAAI,UAAU;AACd,eAAS,WAAW,OAAO;AACzB,eAAO,OAAO,UAAU;AAAA,MAC1B;AACA,UAAI,YAAY;AAChB,eAAS,QAAQ,OAAO;AACtB,eAAO,UAAU,QAAQ,UAAU;AAAA,MACrC;AACA,UAAI,UAAU;AACd,eAAS,OAAO,OAAO;AACrB,eAAO,SAAS,KAAK,KAAK,MAAM;AAAA,MAClC;AACA,UAAI,SAAS;AACb,eAAS,OAAO,OAAO;AACrB,eAAO,SAAS,KAAK,KAAK,MAAM;AAAA,MAClC;AACA,UAAI,SAAS;AACb,eAAS,YAAY,OAAO,KAAK,KAAK;AACpC,eAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,MAChF;AACA,UAAI,cAAc;AAClB,eAAS,SAAS,OAAO;AACvB,eAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,MACxF;AACA,UAAI,UAAU;AACd,eAAS,UAAU,OAAO;AACxB,eAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,MAC9E;AACA,UAAI,WAAW;AACf,eAAS,KAAK,OAAO;AACnB,eAAO,SAAS,KAAK,KAAK,MAAM;AAAA,MAClC;AACA,UAAI,OAAO;AACX,eAAS,cAAc,OAAO;AAC5B,eAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,MAC5C;AACA,UAAI,gBAAgB;AACpB,eAAS,WAAW,OAAO,OAAO;AAChC,eAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,MAClD;AACA,UAAI,aAAa;AAAA,IACnB,GAAG,OAAO,KAAK,CAAC,EAAE;AAGlB,IAAI,qBAAqB,MAAM;AAAA,MAC7B,YAAY,aAAa,SAAS,mBAAmB;AACnD,aAAK,cAAc;AACnB,aAAK,UAAU;AACf,aAAK,eAAe,CAAC;AACrB,aAAK,YAA4B,uBAAO,OAAO,IAAI;AACnD,cAAM,aAAa,CAAC,UAAU;AAC5B,cAAI,SAAS,MAAM,cAAc;AACjC,cAAI,WAAW,KAAK,aAAa;AAC/B;AAAA,UACF;AACA,cAAI;AACJ,eAAK,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,mBAAmB,MAAM;AACpE,mBAAO,aAAa,MAAM;AAC1B,qBAAS,OAAO,WAAW,MAAM,KAAK,YAAY,MAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UAC3E,CAAC;AACD,eAAK,YAAY,MAAM,KAAK,MAAM;AAAA,QACpC;AACA,cAAM,iBAAiB,CAAC,UAAU;AAChC,qCAA2B,OAAO,gBAAgB,OAAO,KAAK,aAAa,CAAC,CAAC;AAC7E,cAAI,SAAS,MAAM,IAAI,SAAS;AAChC,cAAI,WAAW,KAAK,UAAU,MAAM;AACpC,cAAI,UAAU;AACZ,qBAAS,QAAQ;AACjB,mBAAO,KAAK,UAAU,MAAM;AAAA,UAC9B;AAAA,QACF;AACA,aAAK,aAAa,KAAK,2BAA2B,OAAO,iBAAiB,UAAU,CAAC;AACrF,aAAK,aAAa,KAAK,2BAA2B,OAAO,mBAAmB,cAAc,CAAC;AAC3F,aAAK,aAAa;AAAA,UAChB,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AACpE,2BAAe,MAAM,KAAK;AAC1B,uBAAW,MAAM,KAAK;AAAA,UACxB,CAAC;AAAA,QACH;AACA,aAAK,aAAa;AAAA,UAChB,kBAAkB,CAAC,MAAM;AACvB,uCAA2B,OAAO,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC/D,kBAAI,MAAM,cAAc,MAAM,KAAK,aAAa;AAC9C,+BAAe,KAAK;AACpB,2BAAW,KAAK;AAAA,cAClB;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,SAAS,MAAM;AACb,uCAA2B,OAAO,UAAU,EAAE,QAAQ,cAAc;AACpE,qBAAS,OAAO,KAAK,WAAW;AAC9B,mBAAK,UAAU,GAAG,EAAE,QAAQ;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,CAAC;AACD,mCAA2B,OAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,MAClE;AAAA,MACA,UAAU;AACR,aAAK,aAAa,QAAQ,CAAC,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjD,aAAK,aAAa,SAAS;AAAA,MAC7B;AAAA,MACA,YAAY,UAAU,YAAY;AAChC,aAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AACvC,iBAAO,QAAQ,aAAa,SAAS,SAAS,CAAC;AAAA,QACjD,CAAC,EAAE,KAAK,CAAC,gBAAgB;AACvB,gBAAM,UAAU,YAAY,IAAI,CAAC,MAAM,cAAc,UAAU,CAAC,CAAC;AACjE,cAAI,QAAQ,2BAA2B,OAAO,SAAS,QAAQ;AAC/D,cAAI,SAAS,MAAM,cAAc,MAAM,YAAY;AACjD,uCAA2B,OAAO,gBAAgB,OAAO,YAAY,OAAO;AAAA,UAC9E;AAAA,QACF,CAAC,EAAE,KAAK,QAAQ,CAAC,QAAQ;AACvB,kBAAQ,MAAM,GAAG;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AA4BA,IAAI,oBAAoB,MAAM;AAAA,MAC5B,YAAY,SAAS,oBAAoB;AACvC,aAAK,UAAU;AACf,aAAK,qBAAqB;AAAA,MAC5B;AAAA,MACA,IAAI,oBAAoB;AACtB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,uBAAuB,OAAO,UAAU,SAAS,OAAO;AACtD,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,WAAW,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,QACvE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,gBAAM,WAAW,MAAM,qBAAqB,QAAQ;AACpD,gBAAM,YAAY,IAAI,2BAA2B;AAAA,YAC/C,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AACA,gBAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,UAAU;AACtC,kBAAM,OAAO;AAAA,cACX,OAAO,MAAM;AAAA,cACb,YAAY,MAAM,cAAc,MAAM;AAAA,cACtC,UAAU,MAAM;AAAA,cAChB,YAAY,MAAM;AAAA,cAClB,eAAe,MAAM;AAAA,cACrB,QAAQ,MAAM;AAAA,cACd,SAAS,UAAU,MAAM,OAAO;AAAA,cAChC,OAAO;AAAA,cACP,MAAM,qBAAqB,MAAM,IAAI;AAAA,YACvC;AACA,gBAAI,MAAM,UAAU;AAClB,kBAAI,oBAAoB,MAAM,QAAQ,GAAG;AACvC,qBAAK,QAAQ;AAAA,kBACX,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAAA,kBACrC,SAAS,QAAQ,MAAM,SAAS,OAAO;AAAA,gBACzC;AAAA,cACF,OAAO;AACL,qBAAK,QAAQ,QAAQ,MAAM,SAAS,KAAK;AAAA,cAC3C;AACA,mBAAK,aAAa,MAAM,SAAS;AAAA,YACnC;AACA,gBAAI,MAAM,qBAAqB;AAC7B,mBAAK,sBAAsB,MAAM,oBAAoB,IAAI,UAAU;AAAA,YACrE;AACA,gBAAI,MAAM,qBAAqB,iBAAiB,SAAS;AACvD,mBAAK,kBAAkB,2BAA2B,UAAU,6BAA6B;AAAA,YAC3F;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO;AAAA,YACL,cAAc,KAAK;AAAA,YACnB,aAAa;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAuFA,IAAI,eAAe,MAAM;AAAA,MACvB,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,aAAa,OAAO,UAAU,OAAO;AACnC,YAAI,WAAW,MAAM;AACrB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,QAAQ,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,QACpE,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,iBAAO;AAAA,YACL,OAAO,QAAQ,KAAK,KAAK;AAAA,YACzB,UAAU,oBAAoB,KAAK,QAAQ;AAAA,UAC7C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AA+BA,IAAI,2BAA2B,MAAM;AAAA,MACnC,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,0BAA0B,OAAO,UAAU,OAAO;AAChD,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,uBAAuB,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY;AAC7I,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,iBAAO,QAAQ,IAAI,CAAC,UAAU;AAC5B,mBAAO;AAAA,cACL,OAAO,QAAQ,MAAM,KAAK;AAAA,cAC1B,MAAM,wBAAwB,MAAM,IAAI;AAAA,YAC1C;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAYA,IAAI,oBAAoB,MAAM;AAAA,MAC5B,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,kBAAkB,OAAO,UAAU,OAAO;AACxC,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,QAC3E,CAAC,EAAE,KAAK,CAAC,eAAe;AACtB,cAAI,CAAC,YAAY;AACf;AAAA,UACF;AACA,iBAAO,CAAC,WAAW,UAAU,CAAC;AAAA,QAChC,CAAC;AAAA,MACH;AAAA,IACF;AAOA,IAAI,mBAAmB,MAAM;AAAA,MAC3B,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,kBAAkB,OAAO,UAAU,SAAS,OAAO;AACjD,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,QAC3E,CAAC,EAAE,KAAK,CAAC,YAAY;AACnB,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,iBAAO,QAAQ,IAAI,UAAU;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,gBAAgB,MAAM;AAAA,MACxB,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,mBAAmB,OAAO,UAAU,SAAS,OAAO;AAClD,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,SAAS,SAAS,SAAS,GAAG,aAAa,QAAQ,GAAG,OAAO;AAAA,QAC9E,CAAC,EAAE,KAAK,CAAC,SAAS;AAChB,iBAAO,gBAAgB,IAAI;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAuBA,IAAI,wBAAwB,MAAM;AAAA,MAChC,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,uBAAuB,OAAO,OAAO;AACnC,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,oBAAoB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAChH,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,iBAAO,MAAM,IAAI,CAAC,SAAS;AACzB,gBAAI,iBAAiB,IAAI,GAAG;AAC1B,qBAAO,iBAAiB,IAAI;AAAA,YAC9B;AACA,mBAAO;AAAA,cACL,MAAM,KAAK;AAAA,cACX,QAAQ;AAAA,cACR,eAAe,KAAK;AAAA,cACpB,MAAM,aAAa,KAAK,IAAI;AAAA,cAC5B,OAAO,QAAQ,KAAK,SAAS,KAAK;AAAA,cAClC,gBAAgB,QAAQ,KAAK,SAAS,KAAK;AAAA,cAC3C,MAAM,CAAC;AAAA,YACT;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAyDA,IAAI,sBAAsB,MAAM;AAAA,MAC9B,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,aAAa,OAAO,OAAO;AACzB,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,kBAAkB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9G,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,iBAAO;AAAA,YACL,OAAO,MAAM,IAAI,CAAC,UAAU;AAAA,cAC1B,OAAO,QAAQ,KAAK,KAAK;AAAA,cACzB,KAAK,KAAK;AAAA,YACZ,EAAE;AAAA,UACJ;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,iCAAiC,MAAM;AAAA,MACzC,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,+BAA+B,OAAO,SAAS,OAAO;AACpD,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/F,gBAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,YACF;AACA,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,sCAAsC,MAAM;AAAA,MAC9C,YAAY,SAAS;AACnB,aAAK,UAAU;AACf,aAAK,0BAA0B;AAAA,MACjC;AAAA,MACA,oCAAoC,OAAO,OAAO,SAAS,OAAO;AAChE,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC9C,iBAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,GAAG,sBAAsB,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3G,gBAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC;AAAA,YACF;AACA,mBAAO,MAAM,IAAI,UAAU;AAAA,UAC7B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAOA,IAAI,uBAAuB,MAAM;AAAA,MAC/B,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,sBAAsB,OAAO,OAAO;AAClC,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,mBAAmB,SAAS,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/G,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,iBAAO,MAAM,IAAI,CAAC,UAAU;AAAA,YAC1B,OAAO,KAAK;AAAA,YACZ,OAAO,QAAQ,KAAK,KAAK;AAAA,UAC3B,EAAE;AAAA,QACJ,CAAC;AAAA,MACH;AAAA,MACA,0BAA0B,OAAO,MAAM,OAAO;AAC5C,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,UAC5B,CAAC,YAAY,QAAQ,sBAAsB,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,QACnG,EAAE,KAAK,CAAC,kBAAkB;AACxB,cAAI,CAAC,eAAe;AAClB;AAAA,UACF;AACA,iBAAO,cAAc,IAAI,CAAC,iBAAiB;AACzC,gBAAI,OAAO;AAAA,cACT,OAAO,aAAa;AAAA,YACtB;AACA,gBAAI,aAAa,UAAU;AACzB,mBAAK,WAAW,WAAW,aAAa,QAAQ;AAAA,YAClD;AACA,gBAAI,aAAa,qBAAqB;AACpC,mBAAK,sBAAsB,aAAa,oBAAoB,IAAI,UAAU;AAAA,YAC5E;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,sBAAsB,MAAM;AAAA,MAC9B,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,qBAAqB,OAAO,SAAS,OAAO;AAC1C,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,CAAC,YAAY,QAAQ,iBAAiB,SAAS,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,WAAW;AACvH,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AACA,iBAAO,OAAO,IAAI,CAAC,UAAU;AAC3B,kBAAM,SAAS;AAAA,cACb,OAAO,MAAM,YAAY;AAAA,cACzB,KAAK,MAAM,UAAU;AAAA,YACvB;AACA,gBAAI,OAAO,MAAM,SAAS,aAAa;AACrC,qBAAO,OAAO,mBAAmB,MAAM,IAAI;AAAA,YAC7C;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAYA,IAAI,wBAAwB,MAAM;AAAA,MAChC,YAAY,SAAS;AACnB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,uBAAuB,OAAO,WAAW,OAAO;AAC9C,cAAM,WAAW,MAAM;AACvB,eAAO,KAAK,QAAQ,QAAQ,EAAE;AAAA,UAC5B,CAAC,YAAY,QAAQ;AAAA,YACnB,SAAS,SAAS;AAAA,YAClB,UAAU,IAAI,YAAY;AAAA,UAC5B;AAAA,QACF,EAAE,KAAK,CAAC,oBAAoB;AAC1B,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AACA,iBAAO,gBAAgB,IAAI,CAAC,mBAAmB;AAC7C,kBAAM,SAAS,CAAC;AAChB,mBAAO,gBAAgB;AACrB,qBAAO,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,EAAE,CAAC;AACpD,+BAAiB,eAAe;AAAA,YAClC;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AA4UA,KAAC,SAAS,iBAAiB;AACzB,sBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,sBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,sBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,IAAI,IAAI,EAAE,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,GAAG,IAAI;AAC9C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,GAAG,IAAI,EAAE,IAAI;AAC7C,sBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,sBAAgB,gBAAgB,WAAW,IAAI,EAAE,IAAI;AACrD,sBAAgB,gBAAgB,YAAY,IAAI,GAAG,IAAI;AACvD,sBAAgB,gBAAgB,cAAc,IAAI,EAAE,IAAI;AACxD,sBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,sBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,sBAAgB,gBAAgB,KAAK,IAAI,EAAE,IAAI;AAC/C,sBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,sBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,sBAAgB,gBAAgB,WAAW,IAAI,GAAG,IAAI;AACtD,sBAAgB,gBAAgB,aAAa,IAAI,EAAE,IAAI;AACvD,sBAAgB,gBAAgB,MAAM,IAAI,EAAE,IAAI;AAChD,sBAAgB,gBAAgB,OAAO,IAAI,EAAE,IAAI;AACjD,sBAAgB,gBAAgB,UAAU,IAAI,EAAE,IAAI;AACpD,sBAAgB,gBAAgB,KAAK,IAAI,CAAC,IAAI;AAAA,IAChD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAG1C,IAAI,eAAe,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,aAAO,IAAI,OAAO,KAAK;AAAA,IACzB,CAAC;AACD,IAAI,kBAAkB;AACtB,IAAI,6BAA6B;AAAA,MAC/B,KAAK;AAAA,QACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,iBAAO,OAAO,IAAI,OAAO,KAAK;AAAA,QAChC,CAAC;AAAA,QACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,iBAAO,OAAO,IAAI,OAAO,KAAK;AAAA,QAChC,CAAC;AAAA,QACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC3D,iBAAO,SAAS,IAAI,OAAO,KAAK;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,MACA,KAAK;AAAA,QACH,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,iBAAO,OAAO,IAAI,OAAO,KAAK;AAAA,QAChC,CAAC;AAAA,QACD,MAAM,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AACzD,iBAAO,OAAO,IAAI,OAAO,KAAK;AAAA,QAChC,CAAC;AAAA,QACD,QAAQ,IAAI,MAAM,eAAe,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU;AAC3D,iBAAO,SAAS,IAAI,OAAO,KAAK;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AAIA,KAAC,SAAS,eAAe;AACvB,oBAAc,UAAU;AAAA,QACtB,oBAAoB;AAAA,MACtB;AAAA,IACF,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAGtC,IAAI,iBAAiB;AAErB,KAAC,SAAS,YAAY;AACpB,iBAAW,WAAW,MAAM,IAAI,CAAC,IAAI;AACrC,iBAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,iBAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,iBAAW,WAAW,uBAAuB,IAAI,CAAC,IAAI;AACtD,iBAAW,WAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,iBAAW,WAAW,wBAAwB,IAAI,CAAC,IAAI;AACvD,iBAAW,WAAW,kBAAkB,IAAI,CAAC,IAAI;AAAA,IACnD,GAAG,cAAc,YAAY,CAAC,EAAE;AAEhC,KAAC,SAAS,aAAa;AACrB,kBAAY,YAAY,gBAAgB,IAAI,CAAC,IAAI;AACjD,kBAAY,YAAY,iBAAiB,IAAI,CAAC,IAAI;AAClD,kBAAY,YAAY,kBAAkB,IAAI,CAAC,IAAI;AACnD,kBAAY,YAAY,mBAAmB,IAAI,CAAC,IAAI;AACpD,kBAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,kBAAY,YAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,kBAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,kBAAY,YAAY,aAAa,IAAI,CAAC,IAAI;AAC9C,kBAAY,YAAY,cAAc,IAAI,CAAC,IAAI;AAC/C,kBAAY,YAAY,eAAe,IAAI,EAAE,IAAI;AACjD,kBAAY,YAAY,gBAAgB,IAAI,EAAE,IAAI;AAClD,kBAAY,YAAY,mBAAmB,IAAI,EAAE,IAAI;AACrD,kBAAY,YAAY,oBAAoB,IAAI,EAAE,IAAI;AACtD,kBAAY,YAAY,iBAAiB,IAAI,EAAE,IAAI;AACnD,kBAAY,YAAY,QAAQ,IAAI,EAAE,IAAI;AAC1C,kBAAY,YAAY,SAAS,IAAI,EAAE,IAAI;AAC3C,kBAAY,YAAY,KAAK,IAAI,EAAE,IAAI;AAAA,IACzC,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,KAAC,SAAS,iBAAiB;AACzB,sBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,sBAAgB,gBAAgB,qBAAqB,IAAI,CAAC,IAAI;AAC9D,sBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,sBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,sBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,sBAAgB,gBAAgB,eAAe,IAAI,CAAC,IAAI;AACxD,sBAAgB,gBAAgB,oBAAoB,IAAI,CAAC,IAAI;AAC7D,sBAAgB,gBAAgB,sBAAsB,IAAI,CAAC,IAAI;AAC/D,sBAAgB,gBAAgB,mBAAmB,IAAI,CAAC,IAAI;AAC5D,sBAAgB,gBAAgB,qBAAqB,IAAI,EAAE,IAAI;AAC/D,sBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,sBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,sBAAgB,gBAAgB,uBAAuB,IAAI,EAAE,IAAI;AACjE,sBAAgB,gBAAgB,gBAAgB,IAAI,EAAE,IAAI;AAC1D,sBAAgB,gBAAgB,wBAAwB,IAAI,EAAE,IAAI;AAClE,sBAAgB,gBAAgB,kBAAkB,IAAI,EAAE,IAAI;AAAA,IAC9D,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAS1C,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AACzB,IAAI,eAAe,MAAM,cAAc;AAAA,MACrC,YAAY,QAAQ,MAAM;AACxB,aAAK,SAAS;AACd,aAAK,OAAO;AAAA,MACd;AAAA,MACA,OAAO,IAAI,SAAS;AAClB,YAAI,SAAS;AACX,iBAAO,QAAQ;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AAAA,MACA,OAAO,KAAK,SAAS,MAAM;AACzB,eAAO,IAAI,cAAc,SAAS,IAAI;AAAA,MACxC;AAAA,MACA,OAAO,OAAO,GAAG,GAAG;AAClB,YAAI,CAAC,KAAK,CAAC,GAAG;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,KAAK,CAAC,GAAG;AACZ,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,GAAG;AACb,cAAI,MAAM,GAAG;AACX,mBAAO;AAAA,UACT;AACA,cAAI,EAAE,SAAS,EAAE,MAAM;AACrB,mBAAO;AAAA,UACT;AACA,cAAI,EAAE;AACN,cAAI,EAAE;AAAA,QACR;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,YAAY,MAAM,WAAW;AAAA,MAC/B,YAAY,OAAO,WAAW,cAAc,SAAS;AACnD,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,QAAQ;AACN,eAAO,IAAI,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO;AAAA,MACpF;AAAA,MACA,OAAO,OAAO;AACZ,YAAI,UAAU,MAAM;AAClB,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,SAAS,EAAE,iBAAiB,aAAa;AAC5C,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,cAAc,MAAM,aAAa,KAAK,iBAAiB,MAAM,gBAAgB,aAAa,OAAO,KAAK,SAAS,MAAM,OAAO;AAAA,MAC1I;AAAA,MACA,eAAe;AACb,eAAO,KAAK;AAAA,MACd;AAAA,MACA,aAAa,OAAO;AAClB,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAwHA,IAAI,yBAAyB,cAAc,mBAAmB;AAAA,MAC5D,YAAY,YAAY,SAAS,UAAU;AACzC,cAAM,YAAY,SAAS,SAAS,WAAW;AAC/C,aAAK,aAAa;AAAA,UAChB,2BAA2B,OAAO,mBAAmB,CAAC,UAAU;AAC9D,iBAAK,aAAa,MAAM,GAAG;AAAA,UAC7B,CAAC;AAAA,QACH;AACA,aAAK,aAAa;AAAA,UAChB,2BAA2B,OAAO,yBAAyB,CAAC,UAAU;AACpE,iBAAK,aAAa,MAAM,MAAM,GAAG;AAAA,UACnC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,aAAa,UAAU;AACrB,aAAK,QAAQ,EAAE,KAAK,CAAC,YAAY;AAC/B,kBAAQ,YAAY,SAAS,SAAS,CAAC;AAAA,QACzC,CAAC;AAAA,MACH;AAAA,IACF;AAoGA,IAAI,wBAAwB;AAAA,MAC1B,aAAa;AAAA,MACb,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,QAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,QAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA;", "names": []}
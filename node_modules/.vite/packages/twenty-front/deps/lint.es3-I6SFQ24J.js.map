{"version": 3, "sources": ["../../../../@graphiql/codemirror-graphql/esm/utils/jsonParse.js", "../../../../@graphiql/codemirror-graphql/esm/variables/lint.js"], "sourcesContent": ["export default function jsonParse(str) {\n    string = str;\n    strLen = str.length;\n    start = end = lastEnd = -1;\n    ch();\n    lex();\n    const ast = parseObj();\n    expect('EOF');\n    return ast;\n}\nlet string;\nlet strLen;\nlet start;\nlet end;\nlet lastEnd;\nlet code;\nlet kind;\nfunction parseObj() {\n    const nodeStart = start;\n    const members = [];\n    expect('{');\n    if (!skip('}')) {\n        do {\n            members.push(parseMember());\n        } while (skip(','));\n        expect('}');\n    }\n    return {\n        kind: 'Object',\n        start: nodeStart,\n        end: lastEnd,\n        members,\n    };\n}\nfunction parseMember() {\n    const nodeStart = start;\n    const key = kind === 'String' ? curToken() : null;\n    expect('String');\n    expect(':');\n    const value = parseVal();\n    return {\n        kind: 'Member',\n        start: nodeStart,\n        end: lastEnd,\n        key,\n        value,\n    };\n}\nfunction parseArr() {\n    const nodeStart = start;\n    const values = [];\n    expect('[');\n    if (!skip(']')) {\n        do {\n            values.push(parseVal());\n        } while (skip(','));\n        expect(']');\n    }\n    return {\n        kind: 'Array',\n        start: nodeStart,\n        end: lastEnd,\n        values,\n    };\n}\nfunction parseVal() {\n    switch (kind) {\n        case '[':\n            return parseArr();\n        case '{':\n            return parseObj();\n        case 'String':\n        case 'Number':\n        case 'Boolean':\n        case 'Null':\n            const token = curToken();\n            lex();\n            return token;\n    }\n    expect('Value');\n}\nfunction curToken() {\n    return { kind, start, end, value: JSON.parse(string.slice(start, end)) };\n}\nfunction expect(str) {\n    if (kind === str) {\n        lex();\n        return;\n    }\n    let found;\n    if (kind === 'EOF') {\n        found = '[end of file]';\n    }\n    else if (end - start > 1) {\n        found = '`' + string.slice(start, end) + '`';\n    }\n    else {\n        const match = string.slice(start).match(/^.+?\\b/);\n        found = '`' + (match ? match[0] : string[start]) + '`';\n    }\n    throw syntaxError(`Expected ${str} but found ${found}.`);\n}\nexport class JSONSyntaxError extends Error {\n    constructor(message, position) {\n        super(message);\n        this.position = position;\n    }\n}\nfunction syntaxError(message) {\n    return new JSONSyntaxError(message, { start, end });\n}\nfunction skip(k) {\n    if (kind === k) {\n        lex();\n        return true;\n    }\n}\nfunction ch() {\n    if (end < strLen) {\n        end++;\n        code = end === strLen ? 0 : string.charCodeAt(end);\n    }\n    return code;\n}\nfunction lex() {\n    lastEnd = end;\n    while (code === 9 || code === 10 || code === 13 || code === 32) {\n        ch();\n    }\n    if (code === 0) {\n        kind = 'EOF';\n        return;\n    }\n    start = end;\n    switch (code) {\n        case 34:\n            kind = 'String';\n            return readString();\n        case 45:\n        case 48:\n        case 49:\n        case 50:\n        case 51:\n        case 52:\n        case 53:\n        case 54:\n        case 55:\n        case 56:\n        case 57:\n            kind = 'Number';\n            return readNumber();\n        case 102:\n            if (string.slice(start, start + 5) !== 'false') {\n                break;\n            }\n            end += 4;\n            ch();\n            kind = 'Boolean';\n            return;\n        case 110:\n            if (string.slice(start, start + 4) !== 'null') {\n                break;\n            }\n            end += 3;\n            ch();\n            kind = 'Null';\n            return;\n        case 116:\n            if (string.slice(start, start + 4) !== 'true') {\n                break;\n            }\n            end += 3;\n            ch();\n            kind = 'Boolean';\n            return;\n    }\n    kind = string[start];\n    ch();\n}\nfunction readString() {\n    ch();\n    while (code !== 34 && code > 31) {\n        if (code === 92) {\n            code = ch();\n            switch (code) {\n                case 34:\n                case 47:\n                case 92:\n                case 98:\n                case 102:\n                case 110:\n                case 114:\n                case 116:\n                    ch();\n                    break;\n                case 117:\n                    ch();\n                    readHex();\n                    readHex();\n                    readHex();\n                    readHex();\n                    break;\n                default:\n                    throw syntaxError('Bad character escape sequence.');\n            }\n        }\n        else if (end === strLen) {\n            throw syntaxError('Unterminated string.');\n        }\n        else {\n            ch();\n        }\n    }\n    if (code === 34) {\n        ch();\n        return;\n    }\n    throw syntaxError('Unterminated string.');\n}\nfunction readHex() {\n    if ((code >= 48 && code <= 57) ||\n        (code >= 65 && code <= 70) ||\n        (code >= 97 && code <= 102)) {\n        return ch();\n    }\n    throw syntaxError('Expected hexadecimal digit.');\n}\nfunction readNumber() {\n    if (code === 45) {\n        ch();\n    }\n    if (code === 48) {\n        ch();\n    }\n    else {\n        readDigits();\n    }\n    if (code === 46) {\n        ch();\n        readDigits();\n    }\n    if (code === 69 || code === 101) {\n        code = ch();\n        if (code === 43 || code === 45) {\n            ch();\n        }\n        readDigits();\n    }\n}\nfunction readDigits() {\n    if (code < 48 || code > 57) {\n        throw syntaxError('Expected decimal digit.');\n    }\n    do {\n        ch();\n    } while (code >= 48 && code <= 57);\n}\n//# sourceMappingURL=jsonParse.js.map", "import CodeMirror from 'codemirror';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLList, GraphQLNonNull, GraphQLScalarType, } from 'graphql';\nimport jsonParse, { JSONSyntaxError, } from '../utils/jsonParse';\nCodeMirror.registerHelper('lint', 'graphql-variables', (text, options, editor) => {\n    if (!text) {\n        return [];\n    }\n    let ast;\n    try {\n        ast = jsonParse(text);\n    }\n    catch (error) {\n        if (error instanceof JSONSyntaxError) {\n            return [lintError(editor, error.position, error.message)];\n        }\n        throw error;\n    }\n    const { variableToType } = options;\n    if (!variableToType) {\n        return [];\n    }\n    return validateVariables(editor, variableToType, ast);\n});\nfunction validateVariables(editor, variableToType, variablesAST) {\n    var _a;\n    const errors = [];\n    for (const member of variablesAST.members) {\n        if (member) {\n            const variableName = (_a = member.key) === null || _a === void 0 ? void 0 : _a.value;\n            const type = variableToType[variableName];\n            if (type) {\n                for (const [node, message] of validateValue(type, member.value)) {\n                    errors.push(lintError(editor, node, message));\n                }\n            }\n            else {\n                errors.push(lintError(editor, member.key, `Variable \"$${variableName}\" does not appear in any GraphQL query.`));\n            }\n        }\n    }\n    return errors;\n}\nfunction validateValue(type, valueAST) {\n    if (!type || !valueAST) {\n        return [];\n    }\n    if (type instanceof GraphQLNonNull) {\n        if (valueAST.kind === 'Null') {\n            return [[valueAST, `Type \"${type}\" is non-nullable and cannot be null.`]];\n        }\n        return validateValue(type.ofType, valueAST);\n    }\n    if (valueAST.kind === 'Null') {\n        return [];\n    }\n    if (type instanceof GraphQLList) {\n        const itemType = type.ofType;\n        if (valueAST.kind === 'Array') {\n            const values = valueAST.values || [];\n            return mapCat(values, item => validateValue(itemType, item));\n        }\n        return validateValue(itemType, valueAST);\n    }\n    if (type instanceof GraphQLInputObjectType) {\n        if (valueAST.kind !== 'Object') {\n            return [[valueAST, `Type \"${type}\" must be an Object.`]];\n        }\n        const providedFields = Object.create(null);\n        const fieldErrors = mapCat(valueAST.members, member => {\n            var _a;\n            const fieldName = (_a = member === null || member === void 0 ? void 0 : member.key) === null || _a === void 0 ? void 0 : _a.value;\n            providedFields[fieldName] = true;\n            const inputField = type.getFields()[fieldName];\n            if (!inputField) {\n                return [\n                    [\n                        member.key,\n                        `Type \"${type}\" does not have a field \"${fieldName}\".`,\n                    ],\n                ];\n            }\n            const fieldType = inputField ? inputField.type : undefined;\n            return validateValue(fieldType, member.value);\n        });\n        for (const fieldName of Object.keys(type.getFields())) {\n            const field = type.getFields()[fieldName];\n            if (!providedFields[fieldName] &&\n                field.type instanceof GraphQLNonNull &&\n                !field.defaultValue) {\n                fieldErrors.push([\n                    valueAST,\n                    `Object of type \"${type}\" is missing required field \"${fieldName}\".`,\n                ]);\n            }\n        }\n        return fieldErrors;\n    }\n    if ((type.name === 'Boolean' && valueAST.kind !== 'Boolean') ||\n        (type.name === 'String' && valueAST.kind !== 'String') ||\n        (type.name === 'ID' &&\n            valueAST.kind !== 'Number' &&\n            valueAST.kind !== 'String') ||\n        (type.name === 'Float' && valueAST.kind !== 'Number') ||\n        (type.name === 'Int' &&\n            (valueAST.kind !== 'Number' || (valueAST.value | 0) !== valueAST.value))) {\n        return [[valueAST, `Expected value of type \"${type}\".`]];\n    }\n    if ((type instanceof GraphQLEnumType || type instanceof GraphQLScalarType) &&\n        ((valueAST.kind !== 'String' &&\n            valueAST.kind !== 'Number' &&\n            valueAST.kind !== 'Boolean' &&\n            valueAST.kind !== 'Null') ||\n            isNullish(type.parseValue(valueAST.value)))) {\n        return [[valueAST, `Expected value of type \"${type}\".`]];\n    }\n    return [];\n}\nfunction lintError(editor, node, message) {\n    return {\n        message,\n        severity: 'error',\n        type: 'validation',\n        from: editor.posFromIndex(node.start),\n        to: editor.posFromIndex(node.end),\n    };\n}\nfunction isNullish(value) {\n    return value === null || value === undefined || value !== value;\n}\nfunction mapCat(array, mapper) {\n    return Array.prototype.concat.apply([], array.map(mapper));\n}\n//# sourceMappingURL=lint.js.map"], "mappings": ";;;;;;;;;;;;;;;;AAAe,SAAS,UAAU,KAAK;AACnC,WAAS;AACT,WAAS,IAAI;AACb,UAAQ,MAAM,UAAU;AACxB,KAAA;AACA,MAAA;AACA,QAAM,MAAM,SAAA;AACZ,SAAO,KAAK;AACZ,SAAO;AACX;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,WAAW;AAChB,QAAM,YAAY;AAClB,QAAM,UAAU,CAAA;AAChB,SAAO,GAAG;AACV,MAAI,CAAC,KAAK,GAAG,GAAG;AACZ,OAAG;AACC,cAAQ,KAAK,YAAW,CAAE;IACtC,SAAiB,KAAK,GAAG;AACjB,WAAO,GAAG;EACb;AACD,SAAO;IACH,MAAM;IACN,OAAO;IACP,KAAK;IACL;EACR;AACA;AACA,SAAS,cAAc;AACnB,QAAM,YAAY;AAClB,QAAM,MAAM,SAAS,WAAW,SAAQ,IAAK;AAC7C,SAAO,QAAQ;AACf,SAAO,GAAG;AACV,QAAM,QAAQ,SAAA;AACd,SAAO;IACH,MAAM;IACN,OAAO;IACP,KAAK;IACL;IACA;EACR;AACA;AACA,SAAS,WAAW;AAChB,QAAM,YAAY;AAClB,QAAM,SAAS,CAAA;AACf,SAAO,GAAG;AACV,MAAI,CAAC,KAAK,GAAG,GAAG;AACZ,OAAG;AACC,aAAO,KAAK,SAAQ,CAAE;IAClC,SAAiB,KAAK,GAAG;AACjB,WAAO,GAAG;EACb;AACD,SAAO;IACH,MAAM;IACN,OAAO;IACP,KAAK;IACL;EACR;AACA;AACA,SAAS,WAAW;AAChB,UAAQ,MAAI;IACR,KAAK;AACD,aAAO,SAAQ;IACnB,KAAK;AACD,aAAO,SAAQ;IACnB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACD,YAAM,QAAQ,SAAA;AACd,UAAA;AACA,aAAO;EACd;AACD,SAAO,OAAO;AAClB;AACA,SAAS,WAAW;AAChB,SAAO,EAAE,MAAM,OAAO,KAAK,OAAO,KAAK,MAAM,OAAO,MAAM,OAAO,GAAG,CAAC,EAAC;AAC1E;AACA,SAAS,OAAO,KAAK;AACjB,MAAI,SAAS,KAAK;AACd,QAAA;AACA;EACH;AACD,MAAI;AACJ,MAAI,SAAS,OAAO;AAChB,YAAQ;EACX,WACQ,MAAM,QAAQ,GAAG;AACtB,YAAQ,MAAM,OAAO,MAAM,OAAO,GAAG,IAAI;EAC5C,OACI;AACD,UAAM,QAAQ,OAAO,MAAM,KAAK,EAAE,MAAM,QAAQ;AAChD,YAAQ,OAAO,QAAQ,MAAM,CAAC,IAAI,OAAO,KAAK,KAAK;EACtD;AACD,QAAM,YAAY,YAAY,GAAG,cAAc,KAAK,GAAG;AAC3D;AACO,IAAM,kBAAN,cAA8B,MAAM;EACvC,YAAY,SAAS,UAAU;AAC3B,UAAM,OAAO;AACb,SAAK,WAAW;EACnB;AACL;AACA,SAAS,YAAY,SAAS;AAC1B,SAAO,IAAI,gBAAgB,SAAS,EAAE,OAAO,IAAK,CAAA;AACtD;AACA,SAAS,KAAK,GAAG;AACb,MAAI,SAAS,GAAG;AACZ,QAAA;AACA,WAAO;EACV;AACL;AACA,SAAS,KAAK;AACV,MAAI,MAAM,QAAQ;AACd;AACA,WAAO,QAAQ,SAAS,IAAI,OAAO,WAAW,GAAG;EACpD;AACD,SAAO;AACX;AACA,SAAS,MAAM;AACX,YAAU;AACV,SAAO,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,IAAI;AAC5D,OAAA;EACH;AACD,MAAI,SAAS,GAAG;AACZ,WAAO;AACP;EACH;AACD,UAAQ;AACR,UAAQ,MAAI;IACR,KAAK;AACD,aAAO;AACP,aAAO,WAAU;IACrB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACD,aAAO;AACP,aAAO,WAAU;IACrB,KAAK;AACD,UAAI,OAAO,MAAM,OAAO,QAAQ,CAAC,MAAM,SAAS;AAC5C;MACH;AACD,aAAO;AACP,SAAA;AACA,aAAO;AACP;IACJ,KAAK;AACD,UAAI,OAAO,MAAM,OAAO,QAAQ,CAAC,MAAM,QAAQ;AAC3C;MACH;AACD,aAAO;AACP,SAAA;AACA,aAAO;AACP;IACJ,KAAK;AACD,UAAI,OAAO,MAAM,OAAO,QAAQ,CAAC,MAAM,QAAQ;AAC3C;MACH;AACD,aAAO;AACP,SAAA;AACA,aAAO;AACP;EACP;AACD,SAAO,OAAO,KAAK;AACnB,KAAA;AACJ;AACA,SAAS,aAAa;AAClB,KAAA;AACA,SAAO,SAAS,MAAM,OAAO,IAAI;AAC7B,QAAI,SAAS,IAAI;AACb,aAAO,GAAE;AACT,cAAQ,MAAI;QACR,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACD,aAAA;AACA;QACJ,KAAK;AACD,aAAA;AACA,kBAAA;AACA,kBAAA;AACA,kBAAA;AACA,kBAAA;AACA;QACJ;AACI,gBAAM,YAAY,gCAAgC;MACzD;IACJ,WACQ,QAAQ,QAAQ;AACrB,YAAM,YAAY,sBAAsB;IAC3C,OACI;AACD,SAAA;IACH;EACJ;AACD,MAAI,SAAS,IAAI;AACb,OAAA;AACA;EACH;AACD,QAAM,YAAY,sBAAsB;AAC5C;AACA,SAAS,UAAU;AACf,MAAK,QAAQ,MAAM,QAAQ,MACtB,QAAQ,MAAM,QAAQ,MACtB,QAAQ,MAAM,QAAQ,KAAM;AAC7B,WAAO,GAAE;EACZ;AACD,QAAM,YAAY,6BAA6B;AACnD;AACA,SAAS,aAAa;AAClB,MAAI,SAAS,IAAI;AACb,OAAA;EACH;AACD,MAAI,SAAS,IAAI;AACb,OAAA;EACH,OACI;AACD,eAAA;EACH;AACD,MAAI,SAAS,IAAI;AACb,OAAA;AACA,eAAA;EACH;AACD,MAAI,SAAS,MAAM,SAAS,KAAK;AAC7B,WAAO,GAAE;AACT,QAAI,SAAS,MAAM,SAAS,IAAI;AAC5B,SAAA;IACH;AACD,eAAA;EACH;AACL;AACA,SAAS,aAAa;AAClB,MAAI,OAAO,MAAM,OAAO,IAAI;AACxB,UAAM,YAAY,yBAAyB;EAC9C;AACD,KAAG;AACC,OAAA;EACH,SAAQ,QAAQ,MAAM,QAAQ;AACnC;AC7PA,WAAW,eAAe,QAAQ,qBAAqB,CAAC,MAAM,SAAS,WAAW;AAC9E,MAAI,CAAC,MAAM;AACP,WAAO,CAAA;EACV;AACD,MAAI;AACJ,MAAI;AACA,UAAM,UAAU,IAAI;EACvB,SACM,OAAO;AACV,QAAI,iBAAiB,iBAAiB;AAClC,aAAO,CAAC,UAAU,QAAQ,MAAM,UAAU,MAAM,OAAO,CAAC;IAC3D;AACD,UAAM;EACT;AACD,QAAM,EAAE,eAAgB,IAAG;AAC3B,MAAI,CAAC,gBAAgB;AACjB,WAAO,CAAA;EACV;AACD,SAAO,kBAAkB,QAAQ,gBAAgB,GAAG;AACxD,CAAC;AACD,SAAS,kBAAkB,QAAQ,gBAAgB,cAAc;AAC7D,MAAI;AACJ,QAAM,SAAS,CAAA;AACf,aAAW,UAAU,aAAa,SAAS;AACvC,QAAI,QAAQ;AACR,YAAM,gBAAgB,KAAK,OAAO,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC/E,YAAM,OAAO,eAAe,YAAY;AACxC,UAAI,MAAM;AACN,mBAAW,CAAC,MAAM,OAAO,KAAK,cAAc,MAAM,OAAO,KAAK,GAAG;AAC7D,iBAAO,KAAK,UAAU,QAAQ,MAAM,OAAO,CAAC;QAC/C;MACJ,OACI;AACD,eAAO,KAAK,UAAU,QAAQ,OAAO,KAAK,cAAc,YAAY,yCAAyC,CAAC;MACjH;IACJ;EACJ;AACD,SAAO;AACX;AACA,SAAS,cAAc,MAAM,UAAU;AACnC,MAAI,CAAC,QAAQ,CAAC,UAAU;AACpB,WAAO,CAAA;EACV;AACD,MAAI,gBAAgB,gBAAgB;AAChC,QAAI,SAAS,SAAS,QAAQ;AAC1B,aAAO,CAAC,CAAC,UAAU,SAAS,IAAI,uCAAuC,CAAC;IAC3E;AACD,WAAO,cAAc,KAAK,QAAQ,QAAQ;EAC7C;AACD,MAAI,SAAS,SAAS,QAAQ;AAC1B,WAAO,CAAA;EACV;AACD,MAAI,gBAAgB,aAAa;AAC7B,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,SAAS,SAAS;AAC3B,YAAM,SAAS,SAAS,UAAU,CAAA;AAClC,aAAO,OAAO,QAAQ,CAAA,SAAQ,cAAc,UAAU,IAAI,CAAC;IAC9D;AACD,WAAO,cAAc,UAAU,QAAQ;EAC1C;AACD,MAAI,gBAAgB,wBAAwB;AACxC,QAAI,SAAS,SAAS,UAAU;AAC5B,aAAO,CAAC,CAAC,UAAU,SAAS,IAAI,sBAAsB,CAAC;IAC1D;AACD,UAAM,iBAAiB,uBAAO,OAAO,IAAI;AACzC,UAAM,cAAc,OAAO,SAAS,SAAS,CAAA,WAAU;AACnD,UAAI;AACJ,YAAM,aAAa,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5H,qBAAe,SAAS,IAAI;AAC5B,YAAM,aAAa,KAAK,UAAW,EAAC,SAAS;AAC7C,UAAI,CAAC,YAAY;AACb,eAAO;UACH;YACI,OAAO;YACP,SAAS,IAAI,4BAA4B,SAAS;UACrD;QACrB;MACa;AACD,YAAM,YAAY,aAAa,WAAW,OAAO;AACjD,aAAO,cAAc,WAAW,OAAO,KAAK;IACxD,CAAS;AACD,eAAW,aAAa,OAAO,KAAK,KAAK,UAAS,CAAE,GAAG;AACnD,YAAM,QAAQ,KAAK,UAAW,EAAC,SAAS;AACxC,UAAI,CAAC,eAAe,SAAS,KACzB,MAAM,gBAAgB,kBACtB,CAAC,MAAM,cAAc;AACrB,oBAAY,KAAK;UACb;UACA,mBAAmB,IAAI,gCAAgC,SAAS;QACpF,CAAiB;MACJ;IACJ;AACD,WAAO;EACV;AACD,MAAK,KAAK,SAAS,aAAa,SAAS,SAAS,aAC7C,KAAK,SAAS,YAAY,SAAS,SAAS,YAC5C,KAAK,SAAS,QACX,SAAS,SAAS,YAClB,SAAS,SAAS,YACrB,KAAK,SAAS,WAAW,SAAS,SAAS,YAC3C,KAAK,SAAS,UACV,SAAS,SAAS,aAAa,SAAS,QAAQ,OAAO,SAAS,QAAS;AAC9E,WAAO,CAAC,CAAC,UAAU,2BAA2B,IAAI,IAAI,CAAC;EAC1D;AACD,OAAK,gBAAgB,mBAAmB,gBAAgB,uBAClD,SAAS,SAAS,YAChB,SAAS,SAAS,YAClB,SAAS,SAAS,aAClB,SAAS,SAAS,UAClB,UAAU,KAAK,WAAW,SAAS,KAAK,CAAC,IAAI;AACjD,WAAO,CAAC,CAAC,UAAU,2BAA2B,IAAI,IAAI,CAAC;EAC1D;AACD,SAAO,CAAA;AACX;AACA,SAAS,UAAU,QAAQ,MAAM,SAAS;AACtC,SAAO;IACH;IACA,UAAU;IACV,MAAM;IACN,MAAM,OAAO,aAAa,KAAK,KAAK;IACpC,IAAI,OAAO,aAAa,KAAK,GAAG;EACxC;AACA;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,UAAU,QAAQ,UAAU,UAAa,UAAU;AAC9D;AACA,SAAS,OAAO,OAAO,QAAQ;AAC3B,SAAO,MAAM,UAAU,OAAO,MAAM,CAAE,GAAE,MAAM,IAAI,MAAM,CAAC;AAC7D;", "names": []}
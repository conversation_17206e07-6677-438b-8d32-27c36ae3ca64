{"version": 3, "sources": ["../../../../date-fns/esm/locale/he/_lib/formatDistance/index.js", "../../../../date-fns/esm/locale/he/_lib/formatLong/index.js", "../../../../date-fns/esm/locale/he/_lib/formatRelative/index.js", "../../../../date-fns/esm/locale/he/_lib/localize/index.js", "../../../../date-fns/esm/locale/he/_lib/match/index.js", "../../../../date-fns/esm/locale/he/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'פחות משנייה',\n    two: 'פחות משתי שניות',\n    other: 'פחות מ־{{count}} שניות'\n  },\n  xSeconds: {\n    one: 'שנייה',\n    two: 'שתי שניות',\n    other: '{{count}} שניות'\n  },\n  halfAMinute: 'חצי דקה',\n  lessThanXMinutes: {\n    one: 'פחות מדקה',\n    two: 'פחות משתי דקות',\n    other: 'פחות מ־{{count}} דקות'\n  },\n  xMinutes: {\n    one: 'דקה',\n    two: 'שתי דקות',\n    other: '{{count}} דקות'\n  },\n  aboutXHours: {\n    one: 'כשעה',\n    two: 'כשעתיים',\n    other: 'כ־{{count}} שעות'\n  },\n  xHours: {\n    one: 'שעה',\n    two: 'שעתיים',\n    other: '{{count}} שעות'\n  },\n  xDays: {\n    one: 'יום',\n    two: 'יומיים',\n    other: '{{count}} ימים'\n  },\n  aboutXWeeks: {\n    one: 'כשבוע',\n    two: 'כשבועיים',\n    other: 'כ־{{count}} שבועות'\n  },\n  xWeeks: {\n    one: 'שבוע',\n    two: 'שבועיים',\n    other: '{{count}} שבועות'\n  },\n  aboutXMonths: {\n    one: 'כחודש',\n    two: 'כחודשיים',\n    other: 'כ־{{count}} חודשים'\n  },\n  xMonths: {\n    one: 'חודש',\n    two: 'חודשיים',\n    other: '{{count}} חודשים'\n  },\n  aboutXYears: {\n    one: 'כשנה',\n    two: 'כשנתיים',\n    other: 'כ־{{count}} שנים'\n  },\n  xYears: {\n    one: 'שנה',\n    two: 'שנתיים',\n    other: '{{count}} שנים'\n  },\n  overXYears: {\n    one: 'יותר משנה',\n    two: 'יותר משנתיים',\n    other: 'יותר מ־{{count}} שנים'\n  },\n  almostXYears: {\n    one: 'כמעט שנה',\n    two: 'כמעט שנתיים',\n    other: 'כמעט {{count}} שנים'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === 'xDays' && options !== null && options !== void 0 && options.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? 'מחר' : 'מחרתיים';\n    }\n    return count === 1 ? 'אתמול' : 'שלשום';\n  }\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'בעוד ' + result;\n    } else {\n      return 'לפני ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: 'EEEE, d בMMMM y',\n  long: 'd בMMMM y',\n  medium: 'd בMMM y',\n  short: 'd.M.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'בשעה' {{time}}\",\n  long: \"{{date}} 'בשעה' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nexport default formatLong;", "var formatRelativeLocale = {\n  lastWeek: \"eeee 'שעבר בשעה' p\",\n  yesterday: \"'אתמול בשעה' p\",\n  today: \"'היום בשעה' p\",\n  tomorrow: \"'מחר בשעה' p\",\n  nextWeek: \"eeee 'בשעה' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['לפנה״ס', 'לספירה'],\n  abbreviated: ['לפנה״ס', 'לספירה'],\n  wide: ['לפני הספירה', 'לספירה']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['רבעון 1', 'רבעון 2', 'רבעון 3', 'רבעון 4']\n};\nvar monthValues = {\n  narrow: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],\n  abbreviated: ['ינו׳', 'פבר׳', 'מרץ', 'אפר׳', 'מאי', 'יוני', 'יולי', 'אוג׳', 'ספט׳', 'אוק׳', 'נוב׳', 'דצמ׳'],\n  wide: ['ינואר', 'פברואר', 'מרץ', 'אפריל', 'מאי', 'יוני', 'יולי', 'אוגוסט', 'ספטמבר', 'אוקטובר', 'נובמבר', 'דצמבר']\n};\nvar dayValues = {\n  narrow: ['א׳', 'ב׳', 'ג׳', 'ד׳', 'ה׳', 'ו׳', 'ש׳'],\n  short: ['א׳', 'ב׳', 'ג׳', 'ד׳', 'ה׳', 'ו׳', 'ש׳'],\n  abbreviated: ['יום א׳', 'יום ב׳', 'יום ג׳', 'יום ד׳', 'יום ה׳', 'יום ו׳', 'שבת'],\n  wide: ['יום ראשון', 'יום שני', 'יום שלישי', 'יום רביעי', 'יום חמישי', 'יום שישי', 'יום שבת']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בוקר',\n    afternoon: 'אחר הצהריים',\n    evening: 'ערב',\n    night: 'לילה'\n  },\n  abbreviated: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בוקר',\n    afternoon: 'אחר הצהריים',\n    evening: 'ערב',\n    night: 'לילה'\n  },\n  wide: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בוקר',\n    afternoon: 'אחר הצהריים',\n    evening: 'ערב',\n    night: 'לילה'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בבוקר',\n    afternoon: 'בצהריים',\n    evening: 'בערב',\n    night: 'בלילה'\n  },\n  abbreviated: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בבוקר',\n    afternoon: 'אחר הצהריים',\n    evening: 'בערב',\n    night: 'בלילה'\n  },\n  wide: {\n    am: 'לפנה״צ',\n    pm: 'אחה״צ',\n    midnight: 'חצות',\n    noon: 'צהריים',\n    morning: 'בבוקר',\n    afternoon: 'אחר הצהריים',\n    evening: 'בערב',\n    night: 'בלילה'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n\n  // We only show words till 10\n  if (number <= 0 || number > 10) return String(number);\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var isFemale = ['year', 'hour', 'minute', 'second'].indexOf(unit) >= 0;\n  var male = ['ראשון', 'שני', 'שלישי', 'רביעי', 'חמישי', 'שישי', 'שביעי', 'שמיני', 'תשיעי', 'עשירי'];\n  var female = ['ראשונה', 'שנייה', 'שלישית', 'רביעית', 'חמישית', 'שישית', 'שביעית', 'שמינית', 'תשיעית', 'עשירית'];\n  var index = number - 1;\n  return isFemale ? female[index] : male[index];\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "import buildMatchFn from \"../../../_lib/buildMatchFn/index.js\";\nimport buildMatchPatternFn from \"../../../_lib/buildMatchPatternFn/index.js\";\nvar matchOrdinalNumberPattern = /^(\\d+|(ראשון|שני|שלישי|רביעי|חמישי|שישי|שביעי|שמיני|תשיעי|עשירי|ראשונה|שנייה|שלישית|רביעית|חמישית|שישית|שביעית|שמינית|תשיעית|עשירית))/i;\nvar parseOrdinalNumberPattern = /^(\\d+|רא|שנ|של|רב|ח|שי|שב|שמ|ת|ע)/i;\nvar matchEraPatterns = {\n  narrow: /^ל(ספירה|פנה״ס)/i,\n  abbreviated: /^ל(ספירה|פנה״ס)/i,\n  wide: /^ל(פני ה)?ספירה/i\n};\nvar parseEraPatterns = {\n  any: [/^לפ/i, /^לס/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^רבעון [1234]/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^\\d+/i,\n  abbreviated: /^(ינו|פבר|מרץ|אפר|מאי|יוני|יולי|אוג|ספט|אוק|נוב|דצמ)׳?/i,\n  wide: /^(ינואר|פברואר|מרץ|אפריל|מאי|יוני|יולי|אוגוסט|ספטמבר|אוקטובר|נובמבר|דצמבר)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^1$/i, /^2/i, /^3/i, /^4/i, /^5/i, /^6/i, /^7/i, /^8/i, /^9/i, /^10/i, /^11/i, /^12/i],\n  any: [/^ינ/i, /^פ/i, /^מר/i, /^אפ/i, /^מא/i, /^יונ/i, /^יול/i, /^אוג/i, /^ס/i, /^אוק/i, /^נ/i, /^ד/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[אבגדהוש]׳/i,\n  short: /^[אבגדהוש]׳/i,\n  abbreviated: /^(שבת|יום (א|ב|ג|ד|ה|ו)׳)/i,\n  wide: /^יום (ראשון|שני|שלישי|רביעי|חמישי|שישי|שבת)/i\n};\nvar parseDayPatterns = {\n  abbreviated: [/א׳$/i, /ב׳$/i, /ג׳$/i, /ד׳$/i, /ה׳$/i, /ו׳$/i, /^ש/i],\n  wide: [/ן$/i, /ני$/i, /לישי$/i, /עי$/i, /מישי$/i, /שישי$/i, /ת$/i],\n  any: [/^א/i, /^ב/i, /^ג/i, /^ד/i, /^ה/i, /^ו/i, /^ש/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(אחר ה|ב)?(חצות|צהריים|בוקר|ערב|לילה|אחה״צ|לפנה״צ)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^לפ/i,\n    pm: /^אחה/i,\n    midnight: /^ח/i,\n    noon: /^צ/i,\n    morning: /בוקר/i,\n    afternoon: /בצ|אחר/i,\n    evening: /ערב/i,\n    night: /לילה/i\n  }\n};\nvar ordinalName = ['רא', 'שנ', 'של', 'רב', 'ח', 'שי', 'שב', 'שמ', 'ת', 'ע'];\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      var number = parseInt(value, 10);\n      return isNaN(number) ? ordinalName.indexOf(value) + 1 : number;\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nexport default match;", "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Hebrew locale.\n * @language Hebrew\n * @iso-639-2 heb\n * <AUTHOR> [@nirlah]{@link https://github.com/nirlah}\n */\nvar locale = {\n  code: 'he',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;"], "mappings": ";;;;;;;;AAAA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,iBAAiB,SAASA,gBAAe,OAAO,OAAO,SAAS;AAElE,MAAI,UAAU,WAAW,YAAY,QAAQ,YAAY,UAAU,QAAQ,aAAa,SAAS,GAAG;AAClG,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,UAAU,IAAI,QAAQ;AAAA,IAC/B;AACA,WAAO,UAAU,IAAI,UAAU;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,aAAa,qBAAqB,KAAK;AAC3C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAC9D;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,yBAAQ;;;ACzGf,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAO,qBAAQ;;;ACjCf,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,iBAAiB,SAASC,gBAAe,OAAO,OAAO,WAAW,UAAU;AAC9E,SAAO,qBAAqB,KAAK;AACnC;AACA,IAAO,yBAAQ;;;ACVf,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,UAAU,QAAQ;AAAA,EAC3B,aAAa,CAAC,UAAU,QAAQ;AAAA,EAChC,MAAM,CAAC,eAAe,QAAQ;AAChC;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,WAAW,WAAW,WAAW,SAAS;AACnD;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI;AAAA,EACtE,aAAa,CAAC,QAAQ,QAAQ,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC1G,MAAM,CAAC,SAAS,UAAU,OAAO,SAAS,OAAO,QAAQ,QAAQ,UAAU,UAAU,WAAW,UAAU,OAAO;AACnH;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACjD,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,KAAK;AAAA,EAC/E,MAAM,CAAC,aAAa,WAAW,aAAa,aAAa,aAAa,YAAY,SAAS;AAC7F;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,gBAAgB,SAASC,eAAc,aAAa,SAAS;AAC/D,MAAI,SAAS,OAAO,WAAW;AAG/B,MAAI,UAAU,KAAK,SAAS,GAAI,QAAO,OAAO,MAAM;AACpD,MAAI,OAAO,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AAChF,MAAI,WAAW,CAAC,QAAQ,QAAQ,UAAU,QAAQ,EAAE,QAAQ,IAAI,KAAK;AACrE,MAAI,OAAO,CAAC,SAAS,OAAO,SAAS,SAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,OAAO;AACjG,MAAI,SAAS,CAAC,UAAU,SAAS,UAAU,UAAU,UAAU,SAAS,UAAU,UAAU,UAAU,QAAQ;AAC9G,MAAI,QAAQ,SAAS;AACrB,SAAO,WAAW,OAAO,KAAK,IAAI,KAAK,KAAK;AAC9C;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAS,iBAAiB,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAO,mBAAQ;;;AC5Hf,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,QAAQ,MAAM;AACtB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,MAAM;AAAA,EAC/F,KAAK,CAAC,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,SAAS,OAAO,KAAK;AACtG;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EACnE,MAAM,CAAC,OAAO,QAAQ,UAAU,QAAQ,UAAU,UAAU,KAAK;AAAA,EACjE,KAAK,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AACvD;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AACP;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,cAAc,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAC1E,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAS,cAAc,OAAO;AAC3C,UAAI,SAAS,SAAS,OAAO,EAAE;AAC/B,aAAO,MAAM,MAAM,IAAI,YAAY,QAAQ,KAAK,IAAI,IAAI;AAAA,IAC1D;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAASC,eAAc,OAAO;AAC3C,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AACA,IAAO,gBAAQ;;;ACtFf,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAO,aAAQ;", "names": ["formatDistance", "formatRelative", "ordinalNumber", "valueCallback"]}
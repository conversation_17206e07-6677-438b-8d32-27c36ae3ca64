{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/scala/scala.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scala/scala.ts\nvar conf = {\n  /*\n   * `...` is allowed as an identifier.\n   * $ is allowed in identifiers.\n   * unary_<op> is allowed as an identifier.\n   * <name>_= is allowed as an identifier.\n   */\n  wordPattern: /(unary_[@~!#%^&*()\\-=+\\\\|:<>\\/?]+)|([a-zA-Z_$][\\w$]*?_=)|(`[^`]+`)|([a-zA-Z_$][\\w$]*)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".scala\",\n  // We can't easily add everything from Dotty, but we can at least add some of its keywords\n  keywords: [\n    \"asInstanceOf\",\n    \"catch\",\n    \"class\",\n    \"classOf\",\n    \"def\",\n    \"do\",\n    \"else\",\n    \"extends\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"forSome\",\n    \"if\",\n    \"import\",\n    \"isInstanceOf\",\n    \"macro\",\n    \"match\",\n    \"new\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"until\",\n    \"val\",\n    \"var\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // Dotty-specific:\n    \"given\",\n    \"enum\",\n    \"then\"\n  ],\n  // Dotty-specific:\n  softKeywords: [\"as\", \"export\", \"extension\", \"end\", \"derives\", \"on\"],\n  constants: [\"true\", \"false\", \"null\", \"this\", \"super\"],\n  modifiers: [\n    \"abstract\",\n    \"final\",\n    \"implicit\",\n    \"lazy\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"sealed\"\n  ],\n  // Dotty-specific:\n  softModifiers: [\"inline\", \"opaque\", \"open\", \"transparent\", \"using\"],\n  name: /(?:[a-z_$][\\w$]*|`[^`]+`)/,\n  type: /(?:[A-Z][\\w$]*)/,\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/^\\\\%@#]+/,\n  digits: /\\d+(_+\\d+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // C# style strings\n  escapes: /\\\\(?:[btnfr\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  fstring_conv: /[bBhHsScCdoxXeEfgGaAt]|[Tn](?:[HIklMSLNpzZsQ]|[BbhAaCYyjmde]|[RTrDFC])/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // strings\n      [/\\braw\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstringt\" }],\n      [/\\braw\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstring\" }],\n      [/\\bs\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstringt\" }],\n      [/\\bs\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }],\n      [/\\bf\"\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstringt\" }],\n      [/\\bf\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstring\" }],\n      [/\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringt\" }],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\", \"@allowMethod\"],\n      [/(@digits)[fFdD]/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)[lL]?/, \"number\", \"@allowMethod\"],\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_)\\b/, \"keyword\", \"@allowMethod\"],\n      // identifiers and keywords\n      [/\\bimport\\b/, \"keyword\", \"@import\"],\n      [/\\b(case)([ \\t]+)(class)\\b/, [\"keyword.modifier\", \"white\", \"keyword\"]],\n      [/\\bcase\\b/, \"keyword\", \"@case\"],\n      [/\\bva[lr]\\b/, \"keyword\", \"@vardef\"],\n      [\n        /\\b(def)([ \\t]+)((?:unary_)?@symbols|@name(?:_=)|@name)/,\n        [\"keyword\", \"white\", \"identifier\"]\n      ],\n      [/@name(?=[ \\t]*:(?!:))/, \"variable\"],\n      [/(\\.)(@name|@symbols)/, [\"operator\", { token: \"@rematch\", next: \"@allowMethod\" }]],\n      [/([{(])(\\s*)(@name(?=\\s*=>))/, [\"@brackets\", \"white\", \"variable\"]],\n      [\n        /@name/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@softKeywords\": \"keyword\",\n            \"@modifiers\": \"keyword.modifier\",\n            \"@softModifiers\": \"keyword.modifier\",\n            \"@constants\": {\n              token: \"constant\",\n              next: \"@allowMethod\"\n            },\n            \"@default\": {\n              token: \"identifier\",\n              next: \"@allowMethod\"\n            }\n          }\n        }\n      ],\n      [/@type/, \"type\", \"@allowMethod\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // @ annotations.\n      [/@[a-zA-Z_$][\\w$]*(?:\\.[a-zA-Z_$][\\w$]*)*/, \"annotation\"],\n      // delimiters and operators\n      [/[{(]/, \"@brackets\"],\n      [/[})]/, \"@brackets\", \"@allowMethod\"],\n      [/\\[/, \"operator.square\"],\n      [/](?!\\s*(?:va[rl]|def|type)\\b)/, \"operator.square\", \"@allowMethod\"],\n      [/]/, \"operator.square\"],\n      [/([=-]>|<-|>:|<:|:>|<%)(?=[\\s\\w()[\\]{},\\.\"'`])/, \"keyword\"],\n      [/@symbols/, \"operator\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,\\.]/, \"delimiter\"],\n      // symbols\n      [/'[a-zA-Z$][\\w$]*(?!')/, \"attribute.name\"],\n      // characters\n      [/'[^\\\\']'/, \"string\", \"@allowMethod\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", { token: \"string\", next: \"@allowMethod\" }]],\n      [/'/, \"string.invalid\"]\n    ],\n    import: [\n      [/;/, \"delimiter\", \"@pop\"],\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/@name|@type/, \"type\"],\n      [/[(){}]/, \"@brackets\"],\n      [/[[\\]]/, \"operator.square\"],\n      [/[\\.,]/, \"delimiter\"]\n    ],\n    allowMethod: [\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/(?==>[\\s\\w([{])/, \"keyword\", \"@pop\"],\n      [\n        /(@name|@symbols)(?=[ \\t]*[[({\"'`]|[ \\t]+(?:[+-]?\\.?\\d|\\w))/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\", next: \"@pop\" },\n            \"->|<-|>:|<:|<%\": { token: \"keyword\", next: \"@pop\" },\n            \"@default\": { token: \"@rematch\", next: \"@pop\" }\n          }\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    case: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\", \"@allowMethod\"],\n      [/\\bif\\b|=>/, \"keyword\", \"@pop\"],\n      [/`[^`]+`/, \"identifier\", \"@allowMethod\"],\n      [/@name/, \"variable\", \"@allowMethod\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      { include: \"@root\" }\n    ],\n    vardef: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\"],\n      [/@name/, \"variable\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      [/=|:(?!:)/, \"operator\", \"@pop\"],\n      [/$/, \"white\", \"@pop\"],\n      { include: \"@root\" }\n    ],\n    string: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    stringt: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    fstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    fstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    sstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    sstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    interp: [[/{/, \"operator\", \"@push\"], [/}/, \"operator\", \"@pop\"], { include: \"@root\" }],\n    rawstring: [\n      [/[^\"]/, \"string\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    rawstringt: [\n      [/[^\"]/, \"string\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MAsCA;AA/CJ;AAAA;AASA,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOT,aAAa;AAAA,MACb,UAAU;AAAA,QACR,aAAa;AAAA,QACb,cAAc,CAAC,MAAM,IAAI;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,oDAAoD;AAAA,UACtE,KAAK,IAAI,OAAO,sDAAsD;AAAA,QACxE;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA;AAAA,MAEd,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,cAAc,CAAC,MAAM,UAAU,aAAa,OAAO,WAAW,IAAI;AAAA,MAClE,WAAW,CAAC,QAAQ,SAAS,QAAQ,QAAQ,OAAO;AAAA,MACpD,WAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,eAAe,CAAC,UAAU,UAAU,QAAQ,eAAe,OAAO;AAAA,MAClE,MAAM;AAAA,MACN,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA;AAAA,MAEX,SAAS;AAAA,MACT,cAAc;AAAA;AAAA,MAEd,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,YAAY,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,cAAc,CAAC;AAAA,UAC7E,CAAC,UAAU,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,UAC1E,CAAC,UAAU,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,YAAY,CAAC;AAAA,UACzE,CAAC,QAAQ,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,UACtE,CAAC,WAAW,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,YAAY,CAAC;AAAA,UAC1E,CAAC,QAAQ,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,UACtE,CAAC,OAAO,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,UACrE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA;AAAA,UAElE,CAAC,0CAA0C,gBAAgB,cAAc;AAAA,UACzE,CAAC,qDAAqD,gBAAgB,cAAc;AAAA,UACpF,CAAC,0BAA0B,cAAc,cAAc;AAAA,UACvD,CAAC,mBAAmB,gBAAgB,cAAc;AAAA,UAClD,CAAC,kBAAkB,UAAU,cAAc;AAAA,UAC3C,CAAC,SAAS,KAAK;AAAA,UACf,CAAC,WAAW,WAAW,cAAc;AAAA;AAAA,UAErC,CAAC,cAAc,WAAW,SAAS;AAAA,UACnC,CAAC,6BAA6B,CAAC,oBAAoB,SAAS,SAAS,CAAC;AAAA,UACtE,CAAC,YAAY,WAAW,OAAO;AAAA,UAC/B,CAAC,cAAc,WAAW,SAAS;AAAA,UACnC;AAAA,YACE;AAAA,YACA,CAAC,WAAW,SAAS,YAAY;AAAA,UACnC;AAAA,UACA,CAAC,yBAAyB,UAAU;AAAA,UACpC,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,eAAe,CAAC,CAAC;AAAA,UAClF,CAAC,+BAA+B,CAAC,aAAa,SAAS,UAAU,CAAC;AAAA,UAClE;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa;AAAA,gBACb,iBAAiB;AAAA,gBACjB,cAAc;AAAA,gBACd,kBAAkB;AAAA,gBAClB,cAAc;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,gBACA,YAAY;AAAA,kBACV,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,SAAS,QAAQ,cAAc;AAAA;AAAA,UAEhC,EAAE,SAAS,cAAc;AAAA;AAAA,UAEzB,CAAC,4CAA4C,YAAY;AAAA;AAAA,UAEzD,CAAC,QAAQ,WAAW;AAAA,UACpB,CAAC,QAAQ,aAAa,cAAc;AAAA,UACpC,CAAC,MAAM,iBAAiB;AAAA,UACxB,CAAC,iCAAiC,mBAAmB,cAAc;AAAA,UACnE,CAAC,KAAK,iBAAiB;AAAA,UACvB,CAAC,iDAAiD,SAAS;AAAA,UAC3D,CAAC,YAAY,UAAU;AAAA;AAAA,UAEvB,CAAC,UAAU,WAAW;AAAA;AAAA,UAEtB,CAAC,yBAAyB,gBAAgB;AAAA;AAAA,UAE1C,CAAC,YAAY,UAAU,cAAc;AAAA,UACrC,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,EAAE,OAAO,UAAU,MAAM,eAAe,CAAC,CAAC;AAAA,UAC3F,CAAC,KAAK,gBAAgB;AAAA,QACxB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,KAAK,aAAa,MAAM;AAAA,UACzB,CAAC,OAAO,IAAI,MAAM;AAAA,UAClB,CAAC,UAAU,OAAO;AAAA,UAClB,CAAC,WAAW,SAAS,MAAM;AAAA,UAC3B,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,eAAe,MAAM;AAAA,UACtB,CAAC,UAAU,WAAW;AAAA,UACtB,CAAC,SAAS,iBAAiB;AAAA,UAC3B,CAAC,SAAS,WAAW;AAAA,QACvB;AAAA,QACA,aAAa;AAAA,UACX,CAAC,OAAO,IAAI,MAAM;AAAA,UAClB,CAAC,UAAU,OAAO;AAAA,UAClB,CAAC,WAAW,SAAS,MAAM;AAAA,UAC3B,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,mBAAmB,WAAW,MAAM;AAAA,UACrC;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,aAAa,EAAE,OAAO,WAAW,MAAM,OAAO;AAAA,gBAC9C,kBAAkB,EAAE,OAAO,WAAW,MAAM,OAAO;AAAA,gBACnD,YAAY,EAAE,OAAO,YAAY,MAAM,OAAO;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,IAAI,IAAI,MAAM;AAAA,QACjB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,SAAS;AAAA,UACrB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,UAE3B,CAAC,QAAQ,WAAW,MAAM;AAAA,UAC1B,CAAC,SAAS,SAAS;AAAA,QACrB;AAAA,QACA,MAAM;AAAA,UACJ,CAAC,SAAS,KAAK;AAAA,UACf,CAAC,sCAAsC,WAAW,cAAc;AAAA,UAChE,CAAC,aAAa,WAAW,MAAM;AAAA,UAC/B,CAAC,WAAW,cAAc,cAAc;AAAA,UACxC,CAAC,SAAS,YAAY,cAAc;AAAA,UACpC,CAAC,wBAAwB,SAAS;AAAA,UAClC,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,SAAS,KAAK;AAAA,UACf,CAAC,sCAAsC,SAAS;AAAA,UAChD,CAAC,SAAS,UAAU;AAAA,UACpB,CAAC,wBAAwB,SAAS;AAAA,UAClC,CAAC,YAAY,YAAY,MAAM;AAAA,UAC/B,CAAC,KAAK,SAAS,MAAM;AAAA,UACrB,EAAE,SAAS,QAAQ;AAAA,QACrB;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,CAAC,eAAe,QAAQ;AAAA,UACxB,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,OAAO,uBAAuB;AAAA,UAC/B,CAAC,YAAY,QAAQ;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,YAAY,eAAe;AAAA,UAC5B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,QAAQ;AAAA,UACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,UAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,UAC9B,CAAC,MAAM,QAAQ;AAAA,UACf;AAAA,YACE;AAAA,YACA,CAAC,WAAW,oBAAoB,UAAU,SAAS;AAAA,UACrD;AAAA,UACA,CAAC,0CAA0C,CAAC,WAAW,UAAU,SAAS,CAAC;AAAA,UAC3E,CAAC,kCAAkC,CAAC,WAAW,oBAAoB,SAAS,CAAC;AAAA,UAC7E,CAAC,sBAAsB,CAAC,WAAW,SAAS,CAAC;AAAA,UAC7C,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,UACR,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,YAAY,QAAQ;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,QAAQ;AAAA,UACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,UAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,UAC9B,CAAC,MAAM,QAAQ;AAAA,UACf;AAAA,YACE;AAAA,YACA,CAAC,WAAW,oBAAoB,UAAU,SAAS;AAAA,UACrD;AAAA,UACA,CAAC,0CAA0C,CAAC,WAAW,UAAU,SAAS,CAAC;AAAA,UAC3E,CAAC,kCAAkC,CAAC,WAAW,oBAAoB,SAAS,CAAC;AAAA,UAC7E,CAAC,sBAAsB,CAAC,WAAW,SAAS,CAAC;AAAA,UAC7C,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,SAAS;AAAA,UACP,CAAC,YAAY,eAAe;AAAA,UAC5B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,QAAQ;AAAA,UACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,UAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,UAC9B,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,UACR,CAAC,YAAY,eAAe;AAAA,UAC5B,CAAC,YAAY,QAAQ;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,QAAQ,QAAQ;AAAA,UACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,UAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,UAC9B,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,QAAQ,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,CAAC,KAAK,YAAY,MAAM,GAAG,EAAE,SAAS,QAAQ,CAAC;AAAA,QACpF,WAAW;AAAA,UACT,CAAC,QAAQ,QAAQ;AAAA,UACjB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,CAAC,QAAQ,QAAQ;AAAA,UACjB,CAAC,YAAY,QAAQ;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,CAAC,KAAK,QAAQ;AAAA,QAChB;AAAA,QACA,YAAY;AAAA,UACV,CAAC,cAAc,OAAO;AAAA,UACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,UAC9B,CAAC,WAAW,SAAS;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
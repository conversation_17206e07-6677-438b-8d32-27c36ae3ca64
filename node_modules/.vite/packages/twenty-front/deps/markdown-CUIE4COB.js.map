{"version": 3, "sources": ["../../../../monaco-editor/esm/vs/basic-languages/markdown/markdown.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.51.0(67d664a32968e19e2eb08b696a92463804182ae4)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/markdown/markdown.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".md\",\n  // escape codes\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  // escape codes for javascript/CSS strings\n  jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  // non matched elements\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  tokenizer: {\n    root: [\n      // markdown tables\n      [/^\\s*\\|/, \"@rematch\", \"@table_header\"],\n      // headers (with #)\n      [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, [\"white\", \"keyword\", \"keyword\", \"keyword\"]],\n      // headers (with =)\n      [/^\\s*(=+|\\-+)\\s*$/, \"keyword\"],\n      // headers (with ***)\n      [/^\\s*((\\*[ ]?)+)\\s*$/, \"meta.separator\"],\n      // quote\n      [/^\\s*>+/, \"comment\"],\n      // list (starting with * or number)\n      [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, \"keyword\"],\n      // code block (4 spaces indent)\n      [/^(\\t|[ ]{4})[^ ].*$/, \"string\"],\n      // code block (3 tilde)\n      [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // github style code blocks (with backticks and language)\n      [\n        /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n        { token: \"string\", next: \"@codeblockgh\", nextEmbedded: \"$1\" }\n      ],\n      // github style code blocks (with backticks but no language)\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // markup within lines\n      { include: \"@linecontent\" }\n    ],\n    table_header: [\n      { include: \"@table_common\" },\n      [/[^\\|]+/, \"keyword.table.header\"]\n      // table header\n    ],\n    table_body: [{ include: \"@table_common\" }, { include: \"@linecontent\" }],\n    table_common: [\n      [/\\s*[\\-:]+\\s*/, { token: \"keyword\", switchTo: \"table_body\" }],\n      // header-divider\n      [/^\\s*\\|/, \"keyword.table.left\"],\n      // opening |\n      [/^\\s*[^\\|]/, \"@rematch\", \"@pop\"],\n      // exiting\n      [/^\\s*$/, \"@rematch\", \"@pop\"],\n      // exiting\n      [\n        /\\|/,\n        {\n          cases: {\n            \"@eos\": \"keyword.table.right\",\n            // closing |\n            \"@default\": \"keyword.table.middle\"\n            // inner |\n          }\n        }\n      ]\n    ],\n    codeblock: [\n      [/^\\s*~~~\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    // github style code blocks\n    codeblockgh: [\n      [/```\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^`]+/, \"variable.source\"]\n    ],\n    linecontent: [\n      // escapes\n      [/&\\w+;/, \"string.escape\"],\n      [/@escapes/, \"escape\"],\n      // various markup\n      [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, \"strong\"],\n      [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\b_[^_]+_\\b/, \"emphasis\"],\n      [/\\*([^\\\\*]|@escapes)+\\*/, \"emphasis\"],\n      [/`([^\\\\`]|@escapes)+`/, \"variable\"],\n      // links\n      [/\\{+[^}]+\\}+/, \"string.target\"],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, [\"string.link\", \"\", \"string.link\"]],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, \"string.link\"],\n      // or html\n      { include: \"html\" }\n    ],\n    // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n    // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n    // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n    // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n    // we cannot correctly tokenize it in that mode yet.\n    html: [\n      // html tags\n      [/<(\\w+)\\/>/, \"tag\"],\n      [\n        /<(\\w+)(\\-|\\w)*/,\n        {\n          cases: {\n            \"@empty\": { token: \"tag\", next: \"@tag.$1\" },\n            \"@default\": { token: \"tag\", next: \"@tag.$1\" }\n          }\n        }\n      ],\n      [/<\\/(\\w+)(\\-|\\w)*\\s*>/, { token: \"tag\" }],\n      [/<!--/, \"comment\", \"@comment\"]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, \"comment\", \"@pop\"],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    // Almost full HTML tag matching, complete with embedded scripts & styles\n    tag: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [\n        /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [\n        /(type)(\\s*=\\s*)(')([^']+)(')/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name.html\", \"delimiter.html\", \"string.html\"]],\n      [/\\w+/, \"attribute.name.html\"],\n      [/\\/>/, \"tag\", \"@pop\"],\n      [\n        />/,\n        {\n          cases: {\n            \"$S2==style\": {\n              token: \"tag\",\n              switchTo: \"embeddedStyle\",\n              nextEmbedded: \"text/css\"\n            },\n            \"$S2==script\": {\n              cases: {\n                $S3: {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"$S3\"\n                },\n                \"@default\": {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"text/javascript\"\n                }\n              }\n            },\n            \"@default\": { token: \"tag\", next: \"@pop\" }\n          }\n        }\n      ]\n    ],\n    embeddedStyle: [\n      [/[^<]+/, \"\"],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ],\n    embeddedScript: [\n      [/[^<]+/, \"\"],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;;;AAAA,IASI,MA2BA;AApCJ;AAAA;AASA,IAAI,OAAO;AAAA,MACT,UAAU;AAAA,QACR,cAAc,CAAC,QAAQ,KAAK;AAAA,MAC9B;AAAA,MACA,UAAU;AAAA,QACR,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,QACT,CAAC,KAAK,GAAG;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,MAC7C;AAAA,MACA,kBAAkB;AAAA,QAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,QACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO,IAAI,OAAO,+BAA+B;AAAA,UACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA;AAAA,MAEd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA;AAAA,MAET,WAAW;AAAA;AAAA,MAEX,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,MAAM;AAAA;AAAA,UAEJ,CAAC,UAAU,YAAY,eAAe;AAAA;AAAA,UAEtC,CAAC,iDAAiD,CAAC,SAAS,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,UAE5F,CAAC,oBAAoB,SAAS;AAAA;AAAA,UAE9B,CAAC,uBAAuB,gBAAgB;AAAA;AAAA,UAExC,CAAC,UAAU,SAAS;AAAA;AAAA,UAEpB,CAAC,0BAA0B,SAAS;AAAA;AAAA,UAEpC,CAAC,uBAAuB,QAAQ;AAAA;AAAA,UAEhC,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,UAE5E;AAAA,YACE;AAAA,YACA,EAAE,OAAO,UAAU,MAAM,gBAAgB,cAAc,KAAK;AAAA,UAC9D;AAAA;AAAA,UAEA,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,UAEvD,EAAE,SAAS,eAAe;AAAA,QAC5B;AAAA,QACA,cAAc;AAAA,UACZ,EAAE,SAAS,gBAAgB;AAAA,UAC3B,CAAC,UAAU,sBAAsB;AAAA;AAAA,QAEnC;AAAA,QACA,YAAY,CAAC,EAAE,SAAS,gBAAgB,GAAG,EAAE,SAAS,eAAe,CAAC;AAAA,QACtE,cAAc;AAAA,UACZ,CAAC,gBAAgB,EAAE,OAAO,WAAW,UAAU,aAAa,CAAC;AAAA;AAAA,UAE7D,CAAC,UAAU,oBAAoB;AAAA;AAAA,UAE/B,CAAC,aAAa,YAAY,MAAM;AAAA;AAAA,UAEhC,CAAC,SAAS,YAAY,MAAM;AAAA;AAAA,UAE5B;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,QAAQ;AAAA;AAAA,gBAER,YAAY;AAAA;AAAA,cAEd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UACjD,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,UACjD,CAAC,OAAO,iBAAiB;AAAA,QAC3B;AAAA;AAAA,QAEA,aAAa;AAAA,UACX,CAAC,WAAW,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UACnE,CAAC,SAAS,iBAAiB;AAAA,QAC7B;AAAA,QACA,aAAa;AAAA;AAAA,UAEX,CAAC,SAAS,eAAe;AAAA,UACzB,CAAC,YAAY,QAAQ;AAAA;AAAA,UAErB,CAAC,qCAAqC,QAAQ;AAAA,UAC9C,CAAC,uCAAuC,QAAQ;AAAA,UAChD,CAAC,eAAe,UAAU;AAAA,UAC1B,CAAC,0BAA0B,UAAU;AAAA,UACrC,CAAC,wBAAwB,UAAU;AAAA;AAAA,UAEnC,CAAC,eAAe,eAAe;AAAA,UAC/B,CAAC,+CAA+C,CAAC,eAAe,IAAI,aAAa,CAAC;AAAA,UAClF,CAAC,qCAAqC,aAAa;AAAA;AAAA,UAEnD,EAAE,SAAS,OAAO;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM;AAAA;AAAA,UAEJ,CAAC,aAAa,KAAK;AAAA,UACnB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,UAAU,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,gBAC1C,YAAY,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,wBAAwB,EAAE,OAAO,MAAM,CAAC;AAAA,UACzC,CAAC,QAAQ,WAAW,UAAU;AAAA,QAChC;AAAA,QACA,SAAS;AAAA,UACP,CAAC,WAAW,iBAAiB;AAAA,UAC7B,CAAC,OAAO,WAAW,MAAM;AAAA,UACzB,CAAC,QAAQ,yBAAyB;AAAA,UAClC,CAAC,SAAS,iBAAiB;AAAA,QAC7B;AAAA;AAAA,QAEA,KAAK;AAAA,UACH,CAAC,cAAc,OAAO;AAAA,UACtB;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,cACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAAA,UACA,CAAC,mCAAmC,CAAC,uBAAuB,kBAAkB,aAAa,CAAC;AAAA,UAC5F,CAAC,OAAO,qBAAqB;AAAA,UAC7B,CAAC,OAAO,OAAO,MAAM;AAAA,UACrB;AAAA,YACE;AAAA,YACA;AAAA,cACE,OAAO;AAAA,gBACL,cAAc;AAAA,kBACZ,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,cAAc;AAAA,gBAChB;AAAA,gBACA,eAAe;AAAA,kBACb,OAAO;AAAA,oBACL,KAAK;AAAA,sBACH,OAAO;AAAA,sBACP,UAAU;AAAA,sBACV,cAAc;AAAA,oBAChB;AAAA,oBACA,YAAY;AAAA,sBACV,OAAO;AAAA,sBACP,UAAU;AAAA,sBACV,cAAc;AAAA,oBAChB;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA,YAAY,EAAE,OAAO,OAAO,MAAM,OAAO;AAAA,cAC3C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb,CAAC,SAAS,EAAE;AAAA,UACZ,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UAC1E,CAAC,KAAK,EAAE;AAAA,QACV;AAAA,QACA,gBAAgB;AAAA,UACd,CAAC,SAAS,EAAE;AAAA,UACZ,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,UAC3E,CAAC,KAAK,EAAE;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}
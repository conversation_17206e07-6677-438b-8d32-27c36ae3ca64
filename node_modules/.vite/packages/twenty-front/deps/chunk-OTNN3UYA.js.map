{"version": 3, "sources": ["../../../../node_modules/codemirror/addon/edit/matchbrackets.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  var ie_lt8 = /MSIE \\d/.test(navigator.userAgent) &&\n    (document.documentMode == null || document.documentMode < 8);\n\n  var Pos = CodeMirror.Pos;\n\n  var matching = {\"(\": \")>\", \")\": \"(<\", \"[\": \"]>\", \"]\": \"[<\", \"{\": \"}>\", \"}\": \"{<\", \"<\": \">>\", \">\": \"<<\"};\n\n  function bracketRegex(config) {\n    return config && config.bracketRegex || /[(){}[\\]]/\n  }\n\n  function findMatchingBracket(cm, where, config) {\n    var line = cm.getLineHandle(where.line), pos = where.ch - 1;\n    var afterCursor = config && config.afterCursor\n    if (afterCursor == null)\n      afterCursor = /(^| )cm-fat-cursor($| )/.test(cm.getWrapperElement().className)\n    var re = bracketRegex(config)\n\n    // A cursor is defined as between two characters, but in in vim command mode\n    // (i.e. not insert mode), the cursor is visually represented as a\n    // highlighted box on top of the 2nd character. Otherwise, we allow matches\n    // from before or after the cursor.\n    var match = (!afterCursor && pos >= 0 && re.test(line.text.charAt(pos)) && matching[line.text.charAt(pos)]) ||\n        re.test(line.text.charAt(pos + 1)) && matching[line.text.charAt(++pos)];\n    if (!match) return null;\n    var dir = match.charAt(1) == \">\" ? 1 : -1;\n    if (config && config.strict && (dir > 0) != (pos == where.ch)) return null;\n    var style = cm.getTokenTypeAt(Pos(where.line, pos + 1));\n\n    var found = scanForBracket(cm, Pos(where.line, pos + (dir > 0 ? 1 : 0)), dir, style, config);\n    if (found == null) return null;\n    return {from: Pos(where.line, pos), to: found && found.pos,\n            match: found && found.ch == match.charAt(0), forward: dir > 0};\n  }\n\n  // bracketRegex is used to specify which type of bracket to scan\n  // should be a regexp, e.g. /[[\\]]/\n  //\n  // Note: If \"where\" is on an open bracket, then this bracket is ignored.\n  //\n  // Returns false when no bracket was found, null when it reached\n  // maxScanLines and gave up\n  function scanForBracket(cm, where, dir, style, config) {\n    var maxScanLen = (config && config.maxScanLineLength) || 10000;\n    var maxScanLines = (config && config.maxScanLines) || 1000;\n\n    var stack = [];\n    var re = bracketRegex(config)\n    var lineEnd = dir > 0 ? Math.min(where.line + maxScanLines, cm.lastLine() + 1)\n                          : Math.max(cm.firstLine() - 1, where.line - maxScanLines);\n    for (var lineNo = where.line; lineNo != lineEnd; lineNo += dir) {\n      var line = cm.getLine(lineNo);\n      if (!line) continue;\n      var pos = dir > 0 ? 0 : line.length - 1, end = dir > 0 ? line.length : -1;\n      if (line.length > maxScanLen) continue;\n      if (lineNo == where.line) pos = where.ch - (dir < 0 ? 1 : 0);\n      for (; pos != end; pos += dir) {\n        var ch = line.charAt(pos);\n        if (re.test(ch) && (style === undefined ||\n                            (cm.getTokenTypeAt(Pos(lineNo, pos + 1)) || \"\") == (style || \"\"))) {\n          var match = matching[ch];\n          if (match && (match.charAt(1) == \">\") == (dir > 0)) stack.push(ch);\n          else if (!stack.length) return {pos: Pos(lineNo, pos), ch: ch};\n          else stack.pop();\n        }\n      }\n    }\n    return lineNo - dir == (dir > 0 ? cm.lastLine() : cm.firstLine()) ? false : null;\n  }\n\n  function matchBrackets(cm, autoclear, config) {\n    // Disable brace matching in long lines, since it'll cause hugely slow updates\n    var maxHighlightLen = cm.state.matchBrackets.maxHighlightLineLength || 1000,\n      highlightNonMatching = config && config.highlightNonMatching;\n    var marks = [], ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      var match = ranges[i].empty() && findMatchingBracket(cm, ranges[i].head, config);\n      if (match && (match.match || highlightNonMatching !== false) && cm.getLine(match.from.line).length <= maxHighlightLen) {\n        var style = match.match ? \"CodeMirror-matchingbracket\" : \"CodeMirror-nonmatchingbracket\";\n        marks.push(cm.markText(match.from, Pos(match.from.line, match.from.ch + 1), {className: style}));\n        if (match.to && cm.getLine(match.to.line).length <= maxHighlightLen)\n          marks.push(cm.markText(match.to, Pos(match.to.line, match.to.ch + 1), {className: style}));\n      }\n    }\n\n    if (marks.length) {\n      // Kludge to work around the IE bug from issue #1193, where text\n      // input stops going to the textarea whenever this fires.\n      if (ie_lt8 && cm.state.focused) cm.focus();\n\n      var clear = function() {\n        cm.operation(function() {\n          for (var i = 0; i < marks.length; i++) marks[i].clear();\n        });\n      };\n      if (autoclear) setTimeout(clear, 800);\n      else return clear;\n    }\n  }\n\n  function doMatchBrackets(cm) {\n    cm.operation(function() {\n      if (cm.state.matchBrackets.currentlyHighlighted) {\n        cm.state.matchBrackets.currentlyHighlighted();\n        cm.state.matchBrackets.currentlyHighlighted = null;\n      }\n      cm.state.matchBrackets.currentlyHighlighted = matchBrackets(cm, false, cm.state.matchBrackets);\n    });\n  }\n\n  function clearHighlighted(cm) {\n    if (cm.state.matchBrackets && cm.state.matchBrackets.currentlyHighlighted) {\n      cm.state.matchBrackets.currentlyHighlighted();\n      cm.state.matchBrackets.currentlyHighlighted = null;\n    }\n  }\n\n  CodeMirror.defineOption(\"matchBrackets\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.off(\"cursorActivity\", doMatchBrackets);\n      cm.off(\"focus\", doMatchBrackets)\n      cm.off(\"blur\", clearHighlighted)\n      clearHighlighted(cm);\n    }\n    if (val) {\n      cm.state.matchBrackets = typeof val == \"object\" ? val : {};\n      cm.on(\"cursorActivity\", doMatchBrackets);\n      cm.on(\"focus\", doMatchBrackets)\n      cm.on(\"blur\", clearHighlighted)\n    }\n  });\n\n  CodeMirror.defineExtension(\"matchBrackets\", function() {matchBrackets(this, true);});\n  CodeMirror.defineExtension(\"findMatchingBracket\", function(pos, config, oldConfig){\n    // Backwards-compatibility kludge\n    if (oldConfig || typeof config == \"boolean\") {\n      if (!oldConfig) {\n        config = config ? {strict: true} : null\n      } else {\n        oldConfig.strict = config\n        config = oldConfig\n      }\n    }\n    return findMatchingBracket(this, pos, config)\n  });\n  CodeMirror.defineExtension(\"scanForBracket\", function(pos, dir, style, config){\n    return scanForBracket(this, pos, dir, style, config);\n  });\n});\n"], "mappings": ";;;;;;;;;;;AAGA,KAAC,SAAS,KAAK;AAEX,UAAIA,kBAA+B,CAAA;IAKtC,GAAE,SAAS,YAAY;AACtB,UAAI,SAAS,UAAU,KAAK,UAAU,SAAS,MAC5C,SAAS,gBAAgB,QAAQ,SAAS,eAAe;AAE5D,UAAI,MAAM,WAAW;AAErB,UAAI,WAAW,EAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAI;AAEtG,eAAS,aAAa,QAAQ;AAC5B,eAAO,UAAU,OAAO,gBAAgB;MACzC;AAED,eAAS,oBAAoB,IAAI,OAAO,QAAQ;AAC9C,YAAI,OAAO,GAAG,cAAc,MAAM,IAAI,GAAG,MAAM,MAAM,KAAK;AAC1D,YAAI,cAAc,UAAU,OAAO;AACnC,YAAI,eAAe;AACjB,wBAAc,0BAA0B,KAAK,GAAG,kBAAiB,EAAG,SAAS;AAC/E,YAAI,KAAK,aAAa,MAAM;AAM5B,YAAI,QAAS,CAAC,eAAe,OAAO,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,GAAG,CAAC,KAAK,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,KACrG,GAAG,KAAK,KAAK,KAAK,OAAO,MAAM,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,OAAO,EAAE,GAAG,CAAC;AAC1E,YAAI,CAAC,MAAO,QAAO;AACnB,YAAI,MAAM,MAAM,OAAO,CAAC,KAAK,MAAM,IAAI;AACvC,YAAI,UAAU,OAAO,UAAW,MAAM,MAAO,OAAO,MAAM,IAAK,QAAO;AACtE,YAAI,QAAQ,GAAG,eAAe,IAAI,MAAM,MAAM,MAAM,CAAC,CAAC;AAEtD,YAAI,QAAQ,eAAe,IAAI,IAAI,MAAM,MAAM,OAAO,MAAM,IAAI,IAAI,EAAE,GAAG,KAAK,OAAO,MAAM;AAC3F,YAAI,SAAS,KAAM,QAAO;AAC1B,eAAO;UAAC,MAAM,IAAI,MAAM,MAAM,GAAG;UAAG,IAAI,SAAS,MAAM;UAC/C,OAAO,SAAS,MAAM,MAAM,MAAM,OAAO,CAAC;UAAG,SAAS,MAAM;QAAC;MACtE;AASD,eAAS,eAAe,IAAI,OAAO,KAAK,OAAO,QAAQ;AACrD,YAAI,aAAc,UAAU,OAAO,qBAAsB;AACzD,YAAI,eAAgB,UAAU,OAAO,gBAAiB;AAEtD,YAAI,QAAQ,CAAA;AACZ,YAAI,KAAK,aAAa,MAAM;AAC5B,YAAI,UAAU,MAAM,IAAI,KAAK,IAAI,MAAM,OAAO,cAAc,GAAG,SAAQ,IAAK,CAAC,IACrD,KAAK,IAAI,GAAG,UAAS,IAAK,GAAG,MAAM,OAAO,YAAY;AAC9E,iBAAS,SAAS,MAAM,MAAM,UAAU,SAAS,UAAU,KAAK;AAC9D,cAAI,OAAO,GAAG,QAAQ,MAAM;AAC5B,cAAI,CAAC,KAAM;AACX,cAAI,MAAM,MAAM,IAAI,IAAI,KAAK,SAAS,GAAG,MAAM,MAAM,IAAI,KAAK,SAAS;AACvE,cAAI,KAAK,SAAS,WAAY;AAC9B,cAAI,UAAU,MAAM,KAAM,OAAM,MAAM,MAAM,MAAM,IAAI,IAAI;AAC1D,iBAAO,OAAO,KAAK,OAAO,KAAK;AAC7B,gBAAI,KAAK,KAAK,OAAO,GAAG;AACxB,gBAAI,GAAG,KAAK,EAAE,MAAM,UAAU,WACT,GAAG,eAAe,IAAI,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAQ,SAAS,MAAM;AACrF,kBAAI,QAAQ,SAAS,EAAE;AACvB,kBAAI,SAAU,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,EAAI,OAAM,KAAK,EAAE;uBACxD,CAAC,MAAM,OAAQ,QAAO,EAAC,KAAK,IAAI,QAAQ,GAAG,GAAG,GAAM;kBACxD,OAAM,IAAG;YACf;UACF;QACF;AACD,eAAO,SAAS,QAAQ,MAAM,IAAI,GAAG,SAAU,IAAG,GAAG,UAAS,KAAM,QAAQ;MAC7E;AAED,eAAS,cAAc,IAAI,WAAW,QAAQ;AAE5C,YAAI,kBAAkB,GAAG,MAAM,cAAc,0BAA0B,KACrE,uBAAuB,UAAU,OAAO;AAC1C,YAAI,QAAQ,CAAE,GAAE,SAAS,GAAG,eAAc;AAC1C,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC,EAAE,MAAK,KAAM,oBAAoB,IAAI,OAAO,CAAC,EAAE,MAAM,MAAM;AAC/E,cAAI,UAAU,MAAM,SAAS,yBAAyB,UAAU,GAAG,QAAQ,MAAM,KAAK,IAAI,EAAE,UAAU,iBAAiB;AACrH,gBAAI,QAAQ,MAAM,QAAQ,+BAA+B;AACzD,kBAAM,KAAK,GAAG,SAAS,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,CAAC,GAAG,EAAC,WAAW,MAAK,CAAC,CAAC;AAC/F,gBAAI,MAAM,MAAM,GAAG,QAAQ,MAAM,GAAG,IAAI,EAAE,UAAU;AAClD,oBAAM,KAAK,GAAG,SAAS,MAAM,IAAI,IAAI,MAAM,GAAG,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,EAAC,WAAW,MAAK,CAAC,CAAC;UAC5F;QACF;AAED,YAAI,MAAM,QAAQ;AAGhB,cAAI,UAAU,GAAG,MAAM,QAAS,IAAG,MAAA;AAEnC,cAAI,QAAQ,WAAW;AACrB,eAAG,UAAU,WAAW;AACtB,uBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,KAAK,OAAMA,EAAC,EAAE,MAAK;YAC/D,CAAS;UACT;AACM,cAAI,UAAW,YAAW,OAAO,GAAG;cAC/B,QAAO;QACb;MACF;AAED,eAAS,gBAAgB,IAAI;AAC3B,WAAG,UAAU,WAAW;AACtB,cAAI,GAAG,MAAM,cAAc,sBAAsB;AAC/C,eAAG,MAAM,cAAc,qBAAA;AACvB,eAAG,MAAM,cAAc,uBAAuB;UAC/C;AACD,aAAG,MAAM,cAAc,uBAAuB,cAAc,IAAI,OAAO,GAAG,MAAM,aAAa;QACnG,CAAK;MACF;AAED,eAAS,iBAAiB,IAAI;AAC5B,YAAI,GAAG,MAAM,iBAAiB,GAAG,MAAM,cAAc,sBAAsB;AACzE,aAAG,MAAM,cAAc,qBAAA;AACvB,aAAG,MAAM,cAAc,uBAAuB;QAC/C;MACF;AAED,iBAAW,aAAa,iBAAiB,OAAO,SAAS,IAAI,KAAK,KAAK;AACrE,YAAI,OAAO,OAAO,WAAW,MAAM;AACjC,aAAG,IAAI,kBAAkB,eAAe;AACxC,aAAG,IAAI,SAAS,eAAe;AAC/B,aAAG,IAAI,QAAQ,gBAAgB;AAC/B,2BAAiB,EAAE;QACpB;AACD,YAAI,KAAK;AACP,aAAG,MAAM,gBAAgB,OAAO,OAAO,WAAW,MAAM,CAAA;AACxD,aAAG,GAAG,kBAAkB,eAAe;AACvC,aAAG,GAAG,SAAS,eAAe;AAC9B,aAAG,GAAG,QAAQ,gBAAgB;QAC/B;MACL,CAAG;AAED,iBAAW,gBAAgB,iBAAiB,WAAW;AAAC,sBAAc,MAAM,IAAI;MAAE,CAAC;AACnF,iBAAW,gBAAgB,uBAAuB,SAAS,KAAK,QAAQ,WAAU;AAEhF,YAAI,aAAa,OAAO,UAAU,WAAW;AAC3C,cAAI,CAAC,WAAW;AACd,qBAAS,SAAS,EAAC,QAAQ,KAAI,IAAI;UAC3C,OAAa;AACL,sBAAU,SAAS;AACnB,qBAAS;UACV;QACF;AACD,eAAO,oBAAoB,MAAM,KAAK,MAAM;MAChD,CAAG;AACD,iBAAW,gBAAgB,kBAAkB,SAAS,KAAK,KAAK,OAAO,QAAO;AAC5E,eAAO,eAAe,MAAM,KAAK,KAAK,OAAO,MAAM;MACvD,CAAG;IACH,CAAC;EAAA,GAAA;;;", "names": ["require$$0", "i"]}
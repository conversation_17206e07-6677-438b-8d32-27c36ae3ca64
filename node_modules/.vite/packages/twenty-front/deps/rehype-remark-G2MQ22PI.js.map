{"version": 3, "sources": ["../../../../hast-util-to-mdast/node_modules/unist-util-position/lib/index.js", "../../../../hast-util-to-mdast/lib/handlers/a.js", "../../../../hast-util-to-mdast/lib/handlers/base.js", "../../../../hast-util-to-mdast/lib/handlers/blockquote.js", "../../../../hast-util-to-mdast/lib/handlers/br.js", "../../../../trim-trailing-lines/index.js", "../../../../hast-util-to-mdast/lib/handlers/code.js", "../../../../hast-util-to-mdast/lib/handlers/comment.js", "../../../../hast-util-to-mdast/lib/handlers/del.js", "../../../../hast-util-to-mdast/lib/util/list-items-spread.js", "../../../../hast-util-to-mdast/lib/handlers/dl.js", "../../../../hast-util-to-mdast/lib/handlers/em.js", "../../../../hast-util-to-mdast/lib/util/drop-surrounding-breaks.js", "../../../../hast-util-to-mdast/lib/handlers/heading.js", "../../../../hast-util-to-mdast/lib/handlers/hr.js", "../../../../hast-util-to-mdast/lib/handlers/iframe.js", "../../../../hast-util-to-mdast/lib/handlers/img.js", "../../../../hast-util-to-mdast/lib/handlers/inline-code.js", "../../../../hast-util-to-mdast/lib/util/find-selected-options.js", "../../../../hast-util-to-mdast/lib/handlers/input.js", "../../../../hast-util-to-mdast/lib/handlers/li.js", "../../../../hast-util-to-mdast/lib/handlers/list.js", "../../../../hast-util-to-mdast/node_modules/mdast-util-to-string/lib/index.js", "../../../../hast-util-to-mdast/lib/util/wrap.js", "../../../../hast-util-to-mdast/lib/handlers/media.js", "../../../../hast-util-to-mdast/lib/handlers/p.js", "../../../../hast-util-to-mdast/lib/handlers/q.js", "../../../../hast-util-to-mdast/lib/handlers/root.js", "../../../../hast-util-to-mdast/lib/handlers/select.js", "../../../../hast-util-to-mdast/lib/handlers/strong.js", "../../../../hast-util-to-mdast/lib/handlers/table-cell.js", "../../../../hast-util-to-mdast/lib/handlers/table-row.js", "../../../../hast-util-to-mdast/lib/handlers/table.js", "../../../../hast-util-to-mdast/lib/handlers/text.js", "../../../../hast-util-to-mdast/lib/handlers/textarea.js", "../../../../hast-util-to-mdast/lib/handlers/wbr.js", "../../../../hast-util-to-mdast/lib/handlers/index.js", "../../../../hast-util-to-mdast/lib/state.js", "../../../../hast-util-to-mdast/lib/index.js", "../../../../rehype-remark/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    if (\n      typeof point.line === 'number' &&\n      point.line > 0 &&\n      typeof point.column === 'number' &&\n      point.column > 0\n    ) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset:\n          typeof point.offset === 'number' && point.offset > -1\n            ? point.offset\n            : undefined\n      }\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node)\n  const end = pointEnd(node)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link}\n *   mdast node.\n */\nexport function a(state, node) {\n  const properties = node.properties || {}\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    url: state.resolve(String(properties.href || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {undefined}\n *   Nothing.\n */\nexport function base(state, node) {\n  if (!state.baseFound) {\n    state.frozenBaseUrl =\n      String((node.properties && node.properties.href) || '') || undefined\n    state.baseFound = true\n  }\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Blockquote} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Blockquote}\n *   mdast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Blockquote} */\n  const result = {type: 'blockquote', children: state.toFlow(state.all(node))}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Break} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Break}\n *   mdast node.\n */\nexport function br(state, node) {\n  /** @type {Break} */\n  const result = {type: 'break'}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * Remove final line endings from `value`\n *\n * @param {unknown} value\n *   Value with trailing line endings, coerced to string.\n * @return {string}\n *   Value without trailing line endings.\n */\nexport function trimTrailingLines(value) {\n  const input = String(value)\n  let end = input.length\n\n  while (end > 0) {\n    const code = input.codePointAt(end - 1)\n    if (code !== undefined && (code === 10 || code === 13)) {\n      end--\n    } else {\n      break\n    }\n  }\n\n  return input.slice(0, end)\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Code} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\nimport {trimTrailingLines} from 'trim-trailing-lines'\n\nconst prefix = 'language-'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Code}\n *   mdast node.\n */\nexport function code(state, node) {\n  const children = node.children\n  let index = -1\n  /** @type {Array<number | string> | undefined} */\n  let classList\n  /** @type {string | undefined} */\n  let lang\n\n  if (node.tagName === 'pre') {\n    while (++index < children.length) {\n      const child = children[index]\n\n      if (\n        child.type === 'element' &&\n        child.tagName === 'code' &&\n        child.properties &&\n        child.properties.className &&\n        Array.isArray(child.properties.className)\n      ) {\n        classList = child.properties.className\n        break\n      }\n    }\n  }\n\n  if (classList) {\n    index = -1\n\n    while (++index < classList.length) {\n      if (String(classList[index]).slice(0, prefix.length) === prefix) {\n        lang = String(classList[index]).slice(prefix.length)\n        break\n      }\n    }\n  }\n\n  /** @type {Code} */\n  const result = {\n    type: 'code',\n    lang: lang || null,\n    meta: null,\n    value: trimTrailingLines(toText(node))\n  }\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Comment} from 'hast'\n * @import {Html} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Comment>} node\n *   hast element to transform.\n * @returns {Html}\n *   mdast node.\n */\nexport function comment(state, node) {\n  /** @type {Html} */\n  const result = {\n    type: 'html',\n    value: '<!--' + node.value + '-->'\n  }\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Delete, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Delete}\n *   mdast node.\n */\nexport function del(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n  /** @type {Delete} */\n  const result = {type: 'delete', children}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {ListContent} from 'mdast'\n */\n\n/**\n * Infer whether list items are spread.\n *\n * @param {Readonly<Array<Readonly<ListContent>>>} children\n *   List items.\n * @returns {boolean}\n *   Whether one or more list items are spread.\n */\nexport function listItemsSpread(children) {\n  let index = -1\n\n  if (children.length > 1) {\n    while (++index < children.length) {\n      if (children[index].spread) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {ElementContent, Element} from 'hast'\n * @import {BlockContent, DefinitionContent, ListContent, ListItem, List} from 'mdast'\n */\n\n/**\n * @typedef Group\n *   Title/definition group.\n * @property {Array<Element>} titles\n *   One or more titles.\n * @property {Array<ElementContent>} definitions\n *   One or more definitions.\n */\n\nimport {listItemsSpread} from '../util/list-items-spread.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List | undefined}\n *   mdast node.\n */\nexport function dl(state, node) {\n  /** @type {Array<ElementContent>} */\n  const clean = []\n  /** @type {Array<Group>} */\n  const groups = []\n  let index = -1\n\n  // Unwrap `<div>`s\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'div') {\n      clean.push(...child.children)\n    } else {\n      clean.push(child)\n    }\n  }\n\n  /** @type {Group} */\n  let group = {definitions: [], titles: []}\n  index = -1\n\n  // Group titles and definitions.\n  while (++index < clean.length) {\n    const child = clean[index]\n\n    if (child.type === 'element' && child.tagName === 'dt') {\n      const previous = clean[index - 1]\n\n      if (\n        previous &&\n        previous.type === 'element' &&\n        previous.tagName === 'dd'\n      ) {\n        groups.push(group)\n        group = {definitions: [], titles: []}\n      }\n\n      group.titles.push(child)\n    } else {\n      group.definitions.push(child)\n    }\n  }\n\n  groups.push(group)\n\n  // Create items.\n  index = -1\n  /** @type {Array<ListContent>} */\n  const content = []\n\n  while (++index < groups.length) {\n    const result = [\n      ...handle(state, groups[index].titles),\n      ...handle(state, groups[index].definitions)\n    ]\n\n    if (result.length > 0) {\n      content.push({\n        type: 'listItem',\n        spread: result.length > 1,\n        checked: null,\n        children: result\n      })\n    }\n  }\n\n  // Create a list if there are items.\n  if (content.length > 0) {\n    /** @type {List} */\n    const result = {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: listItemsSpread(content),\n      children: content\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Array<ElementContent>} children\n *   hast element children to transform.\n * @returns {Array<BlockContent | DefinitionContent>}\n *   mdast nodes.\n */\nfunction handle(state, children) {\n  const nodes = state.all({type: 'root', children})\n  const listItems = state.toSpecificContent(nodes, create)\n\n  if (listItems.length === 0) {\n    return []\n  }\n\n  if (listItems.length === 1) {\n    return listItems[0].children\n  }\n\n  return [\n    {\n      type: 'list',\n      ordered: false,\n      start: null,\n      spread: listItemsSpread(listItems),\n      children: listItems\n    }\n  ]\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Emphasis, PhrasingContent} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Emphasis}\n *   mdast node.\n */\nexport function em(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Emphasis} */\n  const result = {type: 'emphasis', children}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {Nodes} from 'mdast'\n */\n\n/**\n * Drop trailing initial and final `br`s.\n *\n * @template {Nodes} Node\n *   Node type.\n * @param {Array<Node>} nodes\n *   List of nodes.\n * @returns {Array<Node>}\n *   List of nodes w/o `break`s.\n */\nexport function dropSurroundingBreaks(nodes) {\n  let start = 0\n  let end = nodes.length\n\n  while (start < end && nodes[start].type === 'break') start++\n  while (end > start && nodes[end - 1].type === 'break') end--\n\n  return start === 0 && end === nodes.length ? nodes : nodes.slice(start, end)\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Heading, PhrasingContent} from 'mdast'\n */\n\nimport {dropSurroundingBreaks} from '../util/drop-surrounding-breaks.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Heading}\n *   mdast node.\n */\nexport function heading(state, node) {\n  const depth = /** @type {Heading['depth']} */ (\n    /* c8 ignore next */\n    Number(node.tagName.charAt(1)) || 1\n  )\n  const children = dropSurroundingBreaks(\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  /** @type {Heading} */\n  const result = {type: 'heading', depth, children}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ThematicBreak} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ThematicBreak}\n *   mdast node.\n */\nexport function hr(state, node) {\n  /** @type {ThematicBreak} */\n  const result = {type: 'thematicBreak'}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Link} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Link | undefined}\n *   mdast node.\n */\nexport function iframe(state, node) {\n  const properties = node.properties || {}\n  const source = String(properties.src || '')\n  const title = String(properties.title || '')\n\n  // Only create a link if there is a title.\n  // We can’t use the content of the frame because conforming HTML parsers treat\n  // it as text, whereas legacy parsers treat it as HTML, so it will likely\n  // contain tags that will show up in text.\n  if (source && title) {\n    /** @type {Link} */\n    const result = {\n      type: 'link',\n      title: null,\n      url: state.resolve(source),\n      children: [{type: 'text', value: title}]\n    }\n    state.patch(node, result)\n    return result\n  }\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Image}\n *   mdast node.\n */\nexport function img(state, node) {\n  const properties = node.properties || {}\n\n  /** @type {Image} */\n  const result = {\n    type: 'image',\n    url: state.resolve(String(properties.src || '') || null),\n    title: properties.title ? String(properties.title) : null,\n    alt: properties.alt ? String(properties.alt) : ''\n  }\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {InlineCode} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {InlineCode}\n *   mdast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {InlineCode} */\n  const result = {type: 'inlineCode', value: toText(node)}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {Element, Properties} from 'hast'\n */\n\n/**\n * @typedef {[string, Value]} Option\n *   Option, where the item at `0` is the label, the item at `1` the value.\n *\n * @typedef {Array<Option>} Options\n *   List of options.\n *\n * @typedef {string | undefined} Value\n *   `value` field of option.\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {Readonly<Element>} node\n *   hast element to inspect.\n * @param {Properties | undefined} [explicitProperties]\n *   Properties to use, normally taken from `node`, but can be changed.\n * @returns {Options}\n *   Options.\n */\nexport function findSelectedOptions(node, explicitProperties) {\n  /** @type {Array<Element>} */\n  const selectedOptions = []\n  /** @type {Options} */\n  const values = []\n  const properties = explicitProperties || node.properties || {}\n  const options = findOptions(node)\n  const size =\n    Math.min(Number.parseInt(String(properties.size), 10), 0) ||\n    (properties.multiple ? 4 : 1)\n  let index = -1\n\n  while (++index < options.length) {\n    const option = options[index]\n\n    if (option && option.properties && option.properties.selected) {\n      selectedOptions.push(option)\n    }\n  }\n\n  const list = selectedOptions.length > 0 ? selectedOptions : options\n  const max = Math.min(list.length, size)\n  index = -1\n\n  while (++index < max) {\n    const option = list[index]\n    const properties = option.properties || {}\n    const content = toText(option)\n    const label = content || String(properties.label || '')\n    const value = String(properties.value || '') || content\n    values.push([value, label === value ? undefined : label])\n  }\n\n  return values\n}\n\n/**\n * @param {Element} node\n *   Parent to find in.\n * @returns {Array<Element>}\n *   Option elements.\n */\nfunction findOptions(node) {\n  /** @type {Array<Element>} */\n  const results = []\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if ('children' in child && Array.isArray(child.children)) {\n      results.push(...findOptions(child))\n    }\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'option' &&\n      (!child.properties || !child.properties.disabled)\n    ) {\n      results.push(child)\n    }\n  }\n\n  return results\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, Text} from 'mdast'\n * @import {Options} from '../util/find-selected-options.js'\n */\n\nimport {findSelectedOptions} from '../util/find-selected-options.js'\n\nconst defaultChecked = '[x]'\nconst defaultUnchecked = '[ ]'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<Link | Text> | Image | Text | undefined}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nexport function input(state, node) {\n  const properties = node.properties || {}\n  const value = String(properties.value || properties.placeholder || '')\n\n  if (\n    properties.disabled ||\n    properties.type === 'hidden' ||\n    properties.type === 'file'\n  ) {\n    return\n  }\n\n  if (properties.type === 'checkbox' || properties.type === 'radio') {\n    /** @type {Text} */\n    const result = {\n      type: 'text',\n      value: properties.checked\n        ? state.options.checked || defaultChecked\n        : state.options.unchecked || defaultUnchecked\n    }\n    state.patch(node, result)\n    return result\n  }\n\n  if (properties.type === 'image') {\n    const alt = properties.alt || value\n\n    if (alt) {\n      /** @type {Image} */\n      const result = {\n        type: 'image',\n        url: state.resolve(String(properties.src || '') || null),\n        title: String(properties.title || '') || null,\n        alt: String(alt)\n      }\n      state.patch(node, result)\n      return result\n    }\n\n    return\n  }\n\n  /** @type {Options} */\n  let values = []\n\n  if (value) {\n    values = [[value, undefined]]\n  } else if (\n    // `list` is not supported on these types:\n    properties.type !== 'button' &&\n    properties.type !== 'file' &&\n    properties.type !== 'password' &&\n    properties.type !== 'reset' &&\n    properties.type !== 'submit' &&\n    properties.list\n  ) {\n    const list = String(properties.list)\n    const datalist = state.elementById.get(list)\n\n    if (datalist && datalist.tagName === 'datalist') {\n      values = findSelectedOptions(datalist, properties)\n    }\n  }\n\n  if (values.length === 0) {\n    return\n  }\n\n  // Hide password value.\n  if (properties.type === 'password') {\n    // Passwords don’t support `list`.\n    values[0] = ['•'.repeat(values[0][0].length), undefined]\n  }\n\n  if (properties.type === 'email' || properties.type === 'url') {\n    /** @type {Array<Link | Text>} */\n    const results = []\n    let index = -1\n\n    while (++index < values.length) {\n      const value = state.resolve(values[index][0])\n      /** @type {Link} */\n      const result = {\n        type: 'link',\n        title: null,\n        url: properties.type === 'email' ? 'mailto:' + value : value,\n        children: [{type: 'text', value: values[index][1] || value}]\n      }\n\n      results.push(result)\n\n      if (index !== values.length - 1) {\n        results.push({type: 'text', value: ', '})\n      }\n    }\n\n    return results\n  }\n\n  /** @type {Array<string>} */\n  const texts = []\n  let index = -1\n\n  while (++index < values.length) {\n    texts.push(\n      values[index][1]\n        ? values[index][1] + ' (' + values[index][0] + ')'\n        : values[index][0]\n    )\n  }\n\n  /** @type {Text} */\n  const result = {type: 'text', value: texts.join(', ')}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem} from 'mdast'\n */\n\n/**\n * @typedef ExtractResult\n *   Result of extracting a leading checkbox.\n * @property {Element | undefined} checkbox\n *   The checkbox that was removed, if any.\n * @property {Element} rest\n *   If there was a leading checkbox, a deep clone of the node w/o the leading\n *   checkbox; otherwise a reference to the given, untouched, node.\n */\n\nimport {phrasing} from 'hast-util-phrasing'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {ListItem}\n *   mdast node.\n */\nexport function li(state, node) {\n  // If the list item starts with a checkbox, remove the checkbox and mark the\n  // list item as a GFM task list item.\n  const {rest, checkbox} = extractLeadingCheckbox(node)\n  const checked = checkbox ? Boolean(checkbox.properties.checked) : null\n  const spread = spreadout(rest)\n  const children = state.toFlow(state.all(rest))\n\n  /** @type {ListItem} */\n  const result = {type: 'listItem', spread, checked, children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Check if an element should spread out.\n *\n * The reason to spread out a markdown list item is primarily whether writing\n * the equivalent in markdown, would yield a spread out item.\n *\n * A spread out item results in `<p>` and `</p>` tags.\n * Otherwise, the phrasing would be output directly.\n * We can check for that: if there’s a `<p>` element, spread it out.\n *\n * But what if there are no paragraphs?\n * In that case, we can also assume that if two “block” things were written in\n * an item, that it is spread out, because blocks are typically joined by blank\n * lines, which also means a spread item.\n *\n * Lastly, because in HTML things can be wrapped in a `<div>` or similar, we\n * delve into non-phrasing elements here to figure out if they themselves\n * contain paragraphs or 2 or more flow non-phrasing elements.\n *\n * @param {Readonly<Element>} node\n * @returns {boolean}\n */\nfunction spreadout(node) {\n  let index = -1\n  let seenFlow = false\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element') {\n      if (phrasing(child)) continue\n\n      if (child.tagName === 'p' || seenFlow || spreadout(child)) {\n        return true\n      }\n\n      seenFlow = true\n    }\n  }\n\n  return false\n}\n\n/**\n * Extract a leading checkbox from a list item.\n *\n * If there was a leading checkbox, makes a deep clone of the node w/o the\n * leading checkbox; otherwise a reference to the given, untouched, node is\n * given back.\n *\n * So for example:\n *\n * ```html\n * <li><input type=\"checkbox\">Text</li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li>Text</li>\n * ```\n *\n * ```html\n * <li><p><input type=\"checkbox\">Text</p></li>\n * ```\n *\n * …becomes:\n *\n * ```html\n * <li><p>Text</p></li>\n * ```\n *\n * @param {Readonly<Element>} node\n * @returns {ExtractResult}\n */\nfunction extractLeadingCheckbox(node) {\n  const head = node.children[0]\n\n  if (\n    head &&\n    head.type === 'element' &&\n    head.tagName === 'input' &&\n    head.properties &&\n    (head.properties.type === 'checkbox' || head.properties.type === 'radio')\n  ) {\n    const rest = {...node, children: node.children.slice(1)}\n    return {checkbox: head, rest}\n  }\n\n  // The checkbox may be nested in another element.\n  // If the first element has children, look for a leading checkbox inside it.\n  //\n  // This only handles nesting in `<p>` elements, which is most common.\n  // It’s possible a leading checkbox might be nested in other types of flow or\n  // phrasing elements (and *deeply* nested, which is not possible with `<p>`).\n  // Limiting things to `<p>` elements keeps this simpler for now.\n  if (head && head.type === 'element' && head.tagName === 'p') {\n    const {checkbox, rest: restHead} = extractLeadingCheckbox(head)\n\n    if (checkbox) {\n      const rest = {...node, children: [restHead, ...node.children.slice(1)]}\n      return {checkbox, rest}\n    }\n  }\n\n  return {checkbox: undefined, rest: node}\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {ListItem, List} from 'mdast'\n */\n\nimport {listItemsSpread} from '../util/list-items-spread.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {List}\n *   mdast node.\n */\nexport function list(state, node) {\n  const ordered = node.tagName === 'ol'\n  const children = state.toSpecificContent(state.all(node), create)\n  /** @type {number | null} */\n  let start = null\n\n  if (ordered) {\n    start =\n      node.properties && node.properties.start\n        ? Number.parseInt(String(node.properties.start), 10)\n        : 1\n  }\n\n  /** @type {List} */\n  const result = {\n    type: 'list',\n    ordered,\n    start,\n    spread: listItemsSpread(children),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {ListItem}\n */\nfunction create() {\n  return {type: 'listItem', spread: false, checked: null, children: []}\n}\n", "/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n", "/**\n * @import {} from 'mdast-util-to-hast'\n * @import {\n *   BlockContent,\n *   Delete,\n *   Link,\n *   Nodes,\n *   Paragraph,\n *   Parents,\n *   PhrasingContent,\n *   RootContent\n * } from 'mdast'\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {phrasing as hastPhrasing} from 'hast-util-phrasing'\nimport {whitespace} from 'hast-util-whitespace'\nimport {phrasing as mdastPhrasing} from 'mdast-util-phrasing'\nimport {dropSurroundingBreaks} from './drop-surrounding-breaks.js'\n\n/**\n * Check if there are phrasing mdast nodes.\n *\n * This is needed if a fragment is given, which could just be a sentence, and\n * doesn’t need a wrapper paragraph.\n *\n * @param {Array<Nodes>} nodes\n * @returns {boolean}\n */\nexport function wrapNeeded(nodes) {\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (!phrasing(node) || ('children' in node && wrapNeeded(node.children))) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Wrap runs of phrasing content into paragraphs, leaving the non-phrasing\n * content as-is.\n *\n * @param {Array<RootContent>} nodes\n *   Content.\n * @returns {Array<BlockContent>}\n *   Content where phrasing is wrapped in paragraphs.\n */\nexport function wrap(nodes) {\n  return runs(nodes, onphrasing, function (d) {\n    return d\n  })\n\n  /**\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<Paragraph>}\n   */\n  function onphrasing(nodes) {\n    return nodes.every(function (d) {\n      return d.type === 'text' ? whitespace(d.value) : false\n    })\n      ? []\n      : [{type: 'paragraph', children: dropSurroundingBreaks(nodes)}]\n  }\n}\n\n/**\n * @param {Delete | Link} node\n * @returns {Array<BlockContent>}\n */\nfunction split(node) {\n  return runs(node.children, onphrasing, onnonphrasing)\n\n  /**\n   * Use `parent`, put the phrasing run inside it.\n   *\n   * @param {Array<PhrasingContent>} nodes\n   * @returns {Array<BlockContent>}\n   */\n  function onphrasing(nodes) {\n    const newParent = cloneWithoutChildren(node)\n    newParent.children = nodes\n    // @ts-expect-error Assume fine.\n    return [newParent]\n  }\n\n  /**\n   * Use `child`, add `parent` as its first child, put the original children\n   * into `parent`.\n   * If `child` is not a parent, `parent` will not be added.\n   *\n   * @param {BlockContent} child\n   * @returns {BlockContent}\n   */\n  function onnonphrasing(child) {\n    if ('children' in child && 'children' in node) {\n      const newParent = cloneWithoutChildren(node)\n      const newChild = cloneWithoutChildren(child)\n      // @ts-expect-error Assume fine.\n      newParent.children = child.children\n      // @ts-expect-error Assume fine.\n      newChild.children.push(newParent)\n      return newChild\n    }\n\n    return {...child}\n  }\n}\n\n/**\n * Wrap all runs of mdast phrasing content in `paragraph` nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of input nodes.\n * @param {(nodes: Array<PhrasingContent>) => Array<BlockContent>} onphrasing\n *   Turn phrasing content into block content.\n * @param {(node: BlockContent) => BlockContent} onnonphrasing\n *   Map block content (defaults to keeping them as-is).\n * @returns {Array<BlockContent>}\n */\nfunction runs(nodes, onphrasing, onnonphrasing) {\n  const flattened = flatten(nodes)\n  /** @type {Array<BlockContent>} */\n  const result = []\n  /** @type {Array<PhrasingContent>} */\n  let queue = []\n  let index = -1\n\n  while (++index < flattened.length) {\n    const node = flattened[index]\n\n    if (phrasing(node)) {\n      queue.push(node)\n    } else {\n      if (queue.length > 0) {\n        result.push(...onphrasing(queue))\n        queue = []\n      }\n\n      // @ts-expect-error Assume non-phrasing.\n      result.push(onnonphrasing(node))\n    }\n  }\n\n  if (queue.length > 0) {\n    result.push(...onphrasing(queue))\n    queue = []\n  }\n\n  return result\n}\n\n/**\n * Flatten a list of nodes.\n *\n * @param {Array<RootContent>} nodes\n *   List of nodes, will unravel `delete` and `link`.\n * @returns {Array<RootContent>}\n *   Unraveled nodes.\n */\nfunction flatten(nodes) {\n  /** @type {Array<RootContent>} */\n  const flattened = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    // Straddling: some elements are *weird*.\n    // Namely: `map`, `ins`, `del`, and `a`, as they are hybrid elements.\n    // See: <https://html.spec.whatwg.org/#paragraphs>.\n    // Paragraphs are the weirdest of them all.\n    // See the straddling fixture for more info!\n    // `ins` is ignored in mdast, so we don’t need to worry about that.\n    // `map` maps to its content, so we don’t need to worry about that either.\n    // `del` maps to `delete` and `a` to `link`, so we do handle those.\n    // What we’ll do is split `node` over each of its children.\n    if (\n      (node.type === 'delete' || node.type === 'link') &&\n      wrapNeeded(node.children)\n    ) {\n      flattened.push(...split(node))\n    } else {\n      flattened.push(node)\n    }\n  }\n\n  return flattened\n}\n\n/**\n * Check if an mdast node is phrasing.\n *\n * Also supports checking embedded hast fields.\n *\n * @param {Nodes} node\n *   mdast node to check.\n * @returns {node is PhrasingContent}\n *   Whether `node` is phrasing content (includes nodes with `hName` fields\n *   set to phrasing hast element names).\n */\nfunction phrasing(node) {\n  const tagName = node.data && node.data.hName\n  return tagName\n    ? hastPhrasing({type: 'element', tagName, properties: {}, children: []})\n    : mdastPhrasing(node)\n}\n\n/**\n * @template {Parents} ParentType\n *   Parent type.\n * @param {ParentType} node\n *   Node to clone.\n * @returns {ParentType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return structuredClone({...node, children: []})\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Image, Link, PhrasingContent, RootContent as MdastRootContent, Root} from 'mdast'\n */\n\nimport {toString} from 'mdast-util-to-string'\nimport {EXIT, visit} from 'unist-util-visit'\nimport {wrapNeeded} from '../util/wrap.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent> | Link}\n *   mdast node.\n */\nexport function media(state, node) {\n  const properties = node.properties || {}\n  const poster = node.tagName === 'video' ? String(properties.poster || '') : ''\n  let source = String(properties.src || '')\n  let index = -1\n  let linkInFallbackContent = false\n  let nodes = state.all(node)\n\n  /** @type {Root} */\n  const fragment = {type: 'root', children: nodes}\n\n  visit(fragment, function (node) {\n    if (node.type === 'link') {\n      linkInFallbackContent = true\n      return EXIT\n    }\n  })\n\n  // If the content links to something, or if it’s not phrasing…\n  if (linkInFallbackContent || wrapNeeded(nodes)) {\n    return nodes\n  }\n\n  // Find the source.\n  while (!source && ++index < node.children.length) {\n    const child = node.children[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'source' &&\n      child.properties\n    ) {\n      source = String(child.properties.src || '')\n    }\n  }\n\n  // If there’s a poster defined on the video, create an image.\n  if (poster) {\n    /** @type {Image} */\n    const image = {\n      type: 'image',\n      title: null,\n      url: state.resolve(poster),\n      alt: toString(nodes)\n    }\n    state.patch(node, image)\n    nodes = [image]\n  }\n\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (nodes)\n\n  // Link to the media resource.\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    title: properties.title ? String(properties.title) : null,\n    url: state.resolve(source),\n    children\n  }\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Paragraph, PhrasingContent} from 'mdast'\n */\n\nimport {dropSurroundingBreaks} from '../util/drop-surrounding-breaks.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Paragraph | undefined}\n *   mdast node.\n */\nexport function p(state, node) {\n  const children = dropSurroundingBreaks(\n    // Allow potentially “invalid” nodes, they might be unknown.\n    // We also support straddling later.\n    /** @type {Array<PhrasingContent>} */ (state.all(node))\n  )\n\n  if (children.length > 0) {\n    /** @type {Paragraph} */\n    const result = {type: 'paragraph', children}\n    state.patch(node, result)\n    return result\n  }\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RootContent as MdastRootContent} from 'mdast'\n */\n\nconst defaultQuotes = ['\"']\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Array<MdastRootContent>}\n *   mdast nodes.\n */\nexport function q(state, node) {\n  const quotes = state.options.quotes || defaultQuotes\n\n  state.qNesting++\n  const contents = state.all(node)\n  state.qNesting--\n\n  const quote = quotes[state.qNesting % quotes.length]\n  const head = contents[0]\n  const tail = contents[contents.length - 1]\n  const open = quote.charAt(0)\n  const close = quote.length > 1 ? quote.charAt(1) : quote\n\n  if (head && head.type === 'text') {\n    head.value = open + head.value\n  } else {\n    contents.unshift({type: 'text', value: open})\n  }\n\n  if (tail && tail.type === 'text') {\n    tail.value += close\n  } else {\n    contents.push({type: 'text', value: close})\n  }\n\n  return contents\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n */\n\nimport {wrap, wrapNeeded} from '../util/wrap.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastRoot>} node\n *   hast root to transform.\n * @returns {MdastRoot}\n *   mdast node.\n */\nexport function root(state, node) {\n  let children = state.all(node)\n\n  if (state.options.document || wrapNeeded(children)) {\n    children = wrap(children)\n  }\n\n  /** @type {MdastRoot} */\n  const result = {type: 'root', children}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\nimport {findSelectedOptions} from '../util/find-selected-options.js'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text | undefined}\n *   mdast node.\n */\nexport function select(state, node) {\n  const values = findSelectedOptions(node)\n  let index = -1\n  /** @type {Array<string>} */\n  const results = []\n\n  while (++index < values.length) {\n    const value = values[index]\n    results.push(value[1] ? value[1] + ' (' + value[0] + ')' : value[0])\n  }\n\n  if (results.length > 0) {\n    /** @type {Text} */\n    const result = {type: 'text', value: results.join(', ')}\n    state.patch(node, result)\n    return result\n  }\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, Strong} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Strong}\n *   mdast node.\n */\nexport function strong(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {Strong} */\n  const result = {type: 'strong', children}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {PhrasingContent, TableCell} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableCell}\n *   mdast node.\n */\nexport function tableCell(state, node) {\n  // Allow potentially “invalid” nodes, they might be unknown.\n  // We also support straddling later.\n  const children = /** @type {Array<PhrasingContent>} */ (state.all(node))\n\n  /** @type {TableCell} */\n  const result = {type: 'tableCell', children}\n  state.patch(node, result)\n\n  if (node.properties) {\n    const rowSpan = node.properties.rowSpan\n    const colSpan = node.properties.colSpan\n\n    if (rowSpan || colSpan) {\n      const data = /** @type {Record<string, unknown>} */ (\n        result.data || (result.data = {})\n      )\n      if (rowSpan) data.hastUtilToMdastTemporaryRowSpan = rowSpan\n      if (colSpan) data.hastUtilToMdastTemporaryColSpan = colSpan\n    }\n  }\n\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {RowContent, TableRow} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {TableRow}\n *   mdast node.\n */\nexport function tableRow(state, node) {\n  const children = state.toSpecificContent(state.all(node), create)\n\n  /** @type {TableRow} */\n  const result = {type: 'tableRow', children}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * @returns {RowContent}\n */\nfunction create() {\n  return {type: 'tableCell', children: []}\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {AlignType, RowContent, TableContent, Table, Text} from 'mdast'\n */\n\n/**\n * @typedef Info\n *   Inferred info on a table.\n * @property {Array<AlignType>} align\n *   Alignment.\n * @property {boolean} headless\n *   Whether a `thead` is missing.\n */\n\nimport {toText} from 'hast-util-to-text'\nimport {SKIP, visit} from 'unist-util-visit'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Table | Text}\n *   mdast node.\n */\n// eslint-disable-next-line complexity\nexport function table(state, node) {\n  // Ignore nested tables.\n  if (state.inTable) {\n    /** @type {Text} */\n    const result = {type: 'text', value: toText(node)}\n    state.patch(node, result)\n    return result\n  }\n\n  state.inTable = true\n\n  const {align, headless} = inspect(node)\n  const rows = state.toSpecificContent(state.all(node), createRow)\n\n  // Add an empty header row.\n  if (headless) {\n    rows.unshift(createRow())\n  }\n\n  let rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const row = rows[rowIndex]\n    const cells = state.toSpecificContent(row.children, createCell)\n    row.children = cells\n  }\n\n  let columns = 1\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = -1\n\n    while (++cellIndex < cells.length) {\n      const cell = cells[cellIndex]\n\n      if (cell.data) {\n        const data = /** @type {Record<string, unknown>} */ (cell.data)\n        const colSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryColSpan), 10) || 1\n        const rowSpan =\n          Number.parseInt(String(data.hastUtilToMdastTemporaryRowSpan), 10) || 1\n\n        if (colSpan > 1 || rowSpan > 1) {\n          let otherRowIndex = rowIndex - 1\n\n          while (++otherRowIndex < rowIndex + rowSpan) {\n            let colIndex = cellIndex - 1\n\n            while (++colIndex < cellIndex + colSpan) {\n              if (!rows[otherRowIndex]) {\n                // Don’t add rows that don’t exist.\n                // Browsers don’t render them either.\n                break\n              }\n\n              /** @type {Array<RowContent>} */\n              const newCells = []\n\n              if (otherRowIndex !== rowIndex || colIndex !== cellIndex) {\n                newCells.push({type: 'tableCell', children: []})\n              }\n\n              rows[otherRowIndex].children.splice(colIndex, 0, ...newCells)\n            }\n          }\n        }\n\n        // Clean the data fields.\n        if ('hastUtilToMdastTemporaryColSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryColSpan\n        if ('hastUtilToMdastTemporaryRowSpan' in cell.data)\n          delete cell.data.hastUtilToMdastTemporaryRowSpan\n        if (Object.keys(cell.data).length === 0) delete cell.data\n      }\n    }\n\n    if (cells.length > columns) columns = cells.length\n  }\n\n  // Add extra empty cells.\n  rowIndex = -1\n\n  while (++rowIndex < rows.length) {\n    const cells = rows[rowIndex].children\n    let cellIndex = cells.length - 1\n    while (++cellIndex < columns) {\n      cells.push({type: 'tableCell', children: []})\n    }\n  }\n\n  let alignIndex = align.length - 1\n  while (++alignIndex < columns) {\n    align.push(null)\n  }\n\n  state.inTable = false\n\n  /** @type {Table} */\n  const result = {type: 'table', align, children: rows}\n  state.patch(node, result)\n  return result\n}\n\n/**\n * Infer whether the HTML table has a head and how it aligns.\n *\n * @param {Readonly<Element>} node\n *   Table element to check.\n * @returns {Info}\n *   Info.\n */\nfunction inspect(node) {\n  /** @type {Info} */\n  const info = {align: [null], headless: true}\n  let rowIndex = 0\n  let cellIndex = 0\n\n  visit(node, function (child) {\n    if (child.type === 'element') {\n      // Don’t enter nested tables.\n      if (child.tagName === 'table' && node !== child) {\n        return SKIP\n      }\n\n      if (\n        (child.tagName === 'th' || child.tagName === 'td') &&\n        child.properties\n      ) {\n        if (!info.align[cellIndex]) {\n          const value = String(child.properties.align || '') || null\n\n          if (\n            value === 'center' ||\n            value === 'left' ||\n            value === 'right' ||\n            value === null\n          ) {\n            info.align[cellIndex] = value\n          }\n        }\n\n        // If there is a `th` in the first row, assume there is a header row.\n        if (info.headless && rowIndex < 2 && child.tagName === 'th') {\n          info.headless = false\n        }\n\n        cellIndex++\n      }\n      // If there is a `thead`, assume there is a header row.\n      else if (child.tagName === 'thead') {\n        info.headless = false\n      } else if (child.tagName === 'tr') {\n        rowIndex++\n        cellIndex = 0\n      }\n    }\n  })\n\n  return info\n}\n\n/**\n * @returns {RowContent}\n */\nfunction createCell() {\n  return {type: 'tableCell', children: []}\n}\n\n/**\n * @returns {TableContent}\n */\nfunction createRow() {\n  return {type: 'tableRow', children: []}\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Text as HastText} from 'hast'\n * @import {Text as MdastText} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<HastText>} node\n *   hast element to transform.\n * @returns {MdastText}\n *   mdast node.\n */\nexport function text(state, node) {\n  /** @type {MdastText} */\n  const result = {type: 'text', value: node.value}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\nimport {toText} from 'hast-util-to-text'\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nexport function textarea(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: toText(node)}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Element} from 'hast'\n * @import {Text} from 'mdast'\n */\n\n/**\n * @param {State} state\n *   State.\n * @param {Readonly<Element>} node\n *   hast element to transform.\n * @returns {Text}\n *   mdast node.\n */\nexport function wbr(state, node) {\n  /** @type {Text} */\n  const result = {type: 'text', value: '\\u200B'}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @import {State} from 'hast-util-to-mdast'\n * @import {Parents} from 'hast'\n */\n\nimport {a} from './a.js'\nimport {base} from './base.js'\nimport {blockquote} from './blockquote.js'\nimport {br} from './br.js'\nimport {code} from './code.js'\nimport {comment} from './comment.js'\nimport {del} from './del.js'\nimport {dl} from './dl.js'\nimport {em} from './em.js'\nimport {heading} from './heading.js'\nimport {hr} from './hr.js'\nimport {iframe} from './iframe.js'\nimport {img} from './img.js'\nimport {inlineCode} from './inline-code.js'\nimport {input} from './input.js'\nimport {li} from './li.js'\nimport {list} from './list.js'\nimport {media} from './media.js'\nimport {p} from './p.js'\nimport {q} from './q.js'\nimport {root} from './root.js'\nimport {select} from './select.js'\nimport {strong} from './strong.js'\nimport {tableCell} from './table-cell.js'\nimport {tableRow} from './table-row.js'\nimport {table} from './table.js'\nimport {text} from './text.js'\nimport {textarea} from './textarea.js'\nimport {wbr} from './wbr.js'\n\n/**\n * Default handlers for nodes.\n *\n * Each key is a node type, each value is a `NodeHandler`.\n */\nexport const nodeHandlers = {\n  comment,\n  doctype: ignore,\n  root,\n  text\n}\n\n/**\n * Default handlers for elements.\n *\n * Each key is an element name, each value is a `Handler`.\n */\nexport const handlers = {\n  // Ignore:\n  applet: ignore,\n  area: ignore,\n  basefont: ignore,\n  bgsound: ignore,\n  caption: ignore,\n  col: ignore,\n  colgroup: ignore,\n  command: ignore,\n  content: ignore,\n  datalist: ignore,\n  dialog: ignore,\n  element: ignore,\n  embed: ignore,\n  frame: ignore,\n  frameset: ignore,\n  isindex: ignore,\n  keygen: ignore,\n  link: ignore,\n  math: ignore,\n  menu: ignore,\n  menuitem: ignore,\n  meta: ignore,\n  nextid: ignore,\n  noembed: ignore,\n  noframes: ignore,\n  optgroup: ignore,\n  option: ignore,\n  param: ignore,\n  script: ignore,\n  shadow: ignore,\n  source: ignore,\n  spacer: ignore,\n  style: ignore,\n  svg: ignore,\n  template: ignore,\n  title: ignore,\n  track: ignore,\n\n  // Use children:\n  abbr: all,\n  acronym: all,\n  bdi: all,\n  bdo: all,\n  big: all,\n  blink: all,\n  button: all,\n  canvas: all,\n  cite: all,\n  data: all,\n  details: all,\n  dfn: all,\n  font: all,\n  ins: all,\n  label: all,\n  map: all,\n  marquee: all,\n  meter: all,\n  nobr: all,\n  noscript: all,\n  object: all,\n  output: all,\n  progress: all,\n  rb: all,\n  rbc: all,\n  rp: all,\n  rt: all,\n  rtc: all,\n  ruby: all,\n  slot: all,\n  small: all,\n  span: all,\n  sup: all,\n  sub: all,\n  tbody: all,\n  tfoot: all,\n  thead: all,\n  time: all,\n\n  // Use children as flow.\n  address: flow,\n  article: flow,\n  aside: flow,\n  body: flow,\n  center: flow,\n  div: flow,\n  fieldset: flow,\n  figcaption: flow,\n  figure: flow,\n  form: flow,\n  footer: flow,\n  header: flow,\n  hgroup: flow,\n  html: flow,\n  legend: flow,\n  main: flow,\n  multicol: flow,\n  nav: flow,\n  picture: flow,\n  section: flow,\n\n  // Handle.\n  a,\n  audio: media,\n  b: strong,\n  base,\n  blockquote,\n  br,\n  code: inlineCode,\n  dir: list,\n  dl,\n  dt: li,\n  dd: li,\n  del,\n  em,\n  h1: heading,\n  h2: heading,\n  h3: heading,\n  h4: heading,\n  h5: heading,\n  h6: heading,\n  hr,\n  i: em,\n  iframe,\n  img,\n  image: img,\n  input,\n  kbd: inlineCode,\n  li,\n  listing: code,\n  mark: em,\n  ol: list,\n  p,\n  plaintext: code,\n  pre: code,\n  q,\n  s: del,\n  samp: inlineCode,\n  select,\n  strike: del,\n  strong,\n  summary: p,\n  table,\n  td: tableCell,\n  textarea,\n  th: tableCell,\n  tr: tableRow,\n  tt: inlineCode,\n  u: em,\n  ul: list,\n  var: inlineCode,\n  video: media,\n  wbr,\n  xmp: code\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction all(state, node) {\n  return state.all(node)\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {Parents} node\n *   Parent to transform.\n */\nfunction flow(state, node) {\n  return state.toFlow(state.all(node))\n}\n\n/**\n * @returns {undefined}\n */\nfunction ignore() {}\n", "/**\n * @import {Element, Nodes, Parents} from 'hast'\n * @import {\n *   BlockContent as MdastBlockContent,\n *   DefinitionContent as MdastDefinitionContent,\n *   Nodes as MdastNodes,\n *   Parents as MdastParents,\n *   RootContent as <PERSON><PERSON><PERSON>RootContent\n * } from 'mdast'\n */\n\n/**\n * @typedef {MdastBlockContent | MdastDefinitionContent} MdastFlowContent\n */\n\n/**\n * @callback All\n *   Transform the children of a hast parent to mdast.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n *\n * @callback Handle\n *   Handle a particular element.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Element} element\n *   Element to transform.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback NodeHandle\n *   Handle a particular node.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {any} node\n *   Node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined | void}\n *   mdast node or nodes.\n *\n *   Note: `void` is included until TS nicely infers `undefined`.\n *\n * @callback One\n *   Transform a hast node to mdast.\n * @param {Nodes} node\n *   Expected hast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n *\n * @typedef Options\n *   Configuration.\n * @property {string | null | undefined} [checked='[x]']\n *   Value to use for a checked checkbox or radio input (default: `'[x]'`)\n * @property {boolean | null | undefined} [document]\n *   Whether the given tree represents a complete document (optional).\n *\n *   Applies when the `tree` is a `root` node.\n *   When the tree represents a complete document, then things are wrapped in\n *   paragraphs when needed, and otherwise they’re left as-is.\n *   The default checks for whether there’s mixed content: some phrasing nodes\n *   *and* some non-phrasing nodes.\n * @property {Record<string, Handle | null | undefined> | null | undefined} [handlers]\n *   Object mapping tag names to functions handling the corresponding elements\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {boolean | null | undefined} [newlines=false]\n *   Keep line endings when collapsing whitespace (default: `false`).\n *\n *   The default collapses to a single space.\n * @property {Record<string, NodeHandle | null | undefined> | null | undefined} [nodeHandlers]\n *   Object mapping node types to functions handling the corresponding nodes\n *   (optional).\n *\n *   Merged into the defaults.\n * @property {Array<string> | null | undefined} [quotes=['\"']]\n *   List of quotes to use (default: `['\"']`).\n *\n *   Each value can be one or two characters.\n *   When two, the first character determines the opening quote and the second\n *   the closing quote at that level.\n *   When one, both the opening and closing quote are that character.\n *\n *   The order in which the preferred quotes appear determines which quotes to\n *   use at which level of nesting.\n *   So, to prefer `‘’` at the first level of nesting, and `“”` at the second,\n *   pass `['‘’', '“”']`.\n *   If `<q>`s are nested deeper than the given amount of quotes, the markers\n *   wrap around: a third level of nesting when using `['«»', '‹›']` should\n *   have double guillemets, a fourth single, a fifth double again, etc.\n * @property {string | null | undefined} [unchecked='[ ]']\n *   Value to use for an unchecked checkbox or radio input (default: `'[ ]'`).\n *\n * @callback Patch\n *   Copy a node’s positional info.\n * @param {Nodes} from\n *   hast node to copy from.\n * @param {MdastNodes} to\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n *\n * @callback Resolve\n *   Resolve a URL relative to a base.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {All} all\n *   Transform the children of a hast parent to mdast.\n * @property {boolean} baseFound\n *   Whether a `<base>` element was seen.\n * @property {Map<string, Element>} elementById\n *   Elements by their `id`.\n * @property {string | undefined} frozenBaseUrl\n *   `href` of `<base>`, if any.\n * @property {Record<string, Handle>} handlers\n *   Applied element handlers.\n * @property {boolean} inTable\n *   Whether we’re in a table.\n * @property {Record<string, NodeHandle>} nodeHandlers\n *   Applied node handlers.\n * @property {One} one\n *   Transform a hast node to mdast.\n * @property {Options} options\n *   User configuration.\n * @property {Patch} patch\n *   Copy a node’s positional info.\n * @property {number} qNesting\n *   Non-negative finite integer representing how deep we’re in `<q>`s.\n * @property {Resolve} resolve\n *   Resolve a URL relative to a base.\n * @property {ToFlow} toFlow\n *   Transform a list of mdast nodes to flow.\n * @property {<ChildType extends MdastNodes, ParentType extends MdastParents & {'children': Array<ChildType>}>(nodes: Array<MdastRootContent>, build: (() => ParentType)) => Array<ParentType>} toSpecificContent\n *   Turn arbitrary content into a list of a particular node type.\n *\n *   This is useful for example for lists, which must have list items as\n *   content.\n *   in this example, when non-items are found, they will be queued, and\n *   inserted into an adjacent item.\n *   When no actual items exist, one will be made with `build`.\n *\n * @callback ToFlow\n *   Transform a list of mdast nodes to flow.\n * @param {Array<MdastRootContent>} nodes\n *   mdast nodes.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\n\nimport {position} from 'unist-util-position'\nimport {handlers, nodeHandlers} from './handlers/index.js'\nimport {wrap} from './util/wrap.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a state.\n *\n * @param {Readonly<Options>} options\n *   User configuration.\n * @returns {State}\n *   State.\n */\nexport function createState(options) {\n  return {\n    all,\n    baseFound: false,\n    elementById: new Map(),\n    frozenBaseUrl: undefined,\n    handlers: {...handlers, ...options.handlers},\n    inTable: false,\n    nodeHandlers: {...nodeHandlers, ...options.nodeHandlers},\n    one,\n    options,\n    patch,\n    qNesting: 0,\n    resolve,\n    toFlow,\n    toSpecificContent\n  }\n}\n\n/**\n * Transform the children of a hast parent to mdast.\n *\n * You might want to combine this with `toFlow` or `toSpecificContent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents} parent\n *   Parent.\n * @returns {Array<MdastRootContent>}\n *   mdast children.\n */\nfunction all(parent) {\n  const children = parent.children || []\n  /** @type {Array<MdastRootContent>} */\n  const results = []\n  let index = -1\n\n  while (++index < children.length) {\n    const child = children[index]\n    // Content -> content.\n    const result =\n      /** @type {Array<MdastRootContent> | MdastRootContent | undefined} */ (\n        this.one(child, parent)\n      )\n\n    if (Array.isArray(result)) {\n      results.push(...result)\n    } else if (result) {\n      results.push(result)\n    }\n  }\n\n  return results\n}\n\n/**\n * Transform a hast node to mdast.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node to transform.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<MdastNodes> | MdastNodes | undefined}\n *   mdast result.\n */\nfunction one(node, parent) {\n  if (node.type === 'element') {\n    if (node.properties && node.properties.dataMdast === 'ignore') {\n      return\n    }\n\n    if (own.call(this.handlers, node.tagName)) {\n      return this.handlers[node.tagName](this, node, parent) || undefined\n    }\n  } else if (own.call(this.nodeHandlers, node.type)) {\n    return this.nodeHandlers[node.type](this, node, parent) || undefined\n  }\n\n  // Unknown literal.\n  if ('value' in node && typeof node.value === 'string') {\n    /** @type {MdastRootContent} */\n    const result = {type: 'text', value: node.value}\n    this.patch(node, result)\n    return result\n  }\n\n  // Unknown parent.\n  if ('children' in node) {\n    return this.all(node)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {Nodes} origin\n *   hast node to copy from.\n * @param {MdastNodes} node\n *   mdast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(origin, node) {\n  if (origin.position) node.position = position(origin)\n}\n\n/**\n * @this {State}\n *   Info passed around about the current state.\n * @param {string | null | undefined} url\n *   Possible URL value.\n * @returns {string}\n *   URL, resolved to a `base` element, if any.\n */\nfunction resolve(url) {\n  const base = this.frozenBaseUrl\n\n  if (url === null || url === undefined) {\n    return ''\n  }\n\n  if (base) {\n    return String(new URL(url, base))\n  }\n\n  return url\n}\n\n/**\n * Transform a list of mdast nodes to flow.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Array<MdastRootContent>} nodes\n *   Parent.\n * @returns {Array<MdastFlowContent>}\n *   mdast flow children.\n */\nfunction toFlow(nodes) {\n  return wrap(nodes)\n}\n\n/**\n * Turn arbitrary content into a particular node type.\n *\n * This is useful for example for lists, which must have list items as content.\n * in this example, when non-items are found, they will be queued, and\n * inserted into an adjacent item.\n * When no actual items exist, one will be made with `build`.\n *\n * @template {MdastNodes} ChildType\n *   Node type of children.\n * @template {MdastParents & {'children': Array<ChildType>}} ParentType\n *   Node type of parent.\n * @param {Array<MdastRootContent>} nodes\n *   Nodes, which are either `ParentType`, or will be wrapped in one.\n * @param {() => ParentType} build\n *   Build a parent if needed (must have empty `children`).\n * @returns {Array<ParentType>}\n *   List of parents.\n */\nfunction toSpecificContent(nodes, build) {\n  const reference = build()\n  /** @type {Array<ParentType>} */\n  const results = []\n  /** @type {Array<ChildType>} */\n  let queue = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const node = nodes[index]\n\n    if (expectedParent(node)) {\n      if (queue.length > 0) {\n        node.children.unshift(...queue)\n        queue = []\n      }\n\n      results.push(node)\n    } else {\n      // Assume `node` can be a child of `ParentType`.\n      // If we start checking nodes, we’d run into problems with unknown nodes,\n      // which we do want to support.\n      const child = /** @type {ChildType} */ (node)\n      queue.push(child)\n    }\n  }\n\n  if (queue.length > 0) {\n    let node = results[results.length - 1]\n\n    if (!node) {\n      node = build()\n      results.push(node)\n    }\n\n    node.children.push(...queue)\n    queue = []\n  }\n\n  return results\n\n  /**\n   * @param {MdastNodes} node\n   * @returns {node is ParentType}\n   */\n  function expectedParent(node) {\n    return node.type === reference.type\n  }\n}\n", "/**\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Nodes} from 'hast'\n * @import {Nodes as MdastNodes, RootContent as MdastRootContent} from 'mdast'\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport rehypeMinifyWhitespace from 'rehype-minify-whitespace'\nimport {visit} from 'unist-util-visit'\nimport {createState} from './state.js'\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Transform hast to mdast.\n *\n * @param {Readonly<Nodes>} tree\n *   hast tree to transform.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {MdastNodes}\n *   mdast tree.\n */\nexport function toMdast(tree, options) {\n  // We have to clone, cause we’ll use `rehype-minify-whitespace` on the tree,\n  // which modifies.\n  const cleanTree = structuredClone(tree)\n  const settings = options || emptyOptions\n  const transformWhitespace = rehypeMinifyWhitespace({\n    newlines: settings.newlines === true\n  })\n  const state = createState(settings)\n  /** @type {MdastNodes} */\n  let mdast\n\n  // @ts-expect-error: fine to pass an arbitrary node.\n  transformWhitespace(cleanTree)\n\n  visit(cleanTree, function (node) {\n    if (node && node.type === 'element' && node.properties) {\n      const id = String(node.properties.id || '') || undefined\n\n      if (id && !state.elementById.has(id)) {\n        state.elementById.set(id, node)\n      }\n    }\n  })\n\n  const result = state.one(cleanTree, undefined)\n\n  if (!result) {\n    mdast = {type: 'root', children: []}\n  } else if (Array.isArray(result)) {\n    // Assume content.\n    const children = /** @type {Array<MdastRootContent>} */ (result)\n    mdast = {type: 'root', children}\n  } else {\n    mdast = result\n  }\n\n  // Collapse text nodes, and fix whitespace.\n  //\n  // Most of this is taken care of by `rehype-minify-whitespace`, but\n  // we’re generating some whitespace too, and some nodes are in the end\n  // ignored.\n  // So clean up.\n  visit(mdast, function (node, index, parent) {\n    if (node.type === 'text' && index !== undefined && parent) {\n      const previous = parent.children[index - 1]\n\n      if (previous && previous.type === node.type) {\n        previous.value += node.value\n        parent.children.splice(index, 1)\n\n        if (previous.position && node.position) {\n          previous.position.end = node.position.end\n        }\n\n        // Iterate over the previous node again, to handle its total value.\n        return index - 1\n      }\n\n      node.value = node.value.replace(/[\\t ]*(\\r?\\n|\\r)[\\t ]*/, '$1')\n\n      // We don’t care about other phrasing nodes in between (e.g., `[ asd ]()`),\n      // as there the whitespace matters.\n      if (\n        parent &&\n        (parent.type === 'heading' ||\n          parent.type === 'paragraph' ||\n          parent.type === 'root')\n      ) {\n        if (!index) {\n          node.value = node.value.replace(/^[\\t ]+/, '')\n        }\n\n        if (index === parent.children.length - 1) {\n          node.value = node.value.replace(/[\\t ]+$/, '')\n        }\n      }\n\n      if (!node.value) {\n        parent.children.splice(index, 1)\n        return index\n      }\n    }\n  })\n\n  return mdast\n}\n", "/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Options} from 'hast-util-to-mdast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new mdast tree.\n *   Discards result.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the mdast tree.\n * @param {HastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {MdastRoot}\n *   Tree (mdast).\n */\n\nimport {toMdast} from 'hast-util-to-mdast'\n\n/** @satisfies {Options} */\nconst defaults = {document: true}\n\n/**\n * Turn HTML into markdown.\n *\n * ###### Notes\n *\n * *   if a processor is given, runs the (remark) plugins used on it with an\n *     mdast tree, then discards the result (*bridge mode*)\n * *   otherwise, returns an mdast tree, the plugins used after `rehypeRemark`\n *     are remark plugins (*mutate mode*)\n *\n * > 👉 **Note**: It’s highly unlikely that you want to pass a `processor`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Options | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Options | Processor | null | undefined} [destination]\n * @param {Options | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Options | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Options | null | undefined} [options]\n *   When a processor was given, configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function rehypeRemark(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      const mdastTree = toMdast(tree, {...defaults, ...options})\n      await destination.run(mdastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree) {\n    return /** @type {MdastRoot} */ (\n      toMdast(tree, {...defaults, ...destination})\n    )\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA6BO,IAAM,WAAW,MAAM,KAAK;AAU5B,IAAM,aAAa,MAAM,OAAO;AAUvC,SAAS,MAAM,MAAM;AACnB,SAAOA;AAQP,WAASA,OAAMC,OAAM;AACnB,UAAMD,SAASC,SAAQA,MAAK,YAAYA,MAAK,SAAS,IAAI,KAAM,CAAC;AAEjE,QACE,OAAOD,OAAM,SAAS,YACtBA,OAAM,OAAO,KACb,OAAOA,OAAM,WAAW,YACxBA,OAAM,SAAS,GACf;AACA,aAAO;AAAA,QACL,MAAMA,OAAM;AAAA,QACZ,QAAQA,OAAM;AAAA,QACd,QACE,OAAOA,OAAM,WAAW,YAAYA,OAAM,SAAS,KAC/CA,OAAM,SACN;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAUO,SAAS,SAASC,OAAM;AAC7B,QAAM,QAAQ,WAAWA,KAAI;AAC7B,QAAM,MAAM,SAASA,KAAI;AAEzB,MAAI,SAAS,KAAK;AAChB,WAAO,EAAC,OAAO,IAAG;AAAA,EACpB;AACF;;;AChFO,SAAS,EAAE,OAAOC,OAAM;AAC7B,QAAM,aAAaA,MAAK,cAAc,CAAC;AAGvC,QAAM;AAAA;AAAA,IAAkD,MAAM,IAAIA,KAAI;AAAA;AAGtE,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,KAAK,MAAM,QAAQ,OAAO,WAAW,QAAQ,EAAE,KAAK,IAAI;AAAA,IACxD,OAAO,WAAW,QAAQ,OAAO,WAAW,KAAK,IAAI;AAAA,IACrD;AAAA,EACF;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;AChBO,SAAS,KAAK,OAAOC,OAAM;AAChC,MAAI,CAAC,MAAM,WAAW;AACpB,UAAM,gBACJ,OAAQA,MAAK,cAAcA,MAAK,WAAW,QAAS,EAAE,KAAK;AAC7D,UAAM,YAAY;AAAA,EACpB;AACF;;;ACLO,SAAS,WAAW,OAAOC,OAAM;AAEtC,QAAM,SAAS,EAAC,MAAM,cAAc,UAAU,MAAM,OAAO,MAAM,IAAIA,KAAI,CAAC,EAAC;AAC3E,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACLO,SAAS,GAAG,OAAOC,OAAM;AAE9B,QAAM,SAAS,EAAC,MAAM,QAAO;AAC7B,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACXO,SAAS,kBAAkB,OAAO;AACvC,QAAMC,SAAQ,OAAO,KAAK;AAC1B,MAAI,MAAMA,OAAM;AAEhB,SAAO,MAAM,GAAG;AACd,UAAMC,QAAOD,OAAM,YAAY,MAAM,CAAC;AACtC,QAAIC,UAAS,WAAcA,UAAS,MAAMA,UAAS,KAAK;AACtD;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAOD,OAAM,MAAM,GAAG,GAAG;AAC3B;;;ACbA,IAAM,SAAS;AAUR,SAAS,KAAK,OAAOE,OAAM;AAChC,QAAM,WAAWA,MAAK;AACtB,MAAI,QAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,MAAIA,MAAK,YAAY,OAAO;AAC1B,WAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,YAAM,QAAQ,SAAS,KAAK;AAE5B,UACE,MAAM,SAAS,aACf,MAAM,YAAY,UAClB,MAAM,cACN,MAAM,WAAW,aACjB,MAAM,QAAQ,MAAM,WAAW,SAAS,GACxC;AACA,oBAAY,MAAM,WAAW;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW;AACb,YAAQ;AAER,WAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,UAAI,OAAO,UAAU,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,MAAM,MAAM,QAAQ;AAC/D,eAAO,OAAO,UAAU,KAAK,CAAC,EAAE,MAAM,OAAO,MAAM;AACnD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,MAAM,QAAQ;AAAA,IACd,MAAM;AAAA,IACN,OAAO,kBAAkB,OAAOA,KAAI,CAAC;AAAA,EACvC;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;AClDO,SAAS,QAAQ,OAAOC,OAAM;AAEnC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,OAAO,SAASA,MAAK,QAAQ;AAAA,EAC/B;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACRO,SAAS,IAAI,OAAOC,OAAM;AAG/B,QAAM;AAAA;AAAA,IAAkD,MAAM,IAAIA,KAAI;AAAA;AAEtE,QAAM,SAAS,EAAC,MAAM,UAAU,SAAQ;AACxC,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACVO,SAAS,gBAAgB,UAAU;AACxC,MAAI,QAAQ;AAEZ,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,UAAI,SAAS,KAAK,EAAE,QAAQ;AAC1B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACCO,SAAS,GAAG,OAAOC,OAAM;AAE9B,QAAM,QAAQ,CAAC;AAEf,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAGZ,SAAO,EAAE,QAAQA,MAAK,SAAS,QAAQ;AACrC,UAAM,QAAQA,MAAK,SAAS,KAAK;AAEjC,QAAI,MAAM,SAAS,aAAa,MAAM,YAAY,OAAO;AACvD,YAAM,KAAK,GAAG,MAAM,QAAQ;AAAA,IAC9B,OAAO;AACL,YAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AAGA,MAAI,QAAQ,EAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAC;AACxC,UAAQ;AAGR,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAM,QAAQ,MAAM,KAAK;AAEzB,QAAI,MAAM,SAAS,aAAa,MAAM,YAAY,MAAM;AACtD,YAAM,WAAW,MAAM,QAAQ,CAAC;AAEhC,UACE,YACA,SAAS,SAAS,aAClB,SAAS,YAAY,MACrB;AACA,eAAO,KAAK,KAAK;AACjB,gBAAQ,EAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAC;AAAA,MACtC;AAEA,YAAM,OAAO,KAAK,KAAK;AAAA,IACzB,OAAO;AACL,YAAM,YAAY,KAAK,KAAK;AAAA,IAC9B;AAAA,EACF;AAEA,SAAO,KAAK,KAAK;AAGjB,UAAQ;AAER,QAAM,UAAU,CAAC;AAEjB,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,SAAS;AAAA,MACb,GAAG,OAAO,OAAO,OAAO,KAAK,EAAE,MAAM;AAAA,MACrC,GAAG,OAAO,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C;AAEA,QAAI,OAAO,SAAS,GAAG;AACrB,cAAQ,KAAK;AAAA,QACX,MAAM;AAAA,QACN,QAAQ,OAAO,SAAS;AAAA,QACxB,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,GAAG;AAEtB,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ,gBAAgB,OAAO;AAAA,MAC/B,UAAU;AAAA,IACZ;AACA,UAAM,MAAMA,OAAM,MAAM;AACxB,WAAO;AAAA,EACT;AACF;AAUA,SAAS,OAAO,OAAO,UAAU;AAC/B,QAAM,QAAQ,MAAM,IAAI,EAAC,MAAM,QAAQ,SAAQ,CAAC;AAChD,QAAM,YAAY,MAAM,kBAAkB,OAAO,MAAM;AAEvD,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,UAAU,CAAC,EAAE;AAAA,EACtB;AAEA,SAAO;AAAA,IACL;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ,gBAAgB,SAAS;AAAA,MACjC,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AAKA,SAAS,SAAS;AAChB,SAAO,EAAC,MAAM,YAAY,QAAQ,OAAO,SAAS,MAAM,UAAU,CAAC,EAAC;AACtE;;;ACjIO,SAAS,GAAG,OAAOC,OAAM;AAG9B,QAAM;AAAA;AAAA,IAAkD,MAAM,IAAIA,KAAI;AAAA;AAGtE,QAAM,SAAS,EAAC,MAAM,YAAY,SAAQ;AAC1C,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACTO,SAAS,sBAAsB,OAAO;AAC3C,MAAI,QAAQ;AACZ,MAAI,MAAM,MAAM;AAEhB,SAAO,QAAQ,OAAO,MAAM,KAAK,EAAE,SAAS,QAAS;AACrD,SAAO,MAAM,SAAS,MAAM,MAAM,CAAC,EAAE,SAAS,QAAS;AAEvD,SAAO,UAAU,KAAK,QAAQ,MAAM,SAAS,QAAQ,MAAM,MAAM,OAAO,GAAG;AAC7E;;;ACNO,SAAS,QAAQ,OAAOC,OAAM;AACnC,QAAM;AAAA;AAAA;AAAA,IAEJ,OAAOA,MAAK,QAAQ,OAAO,CAAC,CAAC,KAAK;AAAA;AAEpC,QAAM,WAAW;AAAA;AAAA,IACwB,MAAM,IAAIA,KAAI;AAAA,EACvD;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,OAAO,SAAQ;AAChD,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACfO,SAAS,GAAG,OAAOC,OAAM;AAE9B,QAAM,SAAS,EAAC,MAAM,gBAAe;AACrC,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACLO,SAAS,OAAO,OAAOC,OAAM;AAClC,QAAM,aAAaA,MAAK,cAAc,CAAC;AACvC,QAAM,SAAS,OAAO,WAAW,OAAO,EAAE;AAC1C,QAAM,QAAQ,OAAO,WAAW,SAAS,EAAE;AAM3C,MAAI,UAAU,OAAO;AAEnB,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK,MAAM,QAAQ,MAAM;AAAA,MACzB,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,MAAK,CAAC;AAAA,IACzC;AACA,UAAM,MAAMA,OAAM,MAAM;AACxB,WAAO;AAAA,EACT;AACF;;;ACpBO,SAAS,IAAI,OAAOC,OAAM;AAC/B,QAAM,aAAaA,MAAK,cAAc,CAAC;AAGvC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,KAAK,MAAM,QAAQ,OAAO,WAAW,OAAO,EAAE,KAAK,IAAI;AAAA,IACvD,OAAO,WAAW,QAAQ,OAAO,WAAW,KAAK,IAAI;AAAA,IACrD,KAAK,WAAW,MAAM,OAAO,WAAW,GAAG,IAAI;AAAA,EACjD;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACVO,SAAS,WAAW,OAAOC,OAAM;AAEtC,QAAM,SAAS,EAAC,MAAM,cAAc,OAAO,OAAOA,KAAI,EAAC;AACvD,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACIO,SAAS,oBAAoBC,OAAM,oBAAoB;AAE5D,QAAM,kBAAkB,CAAC;AAEzB,QAAM,SAAS,CAAC;AAChB,QAAM,aAAa,sBAAsBA,MAAK,cAAc,CAAC;AAC7D,QAAM,UAAU,YAAYA,KAAI;AAChC,QAAM,OACJ,KAAK,IAAI,OAAO,SAAS,OAAO,WAAW,IAAI,GAAG,EAAE,GAAG,CAAC,MACvD,WAAW,WAAW,IAAI;AAC7B,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,SAAS,QAAQ,KAAK;AAE5B,QAAI,UAAU,OAAO,cAAc,OAAO,WAAW,UAAU;AAC7D,sBAAgB,KAAK,MAAM;AAAA,IAC7B;AAAA,EACF;AAEA,QAAMC,QAAO,gBAAgB,SAAS,IAAI,kBAAkB;AAC5D,QAAM,MAAM,KAAK,IAAIA,MAAK,QAAQ,IAAI;AACtC,UAAQ;AAER,SAAO,EAAE,QAAQ,KAAK;AACpB,UAAM,SAASA,MAAK,KAAK;AACzB,UAAMC,cAAa,OAAO,cAAc,CAAC;AACzC,UAAM,UAAU,OAAO,MAAM;AAC7B,UAAM,QAAQ,WAAW,OAAOA,YAAW,SAAS,EAAE;AACtD,UAAM,QAAQ,OAAOA,YAAW,SAAS,EAAE,KAAK;AAChD,WAAO,KAAK,CAAC,OAAO,UAAU,QAAQ,SAAY,KAAK,CAAC;AAAA,EAC1D;AAEA,SAAO;AACT;AAQA,SAAS,YAAYF,OAAM;AAEzB,QAAM,UAAU,CAAC;AACjB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,MAAK,SAAS,QAAQ;AACrC,UAAM,QAAQA,MAAK,SAAS,KAAK;AAEjC,QAAI,cAAc,SAAS,MAAM,QAAQ,MAAM,QAAQ,GAAG;AACxD,cAAQ,KAAK,GAAG,YAAY,KAAK,CAAC;AAAA,IACpC;AAEA,QACE,MAAM,SAAS,aACf,MAAM,YAAY,aACjB,CAAC,MAAM,cAAc,CAAC,MAAM,WAAW,WACxC;AACA,cAAQ,KAAK,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;;;AChFA,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AAWlB,SAAS,MAAM,OAAOG,OAAM;AACjC,QAAM,aAAaA,MAAK,cAAc,CAAC;AACvC,QAAM,QAAQ,OAAO,WAAW,SAAS,WAAW,eAAe,EAAE;AAErE,MACE,WAAW,YACX,WAAW,SAAS,YACpB,WAAW,SAAS,QACpB;AACA;AAAA,EACF;AAEA,MAAI,WAAW,SAAS,cAAc,WAAW,SAAS,SAAS;AAEjE,UAAMC,UAAS;AAAA,MACb,MAAM;AAAA,MACN,OAAO,WAAW,UACd,MAAM,QAAQ,WAAW,iBACzB,MAAM,QAAQ,aAAa;AAAA,IACjC;AACA,UAAM,MAAMD,OAAMC,OAAM;AACxB,WAAOA;AAAA,EACT;AAEA,MAAI,WAAW,SAAS,SAAS;AAC/B,UAAM,MAAM,WAAW,OAAO;AAE9B,QAAI,KAAK;AAEP,YAAMA,UAAS;AAAA,QACb,MAAM;AAAA,QACN,KAAK,MAAM,QAAQ,OAAO,WAAW,OAAO,EAAE,KAAK,IAAI;AAAA,QACvD,OAAO,OAAO,WAAW,SAAS,EAAE,KAAK;AAAA,QACzC,KAAK,OAAO,GAAG;AAAA,MACjB;AACA,YAAM,MAAMD,OAAMC,OAAM;AACxB,aAAOA;AAAA,IACT;AAEA;AAAA,EACF;AAGA,MAAI,SAAS,CAAC;AAEd,MAAI,OAAO;AACT,aAAS,CAAC,CAAC,OAAO,MAAS,CAAC;AAAA,EAC9B;AAAA;AAAA,IAEE,WAAW,SAAS,YACpB,WAAW,SAAS,UACpB,WAAW,SAAS,cACpB,WAAW,SAAS,WACpB,WAAW,SAAS,YACpB,WAAW;AAAA,IACX;AACA,UAAMC,QAAO,OAAO,WAAW,IAAI;AACnC,UAAM,WAAW,MAAM,YAAY,IAAIA,KAAI;AAE3C,QAAI,YAAY,SAAS,YAAY,YAAY;AAC/C,eAAS,oBAAoB,UAAU,UAAU;AAAA,IACnD;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,GAAG;AACvB;AAAA,EACF;AAGA,MAAI,WAAW,SAAS,YAAY;AAElC,WAAO,CAAC,IAAI,CAAC,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,MAAS;AAAA,EACzD;AAEA,MAAI,WAAW,SAAS,WAAW,WAAW,SAAS,OAAO;AAE5D,UAAM,UAAU,CAAC;AACjB,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,YAAMC,SAAQ,MAAM,QAAQ,OAAOD,MAAK,EAAE,CAAC,CAAC;AAE5C,YAAMF,UAAS;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK,WAAW,SAAS,UAAU,YAAYG,SAAQA;AAAA,QACvD,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAOD,MAAK,EAAE,CAAC,KAAKC,OAAK,CAAC;AAAA,MAC7D;AAEA,cAAQ,KAAKH,OAAM;AAEnB,UAAIE,WAAU,OAAO,SAAS,GAAG;AAC/B,gBAAQ,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,MAC1C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAGA,QAAM,QAAQ,CAAC;AACf,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM;AAAA,MACJ,OAAO,KAAK,EAAE,CAAC,IACX,OAAO,KAAK,EAAE,CAAC,IAAI,OAAO,OAAO,KAAK,EAAE,CAAC,IAAI,MAC7C,OAAO,KAAK,EAAE,CAAC;AAAA,IACrB;AAAA,EACF;AAGA,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,MAAM,KAAK,IAAI,EAAC;AACrD,QAAM,MAAMH,OAAM,MAAM;AACxB,SAAO;AACT;;;AC9GO,SAAS,GAAG,OAAOK,OAAM;AAG9B,QAAM,EAAC,MAAM,SAAQ,IAAI,uBAAuBA,KAAI;AACpD,QAAM,UAAU,WAAW,QAAQ,SAAS,WAAW,OAAO,IAAI;AAClE,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,WAAW,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC;AAG7C,QAAM,SAAS,EAAC,MAAM,YAAY,QAAQ,SAAS,SAAQ;AAC3D,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;AAwBA,SAAS,UAAUA,OAAM;AACvB,MAAI,QAAQ;AACZ,MAAI,WAAW;AAEf,SAAO,EAAE,QAAQA,MAAK,SAAS,QAAQ;AACrC,UAAM,QAAQA,MAAK,SAAS,KAAK;AAEjC,QAAI,MAAM,SAAS,WAAW;AAC5B,UAAI,SAAS,KAAK,EAAG;AAErB,UAAI,MAAM,YAAY,OAAO,YAAY,UAAU,KAAK,GAAG;AACzD,eAAO;AAAA,MACT;AAEA,iBAAW;AAAA,IACb;AAAA,EACF;AAEA,SAAO;AACT;AAkCA,SAAS,uBAAuBA,OAAM;AACpC,QAAM,OAAOA,MAAK,SAAS,CAAC;AAE5B,MACE,QACA,KAAK,SAAS,aACd,KAAK,YAAY,WACjB,KAAK,eACJ,KAAK,WAAW,SAAS,cAAc,KAAK,WAAW,SAAS,UACjE;AACA,UAAM,OAAO,EAAC,GAAGA,OAAM,UAAUA,MAAK,SAAS,MAAM,CAAC,EAAC;AACvD,WAAO,EAAC,UAAU,MAAM,KAAI;AAAA,EAC9B;AASA,MAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,UAAM,EAAC,UAAU,MAAM,SAAQ,IAAI,uBAAuB,IAAI;AAE9D,QAAI,UAAU;AACZ,YAAM,OAAO,EAAC,GAAGA,OAAM,UAAU,CAAC,UAAU,GAAGA,MAAK,SAAS,MAAM,CAAC,CAAC,EAAC;AACtE,aAAO,EAAC,UAAU,KAAI;AAAA,IACxB;AAAA,EACF;AAEA,SAAO,EAAC,UAAU,QAAW,MAAMA,MAAI;AACzC;;;AClIO,SAAS,KAAK,OAAOC,OAAM;AAChC,QAAM,UAAUA,MAAK,YAAY;AACjC,QAAM,WAAW,MAAM,kBAAkB,MAAM,IAAIA,KAAI,GAAGC,OAAM;AAEhE,MAAI,QAAQ;AAEZ,MAAI,SAAS;AACX,YACED,MAAK,cAAcA,MAAK,WAAW,QAC/B,OAAO,SAAS,OAAOA,MAAK,WAAW,KAAK,GAAG,EAAE,IACjD;AAAA,EACR;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,QAAQ,gBAAgB,QAAQ;AAAA,IAChC;AAAA,EACF;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;AAKA,SAASC,UAAS;AAChB,SAAO,EAAC,MAAM,YAAY,QAAQ,OAAO,SAAS,MAAM,UAAU,CAAC,EAAC;AACtE;;;AClCA,IAAM,eAAe,CAAC;AAef,SAAS,SAAS,OAAO,SAAS;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,kBACJ,OAAO,SAAS,oBAAoB,YAChC,SAAS,kBACT;AACN,QAAM,cACJ,OAAO,SAAS,gBAAgB,YAAY,SAAS,cAAc;AAErE,SAAO,IAAI,OAAO,iBAAiB,WAAW;AAChD;AAcA,SAAS,IAAI,OAAO,iBAAiB,aAAa;AAChD,MAAI,KAAK,KAAK,GAAG;AACf,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,SAAS,UAAU,CAAC,cAAc,KAAK,MAAM;AAAA,IAC5D;AAEA,QAAI,mBAAmB,SAAS,SAAS,MAAM,KAAK;AAClD,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,cAAc,OAAO;AACvB,aAAO,IAAI,MAAM,UAAU,iBAAiB,WAAW;AAAA,IACzD;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,IAAI,OAAO,iBAAiB,WAAW;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAAS,IAAI,QAAQ,iBAAiB,aAAa;AAEjD,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,WAAO,KAAK,IAAI,IAAI,OAAO,KAAK,GAAG,iBAAiB,WAAW;AAAA,EACjE;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAUA,SAAS,KAAK,OAAO;AACnB,SAAO,QAAQ,SAAS,OAAO,UAAU,QAAQ;AACnD;;;AC9EO,SAAS,WAAW,OAAO;AAChC,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAMC,QAAO,MAAM,KAAK;AAExB,QAAI,CAACC,UAASD,KAAI,KAAM,cAAcA,SAAQ,WAAWA,MAAK,QAAQ,GAAI;AACxE,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAWO,SAAS,KAAK,OAAO;AAC1B,SAAO,KAAK,OAAO,YAAY,SAAU,GAAG;AAC1C,WAAO;AAAA,EACT,CAAC;AAMD,WAAS,WAAWE,QAAO;AACzB,WAAOA,OAAM,MAAM,SAAU,GAAG;AAC9B,aAAO,EAAE,SAAS,SAAS,WAAW,EAAE,KAAK,IAAI;AAAA,IACnD,CAAC,IACG,CAAC,IACD,CAAC,EAAC,MAAM,aAAa,UAAU,sBAAsBA,MAAK,EAAC,CAAC;AAAA,EAClE;AACF;AAMA,SAAS,MAAMF,OAAM;AACnB,SAAO,KAAKA,MAAK,UAAU,YAAY,aAAa;AAQpD,WAAS,WAAW,OAAO;AACzB,UAAM,YAAY,qBAAqBA,KAAI;AAC3C,cAAU,WAAW;AAErB,WAAO,CAAC,SAAS;AAAA,EACnB;AAUA,WAAS,cAAc,OAAO;AAC5B,QAAI,cAAc,SAAS,cAAcA,OAAM;AAC7C,YAAM,YAAY,qBAAqBA,KAAI;AAC3C,YAAM,WAAW,qBAAqB,KAAK;AAE3C,gBAAU,WAAW,MAAM;AAE3B,eAAS,SAAS,KAAK,SAAS;AAChC,aAAO;AAAA,IACT;AAEA,WAAO,EAAC,GAAG,MAAK;AAAA,EAClB;AACF;AAaA,SAAS,KAAK,OAAO,YAAY,eAAe;AAC9C,QAAM,YAAY,QAAQ,KAAK;AAE/B,QAAM,SAAS,CAAC;AAEhB,MAAI,QAAQ,CAAC;AACb,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,UAAMA,QAAO,UAAU,KAAK;AAE5B,QAAIC,UAASD,KAAI,GAAG;AAClB,YAAM,KAAKA,KAAI;AAAA,IACjB,OAAO;AACL,UAAI,MAAM,SAAS,GAAG;AACpB,eAAO,KAAK,GAAG,WAAW,KAAK,CAAC;AAChC,gBAAQ,CAAC;AAAA,MACX;AAGA,aAAO,KAAK,cAAcA,KAAI,CAAC;AAAA,IACjC;AAAA,EACF;AAEA,MAAI,MAAM,SAAS,GAAG;AACpB,WAAO,KAAK,GAAG,WAAW,KAAK,CAAC;AAChC,YAAQ,CAAC;AAAA,EACX;AAEA,SAAO;AACT;AAUA,SAAS,QAAQ,OAAO;AAEtB,QAAM,YAAY,CAAC;AACnB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAMA,QAAO,MAAM,KAAK;AAWxB,SACGA,MAAK,SAAS,YAAYA,MAAK,SAAS,WACzC,WAAWA,MAAK,QAAQ,GACxB;AACA,gBAAU,KAAK,GAAG,MAAMA,KAAI,CAAC;AAAA,IAC/B,OAAO;AACL,gBAAU,KAAKA,KAAI;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AAaA,SAASC,UAASD,OAAM;AACtB,QAAM,UAAUA,MAAK,QAAQA,MAAK,KAAK;AACvC,SAAO,UACH,SAAa,EAAC,MAAM,WAAW,SAAS,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC,CAAC,IACrEC,UAAcD,KAAI;AACxB;AAUA,SAAS,qBAAqBA,OAAM;AAClC,SAAO,YAAgB,EAAC,GAAGA,OAAM,UAAU,CAAC,EAAC,CAAC;AAChD;;;AC5MO,SAAS,MAAM,OAAOG,OAAM;AACjC,QAAM,aAAaA,MAAK,cAAc,CAAC;AACvC,QAAM,SAASA,MAAK,YAAY,UAAU,OAAO,WAAW,UAAU,EAAE,IAAI;AAC5E,MAAI,SAAS,OAAO,WAAW,OAAO,EAAE;AACxC,MAAI,QAAQ;AACZ,MAAI,wBAAwB;AAC5B,MAAI,QAAQ,MAAM,IAAIA,KAAI;AAG1B,QAAM,WAAW,EAAC,MAAM,QAAQ,UAAU,MAAK;AAE/C,QAAM,UAAU,SAAUA,OAAM;AAC9B,QAAIA,MAAK,SAAS,QAAQ;AACxB,8BAAwB;AACxB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAGD,MAAI,yBAAyB,WAAW,KAAK,GAAG;AAC9C,WAAO;AAAA,EACT;AAGA,SAAO,CAAC,UAAU,EAAE,QAAQA,MAAK,SAAS,QAAQ;AAChD,UAAM,QAAQA,MAAK,SAAS,KAAK;AAEjC,QACE,MAAM,SAAS,aACf,MAAM,YAAY,YAClB,MAAM,YACN;AACA,eAAS,OAAO,MAAM,WAAW,OAAO,EAAE;AAAA,IAC5C;AAAA,EACF;AAGA,MAAI,QAAQ;AAEV,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK,MAAM,QAAQ,MAAM;AAAA,MACzB,KAAK,SAAS,KAAK;AAAA,IACrB;AACA,UAAM,MAAMA,OAAM,KAAK;AACvB,YAAQ,CAAC,KAAK;AAAA,EAChB;AAIA,QAAM;AAAA;AAAA,IAAkD;AAAA;AAIxD,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,OAAO,WAAW,QAAQ,OAAO,WAAW,KAAK,IAAI;AAAA,IACrD,KAAK,MAAM,QAAQ,MAAM;AAAA,IACzB;AAAA,EACF;AACA,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACjEO,SAAS,EAAE,OAAOC,OAAM;AAC7B,QAAM,WAAW;AAAA;AAAA;AAAA;AAAA,IAGwB,MAAM,IAAIA,KAAI;AAAA,EACvD;AAEA,MAAI,SAAS,SAAS,GAAG;AAEvB,UAAM,SAAS,EAAC,MAAM,aAAa,SAAQ;AAC3C,UAAM,MAAMA,OAAM,MAAM;AACxB,WAAO;AAAA,EACT;AACF;;;ACvBA,IAAM,gBAAgB,CAAC,GAAG;AAUnB,SAAS,EAAE,OAAOC,OAAM;AAC7B,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,QAAM;AACN,QAAM,WAAW,MAAM,IAAIA,KAAI;AAC/B,QAAM;AAEN,QAAM,QAAQ,OAAO,MAAM,WAAW,OAAO,MAAM;AACnD,QAAM,OAAO,SAAS,CAAC;AACvB,QAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AACzC,QAAM,OAAO,MAAM,OAAO,CAAC;AAC3B,QAAM,QAAQ,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,IAAI;AAEnD,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,QAAQ,OAAO,KAAK;AAAA,EAC3B,OAAO;AACL,aAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EAC9C;AAEA,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,SAAS;AAAA,EAChB,OAAO;AACL,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,MAAK,CAAC;AAAA,EAC5C;AAEA,SAAO;AACT;;;AC1BO,SAAS,KAAK,OAAOC,OAAM;AAChC,MAAI,WAAW,MAAM,IAAIA,KAAI;AAE7B,MAAI,MAAM,QAAQ,YAAY,WAAW,QAAQ,GAAG;AAClD,eAAW,KAAK,QAAQ;AAAA,EAC1B;AAGA,QAAM,SAAS,EAAC,MAAM,QAAQ,SAAQ;AACtC,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACXO,SAAS,OAAO,OAAOC,OAAM;AAClC,QAAM,SAAS,oBAAoBA,KAAI;AACvC,MAAI,QAAQ;AAEZ,QAAM,UAAU,CAAC;AAEjB,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAO,KAAK;AAC1B,YAAQ,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,EACrE;AAEA,MAAI,QAAQ,SAAS,GAAG;AAEtB,UAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,QAAQ,KAAK,IAAI,EAAC;AACvD,UAAM,MAAMA,OAAM,MAAM;AACxB,WAAO;AAAA,EACT;AACF;;;ACnBO,SAAS,OAAO,OAAOC,OAAM;AAGlC,QAAM;AAAA;AAAA,IAAkD,MAAM,IAAIA,KAAI;AAAA;AAGtE,QAAM,SAAS,EAAC,MAAM,UAAU,SAAQ;AACxC,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACTO,SAAS,UAAU,OAAOC,OAAM;AAGrC,QAAM;AAAA;AAAA,IAAkD,MAAM,IAAIA,KAAI;AAAA;AAGtE,QAAM,SAAS,EAAC,MAAM,aAAa,SAAQ;AAC3C,QAAM,MAAMA,OAAM,MAAM;AAExB,MAAIA,MAAK,YAAY;AACnB,UAAM,UAAUA,MAAK,WAAW;AAChC,UAAM,UAAUA,MAAK,WAAW;AAEhC,QAAI,WAAW,SAAS;AACtB,YAAM;AAAA;AAAA,QACJ,OAAO,SAAS,OAAO,OAAO,CAAC;AAAA;AAEjC,UAAI,QAAS,MAAK,kCAAkC;AACpD,UAAI,QAAS,MAAK,kCAAkC;AAAA,IACtD;AAAA,EACF;AAEA,SAAO;AACT;;;ACvBO,SAAS,SAAS,OAAOC,OAAM;AACpC,QAAM,WAAW,MAAM,kBAAkB,MAAM,IAAIA,KAAI,GAAGC,OAAM;AAGhE,QAAM,SAAS,EAAC,MAAM,YAAY,SAAQ;AAC1C,QAAM,MAAMD,OAAM,MAAM;AACxB,SAAO;AACT;AAKA,SAASC,UAAS;AAChB,SAAO,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC;AACzC;;;ACDO,SAAS,MAAM,OAAOC,OAAM;AAEjC,MAAI,MAAM,SAAS;AAEjB,UAAMC,UAAS,EAAC,MAAM,QAAQ,OAAO,OAAOD,KAAI,EAAC;AACjD,UAAM,MAAMA,OAAMC,OAAM;AACxB,WAAOA;AAAA,EACT;AAEA,QAAM,UAAU;AAEhB,QAAM,EAAC,OAAO,SAAQ,IAAI,QAAQD,KAAI;AACtC,QAAM,OAAO,MAAM,kBAAkB,MAAM,IAAIA,KAAI,GAAG,SAAS;AAG/D,MAAI,UAAU;AACZ,SAAK,QAAQ,UAAU,CAAC;AAAA,EAC1B;AAEA,MAAI,WAAW;AAEf,SAAO,EAAE,WAAW,KAAK,QAAQ;AAC/B,UAAM,MAAM,KAAK,QAAQ;AACzB,UAAM,QAAQ,MAAM,kBAAkB,IAAI,UAAU,UAAU;AAC9D,QAAI,WAAW;AAAA,EACjB;AAEA,MAAI,UAAU;AACd,aAAW;AAEX,SAAO,EAAE,WAAW,KAAK,QAAQ;AAC/B,UAAM,QAAQ,KAAK,QAAQ,EAAE;AAC7B,QAAI,YAAY;AAEhB,WAAO,EAAE,YAAY,MAAM,QAAQ;AACjC,YAAM,OAAO,MAAM,SAAS;AAE5B,UAAI,KAAK,MAAM;AACb,cAAM;AAAA;AAAA,UAA+C,KAAK;AAAA;AAC1D,cAAM,UACJ,OAAO,SAAS,OAAO,KAAK,+BAA+B,GAAG,EAAE,KAAK;AACvE,cAAM,UACJ,OAAO,SAAS,OAAO,KAAK,+BAA+B,GAAG,EAAE,KAAK;AAEvE,YAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,cAAI,gBAAgB,WAAW;AAE/B,iBAAO,EAAE,gBAAgB,WAAW,SAAS;AAC3C,gBAAI,WAAW,YAAY;AAE3B,mBAAO,EAAE,WAAW,YAAY,SAAS;AACvC,kBAAI,CAAC,KAAK,aAAa,GAAG;AAGxB;AAAA,cACF;AAGA,oBAAM,WAAW,CAAC;AAElB,kBAAI,kBAAkB,YAAY,aAAa,WAAW;AACxD,yBAAS,KAAK,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC,CAAC;AAAA,cACjD;AAEA,mBAAK,aAAa,EAAE,SAAS,OAAO,UAAU,GAAG,GAAG,QAAQ;AAAA,YAC9D;AAAA,UACF;AAAA,QACF;AAGA,YAAI,qCAAqC,KAAK;AAC5C,iBAAO,KAAK,KAAK;AACnB,YAAI,qCAAqC,KAAK;AAC5C,iBAAO,KAAK,KAAK;AACnB,YAAI,OAAO,KAAK,KAAK,IAAI,EAAE,WAAW,EAAG,QAAO,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,QAAS,WAAU,MAAM;AAAA,EAC9C;AAGA,aAAW;AAEX,SAAO,EAAE,WAAW,KAAK,QAAQ;AAC/B,UAAM,QAAQ,KAAK,QAAQ,EAAE;AAC7B,QAAI,YAAY,MAAM,SAAS;AAC/B,WAAO,EAAE,YAAY,SAAS;AAC5B,YAAM,KAAK,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC,CAAC;AAAA,IAC9C;AAAA,EACF;AAEA,MAAI,aAAa,MAAM,SAAS;AAChC,SAAO,EAAE,aAAa,SAAS;AAC7B,UAAM,KAAK,IAAI;AAAA,EACjB;AAEA,QAAM,UAAU;AAGhB,QAAM,SAAS,EAAC,MAAM,SAAS,OAAO,UAAU,KAAI;AACpD,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;AAUA,SAAS,QAAQA,OAAM;AAErB,QAAM,OAAO,EAAC,OAAO,CAAC,IAAI,GAAG,UAAU,KAAI;AAC3C,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,QAAMA,OAAM,SAAU,OAAO;AAC3B,QAAI,MAAM,SAAS,WAAW;AAE5B,UAAI,MAAM,YAAY,WAAWA,UAAS,OAAO;AAC/C,eAAO;AAAA,MACT;AAEA,WACG,MAAM,YAAY,QAAQ,MAAM,YAAY,SAC7C,MAAM,YACN;AACA,YAAI,CAAC,KAAK,MAAM,SAAS,GAAG;AAC1B,gBAAM,QAAQ,OAAO,MAAM,WAAW,SAAS,EAAE,KAAK;AAEtD,cACE,UAAU,YACV,UAAU,UACV,UAAU,WACV,UAAU,MACV;AACA,iBAAK,MAAM,SAAS,IAAI;AAAA,UAC1B;AAAA,QACF;AAGA,YAAI,KAAK,YAAY,WAAW,KAAK,MAAM,YAAY,MAAM;AAC3D,eAAK,WAAW;AAAA,QAClB;AAEA;AAAA,MACF,WAES,MAAM,YAAY,SAAS;AAClC,aAAK,WAAW;AAAA,MAClB,WAAW,MAAM,YAAY,MAAM;AACjC;AACA,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAKA,SAAS,aAAa;AACpB,SAAO,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC;AACzC;AAKA,SAAS,YAAY;AACnB,SAAO,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC;AACxC;;;AC5LO,SAAS,KAAK,OAAOE,OAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAOA,MAAK,MAAK;AAC/C,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACHO,SAAS,SAAS,OAAOC,OAAM;AAEpC,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,OAAOA,KAAI,EAAC;AACjD,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACPO,SAAS,IAAI,OAAOC,OAAM;AAE/B,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,IAAQ;AAC7C,QAAM,MAAMA,OAAM,MAAM;AACxB,SAAO;AACT;;;ACqBO,IAAM,eAAe;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AACF;AAOO,IAAM,WAAW;AAAA;AAAA,EAEtB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,UAAU;AAAA,EACV,OAAO;AAAA,EACP,OAAO;AAAA;AAAA,EAGP,MAAMC;AAAA,EACN,SAASA;AAAA,EACT,KAAKA;AAAA,EACL,KAAKA;AAAA,EACL,KAAKA;AAAA,EACL,OAAOA;AAAA,EACP,QAAQA;AAAA,EACR,QAAQA;AAAA,EACR,MAAMA;AAAA,EACN,MAAMA;AAAA,EACN,SAASA;AAAA,EACT,KAAKA;AAAA,EACL,MAAMA;AAAA,EACN,KAAKA;AAAA,EACL,OAAOA;AAAA,EACP,KAAKA;AAAA,EACL,SAASA;AAAA,EACT,OAAOA;AAAA,EACP,MAAMA;AAAA,EACN,UAAUA;AAAA,EACV,QAAQA;AAAA,EACR,QAAQA;AAAA,EACR,UAAUA;AAAA,EACV,IAAIA;AAAA,EACJ,KAAKA;AAAA,EACL,IAAIA;AAAA,EACJ,IAAIA;AAAA,EACJ,KAAKA;AAAA,EACL,MAAMA;AAAA,EACN,MAAMA;AAAA,EACN,OAAOA;AAAA,EACP,MAAMA;AAAA,EACN,KAAKA;AAAA,EACL,KAAKA;AAAA,EACL,OAAOA;AAAA,EACP,OAAOA;AAAA,EACP,OAAOA;AAAA,EACP,MAAMA;AAAA;AAAA,EAGN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,KAAK;AAAA,EACL,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAGT;AAAA,EACA,OAAO;AAAA,EACP,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,KAAK;AAAA,EACL;AAAA,EACA,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ;AAAA,EACA,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,KAAK;AAAA,EACL;AAAA,EACA,SAAS;AAAA,EACT,MAAM;AAAA,EACN,IAAI;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,EACX,KAAK;AAAA,EACL;AAAA,EACA,GAAG;AAAA,EACH,MAAM;AAAA,EACN;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,OAAO;AAAA,EACP;AAAA,EACA,KAAK;AACP;AAQA,SAASA,KAAI,OAAOC,OAAM;AACxB,SAAO,MAAM,IAAIA,KAAI;AACvB;AAQA,SAAS,KAAK,OAAOA,OAAM;AACzB,SAAO,MAAM,OAAO,MAAM,IAAIA,KAAI,CAAC;AACrC;AAKA,SAAS,SAAS;AAAC;;;ACjEnB,IAAM,MAAM,CAAC,EAAE;AAUR,SAAS,YAAY,SAAS;AACnC,SAAO;AAAA,IACL,KAAAC;AAAA,IACA,WAAW;AAAA,IACX,aAAa,oBAAI,IAAI;AAAA,IACrB,eAAe;AAAA,IACf,UAAU,EAAC,GAAG,UAAU,GAAG,QAAQ,SAAQ;AAAA,IAC3C,SAAS;AAAA,IACT,cAAc,EAAC,GAAG,cAAc,GAAG,QAAQ,aAAY;AAAA,IACvD,KAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAcA,SAASD,KAAI,QAAQ;AACnB,QAAM,WAAW,OAAO,YAAY,CAAC;AAErC,QAAM,UAAU,CAAC;AACjB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,UAAM,QAAQ,SAAS,KAAK;AAE5B,UAAM;AAAA;AAAA,MAEF,KAAK,IAAI,OAAO,MAAM;AAAA;AAG1B,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,cAAQ,KAAK,GAAG,MAAM;AAAA,IACxB,WAAW,QAAQ;AACjB,cAAQ,KAAK,MAAM;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AAcA,SAASC,KAAIC,OAAM,QAAQ;AACzB,MAAIA,MAAK,SAAS,WAAW;AAC3B,QAAIA,MAAK,cAAcA,MAAK,WAAW,cAAc,UAAU;AAC7D;AAAA,IACF;AAEA,QAAI,IAAI,KAAK,KAAK,UAAUA,MAAK,OAAO,GAAG;AACzC,aAAO,KAAK,SAASA,MAAK,OAAO,EAAE,MAAMA,OAAM,MAAM,KAAK;AAAA,IAC5D;AAAA,EACF,WAAW,IAAI,KAAK,KAAK,cAAcA,MAAK,IAAI,GAAG;AACjD,WAAO,KAAK,aAAaA,MAAK,IAAI,EAAE,MAAMA,OAAM,MAAM,KAAK;AAAA,EAC7D;AAGA,MAAI,WAAWA,SAAQ,OAAOA,MAAK,UAAU,UAAU;AAErD,UAAM,SAAS,EAAC,MAAM,QAAQ,OAAOA,MAAK,MAAK;AAC/C,SAAK,MAAMA,OAAM,MAAM;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,cAAcA,OAAM;AACtB,WAAO,KAAK,IAAIA,KAAI;AAAA,EACtB;AACF;AAYA,SAAS,MAAM,QAAQA,OAAM;AAC3B,MAAI,OAAO,SAAU,CAAAA,MAAK,WAAW,SAAS,MAAM;AACtD;AAUA,SAAS,QAAQ,KAAK;AACpB,QAAMC,QAAO,KAAK;AAElB,MAAI,QAAQ,QAAQ,QAAQ,QAAW;AACrC,WAAO;AAAA,EACT;AAEA,MAAIA,OAAM;AACR,WAAO,OAAO,IAAI,IAAI,KAAKA,KAAI,CAAC;AAAA,EAClC;AAEA,SAAO;AACT;AAYA,SAAS,OAAO,OAAO;AACrB,SAAO,KAAK,KAAK;AACnB;AAqBA,SAAS,kBAAkB,OAAO,OAAO;AACvC,QAAM,YAAY,MAAM;AAExB,QAAM,UAAU,CAAC;AAEjB,MAAI,QAAQ,CAAC;AACb,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAMD,QAAO,MAAM,KAAK;AAExB,QAAI,eAAeA,KAAI,GAAG;AACxB,UAAI,MAAM,SAAS,GAAG;AACpB,QAAAA,MAAK,SAAS,QAAQ,GAAG,KAAK;AAC9B,gBAAQ,CAAC;AAAA,MACX;AAEA,cAAQ,KAAKA,KAAI;AAAA,IACnB,OAAO;AAIL,YAAM;AAAA;AAAA,QAAkCA;AAAA;AACxC,YAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,MAAM,SAAS,GAAG;AACpB,QAAIA,QAAO,QAAQ,QAAQ,SAAS,CAAC;AAErC,QAAI,CAACA,OAAM;AACT,MAAAA,QAAO,MAAM;AACb,cAAQ,KAAKA,KAAI;AAAA,IACnB;AAEA,IAAAA,MAAK,SAAS,KAAK,GAAG,KAAK;AAC3B,YAAQ,CAAC;AAAA,EACX;AAEA,SAAO;AAMP,WAAS,eAAeA,OAAM;AAC5B,WAAOA,MAAK,SAAS,UAAU;AAAA,EACjC;AACF;;;ACxXA,IAAME,gBAAe,CAAC;AAYf,SAAS,QAAQ,MAAM,SAAS;AAGrC,QAAM,YAAY,YAAgB,IAAI;AACtC,QAAM,WAAW,WAAWA;AAC5B,QAAM,sBAAsB,uBAAuB;AAAA,IACjD,UAAU,SAAS,aAAa;AAAA,EAClC,CAAC;AACD,QAAM,QAAQ,YAAY,QAAQ;AAElC,MAAI;AAGJ,sBAAoB,SAAS;AAE7B,QAAM,WAAW,SAAUC,OAAM;AAC/B,QAAIA,SAAQA,MAAK,SAAS,aAAaA,MAAK,YAAY;AACtD,YAAM,KAAK,OAAOA,MAAK,WAAW,MAAM,EAAE,KAAK;AAE/C,UAAI,MAAM,CAAC,MAAM,YAAY,IAAI,EAAE,GAAG;AACpC,cAAM,YAAY,IAAI,IAAIA,KAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF,CAAC;AAED,QAAM,SAAS,MAAM,IAAI,WAAW,MAAS;AAE7C,MAAI,CAAC,QAAQ;AACX,YAAQ,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAAA,EACrC,WAAW,MAAM,QAAQ,MAAM,GAAG;AAEhC,UAAM;AAAA;AAAA,MAAmD;AAAA;AACzD,YAAQ,EAAC,MAAM,QAAQ,SAAQ;AAAA,EACjC,OAAO;AACL,YAAQ;AAAA,EACV;AAQA,QAAM,OAAO,SAAUA,OAAM,OAAO,QAAQ;AAC1C,QAAIA,MAAK,SAAS,UAAU,UAAU,UAAa,QAAQ;AACzD,YAAM,WAAW,OAAO,SAAS,QAAQ,CAAC;AAE1C,UAAI,YAAY,SAAS,SAASA,MAAK,MAAM;AAC3C,iBAAS,SAASA,MAAK;AACvB,eAAO,SAAS,OAAO,OAAO,CAAC;AAE/B,YAAI,SAAS,YAAYA,MAAK,UAAU;AACtC,mBAAS,SAAS,MAAMA,MAAK,SAAS;AAAA,QACxC;AAGA,eAAO,QAAQ;AAAA,MACjB;AAEA,MAAAA,MAAK,QAAQA,MAAK,MAAM,QAAQ,0BAA0B,IAAI;AAI9D,UACE,WACC,OAAO,SAAS,aACf,OAAO,SAAS,eAChB,OAAO,SAAS,SAClB;AACA,YAAI,CAAC,OAAO;AACV,UAAAA,MAAK,QAAQA,MAAK,MAAM,QAAQ,WAAW,EAAE;AAAA,QAC/C;AAEA,YAAI,UAAU,OAAO,SAAS,SAAS,GAAG;AACxC,UAAAA,MAAK,QAAQA,MAAK,MAAM,QAAQ,WAAW,EAAE;AAAA,QAC/C;AAAA,MACF;AAEA,UAAI,CAACA,MAAK,OAAO;AACf,eAAO,SAAS,OAAO,OAAO,CAAC;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AC1EA,IAAM,WAAW,EAAC,UAAU,KAAI;AAmCjB,SAAR,aAA8B,aAAa,SAAS;AACzD,MAAI,eAAe,SAAS,aAAa;AAIvC,WAAO,eAAgB,MAAM,MAAM;AACjC,YAAM,YAAY,QAAQ,MAAM,EAAC,GAAG,UAAU,GAAG,QAAO,CAAC;AACzD,YAAM,YAAY,IAAI,WAAW,IAAI;AAAA,IACvC;AAAA,EACF;AAKA,SAAO,SAAU,MAAM;AACrB;AAAA;AAAA,MACE,QAAQ,MAAM,EAAC,GAAG,UAAU,GAAG,YAAW,CAAC;AAAA;AAAA,EAE/C;AACF;", "names": ["point", "node", "node", "node", "node", "node", "input", "code", "node", "node", "node", "node", "node", "node", "node", "node", "node", "node", "node", "list", "properties", "node", "result", "list", "index", "value", "node", "node", "create", "node", "phrasing", "nodes", "node", "node", "node", "node", "node", "node", "node", "node", "create", "node", "result", "node", "node", "node", "all", "node", "all", "one", "node", "base", "emptyOptions", "node"]}
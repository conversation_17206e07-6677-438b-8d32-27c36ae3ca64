{"version": 3, "sources": ["../../../../graphiql/src/components/GraphiQL.tsx"], "sourcesContent": ["/**\n *  Copyright (c) 2020 GraphQL Contributors.\n *\n *  This source code is licensed under the MIT license found in the\n *  LICENSE file in the root directory of this source tree.\n */\n\nimport React, {\n  ComponentType,\n  Fragment,\n  MouseEventHandler,\n  PropsWithChildren,\n  ReactNode,\n  ReactElement,\n  useCallback,\n  useState,\n  useEffect,\n  useMemo,\n} from 'react';\n\nimport {\n  Button,\n  ButtonGroup,\n  ChevronDownIcon,\n  ChevronUpIcon,\n  CopyIcon,\n  Dialog,\n  ExecuteButton,\n  GraphiQLProvider,\n  GraphiQLProviderProps,\n  HeaderEditor,\n  KeyboardShortcutIcon,\n  MergeIcon,\n  PlusIcon,\n  PrettifyIcon,\n  QueryEditor,\n  ReloadIcon,\n  ResponseEditor,\n  SettingsIcon,\n  Spinner,\n  Tab,\n  Tabs,\n  ToolbarButton,\n  Tooltip,\n  UnStyledButton,\n  useCopyQuery,\n  useDragResize,\n  useEditorContext,\n  useExecutionContext,\n  UseHeaderEditorArgs,\n  useMergeQuery,\n  usePluginContext,\n  usePrettifyEditors,\n  UseQueryEditorArgs,\n  UseResponseEditorArgs,\n  useSchemaContext,\n  useStorageContext,\n  useTheme,\n  UseVariableEditorArgs,\n  VariableEditor,\n  WriteableEditorProps,\n} from '@graphiql/react';\n\nconst majorVersion = parseInt(React.version.slice(0, 2), 10);\n\nif (majorVersion < 16) {\n  throw new Error(\n    [\n      'GraphiQL 0.18.0 and after is not compatible with React 15 or below.',\n      'If you are using a CDN source (jsdelivr, unpkg, etc), follow this example:',\n      'https://github.com/graphql/graphiql/blob/master/examples/graphiql-cdn/index.html#L49',\n    ].join('\\n'),\n  );\n}\n\nexport type GraphiQLToolbarConfig = {\n  /**\n   * This content will be rendered after the built-in buttons of the toolbar.\n   * Note that this will not apply if you provide a completely custom toolbar\n   * (by passing `GraphiQL.Toolbar` as child to the `GraphiQL` component).\n   */\n  additionalContent?: React.ReactNode;\n\n  /**\n   * same as above, except a component with access to context\n   */\n  additionalComponent?: React.JSXElementConstructor<any>;\n};\n\n/**\n * API docs for this live here:\n *\n * https://graphiql-test.netlify.app/typedoc/modules/graphiql.html#graphiqlprops\n */\nexport type GraphiQLProps = Omit<GraphiQLProviderProps, 'children'> &\n  GraphiQLInterfaceProps;\n\n/**\n * The top-level React component for GraphiQL, intended to encompass the entire\n * browser viewport.\n *\n * @see https://github.com/graphql/graphiql#usage\n */\n\nexport function GraphiQL({\n  dangerouslyAssumeSchemaIsValid,\n  defaultQuery,\n  defaultTabs,\n  externalFragments,\n  fetcher,\n  getDefaultFieldNames,\n  headers,\n  inputValueDeprecation,\n  introspectionQueryName,\n  maxHistoryLength,\n  onEditOperationName,\n  onSchemaChange,\n  onTabChange,\n  onTogglePluginVisibility,\n  operationName,\n  plugins,\n  query,\n  response,\n  schema,\n  schemaDescription,\n  shouldPersistHeaders,\n  storage,\n  validationRules,\n  variables,\n  visiblePlugin,\n  defaultHeaders,\n  ...props\n}: GraphiQLProps) {\n  // Ensure props are correct\n  if (typeof fetcher !== 'function') {\n    throw new TypeError(\n      'The `GraphiQL` component requires a `fetcher` function to be passed as prop.',\n    );\n  }\n\n  return (\n    <GraphiQLProvider\n      getDefaultFieldNames={getDefaultFieldNames}\n      dangerouslyAssumeSchemaIsValid={dangerouslyAssumeSchemaIsValid}\n      defaultQuery={defaultQuery}\n      defaultHeaders={defaultHeaders}\n      defaultTabs={defaultTabs}\n      externalFragments={externalFragments}\n      fetcher={fetcher}\n      headers={headers}\n      inputValueDeprecation={inputValueDeprecation}\n      introspectionQueryName={introspectionQueryName}\n      maxHistoryLength={maxHistoryLength}\n      onEditOperationName={onEditOperationName}\n      onSchemaChange={onSchemaChange}\n      onTabChange={onTabChange}\n      onTogglePluginVisibility={onTogglePluginVisibility}\n      plugins={plugins}\n      visiblePlugin={visiblePlugin}\n      operationName={operationName}\n      query={query}\n      response={response}\n      schema={schema}\n      schemaDescription={schemaDescription}\n      shouldPersistHeaders={shouldPersistHeaders}\n      storage={storage}\n      validationRules={validationRules}\n      variables={variables}\n    >\n      <GraphiQLInterface\n        showPersistHeadersSettings={shouldPersistHeaders !== false}\n        disableTabs={props.disableTabs ?? false}\n        forcedTheme={props.forcedTheme}\n        {...props}\n      />\n    </GraphiQLProvider>\n  );\n}\n\n// Export main windows/panes to be used separately if desired.\nGraphiQL.Logo = GraphiQLLogo;\nGraphiQL.Toolbar = GraphiQLToolbar;\nGraphiQL.Footer = GraphiQLFooter;\n\ntype AddSuffix<Obj extends Record<string, any>, Suffix extends string> = {\n  [Key in keyof Obj as `${string & Key}${Suffix}`]: Obj[Key];\n};\n\nexport type GraphiQLInterfaceProps = WriteableEditorProps &\n  AddSuffix<Pick<UseQueryEditorArgs, 'onEdit'>, 'Query'> &\n  Pick<UseQueryEditorArgs, 'onCopyQuery'> &\n  AddSuffix<Pick<UseVariableEditorArgs, 'onEdit'>, 'Variables'> &\n  AddSuffix<Pick<UseHeaderEditorArgs, 'onEdit'>, 'Headers'> &\n  Pick<UseResponseEditorArgs, 'responseTooltip'> & {\n    children?: ReactNode;\n    /**\n     * Set the default state for the editor tools.\n     * - `false` hides the editor tools\n     * - `true` shows the editor tools\n     * - `'variables'` specifically shows the variables editor\n     * - `'headers'` specifically shows the headers editor\n     * By default the editor tools are initially shown when at least one of the\n     * editors has contents.\n     */\n    defaultEditorToolsVisibility?: boolean | 'variables' | 'headers';\n    /**\n     * Toggle if the headers editor should be shown inside the editor tools.\n     * @default true\n     */\n    isHeadersEditorEnabled?: boolean;\n    /**\n     * An object that allows configuration of the toolbar next to the query\n     * editor.\n     */\n    toolbar?: GraphiQLToolbarConfig;\n    /**\n     * Indicates if settings for persisting headers should appear in the\n     * settings modal.\n     */\n    showPersistHeadersSettings?: boolean;\n    disableTabs?: boolean;\n    /**\n     * forcedTheme allows enforcement of a specific theme for GraphiQL.\n     * This is useful when you want to make sure that GraphiQL is always\n     * rendered with a specific theme\n     */\n    forcedTheme?: (typeof THEMES)[number];\n    /**\n     * Additional class names which will be appended to the container element.\n     */\n    className?: string;\n  };\n\nconst THEMES = ['light', 'dark', 'system'] as const;\n\nexport function GraphiQLInterface(props: GraphiQLInterfaceProps) {\n  const isHeadersEditorEnabled = props.isHeadersEditorEnabled ?? true;\n  const editorContext = useEditorContext({ nonNull: true });\n  const executionContext = useExecutionContext({ nonNull: true });\n  const schemaContext = useSchemaContext({ nonNull: true });\n  const storageContext = useStorageContext();\n  const pluginContext = usePluginContext();\n  const forcedTheme = useMemo(\n    () =>\n      props.forcedTheme && THEMES.includes(props.forcedTheme)\n        ? props.forcedTheme\n        : undefined,\n    [props.forcedTheme],\n  );\n\n  const copy = useCopyQuery({ onCopyQuery: props.onCopyQuery });\n  const merge = useMergeQuery();\n  const prettify = usePrettifyEditors();\n\n  const { theme, setTheme } = useTheme();\n\n  useEffect(() => {\n    if (forcedTheme === 'system') {\n      setTheme(null);\n    } else if (forcedTheme === 'light' || forcedTheme === 'dark') {\n      setTheme(forcedTheme);\n    }\n  }, [forcedTheme, setTheme]);\n\n  const PluginContent = pluginContext?.visiblePlugin?.content;\n\n  const pluginResize = useDragResize({\n    defaultSizeRelation: 1 / 3,\n    direction: 'horizontal',\n    initiallyHidden: pluginContext?.visiblePlugin ? undefined : 'first',\n    onHiddenElementChange(resizableElement) {\n      if (resizableElement === 'first') {\n        pluginContext?.setVisiblePlugin(null);\n      }\n    },\n    sizeThresholdSecond: 200,\n    storageKey: 'docExplorerFlex',\n  });\n  const editorResize = useDragResize({\n    direction: 'horizontal',\n    storageKey: 'editorFlex',\n  });\n  const editorToolsResize = useDragResize({\n    defaultSizeRelation: 3,\n    direction: 'vertical',\n    initiallyHidden: (() => {\n      if (\n        props.defaultEditorToolsVisibility === 'variables' ||\n        props.defaultEditorToolsVisibility === 'headers'\n      ) {\n        return;\n      }\n\n      if (typeof props.defaultEditorToolsVisibility === 'boolean') {\n        return props.defaultEditorToolsVisibility ? undefined : 'second';\n      }\n\n      return editorContext.initialVariables || editorContext.initialHeaders\n        ? undefined\n        : 'second';\n    })(),\n    sizeThresholdSecond: 60,\n    storageKey: 'secondaryEditorFlex',\n  });\n\n  const [activeSecondaryEditor, setActiveSecondaryEditor] = useState<\n    'variables' | 'headers'\n  >(() => {\n    if (\n      props.defaultEditorToolsVisibility === 'variables' ||\n      props.defaultEditorToolsVisibility === 'headers'\n    ) {\n      return props.defaultEditorToolsVisibility;\n    }\n    return !editorContext.initialVariables &&\n      editorContext.initialHeaders &&\n      isHeadersEditorEnabled\n      ? 'headers'\n      : 'variables';\n  });\n  const [showDialog, setShowDialog] = useState<\n    'settings' | 'short-keys' | null\n  >(null);\n  const [clearStorageStatus, setClearStorageStatus] = useState<\n    'success' | 'error' | null\n  >(null);\n\n  const children = React.Children.toArray(props.children);\n\n  const logo = children.find(child =>\n    isChildComponentType(child, GraphiQL.Logo),\n  ) || <GraphiQL.Logo />;\n\n  const toolbar = children.find(child =>\n    isChildComponentType(child, GraphiQL.Toolbar),\n  ) || (\n    <>\n      <ToolbarButton onClick={prettify} label=\"Prettify query (Shift-Ctrl-P)\">\n        <PrettifyIcon className=\"graphiql-toolbar-icon\" aria-hidden=\"true\" />\n      </ToolbarButton>\n      <ToolbarButton\n        onClick={merge}\n        label=\"Merge fragments into query (Shift-Ctrl-M)\"\n      >\n        <MergeIcon className=\"graphiql-toolbar-icon\" aria-hidden=\"true\" />\n      </ToolbarButton>\n      <ToolbarButton onClick={copy} label=\"Copy query (Shift-Ctrl-C)\">\n        <CopyIcon className=\"graphiql-toolbar-icon\" aria-hidden=\"true\" />\n      </ToolbarButton>\n      {props.toolbar?.additionalContent}\n      {props.toolbar?.additionalComponent && (\n        <props.toolbar.additionalComponent />\n      )}\n    </>\n  );\n\n  const footer = children.find(child =>\n    isChildComponentType(child, GraphiQL.Footer),\n  );\n\n  const onClickReference = useCallback(() => {\n    if (pluginResize.hiddenElement === 'first') {\n      pluginResize.setHiddenElement(null);\n    }\n  }, [pluginResize]);\n\n  const handleClearData = useCallback(() => {\n    try {\n      storageContext?.clear();\n      setClearStorageStatus('success');\n    } catch {\n      setClearStorageStatus('error');\n    }\n  }, [storageContext]);\n\n  const handlePersistHeaders: MouseEventHandler<HTMLButtonElement> =\n    useCallback(\n      event => {\n        editorContext.setShouldPersistHeaders(\n          event.currentTarget.dataset.value === 'true',\n        );\n      },\n      [editorContext],\n    );\n\n  const handleChangeTheme: MouseEventHandler<HTMLButtonElement> = useCallback(\n    event => {\n      const selectedTheme = event.currentTarget.dataset.theme as\n        | 'light'\n        | 'dark'\n        | undefined;\n      setTheme(selectedTheme || null);\n    },\n    [setTheme],\n  );\n\n  const handleAddTab = editorContext.addTab;\n  const handleRefetchSchema = schemaContext.introspect;\n  const handleReorder = editorContext.moveTab;\n\n  const handleShowDialog: MouseEventHandler<HTMLButtonElement> = useCallback(\n    event => {\n      setShowDialog(\n        event.currentTarget.dataset.value as 'short-keys' | 'settings',\n      );\n    },\n    [],\n  );\n\n  const handlePluginClick: MouseEventHandler<HTMLButtonElement> = useCallback(\n    e => {\n      const context = pluginContext!;\n      const pluginIndex = Number(e.currentTarget.dataset.index!);\n      const plugin = context.plugins.find((_, index) => pluginIndex === index)!;\n      const isVisible = plugin === context.visiblePlugin;\n      if (isVisible) {\n        context.setVisiblePlugin(null);\n        pluginResize.setHiddenElement('first');\n      } else {\n        context.setVisiblePlugin(plugin);\n        pluginResize.setHiddenElement(null);\n      }\n    },\n    [pluginContext, pluginResize],\n  );\n\n  const handleToolsTabClick: MouseEventHandler<HTMLButtonElement> = useCallback(\n    event => {\n      if (editorToolsResize.hiddenElement === 'second') {\n        editorToolsResize.setHiddenElement(null);\n      }\n      setActiveSecondaryEditor(\n        event.currentTarget.dataset.name as 'variables' | 'headers',\n      );\n    },\n    [editorToolsResize],\n  );\n\n  const toggleEditorTools: MouseEventHandler<HTMLButtonElement> =\n    useCallback(() => {\n      editorToolsResize.setHiddenElement(\n        editorToolsResize.hiddenElement === 'second' ? null : 'second',\n      );\n    }, [editorToolsResize]);\n\n  const handleOpenShortKeysDialog = useCallback((isOpen: boolean) => {\n    if (!isOpen) {\n      setShowDialog(null);\n    }\n  }, []);\n\n  const handleOpenSettingsDialog = useCallback((isOpen: boolean) => {\n    if (!isOpen) {\n      setShowDialog(null);\n      setClearStorageStatus(null);\n    }\n  }, []);\n\n  const addTab = (\n    <Tooltip label=\"Add tab\">\n      <UnStyledButton\n        type=\"button\"\n        className=\"graphiql-tab-add\"\n        onClick={handleAddTab}\n        aria-label=\"Add tab\"\n      >\n        <PlusIcon aria-hidden=\"true\" />\n      </UnStyledButton>\n    </Tooltip>\n  );\n\n  const className = props.className ? ` ${props.className}` : '';\n\n  return (\n    <Tooltip.Provider>\n      <div\n        data-testid=\"graphiql-container\"\n        className={`graphiql-container${className}`}\n      >\n        <div className=\"graphiql-sidebar\">\n          <div className=\"graphiql-sidebar-section\">\n            {pluginContext?.plugins.map((plugin, index) => {\n              const isVisible = plugin === pluginContext.visiblePlugin;\n              const label = `${isVisible ? 'Hide' : 'Show'} ${plugin.title}`;\n              const Icon = plugin.icon;\n              return (\n                <Tooltip key={plugin.title} label={label}>\n                  <UnStyledButton\n                    type=\"button\"\n                    className={isVisible ? 'active' : ''}\n                    onClick={handlePluginClick}\n                    data-index={index}\n                    aria-label={label}\n                  >\n                    <Icon aria-hidden=\"true\" />\n                  </UnStyledButton>\n                </Tooltip>\n              );\n            })}\n          </div>\n          <div className=\"graphiql-sidebar-section\">\n            <Tooltip label=\"Re-fetch GraphQL schema\">\n              <UnStyledButton\n                type=\"button\"\n                disabled={schemaContext.isFetching}\n                onClick={handleRefetchSchema}\n                aria-label=\"Re-fetch GraphQL schema\"\n              >\n                <ReloadIcon\n                  className={schemaContext.isFetching ? 'graphiql-spin' : ''}\n                  aria-hidden=\"true\"\n                />\n              </UnStyledButton>\n            </Tooltip>\n            <Tooltip label=\"Open short keys dialog\">\n              <UnStyledButton\n                type=\"button\"\n                data-value=\"short-keys\"\n                onClick={handleShowDialog}\n                aria-label=\"Open short keys dialog\"\n              >\n                <KeyboardShortcutIcon aria-hidden=\"true\" />\n              </UnStyledButton>\n            </Tooltip>\n            <Tooltip label=\"Open settings dialog\">\n              <UnStyledButton\n                type=\"button\"\n                data-value=\"settings\"\n                onClick={handleShowDialog}\n                aria-label=\"Open settings dialog\"\n              >\n                <SettingsIcon aria-hidden=\"true\" />\n              </UnStyledButton>\n            </Tooltip>\n          </div>\n        </div>\n        <div className=\"graphiql-main\">\n          <div\n            ref={pluginResize.firstRef}\n            style={{\n              // Make sure the container shrinks when containing long\n              // non-breaking texts\n              minWidth: '200px',\n            }}\n          >\n            <div className=\"graphiql-plugin\">\n              {PluginContent ? <PluginContent /> : null}\n            </div>\n          </div>\n          {pluginContext?.visiblePlugin && (\n            <div\n              className=\"graphiql-horizontal-drag-bar\"\n              ref={pluginResize.dragBarRef}\n            />\n          )}\n          <div ref={pluginResize.secondRef} className=\"graphiql-sessions\">\n            <div className=\"graphiql-session-header\">\n              {!props.disableTabs && (\n                <Tabs\n                  values={editorContext.tabs}\n                  onReorder={handleReorder}\n                  aria-label=\"Select active operation\"\n                >\n                  {editorContext.tabs.length > 1 && (\n                    <>\n                      {editorContext.tabs.map((tab, index) => (\n                        <Tab\n                          key={tab.id}\n                          value={tab}\n                          isActive={index === editorContext.activeTabIndex}\n                        >\n                          <Tab.Button\n                            aria-controls=\"graphiql-session\"\n                            id={`graphiql-session-tab-${index}`}\n                            onClick={() => {\n                              executionContext.stop();\n                              editorContext.changeTab(index);\n                            }}\n                          >\n                            {tab.title}\n                          </Tab.Button>\n                          <Tab.Close\n                            onClick={() => {\n                              if (editorContext.activeTabIndex === index) {\n                                executionContext.stop();\n                              }\n                              editorContext.closeTab(index);\n                            }}\n                          />\n                        </Tab>\n                      ))}\n                      {addTab}\n                    </>\n                  )}\n                </Tabs>\n              )}\n              <div className=\"graphiql-session-header-right\">\n                {editorContext.tabs.length === 1 && addTab}\n                {logo}\n              </div>\n            </div>\n            <div\n              role=\"tabpanel\"\n              id=\"graphiql-session\"\n              className=\"graphiql-session\"\n              aria-labelledby={`graphiql-session-tab-${editorContext.activeTabIndex}`}\n            >\n              <div ref={editorResize.firstRef}>\n                <div\n                  className={`graphiql-editors${\n                    editorContext.tabs.length === 1 ? ' full-height' : ''\n                  }`}\n                >\n                  <div ref={editorToolsResize.firstRef}>\n                    <section\n                      className=\"graphiql-query-editor\"\n                      aria-label=\"Query Editor\"\n                    >\n                      <QueryEditor\n                        editorTheme={props.editorTheme}\n                        keyMap={props.keyMap}\n                        onClickReference={onClickReference}\n                        onCopyQuery={props.onCopyQuery}\n                        onEdit={props.onEditQuery}\n                        readOnly={props.readOnly}\n                      />\n                      <div\n                        className=\"graphiql-toolbar\"\n                        role=\"toolbar\"\n                        aria-label=\"Editor Commands\"\n                      >\n                        <ExecuteButton />\n                        {toolbar}\n                      </div>\n                    </section>\n                  </div>\n\n                  <div ref={editorToolsResize.dragBarRef}>\n                    <div className=\"graphiql-editor-tools\">\n                      <UnStyledButton\n                        type=\"button\"\n                        className={\n                          activeSecondaryEditor === 'variables' &&\n                          editorToolsResize.hiddenElement !== 'second'\n                            ? 'active'\n                            : ''\n                        }\n                        onClick={handleToolsTabClick}\n                        data-name=\"variables\"\n                      >\n                        Variables\n                      </UnStyledButton>\n                      {isHeadersEditorEnabled && (\n                        <UnStyledButton\n                          type=\"button\"\n                          className={\n                            activeSecondaryEditor === 'headers' &&\n                            editorToolsResize.hiddenElement !== 'second'\n                              ? 'active'\n                              : ''\n                          }\n                          onClick={handleToolsTabClick}\n                          data-name=\"headers\"\n                        >\n                          Headers\n                        </UnStyledButton>\n                      )}\n\n                      <Tooltip\n                        label={\n                          editorToolsResize.hiddenElement === 'second'\n                            ? 'Show editor tools'\n                            : 'Hide editor tools'\n                        }\n                      >\n                        <UnStyledButton\n                          type=\"button\"\n                          onClick={toggleEditorTools}\n                          aria-label={\n                            editorToolsResize.hiddenElement === 'second'\n                              ? 'Show editor tools'\n                              : 'Hide editor tools'\n                          }\n                          className=\"graphiql-toggle-editor-tools\"\n                        >\n                          {editorToolsResize.hiddenElement === 'second' ? (\n                            <ChevronUpIcon\n                              className=\"graphiql-chevron-icon\"\n                              aria-hidden=\"true\"\n                            />\n                          ) : (\n                            <ChevronDownIcon\n                              className=\"graphiql-chevron-icon\"\n                              aria-hidden=\"true\"\n                            />\n                          )}\n                        </UnStyledButton>\n                      </Tooltip>\n                    </div>\n                  </div>\n\n                  <div ref={editorToolsResize.secondRef}>\n                    <section\n                      className=\"graphiql-editor-tool\"\n                      aria-label={\n                        activeSecondaryEditor === 'variables'\n                          ? 'Variables'\n                          : 'Headers'\n                      }\n                    >\n                      <VariableEditor\n                        editorTheme={props.editorTheme}\n                        isHidden={activeSecondaryEditor !== 'variables'}\n                        keyMap={props.keyMap}\n                        onEdit={props.onEditVariables}\n                        onClickReference={onClickReference}\n                        readOnly={props.readOnly}\n                      />\n                      {isHeadersEditorEnabled && (\n                        <HeaderEditor\n                          editorTheme={props.editorTheme}\n                          isHidden={activeSecondaryEditor !== 'headers'}\n                          keyMap={props.keyMap}\n                          onEdit={props.onEditHeaders}\n                          readOnly={props.readOnly}\n                        />\n                      )}\n                    </section>\n                  </div>\n                </div>\n              </div>\n\n              <div\n                className=\"graphiql-horizontal-drag-bar\"\n                ref={editorResize.dragBarRef}\n              />\n\n              <div ref={editorResize.secondRef}>\n                <div className=\"graphiql-response\">\n                  {executionContext.isFetching ? <Spinner /> : null}\n                  <ResponseEditor\n                    editorTheme={props.editorTheme}\n                    responseTooltip={props.responseTooltip}\n                    keyMap={props.keyMap}\n                  />\n                  {footer}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <Dialog\n          open={showDialog === 'short-keys'}\n          onOpenChange={handleOpenShortKeysDialog}\n        >\n          <div className=\"graphiql-dialog-header\">\n            <Dialog.Title className=\"graphiql-dialog-title\">\n              Short Keys\n            </Dialog.Title>\n            <Dialog.Close />\n          </div>\n          <div className=\"graphiql-dialog-section\">\n            <ShortKeys keyMap={props.keyMap || 'sublime'} />\n          </div>\n        </Dialog>\n        <Dialog\n          open={showDialog === 'settings'}\n          onOpenChange={handleOpenSettingsDialog}\n        >\n          <div className=\"graphiql-dialog-header\">\n            <Dialog.Title className=\"graphiql-dialog-title\">\n              Settings\n            </Dialog.Title>\n            <Dialog.Close />\n          </div>\n          {props.showPersistHeadersSettings ? (\n            <div className=\"graphiql-dialog-section\">\n              <div>\n                <div className=\"graphiql-dialog-section-title\">\n                  Persist headers\n                </div>\n                <div className=\"graphiql-dialog-section-caption\">\n                  Save headers upon reloading.{' '}\n                  <span className=\"graphiql-warning-text\">\n                    Only enable if you trust this device.\n                  </span>\n                </div>\n              </div>\n              <ButtonGroup>\n                <Button\n                  type=\"button\"\n                  id=\"enable-persist-headers\"\n                  className={editorContext.shouldPersistHeaders ? 'active' : ''}\n                  data-value=\"true\"\n                  onClick={handlePersistHeaders}\n                >\n                  On\n                </Button>\n                <Button\n                  type=\"button\"\n                  id=\"disable-persist-headers\"\n                  className={editorContext.shouldPersistHeaders ? '' : 'active'}\n                  onClick={handlePersistHeaders}\n                >\n                  Off\n                </Button>\n              </ButtonGroup>\n            </div>\n          ) : null}\n          {!forcedTheme && (\n            <div className=\"graphiql-dialog-section\">\n              <div>\n                <div className=\"graphiql-dialog-section-title\">Theme</div>\n                <div className=\"graphiql-dialog-section-caption\">\n                  Adjust how the interface appears.\n                </div>\n              </div>\n              <ButtonGroup>\n                <Button\n                  type=\"button\"\n                  className={theme === null ? 'active' : ''}\n                  onClick={handleChangeTheme}\n                >\n                  System\n                </Button>\n                <Button\n                  type=\"button\"\n                  className={theme === 'light' ? 'active' : ''}\n                  data-theme=\"light\"\n                  onClick={handleChangeTheme}\n                >\n                  Light\n                </Button>\n                <Button\n                  type=\"button\"\n                  className={theme === 'dark' ? 'active' : ''}\n                  data-theme=\"dark\"\n                  onClick={handleChangeTheme}\n                >\n                  Dark\n                </Button>\n              </ButtonGroup>\n            </div>\n          )}\n          {storageContext ? (\n            <div className=\"graphiql-dialog-section\">\n              <div>\n                <div className=\"graphiql-dialog-section-title\">\n                  Clear storage\n                </div>\n                <div className=\"graphiql-dialog-section-caption\">\n                  Remove all locally stored data and start fresh.\n                </div>\n              </div>\n              <Button\n                type=\"button\"\n                state={clearStorageStatus || undefined}\n                disabled={clearStorageStatus === 'success'}\n                onClick={handleClearData}\n              >\n                {{\n                  success: 'Cleared data',\n                  error: 'Failed',\n                }[clearStorageStatus!] || 'Clear data'}\n              </Button>\n            </div>\n          ) : null}\n        </Dialog>\n      </div>\n    </Tooltip.Provider>\n  );\n}\n\nconst modifier =\n  typeof window !== 'undefined' &&\n  window.navigator.platform.toLowerCase().indexOf('mac') === 0\n    ? 'Cmd'\n    : 'Ctrl';\n\nconst SHORT_KEYS = Object.entries({\n  'Search in editor': [modifier, 'F'],\n  'Search in documentation': [modifier, 'K'],\n  'Execute query': [modifier, 'Enter'],\n  'Prettify editors': ['Ctrl', 'Shift', 'P'],\n  'Merge fragments definitions into operation definition': [\n    'Ctrl',\n    'Shift',\n    'M',\n  ],\n  'Copy query': ['Ctrl', 'Shift', 'C'],\n  'Re-fetch schema using introspection': ['Ctrl', 'Shift', 'R'],\n});\n\nfunction ShortKeys({ keyMap }: { keyMap: string }): ReactElement {\n  return (\n    <div>\n      <table className=\"graphiql-table\">\n        <thead>\n          <tr>\n            <th>Short Key</th>\n            <th>Function</th>\n          </tr>\n        </thead>\n        <tbody>\n          {SHORT_KEYS.map(([title, keys]) => (\n            <tr key={title}>\n              <td>\n                {keys.map((key, index, array) => (\n                  <Fragment key={key}>\n                    <code className=\"graphiql-key\">{key}</code>\n                    {index !== array.length - 1 && ' + '}\n                  </Fragment>\n                ))}\n              </td>\n              <td>{title}</td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n      <p>\n        The editors use{' '}\n        <a\n          href=\"https://codemirror.net/5/doc/manual.html#keymaps\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          CodeMirror Key Maps\n        </a>{' '}\n        that add more short keys. This instance of Graph<em>i</em>QL uses{' '}\n        <code>{keyMap}</code>.\n      </p>\n    </div>\n  );\n}\n\n// Configure the UI by providing this Component as a child of GraphiQL.\nfunction GraphiQLLogo<TProps>(props: PropsWithChildren<TProps>) {\n  return (\n    <div className=\"graphiql-logo\">\n      {props.children || (\n        <a\n          className=\"graphiql-logo-link\"\n          href=\"https://github.com/graphql/graphiql\"\n          target=\"_blank\"\n          rel=\"noreferrer\"\n        >\n          Graph\n          <em>i</em>\n          QL\n        </a>\n      )}\n    </div>\n  );\n}\n\nGraphiQLLogo.displayName = 'GraphiQLLogo';\n\n// Configure the UI by providing this Component as a child of GraphiQL.\nfunction GraphiQLToolbar<TProps>(props: PropsWithChildren<TProps>) {\n  // eslint-disable-next-line react/jsx-no-useless-fragment\n  return <>{props.children}</>;\n}\n\nGraphiQLToolbar.displayName = 'GraphiQLToolbar';\n\n// Configure the UI by providing this Component as a child of GraphiQL.\nfunction GraphiQLFooter<TProps>(props: PropsWithChildren<TProps>) {\n  return <div className=\"graphiql-footer\">{props.children}</div>;\n}\n\nGraphiQLFooter.displayName = 'GraphiQLFooter';\n\n// Determines if the React child is of the same type of the provided React component\nfunction isChildComponentType<T extends ComponentType>(\n  child: any,\n  component: T,\n): child is T {\n  if (\n    child?.type?.displayName &&\n    child.type.displayName === component.displayName\n  ) {\n    return true;\n  }\n\n  return child.type === component;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAWO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CP,IAAM,eAAe,SAAS,aAAAA,QAAM,QAAQ,MAAM,GAAG,CAAC,GAAG,EAAE;AAE3D,IAAI,eAAe,IAAI;AACrB,QAAM,IAAI,MACR;IACE;IACA;IACA;IACA,KAAK,IAAI,CAAC;;AAiCV,SAAU,SAAS,IA4BT;;AA3Bd,MAAA,iCAA8B,GAAA,gCAC9B,eAAY,GAAA,cACZ,cAAW,GAAA,aACX,oBAAiB,GAAA,mBACjB,UAAO,GAAA,SACP,uBAAoB,GAAA,sBACpB,UAAO,GAAA,SACP,wBAAqB,GAAA,uBACrB,yBAAsB,GAAA,wBACtB,mBAAgB,GAAA,kBAChB,sBAAmB,GAAA,qBACnB,iBAAc,GAAA,gBACd,cAAW,GAAA,aACX,2BAAwB,GAAA,0BACxB,gBAAa,GAAA,eACb,UAAO,GAAA,SACP,QAAK,GAAA,OACL,WAAQ,GAAA,UACR,SAAM,GAAA,QACN,oBAAiB,GAAA,mBACjB,uBAAoB,GAAA,sBACpB,UAAO,GAAA,SACP,kBAAe,GAAA,iBACf,YAAS,GAAA,WACT,gBAAa,GAAA,eACb,iBAAc,GAAA,gBACX,QAAK,OAAA,IA3Be,CAAA,kCAAA,gBAAA,eAAA,qBAAA,WAAA,wBAAA,WAAA,yBAAA,0BAAA,oBAAA,uBAAA,kBAAA,eAAA,4BAAA,iBAAA,WAAA,SAAA,YAAA,UAAA,qBAAA,wBAAA,WAAA,mBAAA,aAAA,iBAAA,gBAAA,CA4BxB;AAEC,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM,IAAI,UACR,8EAA8E;;AAIlF,SACE,aAAAA,QAAA;IAAC;IAAgB,EACf,sBACA,gCACA,cACA,gBACA,aACA,mBACA,SACA,SACA,uBACA,wBACA,kBACA,qBACA,gBACA,aACA,0BACA,SACA,eACA,eACA,OACA,UACA,QACA,mBACA,sBACA,SACA,iBACA,UAAoB;IAEpB,aAAAA,QAAA,cAAC,mBAAiB,SAAA,EAChB,4BAA4B,yBAAyB,OACrD,cAAa,KAAA,MAAM,iBAAW,QAAA,OAAA,SAAA,KAAI,OAClC,aAAa,MAAM,YAAW,GAC1B,KAAK,CAAA;EACT;AAGR;AAGA,SAAS,OAAO;AAChB,SAAS,UAAU;AACnB,SAAS,SAAS;AAmDlB,IAAM,SAAS,CAAC,SAAS,QAAQ,QAAQ;AAEnC,SAAU,kBAAkB,OAA6B;;AAC7D,MAAM,0BAAyB,KAAA,MAAM,4BAAsB,QAAA,OAAA,SAAA,KAAI;AAC/D,MAAM,gBAAgB,iBAAiB,EAAE,SAAS,KAAI,CAAE;AACxD,MAAM,mBAAmB,oBAAoB,EAAE,SAAS,KAAI,CAAE;AAC9D,MAAM,gBAAgB,iBAAiB,EAAE,SAAS,KAAI,CAAE;AACxD,MAAM,iBAAiB,kBAAiB;AACxC,MAAM,gBAAgB,iBAAgB;AACtC,MAAM,kBAAc,sBAClB,WAAA;AACE,WAAA,MAAM,eAAe,OAAO,SAAS,MAAM,WAAW,IAClD,MAAM,cACN;EAFJ,GAGF,CAAC,MAAM,WAAW,CAAC;AAGrB,MAAM,OAAO,aAAa,EAAE,aAAa,MAAM,YAAW,CAAE;AAC5D,MAAM,QAAQ,cAAa;AAC3B,MAAM,WAAW,mBAAkB;AAE7B,MAAA,KAAsB,SAAQ,GAA5B,QAAK,GAAA,OAAE,WAAQ,GAAA;AAEvB,8BAAU,WAAA;AACR,QAAI,gBAAgB,UAAU;AAC5B,eAAS,IAAI;eACJ,gBAAgB,WAAW,gBAAgB,QAAQ;AAC5D,eAAS,WAAW;;EAExB,GAAG,CAAC,aAAa,QAAQ,CAAC;AAE1B,MAAM,iBAAgB,KAAA,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE;AAEpD,MAAM,eAAe,cAAc;IACjC,qBAAqB,IAAI;IACzB,WAAW;IACX,kBAAiB,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,iBAAgB,SAAY;IAC5D,uBAAqB,SAAC,kBAAgB;AACpC,UAAI,qBAAqB,SAAS;AAChC,0BAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,iBAAiB,IAAI;;IAExC;IACA,qBAAqB;IACrB,YAAY;GACb;AACD,MAAM,eAAe,cAAc;IACjC,WAAW;IACX,YAAY;GACb;AACD,MAAM,oBAAoB,cAAc;IACtC,qBAAqB;IACrB,WAAW;IACX,iBAAkB,WAAA;AAChB,UACE,MAAM,iCAAiC,eACvC,MAAM,iCAAiC,WACvC;AACA;;AAGF,UAAI,OAAO,MAAM,iCAAiC,WAAW;AAC3D,eAAO,MAAM,+BAA+B,SAAY;;AAG1D,aAAO,cAAc,oBAAoB,cAAc,iBACnD,SACA;IACN,EAAE;IACF,qBAAqB;IACrB,YAAY;GACb;AAEK,MAAA,KAAA,WAAoD,uBAExD,WAAA;AACA,QACE,MAAM,iCAAiC,eACvC,MAAM,iCAAiC,WACvC;AACA,aAAO,MAAM;;AAEf,WAAO,CAAC,cAAc,oBACpB,cAAc,kBACd,yBACE,YACA;EACN,CAAC,GAAC,CAAA,GAdK,wBAAqB,GAAA,CAAA,GAAE,2BAAwB,GAAA,CAAA;AAehD,MAAA,KAAA,WAA8B,uBAElC,IAAI,GAAC,CAAA,GAFA,aAAU,GAAA,CAAA,GAAE,gBAAa,GAAA,CAAA;AAG1B,MAAA,KAAA,WAA8C,uBAElD,IAAI,GAAC,CAAA,GAFA,qBAAkB,GAAA,CAAA,GAAE,wBAAqB,GAAA,CAAA;AAIhD,MAAM,WAAW,aAAAA,QAAM,SAAS,QAAQ,MAAM,QAAQ;AAEtD,MAAM,OAAO,SAAS,KAAK,SAAA,OAAK;AAC9B,WAAA,qBAAqB,OAAO,SAAS,IAAI;EAAzC,CAA0C,KACvC,aAAAA,QAAA,cAAC,SAAS,MAAI,IAAA;AAEnB,MAAM,UAAU,SAAS,KAAK,SAAA,OAAK;AACjC,WAAA,qBAAqB,OAAO,SAAS,OAAO;EAA5C,CAA6C,KAE7C,aAAAA,QAAA;IAAA,aAAAA,QAAA;IAAA;IACE,aAAAA,QAAA;MAAC;MAAa,EAAC,SAAS,UAAU,OAAM,gCAA+B;MACrE,aAAAA,QAAA,cAAC,cAAY,EAAC,WAAU,yBAAuB,eAAa,OAAM,CAAA;IAAG;IAEvE,aAAAA,QAAA;MAAC;MAAa,EACZ,SAAS,OACT,OAAM,4CAA2C;MAEjD,aAAAA,QAAA,cAAC,WAAS,EAAC,WAAU,yBAAuB,eAAa,OAAM,CAAA;IAAG;IAEpE,aAAAA,QAAA;MAAC;MAAa,EAAC,SAAS,MAAM,OAAM,4BAA2B;MAC7D,aAAAA,QAAA,cAAC,UAAQ,EAAC,WAAU,yBAAuB,eAAa,OAAM,CAAA;IAAG;KAElE,KAAA,MAAM,aAAO,QAAA,OAAA,SAAA,YAAE;MACf,KAAA,MAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,wBACd,aAAAA,QAAA,cAAC,MAAM,QAAQ,qBAAmB,IAAA;EACnC;AAIL,MAAM,SAAS,SAAS,KAAK,SAAA,OAAK;AAChC,WAAA,qBAAqB,OAAO,SAAS,MAAM;EAA3C,CAA4C;AAG9C,MAAM,uBAAmB,0BAAY,WAAA;AACnC,QAAI,aAAa,kBAAkB,SAAS;AAC1C,mBAAa,iBAAiB,IAAI;;EAEtC,GAAG,CAAC,YAAY,CAAC;AAEjB,MAAM,sBAAkB,0BAAY,WAAA;AAClC,QAAI;AACF,yBAAc,QAAd,mBAAc,SAAA,SAAd,eAAgB,MAAK;AACrB,4BAAsB,SAAS;aAC/BC,KAAM;AACN,4BAAsB,OAAO;;EAEjC,GAAG,CAAC,cAAc,CAAC;AAEnB,MAAM,2BACJ,0BACE,SAAA,OAAK;AACH,kBAAc,wBACZ,MAAM,cAAc,QAAQ,UAAU,MAAM;EAEhD,GACA,CAAC,aAAa,CAAC;AAGnB,MAAM,wBAA0D,0BAC9D,SAAA,OAAK;AACH,QAAM,gBAAgB,MAAM,cAAc,QAAQ;AAIlD,aAAS,iBAAiB,IAAI;EAChC,GACA,CAAC,QAAQ,CAAC;AAGZ,MAAM,eAAe,cAAc;AACnC,MAAM,sBAAsB,cAAc;AAC1C,MAAM,gBAAgB,cAAc;AAEpC,MAAM,uBAAyD,0BAC7D,SAAA,OAAK;AACH,kBACE,MAAM,cAAc,QAAQ,KAAkC;EAElE,GACA,CAAA,CAAE;AAGJ,MAAM,wBAA0D,0BAC9D,SAAA,GAAC;AACC,QAAM,UAAU;AAChB,QAAM,cAAc,OAAO,EAAE,cAAc,QAAQ,KAAM;AACzD,QAAM,SAAS,QAAQ,QAAQ,KAAK,SAAC,GAAG,OAAK;AAAK,aAAA,gBAAgB;IAAhB,CAAqB;AACvE,QAAM,YAAY,WAAW,QAAQ;AACrC,QAAI,WAAW;AACb,cAAQ,iBAAiB,IAAI;AAC7B,mBAAa,iBAAiB,OAAO;WAChC;AACL,cAAQ,iBAAiB,MAAM;AAC/B,mBAAa,iBAAiB,IAAI;;EAEtC,GACA,CAAC,eAAe,YAAY,CAAC;AAG/B,MAAM,0BAA4D,0BAChE,SAAA,OAAK;AACH,QAAI,kBAAkB,kBAAkB,UAAU;AAChD,wBAAkB,iBAAiB,IAAI;;AAEzC,6BACE,MAAM,cAAc,QAAQ,IAA+B;EAE/D,GACA,CAAC,iBAAiB,CAAC;AAGrB,MAAM,wBACJ,0BAAY,WAAA;AACV,sBAAkB,iBAChB,kBAAkB,kBAAkB,WAAW,OAAO,QAAQ;EAElE,GAAG,CAAC,iBAAiB,CAAC;AAExB,MAAM,gCAA4B,0BAAY,SAAC,QAAe;AAC5D,QAAI,CAAC,QAAQ;AACX,oBAAc,IAAI;;EAEtB,GAAG,CAAA,CAAE;AAEL,MAAM,+BAA2B,0BAAY,SAAC,QAAe;AAC3D,QAAI,CAAC,QAAQ;AACX,oBAAc,IAAI;AAClB,4BAAsB,IAAI;;EAE9B,GAAG,CAAA,CAAE;AAEL,MAAM,SACJ,aAAAD,QAAA;IAAC;IAAO,EAAC,OAAM,UAAS;IACtB,aAAAA,QAAA;MAAC;MAAc,EACb,MAAK,UACL,WAAU,oBACV,SAAS,cAAY,cACV,UAAS;MAEpB,aAAAA,QAAA,cAAC,UAAQ,EAAA,eAAa,OAAM,CAAA;IAAG;EAChB;AAIrB,MAAM,YAAY,MAAM,YAAY,IAAA,OAAI,MAAM,SAAS,IAAK;AAE5D,SACE,aAAAA,QAAA;IAAC,QAAQ;IAAQ;IACf,aAAAA,QAAA;MAAA;MAAA,EAAA,eACc,sBACZ,WAAW,qBAAA,OAAqB,SAAS,EAAE;MAE3C,aAAAA,QAAA;QAAA;QAAA,EAAK,WAAU,mBAAkB;QAC/B,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,2BAA0B,GACtC,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,QAAQ,IAAI,SAAC,QAAQ,OAAK;AACxC,cAAM,YAAY,WAAW,cAAc;AAC3C,cAAM,QAAQ,GAAA,OAAG,YAAY,SAAS,QAAM,GAAA,EAAA,OAAI,OAAO,KAAK;AAC5D,cAAM,OAAO,OAAO;AACpB,iBACE,aAAAA,QAAA;YAAC;YAAO,EAAC,KAAK,OAAO,OAAO,MAAY;YACtC,aAAAA,QAAA;cAAC;cAAc,EACb,MAAK,UACL,WAAW,YAAY,WAAW,IAClC,SAAS,mBAAiB,cACd,OAAK,cACL,MAAK;cAEjB,aAAAA,QAAA,cAAC,MAAI,EAAA,eAAa,OAAM,CAAA;YAAG;UACZ;QAGvB,CAAC,CAAC;QAEJ,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,2BAA0B;UACvC,aAAAA,QAAA;YAAC;YAAO,EAAC,OAAM,0BAAyB;YACtC,aAAAA,QAAA;cAAC;cAAc,EACb,MAAK,UACL,UAAU,cAAc,YACxB,SAAS,qBAAmB,cACjB,0BAAyB;cAEpC,aAAAA,QAAA,cAAC,YAAU,EACT,WAAW,cAAc,aAAa,kBAAkB,IAAE,eAC9C,OAAM,CAAA;YAClB;UACa;UAEnB,aAAAA,QAAA;YAAC;YAAO,EAAC,OAAM,yBAAwB;YACrC,aAAAA,QAAA;cAAC;cAAc,EACb,MAAK,UAAQ,cACF,cACX,SAAS,kBAAgB,cACd,yBAAwB;cAEnC,aAAAA,QAAA,cAAC,sBAAoB,EAAA,eAAa,OAAM,CAAA;YAAG;UAC5B;UAEnB,aAAAA,QAAA;YAAC;YAAO,EAAC,OAAM,uBAAsB;YACnC,aAAAA,QAAA;cAAC;cAAc,EACb,MAAK,UAAQ,cACF,YACX,SAAS,kBAAgB,cACd,uBAAsB;cAEjC,aAAAA,QAAA,cAAC,cAAY,EAAA,eAAa,OAAM,CAAA;YAAG;UACpB;QACT;MACN;MAER,aAAAA,QAAA;QAAA;QAAA,EAAK,WAAU,gBAAe;QAC5B,aAAAA,QAAA;UAAA;UAAA,EACE,KAAK,aAAa,UAClB,OAAO;YAGL,UAAU;YACX;UAED,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,kBAAiB,GAC7B,gBAAgB,aAAAA,QAAA,cAAC,eAAa,IAAA,IAAM,IAAI;QACrC;SAEP,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,kBACd,aAAAA,QAAA,cAAA,OAAA,EACE,WAAU,gCACV,KAAK,aAAa,WAAU,CAAA;QAGhC,aAAAA,QAAA;UAAA;UAAA,EAAK,KAAK,aAAa,WAAW,WAAU,oBAAmB;UAC7D,aAAAA,QAAA;YAAA;YAAA,EAAK,WAAU,0BAAyB;YACrC,CAAC,MAAM,eACN,aAAAA,QAAA,cAAC,MAAI,EACH,QAAQ,cAAc,MACtB,WAAW,eAAa,cACb,0BAAyB,GAEnC,cAAc,KAAK,SAAS,KAC3B,aAAAA,QAAA;cAAA,aAAAA,QAAA;cAAA;cACG,cAAc,KAAK,IAAI,SAAC,KAAK,OAAK;AAAK,uBACtC,aAAAA,QAAA;kBAAC;kBAAG,EACF,KAAK,IAAI,IACT,OAAO,KACP,UAAU,UAAU,cAAc,eAAc;kBAEhD,aAAAA,QAAA,cAAC,IAAI,QAAM,EAAA,iBACK,oBACd,IAAI,wBAAA,OAAwB,KAAK,GACjC,SAAS,WAAA;AACP,qCAAiB,KAAI;AACrB,kCAAc,UAAU,KAAK;kBAC/B,EAAC,GAEA,IAAI,KAAK;kBAEZ,aAAAA,QAAA,cAAC,IAAI,OAAK,EACR,SAAS,WAAA;AACP,wBAAI,cAAc,mBAAmB,OAAO;AAC1C,uCAAiB,KAAI;;AAEvB,kCAAc,SAAS,KAAK;kBAC9B,EAAC,CAAA;gBACD;cAvBkC,CAyBvC;cACA;YAAM,CAEV;YAGL,aAAAA,QAAA;cAAA;cAAA,EAAK,WAAU,gCAA+B;cAC3C,cAAc,KAAK,WAAW,KAAK;cACnC;YAAI;UACD;UAER,aAAAA,QAAA;YAAA;YAAA,EACE,MAAK,YACL,IAAG,oBACH,WAAU,oBAAkB,mBACX,wBAAA,OAAwB,cAAc,cAAc,EAAE;YAEvE,aAAAA,QAAA;cAAA;cAAA,EAAK,KAAK,aAAa,SAAQ;cAC7B,aAAAA,QAAA;gBAAA;gBAAA,EACE,WAAW,mBAAA,OACT,cAAc,KAAK,WAAW,IAAI,iBAAiB,EAAE,EACrD;gBAEF,aAAAA,QAAA;kBAAA;kBAAA,EAAK,KAAK,kBAAkB,SAAQ;kBAClC,aAAAA,QAAA;oBAAA;oBAAA,EACE,WAAU,yBAAuB,cACtB,eAAc;oBAEzB,aAAAA,QAAA,cAAC,aAAW,EACV,aAAa,MAAM,aACnB,QAAQ,MAAM,QACd,kBACA,aAAa,MAAM,aACnB,QAAQ,MAAM,aACd,UAAU,MAAM,SAAQ,CAAA;oBAE1B,aAAAA,QAAA;sBAAA;sBAAA,EACE,WAAU,oBACV,MAAK,WAAS,cACH,kBAAiB;sBAE5B,aAAAA,QAAA,cAAC,eAAa,IAAA;sBACb;oBAAO;kBACJ;gBACE;gBAGZ,aAAAA,QAAA;kBAAA;kBAAA,EAAK,KAAK,kBAAkB,WAAU;kBACpC,aAAAA,QAAA;oBAAA;oBAAA,EAAK,WAAU,wBAAuB;oBACpC,aAAAA,QAAA,cAAC,gBAAc,EACb,MAAK,UACL,WACE,0BAA0B,eAC1B,kBAAkB,kBAAkB,WAChC,WACA,IAEN,SAAS,qBAAmB,aAClB,YAAW,GAAA,WAAA;oBAItB,0BACC,aAAAA,QAAA,cAAC,gBAAc,EACb,MAAK,UACL,WACE,0BAA0B,aAC1B,kBAAkB,kBAAkB,WAChC,WACA,IAEN,SAAS,qBAAmB,aAClB,UAAS,GAAA,SAAA;oBAMvB,aAAAA,QAAA;sBAAC;sBAAO,EACN,OACE,kBAAkB,kBAAkB,WAChC,sBACA,oBAAmB;sBAGzB,aAAAA,QAAA,cAAC,gBAAc,EACb,MAAK,UACL,SAAS,mBAAiB,cAExB,kBAAkB,kBAAkB,WAChC,sBACA,qBAEN,WAAU,+BAA8B,GAEvC,kBAAkB,kBAAkB,WACnC,aAAAA,QAAA,cAAC,eAAa,EACZ,WAAU,yBAAuB,eACrB,OAAM,CAAA,IAGpB,aAAAA,QAAA,cAAC,iBAAe,EACd,WAAU,yBAAuB,eACrB,OAAM,CAAA,CAErB;oBACc;kBACT;gBACN;gBAGR,aAAAA,QAAA;kBAAA;kBAAA,EAAK,KAAK,kBAAkB,UAAS;kBACnC,aAAAA,QAAA;oBAAA;oBAAA,EACE,WAAU,wBAAsB,cAE9B,0BAA0B,cACtB,cACA,UAAS;oBAGf,aAAAA,QAAA,cAAC,gBAAc,EACb,aAAa,MAAM,aACnB,UAAU,0BAA0B,aACpC,QAAQ,MAAM,QACd,QAAQ,MAAM,iBACd,kBACA,UAAU,MAAM,SAAQ,CAAA;oBAEzB,0BACC,aAAAA,QAAA,cAAC,cAAY,EACX,aAAa,MAAM,aACnB,UAAU,0BAA0B,WACpC,QAAQ,MAAM,QACd,QAAQ,MAAM,eACd,UAAU,MAAM,SAAQ,CAAA;kBAE3B;gBACO;cACN;YACF;YAGR,aAAAA,QAAA,cAAA,OAAA,EACE,WAAU,gCACV,KAAK,aAAa,WAAU,CAAA;YAG9B,aAAAA,QAAA;cAAA;cAAA,EAAK,KAAK,aAAa,UAAS;cAC9B,aAAAA,QAAA;gBAAA;gBAAA,EAAK,WAAU,oBAAmB;gBAC/B,iBAAiB,aAAa,aAAAA,QAAA,cAAC,SAAO,IAAA,IAAM;gBAC7C,aAAAA,QAAA,cAAC,gBAAc,EACb,aAAa,MAAM,aACnB,iBAAiB,MAAM,iBACvB,QAAQ,MAAM,OAAM,CAAA;gBAErB;cAAM;YACH;UACF;QACF;MACF;MAER,aAAAA,QAAA;QAAC;QAAM,EACL,MAAM,eAAe,cACrB,cAAc,0BAAyB;QAEvC,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,yBAAwB;UACrC,aAAAA,QAAA,cAAC,OAAO,OAAK,EAAC,WAAU,wBAAuB,GAAA,YAAA;UAG/C,aAAAA,QAAA,cAAC,OAAO,OAAK,IAAA;QAAG;QAElB,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,0BAAyB;UACtC,aAAAA,QAAA,cAAC,WAAS,EAAC,QAAQ,MAAM,UAAU,UAAS,CAAA;QAAI;MAC5C;MAER,aAAAA,QAAA;QAAC;QAAM,EACL,MAAM,eAAe,YACrB,cAAc,yBAAwB;QAEtC,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,yBAAwB;UACrC,aAAAA,QAAA,cAAC,OAAO,OAAK,EAAC,WAAU,wBAAuB,GAAA,UAAA;UAG/C,aAAAA,QAAA,cAAC,OAAO,OAAK,IAAA;QAAG;QAEjB,MAAM,6BACL,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,0BAAyB;UACtC,aAAAA,QAAA;YAAA;YAAA;YACE,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gCAA+B,GAAA,iBAAA;YAG9C,aAAAA,QAAA;cAAA;cAAA,EAAK,WAAU,kCAAiC;;cACjB;cAC7B,aAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,wBAAuB,GAAA,uCAAA;YAEhC;UACH;UAER,aAAAA,QAAA;YAAC;YAAW;YACV,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,IAAG,0BACH,WAAW,cAAc,uBAAuB,WAAW,IAAE,cAClD,QACX,SAAS,qBAAoB,GAAA,IAAA;YAI/B,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,IAAG,2BACH,WAAW,cAAc,uBAAuB,KAAK,UACrD,SAAS,qBAAoB,GAAA,KAAA;UAGtB;QACG,IAEd;QACH,CAAC,eACA,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,0BAAyB;UACtC,aAAAA,QAAA;YAAA;YAAA;YACE,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gCAA+B,GAAA,OAAA;YAC9C,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,kCAAiC,GAAA,mCAAA;UAE1C;UAER,aAAAA,QAAA;YAAC;YAAW;YACV,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,WAAW,UAAU,OAAO,WAAW,IACvC,SAAS,kBAAiB,GAAA,QAAA;YAI5B,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,WAAW,UAAU,UAAU,WAAW,IAAE,cACjC,SACX,SAAS,kBAAiB,GAAA,OAAA;YAI5B,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,WAAW,UAAU,SAAS,WAAW,IAAE,cAChC,QACX,SAAS,kBAAiB,GAAA,MAAA;UAGnB;QACG;QAGjB,iBACC,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAU,0BAAyB;UACtC,aAAAA,QAAA;YAAA;YAAA;YACE,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gCAA+B,GAAA,eAAA;YAG9C,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,kCAAiC,GAAA,iDAAA;UAE1C;UAER,aAAAA,QAAA,cAAC,UAAM,EACL,MAAK,UACL,OAAO,sBAAsB,QAC7B,UAAU,uBAAuB,WACjC,SAAS,gBAAe,GAEvB;YACC,SAAS;YACT,OAAO;YACP,kBAAmB,KAAK,YAAY;QAC/B,IAET;MAAI;IACD;EACL;AAGZ;AAEA,IAAM,WACJ,OAAO,WAAW,eAClB,OAAO,UAAU,SAAS,YAAW,EAAG,QAAQ,KAAK,MAAM,IACvD,QACA;AAEN,IAAM,aAAa,OAAO,QAAQ;EAChC,oBAAoB,CAAC,UAAU,GAAG;EAClC,2BAA2B,CAAC,UAAU,GAAG;EACzC,iBAAiB,CAAC,UAAU,OAAO;EACnC,oBAAoB,CAAC,QAAQ,SAAS,GAAG;EACzC,yDAAyD;IACvD;IACA;IACA;;EAEF,cAAc,CAAC,QAAQ,SAAS,GAAG;EACnC,uCAAuC,CAAC,QAAQ,SAAS,GAAG;CAC7D;AAED,SAAS,UAAU,IAA8B;MAA5B,SAAM,GAAA;AACzB,SACE,aAAAA,QAAA;IAAA;IAAA;IACE,aAAAA,QAAA;MAAA;MAAA,EAAO,WAAU,iBAAgB;MAC/B,aAAAA,QAAA;QAAA;QAAA;QACE,aAAAA,QAAA;UAAA;UAAA;UACE,aAAAA,QAAA,cAAA,MAAA,MAAA,WAAA;UACA,aAAAA,QAAA,cAAA,MAAA,MAAA,UAAA;QAAiB;MACd;MAEP,aAAAA,QAAA,cAAA,SAAA,MACG,WAAW,IAAI,SAACC,KAAa;YAAb,KAAA,OAAAA,KAAA,CAAA,GAAC,QAAK,GAAA,CAAA,GAAE,OAAI,GAAA,CAAA;AAAM,eACjC,aAAAD,QAAA;UAAA;UAAA,EAAI,KAAK,MAAK;UACZ,aAAAA,QAAA,cAAA,MAAA,MACG,KAAK,IAAI,SAAC,KAAK,OAAO,OAAK;AAAK,mBAC/B,aAAAA,QAAA;cAAC;cAAQ,EAAC,IAAQ;cAChB,aAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,eAAc,GAAE,GAAG;cAClC,UAAU,MAAM,SAAS,KAAK;YAAK;UAHP,CAKhC,CAAC;UAEJ,aAAAA,QAAA,cAAA,MAAA,MAAK,KAAK;QAAM;MAVe,CAYlC,CAAC;IACI;IAEV,aAAAA,QAAA;MAAA;MAAA;;MACkB;MAChB,aAAAA,QAAA,cAAA,KAAA,EACE,MAAK,oDACL,QAAO,UACP,KAAI,sBAAqB,GAAA,qBAAA;MAGtB;;MAC2C,aAAAA,QAAA,cAAA,MAAA,MAAA,GAAA;;MAAkB;MAClE,aAAAA,QAAA,cAAA,QAAA,MAAO,MAAM;;;EACX;AAGV;AAGA,SAAS,aAAqB,OAAgC;AAC5D,SACE,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gBAAe,GAC3B,MAAM,YACL,aAAAA,QAAA;IAAA;IAAA,EACE,WAAU,sBACV,MAAK,uCACL,QAAO,UACP,KAAI,aAAY;;IAGhB,aAAAA,QAAA,cAAA,MAAA,MAAA,GAAA;;GAGH;AAGP;AAEA,aAAa,cAAc;AAG3B,SAAS,gBAAwB,OAAgC;AAE/D,SAAO,aAAAA,QAAA,cAAA,aAAAA,QAAA,UAAA,MAAG,MAAM,QAAQ;AAC1B;AAEA,gBAAgB,cAAc;AAG9B,SAAS,eAAuB,OAAgC;AAC9D,SAAO,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,kBAAiB,GAAE,MAAM,QAAQ;AACzD;AAEA,eAAe,cAAc;AAG7B,SAAS,qBACP,OACA,WAAY;;AAEZ,QACE,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,gBACb,MAAM,KAAK,gBAAgB,UAAU,aACrC;AACA,WAAO;;AAGT,SAAO,MAAM,SAAS;AACxB;", "names": ["React", "_a"]}